/**
 * 角色创建教程
 * 引导用户学习如何创建和自定义3D角色
 */
import { i18n } from '../i18n';
import { ValidationConditionType } from '../services/TutorialValidationService';

/**
 * 角色创建教程
 */
export const CharacterCreationTutorial = {
  id: 'character-creation',
  title: i18n.t('tutorials.characterCreation.title'),
  description: i18n.t('tutorials.characterCreation.description'),
  category: 'character',
  difficulty: 'intermediate',
  duration: 30,
  interactive: true,
  tags: ['角色', '模型', '材质', '骨骼', '动画'],
  steps: [
    // 步骤1：介绍
    {
      id: 'introduction',
      title: i18n.t('tutorials.characterCreation.steps.introduction.title'),
      description: i18n.t('tutorials.characterCreation.steps.introduction.description'),
      completed: false,
      tasks: [
        {
          id: 'read-introduction',
          title: '阅读介绍',
          description: '阅读本教程的介绍，了解将要学习的内容。',
          completed: false,
          conditions: [
            {
              type: ValidationConditionType.ACTION_PERFORMED,
              value: 'read-introduction'
            }
          ]
        }
      ],
      highlightSelector: '.tutorial-panel',
      highlightMessage: '欢迎来到角色创建教程！'
    },
    
    // 步骤2：导入模型
    {
      id: 'model-import',
      title: i18n.t('tutorials.characterCreation.steps.modelImport.title'),
      description: i18n.t('tutorials.characterCreation.steps.modelImport.description'),
      completed: false,
      tasks: [
        {
          id: 'open-import-dialog',
          title: '打开导入对话框',
          description: '点击顶部菜单中的"导入"按钮，打开导入对话框。',
          completed: false,
          conditions: [
            {
              type: ValidationConditionType.PANEL_OPENED,
              value: 'import-panel'
            }
          ],
          hint: '在顶部菜单栏中找到"导入"按钮，点击它打开导入对话框。'
        },
        {
          id: 'select-character-model',
          title: '选择角色模型',
          description: '从资源库中选择一个角色模型。',
          completed: false,
          conditions: [
            {
              type: ValidationConditionType.PROPERTY_EXISTS,
              target: 'asset',
              property: 'selectedAsset'
            }
          ],
          hint: '在资源库中找到"角色"分类，然后选择一个角色模型。'
        },
        {
          id: 'import-model',
          title: '导入模型',
          description: '点击"导入"按钮，将选中的模型导入到场景中。',
          completed: false,
          conditions: [
            {
              type: ValidationConditionType.OBJECT_SELECTED,
              value: 'character'
            }
          ],
          hint: '选择模型后，点击对话框底部的"导入"按钮。'
        }
      ],
      highlightSelector: '.import-button',
      highlightMessage: '点击这里导入模型'
    },
    
    // 步骤3：设置材质
    {
      id: 'material-setup',
      title: i18n.t('tutorials.characterCreation.steps.materialSetup.title'),
      description: i18n.t('tutorials.characterCreation.steps.materialSetup.description'),
      completed: false,
      tasks: [
        {
          id: 'open-material-editor',
          title: '打开材质编辑器',
          description: '选中角色模型，然后点击属性面板中的"材质"选项卡。',
          completed: false,
          conditions: [
            {
              type: ValidationConditionType.PANEL_OPENED,
              value: 'material-editor'
            }
          ],
          hint: '在右侧属性面板中找到"材质"选项卡，点击它打开材质编辑器。'
        },
        {
          id: 'adjust-material-properties',
          title: '调整材质属性',
          description: '调整角色材质的基本属性，如颜色、光泽度等。',
          completed: false,
          conditions: [
            {
              type: ValidationConditionType.ACTION_PERFORMED,
              value: 'adjust-material'
            }
          ],
          hint: '在材质编辑器中，尝试调整"基础颜色"、"金属度"和"粗糙度"等属性。'
        }
      ],
      highlightSelector: '.material-tab',
      highlightMessage: '点击这里打开材质编辑器'
    },
    
    // 步骤4：骨骼绑定
    {
      id: 'rigging',
      title: i18n.t('tutorials.characterCreation.steps.rigging.title'),
      description: i18n.t('tutorials.characterCreation.steps.rigging.description'),
      completed: false,
      tasks: [
        {
          id: 'open-rigging-panel',
          title: '打开骨骼面板',
          description: '点击顶部菜单中的"骨骼"按钮，打开骨骼面板。',
          completed: false,
          conditions: [
            {
              type: ValidationConditionType.PANEL_OPENED,
              value: 'rigging-panel'
            }
          ],
          hint: '在顶部菜单栏中找到"骨骼"按钮，点击它打开骨骼面板。'
        },
        {
          id: 'create-skeleton',
          title: '创建骨骼',
          description: '使用骨骼工具为角色创建基本骨骼结构。',
          completed: false,
          optional: true,
          conditions: [
            {
              type: ValidationConditionType.ACTION_PERFORMED,
              value: 'create-skeleton'
            }
          ],
          hint: '在骨骼面板中，使用"创建骨骼"工具添加骨骼。从角色的根部开始，逐步添加骨骼到四肢和头部。'
        },
        {
          id: 'bind-skeleton',
          title: '绑定骨骼',
          description: '将创建的骨骼绑定到角色模型。',
          completed: false,
          conditions: [
            {
              type: ValidationConditionType.ACTION_PERFORMED,
              value: 'bind-skeleton'
            }
          ],
          hint: '选择角色模型和骨骼，然后点击"绑定"按钮。'
        }
      ],
      highlightSelector: '.rigging-button',
      highlightMessage: '点击这里打开骨骼面板'
    },
    
    // 步骤5：动画设置
    {
      id: 'animation-setup',
      title: i18n.t('tutorials.characterCreation.steps.animationSetup.title'),
      description: i18n.t('tutorials.characterCreation.steps.animationSetup.description'),
      completed: false,
      tasks: [
        {
          id: 'open-animation-panel',
          title: '打开动画面板',
          description: '点击顶部菜单中的"动画"按钮，打开动画面板。',
          completed: false,
          conditions: [
            {
              type: ValidationConditionType.PANEL_OPENED,
              value: 'animation-panel'
            }
          ],
          hint: '在顶部菜单栏中找到"动画"按钮，点击它打开动画面板。'
        },
        {
          id: 'import-animation',
          title: '导入动画',
          description: '从动画库中导入预设动画。',
          completed: false,
          conditions: [
            {
              type: ValidationConditionType.ACTION_PERFORMED,
              value: 'import-animation'
            }
          ],
          hint: '在动画面板中，点击"导入"按钮，然后从动画库中选择一个预设动画。'
        },
        {
          id: 'play-animation',
          title: '播放动画',
          description: '播放导入的动画，检查效果。',
          completed: false,
          conditions: [
            {
              type: ValidationConditionType.ACTION_PERFORMED,
              value: 'play-animation'
            }
          ],
          hint: '选择导入的动画，然后点击"播放"按钮。'
        }
      ],
      highlightSelector: '.animation-button',
      highlightMessage: '点击这里打开动画面板'
    },
    
    // 步骤6：总结
    {
      id: 'conclusion',
      title: i18n.t('tutorials.characterCreation.steps.conclusion.title'),
      description: i18n.t('tutorials.characterCreation.steps.conclusion.description'),
      completed: false,
      tasks: [
        {
          id: 'review-character',
          title: '查看完成的角色',
          description: '查看您创建的角色，确保所有设置都正确。',
          completed: false,
          conditions: [
            {
              type: ValidationConditionType.ACTION_PERFORMED,
              value: 'review-character'
            }
          ]
        },
        {
          id: 'save-character',
          title: '保存角色',
          description: '将创建的角色保存到项目中。',
          completed: false,
          conditions: [
            {
              type: ValidationConditionType.ACTION_PERFORMED,
              value: 'save-character'
            }
          ],
          hint: '点击顶部菜单中的"保存"按钮，为角色命名并保存。'
        }
      ]
    }
  ]
};
