/**
 * 资产面板组件
 */
import React, { useState } from 'react';
import { Input, Button, Menu, Dropdown, Tabs, Card, List, Empty, Tag, Space, Tooltip, Modal, Upload, Progress } from 'antd';
import {
  SearchOutlined,
  FolderOutlined,
  FolderAddOutlined,
  UploadOutlined,
  DownloadOutlined,
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  FileImageOutlined,
  FileUnknownOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  FileZipOutlined,
  FileExcelOutlined,
  FileMarkdownOutlined,
  AppstoreOutlined,
  BarsOutlined,
  SortAscendingOutlined,
  FilterOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import { setSelectedAssets, setCurrentFolder, AssetType } from '../../store/asset/assetSlice';

const { Search } = Input;
const { TabPane } = Tabs;
const { <PERSON>agger } = Upload;

const AssetsPanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  
  const { assets, folders, currentFolder, selectedAssets, isUploading, uploadProgress } = useAppSelector(
    (state) => state.asset
  );
  
  const [searchValue, setSearchValue] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'name' | 'type' | 'date'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  
  // 示例数据，实际应该从API获取
  const sampleFolders = [
    { id: 'models', name: 'Models', projectId: '1', createdAt: '2023-01-01', updatedAt: '2023-01-01' },
    { id: 'textures', name: 'Textures', projectId: '1', createdAt: '2023-01-01', updatedAt: '2023-01-01' },
    { id: 'materials', name: 'Materials', projectId: '1', createdAt: '2023-01-01', updatedAt: '2023-01-01' },
    { id: 'audio', name: 'Audio', projectId: '1', createdAt: '2023-01-01', updatedAt: '2023-01-01' },
  ];
  
  const sampleAssets = [
    {
      id: '1',
      name: 'Cube.glb',
      type: AssetType.MODEL,
      url: '/assets/models/cube.glb',
      thumbnail: '/assets/thumbnails/cube.png',
      projectId: '1',
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01',
    },
    {
      id: '2',
      name: 'Grass.jpg',
      type: AssetType.TEXTURE,
      url: '/assets/textures/grass.jpg',
      thumbnail: '/assets/thumbnails/grass.jpg',
      projectId: '1',
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01',
    },
    {
      id: '3',
      name: 'Metal.material',
      type: AssetType.MATERIAL,
      url: '/assets/materials/metal.material',
      projectId: '1',
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01',
    },
    {
      id: '4',
      name: 'Footstep.mp3',
      type: AssetType.AUDIO,
      url: '/assets/audio/footstep.mp3',
      projectId: '1',
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01',
    },
  ];
  
  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
  };
  
  // 处理文件夹点击
  const handleFolderClick = (folder: any) => {
    dispatch(setCurrentFolder(folder));
  };
  
  // 处理资产选择
  const handleAssetSelect = (asset: any) => {
    dispatch(setSelectedAssets([asset]));
  };
  
  // 处理资产双击
  const handleAssetDoubleClick = (asset: any) => {
    // 根据资产类型执行不同操作
    switch (asset.type) {
      case AssetType.MODEL:
        // 添加模型到场景
        break;
      case AssetType.TEXTURE:
        // 打开纹理预览
        break;
      case AssetType.MATERIAL:
        // 打开材质编辑器
        break;
      case AssetType.AUDIO:
        // 播放音频
        break;
      default:
        // 默认操作
        break;
    }
  };
  
  // 获取资产图标
  const getAssetIcon = (type: AssetType) => {
    switch (type) {
      case AssetType.MODEL:
        return <FileUnknownOutlined />;
      case AssetType.TEXTURE:
        return <FileImageOutlined />;
      case AssetType.MATERIAL:
        return <FilePdfOutlined />;
      case AssetType.AUDIO:
        return <FileTextOutlined />;
      case AssetType.SCRIPT:
        return <FileMarkdownOutlined />;
      case AssetType.PREFAB:
        return <FileZipOutlined />;
      case AssetType.SCENE:
        return <FileExcelOutlined />;
      default:
        return <FileUnknownOutlined />;
    }
  };
  
  // 获取资产类型标签颜色
  const getAssetTypeColor = (type: AssetType) => {
    switch (type) {
      case AssetType.MODEL:
        return 'blue';
      case AssetType.TEXTURE:
        return 'green';
      case AssetType.MATERIAL:
        return 'purple';
      case AssetType.AUDIO:
        return 'orange';
      case AssetType.SCRIPT:
        return 'red';
      case AssetType.PREFAB:
        return 'cyan';
      case AssetType.SCENE:
        return 'magenta';
      default:
        return 'default';
    }
  };
  
  // 文件夹菜单
  const folderMenu = (
    <Menu>
      <Menu.Item key="newFolder" icon={<FolderAddOutlined />}>
        {t('editor.newFolder')}
      </Menu.Item>
      <Menu.Item key="upload" icon={<UploadOutlined />} onClick={() => setUploadModalVisible(true)}>
        {t('editor.uploadAssets')}
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="refresh" icon={<SearchOutlined />}>
        {t('editor.refresh')}
      </Menu.Item>
    </Menu>
  );
  
  // 排序菜单
  const sortMenu = (
    <Menu>
      <Menu.Item key="name" onClick={() => setSortBy('name')}>
        {t('editor.sortByName')}
      </Menu.Item>
      <Menu.Item key="type" onClick={() => setSortBy('type')}>
        {t('editor.sortByType')}
      </Menu.Item>
      <Menu.Item key="date" onClick={() => setSortBy('date')}>
        {t('editor.sortByDate')}
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="asc" onClick={() => setSortOrder('asc')}>
        {t('editor.ascending')}
      </Menu.Item>
      <Menu.Item key="desc" onClick={() => setSortOrder('desc')}>
        {t('editor.descending')}
      </Menu.Item>
    </Menu>
  );
  
  // 渲染网格视图
  const renderGridView = () => {
    return (
      <div style={{ padding: '8px' }}>
        <List
          grid={{ gutter: 16, column: 4 }}
          dataSource={[...sampleFolders, ...sampleAssets]}
          renderItem={(item) => {
            if ('type' in item) {
              // 资产
              return (
                <List.Item>
                  <Card
                    hoverable
                    size="small"
                    cover={
                      item.thumbnail ? (
                        <img alt={item.name} src={item.thumbnail} style={{ height: 80, objectFit: 'cover' }} />
                      ) : (
                        <div style={{ height: 80, display: 'flex', justifyContent: 'center', alignItems: 'center', background: '#f0f0f0' }}>
                          {getAssetIcon(item.type)}
                        </div>
                      )
                    }
                    onClick={() => handleAssetSelect(item)}
                    onDoubleClick={() => handleAssetDoubleClick(item)}
                    style={{ 
                      border: selectedAssets.some(a => a.id === item.id) ? '2px solid #1890ff' : '1px solid #f0f0f0',
                    }}
                  >
                    <Card.Meta
                      title={item.name}
                      description={<Tag color={getAssetTypeColor(item.type)}>{item.type}</Tag>}
                    />
                  </Card>
                </List.Item>
              );
            } else {
              // 文件夹
              return (
                <List.Item>
                  <Card
                    hoverable
                    size="small"
                    onClick={() => handleFolderClick(item)}
                    style={{ border: '1px solid #f0f0f0' }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <FolderOutlined style={{ fontSize: 24, marginRight: 8, color: '#faad14' }} />
                      <div>{item.name}</div>
                    </div>
                  </Card>
                </List.Item>
              );
            }
          }}
        />
      </div>
    );
  };
  
  // 渲染列表视图
  const renderListView = () => {
    return (
      <List
        itemLayout="horizontal"
        dataSource={[...sampleFolders, ...sampleAssets]}
        renderItem={(item) => {
          if ('type' in item) {
            // 资产
            return (
              <List.Item
                actions={[
                  <Button type="text" icon={<EditOutlined />} />,
                  <Button type="text" icon={<DeleteOutlined />} />,
                ]}
                onClick={() => handleAssetSelect(item)}
                onDoubleClick={() => handleAssetDoubleClick(item)}
                style={{ 
                  background: selectedAssets.some(a => a.id === item.id) ? 'rgba(24, 144, 255, 0.1)' : 'transparent',
                }}
              >
                <List.Item.Meta
                  avatar={getAssetIcon(item.type)}
                  title={item.name}
                  description={<Tag color={getAssetTypeColor(item.type)}>{item.type}</Tag>}
                />
              </List.Item>
            );
          } else {
            // 文件夹
            return (
              <List.Item
                actions={[
                  <Button type="text" icon={<EditOutlined />} />,
                  <Button type="text" icon={<DeleteOutlined />} />,
                ]}
                onClick={() => handleFolderClick(item)}
              >
                <List.Item.Meta
                  avatar={<FolderOutlined style={{ color: '#faad14' }} />}
                  title={item.name}
                  description={t('editor.folder')}
                />
              </List.Item>
            );
          }
        }}
      />
    );
  };
  
  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <div style={{ padding: '8px', borderBottom: '1px solid #f0f0f0' }}>
        <Search
          placeholder={t('editor.searchAssets')}
          allowClear
          onChange={(e) => handleSearch(e.target.value)}
          style={{ width: '100%' }}
        />
      </div>
      
      <div style={{ display: 'flex', justifyContent: 'space-between', padding: '8px', borderBottom: '1px solid #f0f0f0' }}>
        <Space>
          <Dropdown overlay={folderMenu} placement="bottomLeft">
            <Button icon={<FolderAddOutlined />} size="small">
              {t('editor.new')}
            </Button>
          </Dropdown>
          <Button icon={<UploadOutlined />} size="small" onClick={() => setUploadModalVisible(true)}>
            {t('editor.upload')}
          </Button>
        </Space>
        
        <Space>
          <Tooltip title={t('editor.gridView')}>
            <Button
              type={viewMode === 'grid' ? 'primary' : 'default'}
              icon={<AppstoreOutlined />}
              size="small"
              onClick={() => setViewMode('grid')}
            />
          </Tooltip>
          <Tooltip title={t('editor.listView')}>
            <Button
              type={viewMode === 'list' ? 'primary' : 'default'}
              icon={<BarsOutlined />}
              size="small"
              onClick={() => setViewMode('list')}
            />
          </Tooltip>
          <Dropdown overlay={sortMenu} placement="bottomRight">
            <Button icon={<SortAscendingOutlined />} size="small" />
          </Dropdown>
        </Space>
      </div>
      
      <div style={{ flex: 1, overflow: 'auto' }}>
        <Tabs defaultActiveKey="all">
          <TabPane tab={t('editor.allAssets')} key="all">
            {viewMode === 'grid' ? renderGridView() : renderListView()}
          </TabPane>
          <TabPane tab={t('editor.models')} key="models">
            <Empty description={t('editor.noModels')} />
          </TabPane>
          <TabPane tab={t('editor.textures')} key="textures">
            <Empty description={t('editor.noTextures')} />
          </TabPane>
          <TabPane tab={t('editor.materials')} key="materials">
            <Empty description={t('editor.noMaterials')} />
          </TabPane>
        </Tabs>
      </div>
      
      <Modal
        title={t('editor.uploadAssets')}
        visible={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={null}
      >
        <Dragger multiple>
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">{t('editor.clickOrDragToUpload')}</p>
          <p className="ant-upload-hint">{t('editor.supportedFormats')}</p>
        </Dragger>
        
        {isUploading && (
          <div style={{ marginTop: 16 }}>
            <Progress percent={uploadProgress} status="active" />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default AssetsPanel;
