/**
 * 资源依赖面板样式
 */
.resource-dependency-panel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;

    h2 {
      margin: 0;
      font-size: 18px;
      color: #333;
    }
  }

  .panel-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .ant-tabs-content {
      flex: 1;
      overflow: hidden;

      .ant-tabs-tabpane {
        height: 100%;
        overflow: hidden;
      }
    }
  }

  .dependency-help {
    padding: 16px;
    background-color: #f9f9f9;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    margin: 0 16px 16px;

    h3 {
      font-size: 16px;
      margin-top: 0;
      margin-bottom: 12px;
      color: #333;
    }

    h4 {
      font-size: 14px;
      margin-top: 16px;
      margin-bottom: 8px;
      color: #333;
    }

    p {
      margin-bottom: 12px;
      color: #666;
    }

    ul {
      margin-bottom: 12px;
      padding-left: 20px;

      li {
        margin-bottom: 6px;
        color: #666;

        strong {
          color: #333;
        }
      }
    }
  }
}
