/**
 * 节点注册表
 * 用于管理和创建可视化脚本节点
 */

import { VisualScriptNode } from './VisualScriptNode';

export type NodeConstructor = new (...args: any[]) => VisualScriptNode;

export interface NodeDefinition {
  constructor: NodeConstructor;
  category: string;
  description: string;
  icon?: string;
  color?: string;
}

export class NodeRegistry {
  private static nodes: Map<string, NodeDefinition> = new Map();
  private static categories: Set<string> = new Set();

  /**
   * 注册节点类型
   */
  public static register(
    nodeType: string, 
    constructor: NodeConstructor, 
    category: string = 'General',
    description: string = '',
    icon?: string,
    color?: string
  ): void {
    this.nodes.set(nodeType, {
      constructor,
      category,
      description,
      icon,
      color
    });
    
    this.categories.add(category);
  }

  /**
   * 创建节点实例
   */
  public static createNode(nodeType: string, ...args: any[]): VisualScriptNode | null {
    const definition = this.nodes.get(nodeType);
    if (!definition) {
      console.warn(`未找到节点类型: ${nodeType}`);
      return null;
    }

    try {
      return new definition.constructor(...args);
    } catch (error) {
      console.error(`创建节点失败: ${nodeType}`, error);
      return null;
    }
  }

  /**
   * 获取节点定义
   */
  public static getNodeDefinition(nodeType: string): NodeDefinition | null {
    return this.nodes.get(nodeType) || null;
  }

  /**
   * 获取所有注册的节点类型
   */
  public static getRegisteredNodeTypes(): string[] {
    return Array.from(this.nodes.keys());
  }

  /**
   * 获取所有类别
   */
  public static getCategories(): string[] {
    return Array.from(this.categories);
  }

  /**
   * 根据类别获取节点类型
   */
  public static getNodeTypesByCategory(category: string): string[] {
    const result: string[] = [];
    
    for (const [nodeType, definition] of this.nodes.entries()) {
      if (definition.category === category) {
        result.push(nodeType);
      }
    }
    
    return result;
  }

  /**
   * 检查节点类型是否已注册
   */
  public static isRegistered(nodeType: string): boolean {
    return this.nodes.has(nodeType);
  }

  /**
   * 注销节点类型
   */
  public static unregister(nodeType: string): boolean {
    return this.nodes.delete(nodeType);
  }

  /**
   * 清空所有注册的节点
   */
  public static clear(): void {
    this.nodes.clear();
    this.categories.clear();
  }

  /**
   * 获取节点信息列表
   */
  public static getNodeInfoList(): Array<{
    nodeType: string;
    category: string;
    description: string;
    icon?: string;
    color?: string;
  }> {
    const result: Array<{
      nodeType: string;
      category: string;
      description: string;
      icon?: string;
      color?: string;
    }> = [];

    for (const [nodeType, definition] of this.nodes.entries()) {
      result.push({
        nodeType,
        category: definition.category,
        description: definition.description,
        icon: definition.icon,
        color: definition.color
      });
    }

    return result;
  }

  /**
   * 搜索节点
   */
  public static searchNodes(query: string): string[] {
    const lowerQuery = query.toLowerCase();
    const result: string[] = [];

    for (const [nodeType, definition] of this.nodes.entries()) {
      if (
        nodeType.toLowerCase().includes(lowerQuery) ||
        definition.description.toLowerCase().includes(lowerQuery) ||
        definition.category.toLowerCase().includes(lowerQuery)
      ) {
        result.push(nodeType);
      }
    }

    return result;
  }
}
