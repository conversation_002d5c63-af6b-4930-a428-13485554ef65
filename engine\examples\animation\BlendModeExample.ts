/**
 * 混合模式示例
 * 展示不同混合模式的使用方法
 */
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { AnimationBlender, BlendMode, BlendLayer } from '../../src/animation/AnimationBlender';
import { Animator } from '../../src/animation/Animator';
import { GUI } from 'dat.gui';

// 场景类
class BlendModeExample {
  // 场景
  private scene: THREE.Scene;
  // 相机
  private camera: THREE.PerspectiveCamera;
  // 渲染器
  private renderer: THREE.WebGLRenderer;
  // 控制器
  private controls: OrbitControls;
  // 时钟
  private clock: THREE.Clock;
  // 模型
  private model: THREE.Object3D | null = null;
  // 混合器
  private mixer: THREE.AnimationMixer | null = null;
  // 动画控制器
  private animator: Animator | null = null;
  // 混合器
  private blender: AnimationBlender | null = null;
  // 动画片段
  private animations: THREE.AnimationClip[] = [];
  // GUI
  private gui: GUI;
  // 混合层
  private layers: BlendLayer[] = [];
  // 混合模式选项
  private blendModeOptions = {
    OVERRIDE: BlendMode.OVERRIDE,
    ADDITIVE: BlendMode.ADDITIVE,
    MULTIPLY: BlendMode.MULTIPLY,
    DIFFERENCE: BlendMode.DIFFERENCE,
    AVERAGE: BlendMode.AVERAGE,
    MAX: BlendMode.MAX,
    MIN: BlendMode.MIN,
    CROSS_FADE: BlendMode.CROSS_FADE,
    BLEND_TREE: BlendMode.BLEND_TREE,
    WEIGHTED: BlendMode.WEIGHTED,
    LAYERED: BlendMode.LAYERED,
    SEQUENTIAL: BlendMode.SEQUENTIAL
  };

  /**
   * 构造函数
   */
  constructor() {
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0xa0a0a0);
    this.scene.fog = new THREE.Fog(0xa0a0a0, 10, 50);

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 100);
    this.camera.position.set(0, 2, 5);
    this.camera.lookAt(0, 1, 0);

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    document.body.appendChild(this.renderer.domElement);

    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 1, 0);
    this.controls.update();

    // 创建时钟
    this.clock = new THREE.Clock();

    // 创建灯光
    this.setupLights();

    // 创建地面
    this.setupGround();

    // 创建GUI
    this.gui = new GUI();
    this.gui.width = 300;

    // 加载模型
    this.loadModel();

    // 添加窗口大小调整监听器
    window.addEventListener('resize', this.onWindowResize.bind(this));

    // 开始动画循环
    this.animate();
  }

  /**
   * 设置灯光
   */
  private setupLights(): void {
    // 半球光
    const hemiLight = new THREE.HemisphereLight(0xffffff, 0x444444, 0.6);
    hemiLight.position.set(0, 20, 0);
    this.scene.add(hemiLight);

    // 方向光
    const dirLight = new THREE.DirectionalLight(0xffffff, 0.8);
    dirLight.position.set(3, 10, 10);
    dirLight.castShadow = true;
    dirLight.shadow.camera.top = 2;
    dirLight.shadow.camera.bottom = -2;
    dirLight.shadow.camera.left = -2;
    dirLight.shadow.camera.right = 2;
    dirLight.shadow.camera.near = 0.1;
    dirLight.shadow.camera.far = 40;
    this.scene.add(dirLight);
  }

  /**
   * 设置地面
   */
  private setupGround(): void {
    // 地面
    const ground = new THREE.Mesh(
      new THREE.PlaneGeometry(100, 100),
      new THREE.MeshPhongMaterial({ color: 0x999999, depthWrite: false })
    );
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    this.scene.add(ground);

    // 网格
    const grid = new THREE.GridHelper(100, 100, 0x000000, 0x000000);
    grid.material.opacity = 0.2;
    grid.material.transparent = true;
    this.scene.add(grid);
  }

  /**
   * 加载模型
   */
  private loadModel(): void {
    const loader = new GLTFLoader();
    loader.load(
      'models/gltf/Xbot.glb',
      (gltf) => {
        this.model = gltf.scene;
        this.model.traverse((object) => {
          if ((object as THREE.Mesh).isMesh) {
            object.castShadow = true;
          }
        });
        this.scene.add(this.model);

        // 获取动画
        this.animations = gltf.animations;

        // 创建混合器
        this.mixer = new THREE.AnimationMixer(this.model);

        // 创建动画控制器
        this.animator = new Animator(this.mixer, this.animations);

        // 创建混合器
        this.blender = new AnimationBlender(this.animator);

        // 设置默认混合层
        this.setupDefaultLayers();

        // 设置GUI
        this.setupGUI();
      },
      undefined,
      (error) => {
        console.error('加载模型时出错:', error);
      }
    );
  }

  /**
   * 设置默认混合层
   */
  private setupDefaultLayers(): void {
    if (!this.blender) return;

    // 清空混合层
    this.blender.clearLayers();
    this.layers = [];

    // 添加默认混合层
    const idleLayer: BlendLayer = {
      clipName: 'idle',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.OVERRIDE
    };
    this.blender.addLayer(idleLayer);
    this.layers.push(idleLayer);
  }

  /**
   * 设置GUI
   */
  private setupGUI(): void {
    if (!this.animator || !this.blender) return;

    // 清空GUI
    this.gui.destroy();
    this.gui = new GUI();
    this.gui.width = 300;

    // 添加动画列表
    const animationFolder = this.gui.addFolder('动画');
    const animationNames = this.animations.map(clip => clip.name);
    const animationController = {
      animation: animationNames[0],
      play: () => {
        const layer: BlendLayer = {
          clipName: animationController.animation,
          weight: 1.0,
          timeScale: 1.0,
          blendMode: BlendMode.OVERRIDE
        };
        this.blender!.clearLayers();
        this.blender!.addLayer(layer);
        this.layers = [layer];
        this.setupLayersGUI();
      }
    };
    animationFolder.add(animationController, 'animation', animationNames);
    animationFolder.add(animationController, 'play');
    animationFolder.open();

    // 添加混合层控制
    this.setupLayersGUI();

    // 添加混合模式示例
    const examplesFolder = this.gui.addFolder('混合模式示例');
    const exampleController = {
      showOverride: () => this.showOverrideExample(),
      showAdditive: () => this.showAdditiveExample(),
      showCrossFade: () => this.showCrossFadeExample(),
      showBlendTree: () => this.showBlendTreeExample(),
      showWeighted: () => this.showWeightedExample(),
      showLayered: () => this.showLayeredExample(),
      showSequential: () => this.showSequentialExample()
    };
    examplesFolder.add(exampleController, 'showOverride').name('覆盖示例');
    examplesFolder.add(exampleController, 'showAdditive').name('叠加示例');
    examplesFolder.add(exampleController, 'showCrossFade').name('交叉淡入淡出示例');
    examplesFolder.add(exampleController, 'showBlendTree').name('混合树示例');
    examplesFolder.add(exampleController, 'showWeighted').name('加权示例');
    examplesFolder.add(exampleController, 'showLayered').name('分层示例');
    examplesFolder.add(exampleController, 'showSequential').name('序列示例');
    examplesFolder.open();
  }

  /**
   * 设置混合层GUI
   */
  private setupLayersGUI(): void {
    if (!this.animator || !this.blender) return;

    // 移除旧的混合层文件夹
    const oldFolder = this.gui.__folders['混合层'];
    if (oldFolder) {
      this.gui.removeFolder(oldFolder);
    }

    // 添加混合层文件夹
    const layersFolder = this.gui.addFolder('混合层');

    // 添加混合层控制
    for (let i = 0; i < this.layers.length; i++) {
      const layer = this.layers[i];
      const layerFolder = layersFolder.addFolder(`层 ${i + 1}: ${layer.clipName}`);
      layerFolder.add(layer, 'weight', 0, 1, 0.01).name('权重').onChange(() => this.blender!.update(0));
      layerFolder.add(layer, 'timeScale', 0.1, 2, 0.1).name('时间缩放').onChange(() => this.blender!.update(0));
      layerFolder.add(layer, 'blendMode', this.blendModeOptions).name('混合模式').onChange(() => this.blender!.update(0));
      layerFolder.open();
    }

    // 添加新层按钮
    const layerController = {
      addLayer: () => {
        if (this.animations.length > 0) {
          const layer: BlendLayer = {
            clipName: this.animations[0].name,
            weight: 1.0,
            timeScale: 1.0,
            blendMode: BlendMode.ADDITIVE
          };
          this.blender!.addLayer(layer);
          this.layers.push(layer);
          this.setupLayersGUI();
        }
      },
      clearLayers: () => {
        this.blender!.clearLayers();
        this.layers = [];
        this.setupLayersGUI();
      }
    };
    layersFolder.add(layerController, 'addLayer').name('添加层');
    layersFolder.add(layerController, 'clearLayers').name('清空层');
    layersFolder.open();
  }

  /**
   * 显示覆盖示例
   */
  private showOverrideExample(): void {
    if (!this.blender) return;

    // 清空混合层
    this.blender.clearLayers();
    this.layers = [];

    // 添加行走动画层
    const walkLayer: BlendLayer = {
      clipName: 'walk',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.OVERRIDE
    };
    this.blender.addLayer(walkLayer);
    this.layers.push(walkLayer);

    // 更新GUI
    this.setupLayersGUI();
  }

  /**
   * 显示叠加示例
   */
  private showAdditiveExample(): void {
    if (!this.blender) return;

    // 清空混合层
    this.blender.clearLayers();
    this.layers = [];

    // 添加行走动画层
    const walkLayer: BlendLayer = {
      clipName: 'walk',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.OVERRIDE
    };
    this.blender.addLayer(walkLayer);
    this.layers.push(walkLayer);

    // 添加挥手动画层
    const waveLayer: BlendLayer = {
      clipName: 'wave',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.ADDITIVE
    };
    this.blender.addLayer(waveLayer);
    this.layers.push(waveLayer);

    // 更新GUI
    this.setupLayersGUI();
  }

  /**
   * 显示交叉淡入淡出示例
   */
  private showCrossFadeExample(): void {
    if (!this.blender) return;

    // 清空混合层
    this.blender.clearLayers();
    this.layers = [];

    // 添加行走动画层
    const walkLayer: BlendLayer = {
      clipName: 'walk',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.OVERRIDE
    };
    this.blender.addLayer(walkLayer);
    this.layers.push(walkLayer);

    // 添加跑步动画层
    const runLayer: BlendLayer = {
      clipName: 'run',
      weight: 0.5,
      timeScale: 1.0,
      blendMode: BlendMode.CROSS_FADE,
      crossFadeTime: 1.0
    };
    this.blender.addLayer(runLayer);
    this.layers.push(runLayer);

    // 更新GUI
    this.setupLayersGUI();
  }

  /**
   * 显示混合树示例
   */
  private showBlendTreeExample(): void {
    if (!this.blender) return;

    // 清空混合层
    this.blender.clearLayers();
    this.layers = [];

    // 添加混合树层
    const blendTreeLayer: BlendLayer = {
      clipName: 'idle',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.BLEND_TREE,
      blendTreeParams: {
        paramName: 'speed',
        paramValue: 0.5,
        blendType: '1D',
        nodes: [
          {
            clipName: 'idle',
            threshold: 0,
            weight: 0.5
          },
          {
            clipName: 'walk',
            threshold: 0.5,
            weight: 0.5
          },
          {
            clipName: 'run',
            threshold: 1,
            weight: 0
          }
        ]
      }
    };
    this.blender.addLayer(blendTreeLayer);
    this.layers.push(blendTreeLayer);

    // 更新GUI
    this.setupLayersGUI();

    // 添加混合树参数控制
    const blendTreeFolder = this.gui.addFolder('混合树参数');
    blendTreeFolder.add(blendTreeLayer.blendTreeParams!, 'paramValue', 0, 1, 0.01).name('速度').onChange(() => {
      this.blender!.update(0);
    });
    blendTreeFolder.open();
  }

  /**
   * 显示加权示例
   */
  private showWeightedExample(): void {
    if (!this.blender) return;

    // 清空混合层
    this.blender.clearLayers();
    this.layers = [];

    // 添加加权层
    const weightedLayer: BlendLayer = {
      clipName: 'idle',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.WEIGHTED,
      weightParams: {
        weights: {
          'idle': 0.2,
          'walk': 0.5,
          'run': 0.3
        },
        normalize: true
      }
    };
    this.blender.addLayer(weightedLayer);
    this.layers.push(weightedLayer);

    // 更新GUI
    this.setupLayersGUI();

    // 添加权重参数控制
    const weightedFolder = this.gui.addFolder('权重参数');
    weightedFolder.add(weightedLayer.weightParams!.weights, 'idle', 0, 1, 0.01).name('待机权重').onChange(() => {
      this.blender!.update(0);
    });
    weightedFolder.add(weightedLayer.weightParams!.weights, 'walk', 0, 1, 0.01).name('行走权重').onChange(() => {
      this.blender!.update(0);
    });
    weightedFolder.add(weightedLayer.weightParams!.weights, 'run', 0, 1, 0.01).name('跑步权重').onChange(() => {
      this.blender!.update(0);
    });
    weightedFolder.open();
  }

  /**
   * 显示分层示例
   */
  private showLayeredExample(): void {
    if (!this.blender) return;

    // 清空混合层
    this.blender.clearLayers();
    this.layers = [];

    // 添加分层层
    const layeredLayer: BlendLayer = {
      clipName: 'idle',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.LAYERED,
      layerParams: {
        layers: [
          {
            clipName: 'walk',
            layerIndex: 0,
            weight: 1.0,
            mask: ['leftLeg', 'rightLeg']
          },
          {
            clipName: 'wave',
            layerIndex: 1,
            weight: 1.0,
            mask: ['leftArm', 'rightArm']
          }
        ]
      }
    };
    this.blender.addLayer(layeredLayer);
    this.layers.push(layeredLayer);

    // 更新GUI
    this.setupLayersGUI();

    // 添加分层参数控制
    const layeredFolder = this.gui.addFolder('分层参数');
    layeredFolder.add(layeredLayer.layerParams!.layers[0], 'weight', 0, 1, 0.01).name('腿部权重').onChange(() => {
      this.blender!.update(0);
    });
    layeredFolder.add(layeredLayer.layerParams!.layers[1], 'weight', 0, 1, 0.01).name('手臂权重').onChange(() => {
      this.blender!.update(0);
    });
    layeredFolder.open();
  }

  /**
   * 显示序列示例
   */
  private showSequentialExample(): void {
    if (!this.blender) return;

    // 清空混合层
    this.blender.clearLayers();
    this.layers = [];

    // 添加序列层
    const sequentialLayer: BlendLayer = {
      clipName: 'idle',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.SEQUENTIAL,
      sequenceParams: {
        sequence: [
          {
            clipName: 'idle',
            duration: 2.0,
            transitionTime: 0.5
          },
          {
            clipName: 'walk',
            duration: 3.0,
            transitionTime: 0.5
          },
          {
            clipName: 'run',
            duration: 2.0,
            transitionTime: 0.5
          },
          {
            clipName: 'jump',
            duration: 1.0,
            transitionTime: 0.5
          }
        ],
        currentIndex: 0,
        loop: true
      }
    };
    this.blender.addLayer(sequentialLayer);
    this.layers.push(sequentialLayer);

    // 更新GUI
    this.setupLayersGUI();
  }

  /**
   * 窗口大小调整处理函数
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 动画循环
   */
  private animate(): void {
    requestAnimationFrame(this.animate.bind(this));

    const delta = this.clock.getDelta();

    // 更新控制器
    this.controls.update();

    // 更新混合器
    if (this.mixer) {
      this.mixer.update(delta);
    }

    // 更新混合器
    if (this.blender) {
      this.blender.update(delta);
    }

    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }
}

// 创建示例
new BlendModeExample();
