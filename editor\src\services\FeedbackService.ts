/**
 * 反馈服务
 * 用于处理用户反馈的提交和管理
 */
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 反馈事件类型
 */
export enum FeedbackEventType {
  /** 提交开始 */
  SUBMIT_START = 'submit_start',
  /** 提交完成 */
  SUBMIT_COMPLETE = 'submit_complete',
  /** 提交错误 */
  SUBMIT_ERROR = 'submit_error',
  /** 获取开始 */
  FETCH_START = 'fetch_start',
  /** 获取完成 */
  FETCH_COMPLETE = 'fetch_complete',
  /** 获取错误 */
  FETCH_ERROR = 'fetch_error',
  /** 新反馈 */
  NEW_FEEDBACK = 'new_feedback',
  /** 更新反馈 */
  UPDATE_FEEDBACK = 'update_feedback',
  /** 删除反馈 */
  DELETE_FEEDBACK = 'delete_feedback',
  /** 添加评论 */
  ADD_COMMENT = 'add_comment',
}

/**
 * 反馈评论接口
 */
export interface FeedbackComment {
  /** 评论ID */
  id: string;
  /** 反馈ID */
  feedbackId: string;
  /** 用户ID */
  userId: string;
  /** 用户名 */
  userName: string;
  /** 评论内容 */
  content: string;
  /** 时间戳 */
  timestamp: string;
  /** 是否是工作人员 */
  isStaff?: boolean;
}

/**
 * 反馈数据接口
 */
export interface FeedbackData {
  /** 反馈ID */
  id?: string;
  /** 反馈类型 */
  type: string;
  /** 反馈子类型 */
  subType?: string;
  /** 反馈标题 */
  title: string;
  /** 反馈描述 */
  description: string;
  /** 反馈类别 */
  feedbackType: 'bug' | 'feature' | 'improvement' | 'performance' | 'usability' | 'other';
  /** 问题类别 */
  issueCategory?: string;
  /** 重现步骤 */
  reproductionSteps?: string;
  /** 满意度评分 */
  satisfaction?: number;
  /** 改进建议 */
  suggestions?: string;
  /** 截图 */
  screenshots?: File[] | string[];
  /** 允许联系 */
  allowContact?: boolean;
  /** 相关实体ID */
  entityId?: string;
  /** 相关实体名称 */
  entityName?: string;
  /** 相关资源ID */
  resourceId?: string;
  /** 时间戳 */
  timestamp?: string;
  /** 浏览器信息 */
  browser?: string;
  /** 屏幕尺寸 */
  screenSize?: string;
  /** 性能数据 */
  performanceData?: any;
  /** 上下文数据 */
  contextData?: any;
  /** 用户ID */
  userId?: string;
  /** 用户名 */
  userName?: string;
  /** 状态 */
  status?: 'new' | 'inProgress' | 'resolved' | 'closed';
  /** 评论 */
  comments?: FeedbackComment[];
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
}

/**
 * 反馈趋势数据接口
 */
export interface FeedbackTrendItem {
  /** 日期 */
  date: string;
  /** 数量 */
  count: number;
  /** 按类型统计 */
  byType: Record<string, number>;
}

/**
 * 反馈统计数据接口
 */
export interface FeedbackStats {
  /** 总反馈数 */
  total: number;
  /** 按类型统计 */
  byType: Record<string, number>;
  /** 按子类型统计 */
  bySubType: Record<string, number>;
  /** 按反馈类别统计 */
  byFeedbackType: Record<string, number>;
  /** 按状态统计 */
  byStatus: Record<string, number>;
  /** 平均满意度 */
  averageSatisfaction: number;
  /** 最近反馈 */
  recentFeedback: FeedbackData[];
  /** 趋势数据 */
  trendData?: FeedbackTrendItem[];
}

/**
 * 反馈服务类
 */
class FeedbackServiceClass {
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** API URL */
  private apiUrl: string = '/api/feedback';
  /** 本地存储键 */
  private storageKey: string = 'ir_engine_feedback';
  /** 是否使用本地存储 */
  private useLocalStorage: boolean = process.env.NODE_ENV !== 'production';
  /** 本地反馈数据 */
  private localFeedback: FeedbackData[] = [];

  /**
   * 构造函数
   */
  constructor() {
    // 从本地存储加载反馈数据
    if (this.useLocalStorage) {
      this.loadFromLocalStorage();
    }
  }

  /**
   * 从本地存储加载反馈数据
   */
  private loadFromLocalStorage(): void {
    try {
      const storedData = localStorage.getItem(this.storageKey);
      if (storedData) {
        this.localFeedback = JSON.parse(storedData);
      }
    } catch (error) {
      console.error('从本地存储加载反馈数据失败:', error);
      this.localFeedback = [];
    }
  }

  /**
   * 保存反馈数据到本地存储
   */
  private saveToLocalStorage(): void {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.localFeedback));
    } catch (error) {
      console.error('保存反馈数据到本地存储失败:', error);
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器函数
   */
  public addEventListener(event: FeedbackEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器函数
   */
  public removeEventListener(event: FeedbackEventType, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 提交反馈
   * @param data 反馈数据
   * @returns 提交的反馈数据
   */
  public async submitFeedback(data: FeedbackData): Promise<FeedbackData> {
    try {
      // 发出提交开始事件
      this.eventEmitter.emit(FeedbackEventType.SUBMIT_START, data);

      // 生成ID和时间戳
      const feedbackId = uuidv4();
      const timestamp = new Date().toISOString();

      // 准备反馈数据
      const feedbackData: FeedbackData = {
        ...data,
        id: feedbackId,
        timestamp,
        createdAt: timestamp,
        updatedAt: timestamp
      };

      // 如果是生产环境，则提交到API
      if (!this.useLocalStorage) {
        const response = await axios.post(this.apiUrl, feedbackData);

        // 发出提交完成事件
        this.eventEmitter.emit(FeedbackEventType.SUBMIT_COMPLETE, response.data);
        this.eventEmitter.emit(FeedbackEventType.NEW_FEEDBACK, response.data);

        return response.data;
      }

      // 否则，保存到本地存储
      this.localFeedback.push(feedbackData);
      this.saveToLocalStorage();

      // 发出提交完成事件
      this.eventEmitter.emit(FeedbackEventType.SUBMIT_COMPLETE, feedbackData);
      this.eventEmitter.emit(FeedbackEventType.NEW_FEEDBACK, feedbackData);

      return feedbackData;
    } catch (error) {
      console.error('提交反馈失败:', error);

      // 发出提交错误事件
      this.eventEmitter.emit(FeedbackEventType.SUBMIT_ERROR, error);

      throw error;
    }
  }

  /**
   * 获取反馈列表
   * @param filters 过滤条件
   * @returns 反馈列表
   */
  public async getFeedbackList(filters?: Partial<FeedbackData>): Promise<FeedbackData[]> {
    try {
      // 发出获取开始事件
      this.eventEmitter.emit(FeedbackEventType.FETCH_START, filters);

      // 如果是生产环境，则从API获取
      if (!this.useLocalStorage) {
        const response = await axios.get(this.apiUrl, { params: filters });

        // 发出获取完成事件
        this.eventEmitter.emit(FeedbackEventType.FETCH_COMPLETE, response.data);

        return response.data;
      }

      // 否则，从本地存储获取并过滤
      let result = [...this.localFeedback];

      // 应用过滤条件
      if (filters) {
        result = result.filter(feedback => {
          for (const key in filters) {
            if (filters[key as keyof FeedbackData] !== undefined &&
                feedback[key as keyof FeedbackData] !== filters[key as keyof FeedbackData]) {
              return false;
            }
          }
          return true;
        });
      }

      // 发出获取完成事件
      this.eventEmitter.emit(FeedbackEventType.FETCH_COMPLETE, result);

      return result;
    } catch (error) {
      console.error('获取反馈列表失败:', error);

      // 发出获取错误事件
      this.eventEmitter.emit(FeedbackEventType.FETCH_ERROR, error);

      throw error;
    }
  }

  /**
   * 获取反馈统计数据
   * @param filters 过滤条件
   * @returns 反馈统计数据
   */
  public async getFeedbackStats(filters?: Partial<FeedbackData>): Promise<FeedbackStats> {
    try {
      // 获取反馈列表
      const feedbackList = await this.getFeedbackList(filters);

      // 计算统计数据
      const stats: FeedbackStats = {
        total: feedbackList.length,
        byType: {},
        bySubType: {},
        byFeedbackType: {},
        byStatus: {},
        averageSatisfaction: 0,
        recentFeedback: [],
        trendData: []
      };

      // 计算各种统计
      let totalSatisfaction = 0;
      let satisfactionCount = 0;

      // 用于计算趋势数据的映射
      const trendMap: Record<string, { count: number; byType: Record<string, number> }> = {};

      for (const feedback of feedbackList) {
        // 按类型统计
        if (feedback.type) {
          stats.byType[feedback.type] = (stats.byType[feedback.type] || 0) + 1;
        }

        // 按子类型统计
        if (feedback.subType) {
          stats.bySubType[feedback.subType] = (stats.bySubType[feedback.subType] || 0) + 1;
        }

        // 按反馈类别统计
        if (feedback.feedbackType) {
          stats.byFeedbackType[feedback.feedbackType] = (stats.byFeedbackType[feedback.feedbackType] || 0) + 1;
        }

        // 按状态统计
        const status = feedback.status || 'new';
        stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;

        // 计算满意度总和
        if (feedback.satisfaction !== undefined) {
          totalSatisfaction += feedback.satisfaction;
          satisfactionCount++;
        }

        // 计算趋势数据
        if (feedback.createdAt) {
          const date = new Date(feedback.createdAt);
          const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD

          if (!trendMap[dateStr]) {
            trendMap[dateStr] = { count: 0, byType: {} };
          }

          trendMap[dateStr].count++;

          if (feedback.feedbackType) {
            trendMap[dateStr].byType[feedback.feedbackType] = (trendMap[dateStr].byType[feedback.feedbackType] || 0) + 1;
          }
        }
      }

      // 计算平均满意度
      stats.averageSatisfaction = satisfactionCount > 0 ? totalSatisfaction / satisfactionCount : 0;

      // 获取最近反馈
      stats.recentFeedback = [...feedbackList]
        .sort((a, b) => {
          const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
          const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
          return dateB - dateA;
        })
        .slice(0, 5);

      // 转换趋势数据
      stats.trendData = Object.entries(trendMap)
        .map(([date, data]) => ({
          date,
          count: data.count,
          byType: data.byType
        }))
        .sort((a, b) => a.date.localeCompare(b.date));

      return stats;
    } catch (error) {
      console.error('获取反馈统计数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取反馈详情
   * @param id 反馈ID
   * @returns 反馈详情
   */
  public async getFeedbackDetail(id: string): Promise<FeedbackData> {
    try {
      // 如果是生产环境，则从API获取
      if (!this.useLocalStorage) {
        const response = await axios.get(`${this.apiUrl}/${id}`);
        return response.data;
      }

      // 否则，从本地存储获取
      const feedback = this.localFeedback.find(item => item.id === id);

      if (!feedback) {
        throw new Error(`未找到ID为 ${id} 的反馈`);
      }

      return feedback;
    } catch (error) {
      console.error('获取反馈详情失败:', error);
      throw error;
    }
  }

  /**
   * 更新反馈
   * @param id 反馈ID
   * @param data 更新数据
   * @returns 更新后的反馈数据
   */
  public async updateFeedback(id: string, data: Partial<FeedbackData>): Promise<FeedbackData> {
    try {
      // 如果是生产环境，则通过API更新
      if (!this.useLocalStorage) {
        const response = await axios.put(`${this.apiUrl}/${id}`, data);

        // 发出更新事件
        this.eventEmitter.emit(FeedbackEventType.UPDATE_FEEDBACK, response.data);

        return response.data;
      }

      // 否则，更新本地存储
      const index = this.localFeedback.findIndex(item => item.id === id);

      if (index === -1) {
        throw new Error(`未找到ID为 ${id} 的反馈`);
      }

      // 更新数据
      const updatedFeedback = {
        ...this.localFeedback[index],
        ...data,
        updatedAt: new Date().toISOString()
      };

      this.localFeedback[index] = updatedFeedback;
      this.saveToLocalStorage();

      // 发出更新事件
      this.eventEmitter.emit(FeedbackEventType.UPDATE_FEEDBACK, updatedFeedback);

      return updatedFeedback;
    } catch (error) {
      console.error('更新反馈失败:', error);
      throw error;
    }
  }

  /**
   * 删除反馈
   * @param id 反馈ID
   * @returns 是否删除成功
   */
  public async deleteFeedback(id: string): Promise<boolean> {
    try {
      // 如果是生产环境，则通过API删除
      if (!this.useLocalStorage) {
        await axios.delete(`${this.apiUrl}/${id}`);

        // 发出删除事件
        this.eventEmitter.emit(FeedbackEventType.DELETE_FEEDBACK, id);

        return true;
      }

      // 否则，从本地存储删除
      const index = this.localFeedback.findIndex(item => item.id === id);

      if (index === -1) {
        throw new Error(`未找到ID为 ${id} 的反馈`);
      }

      this.localFeedback.splice(index, 1);
      this.saveToLocalStorage();

      // 发出删除事件
      this.eventEmitter.emit(FeedbackEventType.DELETE_FEEDBACK, id);

      return true;
    } catch (error) {
      console.error('删除反馈失败:', error);
      throw error;
    }
  }

  /**
   * 添加反馈评论
   * @param feedbackId 反馈ID
   * @param content 评论内容
   * @param isStaff 是否是工作人员
   * @returns 添加的评论
   */
  public async addFeedbackComment(feedbackId: string, content: string, isStaff: boolean = true): Promise<FeedbackComment> {
    try {
      // 创建评论对象
      const comment: FeedbackComment = {
        id: uuidv4(),
        feedbackId,
        userId: 'staff', // 默认为工作人员
        userName: isStaff ? '工作人员' : '用户',
        content,
        timestamp: new Date().toISOString(),
        isStaff
      };

      // 如果是生产环境，则通过API添加
      if (!this.useLocalStorage) {
        const response = await axios.post(`${this.apiUrl}/${feedbackId}/comments`, comment);

        // 发出添加评论事件
        this.eventEmitter.emit(FeedbackEventType.ADD_COMMENT, response.data);

        return response.data;
      }

      // 否则，添加到本地存储
      const index = this.localFeedback.findIndex(item => item.id === feedbackId);

      if (index === -1) {
        throw new Error(`未找到ID为 ${feedbackId} 的反馈`);
      }

      // 确保comments数组存在
      if (!this.localFeedback[index].comments) {
        this.localFeedback[index].comments = [];
      }

      // 添加评论
      this.localFeedback[index].comments!.push(comment);

      // 更新反馈的更新时间
      this.localFeedback[index].updatedAt = new Date().toISOString();

      this.saveToLocalStorage();

      // 发出添加评论事件
      this.eventEmitter.emit(FeedbackEventType.ADD_COMMENT, comment);

      return comment;
    } catch (error) {
      console.error('添加反馈评论失败:', error);
      throw error;
    }
  }

  /**
   * 导出反馈数据
   * @param format 导出格式
   * @param filters 过滤条件
   * @returns 导出的数据
   */
  public async exportFeedback(format: 'excel' | 'pdf' | 'json', filters?: Partial<FeedbackData>): Promise<any> {
    try {
      // 获取反馈列表
      const feedbackList = await this.getFeedbackList(filters);

      // 如果是生产环境，则通过API导出
      if (!this.useLocalStorage) {
        const response = await axios.post(`${this.apiUrl}/export`, {
          format,
          filters
        }, {
          responseType: 'blob'
        });

        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `feedback_export_${new Date().toISOString()}.${format}`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        return true;
      }

      // 否则，直接导出本地数据
      if (format === 'json') {
        // 创建JSON文件
        const jsonData = JSON.stringify(feedbackList, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        // 创建下载链接
        const link = document.createElement('a');
        link.href = url;
        link.download = `feedback_export_${new Date().toISOString()}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        return true;
      }

      // 其他格式暂不支持本地导出
      throw new Error(`本地模式不支持导出为 ${format} 格式`);
    } catch (error) {
      console.error('导出反馈数据失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
export const FeedbackService = new FeedbackServiceClass();
