/**
 * 响应式控件组件
 * 提供自适应不同设备和屏幕尺寸的控件
 */
import React, { useState, useEffect, ReactNode } from 'react';
import { Button, Input, Select, Checkbox, Radio, Slider, Switch, Tooltip } from 'antd';
import { ButtonProps } from 'antd/lib/button';
import { InputProps } from 'antd/lib/input';
import { SelectProps } from 'antd/lib/select';
import { CheckboxProps } from 'antd/lib/checkbox';
import { RadioProps } from 'antd/lib/radio';
import { SliderProps } from 'antd/lib/slider';
import { SwitchProps } from 'antd/lib/switch';
import ResponsiveDesignService, { ControlSize } from '../../services/ResponsiveDesignService';
import './ResponsiveControls.less';

// 响应式按钮属性接口
export interface ResponsiveButtonProps extends ButtonProps {
  // 是否启用触控优化
  touchFriendly?: boolean;
  // 控件尺寸（覆盖自动尺寸）
  controlSize?: ControlSize;
  // 提示文本
  tooltip?: string;
  // 提示位置
  tooltipPlacement?: 'top' | 'right' | 'bottom' | 'left';
}

// 响应式输入框属性接口
export interface ResponsiveInputProps extends InputProps {
  // 是否启用触控优化
  touchFriendly?: boolean;
  // 控件尺寸（覆盖自动尺寸）
  controlSize?: ControlSize;
  // 提示文本
  tooltip?: string;
  // 提示位置
  tooltipPlacement?: 'top' | 'right' | 'bottom' | 'left';
}

// 响应式选择器属性接口
export interface ResponsiveSelectProps extends SelectProps<any> {
  // 是否启用触控优化
  touchFriendly?: boolean;
  // 控件尺寸（覆盖自动尺寸）
  controlSize?: ControlSize;
  // 提示文本
  tooltip?: string;
  // 提示位置
  tooltipPlacement?: 'top' | 'right' | 'bottom' | 'left';
}

// 响应式复选框属性接口
export interface ResponsiveCheckboxProps extends CheckboxProps {
  // 是否启用触控优化
  touchFriendly?: boolean;
  // 控件尺寸（覆盖自动尺寸）
  controlSize?: ControlSize;
  // 提示文本
  tooltip?: string;
  // 提示位置
  tooltipPlacement?: 'top' | 'right' | 'bottom' | 'left';
}

// 响应式单选框属性接口
export interface ResponsiveRadioProps extends RadioProps {
  // 是否启用触控优化
  touchFriendly?: boolean;
  // 控件尺寸（覆盖自动尺寸）
  controlSize?: ControlSize;
  // 提示文本
  tooltip?: string;
  // 提示位置
  tooltipPlacement?: 'top' | 'right' | 'bottom' | 'left';
}

// 响应式滑块属性接口
export interface ResponsiveSliderProps extends SliderProps {
  // 是否启用触控优化
  touchFriendly?: boolean;
  // 控件尺寸（覆盖自动尺寸）
  controlSize?: ControlSize;
  // 提示文本
  tooltip?: string;
  // 提示位置
  tooltipPlacement?: 'top' | 'right' | 'bottom' | 'left';
}

// 响应式开关属性接口
export interface ResponsiveSwitchProps extends SwitchProps {
  // 是否启用触控优化
  touchFriendly?: boolean;
  // 控件尺寸（覆盖自动尺寸）
  controlSize?: ControlSize;
  // 提示文本
  tooltip?: string;
  // 提示位置
  tooltipPlacement?: 'top' | 'right' | 'bottom' | 'left';
}

/**
 * 响应式按钮组件
 */
export const ResponsiveButton: React.FC<ResponsiveButtonProps> = ({
  touchFriendly = true,
  controlSize,
  tooltip,
  tooltipPlacement = 'top',
  className = '',
  ...props
}) => {
  const [currentControlSize, setCurrentControlSize] = useState<ControlSize>(
    controlSize || ResponsiveDesignService.getCurrentControlSize()
  );
  const [isTouchMode, setIsTouchMode] = useState<boolean>(
    ResponsiveDesignService.isTouchMode()
  );

  // 监听控件尺寸变化
  useEffect(() => {
    const handleControlSizeChange = (data: any) => {
      if (!controlSize) {
        setCurrentControlSize(data.newControlSize);
      }
    };

    const handleTouchModeChange = (data: any) => {
      setIsTouchMode(data.newTouchMode);
    };

    // 添加事件监听
    ResponsiveDesignService.on('controlSizeChanged', handleControlSizeChange);
    ResponsiveDesignService.on('touchModeChanged', handleTouchModeChange);

    // 清理函数
    return () => {
      ResponsiveDesignService.off('controlSizeChanged', handleControlSizeChange);
      ResponsiveDesignService.off('touchModeChanged', handleTouchModeChange);
    };
  }, [controlSize]);

  // 获取按钮尺寸
  const getButtonSize = (): 'large' | 'middle' | 'small' => {
    if (currentControlSize === ControlSize.EXTRA_LARGE) {
      return 'large';
    } else if (currentControlSize === ControlSize.LARGE) {
      return 'large';
    } else if (currentControlSize === ControlSize.MEDIUM) {
      return 'middle';
    } else {
      return 'small';
    }
  };

  // 获取按钮类名
  const getButtonClassName = (): string => {
    return `responsive-button ${className} 
      ${touchFriendly && isTouchMode ? 'touch-friendly' : ''} 
      size-${currentControlSize.toLowerCase()}`;
  };

  // 渲染按钮
  const renderButton = () => (
    <Button
      {...props}
      className={getButtonClassName()}
      size={getButtonSize()}
    />
  );

  // 如果有提示，则包装在Tooltip中
  if (tooltip) {
    return (
      <Tooltip title={tooltip} placement={tooltipPlacement}>
        {renderButton()}
      </Tooltip>
    );
  }

  return renderButton();
};

/**
 * 响应式输入框组件
 */
export const ResponsiveInput: React.FC<ResponsiveInputProps> = ({
  touchFriendly = true,
  controlSize,
  tooltip,
  tooltipPlacement = 'top',
  className = '',
  ...props
}) => {
  const [currentControlSize, setCurrentControlSize] = useState<ControlSize>(
    controlSize || ResponsiveDesignService.getCurrentControlSize()
  );
  const [isTouchMode, setIsTouchMode] = useState<boolean>(
    ResponsiveDesignService.isTouchMode()
  );

  // 监听控件尺寸变化
  useEffect(() => {
    const handleControlSizeChange = (data: any) => {
      if (!controlSize) {
        setCurrentControlSize(data.newControlSize);
      }
    };

    const handleTouchModeChange = (data: any) => {
      setIsTouchMode(data.newTouchMode);
    };

    // 添加事件监听
    ResponsiveDesignService.on('controlSizeChanged', handleControlSizeChange);
    ResponsiveDesignService.on('touchModeChanged', handleTouchModeChange);

    // 清理函数
    return () => {
      ResponsiveDesignService.off('controlSizeChanged', handleControlSizeChange);
      ResponsiveDesignService.off('touchModeChanged', handleTouchModeChange);
    };
  }, [controlSize]);

  // 获取输入框尺寸
  const getInputSize = (): 'large' | 'middle' | 'small' => {
    if (currentControlSize === ControlSize.EXTRA_LARGE) {
      return 'large';
    } else if (currentControlSize === ControlSize.LARGE) {
      return 'large';
    } else if (currentControlSize === ControlSize.MEDIUM) {
      return 'middle';
    } else {
      return 'small';
    }
  };

  // 获取输入框类名
  const getInputClassName = (): string => {
    return `responsive-input ${className} 
      ${touchFriendly && isTouchMode ? 'touch-friendly' : ''} 
      size-${currentControlSize.toLowerCase()}`;
  };

  // 渲染输入框
  const renderInput = () => (
    <Input
      {...props}
      className={getInputClassName()}
      size={getInputSize()}
    />
  );

  // 如果有提示，则包装在Tooltip中
  if (tooltip) {
    return (
      <Tooltip title={tooltip} placement={tooltipPlacement}>
        {renderInput()}
      </Tooltip>
    );
  }

  return renderInput();
};

/**
 * 响应式选择器组件
 */
export const ResponsiveSelect: React.FC<ResponsiveSelectProps> = ({
  touchFriendly = true,
  controlSize,
  tooltip,
  tooltipPlacement = 'top',
  className = '',
  ...props
}) => {
  const [currentControlSize, setCurrentControlSize] = useState<ControlSize>(
    controlSize || ResponsiveDesignService.getCurrentControlSize()
  );
  const [isTouchMode, setIsTouchMode] = useState<boolean>(
    ResponsiveDesignService.isTouchMode()
  );

  // 监听控件尺寸变化
  useEffect(() => {
    const handleControlSizeChange = (data: any) => {
      if (!controlSize) {
        setCurrentControlSize(data.newControlSize);
      }
    };

    const handleTouchModeChange = (data: any) => {
      setIsTouchMode(data.newTouchMode);
    };

    // 添加事件监听
    ResponsiveDesignService.on('controlSizeChanged', handleControlSizeChange);
    ResponsiveDesignService.on('touchModeChanged', handleTouchModeChange);

    // 清理函数
    return () => {
      ResponsiveDesignService.off('controlSizeChanged', handleControlSizeChange);
      ResponsiveDesignService.off('touchModeChanged', handleTouchModeChange);
    };
  }, [controlSize]);

  // 获取选择器尺寸
  const getSelectSize = (): 'large' | 'middle' | 'small' => {
    if (currentControlSize === ControlSize.EXTRA_LARGE) {
      return 'large';
    } else if (currentControlSize === ControlSize.LARGE) {
      return 'large';
    } else if (currentControlSize === ControlSize.MEDIUM) {
      return 'middle';
    } else {
      return 'small';
    }
  };

  // 获取选择器类名
  const getSelectClassName = (): string => {
    return `responsive-select ${className} 
      ${touchFriendly && isTouchMode ? 'touch-friendly' : ''} 
      size-${currentControlSize.toLowerCase()}`;
  };

  // 渲染选择器
  const renderSelect = () => (
    <Select
      {...props}
      className={getSelectClassName()}
      size={getSelectSize()}
    />
  );

  // 如果有提示，则包装在Tooltip中
  if (tooltip) {
    return (
      <Tooltip title={tooltip} placement={tooltipPlacement}>
        {renderSelect()}
      </Tooltip>
    );
  }

  return renderSelect();
};

// 导出其他响应式控件组件
export {
  ResponsiveButton as Button,
  ResponsiveInput as Input,
  ResponsiveSelect as Select
};
