/**
 * 设备信息工具
 * 提供获取浏览器、操作系统和屏幕尺寸等信息的工具函数
 */

/**
 * 获取浏览器信息
 * @returns 浏览器名称和版本
 */
export function getBrowserInfo(): string {
  const userAgent = navigator.userAgent;
  let browserName = '';
  let browserVersion = '';

  // 检测常见浏览器
  if (userAgent.indexOf('Firefox') > -1) {
    browserName = 'Firefox';
    browserVersion = userAgent.match(/Firefox\/([\d.]+)/)![1];
  } else if (userAgent.indexOf('Chrome') > -1 && userAgent.indexOf('Edge') === -1 && userAgent.indexOf('Edg') === -1) {
    browserName = 'Chrome';
    browserVersion = userAgent.match(/Chrome\/([\d.]+)/)![1];
  } else if (userAgent.indexOf('Safari') > -1 && userAgent.indexOf('Chrome') === -1) {
    browserName = 'Safari';
    browserVersion = userAgent.match(/Version\/([\d.]+)/)![1];
  } else if (userAgent.indexOf('Edge') > -1) {
    browserName = 'Edge (Legacy)';
    browserVersion = userAgent.match(/Edge\/([\d.]+)/)![1];
  } else if (userAgent.indexOf('Edg') > -1) {
    browserName = 'Edge (Chromium)';
    browserVersion = userAgent.match(/Edg\/([\d.]+)/)![1];
  } else if (userAgent.indexOf('MSIE') > -1 || userAgent.indexOf('Trident') > -1) {
    browserName = 'Internet Explorer';
    browserVersion = userAgent.match(/(?:MSIE |rv:)([\d.]+)/)![1];
  } else {
    browserName = '未知浏览器';
    browserVersion = '未知版本';
  }

  return `${browserName} ${browserVersion}`;
}

/**
 * 获取操作系统信息
 * @returns 操作系统名称和版本
 */
export function getOSInfo(): string {
  const userAgent = navigator.userAgent;
  let osName = '';
  let osVersion = '';

  // 检测常见操作系统
  if (userAgent.indexOf('Windows NT') > -1) {
    osName = 'Windows';
    const ntVersion = userAgent.match(/Windows NT ([\d.]+)/)![1];
    
    // 转换NT版本到Windows版本
    switch (ntVersion) {
      case '10.0':
        osVersion = '10/11';
        break;
      case '6.3':
        osVersion = '8.1';
        break;
      case '6.2':
        osVersion = '8';
        break;
      case '6.1':
        osVersion = '7';
        break;
      case '6.0':
        osVersion = 'Vista';
        break;
      case '5.2':
      case '5.1':
        osVersion = 'XP';
        break;
      default:
        osVersion = ntVersion;
    }
  } else if (userAgent.indexOf('Mac OS X') > -1) {
    osName = 'macOS';
    osVersion = userAgent.match(/Mac OS X ([0-9_\.]+)/)![1].replace(/_/g, '.');
  } else if (userAgent.indexOf('Android') > -1) {
    osName = 'Android';
    osVersion = userAgent.match(/Android ([\d.]+)/)![1];
  } else if (userAgent.indexOf('iOS') > -1 || userAgent.indexOf('iPhone') > -1 || userAgent.indexOf('iPad') > -1) {
    osName = 'iOS';
    osVersion = userAgent.match(/OS ([\d_]+)/)![1].replace(/_/g, '.');
  } else if (userAgent.indexOf('Linux') > -1) {
    osName = 'Linux';
    osVersion = '';
  } else {
    osName = '未知操作系统';
    osVersion = '';
  }

  return osVersion ? `${osName} ${osVersion}` : osName;
}

/**
 * 获取屏幕尺寸信息
 * @returns 屏幕尺寸信息
 */
export function getScreenSize(): string {
  const width = window.screen.width;
  const height = window.screen.height;
  const pixelRatio = window.devicePixelRatio || 1;
  
  return `${width}x${height} (${pixelRatio}x)`;
}

/**
 * 获取设备类型
 * @returns 设备类型
 */
export function getDeviceType(): string {
  const userAgent = navigator.userAgent;
  
  if (/Mobi|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
    if (/iPad|Tablet|Android(?!.*Mobile)/i.test(userAgent)) {
      return '平板设备';
    }
    return '移动设备';
  }
  
  return '桌面设备';
}

/**
 * 获取网络连接信息
 * @returns 网络连接信息
 */
export function getNetworkInfo(): string {
  // @ts-ignore
  const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
  
  if (connection) {
    const type = connection.type || '未知';
    const effectiveType = connection.effectiveType || '未知';
    const downlink = connection.downlink ? `${connection.downlink} Mbps` : '未知';
    
    return `类型: ${type}, 有效类型: ${effectiveType}, 下行速度: ${downlink}`;
  }
  
  return '未知网络信息';
}

/**
 * 获取完整设备信息
 * @returns 完整设备信息对象
 */
export function getFullDeviceInfo(): Record<string, string> {
  return {
    browser: getBrowserInfo(),
    os: getOSInfo(),
    screenSize: getScreenSize(),
    deviceType: getDeviceType(),
    networkInfo: getNetworkInfo(),
    userAgent: navigator.userAgent,
    language: navigator.language,
    cookiesEnabled: navigator.cookieEnabled ? '是' : '否',
    doNotTrack: navigator.doNotTrack ? '是' : '否',
    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
  };
}
