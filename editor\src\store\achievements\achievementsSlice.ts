/**
 * 成就状态切片
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '..';
import { Achievement } from '../../services/AchievementService';

/**
 * 成就状态接口
 */
export interface AchievementsState {
  achievements: Achievement[];
  recentUnlocked: string[];
  showNotification: boolean;
}

/**
 * 初始状态
 */
const initialState: AchievementsState = {
  achievements: [],
  recentUnlocked: [],
  showNotification: false
};

/**
 * 创建成就切片
 */
export const achievementsSlice = createSlice({
  name: 'achievements',
  initialState,
  reducers: {
    // 添加成就
    addAchievement: (state, action: PayloadAction<Achievement>) => {
      const existingIndex = state.achievements.findIndex(a => a.id === action.payload.id);
      if (existingIndex !== -1) {
        state.achievements[existingIndex] = action.payload;
      } else {
        state.achievements.push(action.payload);
      }
    },

    // 解锁成就
    unlockAchievement: (state, action: PayloadAction<string>) => {
      const achievement = state.achievements.find(a => a.id === action.payload);
      if (achievement && !achievement.unlocked) {
        achievement.unlocked = true;
        achievement.unlockedAt = Date.now();
        
        // 添加到最近解锁列表
        state.recentUnlocked.unshift(action.payload);
        if (state.recentUnlocked.length > 5) {
          state.recentUnlocked.pop();
        }
        
        // 显示通知
        state.showNotification = true;
      }
    },

    // 更新成就进度
    updateProgress: (state, action: PayloadAction<{ id: string, progress: number }>) => {
      const achievement = state.achievements.find(a => a.id === action.payload.id);
      if (achievement && achievement.maxProgress !== undefined) {
        achievement.progress = Math.min(action.payload.progress, achievement.maxProgress);
        
        // 如果达到最大进度，解锁成就
        if (achievement.progress >= achievement.maxProgress) {
          achievement.unlocked = true;
          achievement.unlockedAt = Date.now();
          
          // 添加到最近解锁列表
          state.recentUnlocked.unshift(action.payload.id);
          if (state.recentUnlocked.length > 5) {
            state.recentUnlocked.pop();
          }
          
          // 显示通知
          state.showNotification = true;
        }
      }
    },

    // 隐藏通知
    hideNotification: (state) => {
      state.showNotification = false;
    },

    // 清除最近解锁列表
    clearRecentUnlocked: (state) => {
      state.recentUnlocked = [];
    },

    // 重置所有成就
    resetAchievements: (state) => {
      state.achievements.forEach(achievement => {
        achievement.unlocked = false;
        achievement.unlockedAt = undefined;
        if (achievement.progress !== undefined) {
          achievement.progress = 0;
        }
      });
      state.recentUnlocked = [];
      state.showNotification = false;
    }
  }
});

// 导出操作
export const {
  addAchievement,
  unlockAchievement,
  updateProgress,
  hideNotification,
  clearRecentUnlocked,
  resetAchievements
} = achievementsSlice.actions;

// 选择器
export const selectAchievements = (state: RootState) => state.achievements.achievements;
export const selectUnlockedAchievements = (state: RootState) => 
  state.achievements.achievements.filter(a => a.unlocked);
export const selectRecentUnlocked = (state: RootState) => {
  const ids = state.achievements.recentUnlocked;
  return ids.map(id => state.achievements.achievements.find(a => a.id === id)).filter(Boolean) as Achievement[];
};
export const selectShowNotification = (state: RootState) => state.achievements.showNotification;
export const selectAchievementById = (id: string) => (state: RootState) => 
  state.achievements.achievements.find(a => a.id === id);
export const selectAchievementProgress = (id: string) => (state: RootState) => {
  const achievement = state.achievements.achievements.find(a => a.id === id);
  if (!achievement || achievement.maxProgress === undefined) return 0;
  return (achievement.progress || 0) / achievement.maxProgress * 100;
};

// 导出Reducer
export default achievementsSlice.reducer;
