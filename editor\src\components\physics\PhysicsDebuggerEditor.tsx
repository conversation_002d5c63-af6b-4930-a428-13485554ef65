/**
 * 物理调试器编辑器组件
 * 用于配置物理调试器
 */
import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Switch, InputNumber, Button, Collapse, Tooltip, Space, Divider, Card, Row, Col } from 'antd';
import { InfoCircleOutlined, BugOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { updatePhysicsDebuggerSettings } from '../../store/physics/physicsSlice';
import { ColorPicker } from '../common/ColorPicker';

const { Option } = Select;
const { Panel } = Collapse;

/**
 * 物理调试器编辑器组件
 */
const PhysicsDebuggerEditor: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 获取物理调试器设置
  const debuggerSettings = useSelector((state: RootState) => state.physics.debuggerSettings);

  // 表单状态
  const [form] = Form.useForm();

  // 初始化表单
  useEffect(() => {
    if (debuggerSettings) {
      form.setFieldsValue({
        enabled: debuggerSettings.enabled || false,
        useEnhancedDebugger: debuggerSettings.useEnhancedDebugger || false,
        bodyColor: debuggerSettings.bodyColor || '#ff0000',
        constraintColor: debuggerSettings.constraintColor || '#00ff00',
        showVelocities: debuggerSettings.showVelocities || false,
        showForces: debuggerSettings.showForces || false,
        showCenterOfMass: debuggerSettings.showCenterOfMass || false,
        showSleepState: debuggerSettings.showSleepState || false,
        showPerformanceStats: debuggerSettings.showPerformanceStats || false,
        showContactNormals: debuggerSettings.showContactNormals || false,
        showContactForces: debuggerSettings.showContactForces || false,
        showFrictionForces: debuggerSettings.showFrictionForces || false,
        velocityColor: debuggerSettings.velocityColor || '#00ffff',
        forceColor: debuggerSettings.forceColor || '#ff00ff',
        centerOfMassColor: debuggerSettings.centerOfMassColor || '#ffff00',
        sleepStateColor: debuggerSettings.sleepStateColor || '#888888',
        contactNormalColor: debuggerSettings.contactNormalColor || '#00ff00',
        contactForceColor: debuggerSettings.contactForceColor || '#ff0000',
        frictionForceColor: debuggerSettings.frictionForceColor || '#ffa500',
        vectorScale: debuggerSettings.vectorScale || 0.1,
        contactForceScale: debuggerSettings.contactForceScale || 0.01
      });
    }
  }, [debuggerSettings, form]);

  // 处理表单变更
  const handleValuesChange = (changedValues: any, allValues: any) => {
    // 更新物理调试器设置
    dispatch(updatePhysicsDebuggerSettings({
      ...debuggerSettings,
      ...changedValues
    }));
  };

  return (
    <div className="component-editor physics-debugger-editor">
      <Card title={t('editor.physics.physicsDebugger')} extra={<BugOutlined />}>
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleValuesChange}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enabled"
                label={t('editor.physics.enableDebugger')}
                tooltip={t('editor.physics.enableDebuggerTooltip')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="useEnhancedDebugger"
                label={t('editor.physics.useEnhancedDebugger')}
                tooltip={t('editor.physics.useEnhancedDebuggerTooltip')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Collapse defaultActiveKey={['basicSettings', 'enhancedSettings']}>
            <Panel header={t('editor.physics.basicSettings')} key="basicSettings">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="bodyColor"
                    label={t('editor.physics.bodyColor')}
                    tooltip={t('editor.physics.bodyColorTooltip')}
                  >
                    <ColorPicker />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="constraintColor"
                    label={t('editor.physics.constraintColor')}
                    tooltip={t('editor.physics.constraintColorTooltip')}
                  >
                    <ColorPicker />
                  </Form.Item>
                </Col>
              </Row>
            </Panel>

            <Panel header={t('editor.physics.enhancedSettings')} key="enhancedSettings">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="showVelocities"
                    label={t('editor.physics.showVelocities')}
                    tooltip={t('editor.physics.showVelocitiesTooltip')}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="showForces"
                    label={t('editor.physics.showForces')}
                    tooltip={t('editor.physics.showForcesTooltip')}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="showCenterOfMass"
                    label={t('editor.physics.showCenterOfMass')}
                    tooltip={t('editor.physics.showCenterOfMassTooltip')}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="showSleepState"
                    label={t('editor.physics.showSleepState')}
                    tooltip={t('editor.physics.showSleepStateTooltip')}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="showPerformanceStats"
                label={t('editor.physics.showPerformanceStats')}
                tooltip={t('editor.physics.showPerformanceStatsTooltip')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="showContactNormals"
                    label={t('editor.physics.showContactNormals')}
                    tooltip={t('editor.physics.showContactNormalsTooltip')}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="showContactForces"
                    label={t('editor.physics.showContactForces')}
                    tooltip={t('editor.physics.showContactForcesTooltip')}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="showFrictionForces"
                label={t('editor.physics.showFrictionForces')}
                tooltip={t('editor.physics.showFrictionForcesTooltip')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>

              <Divider>{t('editor.physics.colors')}</Divider>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="velocityColor"
                    label={t('editor.physics.velocityColor')}
                    tooltip={t('editor.physics.velocityColorTooltip')}
                  >
                    <ColorPicker />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="forceColor"
                    label={t('editor.physics.forceColor')}
                    tooltip={t('editor.physics.forceColorTooltip')}
                  >
                    <ColorPicker />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="centerOfMassColor"
                    label={t('editor.physics.centerOfMassColor')}
                    tooltip={t('editor.physics.centerOfMassColorTooltip')}
                  >
                    <ColorPicker />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="sleepStateColor"
                    label={t('editor.physics.sleepStateColor')}
                    tooltip={t('editor.physics.sleepStateColorTooltip')}
                  >
                    <ColorPicker />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="contactNormalColor"
                    label={t('editor.physics.contactNormalColor')}
                    tooltip={t('editor.physics.contactNormalColorTooltip')}
                  >
                    <ColorPicker />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="contactForceColor"
                    label={t('editor.physics.contactForceColor')}
                    tooltip={t('editor.physics.contactForceColorTooltip')}
                  >
                    <ColorPicker />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="frictionForceColor"
                label={t('editor.physics.frictionForceColor')}
                tooltip={t('editor.physics.frictionForceColorTooltip')}
              >
                <ColorPicker />
              </Form.Item>

              <Form.Item
                name="vectorScale"
                label={t('editor.physics.vectorScale')}
                tooltip={t('editor.physics.vectorScaleTooltip')}
                rules={[{ type: 'number', min: 0.01 }]}
              >
                <InputNumber min={0.01} step={0.01} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="contactForceScale"
                label={t('editor.physics.contactForceScale')}
                tooltip={t('editor.physics.contactForceScaleTooltip')}
                rules={[{ type: 'number', min: 0.001 }]}
              >
                <InputNumber min={0.001} max={0.1} step={0.001} style={{ width: '100%' }} />
              </Form.Item>
            </Panel>
          </Collapse>
        </Form>
      </Card>
    </div>
  );
};

export default PhysicsDebuggerEditor;
