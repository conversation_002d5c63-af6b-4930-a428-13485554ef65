/**
 * 工作空间服务
 * 提供工作空间的获取、创建、删除等功能
 */
import axios from 'axios';
import { message } from 'antd';
import { Workspace } from '../types/workspace';

/**
 * 获取工作空间列表
 * @returns 工作空间列表
 */
export const fetchWorkspaces = async (): Promise<Workspace[]> => {
  try {
    // TODO: 替换为实际的API调用
    // const response = await axios.get('/api/workspaces');
    // return response.data;

    // 模拟数据
    return mockWorkspaces;
  } catch (error) {
    console.error('获取工作空间失败:', error);
    message.error('获取工作空间失败，请稍后重试');
    return [];
  }
};

/**
 * 获取工作空间详情
 * @param id 工作空间ID
 * @returns 工作空间详情
 */
export const fetchWorkspaceById = async (id: string): Promise<Workspace | null> => {
  try {
    // TODO: 替换为实际的API调用
    // const response = await axios.get(`/api/workspaces/${id}`);
    // return response.data;

    // 模拟数据
    const workspace = mockWorkspaces.find(workspace => workspace.id === id);
    return workspace || null;
  } catch (error) {
    console.error('获取工作空间详情失败:', error);
    message.error('获取工作空间详情失败，请稍后重试');
    return null;
  }
};

/**
 * 创建工作空间
 * @param name 工作空间名称
 * @param path 工作空间路径
 * @returns 创建结果
 */
export const createWorkspace = async (
  name: string,
  path: string
): Promise<Workspace | null> => {
  try {
    // TODO: 替换为实际的API调用
    // const response = await axios.post('/api/workspaces', {
    //   name,
    //   path
    // });
    // return response.data;

    // 模拟创建
    const newWorkspace: Workspace = {
      id: `ws-${Date.now()}`,
      name,
      path,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      projects: []
    };
    console.log('创建工作空间:', newWorkspace);
    return newWorkspace;
  } catch (error) {
    console.error('创建工作空间失败:', error);
    message.error('创建工作空间失败，请稍后重试');
    return null;
  }
};

/**
 * 更新工作空间
 * @param id 工作空间ID
 * @param data 更新数据
 * @returns 更新结果
 */
export const updateWorkspace = async (
  id: string,
  data: Partial<Workspace>
): Promise<boolean> => {
  try {
    // TODO: 替换为实际的API调用
    // const response = await axios.put(`/api/workspaces/${id}`, data);
    // return response.data.success;

    // 模拟更新
    console.log('更新工作空间:', {
      id,
      data
    });
    return true;
  } catch (error) {
    console.error('更新工作空间失败:', error);
    message.error('更新工作空间失败，请稍后重试');
    return false;
  }
};

/**
 * 删除工作空间
 * @param id 工作空间ID
 * @returns 删除结果
 */
export const deleteWorkspace = async (id: string): Promise<boolean> => {
  try {
    // TODO: 替换为实际的API调用
    // const response = await axios.delete(`/api/workspaces/${id}`);
    // return response.data.success;

    // 模拟删除
    console.log('删除工作空间:', id);
    return true;
  } catch (error) {
    console.error('删除工作空间失败:', error);
    message.error('删除工作空间失败，请稍后重试');
    return false;
  }
};

/**
 * 浏览文件夹
 * @returns 选择的文件夹路径
 */
export const browseFolder = async (): Promise<string | null> => {
  try {
    // TODO: 替换为实际的API调用
    // const response = await axios.post('/api/workspaces/browse-folder');
    // return response.data.path;

    // 模拟浏览
    console.log('浏览文件夹');
    return 'C:\\Projects\\NewProject';
  } catch (error) {
    console.error('浏览文件夹失败:', error);
    message.error('浏览文件夹失败，请稍后重试');
    return null;
  }
};

// 模拟数据
const mockWorkspaces: Workspace[] = [
  {
    id: 'ws-1',
    name: '默认工作空间',
    path: 'C:\\Projects\\DefaultWorkspace',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
    projects: [
      {
        id: 'proj-1',
        name: '示例项目1',
        path: 'C:\\Projects\\DefaultWorkspace\\Project1',
        createdAt: '2023-01-02T00:00:00Z',
        updatedAt: '2023-01-02T00:00:00Z',
      },
      {
        id: 'proj-2',
        name: '示例项目2',
        path: 'C:\\Projects\\DefaultWorkspace\\Project2',
        createdAt: '2023-01-03T00:00:00Z',
        updatedAt: '2023-01-03T00:00:00Z',
      },
    ],
  },
  {
    id: 'ws-2',
    name: '个人项目',
    path: 'C:\\Projects\\PersonalProjects',
    createdAt: '2023-02-01T00:00:00Z',
    updatedAt: '2023-02-01T00:00:00Z',
    projects: [
      {
        id: 'proj-3',
        name: '个人项目1',
        path: 'C:\\Projects\\PersonalProjects\\Project1',
        createdAt: '2023-02-02T00:00:00Z',
        updatedAt: '2023-02-02T00:00:00Z',
      },
    ],
  },
];
