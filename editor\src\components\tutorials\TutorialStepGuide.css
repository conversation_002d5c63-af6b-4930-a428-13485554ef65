/**
 * 教程步骤引导样式
 */

.tutorial-guide-card {
  width: 400px;
  max-width: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  overflow: hidden;
}

.tutorial-guide-card.completed {
  background: linear-gradient(135deg, #f0f5ff, #e6f7ff);
}

.tutorial-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.tutorial-title {
  flex: 1;
}

.tutorial-title h4 {
  margin-bottom: 8px !important;
}

.tutorial-progress {
  margin-top: 4px;
}

.close-button {
  margin-left: 8px;
}

.tutorial-steps {
  margin: 16px 0;
}

.tutorial-step-content {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.step-header {
  margin-bottom: 12px;
}

.step-header h4 {
  margin-bottom: 8px !important;
}

.step-progress {
  margin-top: 4px;
}

.step-description {
  margin-bottom: 16px !important;
}

.task-list {
  margin-top: 16px;
}

.task-list-item {
  padding: 8px 0 !important;
  border-bottom: 1px solid #f0f0f0;
}

.task-list-item:last-child {
  border-bottom: none;
}

.task-item {
  display: flex;
  flex-direction: column;
}

.task-item.completed {
  opacity: 0.8;
}

.task-item.optional {
  background-color: rgba(82, 196, 26, 0.05);
  border-radius: 4px;
  padding: 4px;
}

.task-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.task-title {
  margin-left: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-content {
  padding-left: 24px;
}

.task-description {
  margin-bottom: 8px !important;
}

.task-hint {
  margin-top: 8px;
}

.hint-content {
  margin-top: 8px;
  padding: 8px;
  background-color: #fffbe6;
  border-radius: 4px;
  border-left: 3px solid #faad14;
}

.tutorial-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.tutorial-completed {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 24px 16px;
}

.tutorial-completed-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
}

.tutorial-completed-icon {
  font-size: 48px;
  color: gold;
  margin-bottom: 16px;
}

.tutorial-reward {
  margin: 24px 0;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  width: 100%;
}

.tutorial-completed-actions {
  margin-top: 24px;
}

/* 响应式调整 */
@media (max-width: 576px) {
  .tutorial-guide-card {
    width: 100%;
  }
  
  .tutorial-step-content {
    padding: 12px;
  }
  
  .tutorial-footer {
    flex-direction: column;
    align-items: stretch;
  }
  
  .tutorial-footer .ant-space {
    flex-direction: column;
    width: 100%;
  }
  
  .tutorial-footer .ant-btn {
    width: 100%;
    margin-bottom: 8px;
  }
}
