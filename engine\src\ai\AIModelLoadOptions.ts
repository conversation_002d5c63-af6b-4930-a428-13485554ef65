/**
 * AI模型加载选项
 */
export interface AIModelLoadOptions {
  /** 是否强制重新加载 */
  forceReload?: boolean;

  /** 加载超时时间（毫秒） */
  timeout?: number;

  /** 加载优先级 (0-10) */
  priority?: number;

  /** 加载进度回调 */
  onProgress?: (progress: number) => void;

  /** 加载完成回调 */
  onComplete?: (success: boolean) => void;

  /** 加载错误回调 */
  onError?: (error: Error) => void;

  /** 是否使用缓存 */
  useCache?: boolean;

  /** 是否使用离线模型 */
  useOfflineModel?: boolean;

  /** 是否使用低精度模型 */
  useLowPrecision?: boolean;

  /** 是否使用模型工厂 */
  useFactory?: boolean;

  /** 自定义选项 */
  [key: string]: any;
}
