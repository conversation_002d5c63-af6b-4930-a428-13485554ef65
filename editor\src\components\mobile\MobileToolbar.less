/**
 * 移动工具栏样式
 */
.mobile-toolbar {
  position: fixed;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  
  // 横屏模式
  &.landscape {
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    border-radius: 16px 16px 0 0;
  }
  
  // 竖屏模式
  &.portrait {
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    border-radius: 0 0 16px 16px;
  }
  
  // 工具栏主要部分
  .toolbar-main {
    flex: 1;
    display: flex;
    align-items: center;
    
    .toolbar-button {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &:active {
        transform: scale(0.95);
      }
    }
  }
  
  // 工具栏状态部分
  .toolbar-status {
    display: flex;
    align-items: center;
    margin-left: 16px;
    
    .ant-badge {
      font-size: 18px;
      color: #fff;
    }
  }
}

// 移动设备抽屉样式
.ant-drawer {
  .ant-drawer-header {
    background-color: #f0f2f5;
    border-bottom: 1px solid #e8e8e8;
  }
  
  .ant-drawer-body {
    padding: 0;
    
    .ant-menu {
      border-right: none;
      
      .ant-menu-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16px;
        height: 50px;
        line-height: 50px;
        
        .ant-space {
          width: 100%;
          justify-content: space-between;
        }
      }
      
      .ant-menu-submenu-title {
        height: 50px;
        line-height: 50px;
      }
    }
  }
}

// 移动设备下拉菜单样式
.mobile-dropdown-menu {
  .ant-dropdown-menu {
    padding: 8px;
    border-radius: 8px;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    
    .ant-dropdown-menu-item {
      padding: 8px 12px;
      border-radius: 4px;
      
      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
}

// 响应式调整
@media (max-width: 576px) {
  .mobile-toolbar {
    padding: 8px;
    
    .toolbar-main {
      .ant-space {
        gap: 8px !important;
      }
      
      .toolbar-button {
        width: 36px;
        height: 36px;
      }
    }
    
    .toolbar-status {
      margin-left: 8px;
    }
  }
}

// 暗色主题
.dark-theme {
  .mobile-toolbar {
    background-color: rgba(22, 22, 22, 0.7);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  .ant-drawer {
    .ant-drawer-header {
      background-color: #1f1f1f;
      border-bottom: 1px solid #303030;
    }
    
    .ant-drawer-body {
      .ant-menu {
        background-color: #141414;
        color: rgba(255, 255, 255, 0.85);
        
        .ant-menu-item:hover {
          background-color: #1f1f1f;
        }
      }
    }
  }
  
  .mobile-dropdown-menu {
    .ant-dropdown-menu {
      background-color: #1f1f1f;
      
      .ant-dropdown-menu-item {
        color: rgba(255, 255, 255, 0.85);
        
        &:hover {
          background-color: #303030;
        }
      }
    }
  }
}
