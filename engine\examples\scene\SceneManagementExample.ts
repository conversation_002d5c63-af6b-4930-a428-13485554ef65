/**
 * 场景管理示例
 * 展示如何使用场景管理系统
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { Entity } from '../../src/core/Entity';
import { 
  Scene, 
  Skybox, 
  SceneManager, 
  SceneTransitionType,
  SceneSerializer
} from '../../src/scene';
import { AssetManager } from '../../src/assets';

/**
 * 场景管理示例
 */
export class SceneManagementExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 资产管理器 */
  private assetManager: AssetManager;
  
  /** 场景管理器 */
  private sceneManager: SceneManager;
  
  /** 场景序列化器 */
  private sceneSerializer: SceneSerializer;
  
  /** 场景列表 */
  private scenes: Scene[] = [];
  
  /** 当前场景索引 */
  private currentSceneIndex: number = 0;
  
  /** UI元素 */
  private ui: {
    container: HTMLElement;
    sceneInfo: HTMLElement;
    prevButton: HTMLElement;
    nextButton: HTMLElement;
    saveButton: HTMLElement;
    loadButton: HTMLElement;
  };
  
  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建场景管理示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建资产管理器
    this.assetManager = new AssetManager();
    
    // 创建场景管理器
    this.sceneManager = new SceneManager({
      world: this.engine.getWorld(),
      assetManager: this.assetManager,
      defaultTransition: {
        type: SceneTransitionType.FADE,
        duration: 1000
      },
      enableSceneCache: true,
      maxSceneCacheCount: 3
    });
    
    // 创建场景序列化器
    this.sceneSerializer = new SceneSerializer(this.engine.getWorld());
    
    // 创建UI
    this.ui = this.createUI();
  }

  /**
   * 创建UI
   * @returns UI元素
   */
  private createUI(): any {
    // 创建UI容器
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.top = '20px';
    container.style.left = '20px';
    container.style.padding = '10px';
    container.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    container.style.borderRadius = '5px';
    container.style.color = 'white';
    container.style.fontFamily = 'Arial, sans-serif';
    container.style.zIndex = '1000';
    
    // 创建场景信息
    const sceneInfo = document.createElement('div');
    sceneInfo.style.marginBottom = '10px';
    container.appendChild(sceneInfo);
    
    // 创建按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.style.display = 'flex';
    buttonContainer.style.gap = '10px';
    container.appendChild(buttonContainer);
    
    // 创建上一个场景按钮
    const prevButton = document.createElement('button');
    prevButton.textContent = '上一个场景';
    prevButton.addEventListener('click', () => this.loadPreviousScene());
    buttonContainer.appendChild(prevButton);
    
    // 创建下一个场景按钮
    const nextButton = document.createElement('button');
    nextButton.textContent = '下一个场景';
    nextButton.addEventListener('click', () => this.loadNextScene());
    buttonContainer.appendChild(nextButton);
    
    // 创建保存场景按钮
    const saveButton = document.createElement('button');
    saveButton.textContent = '保存场景';
    saveButton.addEventListener('click', () => this.saveCurrentScene());
    buttonContainer.appendChild(saveButton);
    
    // 创建加载场景按钮
    const loadButton = document.createElement('button');
    loadButton.textContent = '加载场景';
    loadButton.addEventListener('click', () => this.loadSavedScene());
    buttonContainer.appendChild(loadButton);
    
    // 添加到文档
    document.body.appendChild(container);
    
    return {
      container,
      sceneInfo,
      prevButton,
      nextButton,
      saveButton,
      loadButton
    };
  }

  /**
   * 初始化示例
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }
    
    // 初始化引擎
    this.engine.initialize();
    
    // 初始化资产管理器
    this.assetManager.initialize();
    
    // 初始化场景管理器
    this.sceneManager.initialize();
    
    // 初始化场景序列化器
    this.sceneSerializer.initialize();
    
    // 创建场景
    await this.createScenes();
    
    // 加载第一个场景
    await this.loadScene(0);
    
    this.initialized = true;
  }

  /**
   * 创建场景
   */
  private async createScenes(): Promise<void> {
    // 创建场景1：基础场景
    const scene1 = this.engine.getWorld().createScene('基础场景');
    
    // 设置天空盒
    scene1.setSkybox(new Skybox('color', new THREE.Color(0x87CEEB)));
    
    // 设置环境光
    scene1.setAmbientLight(new THREE.Color(0xffffff), 0.5);
    
    // 创建地面
    const ground = new Entity('地面');
    ground.getTransform().setPosition(0, -1, 0);
    ground.getTransform().setScale(10, 0.1, 10);
    
    // 添加网格组件
    const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
    const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x808080 });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.addComponent('MeshComponent', { mesh: groundMesh });
    
    // 添加到场景
    scene1.addEntity(ground);
    
    // 创建立方体
    const cube = new Entity('立方体');
    cube.getTransform().setPosition(0, 0, 0);
    
    // 添加网格组件
    const cubeGeometry = new THREE.BoxGeometry(1, 1, 1);
    const cubeMaterial = new THREE.MeshStandardMaterial({ color: 0xff0000 });
    const cubeMesh = new THREE.Mesh(cubeGeometry, cubeMaterial);
    cube.addComponent('MeshComponent', { mesh: cubeMesh });
    
    // 添加到场景
    scene1.addEntity(cube);
    
    // 添加到场景列表
    this.scenes.push(scene1);
    
    // 创建场景2：球体场景
    const scene2 = this.engine.getWorld().createScene('球体场景');
    
    // 设置天空盒
    scene2.setSkybox(new Skybox('color', new THREE.Color(0x000000)));
    
    // 设置环境光
    scene2.setAmbientLight(new THREE.Color(0xffffff), 0.3);
    
    // 添加平行光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(1, 1, 1);
    scene2.getThreeScene().add(directionalLight);
    
    // 创建地面
    const ground2 = new Entity('地面');
    ground2.getTransform().setPosition(0, -1, 0);
    ground2.getTransform().setScale(10, 0.1, 10);
    
    // 添加网格组件
    const ground2Geometry = new THREE.BoxGeometry(1, 1, 1);
    const ground2Material = new THREE.MeshStandardMaterial({ color: 0x404040 });
    const ground2Mesh = new THREE.Mesh(ground2Geometry, ground2Material);
    ground2.addComponent('MeshComponent', { mesh: ground2Mesh });
    
    // 添加到场景
    scene2.addEntity(ground2);
    
    // 创建球体
    const sphere = new Entity('球体');
    sphere.getTransform().setPosition(0, 0, 0);
    
    // 添加网格组件
    const sphereGeometry = new THREE.SphereGeometry(0.5, 32, 32);
    const sphereMaterial = new THREE.MeshStandardMaterial({ color: 0x00ff00 });
    const sphereMesh = new THREE.Mesh(sphereGeometry, sphereMaterial);
    sphere.addComponent('MeshComponent', { mesh: sphereMesh });
    
    // 添加到场景
    scene2.addEntity(sphere);
    
    // 添加到场景列表
    this.scenes.push(scene2);
    
    // 创建场景3：多物体场景
    const scene3 = this.engine.getWorld().createScene('多物体场景');
    
    // 设置天空盒
    scene3.setSkybox(new Skybox('color', new THREE.Color(0x2c3e50)));
    
    // 设置环境光
    scene3.setAmbientLight(new THREE.Color(0xffffff), 0.4);
    
    // 设置雾效
    scene3.setFog(new THREE.Color(0x2c3e50), 2, 20);
    
    // 创建地面
    const ground3 = new Entity('地面');
    ground3.getTransform().setPosition(0, -1, 0);
    ground3.getTransform().setScale(20, 0.1, 20);
    
    // 添加网格组件
    const ground3Geometry = new THREE.BoxGeometry(1, 1, 1);
    const ground3Material = new THREE.MeshStandardMaterial({ color: 0x2c3e50 });
    const ground3Mesh = new THREE.Mesh(ground3Geometry, ground3Material);
    ground3.addComponent('MeshComponent', { mesh: ground3Mesh });
    
    // 添加到场景
    scene3.addEntity(ground3);
    
    // 创建多个物体
    const colors = [0xff0000, 0x00ff00, 0x0000ff, 0xffff00, 0xff00ff, 0x00ffff];
    const shapes = ['box', 'sphere', 'cone', 'torus'];
    
    for (let i = 0; i < 20; i++) {
      const shape = shapes[Math.floor(Math.random() * shapes.length)];
      const color = colors[Math.floor(Math.random() * colors.length)];
      
      const entity = new Entity(`物体${i}`);
      
      // 随机位置
      entity.getTransform().setPosition(
        (Math.random() - 0.5) * 10,
        Math.random() * 2,
        (Math.random() - 0.5) * 10
      );
      
      // 随机旋转
      entity.getTransform().setRotation(
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2,
        Math.random() * Math.PI * 2
      );
      
      // 随机缩放
      const scale = Math.random() * 0.5 + 0.5;
      entity.getTransform().setScale(scale, scale, scale);
      
      // 添加网格组件
      let geometry: THREE.BufferGeometry;
      
      switch (shape) {
        case 'box':
          geometry = new THREE.BoxGeometry(1, 1, 1);
          break;
        case 'sphere':
          geometry = new THREE.SphereGeometry(0.5, 16, 16);
          break;
        case 'cone':
          geometry = new THREE.ConeGeometry(0.5, 1, 16);
          break;
        case 'torus':
          geometry = new THREE.TorusGeometry(0.5, 0.2, 16, 32);
          break;
        default:
          geometry = new THREE.BoxGeometry(1, 1, 1);
      }
      
      const material = new THREE.MeshStandardMaterial({ color });
      const mesh = new THREE.Mesh(geometry, material);
      entity.addComponent('MeshComponent', { mesh });
      
      // 添加到场景
      scene3.addEntity(entity);
    }
    
    // 添加到场景列表
    this.scenes.push(scene3);
  }

  /**
   * 加载场景
   * @param index 场景索引
   */
  private async loadScene(index: number): Promise<void> {
    if (index < 0 || index >= this.scenes.length) {
      return;
    }
    
    // 更新当前场景索引
    this.currentSceneIndex = index;
    
    // 获取场景
    const scene = this.scenes[index];
    
    // 加载场景
    await this.sceneManager.loadScene(scene.id, {
      setActive: true,
      showLoadingScreen: true,
      transition: {
        type: SceneTransitionType.FADE,
        duration: 1000
      },
      onProgress: (progress) => {
        console.log(`加载进度: ${Math.floor(progress * 100)}%`);
      }
    });
    
    // 更新UI
    this.updateUI();
  }

  /**
   * 加载上一个场景
   */
  private loadPreviousScene(): void {
    const index = (this.currentSceneIndex - 1 + this.scenes.length) % this.scenes.length;
    this.loadScene(index);
  }

  /**
   * 加载下一个场景
   */
  private loadNextScene(): void {
    const index = (this.currentSceneIndex + 1) % this.scenes.length;
    this.loadScene(index);
  }

  /**
   * 保存当前场景
   */
  private saveCurrentScene(): void {
    // 获取当前场景
    const scene = this.scenes[this.currentSceneIndex];
    
    // 序列化场景
    const json = this.sceneSerializer.serializeToJSON(scene, {
      includeEntities: true,
      includeComponents: true,
      includeSkybox: true,
      includeAmbientLight: true,
      includeFog: true,
      prettyPrint: true
    });
    
    // 保存到本地存储
    localStorage.setItem('savedScene', json);
    
    alert('场景已保存');
  }

  /**
   * 加载保存的场景
   */
  private loadSavedScene(): void {
    // 从本地存储获取场景数据
    const json = localStorage.getItem('savedScene');
    
    if (!json) {
      alert('没有保存的场景');
      return;
    }
    
    try {
      // 创建新场景
      const scene = this.engine.getWorld().createScene('加载的场景');
      
      // 反序列化场景
      this.sceneSerializer.deserializeFromJSON(json, scene, {
        includeEntities: true,
        includeComponents: true,
        includeSkybox: true,
        includeAmbientLight: true,
        includeFog: true
      });
      
      // 添加到场景列表
      this.scenes.push(scene);
      
      // 加载场景
      this.loadScene(this.scenes.length - 1);
      
      alert('场景已加载');
    } catch (error) {
      console.error('加载场景失败:', error);
      alert('加载场景失败');
    }
  }

  /**
   * 更新UI
   */
  private updateUI(): void {
    // 获取当前场景
    const scene = this.scenes[this.currentSceneIndex];
    
    // 更新场景信息
    this.ui.sceneInfo.textContent = `当前场景: ${scene.name} (${this.currentSceneIndex + 1}/${this.scenes.length})`;
  }

  /**
   * 更新示例
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized) {
      return;
    }
    
    // 更新引擎
    this.engine.update(deltaTime);
    
    // 获取当前场景
    const scene = this.scenes[this.currentSceneIndex];
    
    // 旋转场景中的物体
    for (const entity of scene.getEntities()) {
      // 跳过地面
      if (entity.name.includes('地面')) {
        continue;
      }
      
      // 旋转物体
      const transform = entity.getTransform();
      transform.rotate(deltaTime * 0.5, deltaTime * 0.3, 0);
    }
  }

  /**
   * 渲染示例
   */
  public render(): void {
    if (!this.initialized) {
      return;
    }
    
    // 渲染由引擎处理
  }

  /**
   * 销毁示例
   */
  public dispose(): void {
    // 移除UI
    if (this.ui.container.parentNode) {
      this.ui.container.parentNode.removeChild(this.ui.container);
    }
    
    // 销毁场景序列化器
    this.sceneSerializer.dispose();
    
    // 销毁场景管理器
    this.sceneManager.dispose();
    
    // 销毁资产管理器
    this.assetManager.dispose();
    
    // 销毁引擎
    this.engine.dispose();
  }
}
