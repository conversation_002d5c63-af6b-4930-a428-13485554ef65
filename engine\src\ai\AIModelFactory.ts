/**
 * AI模型工厂
 * 用于创建和管理AI模型实例
 */
import { AIModelType } from './AIModelType';
import { AIModelConfig } from './AIModelConfig';
import { IAIModel } from './models/IAIModel';
import { GPTModel } from './models/GPTModel';
import { StableDiffusionModel } from './models/StableDiffusionModel';
import { BERTModel } from './models/BERTModel';
import { RoBERTaModel } from './models/RoBERTaModel';
import { DistilBERTModel } from './models/DistilBERTModel';
import { ALBERTModel } from './models/ALBERTModel';
import { XLNetModel } from './models/XLNetModel';
import { BARTModel } from './models/BARTModel';
import { T5Model } from './models/T5Model';

/**
 * AI模型工厂配置
 */
export interface AIModelFactoryConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否使用本地模型 */
  useLocalModels?: boolean;
  /** 模型API密钥 */
  apiKeys?: Record<string, string>;
  /** 模型基础URL */
  baseUrls?: Record<string, string>;
  /** 模型版本 */
  modelVersions?: Record<string, string>;
}

/**
 * AI模型工厂
 */
export class AIModelFactory {
  /** 配置 */
  private config: AIModelFactoryConfig;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: AIModelFactoryConfig = {
    debug: false,
    useLocalModels: false,
    apiKeys: {},
    baseUrls: {},
    modelVersions: {}
  };

  /** 模型缓存 */
  private modelCache: Map<string, IAIModel> = new Map();

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: AIModelFactoryConfig = {}) {
    this.config = {
      ...AIModelFactory.DEFAULT_CONFIG,
      ...config
    };
  }

  /**
   * 创建模型
   * @param modelType 模型类型
   * @param config 模型配置
   * @returns 模型实例
   */
  public createModel(modelType: AIModelType, config: AIModelConfig = {}): IAIModel | null {
    try {
      // 生成模型ID
      const modelId = this.generateModelId(modelType, config);

      // 检查缓存
      if (this.modelCache.has(modelId)) {
        return this.modelCache.get(modelId) || null;
      }

      // 创建模型实例
      let model: IAIModel | null = null;

      // 根据模型类型创建不同的模型实例
      switch (modelType) {
        case AIModelType.GPT:
          model = new GPTModel(config, this.config);
          break;
        case AIModelType.STABLE_DIFFUSION:
          model = new StableDiffusionModel(config, this.config);
          break;
        case AIModelType.BERT:
          model = new BERTModel(config, this.config);
          break;
        case AIModelType.ROBERTA:
          // 转换为RoBERTa特定配置
          model = new RoBERTaModel({
            ...config,
            variant: config.variant as 'base' | 'large' | 'distilled' | undefined
          }, this.config);
          break;
        case AIModelType.DISTILBERT:
          // 转换为DistilBERT特定配置
          model = new DistilBERTModel({
            ...config,
            variant: config.variant as 'base' | 'multilingual' | undefined
          }, this.config);
          break;
        case AIModelType.ALBERT:
          // 转换为ALBERT特定配置
          model = new ALBERTModel({
            ...config,
            variant: config.variant as 'base' | 'large' | 'xlarge' | 'xxlarge' | undefined
          }, this.config);
          break;
        case AIModelType.XLNET:
          // 转换为XLNet特定配置
          model = new XLNetModel({
            ...config,
            variant: config.variant as 'base' | 'large' | undefined
          }, this.config);
          break;
        case AIModelType.BART:
          // 转换为BART特定配置
          model = new BARTModel({
            ...config,
            variant: config.variant as 'base' | 'large' | 'cnn' | undefined
          }, this.config);
          break;
        case AIModelType.T5:
          // 转换为T5特定配置
          model = new T5Model({
            ...config,
            variant: config.variant as 'small' | 'base' | 'large' | 'xl' | 'xxl' | undefined
          }, this.config);
          break;
        default:
          console.error(`不支持的模型类型: ${modelType}`);
          return null;
      }

      // 添加到缓存
      if (model) {
        this.modelCache.set(modelId, model);
      }

      return model;
    } catch (error) {
      console.error(`创建模型实例失败: ${error}`);
      return null;
    }
  }

  /**
   * 生成模型ID
   * @param modelType 模型类型
   * @param config 模型配置
   * @returns 模型ID
   */
  private generateModelId(modelType: AIModelType, config: AIModelConfig): string {
    // 基本ID
    let id = `${modelType}`;

    // 添加版本信息
    if (config.version) {
      id += `-${config.version}`;
    } else if (this.config.modelVersions && this.config.modelVersions[modelType]) {
      id += `-${this.config.modelVersions[modelType]}`;
    }

    // 添加其他配置信息
    if (config.variant) {
      id += `-${config.variant}`;
    }

    return id;
  }

  /**
   * 获取模型
   * @param modelId 模型ID
   * @returns 模型实例
   */
  public getModel(modelId: string): IAIModel | null {
    return this.modelCache.get(modelId) || null;
  }

  /**
   * 获取所有模型
   * @returns 模型实例映射
   */
  public getAllModels(): Map<string, IAIModel> {
    return new Map(this.modelCache);
  }

  /**
   * 释放模型
   * @param modelId 模型ID
   * @returns 是否成功
   */
  public releaseModel(modelId: string): boolean {
    // 检查模型是否存在
    if (!this.modelCache.has(modelId)) {
      return false;
    }

    // 获取模型
    const model = this.modelCache.get(modelId);

    // 释放模型
    if (model) {
      (model as any).dispose();
    }

    // 从缓存中移除
    this.modelCache.delete(modelId);

    return true;
  }

  /**
   * 释放所有模型
   */
  public releaseAllModels(): void {
    // 释放所有模型
    for (const model of this.modelCache.values()) {
      (model as any).dispose();
    }

    // 清空缓存
    this.modelCache.clear();
  }

  /**
   * 销毁
   */
  public dispose(): void {
    this.releaseAllModels();
  }
}
