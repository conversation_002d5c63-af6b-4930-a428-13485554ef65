/**
 * 时间相关的可视化脚本节点
 */

import { VisualScriptNode } from '../VisualScriptNode';
import { NodeRegistry } from '../NodeRegistry';

/**
 * 获取当前时间节点
 */
export class GetTimeNode extends VisualScriptNode {
  constructor() {
    super('GetTime', '获取时间');
    this.addOutput('time', 'number', '当前时间');
    this.addOutput('deltaTime', 'number', '帧时间');
  }

  public execute(): any {
    const now = performance.now();
    const deltaTime = this.getContext()?.deltaTime || 0;
    
    return {
      time: now,
      deltaTime: deltaTime
    };
  }
}

/**
 * 延迟节点
 */
export class DelayNode extends VisualScriptNode {
  private startTime: number = 0;
  private isWaiting: boolean = false;

  constructor() {
    super('Delay', '延迟');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('duration', 'number', '延迟时间');
    this.addOutput('completed', 'exec', '完成');
  }

  public execute(inputs: any): any {
    if (inputs.trigger && !this.isWaiting) {
      this.startTime = performance.now();
      this.isWaiting = true;
    }

    if (this.isWaiting) {
      const elapsed = performance.now() - this.startTime;
      const duration = inputs.duration || 1000;

      if (elapsed >= duration) {
        this.isWaiting = false;
        return { completed: true };
      }
    }

    return {};
  }
}

/**
 * 计时器节点
 */
export class TimerNode extends VisualScriptNode {
  private startTime: number = 0;
  private isRunning: boolean = false;

  constructor() {
    super('Timer', '计时器');
    this.addInput('start', 'exec', '开始');
    this.addInput('stop', 'exec', '停止');
    this.addInput('reset', 'exec', '重置');
    this.addOutput('elapsed', 'number', '已用时间');
    this.addOutput('isRunning', 'boolean', '是否运行中');
  }

  public execute(inputs: any): any {
    if (inputs.start && !this.isRunning) {
      this.startTime = performance.now();
      this.isRunning = true;
    }

    if (inputs.stop) {
      this.isRunning = false;
    }

    if (inputs.reset) {
      this.startTime = performance.now();
      this.isRunning = false;
    }

    const elapsed = this.isRunning ? performance.now() - this.startTime : 0;

    return {
      elapsed: elapsed,
      isRunning: this.isRunning
    };
  }
}

/**
 * 注册时间节点
 */
export function registerTimeNodes(): void {
  NodeRegistry.register('GetTime', GetTimeNode);
  NodeRegistry.register('Delay', DelayNode);
  NodeRegistry.register('Timer', TimerNode);
}
