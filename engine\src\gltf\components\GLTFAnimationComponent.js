"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GLTFAnimationComponent = exports.AnimationEventType = exports.AnimationBlendMode = exports.AnimationLoopMode = exports.AnimationState = void 0;
/**
 * GLTF动画组件
 * 用于管理GLTF模型的动画
 */
var THREE = require("three");
var Component_1 = require("../../core/Component");
var EventEmitter_1 = require("../../utils/EventEmitter");
/**
 * 动画状态
 */
var AnimationState;
(function (AnimationState) {
    AnimationState["STOPPED"] = "stopped";
    AnimationState["PLAYING"] = "playing";
    AnimationState["PAUSED"] = "paused";
    AnimationState["FINISHED"] = "finished";
})(AnimationState || (exports.AnimationState = AnimationState = {}));
/**
 * 动画循环模式
 */
var AnimationLoopMode;
(function (AnimationLoopMode) {
    AnimationLoopMode[AnimationLoopMode["ONCE"] = THREE.LoopOnce] = "ONCE";
    AnimationLoopMode[AnimationLoopMode["REPEAT"] = THREE.LoopRepeat] = "REPEAT";
    AnimationLoopMode[AnimationLoopMode["PINGPONG"] = THREE.LoopPingPong] = "PINGPONG";
})(AnimationLoopMode || (exports.AnimationLoopMode = AnimationLoopMode = {}));
/**
 * 动画混合模式
 */
var AnimationBlendMode;
(function (AnimationBlendMode) {
    AnimationBlendMode["NORMAL"] = "normal";
    AnimationBlendMode["ADDITIVE"] = "additive";
})(AnimationBlendMode || (exports.AnimationBlendMode = AnimationBlendMode = {}));
/**
 * 动画事件类型
 */
var AnimationEventType;
(function (AnimationEventType) {
    AnimationEventType["START"] = "start";
    AnimationEventType["STOP"] = "stop";
    AnimationEventType["PAUSE"] = "pause";
    AnimationEventType["RESUME"] = "resume";
    AnimationEventType["LOOP"] = "loop";
    AnimationEventType["FINISHED"] = "finished";
    AnimationEventType["TIME_UPDATE"] = "timeUpdate";
})(AnimationEventType || (exports.AnimationEventType = AnimationEventType = {}));
/**
 * GLTF动画组件
 */
var GLTFAnimationComponent = exports.GLTFAnimationComponent = /** @class */ (function (_super) {
    __extends(GLTFAnimationComponent, _super);
    /**
     * 创建GLTF动画组件
     * @param clips 动画剪辑
     */
    function GLTFAnimationComponent(clips) {
        var _this = _super.call(this, GLTFAnimationComponent.type) || this;
        /** 动画混合器 */
        _this.mixer = null;
        /** 活跃动作 */
        _this.actions = new Map();
        /** 当前状态 */
        _this.state = AnimationState.STOPPED;
        /** 事件发射器 */
        _this.emitter = new EventEmitter_1.EventEmitter();
        /** 时间比例 */
        _this.timeScale = 1.0;
        /** 是否启用 */
        _this.enabled = true;
        /** 默认循环模式 */
        _this.defaultLoopMode = AnimationLoopMode.REPEAT;
        /** 默认混合模式 */
        _this.defaultBlendMode = AnimationBlendMode.NORMAL;
        /** 默认交叉淡入时间 */
        _this.defaultCrossFadeTime = 0.3;
        _this.clips = clips;
        return _this;
    }
    /**
     * 初始化动画混合器
     * @param root 根对象
     */
    GLTFAnimationComponent.prototype.initMixer = function (root) {
        var _this = this;
        this.mixer = new THREE.AnimationMixer(root);
        // 创建所有动作
        for (var _i = 0, _a = this.clips; _i < _a.length; _i++) {
            var clip = _a[_i];
            var action = this.mixer.clipAction(clip);
            this.actions.set(clip.name, action);
        }
        // 监听动画完成事件
        this.mixer.addEventListener('finished', function (event) {
            _this.state = AnimationState.FINISHED;
            _this.emitter.emit(AnimationEventType.FINISHED, event);
        });
    };
    /**
     * 更新动画
     * @param deltaTime 时间增量（秒）
     */
    GLTFAnimationComponent.prototype.update = function (deltaTime) {
        if (!this.enabled || !this.mixer || this.state !== AnimationState.PLAYING) {
            return;
        }
        // 更新混合器
        this.mixer.update(deltaTime * this.timeScale);
        // 发出时间更新事件
        this.emitter.emit(AnimationEventType.TIME_UPDATE, this.mixer.time);
    };
    /**
     * 播放动画
     * @param name 动画名称
     * @param options 播放选项
     * @returns 动画动作
     */
    GLTFAnimationComponent.prototype.play = function (name, options) {
        if (options === void 0) { options = {}; }
        if (!this.mixer) {
            console.warn('动画混合器未初始化');
            return null;
        }
        // 获取动作
        var action = this.actions.get(name);
        if (!action) {
            console.warn("\u627E\u4E0D\u5230\u540D\u4E3A ".concat(name, " \u7684\u52A8\u753B"));
            return null;
        }
        // 设置选项
        var loopMode = options.loopMode !== undefined ? options.loopMode : this.defaultLoopMode;
        var blendMode = options.blendMode !== undefined ? options.blendMode : this.defaultBlendMode;
        var crossFadeTime = options.crossFadeTime !== undefined ? options.crossFadeTime : this.defaultCrossFadeTime;
        var timeScale = options.timeScale !== undefined ? options.timeScale : this.timeScale;
        var clampWhenFinished = options.clampWhenFinished !== undefined ? options.clampWhenFinished : false;
        // 设置动作属性
        action.setLoop(loopMode, Infinity);
        action.clampWhenFinished = clampWhenFinished;
        action.timeScale = timeScale;
        // 设置混合模式
        if (blendMode === AnimationBlendMode.ADDITIVE) {
            action.setEffectiveWeight(1.0);
            action.setEffectiveTimeScale(1.0);
            action.blendMode = THREE.AdditiveBlending;
        }
        else {
            action.blendMode = THREE.NormalBlending;
        }
        // 如果有其他动作正在播放，则交叉淡入
        for (var _i = 0, _a = this.actions; _i < _a.length; _i++) {
            var _b = _a[_i], otherName = _b[0], otherAction = _b[1];
            if (otherName !== name && otherAction.isRunning()) {
                action.crossFadeFrom(otherAction, crossFadeTime, true);
                break;
            }
        }
        // 播放动作
        action.play();
        // 更新状态
        this.state = AnimationState.PLAYING;
        // 发出事件
        this.emitter.emit(AnimationEventType.START, { name: name, action: action });
        return action;
    };
    /**
     * 停止动画
     * @param name 动画名称，如果未提供则停止所有动画
     */
    GLTFAnimationComponent.prototype.stop = function (name) {
        if (!this.mixer) {
            return;
        }
        if (name) {
            // 停止指定动画
            var action = this.actions.get(name);
            if (action) {
                action.stop();
                this.emitter.emit(AnimationEventType.STOP, { name: name, action: action });
            }
        }
        else {
            // 停止所有动画
            for (var _i = 0, _a = this.actions; _i < _a.length; _i++) {
                var _b = _a[_i], name_1 = _b[0], action = _b[1];
                action.stop();
                this.emitter.emit(AnimationEventType.STOP, { name: name_1, action: action });
            }
        }
        this.state = AnimationState.STOPPED;
    };
    /**
     * 暂停动画
     * @param name 动画名称，如果未提供则暂停所有动画
     */
    GLTFAnimationComponent.prototype.pause = function (name) {
        if (!this.mixer) {
            return;
        }
        if (name) {
            // 暂停指定动画
            var action = this.actions.get(name);
            if (action) {
                action.paused = true;
                this.emitter.emit(AnimationEventType.PAUSE, { name: name, action: action });
            }
        }
        else {
            // 暂停所有动画
            for (var _i = 0, _a = this.actions; _i < _a.length; _i++) {
                var _b = _a[_i], name_2 = _b[0], action = _b[1];
                action.paused = true;
                this.emitter.emit(AnimationEventType.PAUSE, { name: name_2, action: action });
            }
        }
        this.state = AnimationState.PAUSED;
    };
    /**
     * 恢复动画
     * @param name 动画名称，如果未提供则恢复所有动画
     */
    GLTFAnimationComponent.prototype.resume = function (name) {
        if (!this.mixer || this.state !== AnimationState.PAUSED) {
            return;
        }
        if (name) {
            // 恢复指定动画
            var action = this.actions.get(name);
            if (action) {
                action.paused = false;
                this.emitter.emit(AnimationEventType.RESUME, { name: name, action: action });
            }
        }
        else {
            // 恢复所有动画
            for (var _i = 0, _a = this.actions; _i < _a.length; _i++) {
                var _b = _a[_i], name_3 = _b[0], action = _b[1];
                action.paused = false;
                this.emitter.emit(AnimationEventType.RESUME, { name: name_3, action: action });
            }
        }
        this.state = AnimationState.PLAYING;
    };
    /**
     * 获取动画剪辑
     * @returns 动画剪辑数组
     */
    GLTFAnimationComponent.prototype.getClips = function () {
        return __spreadArray([], this.clips, true);
    };
    /**
     * 获取动画混合器
     * @returns 动画混合器
     */
    GLTFAnimationComponent.prototype.getMixer = function () {
        return this.mixer;
    };
    /**
     * 获取动作
     * @param name 动画名称
     * @returns 动画动作
     */
    GLTFAnimationComponent.prototype.getAction = function (name) {
        return this.actions.get(name);
    };
    /**
     * 获取所有动作
     * @returns 动作映射
     */
    GLTFAnimationComponent.prototype.getActions = function () {
        return new Map(this.actions);
    };
    /**
     * 获取当前状态
     * @returns 动画状态
     */
    GLTFAnimationComponent.prototype.getState = function () {
        return this.state;
    };
    /**
     * 设置时间比例
     * @param scale 时间比例
     */
    GLTFAnimationComponent.prototype.setTimeScale = function (scale) {
        this.timeScale = scale;
        // 更新所有动作的时间比例
        for (var _i = 0, _a = this.actions.values(); _i < _a.length; _i++) {
            var action = _a[_i];
            action.timeScale = scale;
        }
    };
    /**
     * 获取时间比例
     * @returns 时间比例
     */
    GLTFAnimationComponent.prototype.getTimeScale = function () {
        return this.timeScale;
    };
    /**
     * 设置是否启用
     * @param enabled 是否启用
     */
    GLTFAnimationComponent.prototype.setEnabled = function (enabled) {
        this.enabled = enabled;
    };
    /**
     * 是否启用
     * @returns 是否启用
     */
    GLTFAnimationComponent.prototype.isEnabled = function () {
        return this.enabled;
    };
    /**
     * 添加事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    GLTFAnimationComponent.prototype.on = function (type, listener) {
        this.emitter.on(type, listener);
    };
    /**
     * 移除事件监听器
     * @param type 事件类型
     * @param listener 监听器
     */
    GLTFAnimationComponent.prototype.off = function (type, listener) {
        this.emitter.off(type, listener);
    };
    /**
     * 销毁组件
     */
    GLTFAnimationComponent.prototype.dispose = function () {
        // 停止所有动画
        this.stop();
        // 清除所有事件监听器
        this.emitter.removeAllListeners();
        // 清除动作
        this.actions.clear();
        // 销毁混合器
        if (this.mixer) {
            this.mixer.stopAllAction();
            this.mixer.uncacheRoot(this.mixer.getRoot());
        }
    };
    /** 组件类型 */
    GLTFAnimationComponent.type = 'GLTFAnimationComponent';
    return GLTFAnimationComponent;
}(Component_1.Component));
