/**
 * 级联阴影映射示例
 * 展示如何使用级联阴影映射系统创建高质量阴影
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { World } from '../../src/core/World';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Renderer } from '../../src/rendering/Renderer';
import { Camera } from '../../src/rendering/Camera';
import { Light, LightType } from '../../src/rendering/Light';
import { Transform } from '../../src/scene/Transform';
import { ShadowSystem } from '../../src/rendering/shadows/ShadowSystem';
import { RenderSystem } from '../../src/rendering/RenderSystem';

/**
 * 级联阴影映射示例类
 */
export class CSMExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 世界实例 */
  private world: World;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 相机实体 */
  private cameraEntity: Entity;
  
  /** 方向光实体 */
  private directionalLightEntity: Entity;
  
  /** 渲染系统 */
  private renderSystem: RenderSystem;
  
  /** 阴影系统 */
  private shadowSystem: ShadowSystem;
  
  /** 地面实体 */
  private groundEntity: Entity;
  
  /** 球体实体列表 */
  private sphereEntities: Entity[] = [];
  
  /** 立方体实体列表 */
  private boxEntities: Entity[] = [];
  
  /** 动画ID */
  private animationId: number = 0;
  
  /** 是否正在运行 */
  private running: boolean = false;
  
  /**
   * 创建示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建世界
    this.world = new World();
    this.engine.setWorld(this.world);
    
    // 创建渲染器
    const renderer = new Renderer({
      width: window.innerWidth,
      height: window.innerHeight,
      antialias: true,
      shadows: true,
      shadowMapType: THREE.PCFSoftShadowMap
    });
    
    // 创建渲染系统
    this.renderSystem = new RenderSystem(renderer, {
      enableShadows: true,
      enablePostProcessing: false
    });
    this.world.addSystem(this.renderSystem);
    
    // 获取阴影系统
    this.shadowSystem = this.renderSystem.getShadowSystem()!;
    
    // 创建场景
    this.scene = new Scene('CSM示例场景');
    this.scene.setAmbientLight(new THREE.Color(0x404040), 0.3);
    this.world.addEntity(this.scene);
    
    // 设置活跃场景
    this.renderSystem.setActiveScene(this.scene);
    
    // 创建相机
    this.cameraEntity = new Entity('相机');
    this.cameraEntity.addComponent(new Camera({
      type: 'perspective',
      fov: 60,
      near: 0.1,
      far: 1000,
      aspect: window.innerWidth / window.innerHeight
    }));
    this.cameraEntity.addComponent(new Transform({
      position: { x: 0, y: 5, z: 15 },
      rotation: { x: -0.3, y: 0, z: 0 }
    }));
    this.world.addEntity(this.cameraEntity);
    this.scene.addEntity(this.cameraEntity);
    
    // 设置活跃相机
    this.renderSystem.setActiveCamera(this.cameraEntity.getComponent('Camera') as Camera);
    
    // 创建方向光
    this.directionalLightEntity = new Entity('方向光');
    this.directionalLightEntity.addComponent(new Light({
      type: LightType.DIRECTIONAL,
      color: 0xffffff,
      intensity: 1,
      castShadow: true,
      shadowMapSize: 2048,
      shadowCameraNear: 0.1,
      shadowCameraFar: 500,
      shadowCameraLeft: -50,
      shadowCameraRight: 50,
      shadowCameraTop: 50,
      shadowCameraBottom: -50,
      shadowBias: -0.0005
    }));
    this.directionalLightEntity.addComponent(new Transform({
      position: { x: 20, y: 30, z: 20 },
      rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 }
    }));
    this.world.addEntity(this.directionalLightEntity);
    this.scene.addEntity(this.directionalLightEntity);
    
    // 创建场景内容
    this.createSceneContent();
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', this.handleResize.bind(this));
    
    // 添加DOM元素
    this.addDomElements();
  }
  
  /**
   * 创建场景内容
   */
  private createSceneContent(): void {
    // 创建地面
    this.groundEntity = new Entity('地面');
    this.groundEntity.addComponent(new Transform({
      position: { x: 0, y: -0.5, z: 0 },
      scale: { x: 100, y: 1, z: 100 }
    }));
    
    // 创建地面网格
    const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
    const groundMaterial = new THREE.MeshStandardMaterial({
      color: 0x808080,
      roughness: 0.7,
      metalness: 0.1
    });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.receiveShadow = true;
    
    // 添加到实体
    this.groundEntity.getComponent('Transform')?.getObject3D().add(groundMesh);
    
    // 添加到场景
    this.world.addEntity(this.groundEntity);
    this.scene.addEntity(this.groundEntity);
    
    // 创建多个球体
    for (let i = 0; i < 10; i++) {
      const sphereEntity = new Entity(`球体${i}`);
      sphereEntity.addComponent(new Transform({
        position: {
          x: Math.random() * 20 - 10,
          y: Math.random() * 5 + 1,
          z: Math.random() * 20 - 10
        },
        scale: { x: 1, y: 1, z: 1 }
      }));
      
      // 创建球体网格
      const sphereGeometry = new THREE.SphereGeometry(1, 32, 32);
      const sphereMaterial = new THREE.MeshStandardMaterial({
        color: new THREE.Color(Math.random(), Math.random(), Math.random()),
        roughness: 0.5,
        metalness: 0.2
      });
      const sphereMesh = new THREE.Mesh(sphereGeometry, sphereMaterial);
      sphereMesh.castShadow = true;
      sphereMesh.receiveShadow = true;
      
      // 添加到实体
      sphereEntity.getComponent('Transform')?.getObject3D().add(sphereMesh);
      
      // 添加到场景
      this.world.addEntity(sphereEntity);
      this.scene.addEntity(sphereEntity);
      
      // 添加到列表
      this.sphereEntities.push(sphereEntity);
    }
    
    // 创建多个立方体
    for (let i = 0; i < 10; i++) {
      const boxEntity = new Entity(`立方体${i}`);
      boxEntity.addComponent(new Transform({
        position: {
          x: Math.random() * 20 - 10,
          y: Math.random() * 5 + 1,
          z: Math.random() * 20 - 10
        },
        rotation: {
          x: Math.random() * Math.PI,
          y: Math.random() * Math.PI,
          z: Math.random() * Math.PI
        },
        scale: { x: 1, y: 1, z: 1 }
      }));
      
      // 创建立方体网格
      const boxGeometry = new THREE.BoxGeometry(1, 1, 1);
      const boxMaterial = new THREE.MeshStandardMaterial({
        color: new THREE.Color(Math.random(), Math.random(), Math.random()),
        roughness: 0.5,
        metalness: 0.2
      });
      const boxMesh = new THREE.Mesh(boxGeometry, boxMaterial);
      boxMesh.castShadow = true;
      boxMesh.receiveShadow = true;
      
      // 添加到实体
      boxEntity.getComponent('Transform')?.getObject3D().add(boxMesh);
      
      // 添加到场景
      this.world.addEntity(boxEntity);
      this.scene.addEntity(boxEntity);
      
      // 添加到列表
      this.boxEntities.push(boxEntity);
    }
  }
  
  /**
   * 添加DOM元素
   */
  private addDomElements(): void {
    // 创建信息面板
    const infoPanel = document.createElement('div');
    infoPanel.style.position = 'absolute';
    infoPanel.style.top = '10px';
    infoPanel.style.left = '10px';
    infoPanel.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    infoPanel.style.color = 'white';
    infoPanel.style.padding = '10px';
    infoPanel.style.borderRadius = '5px';
    infoPanel.style.fontFamily = 'Arial, sans-serif';
    infoPanel.innerHTML = `
      <h2>级联阴影映射示例</h2>
      <p>这个示例展示了如何使用级联阴影映射系统创建高质量阴影。</p>
      <p>级联阴影映射将视锥体分割成多个区域，每个区域使用不同的阴影贴图，从而在近处提供高精度阴影，在远处提供覆盖范围更广的阴影。</p>
    `;
    document.body.appendChild(infoPanel);
  }
  
  /**
   * 处理窗口大小变化
   */
  private handleResize(): void {
    // 更新相机宽高比
    const camera = this.cameraEntity.getComponent('Camera') as Camera;
    camera.setAspect(window.innerWidth / window.innerHeight);
  }
  
  /**
   * 更新场景
   * @param deltaTime 帧间隔时间（秒）
   */
  private update(deltaTime: number): void {
    // 旋转球体
    for (let i = 0; i < this.sphereEntities.length; i++) {
      const transform = this.sphereEntities[i].getComponent('Transform');
      if (transform) {
        const position = transform.getPosition();
        position.y = Math.max(1, position.y + Math.sin(Date.now() * 0.001 + i) * 0.05);
        transform.setPosition(position.x, position.y, position.z);
      }
    }
    
    // 旋转立方体
    for (let i = 0; i < this.boxEntities.length; i++) {
      const transform = this.boxEntities[i].getComponent('Transform');
      if (transform) {
        const rotation = transform.getRotation();
        rotation.x += deltaTime * 0.5;
        rotation.y += deltaTime * 0.3;
        transform.setRotation(rotation.x, rotation.y, rotation.z);
      }
    }
    
    // 更新世界
    this.world.update(deltaTime);
  }
  
  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;
    
    const now = performance.now();
    const deltaTime = Math.min((now - (this._lastTime || now)) / 1000, 0.1);
    this._lastTime = now;
    
    this.update(deltaTime);
    
    this.animationId = requestAnimationFrame(this.animate.bind(this));
  }
  
  /** 上一帧时间 */
  private _lastTime: number = 0;
  
  /**
   * 启动示例
   */
  public start(): void {
    if (this.running) return;
    
    this.running = true;
    this._lastTime = performance.now();
    this.animate();
  }
  
  /**
   * 停止示例
   */
  public stop(): void {
    if (!this.running) return;
    
    this.running = false;
    cancelAnimationFrame(this.animationId);
  }
  
  /**
   * 销毁示例
   */
  public dispose(): void {
    this.stop();
    
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize.bind(this));
    
    // 销毁引擎
    this.engine.dispose();
  }
}
