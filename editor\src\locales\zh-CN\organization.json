{"title": "组织权限", "description": "基于组织结构的权限管理", "addNode": "添加节点", "editNode": "编辑节点", "deleteNode": "删除节点", "nodeName": "节点名称", "nodeId": "节点ID", "nodeType": "节点类型", "parentNode": "父节点", "selectNode": "请选择一个节点", "nodeTypes": {"organization": "组织", "department": "部门", "team": "团队", "user": "用户"}, "inheritanceStrategies": {"strict": "严格继承", "additive": "叠加继承", "override": "覆盖继承", "custom": "自定义继承"}, "errors": {"nameRequired": "请输入节点名称", "typeRequired": "请选择节点类型", "parentRequired": "请选择父节点", "nodeNotFound": "节点不存在", "cannotDeleteRoot": "无法删除根节点", "cannotDeleteWithChildren": "无法删除有子节点的节点", "circularReference": "不能创建循环引用"}, "success": {"nodeAdded": "节点已添加", "nodeUpdated": "节点已更新", "nodeDeleted": "节点已删除", "permissionAdded": "权限已添加", "permissionRemoved": "权限已移除", "roleChanged": "角色已更改", "settingsSaved": "设置已保存"}, "confirmations": {"deleteNode": "确定要删除此节点吗？", "removePermission": "确定要移除此权限吗？", "changeRole": "确定要更改节点角色吗？"}, "tooltips": {"inheritedPermission": "此权限继承自父节点", "customPermission": "此权限是节点自定义的", "overriddenPermission": "此权限覆盖了父节点的权限", "nodeInfo": "查看节点详细信息", "permissionInfo": "查看权限详细信息", "inheritanceStrategy": "权限继承策略"}, "settings": {"title": "组织权限设置", "enableOrganizationPermissions": "启用组织权限", "inheritanceStrategy": "继承策略", "defaultRole": "默认角色"}, "actions": {"addPermission": "添加权限", "removePermission": "移除权限", "setRole": "设置角色", "addUser": "添加用户", "removeUser": "移除用户", "moveNode": "移动节点", "expandAll": "展开全部", "collapseAll": "折叠全部"}}