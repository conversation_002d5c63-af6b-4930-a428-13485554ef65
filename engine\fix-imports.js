#!/usr/bin/env node

/**
 * 批量修复TypeScript导入问题的脚本
 */

const fs = require('fs');
const path = require('path');

// 需要修复的导入模式
const importFixes = [
  // 核心模块 - 保持Component为实际导入，其他为类型导入
  {
    pattern: /import\s+\{\s*Entity\s*\}\s+from\s+['"]([^'"]*\/)?Entity['"];?/g,
    replacement: "import type { Entity } from '$1Entity';"
  },
  {
    pattern: /import\s+\{\s*World\s*\}\s+from\s+['"]([^'"]*\/)?World['"];?/g,
    replacement: "import type { World } from '$1World';"
  },
  {
    pattern: /import\s+\{\s*Transform\s*\}\s+from\s+['"]([^'"]*\/)?Transform['"];?/g,
    replacement: "import type { Transform } from '$1Transform';"
  },

  // 网络模块
  {
    pattern: /import\s+\{\s*NetworkEntity\s*\}\s+from\s+['"]([^'"]*\/)?NetworkEntity['"];?/g,
    replacement: "import type { NetworkEntity } from '$1NetworkEntity';"
  },

  // 物理模块 - 在非物理系统文件中使用类型导入
  {
    pattern: /import\s+\{\s*PhysicsBody\s*\}\s+from\s+['"]([^'"]*\/)?PhysicsBody['"];?/g,
    replacement: "import type { PhysicsBody } from '$1PhysicsBody';"
  },

  // 动画模块
  {
    pattern: /import\s+\{\s*AnimationClip\s*\}\s+from\s+['"]([^'"]*\/)?AnimationClip['"];?/g,
    replacement: "import type { AnimationClip } from '$1AnimationClip';"
  },

  // 修复混合导入（既有类型又有值的导入）
  {
    pattern: /import\s+\{\s*([^}]*Entity[^}]*)\s*\}\s+from\s+['"]([^'"]*\/)?Entity['"];?/g,
    replacement: (match, imports, path) => {
      // 如果导入中包含Entity，将其改为type导入
      const fixedImports = imports.replace(/\bEntity\b/g, 'type Entity');
      return `import { ${fixedImports} } from '${path || ''}Entity';`;
    }
  }
];

// 需要添加any类型的属性访问
const propertyFixes = [
  // Transform属性访问 - 更保守的修复
  {
    pattern: /(\w+)\.position\.set\(/g,
    replacement: "($1 as any).setPosition("
  },
  {
    pattern: /(\w+)\.rotation\.set\(/g,
    replacement: "($1 as any).setRotationQuaternion("
  },
  {
    pattern: /(\w+)\.scale\.set\(/g,
    replacement: "($1 as any).setScale("
  },
  {
    pattern: /(\w+)\.quaternion\.set\(/g,
    replacement: "($1 as any).setRotationQuaternion("
  }
];

// 类型断言修复
const typeAssertionFixes = [
  // 常见的类型断言
  {
    pattern: /entity\.getComponent\('Transform'\)/g,
    replacement: "entity.getComponent('Transform') as any"
  },
  {
    pattern: /entity\.getComponent\('PhysicsBody'\)/g,
    replacement: "entity.getComponent('PhysicsBody') as any"
  },
  {
    pattern: /entity\.getComponent\('NetworkEntity'\)/g,
    replacement: "entity.getComponent('NetworkEntity') as any"
  }
];

/**
 * 递归获取所有TypeScript文件
 */
function getAllTsFiles(dir, files = []) {
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      getAllTsFiles(fullPath, files);
    } else if (stat.isFile() && item.endsWith('.ts') && !item.endsWith('.d.ts')) {
      files.push(fullPath);
    }
  }

  return files;
}

/**
 * 修复单个文件
 */
function fixFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // 应用导入修复
  for (const fix of importFixes) {
    const newContent = content.replace(fix.pattern, fix.replacement);
    if (newContent !== content) {
      content = newContent;
      modified = true;
    }
  }

  // 应用属性访问修复
  for (const fix of propertyFixes) {
    const newContent = content.replace(fix.pattern, fix.replacement);
    if (newContent !== content) {
      content = newContent;
      modified = true;
    }
  }

  // 应用类型断言修复
  for (const fix of typeAssertionFixes) {
    const newContent = content.replace(fix.pattern, fix.replacement);
    if (newContent !== content) {
      content = newContent;
      modified = true;
    }
  }

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`已修复: ${filePath}`);
    return true;
  }

  return false;
}

/**
 * 主函数
 */
function main() {
  const srcDir = path.join(__dirname, 'src');
  const files = getAllTsFiles(srcDir);

  console.log(`找到 ${files.length} 个TypeScript文件`);

  let fixedCount = 0;
  for (const file of files) {
    if (fixFile(file)) {
      fixedCount++;
    }
  }

  console.log(`已修复 ${fixedCount} 个文件`);
}

if (require.main === module) {
  main();
}

module.exports = { fixFile, getAllTsFiles };
