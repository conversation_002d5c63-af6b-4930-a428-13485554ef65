"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetManager = void 0;
/**
 * 资产管理器类
 * 负责加载和管理资产
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var AssetLoader_1 = require("./AssetLoader");
var ResourceManager_1 = require("./ResourceManager");
var ResourceDependencyManager_1 = require("./ResourceDependencyManager");
var AssetManager = /** @class */ (function (_super) {
    __extends(AssetManager, _super);
    /**
     * 创建资产管理器实例
     * @param options 资产管理器选项
     */
    function AssetManager(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this) || this;
        /** 资产映射 */
        _this.assets = new Map();
        /** 是否已初始化 */
        _this.initialized = false;
        _this.baseUrl = options.baseUrl || '';
        _this.enableDependencyManagement = options.enableDependencyManagement !== undefined ? options.enableDependencyManagement : true;
        _this.loader = new AssetLoader_1.AssetLoader();
        _this.resourceManager = new ResourceManager_1.ResourceManager(options.resourceOptions);
        _this.dependencyManager = new ResourceDependencyManager_1.ResourceDependencyManager();
        return _this;
    }
    /**
     * 初始化资产管理器
     */
    AssetManager.prototype.initialize = function () {
        if (this.initialized) {
            return;
        }
        // 初始化资源管理器
        this.resourceManager.initialize();
        // 初始化依赖管理器
        if (this.enableDependencyManagement) {
            this.dependencyManager.initialize();
        }
        this.initialized = true;
        this.emit('initialized');
    };
    /**
     * 注册资产
     * @param id 资产ID
     * @param name 资产名称
     * @param type 资产类型
     * @param url 资产URL
     * @param metadata 资产元数据
     * @returns 资产信息
     */
    AssetManager.prototype.registerAsset = function (id, name, type, url, metadata) {
        if (metadata === void 0) { metadata = {}; }
        // 检查是否已存在同ID资产
        if (this.assets.has(id)) {
            throw new Error("\u5DF2\u5B58\u5728ID\u4E3A ".concat(id, " \u7684\u8D44\u4EA7"));
        }
        // 创建资产信息
        var assetInfo = {
            id: id,
            name: name,
            type: type,
            url: url,
            metadata: metadata,
            loaded: false,
        };
        // 添加到资产映射
        this.assets.set(id, assetInfo);
        // 发出资产注册事件
        this.emit('assetRegistered', assetInfo);
        return assetInfo;
    };
    /**
     * 加载资产
     * @param id 资产ID
     * @param loadDependencies 是否加载依赖
     * @returns Promise，解析为资产数据
     */
    AssetManager.prototype.loadAsset = function (id, loadDependencies) {
        if (loadDependencies === void 0) { loadDependencies = true; }
        return __awaiter(this, void 0, void 0, function () {
            var assetInfo, url, data, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        // 检查资产是否存在
                        if (!this.assets.has(id)) {
                            throw new Error("\u627E\u4E0D\u5230ID\u4E3A ".concat(id, " \u7684\u8D44\u4EA7"));
                        }
                        assetInfo = this.assets.get(id);
                        // 如果已经加载，直接返回数据
                        if (assetInfo.loaded && assetInfo.data) {
                            return [2 /*return*/, assetInfo.data];
                        }
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 5, , 6]);
                        if (!(this.enableDependencyManagement && loadDependencies)) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.loadDependencies(id)];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        url = this.resolveUrl(assetInfo.url);
                        return [4 /*yield*/, this.resourceManager.load(id, assetInfo.type, url)];
                    case 4:
                        data = _a.sent();
                        // 更新资产信息
                        assetInfo.data = data;
                        assetInfo.loaded = true;
                        assetInfo.error = undefined;
                        // 发出资产加载事件
                        this.emit('assetLoaded', assetInfo);
                        return [2 /*return*/, data];
                    case 5:
                        error_1 = _a.sent();
                        // 更新资产信息
                        assetInfo.loaded = false;
                        assetInfo.error = error_1;
                        // 发出资产加载错误事件
                        this.emit('assetError', assetInfo, error_1);
                        throw error_1;
                    case 6: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 加载资产依赖
     * @param id 资产ID
     * @returns Promise
     */
    AssetManager.prototype.loadDependencies = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var strongDependencies, promises;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.enableDependencyManagement) {
                            return [2 /*return*/];
                        }
                        strongDependencies = this.dependencyManager.getStrongDependencies(id);
                        // 如果没有强依赖，则直接返回
                        if (strongDependencies.length === 0) {
                            return [2 /*return*/];
                        }
                        promises = strongDependencies.map(function (depId) { return _this.loadAsset(depId, true); });
                        return [4 /*yield*/, Promise.all(promises)];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 批量加载资产
     * @param ids 资产ID数组
     * @param onProgress 进度回调
     * @returns Promise，解析为资产数据映射
     */
    AssetManager.prototype.loadAssets = function (ids, onProgress) {
        return __awaiter(this, void 0, void 0, function () {
            var total, loaded, results, _i, ids_1, id, data, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        total = ids.length;
                        loaded = 0;
                        results = new Map();
                        _i = 0, ids_1 = ids;
                        _a.label = 1;
                    case 1:
                        if (!(_i < ids_1.length)) return [3 /*break*/, 7];
                        id = ids_1[_i];
                        _a.label = 2;
                    case 2:
                        _a.trys.push([2, 4, , 5]);
                        return [4 /*yield*/, this.loadAsset(id)];
                    case 3:
                        data = _a.sent();
                        results.set(id, data);
                        return [3 /*break*/, 5];
                    case 4:
                        error_2 = _a.sent();
                        console.error("\u52A0\u8F7D\u8D44\u4EA7 ".concat(id, " \u5931\u8D25:"), error_2);
                        return [3 /*break*/, 5];
                    case 5:
                        loaded++;
                        if (onProgress) {
                            onProgress(loaded, total);
                        }
                        _a.label = 6;
                    case 6:
                        _i++;
                        return [3 /*break*/, 1];
                    case 7: return [2 /*return*/, results];
                }
            });
        });
    };
    /**
     * 卸载资产
     * @param id 资产ID
     * @param unloadDependents 是否卸载依赖于此资产的资产
     * @returns 是否成功卸载
     */
    AssetManager.prototype.unloadAsset = function (id, unloadDependents) {
        if (unloadDependents === void 0) { unloadDependents = false; }
        // 检查资产是否存在
        if (!this.assets.has(id)) {
            return false;
        }
        var assetInfo = this.assets.get(id);
        // 如果未加载，直接返回
        if (!assetInfo.loaded || !assetInfo.data) {
            return false;
        }
        // 如果启用依赖管理且需要卸载依赖于此资产的资产
        if (this.enableDependencyManagement && unloadDependents) {
            this.unloadDependents(id);
        }
        // 释放资源
        this.resourceManager.release(id);
        // 更新资产信息
        assetInfo.data = undefined;
        assetInfo.loaded = false;
        // 发出资产卸载事件
        this.emit('assetUnloaded', assetInfo);
        return true;
    };
    /**
     * 卸载依赖于指定资产的资产
     * @param id 资产ID
     */
    AssetManager.prototype.unloadDependents = function (id) {
        if (!this.enableDependencyManagement) {
            return;
        }
        // 获取依赖于此资产的资产
        var dependents = this.dependencyManager.getReverseDependencies(id);
        // 卸载所有依赖于此资产的资产
        for (var _i = 0, dependents_1 = dependents; _i < dependents_1.length; _i++) {
            var depId = dependents_1[_i];
            this.unloadAsset(depId, true);
        }
    };
    /**
     * 获取资产
     * @param id 资产ID
     * @returns 资产数据，如果未加载则返回null
     */
    AssetManager.prototype.getAsset = function (id) {
        // 检查资产是否存在
        if (!this.assets.has(id)) {
            return null;
        }
        var assetInfo = this.assets.get(id);
        // 如果未加载，返回null
        if (!assetInfo.loaded || !assetInfo.data) {
            return null;
        }
        return assetInfo.data;
    };
    /**
     * 获取资产信息
     * @param id 资产ID
     * @returns 资产信息，如果不存在则返回null
     */
    AssetManager.prototype.getAssetInfo = function (id) {
        return this.assets.get(id) || null;
    };
    /**
     * 获取所有资产信息
     * @returns 资产信息数组
     */
    AssetManager.prototype.getAllAssetInfo = function () {
        return Array.from(this.assets.values());
    };
    /**
     * 根据类型获取资产信息
     * @param type 资产类型
     * @returns 资产信息数组
     */
    AssetManager.prototype.getAssetInfoByType = function (type) {
        return Array.from(this.assets.values()).filter(function (asset) { return asset.type === type; });
    };
    /**
     * 根据名称查找资产信息
     * @param name 资产名称
     * @returns 资产信息数组
     */
    AssetManager.prototype.findAssetInfoByName = function (name) {
        return Array.from(this.assets.values()).filter(function (asset) { return asset.name === name; });
    };
    /**
     * 移除资产
     * @param id 资产ID
     * @returns 是否成功移除
     */
    AssetManager.prototype.removeAsset = function (id) {
        // 检查资产是否存在
        if (!this.assets.has(id)) {
            return false;
        }
        var assetInfo = this.assets.get(id);
        // 如果已加载，先卸载
        if (assetInfo.loaded && assetInfo.data) {
            this.unloadAsset(id);
        }
        // 从资产映射中移除
        this.assets.delete(id);
        // 发出资产移除事件
        this.emit('assetRemoved', assetInfo);
        return true;
    };
    /**
     * 设置基础URL
     * @param baseUrl 基础URL
     */
    AssetManager.prototype.setBaseUrl = function (baseUrl) {
        this.baseUrl = baseUrl;
    };
    /**
     * 获取基础URL
     * @returns 基础URL
     */
    AssetManager.prototype.getBaseUrl = function () {
        return this.baseUrl;
    };
    /**
     * 解析URL
     * @param url 相对URL
     * @returns 完整URL
     */
    AssetManager.prototype.resolveUrl = function (url) {
        // 如果是绝对URL或数据URL，直接返回
        if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('data:')) {
            return url;
        }
        // 拼接基础URL和相对URL
        return "".concat(this.baseUrl).concat(this.baseUrl.endsWith('/') || url.startsWith('/') ? '' : '/').concat(url);
    };
    /**
     * 获取资源管理器
     * @returns 资源管理器
     */
    AssetManager.prototype.getResourceManager = function () {
        return this.resourceManager;
    };
    /**
     * 获取资源依赖管理器
     * @returns 资源依赖管理器
     */
    AssetManager.prototype.getDependencyManager = function () {
        return this.dependencyManager;
    };
    /**
     * 获取资产加载器
     * @returns 资产加载器
     */
    AssetManager.prototype.getLoader = function () {
        return this.loader;
    };
    /**
     * 添加资产依赖关系
     * @param resourceId 资产ID
     * @param dependencyId 依赖资产ID
     * @param type 依赖类型
     * @returns 是否成功添加
     */
    AssetManager.prototype.addDependency = function (resourceId, dependencyId, type) {
        if (type === void 0) { type = ResourceDependencyManager_1.DependencyType.STRONG; }
        if (!this.enableDependencyManagement) {
            return false;
        }
        // 检查资产是否存在
        if (!this.assets.has(resourceId) || !this.assets.has(dependencyId)) {
            return false;
        }
        // 添加依赖关系
        this.dependencyManager.addDependency(resourceId, dependencyId, type);
        return true;
    };
    /**
     * 移除资产依赖关系
     * @param resourceId 资产ID
     * @param dependencyId 依赖资产ID
     * @returns 是否成功移除
     */
    AssetManager.prototype.removeDependency = function (resourceId, dependencyId) {
        if (!this.enableDependencyManagement) {
            return false;
        }
        return this.dependencyManager.removeDependency(resourceId, dependencyId);
    };
    /**
     * 获取资产的依赖
     * @param id 资产ID
     * @returns 依赖资产ID数组
     */
    AssetManager.prototype.getDependencies = function (id) {
        if (!this.enableDependencyManagement) {
            return [];
        }
        return this.dependencyManager.getDependencies(id).map(function (dep) { return dep.id; });
    };
    /**
     * 获取依赖于资产的资产
     * @param id 资产ID
     * @returns 依赖于此资产的资产ID数组
     */
    AssetManager.prototype.getDependents = function (id) {
        if (!this.enableDependencyManagement) {
            return [];
        }
        return this.dependencyManager.getReverseDependencies(id);
    };
    /**
     * 清空所有资产
     */
    AssetManager.prototype.clear = function () {
        // 卸载所有已加载的资产
        for (var _i = 0, _a = Array.from(this.assets.values()); _i < _a.length; _i++) {
            var assetInfo = _a[_i];
            if (assetInfo.loaded && assetInfo.data) {
                this.unloadAsset(assetInfo.id);
            }
        }
        // 清空资产映射
        this.assets.clear();
        // 发出清空事件
        this.emit('cleared');
    };
    /**
     * 销毁资产管理器
     */
    AssetManager.prototype.dispose = function () {
        // 清空所有资产
        this.clear();
        // 销毁资源管理器
        this.resourceManager.dispose();
        // 销毁依赖管理器
        if (this.enableDependencyManagement) {
            this.dependencyManager.dispose();
        }
        // 销毁加载器
        this.loader.dispose();
        // 移除所有事件监听器
        this.removeAllListeners();
        this.initialized = false;
    };
    return AssetManager;
}(EventEmitter_1.EventEmitter));
exports.AssetManager = AssetManager;
