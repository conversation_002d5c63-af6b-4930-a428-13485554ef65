/**
 * 组件编辑器注册表
 * 用于注册和管理组件编辑器
 */
import { ComponentType } from 'react';

// 导入组件编辑器
import TransformEditor from './scene/TransformEditor';
import MeshRendererEditor from './rendering/MeshRendererEditor';
import LightEditor from './rendering/LightEditor';
import CameraEditor from './rendering/CameraEditor';
import PhysicsBodyEditor from './physics/PhysicsBodyEditor';
import PhysicsColliderEditor from './physics/PhysicsColliderEditor';
import CharacterControllerEditor from './physics/CharacterControllerEditor';
import AdvancedCharacterControllerEditor from './avatar/AdvancedCharacterControllerEditor';
import AnimationEditor from './animation/AnimationEditor';
import AudioSourceEditor from './audio/AudioSourceEditor';
import ScriptEditor from './scripting/ScriptEditor';
import ParticleSystemEditor from './effects/ParticleSystemEditor';
import UIElementEditor from './ui/UIElementEditor';
import InteractionEditor from './interaction/InteractionEditor';
import NetworkComponentEditor from './network/NetworkComponentEditor';

// 导入水体和环境光照编辑器
import WaterPhysicsEditor from './physics/water/WaterPhysicsEditor';
import WaterMaterialEditor from './rendering/water/WaterMaterialEditor';
import UndergroundLightingEditor from './rendering/environment/UndergroundLightingEditor';

/**
 * 组件编辑器接口
 */
export interface ComponentEditor {
  /** 组件类型 */
  componentType: string;
  /** 编辑器组件 */
  editorComponent: ComponentType<any>;
  /** 显示名称 */
  displayName: string;
  /** 描述 */
  description?: string;
  /** 图标 */
  icon?: string;
  /** 分类 */
  category?: string;
  /** 排序顺序 */
  order?: number;
}

/**
 * 组件编辑器注册表
 */
class ComponentEditorRegistry {
  /** 编辑器映射 */
  private editors: Map<string, ComponentEditor> = new Map();

  /**
   * 注册组件编辑器
   * @param editor 组件编辑器
   */
  public register(editor: ComponentEditor): void {
    this.editors.set(editor.componentType, editor);
  }

  /**
   * 获取组件编辑器
   * @param componentType 组件类型
   * @returns 组件编辑器
   */
  public getEditor(componentType: string): ComponentEditor | undefined {
    return this.editors.get(componentType);
  }

  /**
   * 获取所有组件编辑器
   * @returns 组件编辑器数组
   */
  public getAllEditors(): ComponentEditor[] {
    return Array.from(this.editors.values());
  }

  /**
   * 获取分类的组件编辑器
   * @param category 分类
   * @returns 组件编辑器数组
   */
  public getEditorsByCategory(category: string): ComponentEditor[] {
    return Array.from(this.editors.values()).filter(editor => editor.category === category);
  }

  /**
   * 初始化注册表
   */
  public initialize(): void {
    // 注册场景组件编辑器
    this.register({
      componentType: 'transform',
      editorComponent: TransformEditor,
      displayName: '变换',
      description: '控制实体的位置、旋转和缩放',
      icon: 'transform',
      category: '场景',
      order: 0
    });

    // 注册渲染组件编辑器
    this.register({
      componentType: 'meshRenderer',
      editorComponent: MeshRendererEditor,
      displayName: '网格渲染器',
      description: '渲染3D网格',
      icon: 'mesh',
      category: '渲染',
      order: 0
    });

    this.register({
      componentType: 'light',
      editorComponent: LightEditor,
      displayName: '光源',
      description: '添加光照效果',
      icon: 'light',
      category: '渲染',
      order: 1
    });

    this.register({
      componentType: 'camera',
      editorComponent: CameraEditor,
      displayName: '相机',
      description: '控制场景视角',
      icon: 'camera',
      category: '渲染',
      order: 2
    });

    // 注册物理组件编辑器
    this.register({
      componentType: 'physicsBody',
      editorComponent: PhysicsBodyEditor,
      displayName: '物理体',
      description: '添加物理属性',
      icon: 'physics',
      category: '物理',
      order: 0
    });

    this.register({
      componentType: 'physicsCollider',
      editorComponent: PhysicsColliderEditor,
      displayName: '碰撞器',
      description: '添加碰撞检测',
      icon: 'collider',
      category: '物理',
      order: 1
    });

    this.register({
      componentType: 'characterController',
      editorComponent: CharacterControllerEditor,
      displayName: '角色控制器',
      description: '控制角色移动和碰撞',
      icon: 'character',
      category: '物理',
      order: 2
    });

    // 注册角色组件编辑器
    this.register({
      componentType: 'advancedCharacterController',
      editorComponent: AdvancedCharacterControllerEditor,
      displayName: '高级角色控制器',
      description: '提供高级角色控制功能',
      icon: 'character-advanced',
      category: '角色',
      order: 0
    });

    // 注册动画组件编辑器
    this.register({
      componentType: 'animation',
      editorComponent: AnimationEditor,
      displayName: '动画',
      description: '控制模型动画',
      icon: 'animation',
      category: '动画',
      order: 0
    });

    // 注册音频组件编辑器
    this.register({
      componentType: 'audioSource',
      editorComponent: AudioSourceEditor,
      displayName: '音频源',
      description: '播放音频',
      icon: 'audio',
      category: '音频',
      order: 0
    });

    // 注册脚本组件编辑器
    this.register({
      componentType: 'script',
      editorComponent: ScriptEditor,
      displayName: '脚本',
      description: '添加自定义脚本',
      icon: 'script',
      category: '脚本',
      order: 0
    });

    // 注册特效组件编辑器
    this.register({
      componentType: 'particleSystem',
      editorComponent: ParticleSystemEditor,
      displayName: '粒子系统',
      description: '创建粒子特效',
      icon: 'particle',
      category: '特效',
      order: 0
    });

    // 注册UI组件编辑器
    this.register({
      componentType: 'uiElement',
      editorComponent: UIElementEditor,
      displayName: 'UI元素',
      description: '创建用户界面元素',
      icon: 'ui',
      category: 'UI',
      order: 0
    });

    // 注册交互组件编辑器
    this.register({
      componentType: 'interaction',
      editorComponent: InteractionEditor,
      displayName: '交互',
      description: '添加交互功能',
      icon: 'interaction',
      category: '交互',
      order: 0
    });

    // 注册网络组件编辑器
    this.register({
      componentType: 'networkComponent',
      editorComponent: NetworkComponentEditor,
      displayName: '网络组件',
      description: '添加网络同步功能',
      icon: 'network',
      category: '网络',
      order: 0
    });

    // 注册水体物理编辑器
    this.register({
      componentType: 'waterBody',
      editorComponent: WaterPhysicsEditor,
      displayName: '水体物理',
      description: '编辑水体物理属性',
      icon: 'water',
      category: '物理',
      order: 3
    });

    // 注册水体材质编辑器
    this.register({
      componentType: 'waterMaterial',
      editorComponent: WaterMaterialEditor,
      displayName: '水体材质',
      description: '编辑水体材质属性',
      icon: 'water',
      category: '渲染',
      order: 3
    });

    // 注册地下环境光照编辑器
    this.register({
      componentType: 'undergroundLighting',
      editorComponent: UndergroundLightingEditor,
      displayName: '地下环境光照',
      description: '编辑地下环境光照效果',
      icon: 'light',
      category: '渲染',
      order: 4
    });
  }
}

// 创建单例实例
const componentEditorRegistry = new ComponentEditorRegistry();
componentEditorRegistry.initialize();

export default componentEditorRegistry;
