{
  "videoTutorials": {
    "title": "视频教程",
    "allTutorials": "所有教程",
    "recommended": "推荐",
    "watched": "已观看",
    "search": "搜索",
    "searchPlaceholder": "搜索视频教程...",
    "searchResults": "搜索结果：找到 {count} 个与"{query}"相关的教程",
    "noSearchResults": "没有找到匹配的结果",
    "noRecommendations": "没有推荐的教程，您已经观看了所有教程！",
    "watch": "观看",
    "continue": "继续观看",
    "rewatch": "重新观看",
    "minutes": "分钟",
    "loading": "正在加载视频教程...",
    "browserNotSupported": "您的浏览器不支持视频播放，请升级浏览器或使用其他浏览器。",
    "playbackSpeed": "播放速度",
    "toggleChapters": "显示/隐藏章节",
    "fullscreen": "全屏",
    "chapters": "章节",
    "categories": {
      "getting-started": "入门指南",
      "materials": "材质编辑",
      "animation": "动画系统",
      "scripting": "脚本编程",
      "physics": "物理系统",
      "ui": "UI系统",
      "networking": "网络系统",
      "advanced": "高级主题"
    },
    "difficulty": {
      "beginner": "初级",
      "intermediate": "中级",
      "advanced": "高级"
    },
    "editorBasics": {
      "title": "编辑器基础",
      "description": "学习DL（Digital Learning）引擎编辑器的基本界面和操作。",
      "chapters": {
        "introduction": "介绍",
        "interfaceOverview": "界面概览",
        "sceneNavigation": "场景导航",
        "objectManipulation": "对象操作",
        "conclusion": "总结"
      }
    },
    "materialEditing": {
      "title": "材质编辑",
      "description": "学习如何创建和编辑材质，包括PBR材质和纹理映射。",
      "chapters": {
        "introduction": "介绍",
        "materialTypes": "材质类型",
        "textureMapping": "纹理映射",
        "pbrMaterials": "PBR材质",
        "shaderVariants": "着色器变体",
        "conclusion": "总结"
      }
    },
    "animationSystem": {
      "title": "动画系统",
      "description": "学习如何使用DL（Digital Learning）引擎的动画系统创建和编辑动画。",
      "chapters": {
        "introduction": "介绍",
        "animationTypes": "动画类型",
        "keyframeAnimation": "关键帧动画",
        "animationBlending": "动画混合",
        "stateMachines": "状态机",
        "conclusion": "总结"
      }
    },
    "visualScripting": {
      "title": "视觉脚本",
      "description": "学习如何使用视觉脚本系统创建交互式内容。",
      "chapters": {
        "introduction": "介绍",
        "basicConcepts": "基本概念",
        "nodeTypes": "节点类型",
        "creatingLogic": "创建逻辑",
        "debugging": "调试",
        "conclusion": "总结"
      }
    },
    "physicsSystem": {
      "title": "物理系统",
      "description": "学习如何使用DL（Digital Learning）引擎的物理系统创建真实的物理交互。",
      "chapters": {
        "introduction": "介绍",
        "rigidBodies": "刚体",
        "colliders": "碰撞器",
        "joints": "关节",
        "softBodies": "软体",
        "conclusion": "总结"
      }
    },
    "uiSystem": {
      "title": "UI系统",
      "description": "学习如何使用DL（Digital Learning）引擎的UI系统创建用户界面。",
      "chapters": {
        "introduction": "介绍",
        "uiComponents": "UI组件",
        "layouts": "布局",
        "styling": "样式",
        "interaction": "交互",
        "conclusion": "总结"
      }
    },
    "networkingSystem": {
      "title": "网络系统",
      "description": "学习如何使用DL（Digital Learning）引擎的网络系统创建多用户体验。",
      "chapters": {
        "introduction": "介绍",
        "networkArchitecture": "网络架构",
        "entitySynchronization": "实体同步",
        "rpc": "远程过程调用",
        "roomManagement": "房间管理",
        "conclusion": "总结"
      }
    }
  }
}
