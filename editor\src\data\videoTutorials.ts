/**
 * 视频教程数据
 * 定义编辑器中可用的视频教程
 */
import { VideoTutorial } from '../services/VideoTutorialService';

// 视频教程数据
export const videoTutorials: VideoTutorial[] = [
  // 编辑器基础教程
  {
    id: 'editor-basics',
    title: '编辑器基础入门',
    description: '了解DL（Digital Learning）引擎编辑器的基本界面和操作方法，包括界面布局、导航、选择和变换工具等。',
    category: 'getting-started',
    difficulty: 'beginner',
    duration: 10,
    thumbnailUrl: '/assets/images/tutorials/editor-basics-thumbnail.jpg',
    videoUrl: '/assets/videos/tutorials/editor-basics.mp4',
    subtitlesUrl: '/assets/videos/tutorials/subtitles/editor-basics.vtt',
    tags: ['编辑器', '基础', '界面'],
    chapters: [
      {
        id: 'introduction',
        title: '介绍',
        startTime: 0,
        endTime: 60
      },
      {
        id: 'interface-overview',
        title: '界面概览',
        startTime: 60,
        endTime: 180
      },
      {
        id: 'navigation',
        title: '场景导航',
        startTime: 180,
        endTime: 300
      },
      {
        id: 'selection-tools',
        title: '选择工具',
        startTime: 300,
        endTime: 420
      },
      {
        id: 'transform-tools',
        title: '变换工具',
        startTime: 420,
        endTime: 540
      },
      {
        id: 'conclusion',
        title: '总结',
        startTime: 540,
        endTime: 600
      }
    ]
  },
  
  // 角色创建基础教程
  {
    id: 'character-creation-basics',
    title: '角色创建基础',
    description: '学习如何在DL（Digital Learning）引擎中创建和设置3D角色，包括导入模型、设置骨骼和添加基本动画。',
    category: 'character',
    difficulty: 'beginner',
    duration: 15,
    thumbnailUrl: '/assets/images/tutorials/character-creation-basics-thumbnail.jpg',
    videoUrl: '/assets/videos/tutorials/character-creation-basics.mp4',
    subtitlesUrl: '/assets/videos/tutorials/subtitles/character-creation-basics.vtt',
    tags: ['角色', '模型', '骨骼', '动画'],
    prerequisites: ['editor-basics'],
    chapters: [
      {
        id: 'introduction',
        title: '介绍',
        startTime: 0,
        endTime: 60
      },
      {
        id: 'model-preparation',
        title: '模型准备',
        startTime: 60,
        endTime: 180
      },
      {
        id: 'model-import',
        title: '模型导入',
        startTime: 180,
        endTime: 300
      },
      {
        id: 'skeleton-setup',
        title: '骨骼设置',
        startTime: 300,
        endTime: 480
      },
      {
        id: 'basic-animation',
        title: '基本动画',
        startTime: 480,
        endTime: 780
      },
      {
        id: 'conclusion',
        title: '总结',
        startTime: 780,
        endTime: 900
      }
    ]
  },
  
  // 面部动画教程
  {
    id: 'facial-animation',
    title: '面部动画和口型同步',
    description: '学习如何为角色添加面部表情动画和基于音频的口型同步功能。',
    category: 'character',
    difficulty: 'intermediate',
    duration: 20,
    thumbnailUrl: '/assets/images/tutorials/facial-animation-thumbnail.jpg',
    videoUrl: '/assets/videos/tutorials/facial-animation.mp4',
    subtitlesUrl: '/assets/videos/tutorials/subtitles/facial-animation.vtt',
    tags: ['角色', '面部动画', '口型同步', '表情'],
    prerequisites: ['character-creation-basics'],
    chapters: [
      {
        id: 'introduction',
        title: '介绍',
        startTime: 0,
        endTime: 60
      },
      {
        id: 'facial-animation-basics',
        title: '面部动画基础',
        startTime: 60,
        endTime: 240
      },
      {
        id: 'blend-shapes-setup',
        title: '混合形状设置',
        startTime: 240,
        endTime: 420
      },
      {
        id: 'expression-creation',
        title: '表情创建',
        startTime: 420,
        endTime: 600
      },
      {
        id: 'lip-sync-setup',
        title: '口型同步设置',
        startTime: 600,
        endTime: 840
      },
      {
        id: 'audio-analysis',
        title: '音频分析',
        startTime: 840,
        endTime: 1020
      },
      {
        id: 'testing-and-refinement',
        title: '测试和优化',
        startTime: 1020,
        endTime: 1140
      },
      {
        id: 'conclusion',
        title: '总结',
        startTime: 1140,
        endTime: 1200
      }
    ]
  },
  
  // 动画状态机教程
  {
    id: 'animation-state-machine',
    title: '动画状态机',
    description: '学习如何创建和配置动画状态机，实现复杂的角色动画控制。',
    category: 'animation',
    difficulty: 'intermediate',
    duration: 18,
    thumbnailUrl: '/assets/images/tutorials/animation-state-machine-thumbnail.jpg',
    videoUrl: '/assets/videos/tutorials/animation-state-machine.mp4',
    subtitlesUrl: '/assets/videos/tutorials/subtitles/animation-state-machine.vtt',
    tags: ['动画', '状态机', '过渡', '混合'],
    prerequisites: ['character-creation-basics'],
    chapters: [
      {
        id: 'introduction',
        title: '介绍',
        startTime: 0,
        endTime: 60
      },
      {
        id: 'state-machine-basics',
        title: '状态机基础',
        startTime: 60,
        endTime: 180
      },
      {
        id: 'creating-states',
        title: '创建状态',
        startTime: 180,
        endTime: 360
      },
      {
        id: 'transitions',
        title: '过渡设置',
        startTime: 360,
        endTime: 540
      },
      {
        id: 'parameters',
        title: '参数控制',
        startTime: 540,
        endTime: 720
      },
      {
        id: 'blending',
        title: '动画混合',
        startTime: 720,
        endTime: 900
      },
      {
        id: 'testing',
        title: '测试和调试',
        startTime: 900,
        endTime: 1020
      },
      {
        id: 'conclusion',
        title: '总结',
        startTime: 1020,
        endTime: 1080
      }
    ]
  },
  
  // 网络同步教程
  {
    id: 'network-synchronization',
    title: '角色网络同步',
    description: '学习如何实现多人场景中的角色网络同步，包括位置、动画和面部表情的同步。',
    category: 'networking',
    difficulty: 'advanced',
    duration: 25,
    thumbnailUrl: '/assets/images/tutorials/network-synchronization-thumbnail.jpg',
    videoUrl: '/assets/videos/tutorials/network-synchronization.mp4',
    subtitlesUrl: '/assets/videos/tutorials/subtitles/network-synchronization.vtt',
    tags: ['网络', '同步', '多人', '角色'],
    prerequisites: ['character-creation-basics', 'animation-state-machine'],
    chapters: [
      {
        id: 'introduction',
        title: '介绍',
        startTime: 0,
        endTime: 60
      },
      {
        id: 'network-basics',
        title: '网络基础',
        startTime: 60,
        endTime: 240
      },
      {
        id: 'server-setup',
        title: '服务器设置',
        startTime: 240,
        endTime: 420
      },
      {
        id: 'character-synchronization',
        title: '角色同步',
        startTime: 420,
        endTime: 660
      },
      {
        id: 'animation-synchronization',
        title: '动画同步',
        startTime: 660,
        endTime: 900
      },
      {
        id: 'facial-synchronization',
        title: '面部表情同步',
        startTime: 900,
        endTime: 1140
      },
      {
        id: 'optimization',
        title: '优化技巧',
        startTime: 1140,
        endTime: 1380
      },
      {
        id: 'testing',
        title: '测试和调试',
        startTime: 1380,
        endTime: 1440
      },
      {
        id: 'conclusion',
        title: '总结',
        startTime: 1440,
        endTime: 1500
      }
    ]
  }
];
