# 故障排除常见问答

本文档提供了DL（Digital Learning）引擎编辑器和项目中常见问题的故障排除指南，帮助您快速诊断和解决各种技术问题。

## 编辑器问题

### 编辑器无法启动怎么办？

编辑器无法启动的解决方案：

1. **检查系统要求**：
   - 确认您的系统满足最低系统要求
   - 更新显卡驱动到最新版本
   - 确保有足够的磁盘空间（至少10GB可用空间）

2. **以管理员身份运行**：
   - 右键点击编辑器图标
   - 选择"以管理员身份运行"
   - 尝试启动编辑器

3. **重置编辑器配置**：
   - 导航到`%APPDATA%\IR-Engine\Config`（Windows）或`~/Library/Application Support/IR-Engine/Config`（macOS）
   - 重命名或删除`EditorPrefs.json`文件
   - 重新启动编辑器（将使用默认设置）

4. **检查防病毒软件**：
   - 暂时禁用防病毒软件
   - 将DL（Digital Learning）引擎添加到防病毒软件的排除列表中
   - 检查是否有文件被隔离

5. **重新安装编辑器**：
   - 卸载DL（Digital Learning）引擎编辑器
   - 删除残留文件
   - 下载最新版本并重新安装

如果问题仍然存在，请收集日志文件（位于`%APPDATA%\IR-Engine\Logs`或`~/Library/Logs/IR-Engine`）并联系技术支持。

### 编辑器崩溃怎么办？

解决编辑器崩溃问题：

1. **检查崩溃日志**：
   - 导航到`%APPDATA%\IR-Engine\Logs`（Windows）或`~/Library/Logs/IR-Engine`（macOS）
   - 查找最近的崩溃报告
   - 分析错误信息找出可能的原因

2. **更新编辑器**：
   - 确保使用最新版本的DL（Digital Learning）引擎编辑器
   - 检查是否有针对已知崩溃问题的补丁

3. **检查插件**：
   - 启动编辑器时按住Shift键进入安全模式（不加载插件）
   - 如果不再崩溃，逐个启用插件找出问题插件
   - 更新或移除有问题的插件

4. **检查项目文件**：
   - 尝试创建新项目，看是否仍然崩溃
   - 如果只有特定项目崩溃，可能是项目文件损坏
   - 尝试从备份恢复项目

5. **系统问题**：
   - 检查系统内存和磁盘空间
   - 更新显卡驱动
   - 检查系统稳定性（运行内存测试、检查硬盘错误）

对于反复崩溃的情况，请启用详细日志记录（"编辑 > 首选项 > 调试 > 启用详细日志"），重现崩溃，然后将日志发送给技术支持。

### 编辑器界面显示异常怎么办？

解决界面显示问题：

1. **重置界面布局**：
   - 点击"窗口 > 布局 > 重置布局"
   - 或删除`%APPDATA%\IR-Engine\Config\Layout`文件夹中的布局文件

2. **显示设置**：
   - 检查显示器分辨率和缩放设置
   - 尝试不同的显示器或分辨率
   - 禁用高DPI缩放（右键点击应用程序 > 属性 > 兼容性 > 更改高DPI设置）

3. **图形设置**：
   - 点击"编辑 > 首选项 > 显示"
   - 尝试不同的渲染API（DirectX、OpenGL）
   - 调整界面缩放选项

4. **清除缓存**：
   - 关闭编辑器
   - 删除`%APPDATA%\IR-Engine\Cache`文件夹中的内容
   - 重新启动编辑器

5. **更新显卡驱动**：
   - 下载并安装最新的显卡驱动
   - 检查显卡制造商网站是否有已知问题

### 编辑器响应缓慢怎么办？

提高编辑器响应速度：

1. **检查系统资源**：
   - 打开任务管理器监控CPU、内存和磁盘使用情况
   - 关闭其他占用资源的应用程序
   - 检查后台进程

2. **优化编辑器设置**：
   - 点击"编辑 > 首选项 > 性能"
   - 减少"撤销历史大小"
   - 禁用"实时场景预览"
   - 降低"自动保存频率"

3. **优化项目**：
   - 关闭不需要的场景视图
   - 使用"资产管理器"卸载未使用的资产
   - 将大型场景分解为子场景

4. **硬件加速**：
   - 确保启用硬件加速
   - 检查图形API设置
   - 尝试不同的渲染模式

5. **清理系统**：
   - 清理磁盘空间
   - 整理磁盘碎片
   - 重启计算机释放系统资源

## 项目问题

### 项目无法加载怎么办？

解决项目加载问题：

1. **检查项目文件**：
   - 确认项目文件(.irproj)存在且未损坏
   - 检查项目文件夹权限
   - 尝试从备份恢复项目

2. **资源路径问题**：
   - 检查项目是否引用了移动或删除的外部资源
   - 确保所有依赖资源都在正确位置
   - 使用"文件 > 修复项目引用"工具

3. **版本兼容性**：
   - 检查项目是否使用较新版本的编辑器创建
   - 尝试更新编辑器到最新版本
   - 对于旧项目，使用"文件 > 导入 > 旧版项目"

4. **清除缓存**：
   - 删除项目缓存文件夹（位于项目目录下的`.cache`文件夹）
   - 重新启动编辑器并加载项目

5. **项目恢复模式**：
   - 按住Alt键同时打开项目
   - 在弹出的对话框中选择"安全模式"
   - 尝试修复或导出项目数据

### 资产导入失败怎么办？

解决资产导入问题：

1. **检查文件格式**：
   - 确认文件格式受DL（Digital Learning）引擎支持
   - 检查文件是否损坏（尝试在其他应用程序中打开）
   - 尝试将文件转换为不同格式

2. **导入设置**：
   - 调整导入设置（缩放、轴方向等）
   - 尝试不同的导入选项
   - 使用"高级导入"选项手动配置导入参数

3. **文件大小和复杂度**：
   - 检查文件是否过大（>100MB可能导致问题）
   - 简化复杂模型（减少多边形、材质等）
   - 将大型文件分解为多个小文件

4. **权限问题**：
   - 确保有足够的磁盘空间
   - 检查文件和目标文件夹的权限
   - 尝试以管理员身份运行编辑器

5. **插件问题**：
   - 检查是否需要特定插件处理该文件格式
   - 更新相关导入插件
   - 尝试使用外部转换工具预处理文件

### 场景无法保存怎么办？

解决场景保存问题：

1. **磁盘空间和权限**：
   - 确保有足够的磁盘空间
   - 检查项目文件夹的写入权限
   - 尝试保存到不同位置

2. **文件锁定**：
   - 检查文件是否被其他应用程序锁定
   - 关闭可能访问项目文件的其他程序
   - 重启编辑器释放文件锁

3. **路径长度**：
   - 检查文件路径是否过长（超过260字符）
   - 将项目移动到路径较短的位置
   - 使用较短的场景名称

4. **损坏检测**：
   - 启用"文件 > 保存前验证"选项
   - 检查控制台是否有错误消息
   - 尝试导出场景为新文件

5. **备份恢复**：
   - 检查自动备份文件夹（`项目目录/.backup`）
   - 恢复最近的自动备份
   - 使用"文件 > 恢复 > 从自动保存恢复"

### 项目构建失败怎么办？

解决项目构建问题：

1. **检查构建日志**：
   - 点击"窗口 > 日志 > 构建日志"
   - 分析错误消息找出失败原因
   - 搜索特定错误代码的解决方案

2. **资源问题**：
   - 检查是否有丢失或损坏的资源
   - 运行"文件 > 验证项目"检查资源完整性
   - 修复或替换有问题的资源

3. **平台设置**：
   - 检查目标平台的特定设置
   - 确保已安装必要的SDK和工具
   - 更新平台构建工具

4. **清理构建**：
   - 点击"文件 > 构建 > 清理构建"
   - 删除临时构建文件
   - 尝试完全重新构建

5. **项目设置**：
   - 检查项目设置中的构建配置
   - 简化构建（禁用不必要的功能）
   - 尝试不同的构建选项

## 渲染问题

### 模型显示不正确怎么办？

解决模型显示问题：

1. **检查模型导入设置**：
   - 检查缩放和轴方向设置
   - 确认是否正确导入了法线和UV
   - 尝试重新导入模型并调整设置

2. **材质问题**：
   - 检查材质是否正确分配
   - 确认纹理文件存在且未损坏
   - 检查着色器兼容性

3. **网格问题**：
   - 检查网格是否有反面或重叠面
   - 修复网格中的非流形边缘
   - 检查顶点法线方向

4. **渲染设置**：
   - 检查渲染模式（线框、实体等）
   - 调整相机裁剪平面
   - 检查对象是否在可见层

5. **显卡问题**：
   - 更新显卡驱动
   - 检查是否支持所需的图形API
   - 尝试不同的渲染路径

### 纹理显示不正确怎么办？

解决纹理显示问题：

1. **纹理导入设置**：
   - 检查纹理类型设置（普通贴图、法线贴图等）
   - 确认sRGB设置正确
   - 检查压缩设置

2. **UV映射**：
   - 确认模型有正确的UV坐标
   - 检查UV是否重叠或超出范围
   - 使用UV检查器工具查看UV布局

3. **纹理分辨率**：
   - 检查纹理分辨率是否为2的幂（如512x512）
   - 确认纹理未超过硬件支持的最大尺寸
   - 检查MIP映射设置

4. **材质设置**：
   - 确认纹理正确分配到材质属性
   - 检查纹理坐标和缩放设置
   - 尝试不同的纹理过滤模式

5. **文件格式**：
   - 确认使用支持的纹理格式
   - 检查纹理文件是否损坏
   - 尝试转换为不同格式

### 光照和阴影问题怎么办？

解决光照和阴影问题：

1. **光源设置**：
   - 检查光源类型、强度和范围
   - 确认光源位于正确位置
   - 调整光源衰减和颜色

2. **阴影设置**：
   - 检查是否启用了阴影
   - 调整阴影分辨率和距离
   - 检查阴影偏移设置

3. **材质设置**：
   - 确认材质正确响应光照
   - 检查法线贴图设置
   - 调整材质的金属度和粗糙度

4. **环境光设置**：
   - 检查环境光强度
   - 调整环境光遮蔽设置
   - 检查光照探针配置

5. **渲染路径**：
   - 尝试不同的渲染路径
   - 检查是否支持所选光照模型
   - 调整质量设置

### 后期处理效果不工作怎么办？

解决后期处理问题：

1. **检查后期处理组件**：
   - 确认后期处理组件已添加到摄像机
   - 检查后期处理组件是否启用
   - 确认后期处理层设置正确

2. **效果设置**：
   - 检查各效果的参数设置
   - 调整效果强度和阈值
   - 尝试禁用其他效果隔离问题

3. **渲染管线**：
   - 确认当前渲染管线支持所需效果
   - 检查项目的渲染设置
   - 尝试不同的渲染管线

4. **硬件支持**：
   - 确认显卡支持所需的着色器功能
   - 检查是否达到硬件限制
   - 降低效果质量设置

5. **顺序问题**：
   - 检查后期处理效果的执行顺序
   - 调整效果优先级
   - 尝试不同的效果组合

## 脚本和逻辑问题

### 脚本错误怎么办？

解决脚本错误：

1. **检查错误消息**：
   - 查看控制台窗口中的错误消息
   - 注意错误行号和描述
   - 搜索特定错误的解决方案

2. **语法错误**：
   - 检查变量名和函数名拼写
   - 确认所有括号和引号正确配对
   - 检查分号和逗号使用

3. **逻辑错误**：
   - 使用调试器逐步执行代码
   - 添加日志语句跟踪变量值
   - 检查条件语句和循环

4. **引用错误**：
   - 确认引用的对象和组件存在
   - 检查空引用和空值处理
   - 验证对象层次结构和路径

5. **API使用错误**：
   - 查阅API文档确认正确用法
   - 检查API版本兼容性
   - 尝试使用替代API或方法

### 视觉脚本不工作怎么办？

解决视觉脚本问题：

1. **检查节点连接**：
   - 确认所有节点正确连接
   - 检查执行流连接（白色连线）
   - 验证数据连接类型匹配（彩色连线）

2. **节点配置**：
   - 检查每个节点的参数设置
   - 确认引用的对象和组件存在
   - 验证变量类型和值

3. **执行顺序**：
   - 检查事件节点和执行入口点
   - 确认执行流程正确
   - 使用调试模式跟踪执行

4. **调试视觉脚本**：
   - 启用视觉脚本调试模式
   - 添加打印节点输出变量值
   - 使用断点暂停执行

5. **简化和隔离**：
   - 创建简化版本的脚本测试核心功能
   - 逐步添加节点找出问题节点
   - 尝试重建复杂部分

### 物理模拟问题怎么办？

解决物理模拟问题：

1. **碰撞体设置**：
   - 确认对象有正确的碰撞体组件
   - 检查碰撞体大小和形状
   - 验证碰撞层设置

2. **刚体设置**：
   - 检查质量、阻力和角阻力设置
   - 确认运动学设置正确
   - 验证约束设置

3. **物理材质**：
   - 检查摩擦和弹性设置
   - 尝试不同的物理材质
   - 调整物理材质组合模式

4. **物理设置**：
   - 检查项目的物理设置（"编辑 > 项目设置 > 物理"）
   - 调整物理时间步长
   - 增加求解器迭代次数

5. **调试物理**：
   - 启用物理调试可视化（"视图 > 物理调试"）
   - 检查碰撞体和接触点
   - 使用物理射线检测验证碰撞检测

### 动画不播放怎么办？

解决动画问题：

1. **动画组件**：
   - 确认对象有动画组件
   - 检查动画组件是否启用
   - 验证动画剪辑正确分配

2. **动画控制器**：
   - 检查状态机设置
   - 确认过渡条件正确
   - 验证参数值和触发器

3. **动画事件**：
   - 检查动画事件触发器
   - 确认事件处理函数存在
   - 验证事件时间点正确

4. **骨骼和绑定**：
   - 确认模型有正确的骨骼结构
   - 检查骨骼映射
   - 验证蒙皮权重

5. **动画调试**：
   - 使用动画调试器查看状态
   - 检查动画层和权重
   - 手动触发动画测试

## 性能和稳定性问题

### 内存泄漏怎么办？

识别和解决内存泄漏：

1. **使用内存分析器**：
   - 点击"窗口 > 性能 > 内存分析器"
   - 记录内存快照
   - 比较不同时间点的内存使用

2. **常见泄漏源**：
   - 检查未销毁的动态创建对象
   - 查找循环引用
   - 检查事件监听器是否正确移除

3. **资源管理**：
   - 实现对象池减少创建/销毁
   - 使用弱引用
   - 显式释放不再使用的资源

4. **场景管理**：
   - 检查场景加载/卸载是否正确清理资源
   - 使用资源卸载API
   - 实现引用计数系统

5. **定期重启**：
   - 对于长时间运行的应用，实现定期重启机制
   - 在关键点保存状态
   - 使用检查点系统

### 项目崩溃怎么办？

解决项目运行时崩溃：

1. **分析崩溃日志**：
   - 检查运行时日志（"窗口 > 日志"）
   - 查找崩溃前的错误消息
   - 识别崩溃时正在执行的操作

2. **常见崩溃原因**：
   - 空引用访问
   - 数组越界
   - 栈溢出（无限递归）
   - 内存不足
   - 线程同步问题

3. **隔离问题**：
   - 禁用脚本和组件找出问题源
   - 创建简化测试场景
   - 逐步添加功能直到崩溃重现

4. **调试模式**：
   - 使用调试构建运行项目
   - 启用详细日志记录
   - 使用断点和监视变量

5. **更新和补丁**：
   - 检查是否有针对已知崩溃的更新
   - 应用最新补丁
   - 查阅已知问题列表

### 帧率下降怎么办？

解决帧率问题：

1. **使用性能分析器**：
   - 点击"窗口 > 性能 > 性能分析器"
   - 记录性能数据
   - 识别性能瓶颈

2. **渲染优化**：
   - 减少绘制调用（合并网格、使用批处理）
   - 优化光照和阴影
   - 实现LOD系统
   - 使用遮挡剔除

3. **CPU优化**：
   - 优化脚本执行
   - 减少每帧更新
   - 使用协程分散计算
   - 实现多线程处理

4. **内存和资源**：
   - 减少动态内存分配
   - 优化资源加载
   - 实现资源池化
   - 管理垃圾回收

5. **项目设置**：
   - 调整质量设置
   - 优化物理模拟
   - 减少后期处理效果
   - 配置目标帧率

详细优化策略请参阅[性能优化最佳实践](../best-practices/performance.md)。

## 网络和多用户问题

### 网络连接失败怎么办？

解决网络连接问题：

1. **检查网络设置**：
   - 验证网络连接配置
   - 检查服务器地址和端口
   - 确认防火墙设置允许连接

2. **网络诊断**：
   - 使用网络诊断工具（"窗口 > 网络 > 诊断"）
   - 检查延迟和丢包率
   - 验证网络路由

3. **认证问题**：
   - 确认用户凭据正确
   - 检查认证令牌是否过期
   - 验证权限设置

4. **服务器状态**：
   - 检查服务器是否在线
   - 验证服务器版本兼容性
   - 检查服务器负载状态

5. **客户端设置**：
   - 更新到最新客户端版本
   - 清除网络缓存
   - 重新配置网络设置

### 多用户同步问题怎么办？

解决多用户同步问题：

1. **网络质量**：
   - 检查网络延迟和带宽
   - 优化网络设置
   - 实现网络质量适应

2. **同步设置**：
   - 检查同步频率和优先级
   - 调整插值设置
   - 优化同步数据大小

3. **权限问题**：
   - 验证用户权限设置
   - 检查对象锁定机制
   - 确认冲突解决策略

4. **状态一致性**：
   - 实现状态验证机制
   - 使用校验和检查数据一致性
   - 添加自动重同步功能

5. **调试工具**：
   - 使用网络调试器（"窗口 > 网络 > 调试器"）
   - 监控网络消息
   - 分析同步延迟和丢失

### 协作编辑冲突怎么办？

解决协作编辑冲突：

1. **冲突检测**：
   - 了解系统如何检测冲突
   - 识别冲突类型（同时编辑、版本不匹配等）
   - 使用冲突可视化工具

2. **手动解决**：
   - 使用冲突解决对话框
   - 比较不同版本
   - 选择保留、合并或放弃更改

3. **预防冲突**：
   - 使用对象锁定机制
   - 分配编辑区域
   - 改进团队沟通

4. **版本控制**：
   - 使用版本控制系统
   - 创建分支处理大型更改
   - 定期同步和合并

5. **协作工具**：
   - 使用内置聊天和注释系统
   - 协调编辑活动
   - 使用会话管理功能

详细协作策略请参阅[协作工作流最佳实践](../best-practices/collaboration-workflow.md)。

## 相关资源

- [一般问题FAQ](./general-questions.md)
- [性能问题FAQ](./performance-issues.md)
- [编辑器手册](../getting-started/editor-interface.md)
- [技术支持联系方式](../getting-started/support.md)
