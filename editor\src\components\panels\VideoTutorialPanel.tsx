/**
 * 视频教程面板
 * 显示和播放视频教程
 */
import React, { useState } from 'react';
import { Tabs } from 'antd';
import { VideoCameraOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import VideoTutorialList from '../tutorials/VideoTutorialList';
import VideoTutorialPlayer from '../tutorials/VideoTutorialPlayer';
import './VideoTutorialPanel.less';

const { TabPane } = Tabs;

interface VideoTutorialPanelProps {
  // 可以添加属性
}

const VideoTutorialPanel: React.FC<VideoTutorialPanelProps> = (props) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('list');
  const [selectedTutorialId, setSelectedTutorialId] = useState<string | null>(null);
  
  // 处理视频教程选择
  const handleTutorialSelect = (tutorialId: string) => {
    setSelectedTutorialId(tutorialId);
    setActiveTab('player');
  };
  
  // 处理返回列表
  const handleBackToList = () => {
    setActiveTab('list');
    setSelectedTutorialId(null);
  };
  
  return (
    <div className="video-tutorial-panel">
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <VideoCameraOutlined />
              {t('videoTutorials.allTutorials')}
            </span>
          }
          key="list"
        >
          <VideoTutorialList onTutorialSelect={handleTutorialSelect} />
        </TabPane>
        
        {selectedTutorialId && (
          <TabPane
            tab={
              <span>
                <PlayCircleOutlined />
                {t('videoTutorials.player')}
              </span>
            }
            key="player"
          >
            <VideoTutorialPlayer
              tutorialId={selectedTutorialId}
              onClose={handleBackToList}
            />
          </TabPane>
        )}
      </Tabs>
    </div>
  );
};

export default VideoTutorialPanel;
