.facial-animation-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  padding: 16px;
  background-color: #f5f5f5;

  .editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .ant-typography {
      margin-bottom: 0;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .timeline {
    background-color: #fff;
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

    .timeline-controls {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
    }

    .timeline-keyframes {
      position: relative;
      height: 24px;
      background-color: #f0f0f0;
      border-radius: 2px;
      margin-top: 8px;

      .keyframe-marker {
        position: absolute;
        width: 12px;
        height: 12px;
        background-color: #1890ff;
        border-radius: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        cursor: pointer;
        z-index: 1;
        transition: all 0.2s;

        &:hover {
          transform: translate(-50%, -50%) scale(1.2);
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        &.current {
          background-color: #ff4d4f;
          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
        }
      }
    }
  }

  .editor-content {
    flex: 1;
    overflow: hidden;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

    .editor-tabs {
      height: 100%;

      .ant-tabs {
        height: 100%;
        display: flex;
        flex-direction: column;

        .ant-tabs-content {
          flex: 1;
          overflow: auto;
          padding: 0 16px 16px;
        }
      }
    }
  }

  .expression-editor,
  .viseme-editor,
  .blend-shapes-editor,
  .clip-settings {
    padding: 16px 0;
  }

  .expression-presets,
  .viseme-presets {
    margin-top: 24px;

    .preset-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 12px;
      margin-top: 12px;

      .preset-card {
        cursor: pointer;
        text-align: center;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
        }

        &.selected {
          border-color: #1890ff;
          background-color: rgba(24, 144, 255, 0.05);
        }

        .preset-name {
          font-size: 12px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .lip-sync-section {
    margin-top: 24px;
  }

  .blend-shapes-editor {
    max-height: 500px;
    overflow-y: auto;
    padding-right: 8px;
  }
}
