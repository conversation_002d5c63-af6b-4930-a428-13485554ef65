.resource-bottleneck-panel {
  padding: 16px;
  
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .bottleneck-list {
    margin-top: 16px;
    
    .bottleneck-card {
      width: 100%;
      margin-bottom: 16px;
      border-left: 4px solid #d9d9d9;
      transition: all 0.3s;
      
      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }
    
    .selected-bottleneck {
      .bottleneck-card {
        border-left-color: #1890ff;
        background-color: #f0f5ff;
      }
    }
    
    .bottleneck-content {
      display: flex;
      align-items: flex-start;
      
      .bottleneck-severity {
        margin-right: 24px;
        flex-shrink: 0;
      }
      
      .bottleneck-details {
        flex: 1;
        
        .bottleneck-suggestions {
          margin-top: 8px;
          
          ul {
            margin-top: 8px;
            padding-left: 20px;
          }
        }
      }
    }
  }
  
  .ant-statistic {
    margin-bottom: 16px;
  }
  
  .memory-usage-chart {
    height: 300px;
  }
}
