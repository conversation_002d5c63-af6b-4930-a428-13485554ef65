/**
 * 曲线编辑器样式
 */
.curve-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .curve-editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background-color: #2a2a2a;
    border-bottom: 1px solid #3a3a3a;
  }
  
  .curve-editor-content {
    flex: 1;
    display: flex;
    overflow: hidden;
    
    .track-list {
      width: 200px;
      flex-shrink: 0;
      background-color: #252525;
      border-right: 1px solid #3a3a3a;
      overflow-y: auto;
      
      .track-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border-bottom: 1px solid #333;
        cursor: pointer;
        
        &:hover {
          background-color: #2f2f2f;
        }
        
        &.selected {
          background-color: #303030;
        }
        
        .track-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
          margin-right: 8px;
        }
        
        .track-name {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    
    .curve-canvas-container {
      flex: 1;
      position: relative;
      background-color: #1a1a1a;
      overflow: hidden;
      
      canvas {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }
  }
}
