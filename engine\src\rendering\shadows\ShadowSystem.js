"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShadowSystem = void 0;
/**
 * 阴影系统
 * 管理场景中的阴影效果
 */
var THREE = require("three");
var System_1 = require("../../core/System");
var CSM_1 = require("./CSM");
/**
 * 阴影系统类
 */
var ShadowSystem = exports.ShadowSystem = /** @class */ (function (_super) {
    __extends(ShadowSystem, _super);
    /**
     * 创建阴影系统
     * @param options 阴影系统配置
     */
    function ShadowSystem(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this) || this;
        /** 级联阴影映射实例 */
        _this.csm = null;
        /** 活跃相机 */
        _this.activeCamera = null;
        /** 活跃场景 */
        _this.activeScene = null;
        /** 渲染器 */
        _this.renderer = null;
        /** 阴影实体列表 */
        _this.shadowEntities = new Map();
        /** 方向光实体列表 */
        _this.directionalLights = [];
        _this.shadowEnabled = options.enabled !== undefined ? options.enabled : true;
        _this.useCSM = options.useCSM !== undefined ? options.useCSM : true;
        _this.cascades = options.cascades || 4;
        _this.shadowMapSize = options.shadowMapSize || 2048;
        _this.shadowBias = options.shadowBias || -0.000001;
        _this.maxShadowDistance = options.maxShadowDistance || 100;
        _this.fade = options.fade !== undefined ? options.fade : true;
        return _this;
    }
    /**
     * 设置渲染器
     * @param renderer Three.js渲染器
     */
    ShadowSystem.prototype.setRenderer = function (renderer) {
        this.renderer = renderer;
        // 配置渲染器的阴影设置
        if (this.shadowEnabled) {
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            renderer.shadowMap.autoUpdate = true;
        }
        else {
            renderer.shadowMap.enabled = false;
        }
    };
    /**
     * 设置活跃相机
     * @param camera 相机
     */
    ShadowSystem.prototype.setActiveCamera = function (camera) {
        this.activeCamera = camera;
        this.updateCSM();
    };
    /**
     * 设置活跃场景
     * @param scene 场景
     */
    ShadowSystem.prototype.setActiveScene = function (scene) {
        this.activeScene = scene;
        this.updateCSM();
    };
    /**
     * 添加阴影组件
     * @param entity 实体
     * @param component 阴影组件
     */
    ShadowSystem.prototype.addShadowComponent = function (entity, component) {
        var _this = this;
        this.shadowEntities.set(entity, component);
        // 获取实体的Three.js对象
        var transformComponent = entity.getComponent('Transform');
        var object3D = transformComponent === null || transformComponent === void 0 ? void 0 : transformComponent.getObject3D();
        if (object3D) {
            // 设置阴影属性
            object3D.traverse(function (object) {
                if (object instanceof THREE.Mesh) {
                    object.castShadow = component.castShadow;
                    object.receiveShadow = component.receiveShadow;
                    // 如果使用CSM且实体接收阴影，设置材质
                    if (_this.csm && component.receiveShadow && object.material) {
                        _this.csm.setupMaterial(object);
                    }
                }
            });
        }
    };
    /**
     * 移除阴影组件
     * @param entity 实体
     */
    ShadowSystem.prototype.removeShadowComponent = function (entity) {
        var _this = this;
        this.shadowEntities.delete(entity);
        // 获取实体的Three.js对象
        var transformComponent = entity.getComponent('Transform');
        var object3D = transformComponent === null || transformComponent === void 0 ? void 0 : transformComponent.getObject3D();
        if (object3D && this.csm) {
            // 清理材质
            object3D.traverse(function (object) {
                var _a, _b;
                if (object instanceof THREE.Mesh && object.material) {
                    if (Array.isArray(object.material)) {
                        for (var _i = 0, _c = object.material; _i < _c.length; _i++) {
                            var material = _c[_i];
                            (_a = _this.csm) === null || _a === void 0 ? void 0 : _a.teardownMaterial(material);
                        }
                    }
                    else {
                        (_b = _this.csm) === null || _b === void 0 ? void 0 : _b.teardownMaterial(object.material);
                    }
                }
            });
        }
    };
    /**
     * 添加方向光
     * @param entity 实体
     */
    ShadowSystem.prototype.addDirectionalLight = function (entity) {
        if (!this.directionalLights.includes(entity)) {
            this.directionalLights.push(entity);
            this.updateCSM();
        }
    };
    /**
     * 移除方向光
     * @param entity 实体
     */
    ShadowSystem.prototype.removeDirectionalLight = function (entity) {
        var index = this.directionalLights.indexOf(entity);
        if (index !== -1) {
            this.directionalLights.splice(index, 1);
            this.updateCSM();
        }
    };
    /**
     * 更新CSM
     */
    ShadowSystem.prototype.updateCSM = function () {
        var _this = this;
        // 如果不使用CSM或没有相机或场景，则销毁现有CSM
        if (!this.useCSM || !this.activeCamera || !this.activeScene || this.directionalLights.length === 0) {
            if (this.csm) {
                this.csm.dispose();
                this.csm = null;
            }
            return;
        }
        // 获取主方向光
        var mainLight = this.getMainDirectionalLight();
        if (!mainLight)
            return;
        // 如果已有CSM，更新它
        if (this.csm) {
            this.csm.changeLights(mainLight);
        }
        else {
            // 创建新的CSM
            this.csm = new CSM_1.CSM({
                light: mainLight,
                cascades: this.cascades,
                maxFar: this.maxShadowDistance,
                shadowMapSize: this.shadowMapSize,
                shadowBias: this.shadowBias,
                fade: this.fade
            });
            // 为所有接收阴影的实体设置材质
            for (var _i = 0, _a = Array.from(this.shadowEntities.entries()); _i < _a.length; _i++) {
                var _b = _a[_i], entity = _b[0], component = _b[1];
                if (component.receiveShadow) {
                    var transformComponent = entity.getComponent('Transform');
                    var object3D = transformComponent === null || transformComponent === void 0 ? void 0 : transformComponent.getObject3D();
                    if (object3D) {
                        object3D.traverse(function (object) {
                            var _a;
                            if (object instanceof THREE.Mesh && object.material) {
                                (_a = _this.csm) === null || _a === void 0 ? void 0 : _a.setupMaterial(object);
                            }
                        });
                    }
                }
            }
        }
    };
    /**
     * 获取主方向光
     * @returns Three.js方向光
     */
    ShadowSystem.prototype.getMainDirectionalLight = function () {
        if (this.directionalLights.length === 0)
            return null;
        // 使用第一个方向光作为主光源
        var mainLightEntity = this.directionalLights[0];
        var lightComponent = mainLightEntity.getComponent('Light');
        if (lightComponent && lightComponent.getType() === 'directional') {
            return lightComponent.getThreeLight();
        }
        return null;
    };
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    ShadowSystem.prototype.update = function (deltaTime) {
        if (!this.shadowEnabled || !this.csm || !this.activeCamera)
            return;
        // 更新CSM
        this.csm.update(this.activeCamera.getThreeCamera());
    };
    /**
     * 销毁系统
     */
    ShadowSystem.prototype.dispose = function () {
        if (this.csm) {
            this.csm.dispose();
            this.csm = null;
        }
        this.shadowEntities.clear();
        this.directionalLights = [];
        _super.prototype.dispose.call(this);
    };
    /** 系统类型 */
    ShadowSystem.type = 'ShadowSystem';
    return ShadowSystem;
}(System_1.System));
