/**
 * 情感响应示例
 * 演示如何使用情感响应系统生成基于情感的面部动画
 */
import * as THREE from 'three';
import { Engine } from '../../core/Engine';
import { World } from '../../core/World';
import { Entity } from '../../core/Entity';
import {
  FacialAnimationSystem,
  FacialAnimationComponent,
  FacialExpressionType,
  VisemeType
} from '../../animation/FacialAnimation';
import {
  FacialAnimationModelAdapterSystem,
  FacialAnimationModelType
} from '../../animation/adapters/FacialAnimationModelAdapterSystem';
import { AIAnimationSynthesisSystem } from '../../animation/AIAnimationSynthesisSystem';
import { EmotionResponseSystem, EmotionEventType, EmotionEventData } from '../../avatar/systems/EmotionResponseSystem';
import { GLTFLoader } from '../../loaders/GLTFLoader';
import { OrbitControls } from '../../utils/OrbitControls';

/**
 * 情感响应示例
 */
export class EmotionResponseExample {
  /** 引擎 */
  private engine: Engine;
  /** 世界 */
  private world: World;
  /** 场景 */
  private scene: THREE.Scene;
  /** 相机 */
  private camera: THREE.PerspectiveCamera;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer;
  /** 控制器 */
  private controls: OrbitControls;
  /** 角色实体 */
  private characterEntity: Entity;
  /** 面部动画系统 */
  private facialAnimationSystem: FacialAnimationSystem;
  /** 面部动画模型适配器系统 */
  private modelAdapterSystem: FacialAnimationModelAdapterSystem;
  /** AI动画合成系统 */
  private aiAnimationSystem: AIAnimationSynthesisSystem;
  /** 情感响应系统 */
  private emotionResponseSystem: EmotionResponseSystem;
  /** 是否运行中 */
  private running: boolean = false;
  /** 模型类型 */
  private modelType: FacialAnimationModelType = FacialAnimationModelType.GENERIC;
  /** 情感事件按钮元素 */
  private emotionEventButtons: HTMLElement[] = [];
  /** 情感事件类型 */
  private emotionEventTypes: EmotionEventType[] = [
    EmotionEventType.STARTLE,
    EmotionEventType.SURPRISE,
    EmotionEventType.THREAT,
    EmotionEventType.JOY,
    EmotionEventType.SADNESS,
    EmotionEventType.ANGER,
    EmotionEventType.FEAR,
    EmotionEventType.DISGUST
  ];
  /** 情感强度滑块 */
  private intensitySlider: HTMLInputElement | null = null;
  /** 情感持续时间滑块 */
  private durationSlider: HTMLInputElement | null = null;
  /** 情感混合开关 */
  private blendingToggle: HTMLInputElement | null = null;
  /** 情感记忆开关 */
  private memoryToggle: HTMLInputElement | null = null;
  /** 情感传染开关 */
  private contagionToggle: HTMLInputElement | null = null;
  /** 情感响应日志元素 */
  private responseLog: HTMLElement | null = null;

  /**
   * 构造函数
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();

    // 获取世界
    this.world = this.engine.getWorld();

    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x333333);

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    this.camera.position.set(0, 1.6, 2);

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    document.body.appendChild(this.renderer.domElement);

    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 1.6, 0);
    this.controls.update();

    // 创建角色实体
    this.characterEntity = this.world.createEntity();

    // 创建面部动画系统
    this.facialAnimationSystem = new FacialAnimationSystem(this.world, {
      debug: true,
      autoDetectAudio: true
    });

    // 创建面部动画模型适配器系统
    this.modelAdapterSystem = new FacialAnimationModelAdapterSystem(this.world, {
      debug: true,
      autoDetectBlendShapes: true
    });

    // 创建AI动画合成系统
    this.aiAnimationSystem = new AIAnimationSynthesisSystem(this.world, {
      debug: true,
      useLocalModel: true
    });

    // 创建情感响应系统
    this.emotionResponseSystem = new EmotionResponseSystem(this.world, {
      debug: true,
      enableEmotionBlending: true,
      enableEmotionMemory: true,
      enableEmotionContagion: false
    });

    // 添加系统到世界
    this.world.addSystem(this.facialAnimationSystem);
    this.world.addSystem(this.modelAdapterSystem);
    this.world.addSystem(this.aiAnimationSystem);
    this.world.addSystem(this.emotionResponseSystem);

    // 创建UI
    this.createUI();

    // 加载模型
    this.loadModel();

    // 添加灯光
    this.addLights();

    // 添加事件监听器
    this.addEventListeners();

    // 开始渲染循环
    this.start();
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    const uiContainer = document.createElement('div');
    uiContainer.style.position = 'absolute';
    uiContainer.style.top = '10px';
    uiContainer.style.left = '10px';
    uiContainer.style.padding = '10px';
    uiContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    uiContainer.style.color = 'white';
    uiContainer.style.borderRadius = '5px';
    uiContainer.style.fontFamily = 'Arial, sans-serif';
    document.body.appendChild(uiContainer);

    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '情感响应示例';
    title.style.margin = '0 0 10px 0';
    uiContainer.appendChild(title);

    // 创建情感事件按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.style.marginBottom = '10px';
    uiContainer.appendChild(buttonContainer);

    // 创建情感事件按钮
    for (const eventType of this.emotionEventTypes) {
      const button = document.createElement('button');
      button.textContent = this.getEventTypeDisplayName(eventType);
      button.style.margin = '5px';
      button.style.padding = '5px 10px';
      button.style.backgroundColor = '#4CAF50';
      button.style.border = 'none';
      button.style.borderRadius = '3px';
      button.style.color = 'white';
      button.style.cursor = 'pointer';
      button.addEventListener('click', () => this.triggerEmotionEvent(eventType));
      buttonContainer.appendChild(button);
      this.emotionEventButtons.push(button);
    }

    // 创建强度滑块
    const intensityContainer = document.createElement('div');
    intensityContainer.style.marginBottom = '10px';
    uiContainer.appendChild(intensityContainer);

    const intensityLabel = document.createElement('label');
    intensityLabel.textContent = '情感强度: ';
    intensityContainer.appendChild(intensityLabel);

    this.intensitySlider = document.createElement('input');
    this.intensitySlider.type = 'range';
    this.intensitySlider.min = '0.1';
    this.intensitySlider.max = '1.0';
    this.intensitySlider.step = '0.1';
    this.intensitySlider.value = '0.8';
    intensityContainer.appendChild(this.intensitySlider);

    const intensityValue = document.createElement('span');
    intensityValue.textContent = '0.8';
    intensityValue.style.marginLeft = '5px';
    intensityContainer.appendChild(intensityValue);

    this.intensitySlider.addEventListener('input', () => {
      intensityValue.textContent = this.intensitySlider!.value;
    });

    // 创建持续时间滑块
    const durationContainer = document.createElement('div');
    durationContainer.style.marginBottom = '10px';
    uiContainer.appendChild(durationContainer);

    const durationLabel = document.createElement('label');
    durationLabel.textContent = '持续时间: ';
    durationContainer.appendChild(durationLabel);

    this.durationSlider = document.createElement('input');
    this.durationSlider.type = 'range';
    this.durationSlider.min = '1';
    this.durationSlider.max = '10';
    this.durationSlider.step = '0.5';
    this.durationSlider.value = '3';
    durationContainer.appendChild(this.durationSlider);

    const durationValue = document.createElement('span');
    durationValue.textContent = '3秒';
    durationValue.style.marginLeft = '5px';
    durationContainer.appendChild(durationValue);

    this.durationSlider.addEventListener('input', () => {
      durationValue.textContent = `${this.durationSlider!.value}秒`;
    });

    // 创建开关容器
    const toggleContainer = document.createElement('div');
    toggleContainer.style.marginBottom = '10px';
    uiContainer.appendChild(toggleContainer);

    // 创建情感混合开关
    const blendingContainer = document.createElement('div');
    blendingContainer.style.marginBottom = '5px';
    toggleContainer.appendChild(blendingContainer);

    const blendingLabel = document.createElement('label');
    blendingLabel.textContent = '情感混合: ';
    blendingContainer.appendChild(blendingLabel);

    this.blendingToggle = document.createElement('input');
    this.blendingToggle.type = 'checkbox';
    this.blendingToggle.checked = true;
    this.blendingToggle.addEventListener('change', () => {
      this.emotionResponseSystem.config.enableEmotionBlending = this.blendingToggle!.checked;
    });
    blendingContainer.appendChild(this.blendingToggle);

    // 创建情感记忆开关
    const memoryContainer = document.createElement('div');
    memoryContainer.style.marginBottom = '5px';
    toggleContainer.appendChild(memoryContainer);

    const memoryLabel = document.createElement('label');
    memoryLabel.textContent = '情感记忆: ';
    memoryContainer.appendChild(memoryLabel);

    this.memoryToggle = document.createElement('input');
    this.memoryToggle.type = 'checkbox';
    this.memoryToggle.checked = true;
    this.memoryToggle.addEventListener('change', () => {
      this.emotionResponseSystem.config.enableEmotionMemory = this.memoryToggle!.checked;
    });
    memoryContainer.appendChild(this.memoryToggle);

    // 创建情感传染开关
    const contagionContainer = document.createElement('div');
    toggleContainer.appendChild(contagionContainer);

    const contagionLabel = document.createElement('label');
    contagionLabel.textContent = '情感传染: ';
    contagionContainer.appendChild(contagionLabel);

    this.contagionToggle = document.createElement('input');
    this.contagionToggle.type = 'checkbox';
    this.contagionToggle.checked = false;
    this.contagionToggle.addEventListener('change', () => {
      this.emotionResponseSystem.config.enableEmotionContagion = this.contagionToggle!.checked;
    });
    contagionContainer.appendChild(this.contagionToggle);

    // 创建响应日志
    const logContainer = document.createElement('div');
    logContainer.style.marginTop = '10px';
    uiContainer.appendChild(logContainer);

    const logTitle = document.createElement('h3');
    logTitle.textContent = '情感响应日志';
    logTitle.style.margin = '0 0 5px 0';
    logContainer.appendChild(logTitle);

    this.responseLog = document.createElement('div');
    this.responseLog.style.height = '200px';
    this.responseLog.style.overflowY = 'auto';
    this.responseLog.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
    this.responseLog.style.padding = '5px';
    this.responseLog.style.borderRadius = '3px';
    this.responseLog.style.fontSize = '12px';
    logContainer.appendChild(this.responseLog);
  }

  /**
   * 获取事件类型显示名称
   * @param eventType 事件类型
   * @returns 显示名称
   */
  private getEventTypeDisplayName(eventType: EmotionEventType): string {
    switch (eventType) {
      case EmotionEventType.STARTLE:
        return '惊吓';
      case EmotionEventType.SURPRISE:
        return '惊喜';
      case EmotionEventType.THREAT:
        return '威胁';
      case EmotionEventType.JOY:
        return '喜悦';
      case EmotionEventType.SADNESS:
        return '悲伤';
      case EmotionEventType.ANGER:
        return '愤怒';
      case EmotionEventType.FEAR:
        return '恐惧';
      case EmotionEventType.DISGUST:
        return '厌恶';
      case EmotionEventType.NEUTRAL:
        return '中性';
      default:
        return '未知';
    }
  }

  /**
   * 触发情感事件
   * @param eventType 事件类型
   */
  private triggerEmotionEvent(eventType: EmotionEventType): void {
    // 获取强度和持续时间
    const intensity = this.intensitySlider ? parseFloat(this.intensitySlider.value) : 0.8;
    const duration = this.durationSlider ? parseFloat(this.durationSlider.value) : 3.0;

    // 创建事件数据
    const eventData: EmotionEventData = {
      type: eventType,
      intensity,
      duration,
      description: `${this.getEventTypeDisplayName(eventType)}情感事件`
    };

    // 触发事件
    this.emotionResponseSystem.triggerEmotionEvent(eventData);

    // 添加到日志
    this.addToLog(`触发${this.getEventTypeDisplayName(eventType)}情感事件，强度: ${intensity.toFixed(1)}，持续时间: ${duration.toFixed(1)}秒`);
  }

  /**
   * 添加到日志
   * @param message 消息
   */
  private addToLog(message: string): void {
    if (!this.responseLog) return;

    // 创建日志条目
    const logEntry = document.createElement('div');
    logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
    logEntry.style.marginBottom = '3px';

    // 添加到日志
    this.responseLog.appendChild(logEntry);

    // 滚动到底部
    this.responseLog.scrollTop = this.responseLog.scrollHeight;
  }

  /**
   * 加载模型
   */
  private loadModel(): void {
    // 创建加载器
    const loader = new GLTFLoader();

    // 加载模型
    loader.load('/models/avatar/head.glb', (gltf) => {
      // 添加到场景
      this.scene.add(gltf.scene);

      // 查找骨骼网格
      let skinnedMesh: THREE.SkinnedMesh | null = null;

      gltf.scene.traverse((child) => {
        if (child instanceof THREE.SkinnedMesh) {
          skinnedMesh = child;
        }
      });

      if (skinnedMesh) {
        // 创建面部动画组件
        const facialAnimation = this.facialAnimationSystem.createFacialAnimation(this.characterEntity);

        // 将面部动画组件与模型绑定
        this.facialAnimationSystem.linkToModel(this.characterEntity, skinnedMesh);

        // 创建AI动画合成组件
        this.aiAnimationSystem.createAIAnimationSynthesis(this.characterEntity);

        console.log('模型加载完成，已绑定面部动画组件');
        console.log('混合形状字典:', skinnedMesh.morphTargetDictionary);
        console.log('混合形状影响:', skinnedMesh.morphTargetInfluences);

        // 添加到日志
        this.addToLog('模型加载完成，已绑定面部动画组件');
      } else {
        console.warn('未找到骨骼网格');
        this.addToLog('警告: 未找到骨骼网格');
      }
    }, undefined, (error) => {
      console.error('加载模型失败:', error);
      this.addToLog(`错误: 加载模型失败 - ${error.message}`);
    });
  }

  /**
   * 添加灯光
   */
  private addLights(): void {
    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(ambientLight);

    // 添加方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 2, 3);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners(): void {
    // 窗口大小变化事件
    window.addEventListener('resize', () => {
      // 更新相机
      this.camera.aspect = window.innerWidth / window.innerHeight;
      this.camera.updateProjectionMatrix();

      // 更新渲染器
      this.renderer.setSize(window.innerWidth, window.innerHeight);
    });

    // 监听情感响应事件
    this.emotionResponseSystem.addEventListener('emotionResponse', (data) => {
      this.addToLog(`情感响应: ${data.description}，表情: ${data.expression}，强度: ${data.intensity.toFixed(2)}`);
    });

    // 监听情感响应结束事件
    this.emotionResponseSystem.addEventListener('emotionResponseEnd', (data) => {
      this.addToLog(`情感响应结束: ${data.description}`);
    });
  }

  /**
   * 开始渲染循环
   */
  private start(): void {
    if (this.running) return;

    this.running = true;
    this.animate();

    this.addToLog('渲染循环已启动');
  }

  /**
   * 停止渲染循环
   */
  private stop(): void {
    this.running = false;

    this.addToLog('渲染循环已停止');
  }

  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;

    // 请求下一帧
    requestAnimationFrame(() => this.animate());

    // 更新控制器
    this.controls.update();

    // 更新世界
    this.world.update();

    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }

  /**
   * 销毁
   */
  public dispose(): void {
    // 停止渲染循环
    this.stop();

    // 移除渲染器
    if (this.renderer.domElement.parentNode) {
      this.renderer.domElement.parentNode.removeChild(this.renderer.domElement);
    }

    // 移除UI元素
    for (const button of this.emotionEventButtons) {
      if (button.parentNode) {
        button.parentNode.removeChild(button);
      }
    }

    // 清空数组
    this.emotionEventButtons = [];

    // 销毁渲染器
    this.renderer.dispose();

    // 销毁控制器
    this.controls.dispose();

    // 销毁场景
    this.scene.clear();

    // 销毁世界
    this.world.dispose();

    // 销毁引擎
    this.engine.dispose();
  }
