# DL（Digital Learning）引擎编辑器开发者文档

## 欢迎

欢迎来到DL（Digital Learning）引擎编辑器开发者文档！本文档旨在帮助开发者了解DL（Digital Learning）引擎编辑器的架构、API和扩展机制，以便开发者能够更好地使用和扩展编辑器。

## 概述

DL（Digital Learning）引擎编辑器是一个基于React、Redux和Ant Design开发的可视化编辑器，用于创建和编辑3D场景、模型、材质、动画等内容。编辑器采用模块化设计，具有高度的可扩展性和灵活性。

## 目录

- [快速入门](#快速入门)
- [架构说明](#架构说明)
- [API文档](#api文档)
- [扩展指南](#扩展指南)
- [代码规范](#代码规范)
- [贡献指南](#贡献指南)
- [常见问题](#常见问题)

## 快速入门

### 环境要求

- Node.js >= 18
- npm >= 9
- 现代浏览器（Chrome、Firefox、Edge等）

### 安装步骤

1. 克隆仓库：

```bash
git clone https://github.com/your-username/ir-engine.git
cd ir-engine/newsystem
```

2. 安装依赖：

```bash
# 安装编辑器依赖
cd editor
npm install
```

3. 启动开发服务器：

```bash
npm run dev
```

4. 在浏览器中访问：

```
http://localhost:5173
```

### 项目结构

```
editor/
├── public/                 # 静态资源
├── src/                    # 源代码
│   ├── assets/             # 资源文件
│   ├── components/         # 组件
│   ├── hooks/              # 自定义钩子
│   ├── pages/              # 页面组件
│   ├── services/           # 服务
│   ├── store/              # Redux状态管理
│   ├── styles/             # 样式文件
│   ├── types/              # 类型定义
│   ├── utils/              # 工具函数
│   ├── App.tsx             # 应用根组件
│   ├── i18n.ts             # 国际化配置
│   └── main.tsx            # 入口文件
├── tests/                  # 测试文件
├── .env                    # 环境变量
├── package.json            # 项目配置
├── tsconfig.json           # TypeScript配置
└── vite.config.ts          # Vite配置
```

### 构建和部署

构建生产版本：

```bash
npm run build
```

预览生产版本：

```bash
npm run preview
```

## 架构说明

DL（Digital Learning）引擎编辑器采用模块化架构，主要包括以下几个部分：

1. **UI层**：基于React和Ant Design的用户界面。
2. **状态管理**：基于Redux的状态管理。
3. **服务层**：提供各种服务，如引擎服务、项目服务、资产服务等。
4. **工具层**：提供各种工具函数和实用工具。
5. **引擎接口层**：与引擎核心交互的接口。

详细的架构说明请参考[架构文档](./architecture/README.md)。

## API文档

DL（Digital Learning）引擎编辑器提供了丰富的API，包括：

1. **核心服务API**：如EngineService、ProjectService、AssetService等。
2. **组件API**：如Viewport、SceneHierarchy、PropertyEditor等。
3. **状态管理API**：如Redux状态切片、选择器、操作等。
4. **工具函数API**：如数学工具、文件工具、UI工具等。

详细的API文档请参考[API文档](./api/README.md)。

## 扩展指南

DL（Digital Learning）引擎编辑器提供了多种扩展机制，允许开发者扩展编辑器的功能：

1. **面板扩展**：添加新的面板到编辑器界面。
2. **工具扩展**：添加新的工具到编辑器工具栏。
3. **组件编辑器扩展**：为自定义组件添加编辑器界面。
4. **资产类型扩展**：添加对新资产类型的支持。
5. **命令扩展**：添加新的命令到编辑器命令系统。

详细的扩展指南请参考[扩展指南](./extension-guide/README.md)。

## 代码规范

为了保持代码的一致性和可维护性，DL（Digital Learning）引擎编辑器项目采用了一系列代码规范：

1. **代码风格**：使用ESLint和Prettier进行代码风格检查和格式化。
2. **命名约定**：使用一致的命名约定，如组件使用PascalCase，变量和函数使用camelCase。
3. **注释规范**：使用JSDoc风格的注释，提供详细的文档信息。
4. **测试规范**：使用Jest进行单元测试和集成测试。

详细的代码规范请参考[代码规范](./code-style/README.md)。

## 贡献指南

我们欢迎开发者为DL（Digital Learning）引擎编辑器项目做出贡献。贡献的方式包括：

1. **报告问题**：如果你发现了问题，请在GitHub上提交Issue。
2. **提交改进**：如果你有改进建议，请提交Pull Request。
3. **编写文档**：帮助我们完善文档。
4. **开发扩展**：开发新的扩展，丰富编辑器的功能。

详细的贡献指南请参考[贡献指南](../CONTRIBUTING.md)。

## 常见问题

### 如何创建自定义面板？

请参考[扩展指南 - 面板扩展](./extension-guide/README.md#面板扩展)。

### 如何添加对新资产类型的支持？

请参考[扩展指南 - 资产类型扩展](./extension-guide/README.md#资产类型扩展)。

### 如何调试编辑器？

1. 启动开发服务器：`npm run dev`
2. 在浏览器中打开开发者工具
3. 使用Redux DevTools查看状态变化
4. 使用React DevTools查看组件结构
5. 使用控制台查看日志和错误

### 如何运行测试？

```bash
# 运行所有测试
npm test

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 运行测试覆盖率报告
npm run test:coverage
```

### 如何贡献代码？

1. Fork仓库
2. 创建特性分支：`git checkout -b feature/your-feature`
3. 提交更改：`git commit -m 'Add your feature'`
4. 推送到分支：`git push origin feature/your-feature`
5. 提交Pull Request

## 联系我们

如果你有任何问题或建议，请通过以下方式联系我们：

- **GitHub Issues**：[https://github.com/your-username/ir-engine/issues](https://github.com/your-username/ir-engine/issues)
- **邮箱**：[<EMAIL>](mailto:<EMAIL>)
- **社区**：[https://community.ir-engine.example.com](https://community.ir-engine.example.com)

## 许可证

DL（Digital Learning）引擎编辑器采用MIT许可证。详细信息请参考[LICENSE](../LICENSE)文件。
