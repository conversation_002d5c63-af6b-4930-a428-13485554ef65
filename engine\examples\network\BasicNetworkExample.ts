/**
 * 基础网络示例
 * 展示如何使用网络系统进行多用户连接和同步
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { World } from '../../src/core/World';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Transform } from '../../src/scene/Transform';
import { RenderSystem } from '../../src/rendering/RenderSystem';
import { Renderer } from '../../src/rendering/Renderer';
import { Camera, CameraType } from '../../src/rendering/Camera';
import { Light, LightType } from '../../src/rendering/Light';
import { NetworkSystem } from '../../src/network/NetworkSystem';
import { NetworkEntityComponent } from '../../src/network/components/NetworkEntityComponent';
import { NetworkTransformComponent } from '../../src/network/components/NetworkTransformComponent';
import { NetworkUserComponent } from '../../src/network/components/NetworkUserComponent';
import { Debug } from '../../src/utils/Debug';
import { UUID } from '../../src/utils/UUID';

/**
 * 基础网络示例
 */
export class BasicNetworkExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 网络系统 */
  private networkSystem: NetworkSystem;
  
  /** 本地玩家实体 */
  private localPlayer: Entity | null = null;
  
  /** 远程玩家实体映射表 */
  private remotePlayers: Map<string, Entity> = new Map();
  
  /** 网络对象映射表 */
  private networkObjects: Map<string, Entity> = new Map();
  
  /** 服务器URL */
  private serverUrl: string = 'wss://example.com/ws';
  
  /** 房间ID */
  private roomId: string = 'test-room';
  
  /** 用户ID */
  private userId: string = UUID.generate();
  
  /** 用户名 */
  private username: string = 'Player_' + Math.floor(Math.random() * 1000);
  
  /** 是否已连接 */
  private connected: boolean = false;

  /**
   * 创建基础网络示例
   * @param canvas 画布元素
   */
  constructor(canvas: HTMLCanvasElement) {
    // 创建引擎
    this.engine = new Engine({
      canvas,
      autoStart: false,
      debug: true,
    });
    
    // 初始化引擎
    this.initialize();
  }

  /**
   * 初始化引擎
   */
  private initialize(): void {
    // 初始化引擎
    this.engine.initialize();
    
    // 创建场景
    this.createScene();
    
    // 创建网络系统
    this.createNetworkSystem();
    
    // 添加事件监听器
    this.setupEventListeners();
    
    // 启动引擎
    this.engine.start();
    
    // 显示连接UI
    this.showConnectionUI();
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 创建场景
    const scene = new Scene();
    
    // 创建相机
    const camera = new Entity();
    camera.addComponent(Transform, {
      position: new THREE.Vector3(0, 5, 10),
      rotation: new THREE.Quaternion().setFromEuler(new THREE.Euler(-0.3, 0, 0)),
    });
    camera.addComponent(Camera, {
      type: CameraType.PERSPECTIVE,
      fov: 60,
      near: 0.1,
      far: 1000,
    });
    scene.addEntity(camera);
    
    // 创建灯光
    const light = new Entity();
    light.addComponent(Transform, {
      position: new THREE.Vector3(5, 10, 5),
    });
    light.addComponent(Light, {
      type: LightType.DIRECTIONAL,
      color: new THREE.Color(1, 1, 1),
      intensity: 1,
      castShadow: true,
    });
    scene.addEntity(light);
    
    // 创建地面
    const ground = new Entity();
    ground.addComponent(Transform, {
      position: new THREE.Vector3(0, 0, 0),
      scale: new THREE.Vector3(20, 1, 20),
    });
    ground.addComponent('MeshRenderer', {
      geometry: 'box',
      material: {
        color: new THREE.Color(0.5, 0.5, 0.5),
        roughness: 0.8,
      },
      receiveShadow: true,
    });
    scene.addEntity(ground);
    
    // 设置场景
    this.engine.getWorld().setActiveScene(scene);
  }

  /**
   * 创建网络系统
   */
  private createNetworkSystem(): void {
    // 创建网络系统
    this.networkSystem = new NetworkSystem({
      autoConnect: false,
      serverUrl: this.serverUrl,
      roomId: this.roomId,
      userId: this.userId,
      username: this.username,
      enableWebRTC: true,
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
      ],
    });
    
    // 添加网络系统到引擎
    this.engine.addSystem(this.networkSystem);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听网络系统事件
    this.networkSystem.on('connected', this.onConnected.bind(this));
    this.networkSystem.on('disconnected', this.onDisconnected.bind(this));
    this.networkSystem.on('error', this.onError.bind(this));
    this.networkSystem.on('userJoined', this.onUserJoined.bind(this));
    this.networkSystem.on('userLeft', this.onUserLeft.bind(this));
    this.networkSystem.on('entityCreated', this.onEntityCreated.bind(this));
    this.networkSystem.on('entityUpdated', this.onEntityUpdated.bind(this));
    this.networkSystem.on('entityDeleted', this.onEntityDeleted.bind(this));
    
    // 监听键盘输入
    window.addEventListener('keydown', this.onKeyDown.bind(this));
  }

  /**
   * 显示连接UI
   */
  private showConnectionUI(): void {
    // 创建连接UI
    const ui = document.createElement('div');
    ui.style.position = 'absolute';
    ui.style.top = '10px';
    ui.style.left = '10px';
    ui.style.padding = '10px';
    ui.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    ui.style.color = 'white';
    ui.style.fontFamily = 'Arial, sans-serif';
    ui.style.zIndex = '1000';
    
    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '网络示例';
    ui.appendChild(title);
    
    // 创建服务器URL输入框
    const serverUrlLabel = document.createElement('label');
    serverUrlLabel.textContent = '服务器URL: ';
    ui.appendChild(serverUrlLabel);
    
    const serverUrlInput = document.createElement('input');
    serverUrlInput.type = 'text';
    serverUrlInput.value = this.serverUrl;
    serverUrlInput.addEventListener('change', (e) => {
      this.serverUrl = (e.target as HTMLInputElement).value;
    });
    ui.appendChild(serverUrlInput);
    ui.appendChild(document.createElement('br'));
    
    // 创建房间ID输入框
    const roomIdLabel = document.createElement('label');
    roomIdLabel.textContent = '房间ID: ';
    ui.appendChild(roomIdLabel);
    
    const roomIdInput = document.createElement('input');
    roomIdInput.type = 'text';
    roomIdInput.value = this.roomId;
    roomIdInput.addEventListener('change', (e) => {
      this.roomId = (e.target as HTMLInputElement).value;
    });
    ui.appendChild(roomIdInput);
    ui.appendChild(document.createElement('br'));
    
    // 创建用户名输入框
    const usernameLabel = document.createElement('label');
    usernameLabel.textContent = '用户名: ';
    ui.appendChild(usernameLabel);
    
    const usernameInput = document.createElement('input');
    usernameInput.type = 'text';
    usernameInput.value = this.username;
    usernameInput.addEventListener('change', (e) => {
      this.username = (e.target as HTMLInputElement).value;
    });
    ui.appendChild(usernameInput);
    ui.appendChild(document.createElement('br'));
    
    // 创建连接按钮
    const connectButton = document.createElement('button');
    connectButton.textContent = '连接';
    connectButton.addEventListener('click', () => {
      if (!this.connected) {
        this.connect();
        connectButton.textContent = '断开连接';
      } else {
        this.disconnect();
        connectButton.textContent = '连接';
      }
    });
    ui.appendChild(connectButton);
    
    // 创建状态文本
    const statusText = document.createElement('p');
    statusText.id = 'status';
    statusText.textContent = '未连接';
    ui.appendChild(statusText);
    
    // 创建用户列表
    const userListTitle = document.createElement('h3');
    userListTitle.textContent = '用户列表';
    ui.appendChild(userListTitle);
    
    const userList = document.createElement('ul');
    userList.id = 'user-list';
    ui.appendChild(userList);
    
    // 添加到文档
    document.body.appendChild(ui);
  }

  /**
   * 更新状态文本
   * @param text 状态文本
   */
  private updateStatusText(text: string): void {
    const statusText = document.getElementById('status');
    if (statusText) {
      statusText.textContent = text;
    }
  }

  /**
   * 更新用户列表
   */
  private updateUserList(): void {
    const userList = document.getElementById('user-list');
    if (!userList) {
      return;
    }
    
    // 清空列表
    userList.innerHTML = '';
    
    // 添加本地用户
    if (this.localPlayer) {
      const userComponent = this.localPlayer.getComponent(NetworkUserComponent);
      if (userComponent) {
        const li = document.createElement('li');
        li.textContent = `${userComponent.username} (本地)`;
        li.style.fontWeight = 'bold';
        userList.appendChild(li);
      }
    }
    
    // 添加远程用户
    for (const [userId, entity] of this.remotePlayers) {
      const userComponent = entity.getComponent(NetworkUserComponent);
      if (userComponent) {
        const li = document.createElement('li');
        li.textContent = userComponent.username;
        userList.appendChild(li);
      }
    }
  }

  /**
   * 连接到服务器
   */
  private connect(): void {
    // 更新网络系统配置
    this.networkSystem.setLocalUserId(this.userId);
    
    // 连接到服务器
    this.networkSystem.connect(this.serverUrl, this.roomId);
    
    // 更新状态文本
    this.updateStatusText('正在连接...');
  }

  /**
   * 断开连接
   */
  private disconnect(): void {
    // 断开连接
    this.networkSystem.disconnect();
    
    // 更新状态文本
    this.updateStatusText('已断开连接');
    
    // 清除玩家
    this.clearPlayers();
    
    this.connected = false;
  }

  /**
   * 清除玩家
   */
  private clearPlayers(): void {
    // 清除本地玩家
    if (this.localPlayer) {
      this.engine.getWorld().getActiveScene().removeEntity(this.localPlayer);
      this.localPlayer = null;
    }
    
    // 清除远程玩家
    for (const entity of this.remotePlayers.values()) {
      this.engine.getWorld().getActiveScene().removeEntity(entity);
    }
    this.remotePlayers.clear();
    
    // 清除网络对象
    for (const entity of this.networkObjects.values()) {
      this.engine.getWorld().getActiveScene().removeEntity(entity);
    }
    this.networkObjects.clear();
    
    // 更新用户列表
    this.updateUserList();
  }

  /**
   * 创建本地玩家
   */
  private createLocalPlayer(): void {
    // 创建玩家实体
    const player = new Entity();
    
    // 添加变换组件
    player.addComponent(Transform, {
      position: new THREE.Vector3(0, 1, 0),
    });
    
    // 添加网络用户组件
    player.addComponent(NetworkUserComponent, {
      userId: this.userId,
      username: this.username,
      isLocal: true,
    });
    
    // 添加网络实体组件
    player.addComponent(NetworkEntityComponent, {
      entityId: UUID.generate(),
      ownerId: this.userId,
      isLocallyOwned: true,
    });
    
    // 添加网络变换组件
    player.addComponent(NetworkTransformComponent, {
      syncInterval: 100,
      autoSync: true,
    });
    
    // 添加渲染组件
    player.addComponent('MeshRenderer', {
      geometry: 'box',
      material: {
        color: new THREE.Color(0, 1, 0),
        roughness: 0.5,
      },
      castShadow: true,
    });
    
    // 添加到场景
    this.engine.getWorld().getActiveScene().addEntity(player);
    
    // 设置为本地玩家
    this.localPlayer = player;
    
    // 更新用户列表
    this.updateUserList();
  }

  /**
   * 创建远程玩家
   * @param userId 用户ID
   * @param username 用户名
   * @returns 玩家实体
   */
  private createRemotePlayer(userId: string, username: string): Entity {
    // 创建玩家实体
    const player = new Entity();
    
    // 添加变换组件
    player.addComponent(Transform, {
      position: new THREE.Vector3(Math.random() * 10 - 5, 1, Math.random() * 10 - 5),
    });
    
    // 添加网络用户组件
    player.addComponent(NetworkUserComponent, {
      userId,
      username,
      isLocal: false,
    });
    
    // 添加网络实体组件
    player.addComponent(NetworkEntityComponent, {
      entityId: UUID.generate(),
      ownerId: userId,
      isLocallyOwned: false,
    });
    
    // 添加网络变换组件
    player.addComponent(NetworkTransformComponent, {
      syncInterval: 100,
      autoSync: false,
    });
    
    // 添加渲染组件
    player.addComponent('MeshRenderer', {
      geometry: 'box',
      material: {
        color: new THREE.Color(1, 0, 0),
        roughness: 0.5,
      },
      castShadow: true,
    });
    
    // 添加到场景
    this.engine.getWorld().getActiveScene().addEntity(player);
    
    // 添加到远程玩家映射表
    this.remotePlayers.set(userId, player);
    
    // 更新用户列表
    this.updateUserList();
    
    return player;
  }

  /**
   * 连接成功事件处理
   */
  private onConnected(): void {
    Debug.log('BasicNetworkExample', 'Connected to server');
    
    // 更新状态
    this.connected = true;
    
    // 更新状态文本
    this.updateStatusText('已连接');
    
    // 创建本地玩家
    this.createLocalPlayer();
  }

  /**
   * 断开连接事件处理
   */
  private onDisconnected(): void {
    Debug.log('BasicNetworkExample', 'Disconnected from server');
    
    // 更新状态
    this.connected = false;
    
    // 更新状态文本
    this.updateStatusText('已断开连接');
    
    // 清除玩家
    this.clearPlayers();
  }

  /**
   * 错误事件处理
   * @param error 错误
   */
  private onError(error: any): void {
    Debug.error('BasicNetworkExample', 'Network error:', error);
    
    // 更新状态文本
    this.updateStatusText(`错误: ${error.message || '未知错误'}`);
  }

  /**
   * 用户加入事件处理
   * @param userId 用户ID
   * @param username 用户名
   */
  private onUserJoined(userId: string, username: string): void {
    Debug.log('BasicNetworkExample', `User joined: ${username} (${userId})`);
    
    // 创建远程玩家
    this.createRemotePlayer(userId, username);
  }

  /**
   * 用户离开事件处理
   * @param userId 用户ID
   */
  private onUserLeft(userId: string): void {
    Debug.log('BasicNetworkExample', `User left: ${userId}`);
    
    // 获取远程玩家
    const player = this.remotePlayers.get(userId);
    
    if (player) {
      // 从场景中移除
      this.engine.getWorld().getActiveScene().removeEntity(player);
      
      // 从远程玩家映射表中移除
      this.remotePlayers.delete(userId);
      
      // 更新用户列表
      this.updateUserList();
    }
  }

  /**
   * 实体创建事件处理
   * @param entityId 实体ID
   * @param entity 实体
   */
  private onEntityCreated(entityId: string, entity: Entity): void {
    Debug.log('BasicNetworkExample', `Entity created: ${entityId}`);
    
    // 添加到网络对象映射表
    this.networkObjects.set(entityId, entity);
  }

  /**
   * 实体更新事件处理
   * @param entityId 实体ID
   * @param entity 实体
   */
  private onEntityUpdated(entityId: string, entity: Entity): void {
    // 实体更新逻辑
  }

  /**
   * 实体删除事件处理
   * @param entityId 实体ID
   */
  private onEntityDeleted(entityId: string): void {
    Debug.log('BasicNetworkExample', `Entity deleted: ${entityId}`);
    
    // 获取网络对象
    const entity = this.networkObjects.get(entityId);
    
    if (entity) {
      // 从场景中移除
      this.engine.getWorld().getActiveScene().removeEntity(entity);
      
      // 从网络对象映射表中移除
      this.networkObjects.delete(entityId);
    }
  }

  /**
   * 键盘按下事件处理
   * @param event 键盘事件
   */
  private onKeyDown(event: KeyboardEvent): void {
    // 如果未连接或没有本地玩家，则不处理
    if (!this.connected || !this.localPlayer) {
      return;
    }
    
    // 获取变换组件
    const transform = this.localPlayer.getComponent(Transform);
    
    if (!transform) {
      return;
    }
    
    // 移动速度
    const speed = 0.5;
    
    // 根据按键移动
    switch (event.key) {
      case 'w':
        transform.position.z -= speed;
        break;
        
      case 's':
        transform.position.z += speed;
        break;
        
      case 'a':
        transform.position.x -= speed;
        break;
        
      case 'd':
        transform.position.x += speed;
        break;
        
      case 'q':
        transform.position.y += speed;
        break;
        
      case 'e':
        transform.position.y -= speed;
        break;
    }
    
    // 标记网络变换组件为脏
    const networkTransform = this.localPlayer.getComponent(NetworkTransformComponent);
    if (networkTransform) {
      networkTransform.markAllPropertiesDirty();
    }
  }
}
