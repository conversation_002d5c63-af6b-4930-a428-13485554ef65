/**
 * 成就面板组件
 */
import React, { useState, useEffect } from 'react';
import { 
  Card, 
  List, 
  Typography, 
  Tabs, 
  Progress, 
  Tag, 
  Empty, 
  Tooltip, 
  Badge, 
  Space,
  Statistic,
  Row,
  Col
} from 'antd';
import { 
  TrophyOutlined, 
  StarOutlined, 
  LockOutlined, 
  CheckCircleOutlined,
  UserOutlined,
  TeamOutlined,
  ToolOutlined,
  BookOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppSelector } from '../../store';
import { selectAchievements } from '../../store/achievements/achievementsSlice';
import { Achievement, AchievementType, AchievementDifficulty } from '../../services/AchievementService';
import './AchievementPanel.css';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

/**
 * 成就图标组件
 */
const AchievementIcon: React.FC<{ achievement: Achievement, size?: number }> = ({ 
  achievement, 
  size = 24 
}) => {
  const iconStyle = { fontSize: size };
  
  switch (achievement.icon) {
    case 'trophy':
      return <TrophyOutlined style={iconStyle} className="achievement-icon trophy" />;
    case 'star':
      return <StarOutlined style={iconStyle} className="achievement-icon star" />;
    case 'user':
      return <UserOutlined style={iconStyle} className="achievement-icon user" />;
    case 'team':
      return <TeamOutlined style={iconStyle} className="achievement-icon team" />;
    case 'scene':
      return <ToolOutlined style={iconStyle} className="achievement-icon scene" />;
    case 'animation':
      return <ToolOutlined style={iconStyle} className="achievement-icon animation" />;
    default:
      return <CheckCircleOutlined style={iconStyle} className="achievement-icon check" />;
  }
};

/**
 * 成就难度标签组件
 */
const DifficultyTag: React.FC<{ difficulty: AchievementDifficulty }> = ({ difficulty }) => {
  let color = '';
  let text = '';
  
  switch (difficulty) {
    case AchievementDifficulty.EASY:
      color = 'success';
      text = '简单';
      break;
    case AchievementDifficulty.MEDIUM:
      color = 'processing';
      text = '中等';
      break;
    case AchievementDifficulty.HARD:
      color = 'warning';
      text = '困难';
      break;
    case AchievementDifficulty.EXPERT:
      color = 'error';
      text = '专家';
      break;
  }
  
  return <Tag color={color}>{text}</Tag>;
};

/**
 * 成就类型标签组件
 */
const TypeTag: React.FC<{ type: AchievementType }> = ({ type }) => {
  let color = '';
  let text = '';
  let icon = null;
  
  switch (type) {
    case AchievementType.TUTORIAL:
      color = 'blue';
      text = '教程';
      icon = <BookOutlined />;
      break;
    case AchievementType.EDITOR:
      color = 'green';
      text = '编辑器';
      icon = <ToolOutlined />;
      break;
    case AchievementType.CREATION:
      color = 'purple';
      text = '创作';
      icon = <UserOutlined />;
      break;
    case AchievementType.COLLABORATION:
      color = 'orange';
      text = '协作';
      icon = <TeamOutlined />;
      break;
    case AchievementType.SPECIAL:
      color = 'magenta';
      text = '特殊';
      icon = <StarOutlined />;
      break;
  }
  
  return (
    <Tag color={color} icon={icon}>
      {text}
    </Tag>
  );
};

/**
 * 成就卡片组件
 */
const AchievementCard: React.FC<{ achievement: Achievement }> = ({ achievement }) => {
  const isLocked = !achievement.unlocked;
  const hasProgress = achievement.maxProgress !== undefined && achievement.progress !== undefined;
  const progressPercent = hasProgress 
    ? Math.floor((achievement.progress! / achievement.maxProgress!) * 100) 
    : 0;
  
  return (
    <Badge.Ribbon 
      text={isLocked ? '未解锁' : '已解锁'} 
      color={isLocked ? 'default' : 'gold'}
      style={{ display: isLocked ? 'none' : 'block' }}
    >
      <Card 
        className={`achievement-item-card ${isLocked ? 'locked' : 'unlocked'}`}
        hoverable
      >
        <div className="achievement-item-content">
          <div className="achievement-item-header">
            <div className="achievement-item-icon">
              {isLocked ? (
                <LockOutlined className="achievement-icon locked" />
              ) : (
                <AchievementIcon achievement={achievement} size={32} />
              )}
            </div>
            <div className="achievement-item-title">
              <Title level={5} className={isLocked ? 'locked-text' : ''}>
                {achievement.title}
              </Title>
              <Space size={4}>
                <DifficultyTag difficulty={achievement.difficulty} />
                <TypeTag type={achievement.type} />
              </Space>
            </div>
          </div>
          
          <Paragraph 
            className={`achievement-item-description ${isLocked ? 'locked-text' : ''}`}
            ellipsis={{ rows: 2, expandable: false }}
          >
            {achievement.description}
          </Paragraph>
          
          {hasProgress && (
            <div className="achievement-item-progress">
              <Progress 
                percent={progressPercent} 
                size="small" 
                status={isLocked ? "active" : "success"} 
                format={percent => `${percent}%`}
              />
              <Text className="achievement-progress-text">
                {achievement.progress} / {achievement.maxProgress}
              </Text>
            </div>
          )}
          
          {achievement.reward && !isLocked && (
            <div className="achievement-item-reward">
              <Tag color="gold">
                奖励: {achievement.reward.type === 'badge' ? '徽章' : '特殊物品'}
              </Tag>
            </div>
          )}
          
          {!isLocked && achievement.unlockedAt && (
            <div className="achievement-item-unlock-date">
              <Text type="secondary">
                解锁于: {new Date(achievement.unlockedAt).toLocaleDateString()}
              </Text>
            </div>
          )}
        </div>
      </Card>
    </Badge.Ribbon>
  );
};

/**
 * 成就统计组件
 */
const AchievementStats: React.FC<{ achievements: Achievement[] }> = ({ achievements }) => {
  const totalCount = achievements.length;
  const unlockedCount = achievements.filter(a => a.unlocked).length;
  const completionRate = totalCount > 0 ? Math.floor((unlockedCount / totalCount) * 100) : 0;
  
  const tutorialCount = achievements.filter(a => a.type === AchievementType.TUTORIAL).length;
  const tutorialUnlocked = achievements.filter(a => a.type === AchievementType.TUTORIAL && a.unlocked).length;
  
  const editorCount = achievements.filter(a => a.type === AchievementType.EDITOR).length;
  const editorUnlocked = achievements.filter(a => a.type === AchievementType.EDITOR && a.unlocked).length;
  
  const creationCount = achievements.filter(a => a.type === AchievementType.CREATION).length;
  const creationUnlocked = achievements.filter(a => a.type === AchievementType.CREATION && a.unlocked).length;
  
  return (
    <div className="achievement-stats">
      <Row gutter={16}>
        <Col span={12}>
          <Card>
            <Statistic
              title="总体完成率"
              value={completionRate}
              suffix="%"
              prefix={<TrophyOutlined />}
              valueStyle={{ color: completionRate > 50 ? '#3f8600' : '#cf1322' }}
            />
            <Text type="secondary">{unlockedCount} / {totalCount} 已解锁</Text>
            <Progress percent={completionRate} showInfo={false} />
          </Card>
        </Col>
        <Col span={12}>
          <Card>
            <Row gutter={[0, 16]}>
              <Col span={24}>
                <Statistic
                  title="教程成就"
                  value={tutorialUnlocked}
                  suffix={`/ ${tutorialCount}`}
                  prefix={<BookOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={24}>
                <Statistic
                  title="编辑器成就"
                  value={editorUnlocked}
                  suffix={`/ ${editorCount}`}
                  prefix={<ToolOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={24}>
                <Statistic
                  title="创作成就"
                  value={creationUnlocked}
                  suffix={`/ ${creationCount}`}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

/**
 * 成就面板组件
 */
export const AchievementPanel: React.FC = () => {
  const { t } = useTranslation();
  const achievements = useAppSelector(selectAchievements);
  const [activeTab, setActiveTab] = useState<string>('all');
  const [filteredAchievements, setFilteredAchievements] = useState<Achievement[]>([]);
  
  // 根据选项卡筛选成就
  useEffect(() => {
    let filtered = [...achievements];
    
    switch (activeTab) {
      case 'unlocked':
        filtered = filtered.filter(a => a.unlocked);
        break;
      case 'locked':
        filtered = filtered.filter(a => !a.unlocked);
        break;
      case 'tutorial':
        filtered = filtered.filter(a => a.type === AchievementType.TUTORIAL);
        break;
      case 'editor':
        filtered = filtered.filter(a => a.type === AchievementType.EDITOR);
        break;
      case 'creation':
        filtered = filtered.filter(a => a.type === AchievementType.CREATION);
        break;
      case 'collaboration':
        filtered = filtered.filter(a => a.type === AchievementType.COLLABORATION);
        break;
    }
    
    setFilteredAchievements(filtered);
  }, [activeTab, achievements]);
  
  return (
    <div className="achievement-panel">
      <div className="achievement-panel-header">
        <Title level={3}>
          <TrophyOutlined /> {t('achievements.title')}
        </Title>
        <Text type="secondary">{t('achievements.subtitle')}</Text>
      </div>
      
      <AchievementStats achievements={achievements} />
      
      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        className="achievement-tabs"
      >
        <TabPane tab={t('achievements.tabs.all')} key="all" />
        <TabPane 
          tab={
            <Badge 
              count={achievements.filter(a => a.unlocked).length} 
              style={{ backgroundColor: '#52c41a' }}
            >
              {t('achievements.tabs.unlocked')}
            </Badge>
          } 
          key="unlocked" 
        />
        <TabPane 
          tab={
            <Badge 
              count={achievements.filter(a => !a.unlocked).length}
              style={{ backgroundColor: '#d9d9d9' }}
            >
              {t('achievements.tabs.locked')}
            </Badge>
          } 
          key="locked" 
        />
        <TabPane tab={t('achievements.tabs.tutorial')} key="tutorial" />
        <TabPane tab={t('achievements.tabs.editor')} key="editor" />
        <TabPane tab={t('achievements.tabs.creation')} key="creation" />
        <TabPane tab={t('achievements.tabs.collaboration')} key="collaboration" />
      </Tabs>
      
      {filteredAchievements.length > 0 ? (
        <List
          grid={{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 3, xl: 3, xxl: 4 }}
          dataSource={filteredAchievements}
          renderItem={item => (
            <List.Item>
              <AchievementCard achievement={item} />
            </List.Item>
          )}
        />
      ) : (
        <Empty 
          description={t('achievements.empty')} 
          image={Empty.PRESENTED_IMAGE_SIMPLE} 
        />
      )}
    </div>
  );
};

export default AchievementPanel;
