/**
 * 地形纹理工具样式
 */
.terrain-texture-tool {
  width: 100%;
  
  .texture-layers-card,
  .texture-properties-card,
  .painting-tools-card {
    height: 100%;
    
    .ant-card-body {
      padding: 16px;
      overflow-y: auto;
      max-height: 600px;
    }
  }
  
  .texture-layer-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .texture-layer-item {
      display: flex;
      align-items: center;
      padding: 8px;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        background-color: #f5f5f5;
      }
      
      &.selected {
        background-color: #e6f7ff;
        border-color: #1890ff;
      }
      
      .texture-layer-preview {
        width: 48px;
        height: 48px;
        border-radius: 4px;
        overflow: hidden;
        margin-right: 12px;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .texture-layer-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f0f0f0;
          color: #999;
          font-size: 24px;
        }
      }
      
      .texture-layer-info {
        flex: 1;
        
        .texture-layer-name {
          font-weight: bold;
          margin-bottom: 4px;
        }
        
        .texture-layer-tiling {
          font-size: 12px;
          color: #666;
        }
      }
      
      .texture-layer-actions {
        display: flex;
        gap: 4px;
      }
    }
    
    .add-layer-button {
      margin-top: 8px;
      width: 100%;
    }
  }
  
  .layer-properties {
    display: flex;
    flex-direction: column;
    
    .layer-preview {
      width: 100%;
      height: 150px;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 16px;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .layer-preview-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #f0f0f0;
        color: #999;
        
        .anticon {
          font-size: 32px;
          margin-bottom: 8px;
        }
      }
    }
    
    .layer-info {
      .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
      }
    }
  }
  
  .texture-layer-edit-form {
    .ant-upload-picture-card-wrapper {
      .ant-upload {
        width: 100%;
        height: 120px;
      }
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      margin-top: 16px;
    }
  }
}
