/**
 * 子片段事件
 * 用于在子片段播放过程中触发事件
 */
import { EventEmitter } from '../utils/EventEmitter';
import { SubClip } from './SubClip';
import { AnimationSubClip } from './AnimationSubClip';

/**
 * 子片段事件类型
 */
export enum SubClipEventType {
  /** 事件触发 */
  EVENT_TRIGGERED = 'eventTriggered',
  /** 事件添加 */
  EVENT_ADDED = 'eventAdded',
  /** 事件移除 */
  EVENT_REMOVED = 'eventRemoved',
  /** 事件更新 */
  EVENT_UPDATED = 'eventUpdated'
}

/**
 * 事件触发类型
 */
export enum EventTriggerType {
  /** 时间点 */
  TIME = 'time',
  /** 百分比 */
  PERCENT = 'percent',
  /** 帧 */
  FRAME = 'frame',
  /** 开始 */
  START = 'start',
  /** 结束 */
  END = 'end',
  /** 循环 */
  LOOP = 'loop',
  /** 条件 */
  CONDITION = 'condition'
}

/**
 * 子片段事件配置
 */
export interface SubClipEventConfig {
  /** 事件名称 */
  name?: string;
  /** 触发类型 */
  triggerType?: EventTriggerType;
  /** 触发值 */
  triggerValue?: number;
  /** 触发条件 */
  triggerCondition?: (time: number, progress: number, clip: SubClip | AnimationSubClip) => boolean;
  /** 回调函数 */
  callback?: (event: any) => void;
  /** 自定义数据 */
  userData?: any;
  /** 是否只触发一次 */
  once?: boolean;
  /** 是否启用 */
  enabled?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 子片段事件
 */
export class SubClipEvent {
  /** 事件名称 */
  private name: string;
  /** 触发类型 */
  private triggerType: EventTriggerType;
  /** 触发值 */
  private triggerValue: number;
  /** 触发条件 */
  private triggerCondition?: (time: number, progress: number, clip: SubClip | AnimationSubClip) => boolean;
  /** 回调函数 */
  private callback?: (event: any) => void;
  /** 自定义数据 */
  private userData: any;
  /** 是否只触发一次 */
  private once: boolean;
  /** 是否启用 */
  private enabled: boolean;
  /** 是否启用调试 */
  private debug: boolean;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 是否已触发 */
  private triggered: boolean = false;

  /**
   * 创建子片段事件
   * @param config 配置
   */
  constructor(config: SubClipEventConfig = {}) {
    this.name = config.name || 'event';
    this.triggerType = config.triggerType || EventTriggerType.TIME;
    this.triggerValue = config.triggerValue !== undefined ? config.triggerValue : 0;
    this.triggerCondition = config.triggerCondition;
    this.callback = config.callback;
    this.userData = config.userData || {};
    this.once = config.once !== undefined ? config.once : false;
    this.enabled = config.enabled !== undefined ? config.enabled : true;
    this.debug = config.debug !== undefined ? config.debug : false;
  }

  /**
   * 获取事件名称
   * @returns 事件名称
   */
  public getName(): string {
    return this.name;
  }

  /**
   * 设置事件名称
   * @param name 事件名称
   */
  public setName(name: string): void {
    this.name = name;
  }

  /**
   * 获取触发类型
   * @returns 触发类型
   */
  public getTriggerType(): EventTriggerType {
    return this.triggerType;
  }

  /**
   * 设置触发类型
   * @param type 触发类型
   */
  public setTriggerType(type: EventTriggerType): void {
    this.triggerType = type;
  }

  /**
   * 获取触发值
   * @returns 触发值
   */
  public getTriggerValue(): number {
    return this.triggerValue;
  }

  /**
   * 设置触发值
   * @param value 触发值
   */
  public setTriggerValue(value: number): void {
    this.triggerValue = value;
  }

  /**
   * 设置触发条件
   * @param condition 触发条件
   */
  public setTriggerCondition(condition: (time: number, progress: number, clip: SubClip | AnimationSubClip) => boolean): void {
    this.triggerCondition = condition;
    this.triggerType = EventTriggerType.CONDITION;
  }

  /**
   * 获取回调函数
   * @returns 回调函数
   */
  public getCallback(): ((event: any) => void) | undefined {
    return this.callback;
  }

  /**
   * 设置回调函数
   * @param callback 回调函数
   */
  public setCallback(callback: (event: any) => void): void {
    this.callback = callback;
  }

  /**
   * 获取自定义数据
   * @returns 自定义数据
   */
  public getUserData(): any {
    return { ...this.userData };
  }

  /**
   * 设置自定义数据
   * @param data 自定义数据
   */
  public setUserData(data: any): void {
    this.userData = { ...data };
  }

  /**
   * 是否只触发一次
   * @returns 是否只触发一次
   */
  public isOnce(): boolean {
    return this.once;
  }

  /**
   * 设置是否只触发一次
   * @param once 是否只触发一次
   */
  public setOnce(once: boolean): void {
    this.once = once;
  }

  /**
   * 是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 设置是否启用
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * 是否已触发
   * @returns 是否已触发
   */
  public isTriggered(): boolean {
    return this.triggered;
  }

  /**
   * 重置触发状态
   */
  public reset(): void {
    this.triggered = false;
  }

  /**
   * 检查是否应该触发事件
   * @param time 当前时间
   * @param progress 当前进度（0-1）
   * @param clip 子片段
   * @returns 是否应该触发
   */
  public shouldTrigger(time: number, progress: number, clip: SubClip | AnimationSubClip): boolean {
    if (!this.enabled) return false;
    if (this.once && this.triggered) return false;

    switch (this.triggerType) {
      case EventTriggerType.TIME:
        return time >= this.triggerValue;

      case EventTriggerType.PERCENT:
        return progress >= this.triggerValue;

      case EventTriggerType.FRAME:
        // 假设每秒60帧
        return Math.floor(time * 60) >= this.triggerValue;

      case EventTriggerType.START:
        return time === 0;

      case EventTriggerType.END:
        return progress >= 1.0;

      case EventTriggerType.LOOP:
        // 检查是否刚刚循环
        const duration = clip instanceof SubClip ? clip.getDuration() : clip.getEndTime() - clip.getStartTime();
        return Math.floor(time / duration) > Math.floor((time - 0.01) / duration);

      case EventTriggerType.CONDITION:
        return this.triggerCondition ? this.triggerCondition(time, progress, clip) : false;

      default:
        return false;
    }
  }

  /**
   * 触发事件
   * @param time 当前时间
   * @param progress 当前进度
   * @param clip 子片段
   */
  public trigger(time: number, progress: number, clip: SubClip | AnimationSubClip): void {
    if (!this.enabled) return;
    if (this.once && this.triggered) return;

    // 创建事件数据
    const eventData = {
      name: this.name,
      time,
      progress,
      clip,
      userData: this.userData
    };

    // 调用回调函数
    if (this.callback) {
      this.callback(eventData);
    }

    // 触发事件
    this.eventEmitter.emit(SubClipEventType.EVENT_TRIGGERED, eventData);

    // 标记为已触发
    this.triggered = true;

    if (this.debug) {
      console.log(`触发事件: ${this.name}, 时间: ${time}, 进度: ${progress}, 片段: ${clip.getName()}`);
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public addEventListener(event: SubClipEventType, listener: (data: any) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public removeEventListener(event: SubClipEventType, listener: (data: any) => void): void {
    this.eventEmitter.off(event, listener);
  }
}
