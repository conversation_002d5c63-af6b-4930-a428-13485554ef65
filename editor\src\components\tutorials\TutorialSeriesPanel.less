/**
 * 教程系列面板样式
 */
.tutorial-series-panel {
  padding: 0 10px;

  .series-header {
    margin-bottom: 20px;

    .back-button {
      margin-bottom: 10px;
      padding-left: 0;
    }

    .series-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin: 10px 0;
    }

    .series-progress {
      margin-top: 15px;
      display: flex;
      flex-direction: column;
      gap: 5px;
    }
  }

  .series-content {
    display: flex;
    gap: 20px;

    .series-steps {
      width: 250px;
      flex-shrink: 0;
      border-right: 1px solid #f0f0f0;
      padding-right: 20px;

      .step-description {
        display: flex;
        flex-direction: column;
        gap: 5px;
      }
    }

    .series-tutorials {
      flex: 1;
    }
  }

  .tutorial-item-card {
    position: relative;
    transition: all 0.3s;
    margin-bottom: 15px;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    &.tutorial-locked {
      opacity: 0.7;
      cursor: not-allowed;

      &:hover {
        transform: none;
        box-shadow: none;
      }
    }

    .tutorial-item-number {
      position: absolute;
      top: 10px;
      left: 10px;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: #1890ff;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
    }

    .tutorial-item-status {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;

      .completed-icon {
        color: #52c41a;
        font-size: 24px;
      }

      .locked-icon {
        color: #bfbfbf;
        font-size: 24px;
      }
    }

    .tutorial-item-title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .popular-icon {
        color: #ff4d4f;
        font-size: 16px;
        margin-left: 8px;
      }
    }

    .tutorial-item-description {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .tutorial-item-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-top: 5px;
    }

    .tutorial-progress {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-top: 5px;

      .progress-bar {
        flex: 1;
        height: 6px;
        background-color: #f0f0f0;
        border-radius: 3px;
        overflow: hidden;

        .progress-fill {
          height: 100%;
          background-color: #1890ff;
          border-radius: 3px;
        }
      }
    }

    .tutorial-prerequisites {
      margin-top: 10px;
      padding: 10px;
      background-color: #f9f9f9;
      border-radius: 4px;

      ul {
        margin: 5px 0 0 20px;
        padding: 0;
      }

      .completed-prerequisite {
        color: #52c41a;
        text-decoration: line-through;

        .prereq-check {
          margin-left: 5px;
        }
      }
    }

    .start-tutorial-button {
      margin-top: 10px;
      align-self: flex-end;
    }
  }

  .tutorial-series-skeleton {
    padding: 20px 0;
  }
}

.certificate-modal {
  .certificate {
    background-color: #fff;
    border: 10px solid #f0f0f0;
    padding: 30px;
    text-align: center;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: url('/assets/images/tutorials/certificate-bg.png');
      background-size: cover;
      opacity: 0.05;
      pointer-events: none;
    }

    .certificate-header {
      margin-bottom: 20px;
      border-bottom: 2px solid #f0f0f0;
      padding-bottom: 20px;

      .certificate-icon {
        font-size: 48px;
        color: gold;
        margin-bottom: 10px;
      }
    }

    .certificate-content {
      margin-bottom: 30px;

      .certificate-details {
        margin-top: 20px;
        display: flex;
        flex-direction: column;
        gap: 10px;
        text-align: left;
        border: 1px solid #f0f0f0;
        padding: 15px;
        border-radius: 4px;

        .certificate-detail {
          display: flex;
          gap: 10px;
        }
      }
    }

    .certificate-footer {
      display: flex;
      justify-content: flex-end;

      .certificate-seal {
        width: 80px;
        height: 80px;
      }
    }
  }
}
