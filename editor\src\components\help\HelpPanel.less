.help-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  .ant-card-body {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 0;
  }

  .help-search {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
  }

  .help-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .ant-tabs-content {
      flex: 1;
      overflow: auto;
      padding: 16px;
    }
  }

  .help-content {
    padding: 8px;

    .ant-typography {
      margin-bottom: 16px;
    }

    .markdown-content {
      margin: 16px 0;
      
      h1, h2, h3, h4, h5, h6 {
        margin-top: 24px;
        margin-bottom: 16px;
      }

      p {
        margin-bottom: 16px;
        line-height: 1.6;
      }

      ul, ol {
        margin-bottom: 16px;
        padding-left: 24px;
      }

      code {
        background-color: #f5f5f5;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', <PERSON><PERSON>, <PERSON>urier, monospace;
      }

      pre {
        background-color: #f5f5f5;
        padding: 16px;
        border-radius: 3px;
        overflow: auto;
        margin-bottom: 16px;

        code {
          background-color: transparent;
          padding: 0;
        }
      }

      blockquote {
        border-left: 4px solid #ddd;
        padding-left: 16px;
        margin-left: 0;
        margin-bottom: 16px;
        color: #666;
      }

      img {
        max-width: 100%;
        margin: 16px 0;
      }

      table {
        border-collapse: collapse;
        width: 100%;
        margin-bottom: 16px;

        th, td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
        }

        th {
          background-color: #f5f5f5;
        }

        tr:nth-child(even) {
          background-color: #f9f9f9;
        }
      }
    }

    .video-section {
      margin: 16px 0;
    }

    .related-topics {
      margin: 16px 0;
    }
  }

  .help-list-item {
    cursor: pointer;
    padding: 12px;
    border-radius: 4px;
    transition: background-color 0.3s;

    &:hover {
      background-color: #f5f5f5;
    }
  }

  .category-contents {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
}

// 暗色主题适配
.dark-theme {
  .help-panel {
    background-color: #1f1f1f;
    color: #fff;

    .help-search {
      border-bottom-color: #303030;
    }

    .help-content {
      .markdown-content {
        code {
          background-color: #2a2a2a;
        }

        pre {
          background-color: #2a2a2a;
        }

        blockquote {
          border-left-color: #444;
          color: #aaa;
        }

        table {
          th, td {
            border-color: #444;
          }

          th {
            background-color: #2a2a2a;
          }

          tr:nth-child(even) {
            background-color: #252525;
          }
        }
      }
    }

    .help-list-item {
      &:hover {
        background-color: #2a2a2a;
      }
    }
  }
}
