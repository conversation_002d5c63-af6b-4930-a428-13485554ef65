# 动画系统示例

本文档提供了一系列动画系统的使用示例，帮助您快速上手DL（Digital Learning）引擎的动画系统。

## 基础动画播放

### 创建和播放简单动画

```typescript
import { AnimationClip, Animator, LoopMode } from '../../animation';
import { Entity } from '../../core/Entity';

// 创建实体
const entity = new Entity();

// 创建动画片段
const walkClip = new AnimationClip('walk', 1.0);

// 添加位置轨道
walkClip.addTrack('model.position', 'vector3', [
  { time: 0, value: [0, 0, 0] },
  { time: 0.5, value: [0, 0.1, 0.5] },
  { time: 1.0, value: [0, 0, 1.0] }
]);

// 添加旋转轨道
walkClip.addTrack('model.quaternion', 'quaternion', [
  { time: 0, value: [0, 0, 0, 1] },
  { time: 1.0, value: [0, 0.1, 0, 0.99] }
]);

// 设置循环模式
walkClip.loopMode = LoopMode.REPEAT;

// 创建动画控制器
const animator = new Animator({
  entity: entity,
  clips: [walkClip]
});

// 添加动画控制器到实体
entity.addComponent(animator);

// 播放动画
animator.play('walk');

// 在游戏循环中更新动画
function update(deltaTime: number) {
  animator.update(deltaTime);
}
```

### 动画混合

```typescript
// 创建多个动画片段
const idleClip = new AnimationClip('idle', 2.0);
const walkClip = new AnimationClip('walk', 1.0);
const runClip = new AnimationClip('run', 0.6);

// 添加到动画控制器
const animator = new Animator({
  entity: entity,
  clips: [idleClip, walkClip, runClip]
});

// 播放idle动画
animator.play('idle');

// 当角色开始移动时，混合到walk动画
function startWalking() {
  animator.play('walk', 0.3); // 0.3秒内平滑过渡
}

// 当角色开始跑步时，混合到run动画
function startRunning() {
  animator.play('run', 0.2); // 0.2秒内平滑过渡
}

// 当角色停止移动时，混合回idle动画
function stopMoving() {
  animator.play('idle', 0.5); // 0.5秒内平滑过渡
}
```

## 动画状态机

### 创建角色动画状态机

```typescript
import { AnimationStateMachine, AnimationStateMachineEventType } from '../../animation';

// 创建状态机
const stateMachine = new AnimationStateMachine(animator);

// 添加状态
stateMachine.addState({
  name: 'Idle',
  type: 'SingleAnimationState',
  clipName: 'idle',
  loop: true,
  clamp: false
});

stateMachine.addState({
  name: 'Walk',
  type: 'SingleAnimationState',
  clipName: 'walk',
  loop: true,
  clamp: false
});

stateMachine.addState({
  name: 'Run',
  type: 'SingleAnimationState',
  clipName: 'run',
  loop: true,
  clamp: false
});

stateMachine.addState({
  name: 'Jump',
  type: 'SingleAnimationState',
  clipName: 'jump',
  loop: false,
  clamp: true
});

// 添加转换规则
// 从Idle到Walk
stateMachine.addTransition({
  from: 'Idle',
  to: 'Walk',
  condition: () => stateMachine.getParameter('speed') > 0.1,
  duration: 0.3,
  canInterrupt: true
});

// 从Walk到Idle
stateMachine.addTransition({
  from: 'Walk',
  to: 'Idle',
  condition: () => stateMachine.getParameter('speed') <= 0.1,
  duration: 0.3,
  canInterrupt: true
});

// 从Walk到Run
stateMachine.addTransition({
  from: 'Walk',
  to: 'Run',
  condition: () => stateMachine.getParameter('speed') > 2.0,
  duration: 0.2,
  canInterrupt: true
});

// 从Run到Walk
stateMachine.addTransition({
  from: 'Run',
  to: 'Walk',
  condition: () => stateMachine.getParameter('speed') <= 2.0 && stateMachine.getParameter('speed') > 0.1,
  duration: 0.2,
  canInterrupt: true
});

// 从任何状态到Jump
stateMachine.addTransition({
  from: 'Idle',
  to: 'Jump',
  condition: () => stateMachine.getParameter('jump') === true,
  duration: 0.1,
  canInterrupt: false
});

stateMachine.addTransition({
  from: 'Walk',
  to: 'Jump',
  condition: () => stateMachine.getParameter('jump') === true,
  duration: 0.1,
  canInterrupt: false
});

stateMachine.addTransition({
  from: 'Run',
  to: 'Jump',
  condition: () => stateMachine.getParameter('jump') === true,
  duration: 0.1,
  canInterrupt: false
});

// 从Jump回到Idle
stateMachine.addTransition({
  from: 'Jump',
  to: 'Idle',
  condition: () => stateMachine.getParameter('grounded') === true,
  duration: 0.3,
  canInterrupt: false
});

// 设置初始状态
stateMachine.setCurrentState('Idle');

// 监听状态变化
stateMachine.addEventListener(AnimationStateMachineEventType.STATE_ENTER, (state) => {
  console.log(`进入状态: ${state.name}`);
});

stateMachine.addEventListener(AnimationStateMachineEventType.STATE_EXIT, (state) => {
  console.log(`退出状态: ${state.name}`);
});

// 在游戏循环中更新状态机
function update(deltaTime: number) {
  // 更新参数
  const characterSpeed = character.getSpeed();
  stateMachine.setParameter('speed', characterSpeed);
  
  // 检测跳跃输入
  if (isJumpKeyPressed) {
    stateMachine.setParameter('jump', true);
  } else {
    stateMachine.setParameter('jump', false);
  }
  
  // 检测是否着地
  stateMachine.setParameter('grounded', character.isGrounded());
  
  // 更新状态机
  stateMachine.update(deltaTime);
}
```

## 混合空间

### 使用1D混合空间控制移动动画

```typescript
import { BlendSpace1D } from '../../animation';

// 创建1D混合空间
const locomotionBlendSpace = new BlendSpace1D({
  minValue: 0,
  maxValue: 3
});

// 添加节点
locomotionBlendSpace.addNode(idleClip, 0);
locomotionBlendSpace.addNode(walkClip, 1);
locomotionBlendSpace.addNode(runClip, 3);

// 在游戏循环中更新混合空间
function update(deltaTime: number) {
  // 获取角色速度
  const speed = character.getSpeed();
  
  // 设置混合空间位置
  locomotionBlendSpace.setPosition(speed);
  
  // 更新混合权重
  locomotionBlendSpace.update();
  
  // 获取活跃节点
  const activeNodes = locomotionBlendSpace.getActiveNodes();
  
  // 应用混合结果
  for (const node of activeNodes) {
    // 使用node.clip和node.weight进行动画混合
    // 这里可以使用Animator的API进行混合
  }
}
```

### 使用2D混合空间控制方向性移动动画

```typescript
import { BlendSpace2D } from '../../animation';
import * as THREE from 'three';

// 创建2D混合空间
const directionalBlendSpace = new BlendSpace2D({
  minX: -1,
  maxX: 1,
  minY: -1,
  maxY: 1
});

// 添加节点
directionalBlendSpace.addNode(idleClip, new THREE.Vector2(0, 0));
directionalBlendSpace.addNode(walkForwardClip, new THREE.Vector2(0, 1));
directionalBlendSpace.addNode(walkBackwardClip, new THREE.Vector2(0, -1));
directionalBlendSpace.addNode(walkLeftClip, new THREE.Vector2(-1, 0));
directionalBlendSpace.addNode(walkRightClip, new THREE.Vector2(1, 0));
directionalBlendSpace.addNode(walkForwardLeftClip, new THREE.Vector2(-0.7, 0.7));
directionalBlendSpace.addNode(walkForwardRightClip, new THREE.Vector2(0.7, 0.7));
directionalBlendSpace.addNode(walkBackwardLeftClip, new THREE.Vector2(-0.7, -0.7));
directionalBlendSpace.addNode(walkBackwardRightClip, new THREE.Vector2(0.7, -0.7));

// 在游戏循环中更新混合空间
function update(deltaTime: number) {
  // 获取角色移动方向
  const moveDirection = character.getMoveDirection();
  
  // 设置混合空间位置
  directionalBlendSpace.setPosition(moveDirection.x, moveDirection.z);
  
  // 更新混合权重
  directionalBlendSpace.update();
  
  // 获取活跃节点
  const activeNodes = directionalBlendSpace.getActiveNodes();
  
  // 应用混合结果
  for (const node of activeNodes) {
    // 使用node.clip和node.weight进行动画混合
  }
}
```

## Avatar动画系统

### 创建和使用Avatar动画系统

```typescript
import { AvatarAnimationSystem, AvatarAnimationComponent, AvatarRigComponent } from '../../animation';
import { World } from '../../core/World';

// 创建世界
const world = new World();

// 创建Avatar动画系统
const avatarAnimationSystem = new AvatarAnimationSystem(world, {
  debug: true,
  autoRetarget: true,
  useStateMachine: true,
  useBlendSpace: true
});

// 添加系统到世界
world.addSystem(avatarAnimationSystem);

// 创建角色实体
const characterEntity = world.createEntity();

// 添加Avatar组件
const rigComponent = new AvatarRigComponent();
characterEntity.addComponent(rigComponent);

const animationComponent = new AvatarAnimationComponent();
characterEntity.addComponent(animationComponent);

// 注册Avatar实体
avatarAnimationSystem.registerAvatar(characterEntity);

// 设置更新频率
avatarAnimationSystem.setUpdateFrequency(characterEntity, 1/30); // 每秒30次更新

// 在游戏循环中更新系统
function update(deltaTime: number) {
  // 更新世界
  world.update(deltaTime);
  
  // 世界更新会自动调用avatarAnimationSystem.update
}
```

## 动画重定向

### 将Mixamo动画重定向到VRM模型

```typescript
import { AnimationRetargeting } from '../../animation';
import * as THREE from 'three';

// 加载Mixamo动画
const mixamoLoader = new THREE.FBXLoader();
mixamoLoader.load('animations/mixamo/walk.fbx', (fbx) => {
  // 获取动画片段
  const mixamoClip = fbx.animations[0];
  
  // 获取VRM模型的骨骼
  const vrmSkeleton = vrmModel.skeleton;
  
  // 创建骨骼映射
  const boneMapping = [
    { source: 'mixamorigHips', target: 'hips' },
    { source: 'mixamorigSpine', target: 'spine' },
    { source: 'mixamorigSpine1', target: 'chest' },
    { source: 'mixamorigSpine2', target: 'upperChest' },
    { source: 'mixamorigNeck', target: 'neck' },
    { source: 'mixamorigHead', target: 'head' },
    { source: 'mixamorigLeftShoulder', target: 'leftShoulder' },
    { source: 'mixamorigLeftArm', target: 'leftUpperArm' },
    { source: 'mixamorigLeftForeArm', target: 'leftLowerArm' },
    { source: 'mixamorigLeftHand', target: 'leftHand' },
    { source: 'mixamorigRightShoulder', target: 'rightShoulder' },
    { source: 'mixamorigRightArm', target: 'rightUpperArm' },
    { source: 'mixamorigRightForeArm', target: 'rightLowerArm' },
    { source: 'mixamorigRightHand', target: 'rightHand' },
    { source: 'mixamorigLeftUpLeg', target: 'leftUpperLeg' },
    { source: 'mixamorigLeftLeg', target: 'leftLowerLeg' },
    { source: 'mixamorigLeftFoot', target: 'leftFoot' },
    { source: 'mixamorigLeftToeBase', target: 'leftToes' },
    { source: 'mixamorigRightUpLeg', target: 'rightUpperLeg' },
    { source: 'mixamorigRightLeg', target: 'rightLowerLeg' },
    { source: 'mixamorigRightFoot', target: 'rightFoot' },
    { source: 'mixamorigRightToeBase', target: 'rightToes' }
  ];
  
  // 重定向动画
  const retargetedClip = AnimationRetargeting.retargetClip(
    mixamoClip,
    fbx.skeleton.bones,
    vrmSkeleton.bones,
    {
      boneMapping,
      preservePositionTracks: true,
      preserveScaleTracks: false,
      normalizeRotations: true,
      adjustRootHeight: true,
      adjustBoneLength: true
    }
  );
  
  // 使用重定向后的动画
  const animator = new Animator({
    entity: vrmEntity,
    clips: [retargetedClip]
  });
  
  // 播放动画
  animator.play(retargetedClip.name);
});
```

## 性能优化

### 使用LOD（细节层次）控制动画更新频率

```typescript
import { AvatarAnimationSystem } from '../../animation';
import { Camera } from '../../core/Camera';

// 创建Avatar动画系统
const avatarAnimationSystem = new AvatarAnimationSystem(world);

// 注册多个角色
const characters = [];
for (let i = 0; i < 100; i++) {
  const character = createCharacter();
  characters.push(character);
  avatarAnimationSystem.registerAvatar(character);
}

// 在游戏循环中更新LOD
function updateLOD(camera: Camera) {
  const cameraPosition = camera.getPosition();
  
  for (const character of characters) {
    const characterPosition = character.getPosition();
    const distance = characterPosition.distanceTo(cameraPosition);
    
    // 根据距离设置更新频率
    if (distance < 10) {
      // 近距离：每帧更新
      avatarAnimationSystem.setUpdateFrequency(character, 1/60);
    } else if (distance < 30) {
      // 中距离：每2帧更新一次
      avatarAnimationSystem.setUpdateFrequency(character, 2/60);
    } else if (distance < 50) {
      // 远距离：每4帧更新一次
      avatarAnimationSystem.setUpdateFrequency(character, 4/60);
    } else {
      // 非常远：每8帧更新一次
      avatarAnimationSystem.setUpdateFrequency(character, 8/60);
    }
  }
}
```
