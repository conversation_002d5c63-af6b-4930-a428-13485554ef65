/**
 * 图层面板样式
 */
.layers-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 8px;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    h3 {
      margin: 0;
      font-size: 16px;
    }
  }

  .search-input {
    margin-bottom: 8px;
  }

  .layers-list {
    flex: 1;
    overflow-y: auto;

    .layer-item {
      display: flex;
      align-items: center;
      padding: 8px;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }

      &.selected {
        background-color: rgba(24, 144, 255, 0.1);
      }

      &.layer-group {
        font-weight: 500;

        .expand-button {
          padding: 0;
          margin-right: 4px;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .layer-color {
        width: 16px;
        height: 16px;
        border-radius: 3px;
        margin-right: 8px;
        border: 1px solid rgba(0, 0, 0, 0.1);
      }

      .layer-icon {
        width: 16px;
        height: 16px;
        margin-right: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(0, 0, 0, 0.65);
      }

      .layer-info {
        flex: 1;
        overflow: hidden;

        .layer-name {
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .layer-meta {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.45);
          margin-top: 2px;

          .layer-tag {
            margin-right: 4px;
            font-size: 10px;
            padding: 0 4px;
            height: 18px;
            line-height: 18px;
          }

          .entity-count {
            margin-left: auto;
          }
        }
      }

      .layer-actions {
        display: flex;
        align-items: center;

        .ant-btn {
          padding: 0 4px;

          &:hover {
            background-color: rgba(0, 0, 0, 0.05);
          }
        }
      }
    }
  }
}

// 暗色主题适配
.dark-theme {
  .layers-panel {
    .layer-item {
      &:hover {
        background-color: rgba(255, 255, 255, 0.05);
      }

      &.selected {
        background-color: rgba(24, 144, 255, 0.2);
      }

      .layer-color {
        border-color: rgba(255, 255, 255, 0.1);
      }

      .layer-icon {
        color: rgba(255, 255, 255, 0.65);
      }

      .layer-info {
        .layer-meta {
          color: rgba(255, 255, 255, 0.45);
        }
      }

      .layer-actions {
        .ant-btn {
          &:hover {
            background-color: rgba(255, 255, 255, 0.05);
          }
        }
      }
    }
  }
}
