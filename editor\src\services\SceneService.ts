/**
 * 场景服务
 * 负责场景的加载、保存和管理
 */
import axios from 'axios';
import EngineService, { EngineEventType } from './EngineService';
import { Scene } from '../../engine/src/scene/Scene';
import { Entity } from '../../engine/src/core/Entity';
import { EventEmitter } from '../../engine/src/utils/EventEmitter';

// 场景事件类型
export enum SceneEventType {
  LOADING_START = 'loadingStart',
  LOADING_PROGRESS = 'loadingProgress',
  LOADING_COMPLETE = 'loadingComplete',
  LOADING_ERROR = 'loadingError',
  SAVING_START = 'savingStart',
  SAVING_PROGRESS = 'savingProgress',
  SAVING_COMPLETE = 'savingComplete',
  SAVING_ERROR = 'savingError',
  SCENE_CHANGED = 'sceneChanged',
  SCENE_GRAPH_CHANGED = 'sceneGraphChanged',
}

// 场景图节点
export interface SceneGraphNode {
  id: string;
  name: string;
  type: string;
  children: SceneGraphNode[];
  components: string[];
  visible: boolean;
  locked: boolean;
}

// 场景服务类
class SceneService extends EventEmitter {
  private static instance: SceneService;
  
  private currentSceneId: string | null = null;
  private currentProjectId: string | null = null;
  private sceneGraph: SceneGraphNode | null = null;
  private isDirty: boolean = false;
  private autoSaveInterval: number | null = null;
  
  private constructor() {
    super();
    
    // 监听引擎事件
    EngineService.on(EngineEventType.SCENE_LOADED, this.handleSceneLoaded.bind(this));
    EngineService.on(EngineEventType.SCENE_UNLOADED, this.handleSceneUnloaded.bind(this));
    EngineService.on(EngineEventType.OBJECT_ADDED, this.handleObjectChanged.bind(this));
    EngineService.on(EngineEventType.OBJECT_REMOVED, this.handleObjectChanged.bind(this));
    EngineService.on(EngineEventType.OBJECT_CHANGED, this.handleObjectChanged.bind(this));
    EngineService.on(EngineEventType.COMPONENT_ADDED, this.handleObjectChanged.bind(this));
    EngineService.on(EngineEventType.COMPONENT_REMOVED, this.handleObjectChanged.bind(this));
    EngineService.on(EngineEventType.COMPONENT_CHANGED, this.handleObjectChanged.bind(this));
  }
  
  /**
   * 获取场景服务实例
   */
  public static getInstance(): SceneService {
    if (!SceneService.instance) {
      SceneService.instance = new SceneService();
    }
    return SceneService.instance;
  }
  
  /**
   * 加载场景
   * @param projectId 项目ID
   * @param sceneId 场景ID
   */
  public async loadScene(projectId: string, sceneId: string): Promise<Scene> {
    try {
      // 发出加载开始事件
      this.emit(SceneEventType.LOADING_START, { projectId, sceneId });
      
      // 从API获取场景数据
      const response = await axios.get(`/api/projects/${projectId}/scenes/${sceneId}/data`);
      const sceneData = response.data;
      
      // 加载场景到引擎
      const scene = await EngineService.loadScene(sceneData);
      
      // 设置当前场景和项目ID
      this.currentSceneId = sceneId;
      this.currentProjectId = projectId;
      this.isDirty = false;
      
      // 更新场景图
      this.updateSceneGraph();
      
      // 发出加载完成事件
      this.emit(SceneEventType.LOADING_COMPLETE, { scene, projectId, sceneId });
      
      // 设置自动保存
      this.setupAutoSave();
      
      return scene;
    } catch (error) {
      console.error('加载场景失败:', error);
      this.emit(SceneEventType.LOADING_ERROR, { error, projectId, sceneId });
      throw error;
    }
  }
  
  /**
   * 保存场景
   */
  public async saveScene(): Promise<void> {
    if (!this.currentProjectId || !this.currentSceneId) {
      throw new Error('没有活动场景');
    }
    
    try {
      // 发出保存开始事件
      this.emit(SceneEventType.SAVING_START, { projectId: this.currentProjectId, sceneId: this.currentSceneId });
      
      // 序列化场景
      const sceneData = await EngineService.saveScene();
      
      // 保存到API
      await axios.put(`/api/projects/${this.currentProjectId}/scenes/${this.currentSceneId}/data`, sceneData);
      
      // 更新状态
      this.isDirty = false;
      
      // 发出保存完成事件
      this.emit(SceneEventType.SAVING_COMPLETE, { projectId: this.currentProjectId, sceneId: this.currentSceneId });
    } catch (error) {
      console.error('保存场景失败:', error);
      this.emit(SceneEventType.SAVING_ERROR, { error, projectId: this.currentProjectId, sceneId: this.currentSceneId });
      throw error;
    }
  }
  
  /**
   * 创建新场景
   * @param projectId 项目ID
   * @param name 场景名称
   * @param template 场景模板
   */
  public async createScene(projectId: string, name: string, template: string = 'empty'): Promise<any> {
    try {
      // 创建场景
      const response = await axios.post(`/api/projects/${projectId}/scenes`, {
        name,
        template,
      });
      
      const sceneData = response.data;
      
      // 加载新场景
      await this.loadScene(projectId, sceneData.id);
      
      return sceneData;
    } catch (error) {
      console.error('创建场景失败:', error);
      throw error;
    }
  }
  
  /**
   * 更新场景图
   */
  public updateSceneGraph(): void {
    const scene = EngineService.getActiveScene();
    if (!scene) {
      this.sceneGraph = null;
      return;
    }
    
    // 构建场景图
    const rootEntity = scene.getRootEntity();
    this.sceneGraph = this.buildSceneGraphNode(rootEntity);
    
    // 发出场景图变化事件
    this.emit(SceneEventType.SCENE_GRAPH_CHANGED, this.sceneGraph);
  }
  
  /**
   * 构建场景图节点
   * @param entity 实体
   */
  private buildSceneGraphNode(entity: Entity): SceneGraphNode {
    const components = entity.getAllComponents().map(component => component.getType());
    
    const node: SceneGraphNode = {
      id: entity.getId(),
      name: entity.getName(),
      type: 'Entity',
      children: [],
      components,
      visible: entity.isVisible(),
      locked: entity.isLocked(),
    };
    
    // 递归处理子实体
    const children = entity.getChildren();
    for (const child of children) {
      node.children.push(this.buildSceneGraphNode(child));
    }
    
    return node;
  }
  
  /**
   * 获取场景图
   */
  public getSceneGraph(): SceneGraphNode | null {
    return this.sceneGraph;
  }
  
  /**
   * 获取当前场景ID
   */
  public getCurrentSceneId(): string | null {
    return this.currentSceneId;
  }
  
  /**
   * 获取当前项目ID
   */
  public getCurrentProjectId(): string | null {
    return this.currentProjectId;
  }
  
  /**
   * 检查场景是否有未保存的更改
   */
  public isDirtyScene(): boolean {
    return this.isDirty;
  }
  
  /**
   * 设置自动保存
   * @param interval 自动保存间隔（毫秒），如果为0则禁用自动保存
   */
  public setupAutoSave(interval: number = 300000): void {
    // 清除现有的自动保存
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
    }
    
    // 如果间隔大于0，设置新的自动保存
    if (interval > 0) {
      this.autoSaveInterval = window.setInterval(async () => {
        if (this.isDirty) {
          try {
            await this.saveScene();
            console.log('自动保存成功');
          } catch (error) {
            console.error('自动保存失败:', error);
          }
        }
      }, interval);
    }
  }
  
  /**
   * 处理场景加载事件
   */
  private handleSceneLoaded(scene: Scene): void {
    this.updateSceneGraph();
    this.isDirty = false;
    this.emit(SceneEventType.SCENE_CHANGED, scene);
  }
  
  /**
   * 处理场景卸载事件
   */
  private handleSceneUnloaded(scene: Scene): void {
    this.sceneGraph = null;
    this.isDirty = false;
    this.emit(SceneEventType.SCENE_CHANGED, null);
  }
  
  /**
   * 处理对象变化事件
   */
  private handleObjectChanged(): void {
    this.updateSceneGraph();
    this.isDirty = true;
    this.emit(SceneEventType.SCENE_CHANGED, EngineService.getActiveScene());
  }
  
  /**
   * 销毁场景服务
   */
  public dispose(): void {
    // 清除自动保存
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
    }
    
    // 移除事件监听
    EngineService.off(EngineEventType.SCENE_LOADED, this.handleSceneLoaded);
    EngineService.off(EngineEventType.SCENE_UNLOADED, this.handleSceneUnloaded);
    EngineService.off(EngineEventType.OBJECT_ADDED, this.handleObjectChanged);
    EngineService.off(EngineEventType.OBJECT_REMOVED, this.handleObjectChanged);
    EngineService.off(EngineEventType.OBJECT_CHANGED, this.handleObjectChanged);
    EngineService.off(EngineEventType.COMPONENT_ADDED, this.handleObjectChanged);
    EngineService.off(EngineEventType.COMPONENT_REMOVED, this.handleObjectChanged);
    EngineService.off(EngineEventType.COMPONENT_CHANGED, this.handleObjectChanged);
    
    this.removeAllListeners();
  }
}

export default SceneService.getInstance();
