/**
 * 可停靠面板布局组件
 * 基于rc-dock库实现灵活的面板布局系统
 */
import React, { useRef, useEffect } from 'react';
import { DockLayout as RcDockLayout, LayoutData, TabData, PanelData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import { setLayout, saveLayoutToStorage, loadLayoutFromStorage } from '../../store/ui/layoutSlice';
import 'rc-dock/dist/rc-dock.css';
import './DockLayout.less';

interface DockLayoutProps {
  defaultLayout: LayoutData;
  onLayoutChange?: (layout: LayoutData) => void;
}

const DockLayout: React.FC<DockLayoutProps> = ({ defaultLayout, onLayoutChange }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { layout, theme } = useAppSelector((state) => state.ui);
  const dockLayoutRef = useRef<RcDockLayout>(null);

  // 初始化布局
  useEffect(() => {
    // 尝试从本地存储加载布局
    dispatch(loadLayoutFromStorage());
  }, [dispatch]);

  // 处理布局变化
  const handleLayoutChange = (newLayout: LayoutData) => {
    dispatch(setLayout(newLayout));
    
    // 保存布局到本地存储
    dispatch(saveLayoutToStorage(newLayout));
    
    // 调用外部传入的布局变化回调
    if (onLayoutChange) {
      onLayoutChange(newLayout);
    }
  };

  // 获取当前使用的布局
  const currentLayout = layout || defaultLayout;

  return (
    <div className={`dock-layout-container ${theme === 'dark' ? 'dark-theme' : 'light-theme'}`}>
      <RcDockLayout
        ref={dockLayoutRef}
        defaultLayout={currentLayout}
        style={{ width: '100%', height: '100%' }}
        onLayoutChange={handleLayoutChange}
      />
    </div>
  );
};

export default DockLayout;
