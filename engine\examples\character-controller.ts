/**
 * 角色控制器示例
 * 展示如何使用角色控制器实现角色移动
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { Engine } from '../src/core/Engine';
import { World } from '../src/core/World';
import { Entity } from '../src/core/Entity';
import { PhysicsSystem } from '../src/physics/PhysicsSystem';
import { PhysicsBody } from '../src/physics/PhysicsBody';
import { PhysicsCollider } from '../src/physics/PhysicsCollider';
import { CharacterControllerComponent } from '../src/physics/components/CharacterControllerComponent';
import { InputSystem } from '../src/input/InputSystem';
import { Renderer } from '../src/rendering/Renderer';
import { Camera } from '../src/rendering/Camera';
import { Scene } from '../src/scene/Scene';
import { Transform } from '../src/scene/Transform';

// 创建引擎
const engine = new Engine();

// 创建世界
const world = new World();
engine.setWorld(world);

// 创建物理系统
const physicsSystem = new PhysicsSystem({
  gravity: { x: 0, y: -9.81, z: 0 },
  debug: true
});
world.addSystem(physicsSystem);

// 创建输入系统
const inputSystem = new InputSystem();
world.addSystem(inputSystem);

// 创建渲染系统
const renderer = new Renderer({
  width: window.innerWidth,
  height: window.innerHeight,
  antialias: true
});
document.body.appendChild(renderer.getDomElement());

// 创建场景
const scene = new Scene();
world.addEntity(scene);

// 创建相机
const camera = new Camera({
  type: 'perspective',
  fov: 75,
  aspect: window.innerWidth / window.innerHeight,
  near: 0.1,
  far: 1000
});
camera.getTransform().setPosition(0, 5, 10);
camera.getTransform().lookAt(new THREE.Vector3(0, 0, 0));
world.addEntity(camera);

// 创建地面
const ground = new Entity('ground');
ground.addComponent(new Transform());
ground.addComponent(new PhysicsBody({
  type: 'static',
  mass: 0
}));
ground.addComponent(new PhysicsCollider({
  type: 'box',
  params: {
    halfExtents: { x: 50, y: 0.5, z: 50 }
  }
}));
world.addEntity(ground);

// 创建一些障碍物
for (let i = 0; i < 10; i++) {
  const obstacle = new Entity(`obstacle_${i}`);
  const transform = new Transform();
  transform.setPosition(
    Math.random() * 20 - 10,
    1,
    Math.random() * 20 - 10
  );
  obstacle.addComponent(transform);
  obstacle.addComponent(new PhysicsBody({
    type: 'static',
    mass: 0
  }));
  obstacle.addComponent(new PhysicsCollider({
    type: 'box',
    params: {
      halfExtents: { x: 1, y: 1, z: 1 }
    }
  }));
  world.addEntity(obstacle);
}

// 创建斜坡
const ramp = new Entity('ramp');
const rampTransform = new Transform();
rampTransform.setPosition(0, 0, -5);
rampTransform.setRotation(Math.PI / 10, 0, 0);
ramp.addComponent(rampTransform);
ramp.addComponent(new PhysicsBody({
  type: 'static',
  mass: 0
}));
ramp.addComponent(new PhysicsCollider({
  type: 'box',
  params: {
    halfExtents: { x: 5, y: 0.5, z: 5 }
  }
}));
world.addEntity(ramp);

// 创建角色
const character = new Entity('character');
const characterTransform = new Transform();
characterTransform.setPosition(0, 3, 0);
character.addComponent(characterTransform);
character.addComponent(new PhysicsBody({
  type: 'dynamic',
  mass: 1,
  fixedRotation: true
}));
character.addComponent(new PhysicsCollider({
  type: 'capsule',
  params: {
    radius: 0.5,
    height: 1.0
  }
}));
character.addComponent(new CharacterControllerComponent({
  offset: 0.01,
  maxSlopeClimbAngle: Math.PI / 4, // 45度
  minSlopeSlideAngle: Math.PI / 6, // 30度
  autoStep: {
    maxHeight: 0.5,
    minWidth: 0.1,
    stepOverDynamic: true
  },
  enableSnapToGround: 0.1
}));
world.addEntity(character);

// 移动方向和速度
const moveDirection = new THREE.Vector3();
const moveSpeed = 5;
const jumpForce = 10;

// 更新函数
engine.on('update', (deltaTime: number) => {
  // 处理输入
  moveDirection.set(0, 0, 0);
  
  if (inputSystem.isKeyPressed('KeyW')) {
    moveDirection.z -= 1;
  }
  if (inputSystem.isKeyPressed('KeyS')) {
    moveDirection.z += 1;
  }
  if (inputSystem.isKeyPressed('KeyA')) {
    moveDirection.x -= 1;
  }
  if (inputSystem.isKeyPressed('KeyD')) {
    moveDirection.x += 1;
  }
  
  // 归一化移动方向
  if (moveDirection.lengthSq() > 0) {
    moveDirection.normalize();
  }
  
  // 计算期望的移动向量
  const desiredMovement = new THREE.Vector3(
    moveDirection.x * moveSpeed * deltaTime,
    0,
    moveDirection.z * moveSpeed * deltaTime
  );
  
  // 跳跃
  if (inputSystem.isKeyPressed('Space')) {
    const characterController = physicsSystem.getCharacterController(character);
    if (characterController && characterController.isOnGround()) {
      desiredMovement.y = jumpForce * deltaTime;
    }
  }
  
  // 计算角色控制器移动
  physicsSystem.computeCharacterControllerMovement(character, desiredMovement);
  
  // 获取计算出的移动向量
  const computedMovement = physicsSystem.getCharacterControllerComputedMovement(character);
  
  // 应用移动
  const physicsBody = character.getComponent<PhysicsBody>(PhysicsBody.type);
  if (physicsBody) {
    const cannonBody = physicsBody.getCannonBody();
    if (cannonBody) {
      const position = cannonBody.position.clone();
      position.x += computedMovement.x;
      position.y += computedMovement.y;
      position.z += computedMovement.z;
      cannonBody.position.copy(position);
      cannonBody.wakeUp();
    }
  }
  
  // 更新相机位置
  const characterPosition = characterTransform.getPosition();
  camera.getTransform().setPosition(
    characterPosition.x,
    characterPosition.y + 5,
    characterPosition.z + 10
  );
  camera.getTransform().lookAt(characterPosition);
});

// 启动引擎
engine.start();
