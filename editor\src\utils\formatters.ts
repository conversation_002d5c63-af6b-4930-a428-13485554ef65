/**
 * 格式化工具函数
 */

/**
 * 格式化字节数为人类可读的字符串
 * @param bytes 字节数
 * @param decimals 小数位数
 * @returns 格式化后的字符串
 */
export function formatBytes(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * 格式化数字，添加千位分隔符
 * @param num 数字
 * @param decimals 小数位数
 * @returns 格式化后的字符串
 */
export function formatNumber(num: number, decimals: number = 0): string {
  if (isNaN(num)) return '0';
  
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  });
}

/**
 * 格式化持续时间为人类可读的字符串
 * @param ms 毫秒数
 * @returns 格式化后的字符串
 */
export function formatDuration(ms: number): string {
  if (ms <= 0) return '0秒';

  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}天 ${hours % 24}小时`;
  } else if (hours > 0) {
    return `${hours}小时 ${minutes % 60}分钟`;
  } else if (minutes > 0) {
    return `${minutes}分钟 ${seconds % 60}秒`;
  } else {
    return `${seconds}秒`;
  }
}

/**
 * 格式化日期时间
 * @param date 日期对象或时间戳
 * @param format 格式化模式
 * @returns 格式化后的字符串
 */
export function formatDateTime(date: Date | number, format: 'full' | 'date' | 'time' | 'datetime' = 'full'): string {
  const d = typeof date === 'number' ? new Date(date) : date;
  
  switch (format) {
    case 'date':
      return d.toLocaleDateString('zh-CN');
    case 'time':
      return d.toLocaleTimeString('zh-CN');
    case 'datetime':
      return d.toLocaleDateString('zh-CN') + ' ' + d.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    case 'full':
    default:
      return d.toLocaleDateString('zh-CN') + ' ' + d.toLocaleTimeString('zh-CN');
  }
}

/**
 * 格式化相对时间（例如：3分钟前）
 * @param date 日期对象或时间戳
 * @returns 格式化后的字符串
 */
export function formatRelativeTime(date: Date | number): string {
  const d = typeof date === 'number' ? new Date(date) : date;
  const now = new Date();
  const diff = now.getTime() - d.getTime();
  
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (seconds < 60) {
    return '刚刚';
  } else if (minutes < 60) {
    return `${minutes}分钟前`;
  } else if (hours < 24) {
    return `${hours}小时前`;
  } else if (days < 30) {
    return `${days}天前`;
  } else {
    return formatDateTime(d, 'date');
  }
}

/**
 * 格式化百分比
 * @param value 值
 * @param total 总数
 * @param decimals 小数位数
 * @returns 格式化后的字符串
 */
export function formatPercent(value: number, total: number, decimals: number = 1): string {
  if (total === 0) return '0%';
  
  const percent = (value / total) * 100;
  return percent.toFixed(decimals) + '%';
}

/**
 * 格式化文件大小
 * @param size 文件大小（字节）
 * @returns 格式化后的字符串
 */
export function formatFileSize(size: number): string {
  return formatBytes(size);
}

/**
 * 格式化速率（例如：3.5 MB/s）
 * @param bytesPerSecond 每秒字节数
 * @returns 格式化后的字符串
 */
export function formatRate(bytesPerSecond: number): string {
  return formatBytes(bytesPerSecond) + '/s';
}
