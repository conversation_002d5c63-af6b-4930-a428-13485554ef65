# 渲染系统 (RenderSystem)

渲染系统是DL（Digital Learning）引擎的核心组件之一，负责管理和执行场景的渲染过程。它基于ECS（实体组件系统）架构，作为一个系统组件集成到引擎的世界中。

## 基本用法

```typescript
import { World } from '../core/World';
import { RenderSystem } from '../rendering/RenderSystem';
import { Renderer } from '../rendering/Renderer';

// 创建世界
const world = new World();

// 创建渲染器
const renderer = new Renderer({
  width: window.innerWidth,
  height: window.innerHeight,
  antialias: true,
  shadows: true
});

// 创建渲染系统
const renderSystem = new RenderSystem(renderer, {
  enableShadows: true,
  enablePostProcessing: true
});

// 添加渲染系统到世界
world.addSystem(renderSystem);
```

## 构造函数

```typescript
constructor(renderer: Renderer, options?: RenderSystemOptions)
```

### 参数

- `renderer: Renderer` - 渲染器实例，用于执行实际的渲染操作
- `options?: RenderSystemOptions` - 渲染系统选项

### RenderSystemOptions

```typescript
interface RenderSystemOptions {
  enableShadows?: boolean;       // 是否启用阴影
  enablePostProcessing?: boolean; // 是否启用后处理
  clearColor?: Color;            // 清除颜色
  clearAlpha?: number;           // 清除透明度
  sortObjects?: boolean;         // 是否排序对象
  autoUpdate?: boolean;          // 是否自动更新
}
```

## 属性

| 属性名 | 类型 | 描述 |
|--------|------|------|
| renderer | Renderer | 渲染器实例 |
| activeScene | Scene | 当前活跃的场景 |
| activeCamera | Camera | 当前活跃的相机 |
| shadowsEnabled | boolean | 是否启用阴影 |
| postProcessingEnabled | boolean | 是否启用后处理 |
| clearColor | Color | 清除颜色 |
| clearAlpha | number | 清除透明度 |
| sortObjects | boolean | 是否排序对象 |
| autoUpdate | boolean | 是否自动更新 |

## 方法

### setActiveScene

设置当前活跃的场景。

```typescript
setActiveScene(scene: Scene): void
```

#### 参数

- `scene: Scene` - 要设置为活跃的场景

#### 示例

```typescript
// 创建场景
const scene = new Scene();

// 设置为活跃场景
renderSystem.setActiveScene(scene);
```

### setActiveCamera

设置当前活跃的相机。

```typescript
setActiveCamera(camera: Camera): void
```

#### 参数

- `camera: Camera` - 要设置为活跃的相机

#### 示例

```typescript
// 创建相机
const camera = new Camera({
  type: CameraType.PERSPECTIVE,
  fov: 75,
  aspect: window.innerWidth / window.innerHeight,
  near: 0.1,
  far: 1000,
  position: { x: 0, y: 0, z: 5 },
  lookAt: { x: 0, y: 0, z: 0 }
});

// 设置为活跃相机
renderSystem.setActiveCamera(camera);
```

### setShadowsEnabled

设置是否启用阴影。

```typescript
setShadowsEnabled(enabled: boolean): void
```

#### 参数

- `enabled: boolean` - 是否启用阴影

#### 示例

```typescript
// 启用阴影
renderSystem.setShadowsEnabled(true);

// 禁用阴影
renderSystem.setShadowsEnabled(false);
```

### setPostProcessingEnabled

设置是否启用后处理。

```typescript
setPostProcessingEnabled(enabled: boolean): void
```

#### 参数

- `enabled: boolean` - 是否启用后处理

#### 示例

```typescript
// 启用后处理
renderSystem.setPostProcessingEnabled(true);

// 禁用后处理
renderSystem.setPostProcessingEnabled(false);
```

### setClearColor

设置清除颜色。

```typescript
setClearColor(color: Color, alpha?: number): void
```

#### 参数

- `color: Color` - 清除颜色
- `alpha?: number` - 清除透明度

#### 示例

```typescript
// 设置清除颜色为黑色
renderSystem.setClearColor({ r: 0, g: 0, b: 0 }, 1.0);

// 设置清除颜色为天蓝色
renderSystem.setClearColor({ r: 0.5, g: 0.7, b: 1.0 }, 1.0);
```

### setSortObjects

设置是否排序对象。

```typescript
setSortObjects(sort: boolean): void
```

#### 参数

- `sort: boolean` - 是否排序对象

#### 示例

```typescript
// 启用对象排序
renderSystem.setSortObjects(true);

// 禁用对象排序
renderSystem.setSortObjects(false);
```

### setAutoUpdate

设置是否自动更新。

```typescript
setAutoUpdate(autoUpdate: boolean): void
```

#### 参数

- `autoUpdate: boolean` - 是否自动更新

#### 示例

```typescript
// 启用自动更新
renderSystem.setAutoUpdate(true);

// 禁用自动更新
renderSystem.setAutoUpdate(false);
```

### update

更新渲染系统，执行渲染操作。

```typescript
update(deltaTime: number): void
```

#### 参数

- `deltaTime: number` - 自上一帧以来的时间增量（秒）

#### 示例

```typescript
// 在游戏循环中更新渲染系统
function gameLoop(time) {
  const deltaTime = (time - lastTime) / 1000;
  lastTime = time;
  
  // 更新渲染系统
  renderSystem.update(deltaTime);
  
  requestAnimationFrame(gameLoop);
}

let lastTime = performance.now();
requestAnimationFrame(gameLoop);
```

### resize

调整渲染系统大小。

```typescript
resize(width: number, height: number): void
```

#### 参数

- `width: number` - 新宽度
- `height: number` - 新高度

#### 示例

```typescript
// 调整渲染系统大小以匹配窗口大小
window.addEventListener('resize', () => {
  renderSystem.resize(window.innerWidth, window.innerHeight);
});
```

## 事件

渲染系统会触发以下事件：

| 事件名 | 描述 |
|--------|------|
| beforeRender | 在渲染之前触发 |
| afterRender | 在渲染之后触发 |
| resize | 在调整大小时触发 |

### 示例

```typescript
// 监听渲染事件
renderSystem.addEventListener('beforeRender', (event) => {
  console.log('渲染开始');
});

renderSystem.addEventListener('afterRender', (event) => {
  console.log('渲染完成');
});

renderSystem.addEventListener('resize', (event) => {
  console.log(`调整大小: ${event.width}x${event.height}`);
});
```

## 性能优化

为了获得最佳性能，请考虑以下建议：

1. 仅在需要时启用阴影和后处理
2. 使用适当的清除颜色（通常为黑色或天蓝色）
3. 在静态场景中禁用自动更新
4. 使用对象排序以减少状态更改
5. 在移动设备上降低渲染分辨率

## 与其他系统的集成

渲染系统可以与以下系统集成：

- **物理系统**：用于调试物理形状和碰撞
- **动画系统**：用于渲染骨骼和动画调试信息
- **UI系统**：用于渲染2D和3D用户界面
- **粒子系统**：用于渲染粒子效果

### 示例

```typescript
// 创建世界
const world = new World();

// 创建渲染器
const renderer = new Renderer({
  width: window.innerWidth,
  height: window.innerHeight,
  antialias: true
});

// 创建渲染系统
const renderSystem = new RenderSystem(renderer);

// 创建物理系统
const physicsSystem = new PhysicsSystem();

// 创建动画系统
const animationSystem = new AnimationSystem();

// 创建UI系统
const uiSystem = new UISystem();

// 创建粒子系统
const particleSystem = new ParticleSystem();

// 添加系统到世界
world.addSystem(renderSystem);
world.addSystem(physicsSystem);
world.addSystem(animationSystem);
world.addSystem(uiSystem);
world.addSystem(particleSystem);
```
