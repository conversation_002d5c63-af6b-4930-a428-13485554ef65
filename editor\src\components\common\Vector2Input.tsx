/**
 * 二维向量输入组件
 * 用于输入二维向量
 */
import React from 'react';
import { InputNumber, Space, Row, Col } from 'antd';
import { useTranslation } from 'react-i18next';

/**
 * 二维向量输入组件属性
 */
interface Vector2InputProps {
  /** 值 */
  value?: { x: number; y: number };
  /** 变更回调 */
  onChange?: (value: { x: number; y: number }) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 最小值 */
  min?: number;
  /** 最大值 */
  max?: number;
  /** 步长 */
  step?: number;
  /** 精度 */
  precision?: number;
  /** 标签 */
  labels?: [string, string];
}

/**
 * 二维向量输入组件
 */
const Vector2Input: React.FC<Vector2InputProps> = ({
  value = { x: 0, y: 0 },
  onChange,
  disabled = false,
  min,
  max,
  step = 0.1,
  precision = 2,
  labels = ['X', 'Y']
}) => {
  const { t } = useTranslation();
  
  // 处理X变更
  const handleXChange = (x: number | null) => {
    if (onChange) {
      onChange({ ...value, x: x || 0 });
    }
  };
  
  // 处理Y变更
  const handleYChange = (y: number | null) => {
    if (onChange) {
      onChange({ ...value, y: y || 0 });
    }
  };
  
  return (
    <Row gutter={8}>
      <Col span={12}>
        <Space>
          <span>{labels[0]}</span>
          <InputNumber
            value={value.x}
            onChange={handleXChange}
            disabled={disabled}
            min={min}
            max={max}
            step={step}
            precision={precision}
            style={{ width: '100%' }}
          />
        </Space>
      </Col>
      <Col span={12}>
        <Space>
          <span>{labels[1]}</span>
          <InputNumber
            value={value.y}
            onChange={handleYChange}
            disabled={disabled}
            min={min}
            max={max}
            step={step}
            precision={precision}
            style={{ width: '100%' }}
          />
        </Space>
      </Col>
    </Row>
  );
};

export default Vector2Input;
