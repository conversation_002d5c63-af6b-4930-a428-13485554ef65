# IR Engine Reference Documentation

Welcome to the IR Engine Reference Documentation! This document provides detailed reference information for various components, systems, and APIs of the IR Engine, helping you gain a deeper understanding of the engine's functionality and usage.

## Documentation Directory

- [Component Reference](./components.en.md) - Detailed reference for built-in components
- [Shader Reference](./shaders.en.md) - Detailed reference for built-in shaders
- [Scripting Reference](./scripting.en.md) - Detailed reference for scripting
- [Command Line Tool Reference](./cli.en.md) - Detailed reference for command line tools

## How to Use Reference Documentation

Reference documentation provides detailed technical information about various aspects of the IR Engine, suitable for looking up specific functionality, usage, and parameters during development. Unlike tutorials and guides, reference documentation focuses on providing complete and accurate technical details rather than step-by-step instructions.

### Finding Information

1. **Browse by Category**: Use the directory links above to browse reference documentation by category.
2. **Use Search**: Use the search function on the documentation website to quickly find specific functionality or APIs.
3. **Contextual Help**: In the editor, many interface elements provide contextual help that links directly to relevant reference documentation.

### Documentation Conventions

Reference documentation uses the following conventions:

- **Property Tables**: Properties are listed in tables, including name, type, default value, and description.
- **Method Tables**: Methods are listed in tables, including name, parameters, return value, and description.
- **Code Examples**: Actual code examples are provided to demonstrate how to use specific functionality.
- **Notes**: Important notes and best practices are marked with special formatting.
- **Related Links**: Links to related documentation are provided to help you learn more.

## Component Reference

The [Component Reference](./components.en.md) provides detailed information about all built-in components in the IR Engine, including:

- **Transform Components**: Control the position, rotation, and scale of entities in 3D space.
- **Rendering Components**: Responsible for rendering 3D models, particle effects, and UI elements.
- **Physics Components**: Provide physics simulation functionality, such as collision detection and rigid body dynamics.
- **Animation Components**: Control model animations and property animations.
- **Audio Components**: Play and control audio effects and music.
- **Interaction Components**: Handle user interaction and input.
- **Script Components**: Attach custom scripts to entities.
- **Effect Components**: Create various visual effects.
- **Network Components**: Handle network communication in multi-user scenarios.

## Shader Reference

The [Shader Reference](./shaders.en.md) provides detailed information about all built-in shaders in the IR Engine, including:

- **Standard Shader**: Basic lighting model and texture mapping.
- **PBR Shader**: Physically-based rendering for more realistic lighting and material representation.
- **Unlit Shader**: Simple shader not affected by lighting.
- **Transparent Shader**: Handle transparent and translucent materials.
- **Skybox Shader**: Render environment skyboxes.
- **Post-processing Shader**: Apply screen post-effects.
- **Particle Shader**: Render particle effects.
- **UI Shader**: Render user interface elements.
- **Custom Shader**: Create and use custom shaders.

## Scripting Reference

The [Scripting Reference](./scripting.en.md) provides detailed information about scripting in the IR Engine, including:

- **Scripting System Overview**: Basic concepts and working principles of the scripting system.
- **Script Lifecycle**: Lifecycle functions of script components and when they are called.
- **Script API**: Scripting programming interface and common functions.
- **Entity and Component Operations**: How to manipulate entities and components.
- **Input Handling**: How to handle user input.
- **Physics Interaction**: How to interact with the physics system.
- **Animation Control**: How to control animations.
- **UI Interaction**: How to interact with the UI system.
- **Network Communication**: How to implement network communication in multi-user scenarios.
- **Utility Classes**: Common utility classes and helper functions.
- **Debugging and Performance**: Script debugging and performance optimization techniques.
- **Best Practices**: Best practices and recommended patterns for scripting.

## Command Line Tool Reference

The [Command Line Tool Reference](./cli.en.md) provides detailed information about the IR Engine command line tools, including:

- **Installation and Configuration**: How to install and configure the command line tools.
- **Basic Commands**: Common commands and options.
- **Project Management**: How to create, open, and manage projects.
- **Asset Management**: How to import, export, and manage assets.
- **Building and Publishing**: How to build and publish projects.
- **Batch Operations**: How to perform batch operations.
- **Automation Scripts**: How to create and use automation scripts.
- **Advanced Usage**: Advanced features and techniques.
- **Troubleshooting**: Common issues and solutions.

## Getting More Help

If you can't find the information you need in the reference documentation, you can try the following resources:

- [User Guide](../README.md) - Provides more introductory and usage guidance
- [Tutorial Projects](../../examples/README.md) - Learn engine features through examples
- [API Documentation](../../developer/api/README.md) - More detailed programming interface documentation
- [Official Forum](https://ir-engine.example.com/forum) - Interact with the community and get help
- [Technical Support](mailto:<EMAIL>) - Contact the technical support team

## Contribution

We welcome users to contribute to the reference documentation. If you find errors in the documentation or have suggestions for improvement, please contact us through the following channels:

- Submit an Issue or Pull Request on GitHub
- Send an email to [<EMAIL>](mailto:<EMAIL>)
- Post in the documentation feedback section of the official forum

Thank you for using IR Engine!
