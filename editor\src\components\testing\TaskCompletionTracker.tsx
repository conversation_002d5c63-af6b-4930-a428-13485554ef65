/**
 * 任务完成度跟踪组件
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Progress,
  List,
  Typography,
  Space,
  Tag,
  Button,
  Tooltip,
  Statistic,
  Row,
  Col,
  Timeline,
  Divider,
  Badge,
  Empty
} from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  FileTextOutlined,
  FieldTimeOutlined,
  CheckOutlined,
  CloseOutlined,
  InfoCircleOutlined,
  Bar<PERSON><PERSON>Outlined,
  LineChartOutlined,
  PieChartOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { selectTasks, selectCurrentTaskId } from '../../store/testing/userTestingSlice';
import { TestTask, userTestingService } from '../../services/UserTestingService';

const { Title, Text, Paragraph } = Typography;
const { Countdown } = Statistic;

/**
 * 任务完成度跟踪组件属性
 */
interface TaskCompletionTrackerProps {
  onTaskStart?: (taskId: string) => void;
  onTaskComplete?: (taskId: string) => void;
  showDetailedStats?: boolean;
  showTimeline?: boolean;
  estimatedTimes?: Record<string, number>;
}

/**
 * 任务完成度跟踪组件
 */
const TaskCompletionTracker: React.FC<TaskCompletionTrackerProps> = ({
  onTaskStart,
  onTaskComplete,
  showDetailedStats = true,
  showTimeline = true,
  estimatedTimes = {}
}) => {
  const { t } = useTranslation();
  
  // 从Redux获取状态
  const tasks = useSelector(selectTasks);
  const currentTaskId = useSelector(selectCurrentTaskId);
  
  // 本地状态
  const [taskTimers, setTaskTimers] = useState<Record<string, number>>({});
  const [timerInterval, setTimerInterval] = useState<number | null>(null);
  
  // 计算任务完成进度
  const completedTasks = tasks.filter(task => task.completed);
  const progress = tasks.length > 0 ? Math.round((completedTasks.length / tasks.length) * 100) : 0;
  
  // 计算总耗时
  const totalTimeSpent = completedTasks.reduce((sum, task) => sum + (task.timeSpent || 0), 0);
  
  // 计算平均耗时
  const averageTimeSpent = completedTasks.length > 0 ? totalTimeSpent / completedTasks.length : 0;
  
  // 计算预计剩余时间
  const remainingTasks = tasks.filter(task => !task.completed);
  const estimatedRemainingTime = remainingTasks.reduce((sum, task) => {
    // 使用估计时间或平均耗时
    const estimatedTime = estimatedTimes[task.id] || averageTimeSpent;
    return sum + estimatedTime;
  }, 0);
  
  // 设置定时器更新任务计时
  useEffect(() => {
    if (currentTaskId && !timerInterval) {
      const interval = window.setInterval(() => {
        setTaskTimers(prev => {
          const currentTask = tasks.find(t => t.id === currentTaskId);
          if (currentTask && currentTask.startTime && !currentTask.completed) {
            const elapsed = Date.now() - currentTask.startTime;
            return { ...prev, [currentTaskId]: elapsed };
          }
          return prev;
        });
      }, 1000);
      
      setTimerInterval(interval);
    }
    
    return () => {
      if (timerInterval) {
        window.clearInterval(timerInterval);
        setTimerInterval(null);
      }
    };
  }, [currentTaskId, tasks]);
  
  // 处理任务点击
  const handleTaskClick = (taskId: string) => {
    try {
      userTestingService.setCurrentTask(taskId);
      
      if (onTaskStart) {
        onTaskStart(taskId);
      }
    } catch (error) {
      console.error('设置当前任务失败:', error);
    }
  };
  
  // 处理任务完成
  const handleCompleteTask = (taskId: string) => {
    try {
      userTestingService.completeTask(taskId);
      
      if (onTaskComplete) {
        onTaskComplete(taskId);
      }
    } catch (error) {
      console.error('完成任务失败:', error);
    }
  };
  
  // 格式化时间
  const formatTime = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    const remainingMinutes = minutes % 60;
    const remainingSeconds = seconds % 60;
    
    return `${hours > 0 ? `${hours}小时 ` : ''}${remainingMinutes}分钟 ${remainingSeconds}秒`;
  };
  
  // 渲染任务列表
  const renderTaskList = () => {
    if (!tasks || tasks.length === 0) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="没有测试任务"
        />
      );
    }
    
    return (
      <List
        itemLayout="horizontal"
        dataSource={tasks}
        renderItem={task => (
          <List.Item
            actions={[
              task.completed ? (
                <Tag color="success" icon={<CheckCircleOutlined />}>已完成</Tag>
              ) : task.id === currentTaskId ? (
                <Button
                  type="primary"
                  size="small"
                  icon={<CheckOutlined />}
                  onClick={() => handleCompleteTask(task.id)}
                >
                  完成
                </Button>
              ) : (
                <Button
                  type="default"
                  size="small"
                  icon={<PlayCircleOutlined />}
                  onClick={() => handleTaskClick(task.id)}
                >
                  开始
                </Button>
              )
            ]}
          >
            <List.Item.Meta
              avatar={
                <Badge
                  status={task.completed ? 'success' : task.id === currentTaskId ? 'processing' : 'default'}
                  style={{ marginTop: 8 }}
                />
              }
              title={
                <Space>
                  <Text strong>{task.title}</Text>
                  {task.id === currentTaskId && (
                    <Tag color="blue">当前</Tag>
                  )}
                </Space>
              }
              description={
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Text>{task.description}</Text>
                  {task.startTime && (
                    <Space>
                      <ClockCircleOutlined />
                      <Text type="secondary">
                        {task.completed && task.timeSpent
                          ? `耗时: ${formatTime(task.timeSpent)}`
                          : task.id === currentTaskId
                            ? `进行中: ${formatTime(taskTimers[task.id] || 0)}`
                            : `开始于: ${new Date(task.startTime).toLocaleString()}`
                        }
                      </Text>
                    </Space>
                  )}
                </Space>
              }
            />
          </List.Item>
        )}
      />
    );
  };
  
  // 渲染任务时间线
  const renderTaskTimeline = () => {
    if (!showTimeline || tasks.length === 0) {
      return null;
    }
    
    return (
      <Card title="任务时间线" size="small" style={{ marginTop: 16 }}>
        <Timeline mode="left">
          {tasks.map(task => (
            <Timeline.Item
              key={task.id}
              color={task.completed ? 'green' : task.id === currentTaskId ? 'blue' : 'gray'}
              label={task.startTime ? new Date(task.startTime).toLocaleString() : '未开始'}
            >
              <Space direction="vertical">
                <Text strong>{task.title}</Text>
                {task.completed && task.endTime && (
                  <Text type="secondary">
                    完成于: {new Date(task.endTime).toLocaleString()}
                    {task.timeSpent && ` (耗时: ${formatTime(task.timeSpent)})`}
                  </Text>
                )}
              </Space>
            </Timeline.Item>
          ))}
        </Timeline>
      </Card>
    );
  };
  
  // 渲染详细统计
  const renderDetailedStats = () => {
    if (!showDetailedStats || tasks.length === 0) {
      return null;
    }
    
    return (
      <Card title="任务统计" size="small" style={{ marginTop: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Statistic
              title="总任务数"
              value={tasks.length}
              prefix={<FileTextOutlined />}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="已完成"
              value={completedTasks.length}
              prefix={<CheckCircleOutlined />}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="完成率"
              value={progress}
              suffix="%"
              prefix={<PieChartOutlined />}
            />
          </Col>
        </Row>
        
        <Divider />
        
        <Row gutter={16}>
          <Col span={8}>
            <Statistic
              title="总耗时"
              value={totalTimeSpent}
              formatter={value => formatTime(value as number)}
              prefix={<FieldTimeOutlined />}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="平均耗时"
              value={averageTimeSpent}
              formatter={value => formatTime(value as number)}
              prefix={<BarChartOutlined />}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="预计剩余时间"
              value={estimatedRemainingTime}
              formatter={value => formatTime(value as number)}
              prefix={<LineChartOutlined />}
            />
          </Col>
        </Row>
      </Card>
    );
  };
  
  return (
    <div className="task-completion-tracker">
      <Card
        title="任务完成度跟踪"
        size="small"
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Progress
            percent={progress}
            status={progress === 100 ? 'success' : 'active'}
            format={percent => `${percent}% (${completedTasks.length}/${tasks.length})`}
          />
          
          {renderTaskList()}
        </Space>
      </Card>
      
      {renderTaskTimeline()}
      
      {renderDetailedStats()}
    </div>
  );
};

export default TaskCompletionTracker;
