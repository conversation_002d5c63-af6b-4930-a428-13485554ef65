{"terrain": {"editor": {"title": "地形编辑器", "noTerrainSelected": "请选择一个地形实体", "createTerrain": "创建地形"}, "tabs": {"sculpting": "雕刻", "texture": "纹理", "generation": "生成", "lod": "LOD", "physics": "物理", "performance": "性能", "importExport": "导入/导出"}, "errors": {"noTerrainSelected": "未选择地形实体", "exportFailed": "导出失败", "importFailed": "导入失败", "generationFailed": "地形生成失败"}, "messages": {"saveSuccess": "地形保存成功"}, "brushTypes": {"raise": "提升", "lower": "降低", "smooth": "平滑", "flatten": "平坦化", "noise": "噪声", "paint": "绘制"}, "brushShapes": {"circle": "圆形", "square": "方形"}, "sculpting": {"brushSettings": "笔刷设置", "brushType": "笔刷类型", "brushShape": "笔刷形状", "brushSize": "笔刷大小", "brushSizeTooltip": "调整笔刷的大小", "brushStrength": "笔刷强度", "brushStrengthTooltip": "调整笔刷的强度", "brushFalloff": "笔刷衰减", "brushFalloffTooltip": "调整笔刷的衰减程度", "targetHeight": "目标高度", "targetHeightTooltip": "平坦化时的目标高度", "noiseSeed": "噪声种子", "noiseSeedTooltip": "噪声生成的随机种子", "noiseScale": "噪声比例", "noiseScaleTooltip": "噪声的比例", "visualizationSettings": "可视化设置", "showBrushPreview": "显示笔刷预览", "showHeightContours": "显示高度等高线", "showSlopeOverlay": "显示斜度覆盖", "limitSettings": "限制设置", "enableHeightLimit": "启用高度限制", "minHeightLimit": "最小高度限制", "maxHeightLimit": "最大高度限制", "enableSlopeLimit": "启用斜度限制", "minSlopeLimit": "最小斜度限制", "maxSlopeLimit": "最大斜度限制"}, "texture": {"layers": "纹理层", "layer": "层", "layerProperties": "层属性", "paintingTools": "绘制工具", "addLayer": "添加层", "editLayer": "编辑层", "confirmDelete": "确定要删除此层吗？", "atLeastOneLayer": "至少需要保留一个纹理层", "diffuseTexture": "漫反射纹理", "normalMap": "法线贴图", "roughnessMap": "粗糙度贴图", "upload": "上传", "tiling": "平铺", "blendSettings": "混合设置", "minHeight": "最小高度", "maxHeight": "最大高度", "minSlope": "最小斜度", "maxSlope": "最大斜度", "autoBlendMode": "自动混合模式", "autoBlendModeTooltip": "根据高度和斜度自动混合纹理", "brushSettings": "笔刷设置", "brushShape": "笔刷形状", "brushSize": "笔刷大小", "brushStrength": "笔刷强度", "brushFalloff": "笔刷衰减", "showBrushPreview": "显示笔刷预览", "noTextureSelected": "未选择纹理"}, "generation": {"parameters": "生成参数", "algorithm": "算法", "seed": "种子", "scale": "比例", "scaleTooltip": "地形的整体比例", "persistence": "持久度", "persistenceTooltip": "噪声的持久度，影响地形的粗糙程度", "octaves": "八度", "octavesTooltip": "噪声的八度数，影响地形的细节程度", "frequency": "频率", "frequencyTooltip": "噪声的频率，影响地形的变化频率", "amplitude": "幅度", "amplitudeTooltip": "噪声的幅度，影响地形的高度变化", "erosion": "侵蚀", "erosionIterations": "侵蚀迭代次数", "erosionIterationsTooltip": "侵蚀模拟的迭代次数", "erosionStrength": "侵蚀强度", "erosionStrengthTooltip": "侵蚀的强度", "thermalErosion": "热侵蚀", "thermalErosionIterations": "热侵蚀迭代次数", "thermalErosionStrength": "热侵蚀强度", "slopeThreshold": "坡度阈值", "slopeThresholdTooltip": "触发侵蚀的最小坡度", "depositionRate": "沉积率", "depositionRateTooltip": "侵蚀物质的沉积比例", "hydraulicErosion": "水侵蚀", "hydraulicErosionIterations": "水侵蚀迭代次数", "droplets": "雨滴数量", "dropletsTooltip": "模拟的雨滴数量", "capacity": "容量", "capacityTooltip": "雨滴携带沉积物的能力", "evaporationRate": "蒸发率", "evaporationRateTooltip": "雨滴的蒸发速率", "inertia": "惯性", "inertiaTooltip": "雨滴保持方向的能力", "gravity": "重力", "gravityTooltip": "影响雨滴流动的重力大小", "river": "河流", "riverCount": "河流数量", "riverWidth": "河流宽度", "riverDepth": "河流深度", "sinuosity": "曲折度", "sinuosityTooltip": "河流的曲折程度", "branchProbability": "分支概率", "branchProbabilityTooltip": "河流形成分支的概率", "minLength": "最小长度", "maxLength": "最大长度", "mountain": "山脉", "mountainCount": "山脉数量", "mountainHeight": "山脉高度", "mountainWidth": "山脉宽度", "roughness": "粗糙度", "roughnessTooltip": "山脉的粗糙程度", "sharpness": "锐度", "sharpnessTooltip": "山脉的锐利程度", "canyon": "峡谷", "canyonCount": "峡谷数量", "canyonDepth": "峡谷深度", "canyonWidth": "峡谷宽度", "heightMap": "高度图", "dragHeightMap": "拖拽高度图到此处或点击上传", "heightMapHint": "支持PNG、JPG和RAW格式", "preview": "预览", "previewDisabled": "预览已禁用", "enablePreview": "启用预览", "presets": "预设", "savePreset": "保存预设", "enterPresetName": "请输入预设名称", "presetSaved": "预设已保存"}, "physics": {"settings": "物理设置", "usePhysics": "使用物理", "enablePhysics": "启用地形物理", "physicsResolution": "物理分辨率", "physicsResolutionTooltip": "地形物理碰撞器的分辨率，较高的值提供更精确的碰撞，但会消耗更多性能", "materialSettings": "材质设置", "materialType": "材质类型", "friction": "摩擦力", "frictionTooltip": "地形表面的摩擦系数，影响物体在地形上的滑动", "restitution": "弹性", "restitutionTooltip": "地形表面的弹性系数，影响物体碰撞后的反弹", "density": "密度", "densityTooltip": "地形的密度，影响物体与地形碰撞的物理反应", "debugSettings": "调试设置", "showDebugVisuals": "显示调试可视化", "enableDebugVisuals": "启用物理调试可视化", "applySettings": "应用设置", "settingsApplied": "物理设置已应用", "materials": {"default": "默认", "rock": "岩石", "soil": "土壤", "sand": "沙子", "ice": "冰", "wood": "木头", "metal": "金属", "custom": "自定义"}, "actions": "操作", "generate": "生成地形", "cancel": "取消", "export": "导出", "import": "导入", "exportHeightMap": "导出高度图", "exportJSON": "导出JSON", "importing": "正在导入...", "heightMapImported": "高度图已导入", "jsonImported": "JSON已导入", "generationComplete": "地形生成完成", "generationCancelled": "地形生成已取消", "heightMapExported": "高度图已导出", "jsonExported": "JSON已导出", "algorithms": {"perlin": "柏林噪声", "fractal": "分形", "diamondSquare": "金刚石方块", "multifractal": "多重分形", "heightmap": "高度图", "thermalErosion": "热侵蚀", "hydraulicErosion": "水侵蚀", "river": "河流生成", "undergroundRiver": "地下河生成", "mountain": "山脉生成", "canyon": "峡谷生成", "cave": "洞穴生成", "cliff": "悬崖生成", "volcano": "火山生成", "plain": "平原生成", "hills": "丘陵生成", "desert": "沙漠生成", "island": "岛屿生成", "featureCombination": "特征组合生成"}, "presets": {"mountains": "山脉", "hills": "丘陵", "plains": "平原", "islands": "岛屿", "rivers": "河流", "undergroundRivers": "地下河", "undergroundLakes": "地下湖泊", "undergroundLakesWithStalactites": "钟乳石地下湖泊", "undergroundLakesWithWaterfall": "瀑布地下湖泊", "undergroundHotSprings": "地下温泉", "complexCaveSystem": "复杂洞穴系统", "canyons": "峡谷", "caves": "洞穴", "cliffs": "悬崖", "volcanoes": "火山", "desert": "沙漠", "mountainsAndRivers": "山脉与河流", "volcanoIsland": "火山岛", "canyonLands": "峡谷地带"}, "preview": "预览", "previewEnabled": "预览已启用", "previewDisabled": "预览已禁用", "enablePreview": "启用预览", "params": {"count": "数量", "width": "宽度", "depth": "深度", "height": "高度", "radius": "半径", "sinuosity": "曲折度", "roughness": "粗糙度", "branchProbability": "分支概率", "minLength": "最小长度", "maxLength": "最大长度", "seed": "种子", "randomSeed": "随机种子", "size": "大小", "complexity": "复杂度", "complexityTooltip": "影响地形特征的复杂程度", "connectionProbability": "连接概率", "steepness": "陡峭度", "craterSize": "火山口大小", "craterDepth": "火山口深度", "baseTerrainType": "基础地形类型", "features": "特征", "feature": "特征", "weight": "权重", "addFeature": "添加特征", "removeFeature": "移除特征", "thermal": "热侵蚀", "hydraulic": "水侵蚀", "undergroundRiver": "地下河", "undergroundLake": "地下湖泊", "minRadius": "最小半径", "maxRadius": "最大半径", "minDepthRatio": "最小深度比例", "maxDepthRatio": "最大深度比例", "depthVariation": "深度变化", "depthVariationTooltip": "控制湖底的不平整程度", "caveProbability": "洞穴连接概率", "riverProbability": "河流连接概率", "shapeType": "形状类型", "shapeTypeCircular": "圆形", "shapeTypeIrregular": "不规则", "shapeTypeBranching": "分叉", "shapeTypeConnected": "连接", "shapeTypeCavern": "洞穴型", "generateStalactites": "生成钟乳石", "stalactiteDensity": "钟乳石密度", "generateWaterfall": "生成瀑布", "waterfallHeight": "瀑布高度", "generateHotSpring": "生成温泉", "hotSpringTemperature": "温泉温度"}}, "lod": {"settings": "LOD设置", "useLOD": "使用LOD", "useLODTooltip": "启用地形LOD系统", "useQuadTree": "使用四叉树", "useQuadTreeTooltip": "使用四叉树进行地形分块", "maxLODLevels": "最大LOD级别", "transitionRange": "过渡范围", "transitionRangeTooltip": "LOD级别之间的过渡范围", "lodLevels": "LOD级别", "level": "级别", "distance": "距离", "resolutionScale": "分辨率比例", "enabled": "启用", "addLevel": "添加级别", "editLevel": "编辑级别 {level}", "atLeastOneLevel": "至少需要一个LOD级别", "maxLevelsReached": "已达到最大LOD级别数 ({max})", "visualization": "可视化", "showWireframe": "显示线框", "showLODColors": "显示LOD颜色", "showPerformanceMetrics": "显示性能指标", "triangleCount": "三角形数量", "chunkCount": "块数量", "drawCalls": "绘制调用", "frameTime": "帧时间", "actions": "操作", "applySettings": "应用设置", "settingsApplied": "LOD设置已应用"}, "importExport": {"title": "导入/导出", "heightMap": "高度图", "json": "JSON", "thirdParty": "第三方格式", "format": "格式", "options": "选项", "export": "导出", "import": "导入", "exportSuccess": "导出成功", "importSuccess": "导入成功", "flipY": "反转Y轴", "normalize": "归一化", "width": "宽度", "widthTooltip": "导出图像的宽度，0表示使用地形分辨率", "height": "高度", "heightTooltip": "导出图像的高度，0表示使用地形分辨率", "includeTextures": "包含纹理", "includeNormals": "包含法线", "includePhysics": "包含物理属性", "prettyPrint": "美化输出", "dragHeightMap": "拖拽高度图到此处或点击上传", "heightMapHint": "支持PNG、JPG、RAW等格式", "dragJSON": "拖拽JSON文件到此处或点击上传", "jsonHint": "支持JSON格式的地形数据", "dragThirdParty": "拖拽第三方格式文件到此处或点击上传", "thirdPartyHint": "支持多种第三方地形格式", "formats": {"png": "PNG", "jpeg": "JPEG", "raw": "RAW", "r16": "R16 (16位灰度)", "r32": "R32 (32位浮点)", "asc": "ASC (ESRI ASCII Grid)", "hgt": "HGT (SRTM)", "ter": "TER (Terragen)", "bt": "BT (Binary <PERSON>in)", "unity": "Unity地形", "unreal": "Unreal地形", "worldMachine": "World Machine", "terragen": "Terragen", "l3dt": "L3DT", "geotiff": "GeoTIFF", "usgsDem": "USGS DEM", "srtm": "SRTM"}}}}