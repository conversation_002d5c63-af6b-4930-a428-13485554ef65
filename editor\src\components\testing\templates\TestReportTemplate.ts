/**
 * 测试报告模板
 */
import { TestReport } from '../TestReportGenerator';

/**
 * 生成HTML测试报告
 * @param report 测试报告
 * @returns HTML字符串
 */
export function generateHtmlReport(report: TestReport): string {
  return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>用户测试报告 - ${report.sessionId}</title>
  <style>
    body {
      font-family: "Microsoft YaHei", Arial, sans-serif;
      margin: 0;
      padding: 20px;
      color: #333;
      line-height: 1.6;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: #fff;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      border-radius: 5px;
    }
    h1, h2, h3, h4 {
      color: #0066cc;
      margin-top: 20px;
    }
    h1 {
      text-align: center;
      padding-bottom: 10px;
      border-bottom: 2px solid #0066cc;
      margin-bottom: 30px;
    }
    .summary {
      background-color: #f5f5f5;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 30px;
    }
    .section {
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid #eee;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 12px;
      text-align: left;
    }
    th {
      background-color: #f2f2f2;
      font-weight: bold;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .progress-bar {
      background-color: #e0e0e0;
      border-radius: 4px;
      height: 20px;
      margin-top: 5px;
    }
    .progress-bar-fill {
      background-color: #4caf50;
      height: 100%;
      border-radius: 4px;
      transition: width 0.3s ease;
    }
    .task-completed {
      color: #4caf50;
      font-weight: bold;
    }
    .task-in-progress {
      color: #ff9800;
      font-weight: bold;
    }
    .task-not-started {
      color: #9e9e9e;
    }
    .chart {
      margin: 20px 0;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
      background-color: #f9f9f9;
    }
    .feedback-item {
      border: 1px solid #ddd;
      padding: 15px;
      margin-bottom: 15px;
      border-radius: 5px;
      background-color: #f9f9f9;
    }
    .feedback-content {
      margin-top: 10px;
      padding: 10px;
      background-color: #fff;
      border-radius: 3px;
      border-left: 4px solid #0066cc;
    }
    .rating {
      color: #ff9800;
      font-size: 18px;
    }
    .footer {
      text-align: center;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;
      color: #666;
    }
    .badge {
      display: inline-block;
      padding: 3px 8px;
      border-radius: 3px;
      font-size: 12px;
      font-weight: bold;
      margin-right: 5px;
    }
    .badge-bug {
      background-color: #f44336;
      color: white;
    }
    .badge-suggestion {
      background-color: #2196f3;
      color: white;
    }
    .badge-rating {
      background-color: #ff9800;
      color: white;
    }
    .badge-comment {
      background-color: #4caf50;
      color: white;
    }
    .stats-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 20px;
    }
    .stat-box {
      flex: 1;
      min-width: 200px;
      padding: 15px;
      background-color: #f5f5f5;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #0066cc;
      margin: 10px 0;
    }
    .stat-label {
      font-size: 14px;
      color: #666;
    }
    .timeline {
      position: relative;
      max-width: 1200px;
      margin: 20px auto;
    }
    .timeline::after {
      content: '';
      position: absolute;
      width: 6px;
      background-color: #0066cc;
      top: 0;
      bottom: 0;
      left: 50%;
      margin-left: -3px;
    }
    .timeline-item {
      padding: 10px 40px;
      position: relative;
      width: 50%;
      box-sizing: border-box;
    }
    .timeline-item::after {
      content: '';
      position: absolute;
      width: 20px;
      height: 20px;
      background-color: white;
      border: 4px solid #0066cc;
      border-radius: 50%;
      top: 15px;
      z-index: 1;
    }
    .timeline-left {
      left: 0;
    }
    .timeline-right {
      left: 50%;
    }
    .timeline-left::after {
      right: -10px;
    }
    .timeline-right::after {
      left: -10px;
    }
    .timeline-content {
      padding: 15px;
      background-color: white;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .timeline-date {
      color: #666;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>用户测试报告</h1>
    
    <div class="summary">
      <h2>测试会话摘要</h2>
      <div class="stats-container">
        <div class="stat-box">
          <div class="stat-label">会话ID</div>
          <div class="stat-value" style="font-size: 16px;">${report.sessionId}</div>
        </div>
        <div class="stat-box">
          <div class="stat-label">用户</div>
          <div class="stat-value">${report.userName}</div>
        </div>
        <div class="stat-box">
          <div class="stat-label">开始时间</div>
          <div class="stat-value" style="font-size: 16px;">${new Date(report.sessionStartTime).toLocaleString()}</div>
        </div>
        <div class="stat-box">
          <div class="stat-label">结束时间</div>
          <div class="stat-value" style="font-size: 16px;">${new Date(report.sessionEndTime).toLocaleString()}</div>
        </div>
        <div class="stat-box">
          <div class="stat-label">总时长</div>
          <div class="stat-value">${formatDuration(report.sessionDuration)}</div>
        </div>
      </div>
    </div>
    
    <!-- 任务统计 -->
    <div class="section">
      <h2>任务完成情况</h2>
      
      <div class="stats-container">
        <div class="stat-box">
          <div class="stat-label">总任务数</div>
          <div class="stat-value">${report.taskStats.total}</div>
        </div>
        <div class="stat-box">
          <div class="stat-label">已完成任务</div>
          <div class="stat-value">${report.taskStats.completed}</div>
        </div>
        <div class="stat-box">
          <div class="stat-label">完成率</div>
          <div class="stat-value">${(report.taskStats.completionRate * 100).toFixed(2)}%</div>
        </div>
        <div class="stat-box">
          <div class="stat-label">平均完成时间</div>
          <div class="stat-value">${formatDuration(report.taskStats.averageCompletionTime)}</div>
        </div>
      </div>
      
      <h3>任务详情</h3>
      <table>
        <tr>
          <th>任务</th>
          <th>状态</th>
          <th>开始时间</th>
          <th>结束时间</th>
          <th>耗时</th>
          <th>完成度</th>
        </tr>
        ${report.taskStats.tasks.map(task => `
        <tr>
          <td>
            <strong>${task.title}</strong>
            <div>${task.description}</div>
          </td>
          <td class="task-${task.status}">${getStatusText(task.status)}</td>
          <td>${task.startTime ? new Date(task.startTime).toLocaleString() : '-'}</td>
          <td>${task.endTime ? new Date(task.endTime).toLocaleString() : '-'}</td>
          <td>${task.timeSpent ? formatDuration(task.timeSpent) : '-'}</td>
          <td>
            <div class="progress-bar">
              <div class="progress-bar-fill" style="width: ${task.completionPercentage}%"></div>
            </div>
            ${task.completionPercentage}%
          </td>
        </tr>
        `).join('')}
      </table>
      
      <h3>任务时间线</h3>
      <div class="timeline">
        ${report.taskStats.tasks.map((task, index) => `
        <div class="timeline-item ${index % 2 === 0 ? 'timeline-left' : 'timeline-right'}">
          <div class="timeline-content">
            <div class="timeline-date">${task.startTime ? new Date(task.startTime).toLocaleString() : '未开始'}</div>
            <h3>${task.title}</h3>
            <p>${task.description}</p>
            <p class="task-${task.status}">${getStatusText(task.status)}</p>
            ${task.timeSpent ? `<p>耗时: ${formatDuration(task.timeSpent)}</p>` : ''}
          </div>
        </div>
        `).join('')}
      </div>
    </div>
    
    <!-- 反馈统计 -->
    <div class="section">
      <h2>用户反馈</h2>
      
      <div class="stats-container">
        <div class="stat-box">
          <div class="stat-label">总反馈数</div>
          <div class="stat-value">${report.feedbackStats.total}</div>
        </div>
        <div class="stat-box">
          <div class="stat-label">平均评分</div>
          <div class="stat-value">${report.feedbackStats.averageRating.toFixed(1)}<span style="font-size: 16px;">/5</span></div>
        </div>
      </div>
      
      <h3>反馈类型分布</h3>
      <table>
        <tr>
          <th>类型</th>
          <th>数量</th>
        </tr>
        ${Object.entries(report.feedbackStats.byType).map(([type, count]) => `
        <tr>
          <td>
            <span class="badge badge-${type}">${getFeedbackTypeText(type)}</span>
          </td>
          <td>${count}</td>
        </tr>
        `).join('')}
      </table>
      
      <h3>反馈详情</h3>
      ${report.feedbackStats.feedback.map(feedback => `
      <div class="feedback-item">
        <div>
          <span class="badge badge-${feedback.type}">${getFeedbackTypeText(feedback.type)}</span>
          <span style="float: right;">${new Date(feedback.timestamp).toLocaleString()}</span>
        </div>
        ${feedback.rating ? `<div class="rating">评分: ${'★'.repeat(feedback.rating)}${'☆'.repeat(5 - feedback.rating)} (${feedback.rating}/5)</div>` : ''}
        <div class="feedback-content">${feedback.content}</div>
        ${feedback.taskId ? `<div style="margin-top: 10px;">关联任务: ${feedback.taskId}</div>` : ''}
      </div>
      `).join('')}
    </div>
    
    <!-- 行为统计 -->
    ${report.behaviorStats ? `
    <div class="section">
      <h2>用户行为分析</h2>
      
      <div class="stats-container">
        <div class="stat-box">
          <div class="stat-label">总操作数</div>
          <div class="stat-value">${report.behaviorStats.totalActions}</div>
        </div>
      </div>
      
      <h3>操作类型分布</h3>
      <table>
        <tr>
          <th>类型</th>
          <th>数量</th>
        </tr>
        ${Object.entries(report.behaviorStats.byType).map(([type, count]) => `
        <tr>
          <td>${getActionTypeText(type)}</td>
          <td>${count}</td>
        </tr>
        `).join('')}
      </table>
    </div>
    ` : ''}
    
    <!-- 元数据 -->
    ${report.metadata ? `
    <div class="section">
      <h2>测试环境</h2>
      <table>
        <tr>
          <th>项目ID</th>
          <td>${report.metadata.projectId || '-'}</td>
        </tr>
        <tr>
          <th>场景ID</th>
          <td>${report.metadata.sceneId || '-'}</td>
        </tr>
        <tr>
          <th>浏览器</th>
          <td>${report.metadata.browser || '-'}</td>
        </tr>
        <tr>
          <th>操作系统</th>
          <td>${report.metadata.os || '-'}</td>
        </tr>
        <tr>
          <th>屏幕尺寸</th>
          <td>${report.metadata.screenSize || '-'}</td>
        </tr>
      </table>
    </div>
    ` : ''}
    
    <div class="footer">
      <p>报告生成时间: ${new Date(report.generatedAt).toLocaleString()}</p>
      <p>DL（Digital Learning）引擎编辑器 - 用户测试报告</p>
    </div>
  </div>
</body>
</html>`;
}

/**
 * 格式化时长
 * @param duration 时长（毫秒）
 * @returns 格式化后的时长
 */
function formatDuration(duration: number): string {
  const seconds = Math.floor(duration / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  const remainingMinutes = minutes % 60;
  const remainingSeconds = seconds % 60;
  
  if (hours > 0) {
    return `${hours}小时 ${remainingMinutes}分钟 ${remainingSeconds}秒`;
  } else if (minutes > 0) {
    return `${minutes}分钟 ${remainingSeconds}秒`;
  } else {
    return `${seconds}秒`;
  }
}

/**
 * 获取状态文本
 * @param status 状态
 * @returns 状态文本
 */
function getStatusText(status: string): string {
  switch (status) {
    case 'completed':
      return '已完成';
    case 'in_progress':
      return '进行中';
    case 'not_started':
      return '未开始';
    default:
      return status;
  }
}

/**
 * 获取反馈类型文本
 * @param type 反馈类型
 * @returns 反馈类型文本
 */
function getFeedbackTypeText(type: string): string {
  switch (type) {
    case 'bug':
      return '问题报告';
    case 'suggestion':
      return '改进建议';
    case 'rating':
      return '功能评分';
    case 'comment':
      return '一般评论';
    default:
      return type;
  }
}

/**
 * 获取操作类型文本
 * @param type 操作类型
 * @returns 操作类型文本
 */
function getActionTypeText(type: string): string {
  switch (type) {
    case 'mouse_move':
      return '鼠标移动';
    case 'mouse_click':
      return '鼠标点击';
    case 'key_press':
      return '键盘输入';
    case 'scroll':
      return '滚动';
    case 'component_interaction':
      return '组件交互';
    case 'menu_selection':
      return '菜单选择';
    case 'tool_usage':
      return '工具使用';
    case 'panel_switch':
      return '面板切换';
    case 'dialog_interaction':
      return '对话框交互';
    case 'scene_interaction':
      return '场景交互';
    case 'object_selection':
      return '对象选择';
    case 'property_change':
      return '属性修改';
    case 'navigation':
      return '导航';
    case 'search':
      return '搜索';
    case 'undo_redo':
      return '撤销/重做';
    case 'save_load':
      return '保存/加载';
    case 'collaboration':
      return '协作';
    case 'error':
      return '错误';
    default:
      return type;
  }
}
