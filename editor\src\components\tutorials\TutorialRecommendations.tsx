/**
 * 教程推荐组件
 * 展示推荐的教程和教程系列
 */
import React, { useState, useEffect } from 'react';
import { Card, Tabs, List, Tag, Button, Typography, Tooltip, Empty, Skeleton, Badge, Carousel } from 'antd';
import { 
  PlayCircleOutlined, 
  ReadOutlined, 
  CodeOutlined, 
  StarOutlined, 
  RightOutlined, 
  ClockCircleOutlined,
  TrophyOutlined,
  FireOutlined,
  BookOutlined,
  TagOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { 
  tutorialRecommendationService, 
  TutorialItem, 
  TutorialSeries, 
  TutorialType, 
  TutorialDifficulty 
} from '../../services/TutorialRecommendationService';
import { TutorialService } from '../../services/TutorialService';
import { VideoTutorialService } from '../../services/VideoTutorialService';
import { ExampleProjectService } from '../../services/ExampleProjectService';
import './TutorialRecommendations.less';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

/**
 * 教程推荐组件属性接口
 */
interface TutorialRecommendationsProps {
  maxItems?: number;
  showSeries?: boolean;
  showDifficulty?: boolean;
  showTags?: boolean;
  showProgress?: boolean;
  onTutorialSelect?: (item: TutorialItem) => void;
  onSeriesSelect?: (series: TutorialSeries) => void;
}

/**
 * 教程推荐组件
 */
export const TutorialRecommendations: React.FC<TutorialRecommendationsProps> = ({
  maxItems = 5,
  showSeries = true,
  showDifficulty = true,
  showTags = true,
  showProgress = true,
  onTutorialSelect,
  onSeriesSelect,
}) => {
  const { t } = useTranslation();
  const [recommendedItems, setRecommendedItems] = useState<TutorialItem[]>([]);
  const [recommendedSeries, setRecommendedSeries] = useState<TutorialSeries[]>([]);
  const [recentItems, setRecentItems] = useState<TutorialItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<string>('recommended');

  // 初始化
  useEffect(() => {
    loadRecommendations();

    // 监听教程完成事件
    const handleItemCompleted = () => {
      loadRecommendations();
    };

    tutorialRecommendationService.on('itemCompleted', handleItemCompleted);
    tutorialRecommendationService.on('seriesCompleted', handleItemCompleted);

    return () => {
      tutorialRecommendationService.off('itemCompleted', handleItemCompleted);
      tutorialRecommendationService.off('seriesCompleted', handleItemCompleted);
    };
  }, [maxItems]);

  /**
   * 加载推荐数据
   */
  const loadRecommendations = () => {
    setLoading(true);
    
    // 获取推荐教程项
    const items = tutorialRecommendationService.getRecommendedTutorialItems(maxItems);
    setRecommendedItems(items);
    
    // 获取推荐教程系列
    if (showSeries) {
      const series = tutorialRecommendationService.getRecommendedTutorialSeries(3);
      setRecommendedSeries(series);
    }
    
    // 获取最近查看的教程项
    const recent = tutorialRecommendationService.getRecentlyViewedItems(maxItems);
    setRecentItems(recent);
    
    setLoading(false);
  };

  /**
   * 处理教程项点击
   */
  const handleTutorialItemClick = (item: TutorialItem) => {
    if (onTutorialSelect) {
      onTutorialSelect(item);
      return;
    }

    // 根据教程类型打开相应的教程
    switch (item.type) {
      case TutorialType.INTERACTIVE:
        const tutorialId = item.id.split(':')[1];
        TutorialService.getInstance().startTutorial(tutorialId);
        break;
      case TutorialType.VIDEO:
        const videoId = item.id.split(':')[1];
        VideoTutorialService.getInstance().openTutorial(videoId);
        break;
      case TutorialType.EXAMPLE:
        const exampleId = item.id.split(':')[1];
        ExampleProjectService.getInstance().markExampleAsViewed(exampleId);
        window.open(`/examples/${exampleId}/`, '_blank');
        break;
    }
  };

  /**
   * 处理教程系列点击
   */
  const handleSeriesClick = (series: TutorialSeries) => {
    if (onSeriesSelect) {
      onSeriesSelect(series);
    }
  };

  /**
   * 获取教程类型图标
   */
  const getTutorialTypeIcon = (type: TutorialType) => {
    switch (type) {
      case TutorialType.INTERACTIVE:
        return <ReadOutlined />;
      case TutorialType.VIDEO:
        return <PlayCircleOutlined />;
      case TutorialType.EXAMPLE:
        return <CodeOutlined />;
      default:
        return <BookOutlined />;
    }
  };

  /**
   * 获取教程类型文本
   */
  const getTutorialTypeText = (type: TutorialType) => {
    switch (type) {
      case TutorialType.INTERACTIVE:
        return t('tutorials.types.interactive');
      case TutorialType.VIDEO:
        return t('tutorials.types.video');
      case TutorialType.EXAMPLE:
        return t('tutorials.types.example');
      default:
        return t('tutorials.types.unknown');
    }
  };

  /**
   * 获取难度标签颜色
   */
  const getDifficultyColor = (difficulty: TutorialDifficulty) => {
    switch (difficulty) {
      case TutorialDifficulty.BEGINNER:
        return 'green';
      case TutorialDifficulty.INTERMEDIATE:
        return 'blue';
      case TutorialDifficulty.ADVANCED:
        return 'orange';
      case TutorialDifficulty.EXPERT:
        return 'red';
      default:
        return 'default';
    }
  };

  /**
   * 获取难度文本
   */
  const getDifficultyText = (difficulty: TutorialDifficulty) => {
    switch (difficulty) {
      case TutorialDifficulty.BEGINNER:
        return t('tutorials.difficulty.beginner');
      case TutorialDifficulty.INTERMEDIATE:
        return t('tutorials.difficulty.intermediate');
      case TutorialDifficulty.ADVANCED:
        return t('tutorials.difficulty.advanced');
      case TutorialDifficulty.EXPERT:
        return t('tutorials.difficulty.expert');
      default:
        return t('tutorials.difficulty.unknown');
    }
  };

  /**
   * 渲染教程项卡片
   */
  const renderTutorialItem = (item: TutorialItem) => {
    return (
      <Card 
        className="tutorial-item-card"
        hoverable
        onClick={() => handleTutorialItemClick(item)}
        cover={
          item.thumbnailUrl ? (
            <div className="tutorial-item-cover">
              <img alt={item.title} src={item.thumbnailUrl} />
              {item.completed && (
                <div className="tutorial-completed-badge">
                  <TrophyOutlined />
                </div>
              )}
            </div>
          ) : null
        }
      >
        <Card.Meta
          title={
            <div className="tutorial-item-title">
              <span>{item.title}</span>
              {item.popularity && item.popularity > 80 && (
                <Tooltip title={t('tutorials.popular')}>
                  <FireOutlined className="popular-icon" />
                </Tooltip>
              )}
            </div>
          }
          description={
            <div className="tutorial-item-description">
              <Paragraph ellipsis={{ rows: 2 }}>{item.description}</Paragraph>
              <div className="tutorial-item-meta">
                <Tooltip title={getTutorialTypeText(item.type)}>
                  <Tag icon={getTutorialTypeIcon(item.type)}>
                    {getTutorialTypeText(item.type)}
                  </Tag>
                </Tooltip>
                
                {showDifficulty && (
                  <Tooltip title={t('tutorials.difficulty.title')}>
                    <Tag color={getDifficultyColor(item.difficulty)}>
                      {getDifficultyText(item.difficulty)}
                    </Tag>
                  </Tooltip>
                )}
                
                {item.duration && (
                  <Tooltip title={t('tutorials.duration')}>
                    <Tag icon={<ClockCircleOutlined />}>
                      {item.duration} {t('tutorials.minutes')}
                    </Tag>
                  </Tooltip>
                )}
              </div>
              
              {showProgress && item.progress !== undefined && item.progress > 0 && !item.completed && (
                <div className="tutorial-progress">
                  <div className="progress-bar">
                    <div 
                      className="progress-fill" 
                      style={{ width: `${item.progress}%` }}
                    />
                  </div>
                  <Text type="secondary">{item.progress}%</Text>
                </div>
              )}
              
              {showTags && item.tags && item.tags.length > 0 && (
                <div className="tutorial-tags">
                  <TagOutlined />
                  {item.tags.slice(0, 3).map((tag, index) => (
                    <Tag key={index} className="tutorial-tag">{tag}</Tag>
                  ))}
                  {item.tags.length > 3 && (
                    <Tooltip title={item.tags.slice(3).join(', ')}>
                      <Tag>+{item.tags.length - 3}</Tag>
                    </Tooltip>
                  )}
                </div>
              )}
            </div>
          }
        />
      </Card>
    );
  };

  /**
   * 渲染教程系列卡片
   */
  const renderSeriesCard = (series: TutorialSeries) => {
    return (
      <Card 
        className="tutorial-series-card"
        hoverable
        onClick={() => handleSeriesClick(series)}
        cover={
          series.thumbnailUrl ? (
            <div className="tutorial-series-cover">
              <img alt={series.title} src={series.thumbnailUrl} />
              {series.featured && (
                <div className="series-featured-badge">
                  <StarOutlined />
                </div>
              )}
            </div>
          ) : null
        }
      >
        <Card.Meta
          title={
            <div className="tutorial-series-title">
              <span>{series.title}</span>
              {series.popularity && series.popularity > 80 && (
                <Tooltip title={t('tutorials.popular')}>
                  <FireOutlined className="popular-icon" />
                </Tooltip>
              )}
            </div>
          }
          description={
            <div className="tutorial-series-description">
              <Paragraph ellipsis={{ rows: 2 }}>{series.description}</Paragraph>
              
              <div className="tutorial-series-meta">
                <Tooltip title={t('tutorials.seriesCount', { count: series.tutorials.length })}>
                  <Tag icon={<BookOutlined />}>
                    {series.tutorials.length} {t('tutorials.items')}
                  </Tag>
                </Tooltip>
                
                {showDifficulty && (
                  <Tooltip title={t('tutorials.difficulty.title')}>
                    <Tag color={getDifficultyColor(series.difficulty)}>
                      {getDifficultyText(series.difficulty)}
                    </Tag>
                  </Tooltip>
                )}
              </div>
              
              {showProgress && series.progress !== undefined && (
                <div className="tutorial-progress">
                  <div className="progress-bar">
                    <div 
                      className="progress-fill" 
                      style={{ width: `${series.progress}%` }}
                    />
                  </div>
                  <Text type="secondary">{series.progress}%</Text>
                </div>
              )}
              
              {showTags && series.tags && series.tags.length > 0 && (
                <div className="tutorial-tags">
                  <TagOutlined />
                  {series.tags.slice(0, 3).map((tag, index) => (
                    <Tag key={index} className="tutorial-tag">{tag}</Tag>
                  ))}
                  {series.tags.length > 3 && (
                    <Tooltip title={series.tags.slice(3).join(', ')}>
                      <Tag>+{series.tags.length - 3}</Tag>
                    </Tooltip>
                  )}
                </div>
              )}
              
              <Button 
                type="link" 
                className="view-series-button"
                onClick={(e) => {
                  e.stopPropagation();
                  handleSeriesClick(series);
                }}
              >
                {t('tutorials.viewSeries')} <RightOutlined />
              </Button>
            </div>
          }
        />
      </Card>
    );
  };

  return (
    <div className="tutorial-recommendations">
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane 
          tab={
            <Badge count={recommendedItems.length} style={{ backgroundColor: '#1890ff' }}>
              {t('tutorials.recommended')}
            </Badge>
          } 
          key="recommended"
        >
          {loading ? (
            <div className="tutorial-skeleton">
              <Skeleton active avatar paragraph={{ rows: 3 }} />
              <Skeleton active avatar paragraph={{ rows: 3 }} />
            </div>
          ) : (
            <>
              {showSeries && recommendedSeries.length > 0 && (
                <div className="recommended-series">
                  <Title level={4}>{t('tutorials.recommendedSeries')}</Title>
                  <Carousel autoplay dots={{ className: 'carousel-dots' }}>
                    {recommendedSeries.map((series) => (
                      <div key={series.id} className="series-carousel-item">
                        {renderSeriesCard(series)}
                      </div>
                    ))}
                  </Carousel>
                </div>
              )}
              
              <div className="recommended-tutorials">
                <Title level={4}>{t('tutorials.recommendedTutorials')}</Title>
                {recommendedItems.length > 0 ? (
                  <List
                    grid={{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 3, xl: 3, xxl: 4 }}
                    dataSource={recommendedItems}
                    renderItem={item => (
                      <List.Item>
                        {renderTutorialItem(item)}
                      </List.Item>
                    )}
                  />
                ) : (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={t('tutorials.noRecommendations')}
                  />
                )}
              </div>
            </>
          )}
        </TabPane>
        
        <TabPane 
          tab={
            <Badge count={recentItems.length} style={{ backgroundColor: '#52c41a' }}>
              {t('tutorials.recent')}
            </Badge>
          } 
          key="recent"
        >
          {loading ? (
            <div className="tutorial-skeleton">
              <Skeleton active avatar paragraph={{ rows: 3 }} />
              <Skeleton active avatar paragraph={{ rows: 3 }} />
            </div>
          ) : (
            <div className="recent-tutorials">
              {recentItems.length > 0 ? (
                <List
                  grid={{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 3, xl: 3, xxl: 4 }}
                  dataSource={recentItems}
                  renderItem={item => (
                    <List.Item>
                      {renderTutorialItem(item)}
                    </List.Item>
                  )}
                />
              ) : (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={t('tutorials.noRecentTutorials')}
                />
              )}
            </div>
          )}
        </TabPane>
      </Tabs>
    </div>
  );
};
