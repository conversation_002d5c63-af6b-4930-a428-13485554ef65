/**
 * 后处理示例
 * 展示如何使用后处理系统创建各种效果
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { Scene } from '../../src/scene/Scene';
import { 
  PostProcessingSystem,
  FXAAEffect,
  BloomEffect,
  DepthOfFieldEffect,
  SSAOEffect
} from '../../src/rendering/postprocessing';
import { SSAOPass } from 'three/examples/jsm/postprocessing/SSAOPass';

/**
 * 后处理示例
 */
export class PostProcessingExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 后处理系统 */
  private postProcessingSystem: PostProcessingSystem;
  
  /** Three.js场景 */
  private threeScene: THREE.Scene;
  
  /** Three.js相机 */
  private camera: THREE.PerspectiveCamera;
  
  /** Three.js渲染器 */
  private renderer: THREE.WebGLRenderer;
  
  /** 是否已初始化 */
  private initialized: boolean = false;
  
  /** 旋转对象 */
  private rotatingObjects: THREE.Object3D[] = [];

  /**
   * 创建后处理示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建场景
    this.scene = new Scene('后处理示例场景');
    
    // 创建Three.js场景
    this.threeScene = new THREE.Scene();
    this.threeScene.background = new THREE.Color(0x000000);
    
    // 创建相机
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    this.camera.position.set(0, 0, 5);
    
    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: false });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    
    // 创建后处理系统
    this.postProcessingSystem = new PostProcessingSystem({
      enabled: true,
      autoResize: true,
      renderTargetOptions: {
        samples: 4,
        depthTexture: true
      }
    });
    
    // 添加后处理系统到引擎
    this.engine.addSystem(this.postProcessingSystem);
    
    // 添加窗口大小变化事件监听器
    window.addEventListener('resize', this.handleResize.bind(this));
  }

  /**
   * 初始化示例
   */
  public initialize(): void {
    if (this.initialized) return;
    
    // 创建场景内容
    this.createSceneContent();
    
    // 初始化后处理系统
    this.postProcessingSystem.initialize(this.renderer, this.threeScene, this.camera);
    
    // 添加后处理效果
    this.addPostProcessingEffects();
    
    // 将渲染器添加到DOM
    document.body.appendChild(this.renderer.domElement);
    
    this.initialized = true;
  }

  /**
   * 创建场景内容
   */
  private createSceneContent(): void {
    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0x404040);
    this.threeScene.add(ambientLight);
    
    // 添加平行光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(1, 1, 1);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 50;
    directionalLight.shadow.camera.left = -10;
    directionalLight.shadow.camera.right = 10;
    directionalLight.shadow.camera.top = 10;
    directionalLight.shadow.camera.bottom = -10;
    this.threeScene.add(directionalLight);
    
    // 添加点光源
    const pointLight = new THREE.PointLight(0xff0000, 1, 10);
    pointLight.position.set(0, 2, 0);
    this.threeScene.add(pointLight);
    
    // 创建地面
    const groundGeometry = new THREE.PlaneGeometry(20, 20);
    const groundMaterial = new THREE.MeshStandardMaterial({
      color: 0x808080,
      roughness: 0.8,
      metalness: 0.2
    });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.position.y = -2;
    ground.receiveShadow = true;
    this.threeScene.add(ground);
    
    // 创建立方体
    const cubeGeometry = new THREE.BoxGeometry(1, 1, 1);
    const cubeMaterial = new THREE.MeshStandardMaterial({
      color: 0x00ff00,
      roughness: 0.5,
      metalness: 0.5
    });
    const cube = new THREE.Mesh(cubeGeometry, cubeMaterial);
    cube.position.set(-1.5, 0, 0);
    cube.castShadow = true;
    cube.receiveShadow = true;
    this.threeScene.add(cube);
    this.rotatingObjects.push(cube);
    
    // 创建球体
    const sphereGeometry = new THREE.SphereGeometry(0.5, 32, 32);
    const sphereMaterial = new THREE.MeshStandardMaterial({
      color: 0xff0000,
      roughness: 0.2,
      metalness: 0.8,
      emissive: 0x330000
    });
    const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
    sphere.position.set(1.5, 0, 0);
    sphere.castShadow = true;
    sphere.receiveShadow = true;
    this.threeScene.add(sphere);
    this.rotatingObjects.push(sphere);
    
    // 创建圆环
    const torusGeometry = new THREE.TorusGeometry(0.5, 0.2, 16, 100);
    const torusMaterial = new THREE.MeshStandardMaterial({
      color: 0x0000ff,
      roughness: 0.5,
      metalness: 0.5
    });
    const torus = new THREE.Mesh(torusGeometry, torusMaterial);
    torus.position.set(0, 0, -1.5);
    torus.castShadow = true;
    torus.receiveShadow = true;
    this.threeScene.add(torus);
    this.rotatingObjects.push(torus);
    
    // 创建发光球体
    const glowingSphereGeometry = new THREE.SphereGeometry(0.3, 32, 32);
    const glowingSphereMaterial = new THREE.MeshStandardMaterial({
      color: 0xffff00,
      roughness: 0.2,
      metalness: 0.8,
      emissive: 0xffff00,
      emissiveIntensity: 2
    });
    const glowingSphere = new THREE.Mesh(glowingSphereGeometry, glowingSphereMaterial);
    glowingSphere.position.set(0, 1.5, 0);
    glowingSphere.castShadow = true;
    glowingSphere.receiveShadow = true;
    this.threeScene.add(glowingSphere);
    this.rotatingObjects.push(glowingSphere);
  }

  /**
   * 添加后处理效果
   */
  private addPostProcessingEffects(): void {
    // 添加FXAA抗锯齿效果
    const fxaaEffect = new FXAAEffect({
      name: 'FXAA',
      enabled: true,
      edgeThreshold: 0.1,
      edgeThresholdMin: 0.05
    });
    this.postProcessingSystem.addEffect(fxaaEffect);
    
    // 添加泛光效果
    const bloomEffect = new BloomEffect({
      name: 'Bloom',
      enabled: true,
      strength: 1.5,
      radius: 0.5,
      threshold: 0.6
    });
    this.postProcessingSystem.addEffect(bloomEffect);
    
    // 添加景深效果
    const depthOfFieldEffect = new DepthOfFieldEffect({
      name: 'DepthOfField',
      enabled: false,
      focus: 5.0,
      aperture: 0.025,
      maxBlur: 0.01
    });
    depthOfFieldEffect.setSceneAndCamera(this.threeScene, this.camera);
    this.postProcessingSystem.addEffect(depthOfFieldEffect);
    
    // 添加SSAO效果
    const ssaoEffect = new SSAOEffect({
      name: 'SSAO',
      enabled: false,
      output: SSAOPass.OUTPUT.Default,
      kernelRadius: 16,
      minDistance: 0.005,
      maxDistance: 0.1,
      aoClamp: 0.25,
      lumInfluence: 0.7
    });
    ssaoEffect.setSceneAndCamera(this.threeScene, this.camera);
    this.postProcessingSystem.addEffect(ssaoEffect);
  }

  /**
   * 处理窗口大小变化
   */
  private handleResize(): void {
    // 更新相机宽高比
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    
    // 更新渲染器大小
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 更新示例
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized) return;
    
    // 旋转对象
    for (const object of this.rotatingObjects) {
      object.rotation.x += deltaTime * 0.5;
      object.rotation.y += deltaTime * 0.2;
    }
    
    // 更新引擎
    this.engine.update(deltaTime);
  }

  /**
   * 渲染示例
   */
  public render(): void {
    if (!this.initialized) return;
    
    // 使用后处理系统渲染
    // 注意：后处理系统会在update方法中调用渲染器的render方法
  }

  /**
   * 启用/禁用效果
   * @param name 效果名称
   * @param enabled 是否启用
   */
  public setEffectEnabled(name: string, enabled: boolean): void {
    if (enabled) {
      this.postProcessingSystem.enableEffect(name);
    } else {
      this.postProcessingSystem.disableEffect(name);
    }
  }

  /**
   * 获取引擎
   * @returns 引擎实例
   */
  public getEngine(): Engine {
    return this.engine;
  }

  /**
   * 获取场景
   * @returns 场景实例
   */
  public getScene(): Scene {
    return this.scene;
  }

  /**
   * 获取后处理系统
   * @returns 后处理系统
   */
  public getPostProcessingSystem(): PostProcessingSystem {
    return this.postProcessingSystem;
  }

  /**
   * 销毁示例
   */
  public dispose(): void {
    // 移除窗口大小变化事件监听器
    window.removeEventListener('resize', this.handleResize.bind(this));
    
    // 移除渲染器
    if (this.renderer.domElement.parentNode) {
      this.renderer.domElement.parentNode.removeChild(this.renderer.domElement);
    }
    
    // 销毁引擎
    this.engine.dispose();
  }
}
