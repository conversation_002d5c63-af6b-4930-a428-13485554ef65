"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.RenderSystem = void 0;
/**
 * 渲染系统
 * 负责管理渲染过程和渲染相关组件
 */
var System_1 = require("../core/System");
var ShadowSystem_1 = require("./shadows/ShadowSystem");
var PostProcessingSystem_1 = require("./postprocessing/PostProcessingSystem");
/**
 * 渲染系统
 */
var RenderSystem = exports.RenderSystem = /** @class */ (function (_super) {
    __extends(RenderSystem, _super);
    /**
     * 创建渲染系统
     * @param renderer 渲染器
     * @param options 渲染系统选项
     */
    function RenderSystem(renderer, options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, -100) || this;
        /** 活跃相机 */
        _this.activeCamera = null;
        /** 活跃场景 */
        _this.activeScene = null;
        /** 是否自动清除 */
        _this.autoClear = true;
        /** 是否启用后处理 */
        _this.postProcessingEnabled = false;
        /** 阴影系统 */
        _this.shadowSystem = null;
        /** 后处理系统 */
        _this.postProcessingSystem = null;
        _this.renderer = renderer;
        // 创建阴影系统
        if (options.enableShadows !== false) {
            _this.shadowSystem = new ShadowSystem_1.ShadowSystem({
                enabled: true,
                useCSM: true
            });
            _this.shadowSystem.setRenderer(renderer.getThreeRenderer());
        }
        // 创建后处理系统
        if (options.enablePostProcessing !== false) {
            _this.postProcessingSystem = new PostProcessingSystem_1.PostProcessingSystem({
                enabled: true,
                autoResize: true
            });
        }
        return _this;
    }
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    RenderSystem.prototype.getType = function () {
        return RenderSystem.TYPE;
    };
    /**
     * 初始化系统
     */
    RenderSystem.prototype.initialize = function () {
        _super.prototype.initialize.call(this);
        // 监听窗口大小变化
        window.addEventListener('resize', this.handleResize.bind(this));
        // 初始化时调整大小
        this.handleResize();
    };
    /**
     * 处理窗口大小变化
     */
    RenderSystem.prototype.handleResize = function () {
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    };
    /**
     * 设置活跃相机
     * @param camera 相机
     */
    RenderSystem.prototype.setActiveCamera = function (camera) {
        this.activeCamera = camera;
        // 更新阴影系统的相机
        if (this.shadowSystem) {
            this.shadowSystem.setActiveCamera(camera);
        }
        // 更新后处理系统的相机
        if (this.postProcessingSystem && this.postProcessingSystem.isInitialized() && this.activeScene) {
            this.postProcessingSystem.setup(this.renderer.getThreeRenderer(), this.activeScene.getThreeScene(), camera.getThreeCamera());
        }
    };
    /**
     * 获取活跃相机
     * @returns 活跃相机
     */
    RenderSystem.prototype.getActiveCamera = function () {
        return this.activeCamera;
    };
    /**
     * 设置活跃场景
     * @param scene 场景
     */
    RenderSystem.prototype.setActiveScene = function (scene) {
        this.activeScene = scene;
        // 更新阴影系统的场景
        if (this.shadowSystem) {
            this.shadowSystem.setActiveScene(scene);
        }
        // 更新后处理系统的场景
        if (this.postProcessingSystem && this.postProcessingSystem.isInitialized() && this.activeCamera) {
            this.postProcessingSystem.setup(this.renderer.getThreeRenderer(), scene.getThreeScene(), this.activeCamera.getThreeCamera());
        }
    };
    /**
     * 获取活跃场景
     * @returns 活跃场景
     */
    RenderSystem.prototype.getActiveScene = function () {
        return this.activeScene;
    };
    /**
     * 设置是否自动清除
     * @param autoClear 是否自动清除
     */
    RenderSystem.prototype.setAutoClear = function (autoClear) {
        this.autoClear = autoClear;
    };
    /**
     * 是否自动清除
     * @returns 是否自动清除
     */
    RenderSystem.prototype.isAutoClear = function () {
        return this.autoClear;
    };
    /**
     * 设置是否启用后处理
     * @param enabled 是否启用
     */
    RenderSystem.prototype.setPostProcessingEnabled = function (enabled) {
        this.postProcessingEnabled = enabled;
        // 更新后处理系统状态
        if (this.postProcessingSystem) {
            this.postProcessingSystem.setEnabled(enabled);
        }
    };
    /**
     * 是否启用后处理
     * @returns 是否启用后处理
     */
    RenderSystem.prototype.isPostProcessingEnabled = function () {
        return this.postProcessingEnabled;
    };
    /**
     * 获取阴影系统
     * @returns 阴影系统
     */
    RenderSystem.prototype.getShadowSystem = function () {
        return this.shadowSystem;
    };
    /**
     * 获取后处理系统
     * @returns 后处理系统
     */
    RenderSystem.prototype.getPostProcessingSystem = function () {
        return this.postProcessingSystem;
    };
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    RenderSystem.prototype.update = function (deltaTime) {
        // 如果没有活跃相机或场景，则不渲染
        if (!this.activeCamera || !this.activeScene) {
            return;
        }
        // 更新阴影系统
        if (this.shadowSystem) {
            this.shadowSystem.update(deltaTime);
        }
        // 如果启用了后处理且后处理系统已初始化
        if (this.postProcessingEnabled && this.postProcessingSystem && this.postProcessingSystem.isInitialized()) {
            // 使用后处理系统渲染
            this.postProcessingSystem.update(deltaTime);
        }
        else {
            // 直接渲染场景
            this.renderer.render(this.activeScene, this.activeCamera);
        }
        // 发出渲染事件
        this.emit('render', this.activeScene, this.activeCamera);
    };
    /**
     * 销毁系统
     */
    RenderSystem.prototype.dispose = function () {
        // 移除窗口大小变化监听
        window.removeEventListener('resize', this.handleResize.bind(this));
        // 销毁阴影系统
        if (this.shadowSystem) {
            this.shadowSystem.dispose();
            this.shadowSystem = null;
        }
        // 销毁后处理系统
        if (this.postProcessingSystem) {
            this.postProcessingSystem.dispose();
            this.postProcessingSystem = null;
        }
        // 调用父类销毁方法
        _super.prototype.dispose.call(this);
    };
    /** 系统类型 */
    RenderSystem.TYPE = 'RenderSystem';
    return RenderSystem;
}(System_1.System));
