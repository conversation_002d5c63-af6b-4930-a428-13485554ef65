<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <style>
    .box {
      fill: #f0f0f0;
      stroke: #333;
      stroke-width: 2;
      rx: 10;
      ry: 10;
    }
    .box-init { fill: #e6f7ff; }
    .box-start { fill: #e6ffe6; }
    .box-update { fill: #fff0e6; }
    .box-destroy { fill: #ffe6e6; }
    .arrow {
      stroke: #666;
      stroke-width: 2;
      marker-end: url(#arrowhead);
    }
    .label {
      font-family: 'Arial', sans-serif;
      font-size: 14px;
      text-anchor: middle;
    }
    .title {
      font-family: 'Arial', sans-serif;
      font-size: 24px;
      font-weight: bold;
      text-anchor: middle;
    }
    .subtitle {
      font-family: 'Arial', sans-serif;
      font-size: 16px;
      text-anchor: middle;
    }
  </style>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="400" y="40" class="title">脚本生命周期</text>
  <text x="400" y="70" class="subtitle">DL（Digital Learning）引擎脚本组件的执行顺序和调用时机</text>
  
  <!-- Initialization Phase -->
  <rect x="100" y="120" width="600" height="100" fill="#f8f8f8" stroke="#ccc" stroke-width="1" rx="5" ry="5" />
  <text x="400" y="140" class="subtitle">初始化阶段</text>
  
  <rect x="150" y="160" width="120" height="40" class="box box-init" />
  <text x="210" y="185" class="label">constructor</text>
  
  <rect x="350" y="160" width="120" height="40" class="box box-init" />
  <text x="410" y="185" class="label">onInit</text>
  
  <rect x="550" y="160" width="120" height="40" class="box box-init" />
  <text x="610" y="185" class="label">onStart</text>
  
  <line x1="270" y1="180" x2="350" y2="180" class="arrow" />
  <line x1="470" y1="180" x2="550" y2="180" class="arrow" />
  
  <!-- Update Phase -->
  <rect x="100" y="250" width="600" height="150" fill="#f8f8f8" stroke="#ccc" stroke-width="1" rx="5" ry="5" />
  <text x="400" y="270" class="subtitle">更新阶段（每帧）</text>
  
  <rect x="150" y="300" width="120" height="40" class="box box-update" />
  <text x="210" y="325" class="label">onFixedUpdate</text>
  
  <rect x="350" y="300" width="120" height="40" class="box box-update" />
  <text x="410" y="325" class="label">onUpdate</text>
  
  <rect x="550" y="300" width="120" height="40" class="box box-update" />
  <text x="610" y="325" class="label">onLateUpdate</text>
  
  <line x1="270" y1="320" x2="350" y2="320" class="arrow" />
  <line x1="470" y1="320" x2="550" y2="320" class="arrow" />
  
  <path d="M 610 340 L 610 360 L 210 360 L 210 340" fill="none" class="arrow" />
  <text x="400" y="380" class="label">循环执行</text>
  
  <!-- Event Phase -->
  <rect x="100" y="430" width="600" height="60" fill="#f8f8f8" stroke="#ccc" stroke-width="1" rx="5" ry="5" />
  <text x="400" y="450" class="subtitle">事件响应（按需）</text>
  
  <rect x="130" y="470" width="100" height="30" class="box" />
  <text x="180" y="490" class="label">onCollision</text>
  
  <rect x="250" y="470" width="100" height="30" class="box" />
  <text x="300" y="490" class="label">onTrigger</text>
  
  <rect x="370" y="470" width="100" height="30" class="box" />
  <text x="420" y="490" class="label">onEnable</text>
  
  <rect x="490" y="470" width="100" height="30" class="box" />
  <text x="540" y="490" class="label">onDisable</text>
  
  <!-- Destruction Phase -->
  <rect x="100" y="520" width="600" height="60" fill="#f8f8f8" stroke="#ccc" stroke-width="1" rx="5" ry="5" />
  <text x="400" y="540" class="subtitle">销毁阶段</text>
  
  <rect x="340" y="560" width="120" height="40" class="box box-destroy" />
  <text x="400" y="585" class="label">onDestroy</text>
  
  <!-- Connections between phases -->
  <line x1="610" y1="200" x2="610" y2="250" stroke="#666" stroke-width="2" stroke-dasharray="5,5" />
  <line x1="610" y1="380" x2="610" y2="430" stroke="#666" stroke-width="2" stroke-dasharray="5,5" />
  <line x1="400" y1="490" x2="400" y2="520" stroke="#666" stroke-width="2" stroke-dasharray="5,5" />
</svg>
