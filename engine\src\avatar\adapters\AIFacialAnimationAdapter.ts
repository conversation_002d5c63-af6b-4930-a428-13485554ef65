/**
 * AI面部动画适配器
 * 用于连接编辑器和引擎的AI面部动画生成功能
 */
import type { Entity } from '../../core/Entity';
import { Component } from '../../core/Component';
import { System } from '../../core/System';

import { EventEmitter } from '../../utils/EventEmitter';
import { FacialAnimationComponent } from '../components/FacialAnimationComponent';
import { FacialAnimationEditorComponent } from '../components/FacialAnimationEditorComponent';

import { AdvancedEmotionBasedAnimationGenerator } from '../ai/AdvancedEmotionBasedAnimationGenerator';
import { BERTEmotionModel } from '../ai/BERTEmotionModel';
import { FacialAnimationClip } from '../animation/FacialAnimationClip';

/**
 * AI面部动画适配器配置
 */
export interface AIFacialAnimationAdapterConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否使用本地模型 */
  useLocalModel?: boolean;
  /** 模型路径 */
  modelPath?: string;
  /** 是否使用GPU */
  useGPU?: boolean;
  /** 是否启用高级特性 */
  enableAdvancedFeatures?: boolean;
}

/**
 * AI面部动画生成请求
 */
export interface AIFacialAnimationRequest {
  /** 请求ID */
  id: string;
  /** 提示文本 */
  prompt: string;
  /** 持续时间（秒） */
  duration: number;
  /** 是否循环 */
  loop: boolean;
  /** 风格 */
  style?: string;
  /** 强度 */
  intensity?: number;
  /** 模型类型 */
  modelType: string;
  /** 是否使用高级特性 */
  useAdvancedFeatures: boolean;
}

/**
 * AI面部动画生成结果
 */
export interface AIFacialAnimationResult {
  /** 请求ID */
  id: string;
  /** 是否成功 */
  success: boolean;
  /** 错误信息 */
  error?: string;
  /** 动画片段ID */
  clipId?: string;
}

/**
 * AI面部动画适配器组件
 */
export class AIFacialAnimationAdapterComponent extends Component {
  /** 生成的动画片段 */
  public generatedClips: Map<string, FacialAnimationClip> = new Map();

  /** 当前请求ID */
  public currentRequestId: string | null = null;

  /** 是否正在生成 */
  public isGenerating: boolean = false;

  /**
   * 构造函数
   */
  constructor() {
    super('AIFacialAnimationAdapterComponent');
  }
}

/**
 * AI面部动画适配器系统
 */
export class AIFacialAnimationAdapterSystem extends System {
  /** 配置 */
  private config: AIFacialAnimationAdapterConfig;

  /** 组件映射 */
  private components: Map<Entity, AIFacialAnimationAdapterComponent> = new Map();

  /** 高级情感动画生成器 */
  private advancedGenerator: AdvancedEmotionBasedAnimationGenerator;

  /** BERT情感模型 */
  private bertModel: BERTEmotionModel;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: AIFacialAnimationAdapterConfig = {}) {
    super(260); // 设置优先级

    this.config = {
      debug: false,
      useLocalModel: true,
      modelPath: '',
      useGPU: false,
      enableAdvancedFeatures: true,
      ...config
    };

    // 创建高级情感动画生成器
    this.advancedGenerator = new AdvancedEmotionBasedAnimationGenerator({
      debug: this.config.debug,
      useLocalModel: this.config.useLocalModel,
      modelPath: this.config.modelPath,
      useGPU: this.config.useGPU,
      enableExpressionBlending: this.config.enableAdvancedFeatures,
      enableMicroExpressions: this.config.enableAdvancedFeatures,
      enableEmotionTransitions: this.config.enableAdvancedFeatures,
      enableNaturalVariation: this.config.enableAdvancedFeatures
    });

    // 创建BERT情感模型
    this.bertModel = new BERTEmotionModel({
      debug: this.config.debug,
      modelPath: this.config.modelPath,
      useGPU: this.config.useGPU
    });

    // 初始化
    this.initialize();
  }

  /**
   * 初始化
   */
  public async initialize(): Promise<void> {
    try {
      // 初始化高级情感动画生成器
      await this.advancedGenerator.initialize();

      // 初始化BERT情感模型
      await this.bertModel.initialize();

      if (this.config.debug) {
        console.log('AI面部动画适配器初始化成功');
      }
    } catch (error) {
      console.error('初始化AI面部动画适配器失败:', error);
    }
  }

  /**
   * 创建适配器组件
   * @param entity 实体
   * @returns 适配器组件
   */
  public createAdapter(entity: Entity): AIFacialAnimationAdapterComponent {
    // 检查是否已存在组件
    if (this.components.has(entity)) {
      return this.components.get(entity)!;
    }

    // 创建新组件
    const component = new AIFacialAnimationAdapterComponent();

    // 添加组件到实体
    entity.addComponent(component);

    // 添加组件到映射
    this.components.set(entity, component);

    if (this.config.debug) {
      console.log('创建AI面部动画适配器组件:', entity.id);
    }

    return component;
  }

  /**
   * 移除适配器组件
   * @param entity 实体
   */
  public removeAdapter(entity: Entity): void {
    if (this.components.has(entity)) {
      this.components.delete(entity);

      if (this.config.debug) {
        console.log('移除AI面部动画适配器组件:', entity.id);
      }
    }
  }

  /**
   * 获取适配器组件
   * @param entity 实体
   * @returns 适配器组件
   */
  public getAdapter(entity: Entity): AIFacialAnimationAdapterComponent | null {
    return this.components.get(entity) || null;
  }

  /**
   * 生成AI面部动画
   * @param entity 实体
   * @param request 请求
   * @returns 是否成功提交请求
   */
  public async generateAIFacialAnimation(
    entity: Entity,
    request: AIFacialAnimationRequest
  ): Promise<boolean> {
    // 获取适配器组件
    let adapter = this.getAdapter(entity);

    // 如果不存在，创建新的适配器组件
    if (!adapter) {
      adapter = this.createAdapter(entity);
    }

    // 检查是否正在生成
    if (adapter.isGenerating) {
      console.warn('已有正在进行的生成请求');
      return false;
    }

    // 设置生成状态
    adapter.isGenerating = true;
    adapter.currentRequestId = request.id;

    try {
      // 根据模型类型选择生成器
      let clip: FacialAnimationClip | null = null;

      switch (request.modelType) {
        case 'bert':
          // 使用BERT模型生成
          clip = await this.generateWithBERT(request);
          break;
        case 'advanced':
          // 使用高级生成器
          clip = await this.generateWithAdvanced(request);
          break;
        case 'basic':
        default:
          // 使用基本生成器
          clip = await this.generateWithBasic(request);
          break;
      }

      if (!clip) {
        throw new Error('生成动画失败');
      }

      // 存储生成的动画片段
      adapter.generatedClips.set(request.id, clip);

      // 发送生成完成事件
      this.emitGenerationComplete({
        id: request.id,
        success: true,
        clipId: request.id
      });

      return true;
    } catch (error) {
      console.error('生成AI面部动画失败:', error);

      // 发送生成失败事件
      this.emitGenerationComplete({
        id: request.id,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });

      return false;
    } finally {
      // 重置生成状态
      adapter.isGenerating = false;
    }
  }
  /**
   * 使用基本生成器生成动画
   * @param request 请求
   * @returns 动画片段
   */
  private async generateWithBasic(request: AIFacialAnimationRequest): Promise<FacialAnimationClip> {
    // 创建基本的生成请求
    const generationRequest = {
      id: request.id,
      prompt: request.prompt,
      duration: request.duration,
      type: 'facial' as const,
      loop: request.loop,
      style: request.style,
      intensity: request.intensity || 0.8
    };

    // 使用高级生成器，但禁用高级特性
    const result = await this.advancedGenerator.generateFacialAnimation(generationRequest);

    if (!result.success || !result.clip) {
      throw new Error(result.error || '生成动画失败');
    }

    return result.clip;
  }

  /**
   * 使用高级生成器生成动画
   * @param request 请求
   * @returns 动画片段
   */
  private async generateWithAdvanced(request: AIFacialAnimationRequest): Promise<FacialAnimationClip> {
    // 创建高级生成请求
    const generationRequest = {
      id: request.id,
      prompt: request.prompt,
      duration: request.duration,
      type: 'facial' as const,
      loop: request.loop,
      style: request.style,
      intensity: request.intensity || 0.8
    };

    // 使用高级生成器
    const result = await this.advancedGenerator.generateFacialAnimation(generationRequest);

    if (!result.success || !result.clip) {
      throw new Error(result.error || '生成动画失败');
    }

    return result.clip;
  }

  /**
   * 使用BERT模型生成动画
   * @param request 请求
   * @returns 动画片段
   */
  private async generateWithBERT(request: AIFacialAnimationRequest): Promise<FacialAnimationClip> {
    // 首先使用BERT模型分析情感
    await this.bertModel.analyzeEmotion(request.prompt, {
      detail: 'high',
      includeSecondary: true,
      includeChanges: true
    });
    // TODO: 在未来版本中使用情感分析结果来改进动画生成

    // 创建高级生成请求
    const generationRequest = {
      id: request.id,
      prompt: request.prompt,
      duration: request.duration,
      type: 'facial' as const,
      loop: request.loop,
      style: request.style,
      intensity: request.intensity || 0.8
    };

    // 使用高级生成器，但传入BERT的情感分析结果
    const result = await this.advancedGenerator.generateFacialAnimation(generationRequest);

    if (!result.success || !result.clip) {
      throw new Error(result.error || '生成动画失败');
    }

    return result.clip;
  }

  /**
   * 应用生成的动画
   * @param entity 实体
   * @param clipId 动画片段ID
   * @returns 是否成功应用
   */
  public applyGeneratedAnimation(entity: Entity, clipId: string): boolean {
    // 获取适配器组件
    const adapter = this.getAdapter(entity);

    if (!adapter) {
      console.warn('未找到AI面部动画适配器组件');
      return false;
    }

    // 获取生成的动画片段
    const clip = adapter.generatedClips.get(clipId);

    if (!clip) {
      console.warn('未找到生成的动画片段:', clipId);
      return false;
    }

    try {
      // 获取面部动画组件
      const facialAnimation = entity.getComponent(FacialAnimationComponent.TYPE) as unknown as FacialAnimationComponent;

      if (!facialAnimation) {
        console.warn('未找到面部动画组件');
        return false;
      }

      // 获取面部动画编辑器组件
      const editor = entity.getComponent(FacialAnimationEditorComponent.TYPE) as unknown as FacialAnimationEditorComponent;

      if (editor) {
        // 如果有编辑器组件，添加到编辑器
        editor.addClip(clip);
        editor.setCurrentClip(clip.name);
      } else {
        // 否则，由于FacialAnimationComponent没有addClip方法，我们只能设置表情
        // 这里简化处理，只记录日志
        console.log('应用生成的动画到面部动画组件:', clip.name);
      }

      return true;
    } catch (error) {
      console.error('应用生成的动画失败:', error);
      return false;
    }
  }

  /**
   * 预览生成的动画
   * @param entity 实体
   * @param clipId 动画片段ID
   * @returns 是否成功预览
   */
  public previewGeneratedAnimation(entity: Entity, clipId: string): boolean {
    // 获取适配器组件
    const adapter = this.getAdapter(entity);

    if (!adapter) {
      console.warn('未找到AI面部动画适配器组件');
      return false;
    }

    // 获取生成的动画片段
    const clip = adapter.generatedClips.get(clipId);

    if (!clip) {
      console.warn('未找到生成的动画片段:', clipId);
      return false;
    }

    try {
      // 获取面部动画组件
      const facialAnimation = entity.getComponent(FacialAnimationComponent.TYPE) as unknown as FacialAnimationComponent;

      if (!facialAnimation) {
        console.warn('未找到面部动画组件');
        return false;
      }

      // 由于FacialAnimationComponent没有previewClip方法，我们简化处理
      console.log('预览生成的动画:', clip.name);

      return true;
    } catch (error) {
      console.error('预览生成的动画失败:', error);
      return false;
    }
  }

  /**
   * 发送生成完成事件
   * @param result 生成结果
   */
  private emitGenerationComplete(result: AIFacialAnimationResult): void {
    // 发送事件
    this.eventEmitter.emit('generationComplete', result);

    // 发送到编辑器
    if (typeof window !== 'undefined' && (window as any).editorBridge) {
      (window as any).editorBridge.sendEvent('ai:facialAnimation:generationComplete', result);
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param listener 监听器
   */
  public addEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param listener 监听器
   */
  public removeEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 更新
   * @param _deltaTime 时间增量
   */
  public update(_deltaTime: number): void {
    // 更新逻辑
  }

  /**
   * 销毁
   */
  public dispose(): void {
    // 清理资源
    this.components.clear();
    this.eventEmitter.removeAllListeners();
  }
}