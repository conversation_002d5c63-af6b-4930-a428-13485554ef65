# 动画遮罩文档

## 概述

动画遮罩用于控制动画对骨骼的影响。通过遮罩，可以限制动画只影响特定的骨骼，从而实现更精细的动画控制。

## 基本遮罩类型

### INCLUDE（包含）

只影响列表中的骨骼。

```typescript
const headMask = new AnimationMask({
  name: 'headMask',
  type: MaskType.INCLUDE,
  bones: ['head', 'neck', 'face']
});
```

### EXCLUDE（排除）

影响除列表外的所有骨骼。

```typescript
const bodyMask = new AnimationMask({
  name: 'bodyMask',
  type: MaskType.EXCLUDE,
  bones: ['leftHand', 'rightHand']
});
```

### HIERARCHY（层级）

影响列表中的骨骼及其子骨骼。

```typescript
const armMask = new AnimationMask({
  name: 'armMask',
  type: MaskType.HIERARCHY,
  bones: ['leftArm', 'rightArm']
});
```

### INVERSE_HIERARCHY（反层级）

影响除列表中的骨骼及其子骨骼外的所有骨骼。

```typescript
const notArmMask = new AnimationMask({
  name: 'notArmMask',
  type: MaskType.INVERSE_HIERARCHY,
  bones: ['leftArm', 'rightArm']
});
```

## 高级遮罩类型

### GROUP（骨骼组）

影响预定义骨骼组中的骨骼。

```typescript
const headGroupMask = AnimationMask.createBoneGroupMask(BoneGroupType.HEAD);
const upperBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.UPPER_BODY);
const lowerBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.LOWER_BODY);
```

### BLEND（混合）

混合多个遮罩。

```typescript
const blendMask = AnimationMask.createBlendMask(
  [upperBodyMask, lowerBodyMask],
  [0.7, 0.3],
  true // 是否归一化权重
);
```

### TRANSITION（过渡）

在两个遮罩之间过渡。

```typescript
const transitionMask = AnimationMask.createTransitionMask(
  upperBodyMask,
  lowerBodyMask,
  0.5 // 过渡进度（0-1）
);

// 更新过渡进度
transitionMask.updateTransitionProgress(0.7);
```

## 权重类型

### BINARY（二进制）

骨骼要么完全受影响，要么完全不受影响。

```typescript
const binaryMask = new AnimationMask({
  name: 'binaryMask',
  type: MaskType.INCLUDE,
  weightType: MaskWeightType.BINARY,
  bones: ['head', 'neck', 'face']
});
```

### SMOOTH（平滑）

骨骼可以部分受影响。

```typescript
const smoothMask = new AnimationMask({
  name: 'smoothMask',
  type: MaskType.INCLUDE,
  weightType: MaskWeightType.SMOOTH,
  bones: ['head', 'neck', 'face']
});

// 设置平滑权重
smoothMask.setBoneWeight('head', 0.8);
smoothMask.setBoneWeight('neck', 0.6);
smoothMask.setBoneWeight('face', 0.4);
```

### DISTANCE（距离）

根据骨骼与根骨骼的距离计算权重。

```typescript
const distanceMask = new AnimationMask({
  name: 'distanceMask',
  type: MaskType.INCLUDE,
  weightType: MaskWeightType.DISTANCE,
  bones: ['spine']
});
```

### GRADIENT（渐变）

根据骨骼在层级中的位置计算权重。

```typescript
const gradientMask = new AnimationMask({
  name: 'gradientMask',
  type: MaskType.INCLUDE,
  weightType: MaskWeightType.GRADIENT,
  bones: ['spine', 'chest', 'neck', 'head']
});
```

### DYNAMIC（动态）

根据动画状态动态计算权重。

```typescript
const dynamicMask = new AnimationMask({
  name: 'dynamicMask',
  type: MaskType.INCLUDE,
  weightType: MaskWeightType.DYNAMIC,
  bones: ['leftLeg', 'rightLeg']
});
```

### CURVE（曲线）

使用曲线函数计算权重。

```typescript
const curveMask = new AnimationMask({
  name: 'curveMask',
  type: MaskType.INCLUDE,
  weightType: MaskWeightType.CURVE,
  bones: ['spine', 'chest', 'neck', 'head']
});

// 设置曲线函数
curveMask.setCurveFunction((t) => Math.sin(t * Math.PI / 2));
```

### BLEND（混合）

混合多个权重类型。

```typescript
const blendWeightMask = new AnimationMask({
  name: 'blendWeightMask',
  type: MaskType.INCLUDE,
  weightType: MaskWeightType.BLEND,
  bones: ['spine', 'chest', 'neck', 'head']
});
```

## 骨骼组

IR-Engine 提供了多种预定义的骨骼组，方便创建常用的遮罩。

```typescript
enum BoneGroupType {
  /** 头部 */
  HEAD = 'head',
  /** 躯干 */
  TORSO = 'torso',
  /** 左手臂 */
  LEFT_ARM = 'leftArm',
  /** 右手臂 */
  RIGHT_ARM = 'rightArm',
  /** 左腿 */
  LEFT_LEG = 'leftLeg',
  /** 右腿 */
  RIGHT_LEG = 'rightLeg',
  /** 左手 */
  LEFT_HAND = 'leftHand',
  /** 右手 */
  RIGHT_HAND = 'rightHand',
  /** 左脚 */
  LEFT_FOOT = 'leftFoot',
  /** 右脚 */
  RIGHT_FOOT = 'rightFoot',
  /** 上半身 */
  UPPER_BODY = 'upperBody',
  /** 下半身 */
  LOWER_BODY = 'lowerBody',
  /** 左侧 */
  LEFT_SIDE = 'leftSide',
  /** 右侧 */
  RIGHT_SIDE = 'rightSide',
  /** 自定义 */
  CUSTOM = 'custom'
}
```

## 使用遮罩

### 在混合层中使用遮罩

```typescript
// 创建上半身遮罩
const upperBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.UPPER_BODY);

// 创建下半身遮罩
const lowerBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.LOWER_BODY);

// 添加上半身动画层
const upperLayer: BlendLayer = {
  clipName: 'wave',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.OVERRIDE,
  mask: upperBodyMask.getBones()
};
blender.addLayer(upperLayer);

// 添加下半身动画层
const lowerLayer: BlendLayer = {
  clipName: 'walk',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.OVERRIDE,
  mask: lowerBodyMask.getBones()
};
blender.addLayer(lowerLayer);
```

### 动态更新遮罩

```typescript
// 创建过渡遮罩
const upperBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.UPPER_BODY);
const lowerBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.LOWER_BODY);
const transitionMask = AnimationMask.createTransitionMask(
  upperBodyMask,
  lowerBodyMask,
  0.0 // 初始过渡进度
);

// 添加动画层
const layer: BlendLayer = {
  clipName: 'walk',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.OVERRIDE,
  mask: transitionMask.getBones()
};
blender.addLayer(layer);

// 动态更新过渡进度
function updateTransition(progress: number) {
  transitionMask.updateTransitionProgress(progress);
  blender.update(0);
}

// 示例：从上半身到下半身的平滑过渡
let progress = 0;
const interval = setInterval(() => {
  progress += 0.01;
  if (progress > 1) {
    clearInterval(interval);
    return;
  }
  updateTransition(progress);
}, 16);
```

## 示例

### 上半身和下半身分离

```typescript
// 创建上半身遮罩
const upperBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.UPPER_BODY);

// 创建下半身遮罩
const lowerBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.LOWER_BODY);

// 添加上半身动画层
const upperLayer: BlendLayer = {
  clipName: 'wave',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.OVERRIDE,
  mask: upperBodyMask.getBones()
};
blender.addLayer(upperLayer);

// 添加下半身动画层
const lowerLayer: BlendLayer = {
  clipName: 'walk',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.OVERRIDE,
  mask: lowerBodyMask.getBones()
};
blender.addLayer(lowerLayer);
```

### 左右侧分离

```typescript
// 创建左侧遮罩
const leftSideMask = AnimationMask.createBoneGroupMask(BoneGroupType.LEFT_SIDE);

// 创建右侧遮罩
const rightSideMask = AnimationMask.createBoneGroupMask(BoneGroupType.RIGHT_SIDE);

// 添加左侧动画层
const leftLayer: BlendLayer = {
  clipName: 'walk',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.OVERRIDE,
  mask: leftSideMask.getBones()
};
blender.addLayer(leftLayer);

// 添加右侧动画层
const rightLayer: BlendLayer = {
  clipName: 'run',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.OVERRIDE,
  mask: rightSideMask.getBones()
};
blender.addLayer(rightLayer);
```

### 遮罩混合

```typescript
// 创建上半身遮罩
const upperBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.UPPER_BODY);

// 创建下半身遮罩
const lowerBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.LOWER_BODY);

// 创建混合遮罩
const blendMask = AnimationMask.createBlendMask(
  [upperBodyMask, lowerBodyMask],
  [0.7, 0.3],
  true
);

// 添加动画层
const layer: BlendLayer = {
  clipName: 'wave',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.OVERRIDE,
  mask: blendMask.getBones()
};
blender.addLayer(layer);
```

### 遮罩过渡

```typescript
// 创建上半身遮罩
const upperBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.UPPER_BODY);

// 创建下半身遮罩
const lowerBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.LOWER_BODY);

// 创建过渡遮罩
const transitionMask = AnimationMask.createTransitionMask(
  upperBodyMask,
  lowerBodyMask,
  0.5
);

// 添加动画层
const layer: BlendLayer = {
  clipName: 'wave',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.OVERRIDE,
  mask: transitionMask.getBones()
};
blender.addLayer(layer);
```

## 最佳实践

1. 使用预定义的骨骼组创建常用遮罩。
2. 使用 INCLUDE 和 EXCLUDE 创建简单遮罩。
3. 使用 HIERARCHY 和 INVERSE_HIERARCHY 创建层级遮罩。
4. 使用 BLEND 和 TRANSITION 创建复杂遮罩效果。
5. 使用不同的权重类型创建平滑过渡。
6. 动态更新遮罩参数，创建动态效果。
7. 结合混合模式和遮罩，创建复杂的动画效果。
8. 测试不同的遮罩组合，以获得最佳效果。
