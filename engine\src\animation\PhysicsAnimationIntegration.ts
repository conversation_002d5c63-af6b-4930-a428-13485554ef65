/**
 * 物理动画集成
 * 用于将物理系统与动画系统集成
 */
import * as THREE from 'three';
import type { Entity } from '../core/Entity';
import { PhysicsSystem } from '../physics/PhysicsSystem';
import { PhysicsBodyComponent } from '../physics/components/PhysicsBodyComponent';
import { PhysicsColliderComponent } from '../physics/components/PhysicsColliderComponent';
import { CharacterControllerComponent } from '../physics/components/CharacterControllerComponent';
import { AnimationBlender } from './AnimationBlender';
import { Animator } from './Animator';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 物理动画集成事件类型
 */
export enum PhysicsAnimationEventType {
  /** 物理状态改变 */
  PHYSICS_STATE_CHANGED = 'physicsStateChanged',
  /** 碰撞开始 */
  COLLISION_ENTER = 'collisionEnter',
  /** 碰撞结束 */
  COLLISION_EXIT = 'collisionExit',
  /** 接触地面 */
  GROUNDED = 'grounded',
  /** 离开地面 */
  AIRBORNE = 'airborne'
}

/**
 * 物理动画集成配置
 */
export interface PhysicsAnimationIntegrationConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否自动更新动画参数 */
  autoUpdateParameters?: boolean;
  /** 是否使用角色控制器 */
  useCharacterController?: boolean;
  /** 是否使用物理驱动骨骼 */
  usePhysicsDrivenBones?: boolean;
  /** 物理驱动骨骼列表 */
  physicsDrivenBones?: string[];
  /** 物理骨骼阻尼 */
  physicsBonesDamping?: number;
  /** 物理骨骼弹性 */
  physicsBonesRestitution?: number;
  /** 物理骨骼质量 */
  physicsBonesMass?: number;
}

/**
 * 物理动画集成
 * 用于将物理系统与动画系统集成
 */
export class PhysicsAnimationIntegration {
  /** 实体 */
  private entity: Entity;
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  /** 动画混合器 */
  private blender: AnimationBlender;
  /** 动画控制器 */
  private animator: Animator;
  /** 配置 */
  private config: PhysicsAnimationIntegrationConfig;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 是否在地面上 */
  private isGrounded: boolean = true;
  /** 上一次速度 */
  private lastVelocity: THREE.Vector3 = new THREE.Vector3();
  /** 物理驱动骨骼映射 */
  private physicsBones: Map<string, any> = new Map();
  /** 是否已初始化 */
  private initialized: boolean = false;
  /** 是否已销毁 */
  private destroyed: boolean = false;

  /**
   * 创建物理动画集成
   * @param entity 实体
   * @param physicsSystem 物理系统
   * @param blender 动画混合器
   * @param config 配置
   */
  constructor(
    entity: Entity,
    physicsSystem: PhysicsSystem,
    blender: AnimationBlender,
    config: PhysicsAnimationIntegrationConfig = {}
  ) {
    this.entity = entity;
    this.physicsSystem = physicsSystem;
    this.blender = blender;
    this.animator = blender.getAnimator();

    // 设置配置
    this.config = {
      debug: config.debug || false,
      autoUpdateParameters: config.autoUpdateParameters !== undefined ? config.autoUpdateParameters : true,
      useCharacterController: config.useCharacterController !== undefined ? config.useCharacterController : true,
      usePhysicsDrivenBones: config.usePhysicsDrivenBones !== undefined ? config.usePhysicsDrivenBones : false,
      physicsDrivenBones: config.physicsDrivenBones || [],
      physicsBonesDamping: config.physicsBonesDamping || 0.1,
      physicsBonesRestitution: config.physicsBonesRestitution || 0.3,
      physicsBonesMass: config.physicsBonesMass || 1.0
    };
  }

  /**
   * 初始化
   */
  public initialize(): void {
    if (this.initialized || this.destroyed) return;

    // 检查实体是否有物理组件
    const physicsBody = this.entity.getComponent<PhysicsBodyComponent>(PhysicsBodyComponent.type);
    if (!physicsBody) {
      console.warn('实体没有物理体组件，物理动画集成将不会工作');
      return;
    }

    // 如果使用角色控制器，检查实体是否有角色控制器组件
    if (this.config.useCharacterController) {
      const characterController = this.entity.getComponent<CharacterControllerComponent>(CharacterControllerComponent.type);
      if (!characterController) {
        console.warn('实体没有角色控制器组件，但配置了使用角色控制器');
      }
    }

    // 如果使用物理驱动骨骼，初始化物理骨骼
    if (this.config.usePhysicsDrivenBones && this.config.physicsDrivenBones.length > 0) {
      this.initializePhysicsDrivenBones();
    }

    // 添加碰撞事件监听器
    this.physicsSystem.on('collisionBegin', this.handleCollisionStart.bind(this));
    this.physicsSystem.on('collisionEnd', this.handleCollisionEnd.bind(this));

    this.initialized = true;

    if (this.config.debug) {
      console.log('物理动画集成已初始化');
    }
  }

  /**
   * 销毁
   */
  public destroy(): void {
    if (this.destroyed) return;

    // 移除碰撞事件监听器
    this.physicsSystem.off('collisionBegin', this.handleCollisionStart.bind(this));
    this.physicsSystem.off('collisionEnd', this.handleCollisionEnd.bind(this));

    // 清理物理骨骼
    this.physicsBones.clear();

    // 清理事件监听器
    this.eventEmitter.removeAllListeners();

    this.destroyed = true;

    if (this.config.debug) {
      console.log('物理动画集成已销毁');
    }
  }

  /**
   * 更新
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized || this.destroyed) return;

    // 获取物理体组件
    const physicsBody = this.entity.getComponent<PhysicsBodyComponent>(PhysicsBodyComponent.type);
    if (!physicsBody) return;

    // 获取物理体
    const body = this.physicsSystem.getPhysicsBody(this.entity);
    if (!body) return;

    // 获取速度
    const velocity = body.getVelocity();

    // 检查是否在地面上
    const characterController = this.entity.getComponent<CharacterControllerComponent>(CharacterControllerComponent.type);
    if (characterController) {
      const wasGrounded = this.isGrounded;
      this.isGrounded = characterController.isOnGround();

      // 如果接触地面状态改变，触发事件
      if (wasGrounded !== this.isGrounded) {
        if (this.isGrounded) {
          this.eventEmitter.emit(PhysicsAnimationEventType.GROUNDED);
        } else {
          this.eventEmitter.emit(PhysicsAnimationEventType.AIRBORNE);
        }
      }
    }

    // 如果自动更新参数，更新动画参数
    if (this.config.autoUpdateParameters) {
      this.updateAnimationParameters(velocity);
    }

    // 更新物理驱动骨骼
    if (this.config.usePhysicsDrivenBones && this.physicsBones.size > 0) {
      this.updatePhysicsDrivenBones(deltaTime);
    }

    // 保存当前速度
    this.lastVelocity.copy(velocity);
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public addEventListener(event: PhysicsAnimationEventType, listener: (data: any) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public removeEventListener(event: PhysicsAnimationEventType, listener: (data: any) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 初始化物理驱动骨骼
   */
  private initializePhysicsDrivenBones(): void {
    // 获取骨骼
    const skeleton = this.animator.getSkeleton();
    if (!skeleton) {
      console.warn('动画控制器没有骨骼，无法初始化物理驱动骨骼');
      return;
    }

    // 遍历配置的物理驱动骨骼
    for (const boneName of this.config.physicsDrivenBones) {
      // 在骨骼数组中查找指定名称的骨骼
      const bone = skeleton.bones.find(b => b.name === boneName);
      if (!bone) {
        console.warn(`找不到骨骼: ${boneName}`);
        continue;
      }

      // 创建物理骨骼
      // 这里只是一个简单的实现，实际应用中可能需要更复杂的物理骨骼系统
      const physicsBone = {
        bone,
        originalMatrix: bone.matrix.clone(),
        originalPosition: bone.position.clone(),
        originalQuaternion: bone.quaternion.clone(),
        velocity: new THREE.Vector3(),
        angularVelocity: new THREE.Vector3(),
        mass: this.config.physicsBonesMass,
        damping: this.config.physicsBonesDamping,
        restitution: this.config.physicsBonesRestitution
      };

      // 添加到映射
      this.physicsBones.set(boneName, physicsBone);
    }

    if (this.config.debug) {
      console.log(`初始化了 ${this.physicsBones.size} 个物理驱动骨骼`);
    }
  }

  /**
   * 更新物理驱动骨骼
   * @param deltaTime 帧间隔时间（秒）
   */
  private updatePhysicsDrivenBones(deltaTime: number): void {
    // 获取物理体
    const body = this.physicsSystem.getPhysicsBody(this.entity);
    if (!body) return;

    // 遍历物理骨骼
    this.physicsBones.forEach((physicsBone) => {
      const { bone, velocity, angularVelocity, damping, mass } = physicsBone;

      // 计算物理力
      const force = new THREE.Vector3();

      // 添加重力
      force.y -= 9.8 * mass;

      // 添加阻尼
      force.sub(velocity.clone().multiplyScalar(damping));

      // 更新速度
      velocity.add(force.multiplyScalar(deltaTime / mass));

      // 更新位置
      bone.position.add(velocity.clone().multiplyScalar(deltaTime));

      // 更新旋转
      const rotationDelta = new THREE.Quaternion().setFromEuler(
        new THREE.Euler(
          angularVelocity.x * deltaTime,
          angularVelocity.y * deltaTime,
          angularVelocity.z * deltaTime
        )
      );
      bone.quaternion.multiply(rotationDelta);

      // 更新矩阵
      bone.updateMatrix();
    });
  }

  /**
   * 更新动画参数
   * @param velocity 速度
   */
  private updateAnimationParameters(velocity: THREE.Vector3): void {
    // 计算水平速度
    const horizontalVelocity = new THREE.Vector2(velocity.x, velocity.z).length();

    // 更新动画参数
    this.animator.setParameter('velocity', horizontalVelocity);
    this.animator.setParameter('verticalVelocity', velocity.y);
    this.animator.setParameter('isGrounded', this.isGrounded);

    // 计算加速度
    const acceleration = new THREE.Vector3().subVectors(velocity, this.lastVelocity);
    this.animator.setParameter('acceleration', acceleration.length());

    // 计算移动方向
    if (horizontalVelocity > 0.1) {
      const direction = Math.atan2(velocity.x, velocity.z);
      this.animator.setParameter('direction', direction);
    }
  }

  /**
   * 处理碰撞开始事件
   * @param event 碰撞事件
   */
  private handleCollisionStart(event: any): void {
    // 检查碰撞是否涉及当前实体
    if (event.entityA === this.entity || event.entityB === this.entity) {
      // 获取碰撞的另一个实体
      const otherEntity = event.entityA === this.entity ? event.entityB : event.entityA;

      // 触发碰撞开始事件
      this.eventEmitter.emit(PhysicsAnimationEventType.COLLISION_ENTER, {
        entity: this.entity,
        otherEntity,
        contact: event
      });
    }
  }

  /**
   * 处理碰撞结束事件
   * @param event 碰撞事件
   */
  private handleCollisionEnd(event: any): void {
    // 检查碰撞是否涉及当前实体
    if (event.entityA === this.entity || event.entityB === this.entity) {
      // 获取碰撞的另一个实体
      const otherEntity = event.entityA === this.entity ? event.entityB : event.entityA;

      // 触发碰撞结束事件
      this.eventEmitter.emit(PhysicsAnimationEventType.COLLISION_EXIT, {
        entity: this.entity,
        otherEntity,
        contact: event
      });
    }
  }
}
