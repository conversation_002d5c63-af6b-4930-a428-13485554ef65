/**
 * 面部动画预设面板
 * 用于管理面部动画预设和模板
 */
import React, { useState } from 'react';
import { Tabs } from 'antd';
import { useTranslation } from 'react-i18next';
import { FacialAnimationPresetManager } from '../components/FacialAnimationEditor/FacialAnimationPresetManager';
import { FacialAnimationTemplateManager } from '../components/FacialAnimationEditor/FacialAnimationTemplateManager';
import { FacialAnimationPresetType } from '../../engine/src/avatar/presets/FacialAnimationPresetSystem';
import './FacialAnimationPresetPanel.less';

const { TabPane } = Tabs;

/**
 * 面部动画预设面板属性
 */
interface FacialAnimationPresetPanelProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
  /** 默认活动标签 */
  defaultActiveTab?: string;
  /** 默认预设类型 */
  defaultPresetType?: FacialAnimationPresetType;
  /** 默认文化 */
  defaultCulture?: string;
  /** 应用预设回调 */
  onPresetApply?: (preset: any) => void;
  /** 应用模板回调 */
  onTemplateApply?: (template: any, parameters: any) => void;
}

/**
 * 面部动画预设面板
 */
export const FacialAnimationPresetPanel: React.FC<FacialAnimationPresetPanelProps> = ({
  entityId,
  editable = true,
  defaultActiveTab = 'presets',
  defaultPresetType = FacialAnimationPresetType.STANDARD,
  defaultCulture = 'global',
  onPresetApply,
  onTemplateApply
}) => {
  const { t } = useTranslation();
  
  // 状态
  const [activeTab, setActiveTab] = useState<string>(defaultActiveTab);
  
  return (
    <div className="facial-animation-preset-panel">
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        type="card"
      >
        <TabPane
          tab={t('editor.animation.presets')}
          key="presets"
        >
          <FacialAnimationPresetManager
            entityId={entityId}
            editable={editable}
            defaultPresetType={defaultPresetType}
            defaultCulture={defaultCulture}
            onPresetApply={onPresetApply}
          />
        </TabPane>
        
        <TabPane
          tab={t('editor.animation.templates')}
          key="templates"
        >
          <FacialAnimationTemplateManager
            entityId={entityId}
            editable={editable}
            onTemplateApply={onTemplateApply}
          />
        </TabPane>
      </Tabs>
    </div>
  );
};
