# 增强水体交互系统教程

本教程将介绍如何使用增强的水体交互系统，该系统提供了更精确的浮力计算、更真实的水阻力效果和更丰富的水流交互。

## 目录

1. [系统概述](#系统概述)
2. [基本配置](#基本配置)
3. [高级浮力计算](#高级浮力计算)
4. [高级阻力计算](#高级阻力计算)
5. [高级水流交互](#高级水流交互)
6. [性能优化](#性能优化)
7. [示例代码](#示例代码)
8. [常见问题](#常见问题)

## 系统概述

增强水体交互系统是对基础水体交互系统的扩展，提供了以下主要增强功能：

- **体素浮力计算**：使用体素网格对物体进行细分，计算更精确的浮力和浮力扭矩
- **浮力稳定化**：防止物体在水面上不自然抖动
- **方向性阻力**：考虑物体形状和方向的阻力计算
- **湍流阻力**：在湍流水域中的真实阻力表现
- **旋转阻力**：物体在水中旋转受到的阻力
- **高级水流交互**：更真实的水流冲击力和扭矩效应

## 基本配置

要使用增强水体交互系统，首先需要创建水体物理系统和水体交互系统：

```typescript
// 创建水体物理系统
const waterPhysicsSystem = new WaterPhysicsSystem(world, {
  enabled: true,
  autoUpdate: true,
  updateFrequency: 1,
  enableBuoyancy: true,
  enableDrag: true,
  enableFlow: true,
  enableWaves: true,
  enableCollision: true,
  enableParticles: true,
  enableMultithreading: true,
  workerCount: 4
});
world.addSystem(waterPhysicsSystem);

// 创建水体交互系统（使用增强配置）
const waterInteractionSystem = new WaterInteractionSystem(world, {
  enabled: true,
  autoUpdate: true,
  updateFrequency: 1,
  
  // 启用所有基本效果
  enableSplashEffect: true,
  enableRippleEffect: true,
  enableDropletEffect: true,
  enableFlowEffect: true,
  enableSplittingEffect: true,
  enableBuoyancyEffect: true,
  enableDragEffect: true
});
world.addSystem(waterInteractionSystem);
```

## 高级浮力计算

### 体素浮力计算

体素浮力计算将物体分割成多个小体素，对每个体素单独计算浮力，然后累加得到总浮力和浮力扭矩。这种方法可以更精确地模拟不规则形状物体的浮力效果。

```typescript
// 启用体素浮力计算
const waterInteractionSystem = new WaterInteractionSystem(world, {
  // 其他配置...
  useVoxelBuoyancy: true,
  voxelResolution: 5, // 每个维度的体素数量
});
```

### 浮力稳定化

浮力稳定化通过添加阻尼力矩，防止物体在水面上不自然抖动，使物体在水面上的行为更加稳定。

```typescript
// 启用浮力稳定化
const waterInteractionSystem = new WaterInteractionSystem(world, {
  // 其他配置...
  enableBuoyancyStabilization: true,
  buoyancyStabilizationStrength: 0.5, // 稳定化强度
});
```

## 高级阻力计算

### 方向性阻力

方向性阻力考虑物体在不同方向上的形状和阻力系数，使物体在水中的运动更加真实。例如，流线型物体在前进方向的阻力较小，而在侧向的阻力较大。

```typescript
// 启用方向性阻力
const waterInteractionSystem = new WaterInteractionSystem(world, {
  // 其他配置...
  useDirectionalDrag: true,
  dragCoefficientX: 0.4, // 前后方向阻力系数
  dragCoefficientY: 0.8, // 上下方向阻力系数
  dragCoefficientZ: 0.6, // 左右方向阻力系数
});
```

### 湍流阻力

湍流阻力模拟水体湍流对物体阻力的影响，使物体在湍流水域中的运动更加复杂和真实。

```typescript
// 启用湍流阻力
const waterInteractionSystem = new WaterInteractionSystem(world, {
  // 其他配置...
  enableTurbulenceDrag: true,
});
```

### 旋转阻力

旋转阻力模拟物体在水中旋转时受到的阻力，使物体的旋转运动更加真实。

```typescript
// 启用旋转阻力
const waterInteractionSystem = new WaterInteractionSystem(world, {
  // 其他配置...
  enableRotationalDrag: true,
  rotationalDragStrength: 0.5, // 旋转阻力强度
});
```

## 高级水流交互

高级水流交互提供了更真实的水流冲击力和扭矩效应，使物体在水流中的运动更加真实。

```typescript
// 启用高级水流交互
const waterInteractionSystem = new WaterInteractionSystem(world, {
  // 其他配置...
  useAdvancedFlowInteraction: true,
  enableTurbulentFlow: true, // 启用湍流水流
  flowCoefficientX: 0.4, // 前后方向水流系数
  flowCoefficientY: 0.7, // 上下方向水流系数
  flowCoefficientZ: 0.6, // 左右方向水流系数
});
```

## 性能优化

增强水体交互系统提供了多种性能优化选项，可以根据需要进行调整：

1. **更新频率**：通过调整 `updateFrequency` 参数，可以控制水体交互系统的更新频率，减少计算开销。
2. **体素分辨率**：通过调整 `voxelResolution` 参数，可以控制体素浮力计算的精度，较低的分辨率可以提高性能，但会降低精度。
3. **多线程计算**：水体物理系统支持多线程计算，可以通过 `enableMultithreading` 和 `workerCount` 参数进行配置。

```typescript
// 性能优化配置
const waterPhysicsSystem = new WaterPhysicsSystem(world, {
  // 其他配置...
  updateFrequency: 2, // 每2帧更新一次
  enableMultithreading: true,
  workerCount: 4 // 使用4个工作线程
});

const waterInteractionSystem = new WaterInteractionSystem(world, {
  // 其他配置...
  updateFrequency: 2, // 每2帧更新一次
  voxelResolution: 3 // 使用较低的体素分辨率
});
```

## 示例代码

以下是一个完整的示例，展示如何使用增强水体交互系统：

```typescript
// 创建水体物理系统
const waterPhysicsSystem = new WaterPhysicsSystem(world, {
  enabled: true,
  autoUpdate: true,
  updateFrequency: 1,
  enableBuoyancy: true,
  enableDrag: true,
  enableFlow: true,
  enableWaves: true,
  enableCollision: true,
  enableParticles: true,
  enableMultithreading: true,
  workerCount: 4
});
world.addSystem(waterPhysicsSystem);

// 创建水体交互系统（使用增强配置）
const waterInteractionSystem = new WaterInteractionSystem(world, {
  enabled: true,
  autoUpdate: true,
  updateFrequency: 1,
  
  // 启用所有基本效果
  enableSplashEffect: true,
  enableRippleEffect: true,
  enableDropletEffect: true,
  enableFlowEffect: true,
  enableSplittingEffect: true,
  enableBuoyancyEffect: true,
  enableDragEffect: true,
  
  // 启用高级浮力计算
  useVoxelBuoyancy: true,
  voxelResolution: 5,
  enableBuoyancyStabilization: true,
  buoyancyStabilizationStrength: 0.5,
  
  // 启用高级阻力计算
  useDirectionalDrag: true,
  enableTurbulenceDrag: true,
  enableRotationalDrag: true,
  rotationalDragStrength: 0.5,
  dragCoefficientX: 0.4,
  dragCoefficientY: 0.8,
  dragCoefficientZ: 0.6,
  
  // 启用高级水流交互
  useAdvancedFlowInteraction: true,
  enableTurbulentFlow: true,
  
  // 设置效果强度
  buoyancyEffectStrength: 1.2,
  dragEffectStrength: 1.0,
  flowEffectStrength: 1.0
});
world.addSystem(waterInteractionSystem);

// 创建水体
const waterBody = WaterPresets.createPreset(world, {
  type: WaterPresetType.LAKE,
  size: { width: 20, height: 5, depth: 20 },
  position: new THREE.Vector3(0, 0, 0)
});

// 创建测试物体
const boxEntity = new Entity('box');
const boxTransform = new TransformComponent();
boxTransform.setPosition(new THREE.Vector3(0, 5, 0));
boxEntity.addComponent(boxTransform);

const boxMesh = new MeshComponent();
boxMesh.setGeometry(new THREE.BoxGeometry(1, 1, 1));
boxMesh.setMaterial(new THREE.MeshStandardMaterial({ color: 0xff0000 }));
boxEntity.addComponent(boxMesh);

const boxPhysics = new PhysicsBodyComponent();
boxPhysics.setMass(1);
boxPhysics.setShape('BOX');
boxPhysics.setSize(new THREE.Vector3(1, 1, 1));
boxEntity.addComponent(boxPhysics);

world.addEntity(boxEntity);
```

## 常见问题

### 物体在水面上不稳定或抖动

如果物体在水面上不稳定或抖动，可以尝试以下解决方案：

1. 启用浮力稳定化：`enableBuoyancyStabilization: true`
2. 增加浮力稳定化强度：`buoyancyStabilizationStrength: 0.8`
3. 减小物理系统的时间步长
4. 增加物体的质量

### 物体在水中运动不真实

如果物体在水中的运动不真实，可以尝试以下解决方案：

1. 启用方向性阻力：`useDirectionalDrag: true`
2. 调整阻力系数：`dragCoefficientX`、`dragCoefficientY`、`dragCoefficientZ`
3. 启用旋转阻力：`enableRotationalDrag: true`
4. 调整旋转阻力强度：`rotationalDragStrength`

### 性能问题

如果遇到性能问题，可以尝试以下解决方案：

1. 减小体素分辨率：`voxelResolution: 3`
2. 增加更新频率：`updateFrequency: 2`
3. 禁用一些高级功能，如体素浮力计算、湍流阻力等
4. 启用多线程计算：`enableMultithreading: true`
5. 调整工作线程数量：`workerCount: 2`
