/**
 * 资产管理器类
 * 负责加载和管理资产
 */
import { EventEmitter } from '../utils/EventEmitter';
import { AssetLoader } from './AssetLoader';
import { ResourceManager, AssetType } from './ResourceManager';
import { ResourceDependencyManager, DependencyType } from './ResourceDependencyManager';

export interface AssetInfo {
  /** 资产ID */
  id: string;
  /** 资产名称 */
  name: string;
  /** 资产类型 */
  type: AssetType;
  /** 资产URL */
  url: string;
  /** 资产数据 */
  data?: any;
  /** 资产元数据 */
  metadata?: Record<string, any>;
  /** 是否已加载 */
  loaded: boolean;
  /** 加载错误 */
  error?: Error;
}

export interface AssetManagerOptions {
  /** 基础URL */
  baseUrl?: string;
  /** 资源管理器选项 */
  resourceOptions?: any;
  /** 是否启用依赖管理 */
  enableDependencyManagement?: boolean;
}

export class AssetManager extends EventEmitter {
  /** 资产加载器 */
  private loader: AssetLoader;

  /** 资源管理器 */
  private resourceManager: ResourceManager;

  /** 资源依赖管理器 */
  private dependencyManager: ResourceDependencyManager;

  /** 资产映射 */
  private assets: Map<string, AssetInfo> = new Map();

  /** 基础URL */
  private baseUrl: string;

  /** 是否启用依赖管理 */
  private enableDependencyManagement: boolean;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建资产管理器实例
   * @param options 资产管理器选项
   */
  constructor(options: AssetManagerOptions = {}) {
    super();

    this.baseUrl = options.baseUrl || '';
    this.enableDependencyManagement = options.enableDependencyManagement !== undefined ? options.enableDependencyManagement : true;

    this.loader = new AssetLoader();
    this.resourceManager = new ResourceManager(options.resourceOptions);
    this.dependencyManager = new ResourceDependencyManager();
  }

  /**
   * 初始化资产管理器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 初始化资源管理器
    this.resourceManager.initialize();

    // 初始化依赖管理器
    if (this.enableDependencyManagement) {
      this.dependencyManager.initialize();
    }

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 注册资产
   * @param id 资产ID
   * @param name 资产名称
   * @param type 资产类型
   * @param url 资产URL
   * @param metadata 资产元数据
   * @returns 资产信息
   */
  public registerAsset(
    id: string,
    name: string,
    type: AssetType,
    url: string,
    metadata: Record<string, any> = {}
  ): AssetInfo {
    // 检查是否已存在同ID资产
    if (this.assets.has(id)) {
      throw new Error(`已存在ID为 ${id} 的资产`);
    }

    // 创建资产信息
    const assetInfo: AssetInfo = {
      id,
      name,
      type,
      url,
      metadata,
      loaded: false,
    };

    // 添加到资产映射
    this.assets.set(id, assetInfo);

    // 发出资产注册事件
    this.emit('assetRegistered', assetInfo);

    return assetInfo;
  }

  /**
   * 加载资产
   * @param id 资产ID
   * @param loadDependencies 是否加载依赖
   * @returns Promise，解析为资产数据
   */
  public async loadAsset(id: string, loadDependencies: boolean = true): Promise<any> {
    // 检查资产是否存在
    if (!this.assets.has(id)) {
      throw new Error(`找不到ID为 ${id} 的资产`);
    }

    const assetInfo = this.assets.get(id)!;

    // 如果已经加载，直接返回数据
    if (assetInfo.loaded && assetInfo.data) {
      return assetInfo.data;
    }

    try {
      // 如果启用依赖管理且需要加载依赖，则先加载强依赖
      if (this.enableDependencyManagement && loadDependencies) {
        await this.loadDependencies(id);
      }

      // 构建完整URL
      const url = this.resolveUrl(assetInfo.url);

      // 加载资产
      const data = await this.resourceManager.load(id, assetInfo.type, url);

      // 更新资产信息
      assetInfo.data = data;
      assetInfo.loaded = true;
      assetInfo.error = undefined;

      // 发出资产加载事件
      this.emit('assetLoaded', assetInfo);

      return data;
    } catch (error) {
      // 更新资产信息
      assetInfo.loaded = false;
      assetInfo.error = error as Error;

      // 发出资产加载错误事件
      this.emit('assetError', assetInfo, error);

      throw error;
    }
  }

  /**
   * 加载资产依赖
   * @param id 资产ID
   * @returns Promise
   */
  private async loadDependencies(id: string): Promise<void> {
    if (!this.enableDependencyManagement) {
      return;
    }

    // 获取强依赖
    const strongDependencies = this.dependencyManager.getStrongDependencies(id);

    // 如果没有强依赖，则直接返回
    if (strongDependencies.length === 0) {
      return;
    }

    // 加载所有强依赖
    const promises = strongDependencies.map(depId => this.loadAsset(depId, true));

    await Promise.all(promises);
  }

  /**
   * 批量加载资产
   * @param ids 资产ID数组
   * @param onProgress 进度回调
   * @returns Promise，解析为资产数据映射
   */
  public async loadAssets(
    ids: string[],
    onProgress?: (loaded: number, total: number) => void
  ): Promise<Map<string, any>> {
    const total = ids.length;
    let loaded = 0;
    const results = new Map<string, any>();

    // 加载每个资产
    for (const id of ids) {
      try {
        const data = await this.loadAsset(id);
        results.set(id, data);
      } catch (error) {
        console.error(`加载资产 ${id} 失败:`, error);
      }

      loaded++;

      if (onProgress) {
        onProgress(loaded, total);
      }
    }

    return results;
  }

  /**
   * 卸载资产
   * @param id 资产ID
   * @param unloadDependents 是否卸载依赖于此资产的资产
   * @returns 是否成功卸载
   */
  public unloadAsset(id: string, unloadDependents: boolean = false): boolean {
    // 检查资产是否存在
    if (!this.assets.has(id)) {
      return false;
    }

    const assetInfo = this.assets.get(id)!;

    // 如果未加载，直接返回
    if (!assetInfo.loaded || !assetInfo.data) {
      return false;
    }

    // 如果启用依赖管理且需要卸载依赖于此资产的资产
    if (this.enableDependencyManagement && unloadDependents) {
      this.unloadDependents(id);
    }

    // 释放资源
    this.resourceManager.release(id);

    // 更新资产信息
    assetInfo.data = undefined;
    assetInfo.loaded = false;

    // 发出资产卸载事件
    this.emit('assetUnloaded', assetInfo);

    return true;
  }

  /**
   * 卸载依赖于指定资产的资产
   * @param id 资产ID
   */
  private unloadDependents(id: string): void {
    if (!this.enableDependencyManagement) {
      return;
    }

    // 获取依赖于此资产的资产
    const dependents = this.dependencyManager.getReverseDependencies(id);

    // 卸载所有依赖于此资产的资产
    for (const depId of dependents) {
      this.unloadAsset(depId, true);
    }
  }

  /**
   * 获取资产
   * @param id 资产ID
   * @returns 资产数据，如果未加载则返回null
   */
  public getAsset(id: string): any {
    // 检查资产是否存在
    if (!this.assets.has(id)) {
      return null;
    }

    const assetInfo = this.assets.get(id)!;

    // 如果未加载，返回null
    if (!assetInfo.loaded || !assetInfo.data) {
      return null;
    }

    return assetInfo.data;
  }

  /**
   * 获取资产信息
   * @param id 资产ID
   * @returns 资产信息，如果不存在则返回null
   */
  public getAssetInfo(id: string): AssetInfo | null {
    return this.assets.get(id) || null;
  }

  /**
   * 获取所有资产信息
   * @returns 资产信息数组
   */
  public getAllAssetInfo(): AssetInfo[] {
    return Array.from(this.assets.values());
  }

  /**
   * 根据类型获取资产信息
   * @param type 资产类型
   * @returns 资产信息数组
   */
  public getAssetInfoByType(type: AssetType): AssetInfo[] {
    return Array.from(this.assets.values()).filter(asset => asset.type === type);
  }

  /**
   * 根据名称查找资产信息
   * @param name 资产名称
   * @returns 资产信息数组
   */
  public findAssetInfoByName(name: string): AssetInfo[] {
    return Array.from(this.assets.values()).filter(asset => asset.name === name);
  }

  /**
   * 移除资产
   * @param id 资产ID
   * @returns 是否成功移除
   */
  public removeAsset(id: string): boolean {
    // 检查资产是否存在
    if (!this.assets.has(id)) {
      return false;
    }

    const assetInfo = this.assets.get(id)!;

    // 如果已加载，先卸载
    if (assetInfo.loaded && assetInfo.data) {
      this.unloadAsset(id);
    }

    // 从资产映射中移除
    this.assets.delete(id);

    // 发出资产移除事件
    this.emit('assetRemoved', assetInfo);

    return true;
  }

  /**
   * 设置基础URL
   * @param baseUrl 基础URL
   */
  public setBaseUrl(baseUrl: string): void {
    this.baseUrl = baseUrl;
  }

  /**
   * 获取基础URL
   * @returns 基础URL
   */
  public getBaseUrl(): string {
    return this.baseUrl;
  }

  /**
   * 解析URL
   * @param url 相对URL
   * @returns 完整URL
   */
  private resolveUrl(url: string): string {
    // 如果是绝对URL或数据URL，直接返回
    if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('data:')) {
      return url;
    }

    // 拼接基础URL和相对URL
    return `${this.baseUrl}${this.baseUrl.endsWith('/') || url.startsWith('/') ? '' : '/'}${url}`;
  }

  /**
   * 获取资源管理器
   * @returns 资源管理器
   */
  public getResourceManager(): ResourceManager {
    return this.resourceManager;
  }

  /**
   * 获取资源依赖管理器
   * @returns 资源依赖管理器
   */
  public getDependencyManager(): ResourceDependencyManager {
    return this.dependencyManager;
  }

  /**
   * 获取资产加载器
   * @returns 资产加载器
   */
  public getLoader(): AssetLoader {
    return this.loader;
  }

  /**
   * 添加资产依赖关系
   * @param resourceId 资产ID
   * @param dependencyId 依赖资产ID
   * @param type 依赖类型
   * @returns 是否成功添加
   */
  public addDependency(resourceId: string, dependencyId: string, type: DependencyType = DependencyType.STRONG): boolean {
    if (!this.enableDependencyManagement) {
      return false;
    }

    // 检查资产是否存在
    if (!this.assets.has(resourceId) || !this.assets.has(dependencyId)) {
      return false;
    }

    // 添加依赖关系
    this.dependencyManager.addDependency(resourceId, dependencyId, type);

    return true;
  }

  /**
   * 移除资产依赖关系
   * @param resourceId 资产ID
   * @param dependencyId 依赖资产ID
   * @returns 是否成功移除
   */
  public removeDependency(resourceId: string, dependencyId: string): boolean {
    if (!this.enableDependencyManagement) {
      return false;
    }

    return this.dependencyManager.removeDependency(resourceId, dependencyId);
  }

  /**
   * 获取资产的依赖
   * @param id 资产ID
   * @returns 依赖资产ID数组
   */
  public getDependencies(id: string): string[] {
    if (!this.enableDependencyManagement) {
      return [];
    }

    return this.dependencyManager.getDependencies(id).map(dep => dep.id);
  }

  /**
   * 获取依赖于资产的资产
   * @param id 资产ID
   * @returns 依赖于此资产的资产ID数组
   */
  public getDependents(id: string): string[] {
    if (!this.enableDependencyManagement) {
      return [];
    }

    return this.dependencyManager.getReverseDependencies(id);
  }

  /**
   * 清空所有资产
   */
  public clear(): void {
    // 卸载所有已加载的资产
    for (const assetInfo of Array.from(this.assets.values())) {
      if (assetInfo.loaded && assetInfo.data) {
        this.unloadAsset(assetInfo.id);
      }
    }

    // 清空资产映射
    this.assets.clear();

    // 发出清空事件
    this.emit('cleared');
  }

  /**
   * 销毁资产管理器
   */
  public dispose(): void {
    // 清空所有资产
    this.clear();

    // 销毁资源管理器
    (this.resourceManager as any).dispose();

    // 销毁依赖管理器
    if (this.enableDependencyManagement) {
      (this.dependencyManager as any).dispose();
    }

    // 销毁加载器
    (this.loader as any).dispose();

    // 移除所有事件监听器
    this.removeAllListeners();

    this.initialized = false;
  }
}
