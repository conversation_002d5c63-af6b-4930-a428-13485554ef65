# 场景编辑视频教程脚本

## 视频信息

- **标题**：DL（Digital Learning）引擎编辑器场景编辑基础
- **时长**：约15-20分钟
- **目标受众**：DL（Digital Learning）引擎编辑器新用户
- **先决条件**：已安装DL（Digital Learning）引擎编辑器，完成基本界面熟悉

## 视频目标

本视频教程旨在向用户介绍DL（Digital Learning）引擎编辑器的场景编辑基础功能，包括创建和组织场景对象、使用变换工具、管理层次结构、使用网格和材质以及保存和加载场景。完成本教程后，用户将能够创建和编辑基本的3D场景。

## 脚本大纲

### 1. 介绍（0:00 - 1:00）

**画面**：DL（Digital Learning）引擎编辑器启动画面，过渡到主界面

**旁白**：
"欢迎来到DL（Digital Learning）引擎编辑器场景编辑基础教程。在本视频中，我们将学习如何在DL（Digital Learning）引擎编辑器中创建和编辑3D场景。我们将涵盖创建和组织对象、使用变换工具、管理层次结构、添加网格和材质，以及保存和加载场景等基础知识。无论您是想创建游戏、可视化项目还是交互式体验，掌握场景编辑都是必不可少的第一步。让我们开始吧！"

### 2. 创建新场景（1:00 - 2:30）

**画面**：演示创建新场景的过程

**旁白**：
"首先，让我们创建一个新场景。点击主菜单中的'文件'，然后选择'新建场景'。您也可以使用快捷键Ctrl+N。

在新场景对话框中，我们可以选择一个模板作为起点。对于本教程，我们选择'基础3D场景'模板，它包含一个摄像机和一个方向光。

输入场景名称，如'MyFirstScene'，然后点击'创建'按钮。

现在，我们有了一个新的空场景，包含基本的摄像机和光源设置。场景视图中央的网格表示世界坐标系的地面平面，帮助我们了解对象的位置和比例。"

### 3. 场景导航（2:30 - 4:30）

**画面**：演示场景视图中的导航操作

**旁白**：
"在开始添加对象之前，让我们先学习如何在场景中导航。

要平移视图，按住鼠标中键并拖动，或按住Alt键和左键并拖动。

要旋转视图，按住Alt键和右键并拖动。

要缩放视图，滚动鼠标滚轮，或按住Alt键和Shift键和左键并上下拖动。

如果您选择了一个对象并想将视图聚焦到它，只需按F键。

您还可以使用数字键盘上的按键快速切换到标准视图：
- 1键：前视图
- 3键：右视图
- 7键：顶视图
- 5键：切换透视和正交视图

这些导航技巧将帮助您高效地在场景中移动和查看对象。"

### 4. 添加基本对象（4:30 - 6:30）

**画面**：演示添加各种基本对象

**旁白**：
"现在让我们向场景添加一些基本对象。点击主菜单中的'创建'，然后选择'3D对象'。

首先，让我们添加一个立方体。选择'立方体'，一个默认大小的立方体将出现在场景中心。

接下来，添加一个球体。再次点击'创建 > 3D对象'，然后选择'球体'。

同样，我们可以添加其他基本形状，如圆柱体、平面和胶囊体。

每个新创建的对象都会出现在场景视图中，并在层次结构面板中显示为一个条目。层次结构面板显示了场景中所有对象的组织结构。

您也可以通过资产面板添加预制体或模型。点击资产面板中的模型，然后拖放到场景视图中。"

### 5. 选择和变换对象（6:30 - 9:00）

**画面**：演示选择对象和使用变换工具

**旁白**：
"选择和变换对象是场景编辑的基础操作。

要选择对象，只需在场景视图中点击它，或在层次结构面板中点击其名称。选中的对象会在场景视图中显示边界框，并在属性面板中显示其属性。

DL（Digital Learning）引擎提供了三种主要的变换工具：移动、旋转和缩放。

移动工具（快捷键W）：允许您沿着X、Y、Z轴移动对象。点击并拖动轴向箭头可以沿特定轴移动，或拖动中心方块可以在平面上自由移动。

旋转工具（快捷键E）：允许您围绕不同轴旋转对象。拖动彩色环可以围绕特定轴旋转，或拖动外部灰色环可以自由旋转。

缩放工具（快捷键R）：允许您调整对象大小。拖动轴向控制点可以沿特定轴缩放，或拖动中心方块可以均匀缩放所有轴。

您还可以在属性面板中直接输入精确的变换值，包括位置、旋转和缩放。"

### 6. 组织层次结构（9:00 - 11:00）

**画面**：演示创建和管理对象层次结构

**旁白**：
"在复杂场景中，组织对象层次结构是非常重要的。

要创建空对象作为容器，点击'创建 > 空对象'。空对象只有变换组件，没有可见的网格，适合用作父对象。

要创建父子关系，只需在层次结构面板中将一个对象拖放到另一个对象上。子对象将继承父对象的变换，这意味着当您移动、旋转或缩放父对象时，所有子对象也会相应变化。

例如，让我们创建一个简单的太阳系模型。创建一个空对象命名为'太阳系'，然后添加球体作为'太阳'。再添加一个球体作为'地球'，并将其放置在太阳旁边。然后创建一个更小的球体作为'月球'，并将其放在地球附近。

将'太阳'拖放到'太阳系'下，将'地球'也拖放到'太阳系'下，然后将'月球'拖放到'地球'下。现在，当您移动'太阳系'时，所有天体都会一起移动；当您移动'地球'时，'月球'会跟随移动，但'太阳'不会受影响。

您还可以使用文件夹组织对象。右键点击层次结构面板，选择'创建 > 文件夹'，然后将相关对象拖入文件夹中。"

### 7. 使用网格和材质（11:00 - 13:30）

**画面**：演示添加和编辑网格与材质

**旁白**：
"接下来，让我们学习如何使用网格和材质来定义对象的外观。

每个可见对象都有一个网格渲染器组件和一个网格过滤器组件。网格过滤器定义了对象的形状，而网格渲染器控制如何渲染这个形状。

要更改对象的网格，选择对象，然后在属性面板的网格过滤器组件中，点击'网格'字段旁边的选择按钮。在弹出的选择器中，选择一个不同的网格资产。

材质定义了对象表面的外观属性，如颜色、反射率和纹理。要更改对象的材质，在网格渲染器组件中，点击'材质'字段旁边的选择按钮，然后选择一个现有材质。

您也可以创建新材质。在资产面板中，点击'创建'按钮，选择'材质'，然后选择材质类型，如'标准材质'。

创建材质后，您可以在材质编辑器中调整其属性。双击材质打开编辑器，然后调整颜色、金属度、粗糙度等参数。您还可以添加纹理贴图，如漫反射贴图、法线贴图和高光贴图。

将自定义材质应用到对象上，只需将材质从资产面板拖放到对象上，或在网格渲染器组件中选择该材质。"

### 8. 添加光源和摄像机（13:30 - 15:30）

**画面**：演示添加和配置光源与摄像机

**旁白**：
"光源和摄像机是场景中的关键元素。光源提供照明，而摄像机定义了用户的视角。

要添加光源，点击'创建 > 光源'，然后选择光源类型：
- 方向光：模拟远距离光源，如太阳
- 点光源：从一个点向所有方向发射光线
- 聚光灯：创建锥形光束
- 区域光：从一个矩形区域发射光线

选择光源后，您可以在属性面板中调整其属性，如颜色、强度、范围和阴影设置。

要添加摄像机，点击'创建 > 摄像机'。新摄像机将出现在场景中，您可以调整其位置和旋转来设置视角。

在属性面板中，您可以配置摄像机属性，如视野、裁剪平面、投影类型（透视或正交）等。

要通过特定摄像机预览场景，选择该摄像机，然后点击场景视图顶部的'通过选定摄像机预览'按钮，或按Ctrl+Shift+F键。

您可以在场景中设置多个摄像机，并在运行时切换它们，以创建不同的视角和镜头效果。"

### 9. 保存和加载场景（15:30 - 17:00）

**画面**：演示保存和加载场景

**旁白**：
"完成场景编辑后，保存您的工作非常重要。

要保存场景，点击'文件 > 保存场景'，或按Ctrl+S。如果是首次保存，您需要输入场景名称和选择保存位置。

DL（Digital Learning）引擎还提供自动保存功能，可以在'编辑 > 首选项 > 常规'中配置。建议启用自动保存，并设置适当的时间间隔，如5分钟。

要加载现有场景，点击'文件 > 打开场景'，然后浏览并选择场景文件。您也可以在项目面板中双击场景文件直接打开它。

如果您想在当前项目中管理多个场景，可以使用场景管理器。点击'窗口 > 场景管理器'打开场景管理器面板，您可以在其中查看、加载和管理项目中的所有场景。

场景可以作为预制体保存，允许您在其他场景中重用它们。选择要保存为预制体的对象，右键点击并选择'创建预制体'，然后在资产面板中保存预制体。"

### 10. 总结和下一步（17:00 - 18:00）

**画面**：展示完成的场景，然后回到编辑器主界面

**旁白**：
"恭喜！您现在已经学习了DL（Digital Learning）引擎编辑器中场景编辑的基础知识。我们涵盖了创建和组织对象、使用变换工具、管理层次结构、添加网格和材质，以及保存和加载场景等内容。

这些基础技能将帮助您开始创建自己的3D场景。随着您的进步，您可以探索更高级的功能，如光照烘焙、地形编辑、粒子系统、物理模拟和动画等。

在下一个教程中，我们将学习如何为场景添加交互性，使用视觉脚本系统创建行为和逻辑。

感谢观看！如果您有任何问题，请查阅文档或访问我们的社区论坛。祝您创作愉快！"

## 视频制作注意事项

1. **步调**：保持适中的步调，给观众足够的时间理解每个概念和操作。

2. **屏幕录制**：
   - 使用1080p或更高分辨率
   - 确保鼠标光标清晰可见
   - 放大显示重要UI元素和操作

3. **音频质量**：
   - 使用高质量麦克风录制旁白
   - 确保背景无噪音
   - 语速适中，发音清晰

4. **视觉辅助**：
   - 使用文字标注强调关键概念
   - 为重要操作添加视觉提示（如鼠标点击效果）
   - 在复杂操作前后使用暂停和放大效果

5. **示例场景**：
   - 使用简单但有吸引力的示例场景
   - 确保最终场景视觉上令人满意
   - 考虑提供完成的场景文件供下载

6. **字幕**：
   - 添加字幕以提高可访问性
   - 提供多语言字幕选项

## 相关资源

- [编辑器界面文档](../getting-started/editor-interface.md)
- [变换工具参考](../getting-started/transform-tools.md)
- [材质编辑指南](../features/material-editing.md)
- [光照系统文档](../features/lighting-system.md)
- [摄像机设置指南](../features/camera-system.md)
