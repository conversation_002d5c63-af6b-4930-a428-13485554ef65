/**
 * 输入录制器示例
 * 演示如何使用输入录制器录制和回放输入
 */
import {
  Engine,
  World,
  Scene,
  Camera,
  Renderer,
  InputSystem,
  InputManager,
  InputDevice,
  InputAction,
  InputBinding,
  InputMapping,
  InputComponent,
  ButtonInputAction,
  ValueInputAction,
  VectorInputAction,
  ButtonInputMapping,
  AxisInputMapping,
  VectorInputMapping,
  KeyboardDevice,
  MouseDevice,
  GamepadDevice,
  TouchDevice,
  InputMappingType,
  InputActionType
} from '../src';
import { InputRecorder, InputRecording } from '../src/input/InputRecorder';

// 创建引擎
const engine = new Engine();

// 创建世界
const world = new World();
engine.setWorld(world);

// 创建输入系统
const inputSystem = new InputSystem();
world.addSystem(inputSystem);

// 创建输入管理器
const inputManager = InputManager.getInstance();
inputManager.initialize();

// 创建输入录制器
const inputRecorder = new InputRecorder();

// 当前录制
let currentRecording: InputRecording | null = null;

// 创建渲染系统
const renderer = new Renderer({
  width: window.innerWidth,
  height: window.innerHeight,
  antialias: true
});
document.body.appendChild(renderer.getDomElement());

// 创建场景
const scene = new Scene();
world.addEntity(scene);

// 创建相机
const camera = new Camera({
  type: 'perspective',
  fov: 75,
  aspect: window.innerWidth / window.innerHeight,
  near: 0.1,
  far: 1000
});
camera.position.set(0, 5, 10);
camera.lookAt(0, 0, 0);
world.addEntity(camera);

// 创建玩家实体
const player = world.createEntity();
player.name = '玩家';

// 创建输入动作
const moveAction = new VectorInputAction('move');
const jumpAction = new ButtonInputAction('jump');
const fireAction = new ButtonInputAction('fire');
const aimAction = new ValueInputAction('aim');

// 创建输入映射
const keyboardMoveMapping = new VectorInputMapping(
  'keyboardMove',
  'keyboard',
  'KeyD', // 右
  'KeyW', // 上
  1,
  0.1
);

const keyboardJumpMapping = new ButtonInputMapping(
  'keyboardJump',
  'keyboard',
  'Space'
);

const mouseFireMapping = new ButtonInputMapping(
  'mouseFire',
  'mouse',
  'button:0' // 左键
);

const mouseAimMapping = new AxisInputMapping(
  'mouseAim',
  'mouse',
  'wheel:delta',
  0.01
);

// 创建输入绑定
const moveBinding = new InputBinding('move', 'keyboardMove');
const jumpBinding = new InputBinding('jump', 'keyboardJump');
const fireBinding = new InputBinding('fire', 'mouseFire');
const aimBinding = new InputBinding('aim', 'mouseAim');

// 创建输入组件
const inputComponent = new InputComponent(player, {
  actions: [moveAction, jumpAction, fireAction, aimAction],
  bindings: [moveBinding, jumpBinding, fireBinding, aimBinding],
  mappings: [keyboardMoveMapping, keyboardJumpMapping, mouseFireMapping, mouseAimMapping]
});

// 添加输入组件到玩家实体
player.addComponent(inputComponent);

// 创建UI元素
const createUI = () => {
  const container = document.createElement('div');
  container.style.position = 'absolute';
  container.style.top = '10px';
  container.style.left = '10px';
  container.style.color = 'white';
  container.style.fontFamily = 'Arial, sans-serif';
  container.style.fontSize = '14px';
  container.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
  container.style.padding = '10px';
  container.style.borderRadius = '5px';
  container.style.width = '300px';

  const title = document.createElement('h2');
  title.textContent = '输入录制器示例';
  title.style.margin = '0 0 10px 0';
  container.appendChild(title);

  const recordingInfo = document.createElement('div');
  recordingInfo.id = 'recordingInfo';
  recordingInfo.textContent = '未录制';
  container.appendChild(recordingInfo);

  const recordingButtons = document.createElement('div');
  recordingButtons.style.marginBottom = '10px';
  
  const startRecordingButton = document.createElement('button');
  startRecordingButton.textContent = '开始录制';
  startRecordingButton.onclick = startRecording;
  recordingButtons.appendChild(startRecordingButton);
  
  const stopRecordingButton = document.createElement('button');
  stopRecordingButton.textContent = '停止录制';
  stopRecordingButton.onclick = stopRecording;
  recordingButtons.appendChild(stopRecordingButton);
  
  const playRecordingButton = document.createElement('button');
  playRecordingButton.textContent = '回放录制';
  playRecordingButton.onclick = playRecording;
  recordingButtons.appendChild(playRecordingButton);
  
  const saveRecordingButton = document.createElement('button');
  saveRecordingButton.textContent = '保存录制';
  saveRecordingButton.onclick = saveRecording;
  recordingButtons.appendChild(saveRecordingButton);
  
  const loadRecordingButton = document.createElement('button');
  loadRecordingButton.textContent = '加载录制';
  loadRecordingButton.onclick = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (file) {
        loadRecording(file);
      }
    };
    input.click();
  };
  recordingButtons.appendChild(loadRecordingButton);
  
  container.appendChild(recordingButtons);

  const inputInfo = document.createElement('div');
  inputInfo.id = 'inputInfo';
  container.appendChild(inputInfo);

  document.body.appendChild(container);
};

// 开始录制
const startRecording = () => {
  inputRecorder.startRecording();
  
  // 更新UI
  const recordingInfo = document.getElementById('recordingInfo');
  if (recordingInfo) {
    recordingInfo.textContent = '正在录制...';
    recordingInfo.style.color = 'red';
  }
};

// 停止录制
const stopRecording = () => {
  currentRecording = inputRecorder.stopRecording();
  
  // 更新UI
  const recordingInfo = document.getElementById('recordingInfo');
  if (recordingInfo) {
    if (currentRecording) {
      const duration = (currentRecording.endTimestamp - currentRecording.startTimestamp) / 1000;
      recordingInfo.textContent = `录制完成: ${duration.toFixed(2)}秒, ${currentRecording.events.length}个事件`;
      recordingInfo.style.color = 'green';
    } else {
      recordingInfo.textContent = '未录制';
      recordingInfo.style.color = 'white';
    }
  }
};

// 回放录制
const playRecording = () => {
  if (!currentRecording) {
    alert('没有可回放的录制');
    return;
  }
  
  // 更新UI
  const recordingInfo = document.getElementById('recordingInfo');
  if (recordingInfo) {
    recordingInfo.textContent = '正在回放...';
    recordingInfo.style.color = 'blue';
  }
  
  // 开始回放
  inputRecorder.startPlayback(currentRecording, () => {
    // 回放完成
    if (recordingInfo) {
      const duration = (currentRecording!.endTimestamp - currentRecording!.startTimestamp) / 1000;
      recordingInfo.textContent = `回放完成: ${duration.toFixed(2)}秒, ${currentRecording!.events.length}个事件`;
      recordingInfo.style.color = 'green';
    }
  });
};

// 保存录制
const saveRecording = () => {
  if (!currentRecording) {
    alert('没有可保存的录制');
    return;
  }
  
  InputRecorder.saveToFile(currentRecording, 'input-recording.json');
};

// 加载录制
const loadRecording = (file: File) => {
  InputRecorder.loadFromFile(file)
    .then((recording) => {
      currentRecording = recording;
      
      // 更新UI
      const recordingInfo = document.getElementById('recordingInfo');
      if (recordingInfo) {
        const duration = (currentRecording.endTimestamp - currentRecording.startTimestamp) / 1000;
        recordingInfo.textContent = `已加载录制: ${duration.toFixed(2)}秒, ${currentRecording.events.length}个事件`;
        recordingInfo.style.color = 'green';
      }
    })
    .catch((error) => {
      console.error('加载录制失败:', error);
      alert('加载录制失败: ' + error.message);
    });
};

// 更新UI
const updateUI = () => {
  const inputInfo = document.getElementById('inputInfo');
  if (!inputInfo) return;

  const move = moveAction.getValue();
  const jump = jumpAction.isPressed();
  const fire = fireAction.isPressed();
  const aim = aimAction.getValue();

  let html = '';
  html += `<h3>输入状态</h3>`;
  html += `<div>移动: [${move[0].toFixed(2)}, ${move[1].toFixed(2)}]</div>`;
  html += `<div>跳跃: ${jump ? '按下' : '未按下'}</div>`;
  html += `<div>开火: ${fire ? '按下' : '未按下'}</div>`;
  html += `<div>瞄准: ${aim.toFixed(2)}</div>`;

  inputInfo.innerHTML = html;
};

// 创建UI
createUI();

// 游戏循环
const gameLoop = () => {
  // 更新输入管理器
  inputManager.update(engine.deltaTime);

  // 更新输入组件
  inputComponent.update(engine.deltaTime);

  // 更新UI
  updateUI();

  // 更新世界
  world.update(engine.deltaTime);

  // 渲染场景
  renderer.render(scene, camera);

  // 请求下一帧
  requestAnimationFrame(gameLoop);
};

// 开始游戏循环
gameLoop();

// 窗口大小调整
window.addEventListener('resize', () => {
  renderer.setSize(window.innerWidth, window.innerHeight);
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
});
