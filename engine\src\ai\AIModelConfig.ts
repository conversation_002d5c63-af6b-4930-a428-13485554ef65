/**
 * AI模型配置
 */
export interface AIModelConfig {
  /** 模型版本 */
  version?: string;
  
  /** 模型变体 */
  variant?: string;
  
  /** 模型温度 (0-1) */
  temperature?: number;
  
  /** 最大令牌数 */
  maxTokens?: number;
  
  /** 是否使用流式响应 */
  stream?: boolean;
  
  /** 批处理大小 */
  batchSize?: number;
  
  /** 是否使用量化 */
  quantized?: boolean;
  
  /** 量化位数 */
  quantizationBits?: 8 | 16 | 32;
  
  /** 是否使用GPU加速 */
  useGPU?: boolean;
  
  /** 使用的GPU设备ID */
  gpuDeviceId?: number;
  
  /** 模型API密钥 */
  apiKey?: string;
  
  /** 模型API基础URL */
  baseUrl?: string;
  
  /** 模型路径 */
  modelPath?: string;
  
  /** 是否使用本地模型 */
  useLocalModel?: boolean;
  
  /** 模型缓存大小 */
  cacheSize?: number;
  
  /** 是否启用调试 */
  debug?: boolean;
  
  /** 自定义配置 */
  [key: string]: any;
}
