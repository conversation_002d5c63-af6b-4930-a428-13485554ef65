/**
 * 性能优化标签页组件
 * 集成所有性能优化相关面板
 */
import React, { useState } from 'react';
import { Tabs } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  DashboardOutlined,
  AppstoreOutlined,
  DesktopOutlined,
  RocketOutlined,
} from '@ant-design/icons';
import PerformanceOptimizationPanel from './PerformanceOptimizationPanel';
import ResourceOptimizationPanel from './ResourceOptimizationPanel';
import UIOptimizationPanel from './UIOptimizationPanel';
import './PerformanceOptimizationTabs.less';

const { TabPane } = Tabs;

/**
 * 性能优化标签页组件
 */
const PerformanceOptimizationTabs: React.FC = () => {
  const { t } = useTranslation();
  const [activeKey, setActiveKey] = useState('performance');
  
  return (
    <div className="performance-optimization-tabs">
      <Tabs activeKey={activeKey} onChange={setActiveKey}>
        <TabPane 
          tab={<span><DashboardOutlined />{t('debug.tabs.performance')}</span>} 
          key="performance"
        >
          <PerformanceOptimizationPanel />
        </TabPane>
        <TabPane 
          tab={<span><AppstoreOutlined />{t('debug.tabs.resources')}</span>} 
          key="resources"
        >
          <ResourceOptimizationPanel />
        </TabPane>
        <TabPane 
          tab={<span><DesktopOutlined />{t('debug.tabs.ui')}</span>} 
          key="ui"
        >
          <UIOptimizationPanel />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default PerformanceOptimizationTabs;
