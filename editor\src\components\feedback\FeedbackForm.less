.feedback-form {
  width: 100%;
  max-width: 600px;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  .feedback-form-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    position: relative;

    h2 {
      margin: 0;
      flex-grow: 1;
    }

    .feedback-subtype {
      background-color: #f0f0f0;
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      margin-left: 8px;
    }

    .close-button {
      position: absolute;
      right: 0;
      top: 0;
    }
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-upload-list {
    margin-top: 8px;
  }

  .ant-rate {
    font-size: 24px;
  }

  .ant-checkbox-wrapper {
    margin-bottom: 8px;
  }

  .context-preview-collapse {
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    padding: 8px;
    background-color: #fafafa;

    .context-preview {
      padding: 8px;

      .context-item {
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .screenshot-preview {
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    overflow: hidden;

    .screenshot-preview-header {
      padding: 8px 12px;
      background-color: #fafafa;
      border-bottom: 1px solid #f0f0f0;
    }

    .screenshot-preview-image {
      padding: 8px;
      text-align: center;

      img {
        max-width: 100%;
        max-height: 300px;
        border: 1px solid #d9d9d9;
        border-radius: 2px;
      }
    }
  }
}

// 暗色主题适配
.dark-theme {
  .feedback-form {
    background-color: #1f1f1f;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

    .feedback-form-header {
      .feedback-subtype {
        background-color: #2a2a2a;
      }
    }

    .context-preview-collapse {
      border-color: #303030;
      background-color: #1a1a1a;
    }

    .screenshot-preview {
      border-color: #303030;

      .screenshot-preview-header {
        background-color: #1a1a1a;
        border-color: #303030;
      }

      .screenshot-preview-image img {
        border-color: #303030;
      }
    }
  }
}

.feedback-button {
  margin-right: 8px;
}

.feedback-popover {
  max-width: 800px;

  .ant-popover-inner-content {
    padding: 0;
  }
}
