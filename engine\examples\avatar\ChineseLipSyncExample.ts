/**
 * 中文口型同步示例
 * 演示如何使用中文口型同步系统
 */
import * as THREE from 'three';
import { Engine } from '../../core/Engine';
import { World } from '../../core/World';
import { Entity } from '../../core/Entity';
import { FacialAnimationComponent, VisemeType } from '../../avatar/components/FacialAnimationComponent';
import { FacialAnimationSystem } from '../../avatar/systems/FacialAnimationSystem';
import { FacialAnimationModelAdapterSystem, FacialAnimationModelType } from '../../avatar/systems/FacialAnimationModelAdapterSystem';
import { ChineseLipSyncSystem } from '../../avatar/systems/ChineseLipSyncSystem';
import { OrbitControls } from '../../utils/OrbitControls';

/**
 * 中文口型同步示例
 */
export class ChineseLipSyncExample {
  /** 引擎 */
  private engine: Engine;
  /** 世界 */
  private world: World;
  /** 场景 */
  private scene: THREE.Scene;
  /** 相机 */
  private camera: THREE.PerspectiveCamera;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer;
  /** 控制器 */
  private controls: OrbitControls;
  /** 角色实体 */
  private characterEntity: Entity;
  /** 面部动画系统 */
  private facialAnimationSystem: FacialAnimationSystem;
  /** 面部动画模型适配器系统 */
  private modelAdapterSystem: FacialAnimationModelAdapterSystem;
  /** 中文口型同步系统 */
  private chineseLipSyncSystem: ChineseLipSyncSystem;
  /** 是否运行中 */
  private running: boolean = false;
  /** 文本输入元素 */
  private textInput: HTMLInputElement | null = null;
  /** 语言选择元素 */
  private languageSelect: HTMLSelectElement | null = null;
  /** 分析按钮 */
  private analyzeButton: HTMLButtonElement | null = null;
  /** 当前语言 */
  private currentLanguage: string = 'zh-CN';
  
  /**
   * 构造函数
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    this.world = this.engine.world;
    
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x333333);
    
    // 创建相机
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.camera.position.set(0, 1.6, 2);
    
    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    document.body.appendChild(this.renderer.domElement);
    
    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 1.6, 0);
    this.controls.update();
    
    // 创建角色实体
    this.characterEntity = this.world.createEntity();
    
    // 创建面部动画系统
    this.facialAnimationSystem = new FacialAnimationSystem(this.world, {
      debug: true,
      autoDetectAudio: true
    });
    
    // 创建面部动画模型适配器系统
    this.modelAdapterSystem = new FacialAnimationModelAdapterSystem(this.world, {
      debug: true,
      defaultModelType: FacialAnimationModelType.GLTF,
      autoDetectBlendShapes: true
    });
    
    // 创建中文口型同步系统
    this.chineseLipSyncSystem = new ChineseLipSyncSystem(this.world, {
      debug: true,
      usePinyinAnalysis: true,
      useToneAnalysis: false,
      useSpeechRecognition: true,
      speechRecognitionLang: 'zh-CN',
      usePhonemeAnalysis: true,
      useSmoothTransition: true,
      transitionTime: 0.1,
      useContextPrediction: true
    });
    
    // 添加系统到世界
    this.world.addSystem(this.facialAnimationSystem);
    this.world.addSystem(this.modelAdapterSystem);
    this.world.addSystem(this.chineseLipSyncSystem);
    
    // 创建灯光
    this.createLights();
    
    // 创建UI
    this.createUI();
    
    // 加载模型
    this.loadModel();
    
    // 添加窗口调整事件
    window.addEventListener('resize', this.onWindowResize.bind(this));
  }
  
  /**
   * 创建灯光
   */
  private createLights(): void {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(ambientLight);
    
    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 2, 3);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);
  }
  
  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    const uiContainer = document.createElement('div');
    uiContainer.style.position = 'absolute';
    uiContainer.style.top = '10px';
    uiContainer.style.left = '10px';
    uiContainer.style.padding = '10px';
    uiContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    uiContainer.style.borderRadius = '5px';
    uiContainer.style.color = 'white';
    document.body.appendChild(uiContainer);
    
    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '中文口型同步示例';
    title.style.margin = '0 0 10px 0';
    uiContainer.appendChild(title);
    
    // 创建语言选择
    const languageLabel = document.createElement('label');
    languageLabel.textContent = '语言: ';
    uiContainer.appendChild(languageLabel);
    
    this.languageSelect = document.createElement('select');
    this.languageSelect.style.margin = '0 10px 10px 0';
    this.languageSelect.style.padding = '5px';
    
    const zhOption = document.createElement('option');
    zhOption.value = 'zh-CN';
    zhOption.textContent = '中文';
    this.languageSelect.appendChild(zhOption);
    
    const enOption = document.createElement('option');
    enOption.value = 'en-US';
    enOption.textContent = '英文';
    this.languageSelect.appendChild(enOption);
    
    this.languageSelect.addEventListener('change', this.onLanguageChange.bind(this));
    uiContainer.appendChild(this.languageSelect);
    uiContainer.appendChild(document.createElement('br'));
    
    // 创建文本输入
    const textLabel = document.createElement('label');
    textLabel.textContent = '文本: ';
    uiContainer.appendChild(textLabel);
    
    this.textInput = document.createElement('input');
    this.textInput.type = 'text';
    this.textInput.value = '你好，世界！';
    this.textInput.style.width = '200px';
    this.textInput.style.margin = '0 10px 10px 0';
    this.textInput.style.padding = '5px';
    uiContainer.appendChild(this.textInput);
    uiContainer.appendChild(document.createElement('br'));
    
    // 创建分析按钮
    this.analyzeButton = document.createElement('button');
    this.analyzeButton.textContent = '分析文本';
    this.analyzeButton.style.padding = '5px 10px';
    this.analyzeButton.style.margin = '10px 0';
    this.analyzeButton.addEventListener('click', this.onAnalyzeText.bind(this));
    uiContainer.appendChild(this.analyzeButton);
  }
  
  /**
   * 加载模型
   */
  private loadModel(): void {
    // 加载模型
    // 这里是加载模型的占位代码，实际实现需要根据具体模型
    // TODO: 实现模型加载
    
    // 创建临时几何体
    const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
    const material = new THREE.MeshStandardMaterial({ color: 0x00ff00 });
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.set(0, 1.6, 0);
    this.scene.add(mesh);
    
    // 创建面部动画组件
    const facialAnimation = this.facialAnimationSystem.createFacialAnimation(this.characterEntity);
    
    // 创建口型同步组件
    const lipSync = this.chineseLipSyncSystem.createLipSync(this.characterEntity);
    
    console.log('模型加载完成，已创建面部动画组件和口型同步组件');
  }
  
  /**
   * 窗口调整事件处理
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }
  
  /**
   * 语言变更事件处理
   */
  private onLanguageChange(event: Event): void {
    if (this.languageSelect) {
      this.currentLanguage = this.languageSelect.value;
      
      // 更新口型同步系统的语言
      this.chineseLipSyncSystem.config.speechRecognitionLang = this.currentLanguage;
      
      // 更新示例文本
      if (this.textInput) {
        if (this.currentLanguage === 'zh-CN') {
          this.textInput.value = '你好，世界！';
        } else {
          this.textInput.value = 'Hello, world!';
        }
      }
    }
  }
  
  /**
   * 分析文本事件处理
   */
  private onAnalyzeText(): void {
    if (this.textInput) {
      const text = this.textInput.value.trim();
      
      if (text) {
        // 分析文本
        if (this.currentLanguage === 'zh-CN') {
          this.chineseLipSyncSystem['analyzeChinesePhonemes'](text);
        } else {
          this.chineseLipSyncSystem['analyzeEnglishPhonemes'](text);
        }
      }
    }
  }
  
  /**
   * 启动
   */
  public start(): void {
    if (this.running) return;
    
    this.running = true;
    this.animate();
  }
  
  /**
   * 停止
   */
  public stop(): void {
    this.running = false;
  }
  
  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;
    
    requestAnimationFrame(this.animate.bind(this));
    
    // 更新控制器
    this.controls.update();
    
    // 更新世界
    this.world.update();
    
    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }
}
