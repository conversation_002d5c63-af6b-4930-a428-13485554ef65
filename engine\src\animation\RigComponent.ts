/**
 * 骨骼绑定组件
 * 管理实体与骨骼的映射关系
 */

import { Component } from '../core/Component';

export interface BoneMapping {
  /** 实体ID */
  entityId: string;
  /** 骨骼名称 */
  boneName: string;
  /** 权重 */
  weight?: number;
}

export class RigComponent extends Component {
  public static readonly type: string = 'Rig';
  
  /** 实体到骨骼的映射 */
  public entitiesToBones: Record<string, string> = {};
  
  /** 骨骼到实体的映射 */
  public bonesToEntities: Record<string, string> = {};
  
  /** 骨骼映射列表 */
  private boneMappings: BoneMapping[] = [];
  
  /** 根骨骼名称 */
  public rootBone: string = '';

  constructor() {
    super(RigComponent.type);
  }

  /**
   * 添加骨骼映射
   */
  public addBoneMapping(entityId: string, boneName: string, weight: number = 1.0): void {
    // 添加到映射表
    this.entitiesToBones[entityId] = boneName;
    this.bonesToEntities[boneName] = entityId;
    
    // 添加到映射列表
    const mapping: BoneMapping = {
      entityId,
      boneName,
      weight
    };
    
    this.boneMappings.push(mapping);
  }

  /**
   * 移除骨骼映射
   */
  public removeBoneMapping(entityId: string): void {
    const boneName = this.entitiesToBones[entityId];
    if (boneName) {
      delete this.entitiesToBones[entityId];
      delete this.bonesToEntities[boneName];
      
      // 从映射列表中移除
      this.boneMappings = this.boneMappings.filter(mapping => mapping.entityId !== entityId);
    }
  }

  /**
   * 根据实体ID获取骨骼名称
   */
  public getBoneNameByEntity(entityId: string): string | null {
    return this.entitiesToBones[entityId] || null;
  }

  /**
   * 根据骨骼名称获取实体ID
   */
  public getEntityByBoneName(boneName: string): string | null {
    return this.bonesToEntities[boneName] || null;
  }

  /**
   * 获取所有骨骼映射
   */
  public getBoneMappings(): BoneMapping[] {
    return [...this.boneMappings];
  }

  /**
   * 设置根骨骼
   */
  public setRootBone(boneName: string): void {
    this.rootBone = boneName;
  }

  /**
   * 获取根骨骼
   */
  public getRootBone(): string {
    return this.rootBone;
  }

  /**
   * 清除所有映射
   */
  public clearMappings(): void {
    this.entitiesToBones = {};
    this.bonesToEntities = {};
    this.boneMappings = [];
  }

  /**
   * 检查是否有指定的骨骼映射
   */
  public hasBoneMapping(entityId: string): boolean {
    return entityId in this.entitiesToBones;
  }

  /**
   * 检查是否有指定的实体映射
   */
  public hasEntityMapping(boneName: string): boolean {
    return boneName in this.bonesToEntities;
  }

  /**
   * 获取映射数量
   */
  public getMappingCount(): number {
    return this.boneMappings.length;
  }

  /**
   * 从配置对象创建映射
   */
  public fromConfig(config: {
    entitiesToBones?: Record<string, string>;
    rootBone?: string;
  }): void {
    this.clearMappings();
    
    if (config.entitiesToBones) {
      for (const [entityId, boneName] of Object.entries(config.entitiesToBones)) {
        this.addBoneMapping(entityId, boneName);
      }
    }
    
    if (config.rootBone) {
      this.setRootBone(config.rootBone);
    }
  }

  /**
   * 导出为配置对象
   */
  public toConfig(): {
    entitiesToBones: Record<string, string>;
    rootBone: string;
  } {
    return {
      entitiesToBones: { ...this.entitiesToBones },
      rootBone: this.rootBone
    };
  }

  /**
   * 克隆组件
   */
  public clone(): RigComponent {
    const cloned = new RigComponent();
    cloned.fromConfig(this.toConfig());
    return cloned;
  }
}
