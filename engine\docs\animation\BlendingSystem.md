# 动画混合系统

动画混合系统是DL（Digital Learning）引擎中的核心组件之一，用于控制和混合多个动画，以创建平滑、自然的角色动画。本文档介绍了动画混合系统的架构、功能和使用方法。

## 目录

- [架构概述](#架构概述)
- [核心组件](#核心组件)
  - [AnimationBlender](#animationblender)
  - [AnimationMask](#animationmask)
  - [SubClip](#subclip)
  - [PhysicsAnimationIntegration](#physicsanimationintegration)
  - [InputAnimationIntegration](#inputanimationintegration)
- [基本用法](#基本用法)
  - [创建混合器](#创建混合器)
  - [添加混合层](#添加混合层)
  - [应用混合层](#应用混合层)
- [高级功能](#高级功能)
  - [混合空间](#混合空间)
  - [动画遮罩](#动画遮罩)
  - [子片段](#子片段)
  - [物理集成](#物理集成)
  - [输入集成](#输入集成)
- [示例](#示例)
- [最佳实践](#最佳实践)
- [常见问题](#常见问题)

## 架构概述

动画混合系统基于层次结构，每个动画作为一个层，具有权重、混合模式和过渡时间。系统支持多种混合模式，包括覆盖、叠加和混合。此外，系统还支持动画遮罩、子片段和与物理/输入系统的集成。

## 核心组件

### AnimationBlender

`AnimationBlender` 是动画混合系统的核心类，负责管理和混合动画层。

**主要功能**：

- 添加、移除和清除动画层
- 设置混合曲线和混合模式
- 创建和管理混合空间
- 创建和应用动画遮罩
- 创建和管理子片段
- 与物理和输入系统集成

**示例**：

```typescript
// 创建混合器
const blender = new AnimationBlender(animator);

// 添加层
blender.addLayer('idle', 1.0, BlendMode.OVERRIDE, 0.3);
blender.addLayer('wave', 0.5, BlendMode.ADDITIVE, 0.2);

// 应用混合层
blender.applyLayers();
```

### AnimationMask

`AnimationMask` 用于控制动画对骨骼的影响，可以创建上半身、下半身、左手、右手等遮罩。

**主要功能**：

- 创建包含或排除特定骨骼的遮罩
- 支持二进制或平滑权重
- 应用遮罩到动画片段
- 提供预设遮罩（上半身、下半身、左手、右手）

**示例**：

```typescript
// 创建上半身遮罩
const upperBodyMask = blender.createUpperBodyMask();

// 应用遮罩到动画片段
blender.applyMaskToClip('attack', 'upperBody');
```

### SubClip

`SubClip` 用于从动画片段中提取子片段，可以创建入场、循环、退场等子片段。

**主要功能**：

- 从源片段创建子片段
- 设置开始和结束时间
- 支持循环和反向播放
- 提供预设子片段（动作、循环、入场、退场）

**示例**：

```typescript
// 创建循环子片段
const loopName = blender.createLoopSubClip('dance', 1.0, 3.0);

// 播放子片段
blender.addLayer(loopName, 1.0, BlendMode.OVERRIDE, 0.3);
```

### PhysicsAnimationIntegration

`PhysicsAnimationIntegration` 用于将物理系统与动画系统集成，实现物理驱动的动画。

**主要功能**：

- 根据物理状态更新动画参数
- 支持物理驱动骨骼
- 处理碰撞事件
- 检测接触地面状态

**示例**：

```typescript
// 集成物理系统
const physicsIntegration = blender.integratePhysics(entity, physicsSystem, {
  debug: true,
  autoUpdateParameters: true
});

// 更新物理集成
physicsIntegration.update(deltaTime);
```

### InputAnimationIntegration

`InputAnimationIntegration` 用于将输入系统与动画系统集成，实现输入驱动的动画。

**主要功能**：

- 根据输入状态更新动画参数
- 支持键盘、鼠标、手柄、触摸等输入
- 支持手势和语音输入
- 处理输入事件

**示例**：

```typescript
// 集成输入系统
const inputIntegration = blender.integrateInput(entity, inputSystem, {
  debug: true,
  autoUpdateParameters: true
});

// 更新输入集成
inputIntegration.update(deltaTime);
```

## 基本用法

### 创建混合器

首先，需要创建一个动画混合器：

```typescript
// 创建动画控制器
const animator = new Animator(entity);

// 加载动画
animator.addClip(idleClip);
animator.addClip(walkClip);
animator.addClip(runClip);

// 创建混合器
const blender = new AnimationBlender(animator);
```

### 添加混合层

然后，添加混合层：

```typescript
// 添加基础层
blender.addLayer('idle', 1.0, BlendMode.OVERRIDE, 0.3);

// 添加叠加层
blender.addLayer('wave', 0.5, BlendMode.ADDITIVE, 0.2);
```

### 应用混合层

最后，应用混合层：

```typescript
// 应用混合层
blender.applyLayers();

// 在每一帧更新动画
animator.update(deltaTime);
```

## 高级功能

### 混合空间

混合空间是一种高级混合技术，可以根据参数在多个动画之间进行插值。

**1D混合空间**：

```typescript
// 创建1D混合空间
blender.updateFromBlendSpace1D(
  'locomotion',
  0.5,  // 位置
  0.0,  // 最小值
  1.0,  // 最大值
  ['walk', 'run'],  // 动画片段
  [0.0, 1.0]  // 位置列表
);
```

**2D混合空间**：

```typescript
// 创建2D混合空间
blender.updateFromBlendSpace2D(
  'locomotion2D',
  { x: 0.5, y: 0.5 },  // 位置
  -1.0,  // 最小X值
  1.0,   // 最大X值
  -1.0,  // 最小Y值
  1.0,   // 最大Y值
  ['idle', 'walk', 'run', 'sprint'],  // 动画片段
  [
    { x: 0.0, y: 0.0 },
    { x: 0.5, y: 0.0 },
    { x: 1.0, y: 0.0 },
    { x: 1.0, y: 1.0 }
  ],  // 位置列表
  true  // 使用三角形混合
);
```

### 动画遮罩

动画遮罩用于控制动画对骨骼的影响。

**创建遮罩**：

```typescript
// 创建上半身遮罩
const upperBodyMask = blender.createUpperBodyMask();

// 创建自定义遮罩
const customMask = blender.createMask(
  'custom',
  MaskType.INCLUDE,
  ['spine', 'spine1', 'spine2', 'rightShoulder', 'rightArm', 'rightForeArm', 'rightHand'],
  MaskWeightType.SMOOTH
);
```

**应用遮罩**：

```typescript
// 应用遮罩到动画片段
blender.applyMaskToClip('attack', 'upperBody');

// 播放带遮罩的动画
blender.addLayer('attack_upperBody', 1.0, BlendMode.ADDITIVE, 0.3);
```

### 子片段

子片段用于从动画片段中提取部分动画。

**创建子片段**：

```typescript
// 创建子片段
const jumpStartName = blender.createSubClip(
  'jump_start',
  'jump',
  0,
  0.5,
  false
);

// 创建循环子片段
const danceLoopName = blender.createLoopSubClip(
  'dance',
  1.0,
  3.0
);
```

**播放子片段**：

```typescript
// 播放子片段
blender.addLayer(jumpStartName, 1.0, BlendMode.OVERRIDE, 0.3);
```

### 物理集成

物理集成用于将物理系统与动画系统集成。

**创建物理集成**：

```typescript
// 集成物理系统
const physicsIntegration = blender.integratePhysics(entity, physicsSystem, {
  debug: true,
  autoUpdateParameters: true,
  useCharacterController: true
});
```

**更新物理集成**：

```typescript
// 更新物理集成
physicsIntegration.update(deltaTime);
```

### 输入集成

输入集成用于将输入系统与动画系统集成。

**创建输入集成**：

```typescript
// 集成输入系统
const inputIntegration = blender.integrateInput(entity, inputSystem, {
  debug: true,
  autoUpdateParameters: true,
  moveActionName: 'move',
  jumpActionName: 'jump'
});
```

**更新输入集成**：

```typescript
// 更新输入集成
inputIntegration.update(deltaTime);
```

## 示例

请参考 `examples/animation/AdvancedBlendingExample.ts` 文件，了解如何使用动画混合系统的高级功能。

## 最佳实践

- 使用混合空间来实现平滑的动画过渡
- 使用动画遮罩来实现部分身体的动画
- 使用子片段来重用动画的一部分
- 集成物理和输入系统，实现更自然的动画
- 使用适当的混合模式和曲线，实现更平滑的过渡
- 避免过多的混合层，以提高性能

## 常见问题

**问题**：动画混合不平滑，有跳跃感。

**解答**：尝试增加过渡时间，或者使用不同的混合曲线。

**问题**：动画遮罩不起作用。

**解答**：确保骨骼名称正确，并且遮罩类型（包含/排除）设置正确。

**问题**：物理集成导致角色抖动。

**解答**：调整物理参数，如阻尼和弹性，或者减少物理驱动骨骼的数量。

**问题**：输入集成响应延迟。

**解答**：检查输入系统的配置，或者减少输入处理的复杂度。
