/**
 * 示例项目过滤器样式
 */
@import '../../styles/variables.less';

.example-filter {
  width: 100%;
  transition: all 0.3s;

  .filter-header {
    display: flex;
    align-items: center;
    gap: 8px;

    .expand-button {
      display: flex;
      align-items: center;
    }

    .clear-button {
      display: flex;
      align-items: center;
    }
  }

  .filter-content {
    margin-top: 16px;
    padding: 16px;
    background-color: @component-background;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .filter-section {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      h4 {
        margin: 0 0 8px;
        font-size: 14px;
        font-weight: 600;
        color: @heading-color;
      }

      .category-group {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .category-button {
          margin-right: 0;
          border-radius: 4px;

          &.selected {
            color: @primary-color;
            background-color: @primary-1;
            border-color: @primary-color;
          }
        }
      }

      .difficulty-group {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
    }
  }

  &.expanded {
    margin-bottom: 16px;
  }
}
