/**
 * 高级情感分析器
 * 用于分析文本中的复杂情感和情感变化
 */
import { AIModelType } from '../../ai/AIModelType';
import { AIModelFactory } from '../../ai/AIModelFactory';
import { BatchProcessor } from '../../ai/BatchProcessor';
import { AIModelCache } from '../../ai/AIModelCache';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 情感分析请求
 */
export interface EmotionAnalysisRequest {
  /** 文本 */
  text: string;
  /** 是否包含次要情感 */
  includeSecondary?: boolean;
  /** 是否包含情感变化 */
  includeChanges?: boolean;
  /** 详细程度 */
  detail?: 'low' | 'medium' | 'high';
  /** 情感类别 */
  emotionCategories?: string[];
  /** 是否使用上下文 */
  useContext?: boolean;
  /** 上下文文本 */
  contextText?: string;
  /** 语言 */
  language?: string;
  /** 自定义选项 */
  customOptions?: Record<string, any>;
}

/**
 * 情感分析结果
 */
export interface EmotionAnalysisResult {
  /** 主要情感 */
  primaryEmotion: string;
  /** 主要情感强度 */
  primaryIntensity: number;
  /** 次要情感 */
  secondaryEmotion?: string;
  /** 次要情感强度 */
  secondaryIntensity?: number;
  /** 情感分数 */
  scores: Record<string, number>;
  /** 情感变化 */
  emotionChanges?: EmotionChangeResult;
  /** 详细情感 */
  detailedEmotions?: DetailedEmotionResult;
  /** 分析时间（毫秒） */
  analysisTime?: number;
  /** 置信度 */
  confidence?: number;
}

/**
 * 情感变化结果
 */
export interface EmotionChangeResult {
  /** 情感转换 */
  transitions: EmotionTransition[];
  /** 情感时间线 */
  timeline: EmotionTimePoint[];
}

/**
 * 情感转换
 */
export interface EmotionTransition {
  /** 起始情感 */
  from: string;
  /** 目标情感 */
  to: string;
  /** 概率 */
  probability: number;
}

/**
 * 情感时间点
 */
export interface EmotionTimePoint {
  /** 时间（0-1） */
  time: number;
  /** 情感 */
  emotion: string;
  /** 强度 */
  intensity: number;
}

/**
 * 详细情感结果
 */
export interface DetailedEmotionResult {
  /** 主要情感 */
  primary: {
    /** 类别 */
    category: string;
    /** 子类别 */
    subcategories: Record<string, string[]>;
  };
  /** 强度 */
  intensity: {
    /** 整体强度 */
    overall: number;
    /** 唤醒度 */
    arousal: number;
    /** 效价 */
    valence: number;
  };
  /** 置信度 */
  confidence: number;
}

/**
 * 高级情感分析器配置
 */
export interface AdvancedEmotionAnalyzerConfig {
  /** 模型类型 */
  modelType?: AIModelType;
  /** 模型变体 */
  modelVariant?: string;
  /** 是否使用本地模型 */
  useLocalModel?: boolean;
  /** 模型路径 */
  modelPath?: string;
  /** 是否使用GPU */
  useGPU?: boolean;
  /** 是否使用量化模型 */
  useQuantized?: boolean;
  /** 量化位数 */
  quantizationBits?: 8 | 16 | 32;
  /** 批处理大小 */
  batchSize?: number;
  /** 情感类别 */
  emotionCategories?: string[];
  /** 是否使用缓存 */
  useCache?: boolean;
  /** 缓存大小 */
  cacheSize?: number;
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否使用批处理 */
  useBatchProcessing?: boolean;
  /** 最大等待时间（毫秒） */
  maxWaitTime?: number;
  /** 是否使用上下文 */
  useContext?: boolean;
  /** 上下文窗口大小 */
  contextWindowSize?: number;
}

/**
 * 高级情感分析器
 */
export class AdvancedEmotionAnalyzer {
  /** 配置 */
  private config: AdvancedEmotionAnalyzerConfig;
  
  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: AdvancedEmotionAnalyzerConfig = {
    modelType: AIModelType.BERT,
    modelVariant: 'base',
    useLocalModel: false,
    useGPU: false,
    useQuantized: false,
    quantizationBits: 8,
    batchSize: 1,
    emotionCategories: [
      'happy', 'sad', 'angry', 'surprised', 'fear', 'disgust', 'neutral'
    ],
    useCache: true,
    cacheSize: 100,
    debug: false,
    useBatchProcessing: true,
    maxWaitTime: 100,
    useContext: false,
    contextWindowSize: 3
  };
  
  /** 模型工厂 */
  private modelFactory: AIModelFactory;
  
  /** 模型 */
  private model: any = null;
  
  /** 是否已初始化 */
  private initialized: boolean = false;
  
  /** 是否正在初始化 */
  private initializing: boolean = false;
  
  /** 缓存 */
  private cache: AIModelCache<EmotionAnalysisResult>;
  
  /** 批处理器 */
  private batchProcessor: BatchProcessor<EmotionAnalysisRequest, EmotionAnalysisResult> | null = null;
  
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  
  /** 上下文历史 */
  private contextHistory: string[] = [];
  
  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: AdvancedEmotionAnalyzerConfig = {}) {
    this.config = {
      ...AdvancedEmotionAnalyzer.DEFAULT_CONFIG,
      ...config
    };
    
    // 创建模型工厂
    this.modelFactory = new AIModelFactory({
      debug: this.config.debug,
      useLocalModels: this.config.useLocalModel
    });
    
    // 创建缓存
    this.cache = new AIModelCache<EmotionAnalysisResult>({
      maxSize: this.config.cacheSize,
      debug: this.config.debug
    });
    
    // 如果启用批处理，创建批处理器
    if (this.config.useBatchProcessing) {
      this.batchProcessor = new BatchProcessor<EmotionAnalysisRequest, EmotionAnalysisResult>(
        this.processBatch.bind(this),
        {
          maxBatchSize: this.config.batchSize || 1,
          maxWaitTime: this.config.maxWaitTime,
          debug: this.config.debug
        }
      );
    }
  }
  
  /**
   * 初始化
   * @returns 是否成功初始化
   */
  public async initialize(): Promise<boolean> {
    if (this.initialized) {
      return true;
    }
    
    if (this.initializing) {
      // 等待初始化完成
      return new Promise<boolean>((resolve) => {
        const checkInterval = setInterval(() => {
          if (this.initialized) {
            clearInterval(checkInterval);
            resolve(true);
          }
        }, 100);
      });
    }
    
    this.initializing = true;
    
    try {
      if (this.config.debug) {
        console.log('初始化高级情感分析器');
      }
      
      // 创建模型
      this.model = this.modelFactory.createModel(
        this.config.modelType || AIModelType.BERT,
        {
          version: this.config.modelVariant,
          useGPU: this.config.useGPU,
          useQuantized: this.config.useQuantized,
          quantizationBits: this.config.quantizationBits
        }
      );
      
      // 初始化模型
      if (this.model) {
        await this.model.initialize();
      } else {
        throw new Error('创建模型失败');
      }
      
      this.initialized = true;
      this.initializing = false;
      
      if (this.config.debug) {
        console.log('高级情感分析器初始化成功');
      }
      
      return true;
    } catch (error) {
      this.initializing = false;
      console.error('初始化高级情感分析器失败:', error);
      return false;
    }
  }
  
  /**
   * 分析情感
   * @param request 请求
   * @returns 分析结果
   */
  public async analyzeEmotion(request: EmotionAnalysisRequest): Promise<EmotionAnalysisResult> {
    // 确保已初始化
    if (!this.initialized) {
      await this.initialize();
    }
    
    // 规范化文本
    const normalizedText = this.normalizeText(request.text);
    
    // 如果文本为空，返回默认结果
    if (!normalizedText) {
      return this.createDefaultResult();
    }
    
    // 检查缓存
    if (this.config.useCache) {
      const cacheKey = this.generateCacheKey(request);
      const cachedResult = this.cache.get(cacheKey);
      
      if (cachedResult) {
        if (this.config.debug) {
          console.log('使用缓存的情感分析结果');
        }
        
        return cachedResult;
      }
    }
    
    try {
      let result: EmotionAnalysisResult;
      
      // 如果启用批处理，使用批处理器
      if (this.config.useBatchProcessing && this.batchProcessor) {
        result = await this.batchProcessor.process(request);
      } else {
        // 否则，直接处理
        result = await this.processRequest(request);
      }
      
      // 更新缓存
      if (this.config.useCache) {
        const cacheKey = this.generateCacheKey(request);
        this.cache.set(cacheKey, result);
      }
      
      // 更新上下文历史
      if (this.config.useContext) {
        this.updateContextHistory(normalizedText);
      }
      
      return result;
    } catch (error) {
      console.error('分析情感失败:', error);
      throw error;
    }
  }
  
  /**
   * 处理请求
   * @param request 请求
   * @returns 分析结果
   */
  private async processRequest(request: EmotionAnalysisRequest): Promise<EmotionAnalysisResult> {
    const startTime = Date.now();
    
    try {
      // 准备输入文本
      let inputText = request.text;
      
      // 如果启用上下文，添加上下文
      if (this.config.useContext && request.useContext !== false) {
        inputText = this.addContextToText(inputText, request.contextText);
      }
      
      // 调用模型分析情感
      const modelResult = await this.model.analyzeEmotion(inputText);
      
      // 处理模型结果
      const result = this.processModelResult(modelResult, request);
      
      // 添加分析时间
      result.analysisTime = Date.now() - startTime;
      
      return result;
    } catch (error) {
      console.error('处理情感分析请求失败:', error);
      throw error;
    }
  }
  
  /**
   * 处理批次
   * @param requests 请求批次
   * @returns 分析结果批次
   */
  private async processBatch(requests: EmotionAnalysisRequest[]): Promise<EmotionAnalysisResult[]> {
    try {
      // 处理每个请求
      const results: EmotionAnalysisResult[] = [];
      
      for (const request of requests) {
        const result = await this.processRequest(request);
        results.push(result);
      }
      
      return results;
    } catch (error) {
      console.error('处理情感分析批次失败:', error);
      throw error;
    }
  }
  
  /**
   * 处理模型结果
   * @param modelResult 模型结果
   * @param request 请求
   * @returns 分析结果
   */
  private processModelResult(modelResult: any, request: EmotionAnalysisRequest): EmotionAnalysisResult {
    // 创建基本结果
    const result: EmotionAnalysisResult = {
      primaryEmotion: modelResult.primaryEmotion || 'neutral',
      primaryIntensity: modelResult.primaryIntensity || 0.5,
      scores: modelResult.scores || {}
    };
    
    // 添加次要情感
    if (request.includeSecondary && modelResult.secondaryEmotion) {
      result.secondaryEmotion = modelResult.secondaryEmotion;
      result.secondaryIntensity = modelResult.secondaryIntensity;
    }
    
    // 添加情感变化
    if (request.includeChanges) {
      result.emotionChanges = modelResult.emotionChanges || this.generateEmotionChanges(result);
    }
    
    // 添加详细情感
    if (request.detail === 'high') {
      result.detailedEmotions = modelResult.detailedEmotions || this.generateDetailedEmotions(result);
    }
    
    // 添加置信度
    result.confidence = modelResult.confidence || 0.8;
    
    return result;
  }
  
  /**
   * 生成情感变化
   * @param result 基本结果
   * @returns 情感变化结果
   */
  private generateEmotionChanges(result: EmotionAnalysisResult): EmotionChangeResult {
    // 获取主要和次要情感
    const primaryEmotion = result.primaryEmotion;
    const primaryIntensity = result.primaryIntensity;
    const secondaryEmotion = result.secondaryEmotion || 'neutral';
    const secondaryIntensity = result.secondaryIntensity || 0.3;
    
    // 创建情感转换
    const transitions: EmotionTransition[] = [
      {
        from: 'neutral',
        to: primaryEmotion,
        probability: primaryIntensity
      }
    ];
    
    // 如果有次要情感，添加转换
    if (result.secondaryEmotion) {
      transitions.push({
        from: primaryEmotion,
        to: secondaryEmotion,
        probability: secondaryIntensity
      });
    }
    
    // 创建时间线
    const timeline: EmotionTimePoint[] = [
      { time: 0.0, emotion: 'neutral', intensity: 0.8 },
      { time: 0.3, emotion: primaryEmotion, intensity: primaryIntensity }
    ];
    
    // 如果有次要情感，添加时间点
    if (result.secondaryEmotion) {
      timeline.push({ time: 0.7, emotion: secondaryEmotion, intensity: secondaryIntensity });
      timeline.push({ time: 1.0, emotion: primaryEmotion, intensity: primaryIntensity * 0.9 });
    } else {
      timeline.push({ time: 1.0, emotion: primaryEmotion, intensity: primaryIntensity });
    }
    
    return {
      transitions,
      timeline
    };
  }
  
  /**
   * 生成详细情感
   * @param result 基本结果
   * @returns 详细情感结果
   */
  private generateDetailedEmotions(result: EmotionAnalysisResult): DetailedEmotionResult {
    // 获取主要情感
    const primaryEmotion = result.primaryEmotion;
    
    // 创建子类别映射
    const subcategories: Record<string, string[]> = {
      'happy': ['joyful', 'content', 'excited'],
      'sad': ['melancholic', 'disappointed', 'grieving'],
      'angry': ['irritated', 'furious', 'indignant'],
      'surprised': ['amazed', 'astonished', 'shocked'],
      'fear': ['anxious', 'terrified', 'worried'],
      'disgust': ['repulsed', 'revolted', 'averse'],
      'neutral': ['calm', 'indifferent', 'composed']
    };
    
    // 计算强度值
    const overallIntensity = result.primaryIntensity;
    const arousal = this.calculateArousal(result);
    const valence = this.calculateValence(result);
    
    return {
      primary: {
        category: primaryEmotion,
        subcategories
      },
      intensity: {
        overall: overallIntensity,
        arousal,
        valence
      },
      confidence: result.confidence || 0.8
    };
  }
  
  /**
   * 计算唤醒度
   * @param result 分析结果
   * @returns 唤醒度
   */
  private calculateArousal(result: EmotionAnalysisResult): number {
    // 情感唤醒度映射
    const arousalMap: Record<string, number> = {
      'happy': 0.7,
      'sad': 0.3,
      'angry': 0.9,
      'surprised': 0.8,
      'fear': 0.8,
      'disgust': 0.6,
      'neutral': 0.2
    };
    
    // 获取主要情感的唤醒度
    const primaryArousal = arousalMap[result.primaryEmotion] || 0.5;
    
    // 如果有次要情感，计算加权平均
    if (result.secondaryEmotion) {
      const secondaryArousal = arousalMap[result.secondaryEmotion] || 0.5;
      const primaryWeight = result.primaryIntensity;
      const secondaryWeight = result.secondaryIntensity || 0.3;
      const totalWeight = primaryWeight + secondaryWeight;
      
      return (primaryArousal * primaryWeight + secondaryArousal * secondaryWeight) / totalWeight;
    }
    
    return primaryArousal;
  }
  
  /**
   * 计算效价
   * @param result 分析结果
   * @returns 效价
   */
  private calculateValence(result: EmotionAnalysisResult): number {
    // 情感效价映射
    const valenceMap: Record<string, number> = {
      'happy': 0.9,
      'sad': 0.2,
      'angry': 0.1,
      'surprised': 0.6,
      'fear': 0.2,
      'disgust': 0.1,
      'neutral': 0.5
    };
    
    // 获取主要情感的效价
    const primaryValence = valenceMap[result.primaryEmotion] || 0.5;
    
    // 如果有次要情感，计算加权平均
    if (result.secondaryEmotion) {
      const secondaryValence = valenceMap[result.secondaryEmotion] || 0.5;
      const primaryWeight = result.primaryIntensity;
      const secondaryWeight = result.secondaryIntensity || 0.3;
      const totalWeight = primaryWeight + secondaryWeight;
      
      return (primaryValence * primaryWeight + secondaryValence * secondaryWeight) / totalWeight;
    }
    
    return primaryValence;
  }
  
  /**
   * 创建默认结果
   * @returns 默认结果
   */
  private createDefaultResult(): EmotionAnalysisResult {
    return {
      primaryEmotion: 'neutral',
      primaryIntensity: 0.5,
      scores: { 'neutral': 1.0 },
      confidence: 1.0
    };
  }
  
  /**
   * 规范化文本
   * @param text 文本
   * @returns 规范化后的文本
   */
  private normalizeText(text: string): string {
    return text.trim();
  }
  
  /**
   * 生成缓存键
   * @param request 请求
   * @returns 缓存键
   */
  private generateCacheKey(request: EmotionAnalysisRequest): string {
    // 基本键
    let key = `emotion:${request.text}`;
    
    // 添加选项
    if (request.includeSecondary) key += ':secondary';
    if (request.includeChanges) key += ':changes';
    if (request.detail) key += `:detail=${request.detail}`;
    if (request.useContext) key += ':context';
    if (request.language) key += `:lang=${request.language}`;
    
    return key;
  }
  
  /**
   * 更新上下文历史
   * @param text 文本
   */
  private updateContextHistory(text: string): void {
    // 添加到历史
    this.contextHistory.push(text);
    
    // 如果超过窗口大小，移除最早的
    if (this.contextHistory.length > (this.config.contextWindowSize || 3)) {
      this.contextHistory.shift();
    }
  }
  
  /**
   * 添加上下文到文本
   * @param text 文本
   * @param contextText 额外上下文文本
   * @returns 带上下文的文本
   */
  private addContextToText(text: string, contextText?: string): string {
    // 如果历史为空且没有额外上下文，直接返回原文本
    if (this.contextHistory.length === 0 && !contextText) {
      return text;
    }
    
    // 合并历史和额外上下文
    const contextParts: string[] = [...this.contextHistory];
    if (contextText) {
      contextParts.push(contextText);
    }
    
    // 限制上下文长度
    const maxContextLength = 500;
    let context = contextParts.join(' ');
    if (context.length > maxContextLength) {
      context = context.substring(context.length - maxContextLength);
    }
    
    // 返回带上下文的文本
    return `${context} ${text}`;
  }
  
  /**
   * 清空上下文历史
   */
  public clearContextHistory(): void {
    this.contextHistory = [];
  }
  
  /**
   * 获取上下文历史
   * @returns 上下文历史
   */
  public getContextHistory(): string[] {
    return [...this.contextHistory];
  }
  
  /**
   * 销毁
   */
  public dispose(): void {
    // 释放模型
    if (this.model) {
      (this.model as any).dispose();
      this.model = null;
    }
    
    // 释放批处理器
    if (this.batchProcessor) {
      (this.batchProcessor as any).dispose();
      this.batchProcessor = null;
    }
    
    // 释放缓存
    (this.cache as any).dispose();
    
    // 清空上下文历史
    this.clearContextHistory();
    
    // 清空事件监听器
    this.eventEmitter.removeAllListeners();
    
    // 重置状态
    this.initialized = false;
    this.initializing = false;
  }
}
