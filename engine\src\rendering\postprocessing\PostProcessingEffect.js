"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostProcessingEffect = void 0;
/**
 * 后处理效果
 */
var PostProcessingEffect = /** @class */ (function () {
    /**
     * 创建后处理效果
     * @param options 后处理效果选项
     */
    function PostProcessingEffect(options) {
        /** 渲染器 */
        this.renderer = null;
        /** 通道 */
        this.pass = null;
        /** 宽度 */
        this.width = 1;
        /** 高度 */
        this.height = 1;
        /** 是否已初始化 */
        this.initialized = false;
        /** 是否已销毁 */
        this.destroyed = false;
        this.name = options.name;
        this.enabled = options.enabled !== undefined ? options.enabled : true;
    }
    /**
     * 初始化效果
     * @param renderer 渲染器
     * @param width 宽度
     * @param height 高度
     */
    PostProcessingEffect.prototype.initialize = function (renderer, width, height) {
        if (this.initialized || this.destroyed)
            return;
        this.renderer = renderer;
        this.width = width;
        this.height = height;
        // 创建通道
        this.createPass();
        // 设置通道是否启用
        if (this.pass) {
            this.pass.enabled = this.enabled;
        }
        this.initialized = true;
    };
    /**
     * 更新效果
     * @param deltaTime 帧间隔时间（秒）
     */
    PostProcessingEffect.prototype.update = function (deltaTime) {
        // 子类可以重写此方法
    };
    /**
     * 调整大小
     * @param width 宽度
     * @param height 高度
     */
    PostProcessingEffect.prototype.resize = function (width, height) {
        this.width = width;
        this.height = height;
        // 子类可以重写此方法
    };
    /**
     * 获取效果名称
     * @returns 效果名称
     */
    PostProcessingEffect.prototype.getName = function () {
        return this.name;
    };
    /**
     * 设置是否启用
     * @param enabled 是否启用
     */
    PostProcessingEffect.prototype.setEnabled = function (enabled) {
        this.enabled = enabled;
        if (this.pass) {
            this.pass.enabled = enabled;
        }
    };
    /**
     * 获取是否启用
     * @returns 是否启用
     */
    PostProcessingEffect.prototype.isEnabled = function () {
        return this.enabled;
    };
    /**
     * 获取通道
     * @returns 通道
     */
    PostProcessingEffect.prototype.getPass = function () {
        return this.pass;
    };
    /**
     * 销毁效果
     */
    PostProcessingEffect.prototype.dispose = function () {
        if (this.destroyed)
            return;
        // 销毁通道
        if (this.pass) {
            this.pass.dispose();
            this.pass = null;
        }
        this.renderer = null;
        this.initialized = false;
        this.destroyed = true;
    };
    return PostProcessingEffect;
}());
exports.PostProcessingEffect = PostProcessingEffect;
