/**
 * 面部动画预设管理器样式
 */
.facial-animation-preset-manager {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .preset-manager-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .preset-list {
    flex: 1;
    overflow-y: auto;
    
    .ant-card {
      height: 100%;
      
      .ant-card-meta-description {
        height: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      
      .preset-tags {
        margin-top: 8px;
        
        .ant-tag {
          margin-bottom: 4px;
        }
      }
    }
  }
  
  .preset-preview {
    display: flex;
    flex-direction: column;
    
    .facial-animation-preview {
      height: 300px;
      margin-bottom: 16px;
    }
    
    .preset-details {
      padding: 0 16px;
      
      h3 {
        margin-bottom: 8px;
      }
      
      .preset-info {
        margin: 16px 0;
        
        div {
          margin-bottom: 4px;
        }
      }
      
      .preset-tags {
        margin-bottom: 16px;
        
        .ant-tag {
          margin-bottom: 4px;
        }
      }
    }
  }
  
  .ant-upload-drag {
    padding: 16px;
  }
}

/* 响应式布局 */
@media (min-width: 768px) {
  .facial-animation-preset-manager {
    .preset-preview {
      flex-direction: row;
      
      .facial-animation-preview {
        width: 60%;
        height: 400px;
        margin-bottom: 0;
      }
      
      .preset-details {
        width: 40%;
        padding: 0 0 0 16px;
      }
    }
  }
}
