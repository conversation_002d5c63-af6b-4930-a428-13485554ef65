/**
 * 权限管理面板组件
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Table,
  Select,
  Button,
  Space,
  Tag,
  Switch,
  Tooltip,
  Divider,
  Modal,
  message,
  Typography,
  Collapse
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  SettingOutlined,
  PlusOutlined,
  DeleteOutlined,
  QuestionCircleOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
  HistoryOutlined,
  ApartmentOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import {
  selectAllUsers,
  selectAllRoles,
  selectUserPermissions
} from '../../store/collaboration/permissionSlice';
import { selectUsers } from '../../store/collaboration/collaborationSlice';
import { CollaborationRole, CollaborationUser, collaborationService } from '../../services/CollaborationService';
import { Permission, permissionService } from '../../services/PermissionService';
import { permissionCheckService } from '../../services/PermissionCheckService';
import PermissionLogPanel from './PermissionLogPanel';
import OrganizationPermissionPanel from './OrganizationPermissionPanel';

const { TabPane } = Tabs;
const { Option } = Select;
const { Text, Title } = Typography;
const { Panel } = Collapse;
const { confirm } = Modal;

// 权限分类
const permissionCategories = {
  basic: [Permission.VIEW_SCENE, Permission.EDIT_SCENE],
  entity: [Permission.CREATE_ENTITY, Permission.UPDATE_ENTITY, Permission.DELETE_ENTITY],
  component: [Permission.ADD_COMPONENT, Permission.UPDATE_COMPONENT, Permission.REMOVE_COMPONENT],
  asset: [Permission.UPLOAD_ASSET, Permission.DELETE_ASSET],
  scene: [Permission.SAVE_SCENE, Permission.EXPORT_SCENE, Permission.IMPORT_SCENE],
  user: [Permission.MANAGE_USERS, Permission.ASSIGN_ROLES, Permission.MANAGE_PERMISSIONS],
  project: [Permission.MANAGE_PROJECT, Permission.DELETE_PROJECT],
};

/**
 * 权限管理面板组件
 */
const PermissionPanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 从Redux获取状态
  const users = useSelector(selectUsers);
  const allPermissionUsers = useSelector(selectAllUsers);
  const allRoles = useSelector(selectAllRoles);
  const currentUserId = collaborationService.getUserId();

  // 本地状态
  const [activeTab, setActiveTab] = useState<string>('users');
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [selectedRole, setSelectedRole] = useState<CollaborationRole>(CollaborationRole.VIEWER);
  const [selectedPermission, setSelectedPermission] = useState<Permission | ''>('');

  // 当用户列表变化时，自动选择第一个用户
  useEffect(() => {
    if (users.length > 0 && !selectedUserId) {
      setSelectedUserId(users[0].id);
    }
  }, [users, selectedUserId]);

  // 获取当前选中用户的权限信息
  const userPermissions = useSelector(selectUserPermissions(selectedUserId));

  // 检查当前用户是否有管理权限
  const canManagePermissions = permissionCheckService.canManagePermissions();
  const canAssignRoles = permissionCheckService.canAssignRoles();

  // 处理角色变更
  const handleRoleChange = (userId: string, role: CollaborationRole) => {
    // 确认对话框
    confirm({
      title: t('permission.confirmations.changeRole'),
      icon: <ExclamationCircleOutlined />,
      content: t('permission.confirmations.changeRole'),
      onOk() {
        permissionService.setUserRole(userId, role, currentUserId);
        message.success(t('permission.success.roleChanged'));
      },
    });
  };

  // 处理添加自定义权限
  const handleAddCustomPermission = () => {
    if (!selectedUserId || !selectedPermission) {
      return;
    }

    permissionService.addUserCustomPermission(selectedUserId, selectedPermission as Permission, currentUserId);
    message.success(t('permission.success.permissionAdded'));
    setSelectedPermission('');
  };

  // 处理移除自定义权限
  const handleRemoveCustomPermission = (userId: string, permission: Permission) => {
    confirm({
      title: t('permission.confirmations.removePermission'),
      icon: <ExclamationCircleOutlined />,
      content: t('permission.confirmations.removePermission'),
      onOk() {
        permissionService.removeUserCustomPermission(userId, permission, currentUserId);
        message.success(t('permission.success.permissionRemoved'));
      },
    });
  };

  // 渲染用户权限管理标签页
  const renderUserPermissionsTab = () => {
    return (
      <div className="permission-users-tab">
        <div style={{ display: 'flex', marginBottom: 16 }}>
          <Select
            style={{ width: '100%' }}
            value={selectedUserId}
            onChange={setSelectedUserId}
            placeholder={t('permission.selectUser')}
          >
            {users.map(user => (
              <Option key={user.id} value={user.id}>
                {user.name} {user.id === currentUserId && `(${t('collaboration.you')})`}
              </Option>
            ))}
          </Select>
        </div>

        {selectedUserId && (
          <>
            <Card
              title={t('permission.roles.title')}
              size="small"
              extra={
                <Tooltip title={canAssignRoles ? '' : t('permission.errors.noPermission', { permission: t('permission.types.assign_roles') })}>
                  <Select
                    value={userPermissions?.role || CollaborationRole.VIEWER}
                    onChange={(role) => handleRoleChange(selectedUserId, role as CollaborationRole)}
                    disabled={!canAssignRoles || selectedUserId === currentUserId}
                    style={{ width: 120 }}
                  >
                    <Option value={CollaborationRole.VIEWER}>{t('permission.roles.viewer')}</Option>
                    <Option value={CollaborationRole.EDITOR}>{t('permission.roles.editor')}</Option>
                    <Option value={CollaborationRole.ADMIN}>{t('permission.roles.admin')}</Option>
                    <Option value={CollaborationRole.OWNER}>{t('permission.roles.owner')}</Option>
                  </Select>
                </Tooltip>
              }
            >
              <Text>{t(`permission.roles.description.${userPermissions?.role || CollaborationRole.VIEWER}`)}</Text>
            </Card>

            <Divider orientation="left">{t('permission.title')}</Divider>

            <div style={{ marginBottom: 16 }}>
              <Space>
                <Select
                  style={{ width: 200 }}
                  value={selectedPermission}
                  onChange={setSelectedPermission}
                  placeholder={t('permission.selectPermission')}
                  disabled={!canManagePermissions}
                >
                  <Option value="">{t('permission.selectPermission')}</Option>
                  {Object.entries(permissionCategories).map(([category, permissions]) => (
                    <Select.OptGroup key={category} label={t(`permission.categories.${category}`)}>
                      {permissions.map(permission => (
                        <Option key={permission} value={permission}>
                          {t(`permission.types.${permission}`)}
                        </Option>
                      ))}
                    </Select.OptGroup>
                  ))}
                </Select>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddCustomPermission}
                  disabled={!canManagePermissions || !selectedPermission}
                >
                  {t('permission.actions.addPermission')}
                </Button>
              </Space>
            </div>

            <Collapse defaultActiveKey={['basic']}>
              {Object.entries(permissionCategories).map(([category, permissions]) => (
                <Panel header={t(`permission.categories.${category}`)} key={category}>
                  <Table
                    dataSource={permissions.map(permission => ({
                      key: permission,
                      permission,
                      name: t(`permission.types.${permission}`),
                      hasPermission: userPermissions?.permissions.includes(permission) || userPermissions?.customPermissions.includes(permission),
                      isCustom: userPermissions?.customPermissions.includes(permission),
                      isInherited: userPermissions?.permissions.includes(permission) && !userPermissions?.customPermissions.includes(permission),
                    }))}
                    pagination={false}
                    size="small"
                    columns={[
                      {
                        title: t('permission.permissionName'),
                        dataIndex: 'name',
                        key: 'name',
                      },
                      {
                        title: t('permission.status'),
                        key: 'status',
                        render: (_, record) => (
                          <Space>
                            {record.hasPermission ? (
                              <Tag color="success" icon={<CheckCircleOutlined />}>
                                {t('permission.granted')}
                              </Tag>
                            ) : (
                              <Tag color="error" icon={<DeleteOutlined />}>
                                {t('permission.denied')}
                              </Tag>
                            )}
                            {record.isInherited && (
                              <Tooltip title={t('permission.tooltips.inheritedPermission')}>
                                <Tag color="processing" icon={<InfoCircleOutlined />}>
                                  {t('permission.inherited')}
                                </Tag>
                              </Tooltip>
                            )}
                            {record.isCustom && (
                              <Tooltip title={t('permission.tooltips.customPermission')}>
                                <Tag color="warning" icon={<SettingOutlined />}>
                                  {t('permission.custom')}
                                </Tag>
                              </Tooltip>
                            )}
                          </Space>
                        ),
                      },
                      {
                        title: t('permission.actions.title'),
                        key: 'action',
                        render: (_, record) => (
                          <Space>
                            {record.isCustom && (
                              <Button
                                type="text"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => handleRemoveCustomPermission(selectedUserId, record.permission)}
                                disabled={!canManagePermissions}
                              />
                            )}
                          </Space>
                        ),
                      },
                    ]}
                  />
                </Panel>
              ))}
            </Collapse>
          </>
        )}
      </div>
    );
  };

  // 渲染角色权限管理标签页
  const renderRolePermissionsTab = () => {
    return (
      <div className="permission-roles-tab">
        <Collapse defaultActiveKey={['viewer']}>
          {Object.values(CollaborationRole).map(role => (
            <Panel
              header={
                <Space>
                  <Text strong>{t(`permission.roles.${role}`)}</Text>
                  <Text type="secondary">{t(`permission.roles.description.${role}`)}</Text>
                </Space>
              }
              key={role}
            >
              <Table
                dataSource={Object.entries(permissionCategories).flatMap(([category, permissions]) =>
                  permissions.map(permission => ({
                    key: `${role}-${permission}`,
                    category,
                    permission,
                    name: t(`permission.types.${permission}`),
                    hasPermission: allRoles.find(r => r.role === role)?.permissions.includes(permission) || false,
                  }))
                )}
                pagination={false}
                size="small"
                columns={[
                  {
                    title: t('permission.category'),
                    dataIndex: 'category',
                    key: 'category',
                    render: (category) => t(`permission.categories.${category}`),
                  },
                  {
                    title: t('permission.permissionName'),
                    dataIndex: 'name',
                    key: 'name',
                  },
                  {
                    title: t('permission.status'),
                    key: 'status',
                    render: (_, record) => (
                      <Switch
                        checked={record.hasPermission}
                        disabled={!canManagePermissions}
                        onChange={(checked) => {
                          const rolePermissions = allRoles.find(r => r.role === role)?.permissions || [];
                          if (checked) {
                            permissionService.setRolePermissions(role as CollaborationRole,
                              [...rolePermissions, record.permission], currentUserId);
                          } else {
                            permissionService.setRolePermissions(role as CollaborationRole,
                              rolePermissions.filter(p => p !== record.permission), currentUserId);
                          }
                        }}
                      />
                    ),
                  },
                ]}
              />
            </Panel>
          ))}
        </Collapse>
      </div>
    );
  };

  return (
    <div className="permission-panel">
      <Card
        title={
          <Space>
            <LockOutlined />
            {t('permission.title')}
          </Space>
        }
        extra={
          <Switch
            checkedChildren={t('enabled')}
            unCheckedChildren={t('disabled')}
            checked={permissionCheckService.canEditScene()}
            onChange={(checked) => permissionCheckService.setEnabled(checked)}
            disabled={!canManagePermissions}
          />
        }
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
        >
          <TabPane
            tab={
              <span>
                <UserOutlined />
                {t('permission.userPermissions')}
              </span>
            }
            key="users"
          >
            {renderUserPermissionsTab()}
          </TabPane>

          <TabPane
            tab={
              <span>
                <SettingOutlined />
                {t('permission.rolePermissions')}
              </span>
            }
            key="roles"
          >
            {renderRolePermissionsTab()}
          </TabPane>

          <TabPane
            tab={
              <span>
                <HistoryOutlined />
                {t('permission.log.title')}
              </span>
            }
            key="logs"
          >
            <PermissionLogPanel />
          </TabPane>

          <TabPane
            tab={
              <span>
                <ApartmentOutlined />
                {t('organization.title')}
              </span>
            }
            key="organization"
          >
            <OrganizationPermissionPanel />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default PermissionPanel;
