/**
 * 动画事件示例
 * 展示如何使用动画事件系统
 */
import * as THREE from 'three';
import { Engine } from '../../core/Engine';
import { Entity } from '../../core/Entity';
import { World } from '../../core/World';
import { Camera } from '../../core/Camera';
import { Animator } from '../../animation/Animator';
import { AnimationClip, LoopMode } from '../../animation/AnimationClip';
import { AnimationEventSystem, AnimationEventType, AnimationEventData } from '../../animation/AnimationEvent';
import { GLTFLoader } from '../../gltf/GLTFLoader';
import { GLTFModelComponent } from '../../gltf/components/GLTFModelComponent';
import { GLTFAnimationComponent } from '../../gltf/components/GLTFAnimationComponent';
import { SkeletonAnimation } from '../../animation/SkeletonAnimation';
import { ParticleSystem } from '../../particle/ParticleSystem';
import { AudioSystem } from '../../audio/AudioSystem';

/**
 * 动画事件示例
 */
export class AnimationEventExample {
  /** 引擎 */
  private engine: Engine;
  /** 世界 */
  private world: World;
  /** 相机 */
  private camera: Camera;
  /** 模型实体 */
  private modelEntity: Entity;
  /** 动画事件系统 */
  private animationEventSystem: AnimationEventSystem;
  /** 粒子系统 */
  private particleSystem: ParticleSystem;
  /** 音频系统 */
  private audioSystem: AudioSystem;
  /** 是否运行中 */
  private running: boolean = false;
  /** 调试模式 */
  private debug: boolean = true;

  /**
   * 构造函数
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();

    // 获取世界
    this.world = this.engine.getWorld();

    // 创建相机
    this.camera = new Camera();
    this.camera.setPosition(new THREE.Vector3(0, 2, 5));
    this.camera.lookAt(new THREE.Vector3(0, 1, 0));

    // 创建模型实体
    this.modelEntity = this.world.createEntity();

    // 创建动画事件系统
    this.animationEventSystem = new AnimationEventSystem(this.world, this.debug);

    // 创建粒子系统
    this.particleSystem = new ParticleSystem(this.world);

    // 创建音频系统
    this.audioSystem = new AudioSystem(this.world);

    // 添加系统到世界
    this.world.addSystem(this.animationEventSystem);
    this.world.addSystem(this.particleSystem);
    this.world.addSystem(this.audioSystem);
  }

  /**
   * 启动示例
   */
  public start(): void {
    // 创建场景
    this.createScene();

    // 加载模型
    this.loadModel();

    // 启动引擎
    this.engine.start();

    // 开始动画循环
    this.running = true;
    this.animate();

    console.log('动画事件示例已启动');
  }

  /**
   * 停止示例
   */
  public stop(): void {
    this.running = false;
    this.engine.stop();

    console.log('动画事件示例已停止');
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 创建地面
    const groundEntity = this.world.createEntity();
    const groundGeometry = new THREE.PlaneGeometry(10, 10);
    const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x808080 });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.rotation.x = -Math.PI / 2;
    groundMesh.receiveShadow = true;

    // 添加到场景
    this.world.getScene().add(groundMesh);

    // 创建灯光
    const lightEntity = this.world.createEntity();
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(5, 10, 5);
    directionalLight.castShadow = true;

    // 设置阴影参数
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 50;
    directionalLight.shadow.camera.left = -10;
    directionalLight.shadow.camera.right = 10;
    directionalLight.shadow.camera.top = 10;
    directionalLight.shadow.camera.bottom = -10;

    // 添加到场景
    this.world.getScene().add(directionalLight);

    // 创建环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
    this.world.getScene().add(ambientLight);
  }

  /**
   * 加载模型
   */
  private loadModel(): void {
    // 添加GLTF模型组件
    const modelComponent = new GLTFModelComponent();
    this.modelEntity.addComponent(modelComponent);

    // 加载模型
    modelComponent.load('models/character.glb').then(() => {
      console.log('模型加载完成');

      // 获取动画组件
      const animationComponent = this.modelEntity.getComponent<GLTFAnimationComponent>(GLTFAnimationComponent.type);
      if (animationComponent) {
        // 获取所有动画剪辑
        const clips = animationComponent.getClips();
        console.log(`模型包含 ${clips.length} 个动画`);

        // 创建骨骼动画组件
        const skeletonAnimation = new SkeletonAnimation({
          entity: this.modelEntity,
          clips: clips
        });
        this.modelEntity.addComponent(skeletonAnimation);

        // 创建动画事件组件
        const eventComponent = this.animationEventSystem.createAnimationEvent(
          this.modelEntity,
          skeletonAnimation.getAnimator()
        );

        // 添加动画事件
        this.setupAnimationEvents(skeletonAnimation);

        // 播放动画
        skeletonAnimation.play('idle');
      }
    }).catch(error => {
      console.error('模型加载失败:', error);
    });
  }

  /**
   * 设置动画事件
   * @param skeletonAnimation 骨骼动画组件
   */
  private setupAnimationEvents(skeletonAnimation: SkeletonAnimation): void {
    // 获取动画片段
    const idleClip = skeletonAnimation.getClip('idle');
    const walkClip = skeletonAnimation.getClip('walk');
    const runClip = skeletonAnimation.getClip('run');
    const jumpClip = skeletonAnimation.getClip('jump');
    const attackClip = skeletonAnimation.getClip('attack');

    // 添加攻击动画事件
    if (attackClip) {
      // 添加攻击开始事件
      this.animationEventSystem.addEvent(this.modelEntity, 'attack', {
        type: AnimationEventType.CUSTOM,
        name: 'attack_start',
        time: 0.2,
        clipName: 'attack',
        params: { power: 10 }
      });

      // 添加攻击命中事件
      this.animationEventSystem.addEvent(this.modelEntity, 'attack', {
        type: AnimationEventType.CUSTOM,
        name: 'attack_hit',
        time: 0.5,
        clipName: 'attack',
        params: { damage: 20 }
      });

      // 添加攻击结束事件
      this.animationEventSystem.addEvent(this.modelEntity, 'attack', {
        type: AnimationEventType.CUSTOM,
        name: 'attack_end',
        time: 0.8,
        clipName: 'attack',
        params: { cooldown: 1.0 }
      });
    }

    // 添加跳跃动画事件
    if (jumpClip) {
      // 添加跳跃开始事件
      this.animationEventSystem.addEvent(this.modelEntity, 'jump', {
        type: AnimationEventType.CUSTOM,
        name: 'jump_start',
        time: 0.1,
        clipName: 'jump',
        params: { height: 2.0 }
      });

      // 添加跳跃顶点事件
      this.animationEventSystem.addEvent(this.modelEntity, 'jump', {
        type: AnimationEventType.CUSTOM,
        name: 'jump_apex',
        time: 0.5,
        clipName: 'jump',
        params: { }
      });

      // 添加跳跃着陆事件
      this.animationEventSystem.addEvent(this.modelEntity, 'jump', {
        type: AnimationEventType.CUSTOM,
        name: 'jump_land',
        time: 0.9,
        clipName: 'jump',
        params: { impact: 1.0 }
      });
    }

    // 添加事件监听器
    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听攻击事件
    this.animationEventSystem.addEventListener(this.modelEntity, 'attack_start', (event) => {
      console.log('攻击开始:', event);
      this.playSound('attack_swing');
      this.createParticleEffect('attack_start', new THREE.Vector3(1, 1, 0));
    });

    this.animationEventSystem.addEventListener(this.modelEntity, 'attack_hit', (event) => {
      console.log('攻击命中:', event);
      this.playSound('attack_hit');
      this.createParticleEffect('attack_hit', new THREE.Vector3(1.5, 1, 0));
    });

    this.animationEventSystem.addEventListener(this.modelEntity, 'attack_end', (event) => {
      console.log('攻击结束:', event);
    });

    // 监听跳跃事件
    this.animationEventSystem.addEventListener(this.modelEntity, 'jump_start', (event) => {
      console.log('跳跃开始:', event);
      this.playSound('jump');
    });

    this.animationEventSystem.addEventListener(this.modelEntity, 'jump_apex', (event) => {
      console.log('跳跃顶点:', event);
    });

    this.animationEventSystem.addEventListener(this.modelEntity, 'jump_land', (event) => {
      console.log('跳跃着陆:', event);
      this.playSound('land');
      this.createParticleEffect('dust', new THREE.Vector3(0, 0, 0));
    });
  }

  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;

    // 请求下一帧
    requestAnimationFrame(() => this.animate());

    // 更新引擎
    this.engine.update();
  }

  /**
   * 播放声音
   * @param soundName 声音名称
   */
  private playSound(soundName: string): void {
    // 检查音频系统
    if (!this.audioSystem) return;

    // 播放声音
    switch (soundName) {
      case 'attack_swing':
        this.audioSystem.playSound('sounds/attack_swing.mp3', { volume: 0.5 });
        break;
      case 'attack_hit':
        this.audioSystem.playSound('sounds/attack_hit.mp3', { volume: 0.8 });
        break;
      case 'jump':
        this.audioSystem.playSound('sounds/jump.mp3', { volume: 0.6 });
        break;
      case 'land':
        this.audioSystem.playSound('sounds/land.mp3', { volume: 0.7 });
        break;
      default:
        console.warn(`未知声音: ${soundName}`);
    }
  }

  /**
   * 创建粒子效果
   * @param effectName 效果名称
   * @param position 位置
   */
  private createParticleEffect(effectName: string, position: THREE.Vector3): void {
    // 检查粒子系统
    if (!this.particleSystem) return;

    // 获取模型位置
    const modelPosition = new THREE.Vector3();
    const transform = this.modelEntity.getTransform();
    if (transform) {
      modelPosition.copy(transform.getPosition());
    }

    // 计算效果位置
    const effectPosition = modelPosition.clone().add(position);

    // 创建粒子效果
    switch (effectName) {
      case 'attack_start':
        this.particleSystem.createEffect('slash', {
          position: effectPosition,
          count: 20,
          duration: 0.5,
          size: 0.1,
          color: new THREE.Color(0x00ffff),
          speed: 2.0
        });
        break;
      case 'attack_hit':
        this.particleSystem.createEffect('impact', {
          position: effectPosition,
          count: 50,
          duration: 0.8,
          size: 0.05,
          color: new THREE.Color(0xff0000),
          speed: 3.0
        });
        break;
      case 'dust':
        this.particleSystem.createEffect('dust', {
          position: new THREE.Vector3(effectPosition.x, 0.05, effectPosition.z),
          count: 30,
          duration: 1.0,
          size: 0.08,
          color: new THREE.Color(0xcccccc),
          speed: 1.0
        });
        break;
      default:
        console.warn(`未知粒子效果: ${effectName}`);
    }
  }

  /**
   * 播放动画
   * @param animationName 动画名称
   */
  public playAnimation(animationName: string): void {
    // 获取骨骼动画组件
    const skeletonAnimation = this.modelEntity.getComponent<SkeletonAnimation>(SkeletonAnimation.type);
    if (!skeletonAnimation) {
      console.warn('未找到骨骼动画组件');
      return;
    }

    // 播放动画
    skeletonAnimation.play(animationName);

    if (this.debug) {
      console.log(`播放动画: ${animationName}`);
    }
  }

  /**
   * 创建UI
   */
  public createUI(): void {
    // 创建按钮容器
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.bottom = '20px';
    container.style.left = '20px';
    container.style.display = 'flex';
    container.style.flexDirection = 'row';
    container.style.gap = '10px';
    document.body.appendChild(container);

    // 创建动画按钮
    this.createAnimationButton(container, 'idle', '待机');
    this.createAnimationButton(container, 'walk', '行走');
    this.createAnimationButton(container, 'run', '跑步');
    this.createAnimationButton(container, 'jump', '跳跃');
    this.createAnimationButton(container, 'attack', '攻击');
  }

  /**
   * 创建动画按钮
   * @param container 容器
   * @param animationName 动画名称
   * @param buttonText 按钮文本
   */
  private createAnimationButton(container: HTMLElement, animationName: string, buttonText: string): void {
    // 创建按钮
    const button = document.createElement('button');
    button.textContent = buttonText;
    button.style.padding = '10px 20px';
    button.style.fontSize = '16px';
    button.style.cursor = 'pointer';

    // 添加点击事件
    button.addEventListener('click', () => {
      this.playAnimation(animationName);
    });

    // 添加到容器
    container.appendChild(button);
  }
}
