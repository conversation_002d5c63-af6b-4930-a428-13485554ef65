/**
 * 冲突预测状态管理
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { PredictedConflict } from '../../services/IntentBasedConflictPreventionService';

// 状态接口
interface ConflictPredictionState {
  predictedConflicts: PredictedConflict[];
  showPredictionPanel: boolean;
  selectedPredictedConflictId: string | null;
  notificationEnabled: boolean;
  predictionEnabled: boolean;
  predictionThreshold: number;
}

// 初始状态
const initialState: ConflictPredictionState = {
  predictedConflicts: [],
  showPredictionPanel: false,
  selectedPredictedConflictId: null,
  notificationEnabled: true,
  predictionEnabled: true,
  predictionThreshold: 0.7
};

// 创建Slice
export const conflictPredictionSlice = createSlice({
  name: 'conflictPrediction',
  initialState,
  reducers: {
    // 添加预测冲突
    addPredictedConflict: (state, action: PayloadAction<PredictedConflict>) => {
      // 检查是否已存在相同ID的冲突
      const existingIndex = state.predictedConflicts.findIndex(c => c.id === action.payload.id);
      
      if (existingIndex >= 0) {
        // 更新现有冲突
        state.predictedConflicts[existingIndex] = action.payload;
      } else {
        // 添加新冲突
        state.predictedConflicts.push(action.payload);
      }
    },
    
    // 移除预测冲突
    removePredictedConflict: (state, action: PayloadAction<string>) => {
      state.predictedConflicts = state.predictedConflicts.filter(c => c.id !== action.payload);
      
      // 如果移除的是当前选中的冲突，清除选中状态
      if (state.selectedPredictedConflictId === action.payload) {
        state.selectedPredictedConflictId = null;
      }
    },
    
    // 更新预测冲突
    updatePredictedConflict: (state, action: PayloadAction<{ id: string; changes: Partial<PredictedConflict> }>) => {
      const { id, changes } = action.payload;
      const conflictIndex = state.predictedConflicts.findIndex(c => c.id === id);
      
      if (conflictIndex >= 0) {
        state.predictedConflicts[conflictIndex] = {
          ...state.predictedConflicts[conflictIndex],
          ...changes
        };
      }
    },
    
    // 解决预测冲突
    resolvePredictedConflict: (state, action: PayloadAction<string>) => {
      const conflictIndex = state.predictedConflicts.findIndex(c => c.id === action.payload);
      
      if (conflictIndex >= 0) {
        state.predictedConflicts[conflictIndex].resolved = true;
        state.predictedConflicts[conflictIndex].resolvedAt = Date.now();
      }
    },
    
    // 清除所有预测冲突
    clearPredictedConflicts: (state) => {
      state.predictedConflicts = [];
      state.selectedPredictedConflictId = null;
    },
    
    // 清除已解决的预测冲突
    clearResolvedPredictedConflicts: (state) => {
      state.predictedConflicts = state.predictedConflicts.filter(c => !c.resolved);
      
      // 如果清除了当前选中的冲突，清除选中状态
      if (state.selectedPredictedConflictId && 
          !state.predictedConflicts.some(c => c.id === state.selectedPredictedConflictId)) {
        state.selectedPredictedConflictId = null;
      }
    },
    
    // 设置显示预测面板
    setShowPredictionPanel: (state, action: PayloadAction<boolean>) => {
      state.showPredictionPanel = action.payload;
    },
    
    // 设置选中的预测冲突
    setSelectedPredictedConflictId: (state, action: PayloadAction<string | null>) => {
      state.selectedPredictedConflictId = action.payload;
    },
    
    // 设置通知启用状态
    setNotificationEnabled: (state, action: PayloadAction<boolean>) => {
      state.notificationEnabled = action.payload;
    },
    
    // 设置预测启用状态
    setPredictionEnabled: (state, action: PayloadAction<boolean>) => {
      state.predictionEnabled = action.payload;
    },
    
    // 设置预测阈值
    setPredictionThreshold: (state, action: PayloadAction<number>) => {
      state.predictionThreshold = Math.max(0, Math.min(1, action.payload));
    }
  }
});

// 导出Actions
export const {
  addPredictedConflict,
  removePredictedConflict,
  updatePredictedConflict,
  resolvePredictedConflict,
  clearPredictedConflicts,
  clearResolvedPredictedConflicts,
  setShowPredictionPanel,
  setSelectedPredictedConflictId,
  setNotificationEnabled,
  setPredictionEnabled,
  setPredictionThreshold
} = conflictPredictionSlice.actions;

// 导出Selectors
export const selectPredictedConflicts = (state: { conflictPrediction: ConflictPredictionState }) => 
  state.conflictPrediction.predictedConflicts;

export const selectPendingPredictedConflicts = (state: { conflictPrediction: ConflictPredictionState }) => 
  state.conflictPrediction.predictedConflicts.filter(c => !c.resolved);

export const selectResolvedPredictedConflicts = (state: { conflictPrediction: ConflictPredictionState }) => 
  state.conflictPrediction.predictedConflicts.filter(c => c.resolved);

export const selectShowPredictionPanel = (state: { conflictPrediction: ConflictPredictionState }) => 
  state.conflictPrediction.showPredictionPanel;

export const selectSelectedPredictedConflictId = (state: { conflictPrediction: ConflictPredictionState }) => 
  state.conflictPrediction.selectedPredictedConflictId;

export const selectSelectedPredictedConflict = (state: { conflictPrediction: ConflictPredictionState }) => 
  state.conflictPrediction.predictedConflicts.find(c => c.id === state.conflictPrediction.selectedPredictedConflictId);

export const selectNotificationEnabled = (state: { conflictPrediction: ConflictPredictionState }) => 
  state.conflictPrediction.notificationEnabled;

export const selectPredictionEnabled = (state: { conflictPrediction: ConflictPredictionState }) => 
  state.conflictPrediction.predictionEnabled;

export const selectPredictionThreshold = (state: { conflictPrediction: ConflictPredictionState }) => 
  state.conflictPrediction.predictionThreshold;

// 导出Reducer
export default conflictPredictionSlice.reducer;
