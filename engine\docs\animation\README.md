# 动画系统文档

## 概述

动画系统是DL（Digital Learning）引擎的核心组件之一，用于管理和控制3D模型的动画。它提供了丰富的功能，包括动画混合、状态机、重定向等，可以满足各种复杂的动画需求。

主要功能包括：

- 基础动画播放和控制
- 动画混合和过渡
- 动画状态机
- 动画混合空间（1D和2D）
- 动画重定向
- Avatar动画系统

## 核心组件

### AnimationClip

`AnimationClip`是动画系统的基础组件，表示一个动画片段。它包含了一系列的动画轨道，每个轨道控制一个属性的变化。

```typescript
// 创建动画片段
const clip = new AnimationClip('walk', 1.0);

// 添加位置轨道
clip.addTrack('character.position', 'vector3', [
  { time: 0, value: [0, 0, 0] },
  { time: 0.5, value: [0, 0.1, 0.5] },
  { time: 1.0, value: [0, 0, 1.0] }
]);

// 添加旋转轨道
clip.addTrack('character.quaternion', 'quaternion', [
  { time: 0, value: [0, 0, 0, 1] },
  { time: 1.0, value: [0, 0.1, 0, 0.99] }
]);

// 设置循环模式
clip.loopMode = LoopMode.REPEAT; // 可选值：NONE, REPEAT, PING_PONG
```

### Animator

`Animator`是动画控制器，用于控制动画片段的播放、混合和过渡。

```typescript
// 创建动画控制器
const animator = new Animator({
  entity: character,
  clips: [walkClip, runClip, idleClip],
  autoPlay: true,
  defaultBlendTime: 0.3
});

// 播放动画
animator.play('walk');

// 混合到另一个动画
animator.play('run', 0.5); // 0.5秒内混合到run动画

// 停止动画
animator.stop();

// 暂停和恢复
animator.pause();
animator.resume();

// 设置时间缩放
animator.setTimeScale(0.5); // 半速播放

// 设置循环模式
animator.setLoop(true);

// 监听事件
animator.addListener(AnimationEventType.COMPLETE, (data) => {
  console.log(`动画 ${data.name} 播放完成`);
});
```

### AnimationStateMachine

`AnimationStateMachine`是动画状态机，用于管理复杂的动画状态和转换。

```typescript
// 创建状态机
const stateMachine = new AnimationStateMachine(animator);

// 添加状态
stateMachine.addState({
  name: 'Idle',
  type: 'SingleAnimationState',
  clipName: 'idle',
  loop: true,
  clamp: false
});

stateMachine.addState({
  name: 'Walk',
  type: 'SingleAnimationState',
  clipName: 'walk',
  loop: true,
  clamp: false
});

// 添加转换规则
stateMachine.addTransition({
  from: 'Idle',
  to: 'Walk',
  condition: () => {
    return stateMachine.getParameter('speed') > 0.1;
  },
  duration: 0.3,
  canInterrupt: true
});

stateMachine.addTransition({
  from: 'Walk',
  to: 'Idle',
  condition: () => {
    return stateMachine.getParameter('speed') <= 0.1;
  },
  duration: 0.3,
  canInterrupt: true
});

// 设置参数
stateMachine.setParameter('speed', 0);

// 设置当前状态
stateMachine.setCurrentState('Idle');

// 更新状态机
stateMachine.update(deltaTime);

// 监听事件
stateMachine.addEventListener(AnimationStateMachineEventType.STATE_ENTER, (state) => {
  console.log(`进入状态: ${state.name}`);
});
```

### BlendSpace1D 和 BlendSpace2D

混合空间用于在参数空间中混合多个动画。

```typescript
// 创建1D混合空间
const blendSpace1D = new BlendSpace1D({
  minValue: 0,
  maxValue: 1
});

// 添加节点
blendSpace1D.addNode(idleClip, 0);
blendSpace1D.addNode(walkClip, 0.5);
blendSpace1D.addNode(runClip, 1);

// 设置当前位置
blendSpace1D.setPosition(0.25); // 在idle和walk之间混合

// 更新混合权重
blendSpace1D.update();

// 获取活跃节点
const activeNodes = blendSpace1D.getActiveNodes();
```

```typescript
// 创建2D混合空间
const blendSpace2D = new BlendSpace2D({
  minX: -1,
  maxX: 1,
  minY: -1,
  maxY: 1
});

// 添加节点
blendSpace2D.addNode(idleClip, new THREE.Vector2(0, 0));
blendSpace2D.addNode(walkForwardClip, new THREE.Vector2(0, 1));
blendSpace2D.addNode(walkBackwardClip, new THREE.Vector2(0, -1));
blendSpace2D.addNode(walkLeftClip, new THREE.Vector2(-1, 0));
blendSpace2D.addNode(walkRightClip, new THREE.Vector2(1, 0));

// 设置当前位置
blendSpace2D.setPosition(0.5, 0.5); // 在idle、walkForward和walkRight之间混合

// 更新混合权重
blendSpace2D.update();
```

### AnimationRetargeting

`AnimationRetargeting`用于将一个骨骼结构的动画应用到另一个骨骼结构上。

```typescript
// 重定向动画
const retargetedClip = AnimationRetargeting.retargetClip(
  sourceClip,
  sourceSkeleton,
  targetSkeleton,
  {
    boneMapping: [
      { source: 'Hips', target: 'J_Bip_C_Hips' },
      { source: 'Spine', target: 'J_Bip_C_Spine' },
      // ...更多骨骼映射
    ],
    preservePositionTracks: true,
    preserveScaleTracks: false,
    normalizeRotations: true,
    adjustRootHeight: true,
    adjustBoneLength: true
  }
);
```

### AvatarAnimationSystem

`AvatarAnimationSystem`是一个高级系统，用于管理角色动画，包括动画混合、状态机和重定向。

```typescript
// 创建Avatar动画系统
const avatarAnimationSystem = new AvatarAnimationSystem(world, {
  debug: true,
  autoRetarget: true,
  useStateMachine: true,
  useBlendSpace: true
});

// 注册Avatar实体
avatarAnimationSystem.registerAvatar(characterEntity);

// 设置更新频率
avatarAnimationSystem.setUpdateFrequency(characterEntity, 1/30); // 每秒30次更新

// 标记需要更新
avatarAnimationSystem.markNeedsUpdate(characterEntity);

// 更新系统
avatarAnimationSystem.update(deltaTime);
```

## 性能优化

动画系统包含多种性能优化机制：

1. **缓存机制**：
   - `AnimationClip`使用缓存避免重复计算
   - `Animator`缓存动画状态
   - `AvatarAnimationSystem`缓存重定向结果

2. **更新频率控制**：
   - 可以为不同实体设置不同的更新频率
   - 远处或不重要的角色可以使用较低的更新频率

3. **优先级排序**：
   - 系统会优先更新最需要更新的实体
   - 可以限制每帧更新的实体数量

4. **按需更新**：
   - 只有当动画状态发生显著变化时才进行更新
   - 使用标记系统指示哪些实体需要更新

## 使用示例

请参考`examples/animation/AnimationExample.ts`文件，了解如何使用动画系统的完整示例。

## 注意事项

1. 对于大量角色的场景，建议使用更新频率控制和优先级排序
2. 重定向操作比较耗时，建议使用缓存机制
3. 对于远处的角色，可以降低更新频率或禁用某些高级功能
4. 使用状态机和混合空间可以创建更自然的动画过渡
