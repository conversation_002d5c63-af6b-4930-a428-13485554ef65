# 示例项目浏览器集成指南

本文档详细介绍了如何将示例项目浏览器集成到DL（Digital Learning）引擎编辑器中，包括组件结构、数据流、API接口和使用方法。

## 目录

- [组件结构](#组件结构)
- [数据流](#数据流)
- [API接口](#api接口)
- [使用方法](#使用方法)
- [扩展和定制](#扩展和定制)
- [常见问题](#常见问题)

## 组件结构

示例项目浏览器由以下组件组成：

1. **ExampleBrowser**: 主容器组件，管理示例项目的加载和显示
2. **ExampleCard**: 示例项目卡片组件，显示项目预览和基本信息
3. **ExampleFilter**: 过滤和搜索组件，用于筛选示例项目
4. **ExampleDetail**: 示例项目详情组件，显示项目的详细信息和操作选项
5. **ImportDialog**: 导入对话框组件，用于设置导入选项

### 组件关系图

```
ExampleBrowser
├── ExampleFilter
├── ExampleCard (多个)
├── ExampleDetail
│   └── ImportDialog
└── ImportDialog
```

## 数据流

示例项目浏览器的数据流如下：

1. **ExampleBrowser** 组件初始化时，调用 `fetchExamples()` 获取示例项目列表
2. 用户可以通过 **ExampleFilter** 组件筛选示例项目
3. 用户点击 **ExampleCard** 组件，显示 **ExampleDetail** 组件
4. 用户点击导入按钮，显示 **ImportDialog** 组件
5. 用户确认导入，调用 `importExample()` 导入示例项目

### 状态管理

示例项目浏览器使用 React 的 `useState` 和 `useEffect` 钩子管理状态，主要状态包括：

- `examples`: 所有示例项目列表
- `filteredExamples`: 筛选后的示例项目列表
- `selectedCategory`: 选中的类别
- `selectedTags`: 选中的标签
- `searchText`: 搜索文本
- `selectedExample`: 选中的示例项目
- `loading`: 加载状态
- `importDialogVisible`: 导入对话框可见状态

## API接口

示例项目浏览器使用以下API接口：

### 示例项目服务

```typescript
// 获取示例项目列表
fetchExamples(): Promise<Example[]>

// 获取示例项目详情
fetchExampleById(id: string): Promise<Example | null>

// 获取示例项目标签列表
fetchExampleTags(): Promise<string[]>

// 导入示例项目
importExample(
  exampleId: string,
  projectName: string,
  workspaceId: string,
  options: {
    importOption: 'copy' | 'reference';
    includeAssets: boolean;
  }
): Promise<boolean>

// 导出项目为示例项目
exportAsExample(
  projectId: string,
  exampleInfo: {
    title: string;
    description: string;
    category: ExampleCategory;
    tags: string[];
    previewImage: File;
  }
): Promise<boolean>

// 收藏示例项目
favoriteExample(
  exampleId: string,
  favorited: boolean
): Promise<boolean>
```

### 工作空间服务

```typescript
// 获取工作空间列表
fetchWorkspaces(): Promise<Workspace[]>

// 获取工作空间详情
fetchWorkspaceById(id: string): Promise<Workspace | null>

// 创建工作空间
createWorkspace(
  name: string,
  path: string
): Promise<Workspace | null>

// 浏览文件夹
browseFolder(): Promise<string | null>
```

## 使用方法

### 在编辑器中集成示例项目浏览器

1. 在编辑器的菜单中添加"示例项目"选项：

```tsx
// 在菜单项中添加示例项目选项
const menuItems = [
  // 其他菜单项...
  {
    key: 'examples',
    label: t('menu.examples'),
    icon: <AppstoreOutlined />,
  },
];

// 处理菜单点击
const handleMenuClick = ({ key }) => {
  if (key === 'examples') {
    // 打开示例项目浏览器
    setExampleBrowserVisible(true);
  }
  // 处理其他菜单项...
};
```

2. 在编辑器中添加示例项目浏览器组件：

```tsx
// 在编辑器组件中添加示例项目浏览器
import { ExampleBrowser } from '../components/ExampleBrowser';

const Editor = () => {
  const [exampleBrowserVisible, setExampleBrowserVisible] = useState(false);

  // 其他编辑器代码...

  return (
    <Layout className="editor-layout">
      {/* 其他编辑器组件... */}
      
      <Modal
        title={t('exampleBrowser.title')}
        visible={exampleBrowserVisible}
        onCancel={() => setExampleBrowserVisible(false)}
        footer={null}
        width="90%"
        style={{ top: 20 }}
        bodyStyle={{ padding: 0, height: 'calc(90vh - 55px)' }}
      >
        <ExampleBrowser />
      </Modal>
    </Layout>
  );
};
```

### 导入示例项目

1. 在示例项目浏览器中选择要导入的项目
2. 点击"导入项目"按钮
3. 在导入对话框中设置项目名称和保存位置
4. 确认导入

### 导出项目为示例项目

1. 在编辑器中打开要导出的项目
2. 选择"文件 > 导出 > 导出为示例项目"
3. 在导出对话框中设置示例项目的标题、描述、类别和标签
4. 确认导出

## 扩展和定制

### 添加新的示例项目类别

1. 在 `example.ts` 中添加新的类别：

```typescript
export type ExampleCategory = 
  | 'basic'
  | 'material'
  | 'animation'
  | 'physics'
  | 'visualscript'
  | 'performance'
  | 'collaboration'
  | 'tutorial'
  | 'newCategory'; // 添加新类别
```

2. 在 `exampleUtils.ts` 中添加新类别的颜色：

```typescript
export const getCategoryColor = (category: ExampleCategory): string => {
  switch (category) {
    // 现有类别...
    case 'newCategory':
      return 'pink'; // 设置新类别的颜色
    default:
      return 'default';
  }
};
```

3. 在 `ExampleFilter.tsx` 中添加新类别的选项：

```tsx
const renderCategoryOptions = () => {
  const categories: { value: string; label: string }[] = [
    // 现有类别...
    { value: 'newCategory', label: t('exampleBrowser.category.newCategory') },
  ];
  // 其他代码...
};
```

### 自定义示例项目卡片

可以通过修改 `ExampleCard.tsx` 和 `ExampleCard.less` 来自定义示例项目卡片的外观和行为。

## 常见问题

### 示例项目无法加载

可能的原因：
- 网络连接问题
- API服务器未启动
- 权限不足

解决方法：
- 检查网络连接
- 确保API服务器已启动
- 检查用户权限

### 导入示例项目失败

可能的原因：
- 目标路径不存在
- 目标路径没有写入权限
- 示例项目文件损坏

解决方法：
- 确保目标路径存在
- 检查目标路径的写入权限
- 重新下载示例项目

### 示例项目预览无法显示

可能的原因：
- 预览图片路径错误
- 预览图片文件损坏
- 浏览器缓存问题

解决方法：
- 检查预览图片路径
- 重新生成预览图片
- 清除浏览器缓存
