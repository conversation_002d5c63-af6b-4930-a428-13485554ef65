#!/usr/bin/env node

/**
 * 全面修复TypeScript编译错误的脚本
 */

const fs = require('fs');
const path = require('path');

// 全面的修复规则
const fixes = [
  // 1. 导入修复 - 将实体类型改为类型导入
  {
    name: '实体类型导入修复',
    pattern: /import\s*\{\s*([^}]*\bEntity\b[^}]*)\s*\}\s*from\s*(['"][^'"]*Entity['"])/g,
    replacement: (match, imports, modulePath) => {
      const fixedImports = imports.replace(/\bEntity\b/g, 'type Entity');
      return `import { ${fixedImports} } from ${modulePath}`;
    }
  },
  
  // 2. Transform组件访问修复
  {
    name: 'Transform组件访问修复',
    pattern: /(\w+)\.position\.set\s*\(/g,
    replacement: '($1 as any).setPosition('
  },
  {
    name: 'Transform旋转访问修复',
    pattern: /(\w+)\.rotation\.set\s*\(/g,
    replacement: '($1 as any).setRotationQuaternion('
  },
  {
    name: 'Transform缩放访问修复',
    pattern: /(\w+)\.scale\.set\s*\(/g,
    replacement: '($1 as any).setScale('
  },
  {
    name: 'Transform四元数访问修复',
    pattern: /(\w+)\.quaternion\.set\s*\(/g,
    replacement: '($1 as any).setRotationQuaternion('
  },
  
  // 3. 组件获取修复
  {
    name: '组件获取类型断言',
    pattern: /\.getComponent\s*\(\s*['"]([^'"]+)['"]\s*\)/g,
    replacement: '.getComponent(\'$1\') as any'
  },
  
  // 4. 物理体属性访问修复
  {
    name: '物理体位置访问修复',
    pattern: /(\w+)\.position\.([xyz])/g,
    replacement: '($1 as any).getPosition().$2'
  },
  
  // 5. 事件处理修复
  {
    name: '事件处理类型修复',
    pattern: /typeof\s+(\w+)\['(\w+)'\]\s*===\s*'function'/g,
    replacement: 'typeof ($1 as any).$2 === \'function\''
  },
  
  // 6. 类型导入修复
  {
    name: '通用类型导入修复',
    pattern: /import\s*\{\s*([^}]*(?:World|Transform|Camera|PhysicsBody|NetworkEntity)[^}]*)\s*\}\s*from/g,
    replacement: (match, imports) => {
      const typeImports = ['World', 'Transform', 'Camera', 'PhysicsBody', 'NetworkEntity'];
      let fixedImports = imports;
      
      typeImports.forEach(type => {
        const regex = new RegExp(`\\b${type}\\b(?!\\s*as\\s+type)`, 'g');
        fixedImports = fixedImports.replace(regex, `type ${type}`);
      });
      
      return `import { ${fixedImports} } from`;
    }
  },
  
  // 7. 方法调用修复
  {
    name: '方法调用类型断言',
    pattern: /(\w+)\.updateTransform\s*\(\s*\)/g,
    replacement: '($1 as any).updateTransform()'
  },
  
  // 8. 属性访问修复
  {
    name: '属性访问类型断言',
    pattern: /(\w+)\.dispose\s*\(\s*\)/g,
    replacement: '($1 as any).dispose()'
  }
];

/**
 * 递归获取所有TypeScript文件
 */
function getAllTsFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      getAllTsFiles(fullPath, files);
    } else if (stat.isFile() && item.endsWith('.ts') && !item.endsWith('.d.ts')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * 修复单个文件
 */
function fixFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  const appliedFixes = [];
  
  // 应用所有修复规则
  for (const fix of fixes) {
    const originalContent = content;
    
    if (typeof fix.replacement === 'function') {
      content = content.replace(fix.pattern, fix.replacement);
    } else {
      content = content.replace(fix.pattern, fix.replacement);
    }
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.name);
    }
  }
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`已修复: ${path.relative(process.cwd(), filePath)}`);
    console.log(`  应用的修复: ${appliedFixes.join(', ')}`);
    return true;
  }
  
  return false;
}

/**
 * 主函数
 */
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src目录不存在');
    process.exit(1);
  }
  
  const files = getAllTsFiles(srcDir);
  
  console.log(`找到 ${files.length} 个TypeScript文件`);
  console.log('开始修复...\n');
  
  let fixedCount = 0;
  for (const file of files) {
    if (fixFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n修复完成！`);
  console.log(`已修复 ${fixedCount} 个文件`);
  
  if (fixedCount > 0) {
    console.log('\n建议运行以下命令检查编译状态：');
    console.log('npx tsc --noEmit');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixFile, getAllTsFiles };
