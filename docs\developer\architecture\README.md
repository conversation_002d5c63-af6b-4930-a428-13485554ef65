# DL（Digital Learning）引擎编辑器架构说明

## 概述

DL（Digital Learning）引擎编辑器是一个基于React、Redux和Ant Design开发的可视化编辑器，用于创建和编辑3D场景、模型、材质、动画等内容。编辑器采用模块化设计，具有高度的可扩展性和灵活性。

## 架构图

```
+------------------------------------------+
|                 编辑器应用                 |
+------------------------------------------+
|                                          |
|  +-------------+      +---------------+  |
|  |   UI层      |      |    状态管理    |  |
|  | (React组件) | <--> |    (Redux)    |  |
|  +-------------+      +---------------+  |
|         ^                    ^           |
|         |                    |           |
|         v                    v           |
|  +-------------+      +---------------+  |
|  |   服务层    |      |    工具层     |  |
|  | (Services)  | <--> |   (Utilities) |  |
|  +-------------+      +---------------+  |
|         ^                    ^           |
|         |                    |           |
|         v                    v           |
|  +------------------------------------------+
|  |                引擎接口层                 |
|  |          (Engine Interface Layer)        |
|  +------------------------------------------+
|                     ^                        |
+---------------------|------------------------+
                      |
                      v
+------------------------------------------+
|                  引擎核心                 |
|             (Engine Core)                |
+------------------------------------------+
```

## 核心模块

### 1. UI层

UI层是编辑器的用户界面，由React组件构成。主要包括以下部分：

- **布局组件**：负责编辑器的整体布局，包括顶部菜单栏、侧边栏、内容区域等。
- **面板组件**：各种功能面板，如场景面板、层级面板、检查器面板、资产面板等。
- **视口组件**：3D场景的可视化编辑区域。
- **工具组件**：各种编辑工具，如变换工具、选择工具等。
- **对话框组件**：各种对话框，如设置对话框、导入/导出对话框等。
- **通用组件**：按钮、输入框、选择框等通用UI组件。

### 2. 状态管理

状态管理层使用Redux管理编辑器的状态。主要包括以下状态切片：

- **authSlice**：认证状态，包括用户登录信息、权限等。
- **projectSlice**：项目状态，包括当前项目信息、项目列表等。
- **sceneSlice**：场景状态，包括当前场景信息、场景列表等。
- **entitySlice**：实体状态，包括当前选中的实体、实体列表等。
- **assetSlice**：资产状态，包括资产列表、资产分类等。
- **uiSlice**：UI状态，包括面板布局、主题设置等。
- **toolSlice**：工具状态，包括当前选中的工具、工具设置等。
- **historySlice**：历史记录状态，包括撤销/重做栈等。
- **collaborationSlice**：协作状态，包括在线用户、操作同步等。

### 3. 服务层

服务层提供各种服务，负责与后端API交互、管理资源等。主要包括以下服务：

- **EngineService**：引擎服务，负责与引擎核心交互。
- **ProjectService**：项目服务，负责项目的创建、打开、保存等。
- **AssetService**：资产服务，负责资产的上传、下载、管理等。
- **AuthService**：认证服务，负责用户登录、注册、权限验证等。
- **CollaborationService**：协作服务，负责多用户协作编辑。
- **HistoryService**：历史记录服务，负责操作的撤销/重做。
- **LayoutService**：布局服务，负责编辑器界面布局的管理。
- **LogService**：日志服务，负责记录和管理日志。
- **NotificationService**：通知服务，负责显示通知和提醒。

### 4. 工具层

工具层提供各种工具函数和实用工具，辅助编辑器的功能实现。主要包括以下工具：

- **数学工具**：向量、矩阵、四元数等数学运算工具。
- **文件工具**：文件读写、格式转换等工具。
- **UI工具**：UI相关的辅助工具，如主题管理、国际化等。
- **调试工具**：性能监控、错误捕获等调试工具。
- **验证工具**：数据验证、类型检查等工具。

### 5. 引擎接口层

引擎接口层是编辑器与引擎核心之间的桥梁，提供了一组API来操作引擎。主要包括以下接口：

- **场景管理**：创建、加载、保存场景等。
- **实体管理**：创建、删除、查询实体等。
- **组件管理**：添加、移除、查询组件等。
- **资源管理**：加载、释放资源等。
- **渲染控制**：控制渲染过程、设置渲染参数等。
- **物理模拟**：控制物理模拟、设置物理参数等。
- **动画控制**：控制动画播放、设置动画参数等。

## 数据流

编辑器采用单向数据流模式，数据流向如下：

1. 用户通过UI组件触发操作。
2. 操作被分发到Redux的Action。
3. Reducer处理Action并更新状态。
4. 状态变化触发UI组件重新渲染。
5. 如果需要与引擎交互，则通过服务层调用引擎接口。
6. 引擎接口层将操作转换为引擎可理解的命令。
7. 引擎执行命令并返回结果。
8. 结果通过服务层传递给Redux，更新状态。
9. 状态变化再次触发UI组件重新渲染。

## 扩展机制

编辑器提供了多种扩展机制，允许开发者扩展编辑器的功能：

### 1. 面板扩展

开发者可以创建自定义面板，并注册到编辑器的面板系统中：

```typescript
// 注册自定义面板
PanelRegistry.register({
  id: 'custom-panel',
  title: '自定义面板',
  icon: <CustomIcon />,
  component: CustomPanel,
});
```

### 2. 工具扩展

开发者可以创建自定义工具，并注册到编辑器的工具系统中：

```typescript
// 注册自定义工具
ToolRegistry.register({
  id: 'custom-tool',
  name: '自定义工具',
  icon: <CustomToolIcon />,
  component: CustomTool,
});
```

### 3. 组件编辑器扩展

开发者可以为自定义组件创建编辑器，并注册到编辑器的组件编辑器系统中：

```typescript
// 注册自定义组件编辑器
ComponentEditorRegistry.register({
  componentType: 'CustomComponent',
  editor: CustomComponentEditor,
});
```

### 4. 资产类型扩展

开发者可以添加对新资产类型的支持，并注册到编辑器的资产系统中：

```typescript
// 注册自定义资产类型
AssetTypeRegistry.register({
  type: 'custom-asset',
  name: '自定义资产',
  icon: <CustomAssetIcon />,
  extensions: ['.custom'],
  importer: CustomAssetImporter,
  viewer: CustomAssetViewer,
});
```

### 5. 命令扩展

开发者可以创建自定义命令，并注册到编辑器的命令系统中：

```typescript
// 注册自定义命令
CommandRegistry.register({
  id: 'custom-command',
  name: '自定义命令',
  shortcut: 'Ctrl+Shift+C',
  execute: () => {
    // 执行命令的代码
  },
});
```

## 技术栈

- **前端框架**：React 18
- **状态管理**：Redux (Redux Toolkit)
- **UI组件库**：Ant Design
- **3D渲染**：Three.js
- **构建工具**：Vite
- **语言**：TypeScript
- **测试框架**：Jest
- **国际化**：i18next
- **面板布局**：rc-dock

## 性能优化

编辑器采用了多种性能优化策略：

1. **组件懒加载**：使用React.lazy和Suspense实现组件的按需加载。
2. **状态规范化**：Redux状态采用规范化结构，避免数据冗余。
3. **记忆化选择器**：使用reselect创建记忆化选择器，减少不必要的计算。
4. **虚拟列表**：使用虚拟列表技术，高效渲染大量数据。
5. **WebWorker**：将耗时计算放在WebWorker中执行，避免阻塞主线程。
6. **缓存机制**：实现资源缓存，减少重复加载。
7. **按需渲染**：只在需要时更新3D视图，减少不必要的渲染。

## 安全性

编辑器实现了多种安全措施：

1. **认证与授权**：基于JWT的用户认证和基于角色的权限控制。
2. **输入验证**：所有用户输入都经过验证，防止注入攻击。
3. **CSRF保护**：实现CSRF令牌机制，防止跨站请求伪造。
4. **安全的资源加载**：资源加载采用安全策略，防止恶意代码执行。
5. **数据加密**：敏感数据在传输和存储时进行加密。

## 可访问性

编辑器注重可访问性设计：

1. **键盘导航**：支持完全的键盘导航和操作。
2. **屏幕阅读器支持**：兼容主流屏幕阅读器。
3. **高对比度模式**：提供高对比度主题。
4. **可调整字体大小**：支持调整界面字体大小。
5. **焦点管理**：合理的焦点管理，提高操作效率。

## 国际化

编辑器支持多语言：

1. **基于i18next**：使用i18next实现国际化。
2. **默认中文**：默认使用中文界面。
3. **语言切换**：支持在运行时切换语言。
4. **本地化格式**：日期、时间、数字等采用本地化格式。
