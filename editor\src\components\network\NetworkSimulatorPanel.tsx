/**
 * 网络模拟器面板组件
 * 用于模拟各种网络条件，如延迟、丢包、抖动等
 */
import React, { useState, useEffect } from 'react';
import { Card, Form, Slider, InputNumber, Switch, Button, Row, Col, Divider, Tooltip, Alert, Space, Select, Progress, Modal, Steps, Typography, Tag } from 'antd';
import {
  InfoCircleOutlined,
  WarningOutlined,
  ReloadOutlined,
  SaveOutlined,
  UndoOutlined,
  ExperimentOutlined,
  DisconnectOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SaveOutlined as SaveFilled,
  LoadingOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { NetworkSimulatorConfig } from '../../../engine/src/network/NetworkSimulator';
import './NetworkSimulatorPanel.css';

const { Option } = Select;
const { Step } = Steps;
const { Text, Paragraph } = Typography;

// 预设配置
const PRESETS = {
  perfect: {
    name: '完美连接',
    config: {
      enabled: true,
      latency: 0,
      latencyJitter: 0,
      packetLoss: 0,
      bandwidthLimit: 0,
      enableRandomDisconnect: false,
      disconnectProbability: 0,
      reconnectTime: 3000,
    },
  },
  good: {
    name: '良好连接',
    config: {
      enabled: true,
      latency: 50,
      latencyJitter: 10,
      packetLoss: 0.01,
      bandwidthLimit: 1000000, // 1MB/s
      enableRandomDisconnect: false,
      disconnectProbability: 0,
      reconnectTime: 3000,
    },
  },
  average: {
    name: '一般连接',
    config: {
      enabled: true,
      latency: 100,
      latencyJitter: 30,
      packetLoss: 0.03,
      bandwidthLimit: 500000, // 500KB/s
      enableRandomDisconnect: false,
      disconnectProbability: 0.001,
      reconnectTime: 3000,
    },
  },
  poor: {
    name: '较差连接',
    config: {
      enabled: true,
      latency: 200,
      latencyJitter: 50,
      packetLoss: 0.05,
      bandwidthLimit: 200000, // 200KB/s
      enableRandomDisconnect: true,
      disconnectProbability: 0.005,
      reconnectTime: 3000,
    },
  },
  terrible: {
    name: '极差连接',
    config: {
      enabled: true,
      latency: 500,
      latencyJitter: 100,
      packetLoss: 0.1,
      bandwidthLimit: 50000, // 50KB/s
      enableRandomDisconnect: true,
      disconnectProbability: 0.01,
      reconnectTime: 5000,
    },
  },
  mobile3g: {
    name: '3G移动网络',
    config: {
      enabled: true,
      latency: 150,
      latencyJitter: 40,
      packetLoss: 0.02,
      bandwidthLimit: 300000, // 300KB/s
      enableRandomDisconnect: true,
      disconnectProbability: 0.003,
      reconnectTime: 3000,
    },
  },
  mobile4g: {
    name: '4G移动网络',
    config: {
      enabled: true,
      latency: 80,
      latencyJitter: 20,
      packetLoss: 0.01,
      bandwidthLimit: 800000, // 800KB/s
      enableRandomDisconnect: false,
      disconnectProbability: 0.001,
      reconnectTime: 2000,
    },
  },
  satellite: {
    name: '卫星连接',
    config: {
      enabled: true,
      latency: 600,
      latencyJitter: 50,
      packetLoss: 0.03,
      bandwidthLimit: 100000, // 100KB/s
      enableRandomDisconnect: false,
      disconnectProbability: 0.002,
      reconnectTime: 4000,
    },
  },
};

// 场景模拟配置
interface ScenarioConfig {
  /** 场景名称 */
  name: string;
  /** 场景描述 */
  description: string;
  /** 场景步骤 */
  steps: {
    /** 步骤名称 */
    name: string;
    /** 步骤持续时间（毫秒） */
    duration: number;
    /** 网络配置 */
    config: NetworkSimulatorConfig;
  }[];
}

// 预设场景
const SCENARIOS: Record<string, ScenarioConfig> = {
  networkDegradation: {
    name: '网络质量逐渐恶化',
    description: '模拟网络质量从良好到极差的渐变过程',
    steps: [
      {
        name: '良好网络',
        duration: 10000,
        config: PRESETS.good.config,
      },
      {
        name: '一般网络',
        duration: 10000,
        config: PRESETS.average.config,
      },
      {
        name: '较差网络',
        duration: 10000,
        config: PRESETS.poor.config,
      },
      {
        name: '极差网络',
        duration: 10000,
        config: PRESETS.terrible.config,
      },
    ],
  },
  networkRecovery: {
    name: '网络质量逐渐恢复',
    description: '模拟网络质量从极差到良好的恢复过程',
    steps: [
      {
        name: '极差网络',
        duration: 10000,
        config: PRESETS.terrible.config,
      },
      {
        name: '较差网络',
        duration: 10000,
        config: PRESETS.poor.config,
      },
      {
        name: '一般网络',
        duration: 10000,
        config: PRESETS.average.config,
      },
      {
        name: '良好网络',
        duration: 10000,
        config: PRESETS.good.config,
      },
    ],
  },
  networkInstability: {
    name: '网络不稳定',
    description: '模拟网络质量波动和短暂断线的情况',
    steps: [
      {
        name: '良好网络',
        duration: 5000,
        config: PRESETS.good.config,
      },
      {
        name: '较差网络',
        duration: 3000,
        config: {
          ...PRESETS.poor.config,
          enableRandomDisconnect: true,
          disconnectProbability: 0.01,
        },
      },
      {
        name: '一般网络',
        duration: 5000,
        config: PRESETS.average.config,
      },
      {
        name: '断线',
        duration: 2000,
        config: {
          ...PRESETS.terrible.config,
          enableRandomDisconnect: true,
          disconnectProbability: 0.5,
        },
      },
      {
        name: '良好网络',
        duration: 5000,
        config: PRESETS.good.config,
      },
    ],
  },
  mobileNetwork: {
    name: '移动网络场景',
    description: '模拟移动设备在不同网络环境下的切换',
    steps: [
      {
        name: '4G网络',
        duration: 10000,
        config: PRESETS.mobile4g.config,
      },
      {
        name: '网络切换中',
        duration: 3000,
        config: {
          ...PRESETS.terrible.config,
          enableRandomDisconnect: true,
          disconnectProbability: 0.3,
        },
      },
      {
        name: '3G网络',
        duration: 10000,
        config: PRESETS.mobile3g.config,
      },
      {
        name: '网络切换中',
        duration: 3000,
        config: {
          ...PRESETS.terrible.config,
          enableRandomDisconnect: true,
          disconnectProbability: 0.3,
        },
      },
      {
        name: '4G网络',
        duration: 10000,
        config: PRESETS.mobile4g.config,
      },
    ],
  },
};

interface NetworkSimulatorPanelProps {
  /** 当前配置 */
  config: NetworkSimulatorConfig;
  /** 配置变更回调 */
  onConfigChange?: (config: NetworkSimulatorConfig) => void;
  /** 模拟断线回调 */
  onSimulateDisconnect?: () => void;
  /** 模拟重连回调 */
  onSimulateReconnect?: () => void;
}

/**
 * 网络模拟器面板组件
 */
const NetworkSimulatorPanel: React.FC<NetworkSimulatorPanelProps> = ({
  config,
  onConfigChange,
  onSimulateDisconnect,
  onSimulateReconnect,
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [localConfig, setLocalConfig] = useState<NetworkSimulatorConfig>(config);
  const [preset, setPreset] = useState<string>('custom');
  const [scenarioModalVisible, setScenarioModalVisible] = useState<boolean>(false);
  const [selectedScenario, setSelectedScenario] = useState<string>('');
  const [runningScenario, setRunningScenario] = useState<boolean>(false);
  const [currentScenarioStep, setCurrentScenarioStep] = useState<number>(0);
  const [scenarioProgress, setScenarioProgress] = useState<number>(0);
  const [scenarioTimerId, setScenarioTimerId] = useState<number | null>(null);
  const [customScenarios, setCustomScenarios] = useState<Record<string, ScenarioConfig>>({});
  const [saveScenarioModalVisible, setSaveScenarioModalVisible] = useState<boolean>(false);
  const [newScenarioName, setNewScenarioName] = useState<string>('');
  const [newScenarioDescription, setNewScenarioDescription] = useState<string>('');

  // 当配置变更时更新表单
  useEffect(() => {
    form.setFieldsValue(config);
    setLocalConfig(config);

    // 检查是否匹配预设
    const matchedPreset = Object.entries(PRESETS).find(([_, presetConfig]) => {
      const presetValues = presetConfig.config;
      return (
        presetValues.latency === config.latency &&
        presetValues.latencyJitter === config.latencyJitter &&
        presetValues.packetLoss === config.packetLoss &&
        presetValues.bandwidthLimit === config.bandwidthLimit &&
        presetValues.enableRandomDisconnect === config.enableRandomDisconnect &&
        presetValues.disconnectProbability === config.disconnectProbability &&
        presetValues.reconnectTime === config.reconnectTime
      );
    });

    setPreset(matchedPreset ? matchedPreset[0] : 'custom');
  }, [config, form]);

  // 处理表单变更
  const handleFormChange = (changedValues: any, allValues: any) => {
    setLocalConfig(allValues);
    setPreset('custom');
  };

  // 应用配置
  const handleApply = () => {
    if (onConfigChange) {
      onConfigChange(localConfig);
    }
  };

  // 重置配置
  const handleReset = () => {
    form.setFieldsValue(config);
    setLocalConfig(config);
  };

  // 应用预设
  const handlePresetChange = (value: string) => {
    if (value === 'custom') {
      return;
    }

    const presetConfig = PRESETS[value as keyof typeof PRESETS].config;
    form.setFieldsValue(presetConfig);
    setLocalConfig(presetConfig);
    setPreset(value);

    if (onConfigChange) {
      onConfigChange(presetConfig);
    }
  };

  // 打开场景模拟对话框
  const handleOpenScenarioModal = () => {
    setScenarioModalVisible(true);
  };

  // 关闭场景模拟对话框
  const handleCloseScenarioModal = () => {
    setScenarioModalVisible(false);
  };

  // 选择场景
  const handleSelectScenario = (scenarioId: string) => {
    setSelectedScenario(scenarioId);
  };

  // 开始场景模拟
  const handleStartScenario = () => {
    if (!selectedScenario) {
      return;
    }

    // 获取场景配置
    const scenario = SCENARIOS[selectedScenario] || customScenarios[selectedScenario];
    if (!scenario) {
      return;
    }

    // 关闭对话框
    setScenarioModalVisible(false);

    // 设置为运行状态
    setRunningScenario(true);
    setCurrentScenarioStep(0);
    setScenarioProgress(0);

    // 应用第一步配置
    if (scenario.steps.length > 0) {
      const firstStep = scenario.steps[0];
      if (onConfigChange) {
        onConfigChange(firstStep.config);
      }
      form.setFieldsValue(firstStep.config);
      setLocalConfig(firstStep.config);
    }

    // 启动场景定时器
    runScenarioStep(scenario, 0);
  };

  // 运行场景步骤
  const runScenarioStep = (scenario: ScenarioConfig, stepIndex: number) => {
    if (stepIndex >= scenario.steps.length) {
      // 场景结束
      setRunningScenario(false);
      return;
    }

    const step = scenario.steps[stepIndex];
    const totalDuration = scenario.steps.reduce((sum, s) => sum + s.duration, 0);
    const elapsedDuration = scenario.steps.slice(0, stepIndex).reduce((sum, s) => sum + s.duration, 0);

    // 更新进度
    setScenarioProgress(Math.round((elapsedDuration / totalDuration) * 100));

    // 设置当前步骤
    setCurrentScenarioStep(stepIndex);

    // 应用当前步骤配置
    if (onConfigChange) {
      onConfigChange(step.config);
    }
    form.setFieldsValue(step.config);
    setLocalConfig(step.config);

    // 设置下一步定时器
    const timerId = window.setTimeout(() => {
      runScenarioStep(scenario, stepIndex + 1);
    }, step.duration);

    setScenarioTimerId(timerId);
  };

  // 停止场景模拟
  const handleStopScenario = () => {
    if (scenarioTimerId !== null) {
      clearTimeout(scenarioTimerId);
      setScenarioTimerId(null);
    }

    setRunningScenario(false);
  };

  // 打开保存场景对话框
  const handleOpenSaveScenarioModal = () => {
    setNewScenarioName('');
    setNewScenarioDescription('');
    setSaveScenarioModalVisible(true);
  };

  // 关闭保存场景对话框
  const handleCloseSaveScenarioModal = () => {
    setSaveScenarioModalVisible(false);
  };

  // 保存当前配置为自定义场景
  const handleSaveScenario = () => {
    if (!newScenarioName) {
      return;
    }

    // 创建新场景
    const newScenario: ScenarioConfig = {
      name: newScenarioName,
      description: newScenarioDescription || '自定义场景',
      steps: [
        {
          name: '自定义配置',
          duration: 60000, // 默认1分钟
          config: { ...localConfig },
        },
      ],
    };

    // 添加到自定义场景列表
    setCustomScenarios(prev => ({
      ...prev,
      [`custom_${Date.now()}`]: newScenario,
    }));

    // 关闭对话框
    setSaveScenarioModalVisible(false);
  };

  return (
    <div className="network-simulator-panel">
      <Card
        title={
          <Space>
            <ExperimentOutlined />
            {t('network.simulator.title')}
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<PlayCircleOutlined />}
              onClick={handleOpenScenarioModal}
              disabled={!localConfig.enabled || runningScenario}
            >
              {t('network.simulator.scenarioSimulation')}
            </Button>
            <Button
              icon={<DisconnectOutlined />}
              onClick={onSimulateDisconnect}
              disabled={!localConfig.enabled || runningScenario}
            >
              {t('network.simulator.simulateDisconnect')}
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={onSimulateReconnect}
              disabled={!localConfig.enabled || runningScenario}
            >
              {t('network.simulator.simulateReconnect')}
            </Button>
            {runningScenario && (
              <Button
                icon={<StopOutlined />}
                onClick={handleStopScenario}
                danger
              >
                {t('network.simulator.stopScenario')}
              </Button>
            )}
          </Space>
        }
      >
        <Alert
          message={t('network.simulator.warning')}
          description={t('network.simulator.warningDescription')}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form
          form={form}
          layout="vertical"
          initialValues={config}
          onValuesChange={handleFormChange}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label={t('network.simulator.preset')}
                name="preset"
              >
                <Select value={preset} onChange={handlePresetChange}>
                  <Option value="custom">{t('network.simulator.custom')}</Option>
                  <Option value="perfect">{PRESETS.perfect.name}</Option>
                  <Option value="good">{PRESETS.good.name}</Option>
                  <Option value="average">{PRESETS.average.name}</Option>
                  <Option value="poor">{PRESETS.poor.name}</Option>
                  <Option value="terrible">{PRESETS.terrible.name}</Option>
                  <Option value="mobile3g">{PRESETS.mobile3g.name}</Option>
                  <Option value="mobile4g">{PRESETS.mobile4g.name}</Option>
                  <Option value="satellite">{PRESETS.satellite.name}</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label={t('network.simulator.enabled')}
            name="enabled"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Divider>{t('network.simulator.latencySettings')}</Divider>

          <Form.Item
            label={
              <span>
                {t('network.simulator.latency')}
                <Tooltip title={t('network.simulator.latencyTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            name="latency"
          >
            <Slider
              min={0}
              max={1000}
              step={10}
              marks={{
                0: '0ms',
                100: '100ms',
                300: '300ms',
                500: '500ms',
                1000: '1000ms',
              }}
            />
          </Form.Item>

          <Form.Item
            label={
              <span>
                {t('network.simulator.latencyJitter')}
                <Tooltip title={t('network.simulator.latencyJitterTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            name="latencyJitter"
          >
            <Slider
              min={0}
              max={200}
              step={5}
              marks={{
                0: '0ms',
                50: '50ms',
                100: '100ms',
                200: '200ms',
              }}
            />
          </Form.Item>

          <Divider>{t('network.simulator.packetLossSettings')}</Divider>

          <Form.Item
            label={
              <span>
                {t('network.simulator.packetLoss')}
                <Tooltip title={t('network.simulator.packetLossTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            name="packetLoss"
          >
            <Slider
              min={0}
              max={0.3}
              step={0.01}
              tipFormatter={(value) => `${(value * 100).toFixed(0)}%`}
              marks={{
                0: '0%',
                0.05: '5%',
                0.1: '10%',
                0.2: '20%',
                0.3: '30%',
              }}
            />
          </Form.Item>

          <Divider>{t('network.simulator.bandwidthSettings')}</Divider>

          <Form.Item
            label={
              <span>
                {t('network.simulator.bandwidthLimit')}
                <Tooltip title={t('network.simulator.bandwidthLimitTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            name="bandwidthLimit"
          >
            <Slider
              min={0}
              max={2000000}
              step={50000}
              tipFormatter={(value) => {
                if (value === 0) return t('network.simulator.unlimited');
                if (value < 1024) return `${value} B/s`;
                if (value < 1024 * 1024) return `${(value / 1024).toFixed(0)} KB/s`;
                return `${(value / (1024 * 1024)).toFixed(1)} MB/s`;
              }}
              marks={{
                0: t('network.simulator.unlimited'),
                100000: '100KB/s',
                500000: '500KB/s',
                1000000: '1MB/s',
                2000000: '2MB/s',
              }}
            />
          </Form.Item>

          <Divider>{t('network.simulator.disconnectSettings')}</Divider>

          <Form.Item
            label={t('network.simulator.enableRandomDisconnect')}
            name="enableRandomDisconnect"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label={
              <span>
                {t('network.simulator.disconnectProbability')}
                <Tooltip title={t('network.simulator.disconnectProbabilityTooltip')}>
                  <InfoCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </span>
            }
            name="disconnectProbability"
          >
            <Slider
              min={0}
              max={0.05}
              step={0.001}
              tipFormatter={(value) => `${(value * 100).toFixed(1)}%`}
              marks={{
                0: '0%',
                0.01: '1%',
                0.02: '2%',
                0.05: '5%',
              }}
              disabled={!form.getFieldValue('enableRandomDisconnect')}
            />
          </Form.Item>

          <Form.Item
            label={t('network.simulator.reconnectTime')}
            name="reconnectTime"
          >
            <Slider
              min={1000}
              max={10000}
              step={500}
              tipFormatter={(value) => `${(value / 1000).toFixed(1)}s`}
              marks={{
                1000: '1s',
                3000: '3s',
                5000: '5s',
                10000: '10s',
              }}
              disabled={!form.getFieldValue('enableRandomDisconnect')}
            />
          </Form.Item>

          <Divider />

          <Form.Item>
            <Space>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleApply}
              >
                {t('network.simulator.apply')}
              </Button>
              <Button
                icon={<UndoOutlined />}
                onClick={handleReset}
              >
                {t('network.simulator.reset')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 场景模拟对话框 */}
      <Modal
        title={t('network.simulator.scenarioSimulation')}
        open={scenarioModalVisible}
        onCancel={handleCloseScenarioModal}
        footer={[
          <Button key="cancel" onClick={handleCloseScenarioModal}>
            {t('common.cancel')}
          </Button>,
          <Button
            key="start"
            type="primary"
            onClick={handleStartScenario}
            disabled={!selectedScenario}
          >
            {t('network.simulator.startScenario')}
          </Button>,
        ]}
        width={700}
      >
        <div className="scenario-list">
          <Paragraph>{t('network.simulator.selectScenario')}</Paragraph>

          <Divider orientation="left">{t('network.simulator.predefinedScenarios')}</Divider>
          <Row gutter={[16, 16]}>
            {Object.entries(SCENARIOS).map(([id, scenario]) => (
              <Col span={12} key={id}>
                <Card
                  hoverable
                  className={`scenario-card ${selectedScenario === id ? 'selected' : ''}`}
                  onClick={() => handleSelectScenario(id)}
                >
                  <h4>{scenario.name}</h4>
                  <p>{scenario.description}</p>
                  <div className="scenario-steps">
                    <Steps size="small" direction="vertical" current={-1}>
                      {scenario.steps.map((step, index) => (
                        <Step
                          key={index}
                          title={step.name}
                          description={`${(step.duration / 1000).toFixed(0)}秒`}
                        />
                      ))}
                    </Steps>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>

          {Object.keys(customScenarios).length > 0 && (
            <>
              <Divider orientation="left">{t('network.simulator.customScenarios')}</Divider>
              <Row gutter={[16, 16]}>
                {Object.entries(customScenarios).map(([id, scenario]) => (
                  <Col span={12} key={id}>
                    <Card
                      hoverable
                      className={`scenario-card ${selectedScenario === id ? 'selected' : ''}`}
                      onClick={() => handleSelectScenario(id)}
                    >
                      <h4>{scenario.name}</h4>
                      <p>{scenario.description}</p>
                      <div className="scenario-steps">
                        <Steps size="small" direction="vertical" current={-1}>
                          {scenario.steps.map((step, index) => (
                            <Step
                              key={index}
                              title={step.name}
                              description={`${(step.duration / 1000).toFixed(0)}秒`}
                            />
                          ))}
                        </Steps>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </>
          )}

          <div style={{ marginTop: 16, textAlign: 'center' }}>
            <Button
              icon={<SaveFilled />}
              onClick={handleOpenSaveScenarioModal}
            >
              {t('network.simulator.saveCurrentAsScenario')}
            </Button>
          </div>
        </div>
      </Modal>

      {/* 保存场景对话框 */}
      <Modal
        title={t('network.simulator.saveScenario')}
        open={saveScenarioModalVisible}
        onCancel={handleCloseSaveScenarioModal}
        onOk={handleSaveScenario}
        okButtonProps={{ disabled: !newScenarioName }}
      >
        <Form layout="vertical">
          <Form.Item
            label={t('network.simulator.scenarioName')}
            required
          >
            <Input
              value={newScenarioName}
              onChange={e => setNewScenarioName(e.target.value)}
              placeholder={t('network.simulator.enterScenarioName')}
            />
          </Form.Item>
          <Form.Item label={t('network.simulator.scenarioDescription')}>
            <Input.TextArea
              value={newScenarioDescription}
              onChange={e => setNewScenarioDescription(e.target.value)}
              placeholder={t('network.simulator.enterScenarioDescription')}
              rows={3}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 场景运行状态 */}
      {runningScenario && (
        <div className="scenario-progress">
          <Card>
            <div className="scenario-progress-header">
              <h3>
                {t('network.simulator.runningScenario')}:
                {SCENARIOS[selectedScenario]?.name || customScenarios[selectedScenario]?.name}
              </h3>
              <Button
                icon={<StopOutlined />}
                onClick={handleStopScenario}
                danger
                size="small"
              >
                {t('network.simulator.stop')}
              </Button>
            </div>

            <Progress percent={scenarioProgress} status="active" />

            <div className="scenario-current-step">
              <Tag color="blue">
                {t('network.simulator.currentStep')}:
                {(SCENARIOS[selectedScenario] || customScenarios[selectedScenario])
                  ?.steps[currentScenarioStep]?.name}
              </Tag>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default NetworkSimulatorPanel;
