/**
 * 布娃娃物理示例
 * 展示如何使用物理系统创建布娃娃效果
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { Engine } from '../../src/core/Engine';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Vector3 } from '../../src/math/Vector3';
import { Quaternion } from '../../src/math/Quaternion';
import { 
  PhysicsSystem, 
  PhysicsBodyComponent, 
  PhysicsColliderComponent,
  BodyType,
  ColliderType,
  PhysicsMaterialFactory,
  PhysicsConstraintComponent
} from '../../src/physics';
import { InputSystem } from '../../src/input/InputSystem';
import { KeyCode } from '../../src/input/KeyCode';

/**
 * 布娃娃物理示例
 */
export class RagdollPhysicsExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  
  /** 输入系统 */
  private inputSystem: InputSystem;
  
  /** 地面实体 */
  private ground: Entity;
  
  /** 布娃娃部件 */
  private ragdollParts: {
    torso?: Entity;
    head?: Entity;
    upperLeftArm?: Entity;
    lowerLeftArm?: Entity;
    upperRightArm?: Entity;
    lowerRightArm?: Entity;
    upperLeftLeg?: Entity;
    lowerLeftLeg?: Entity;
    upperRightLeg?: Entity;
    lowerRightLeg?: Entity;
  } = {};
  
  /** 布娃娃约束 */
  private ragdollConstraints: PhysicsConstraintComponent[] = [];
  
  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建布娃娃物理示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建场景
    this.scene = new Scene('布娃娃物理示例场景');
    
    // 创建物理系统
    this.physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.82, z: 0 },
      debug: true
    });
    
    // 创建输入系统
    this.inputSystem = new InputSystem();
    
    // 添加系统到引擎
    this.engine.addSystem(this.physicsSystem);
    this.engine.addSystem(this.inputSystem);
    
    // 初始化物理材质工厂
    PhysicsMaterialFactory.initialize();
    
    // 创建地面
    this.ground = this.createGround();
    
    // 添加地面到场景
    this.scene.addEntity(this.ground);
    
    // 注册更新回调
    this.engine.onUpdate.add(this.update.bind(this));
  }

  /**
   * 初始化示例
   */
  public initialize(): void {
    if (this.initialized) return;
    
    // 创建布娃娃
    this.createRagdoll();
    
    // 创建障碍物
    this.createObstacles();
    
    // 设置场景为活跃场景
    this.engine.getWorld().setActiveScene(this.scene);
    
    this.initialized = true;
  }
  
  /**
   * 更新
   * @param deltaTime 时间增量
   */
  private update(deltaTime: number): void {
    if (!this.initialized) return;
    
    // 处理输入
    this.handleInput();
  }
  
  /**
   * 处理输入
   */
  private handleInput(): void {
    // 重置布娃娃
    if (this.inputSystem.isKeyPressed(KeyCode.R)) {
      this.resetRagdoll();
    }
    
    // 应用随机力
    if (this.inputSystem.isKeyPressed(KeyCode.SPACE)) {
      this.applyRandomForce();
    }
  }

  /**
   * 创建地面
   * @returns 地面实体
   */
  private createGround(): Entity {
    // 创建地面实体
    const ground = new Entity('ground');
    
    // 添加变换组件
    const transform = ground.getTransform();
    transform.setPosition(0, -0.5, 0);
    transform.setScale(20, 1, 20);
    
    // 创建地面网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ color: 0x808080 });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    ground.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    ground.addComponent(new PhysicsBodyComponent({
      type: BodyType.STATIC,
      mass: 0
    }));
    
    // 添加碰撞器组件
    ground.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: 10, y: 0.5, z: 10 }
      }
    }));
    
    return ground;
  }
  
  /**
   * 创建布娃娃
   */
  private createRagdoll(): void {
    // 创建躯干
    this.ragdollParts.torso = this.createTorso();
    this.scene.addEntity(this.ragdollParts.torso);
    
    // 创建头部
    this.ragdollParts.head = this.createHead();
    this.scene.addEntity(this.ragdollParts.head);
    
    // 创建上臂
    this.ragdollParts.upperLeftArm = this.createLimb('upper_left_arm', 0.15, 0.4);
    this.ragdollParts.upperRightArm = this.createLimb('upper_right_arm', 0.15, 0.4);
    this.scene.addEntity(this.ragdollParts.upperLeftArm);
    this.scene.addEntity(this.ragdollParts.upperRightArm);
    
    // 创建下臂
    this.ragdollParts.lowerLeftArm = this.createLimb('lower_left_arm', 0.1, 0.4);
    this.ragdollParts.lowerRightArm = this.createLimb('lower_right_arm', 0.1, 0.4);
    this.scene.addEntity(this.ragdollParts.lowerLeftArm);
    this.scene.addEntity(this.ragdollParts.lowerRightArm);
    
    // 创建上腿
    this.ragdollParts.upperLeftLeg = this.createLimb('upper_left_leg', 0.15, 0.5);
    this.ragdollParts.upperRightLeg = this.createLimb('upper_right_leg', 0.15, 0.5);
    this.scene.addEntity(this.ragdollParts.upperLeftLeg);
    this.scene.addEntity(this.ragdollParts.upperRightLeg);
    
    // 创建下腿
    this.ragdollParts.lowerLeftLeg = this.createLimb('lower_left_leg', 0.1, 0.5);
    this.ragdollParts.lowerRightLeg = this.createLimb('lower_right_leg', 0.1, 0.5);
    this.scene.addEntity(this.ragdollParts.lowerLeftLeg);
    this.scene.addEntity(this.ragdollParts.lowerRightLeg);
    
    // 设置初始位置
    this.positionRagdollParts();
    
    // 创建约束
    this.createRagdollConstraints();
  }
  
  /**
   * 创建躯干
   * @returns 躯干实体
   */
  private createTorso(): Entity {
    // 创建躯干实体
    const torso = new Entity('torso');
    
    // 添加变换组件
    const transform = torso.getTransform();
    transform.setScale(0.4, 0.6, 0.2);
    
    // 创建躯干网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ color: 0x3366cc });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    torso.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    torso.addComponent(new PhysicsBodyComponent({
      type: BodyType.DYNAMIC,
      mass: 10,
      material: PhysicsMaterialFactory.getMaterial('default')
    }));
    
    // 添加碰撞器组件
    torso.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: 0.2, y: 0.3, z: 0.1 }
      }
    }));
    
    return torso;
  }
  
  /**
   * 创建头部
   * @returns 头部实体
   */
  private createHead(): Entity {
    // 创建头部实体
    const head = new Entity('head');
    
    // 添加变换组件
    const transform = head.getTransform();
    transform.setScale(0.3, 0.3, 0.3);
    
    // 创建头部网格
    const geometry = new THREE.SphereGeometry(0.5, 16, 16);
    const material = new THREE.MeshStandardMaterial({ color: 0xffcc99 });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    head.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    head.addComponent(new PhysicsBodyComponent({
      type: BodyType.DYNAMIC,
      mass: 3,
      material: PhysicsMaterialFactory.getMaterial('default')
    }));
    
    // 添加碰撞器组件
    head.addComponent(new PhysicsColliderComponent({
      type: ColliderType.SPHERE,
      params: {
        radius: 0.15
      }
    }));
    
    return head;
  }
  
  /**
   * 创建肢体
   * @param name 肢体名称
   * @param radius 肢体半径
   * @param height 肢体高度
   * @returns 肢体实体
   */
  private createLimb(name: string, radius: number, height: number): Entity {
    // 创建肢体实体
    const limb = new Entity(name);
    
    // 添加变换组件
    const transform = limb.getTransform();
    transform.setScale(radius * 2, height, radius * 2);
    
    // 创建肢体网格
    const geometry = new THREE.CapsuleGeometry(0.5, 1, 8, 8);
    const material = new THREE.MeshStandardMaterial({ color: 0x3366cc });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    limb.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    limb.addComponent(new PhysicsBodyComponent({
      type: BodyType.DYNAMIC,
      mass: 2,
      material: PhysicsMaterialFactory.getMaterial('default')
    }));
    
    // 添加碰撞器组件
    limb.addComponent(new PhysicsColliderComponent({
      type: ColliderType.CAPSULE,
      params: {
        radius: radius,
        height: height
      }
    }));
    
    return limb;
  }
  
  /**
   * 设置布娃娃部件位置
   */
  private positionRagdollParts(): void {
    if (!this.ragdollParts.torso) return;
    
    // 设置躯干位置
    this.ragdollParts.torso.getTransform().setPosition(0, 3, 0);
    
    // 设置头部位置
    if (this.ragdollParts.head) {
      this.ragdollParts.head.getTransform().setPosition(0, 3.45, 0);
    }
    
    // 设置上臂位置
    if (this.ragdollParts.upperLeftArm) {
      this.ragdollParts.upperLeftArm.getTransform().setPosition(-0.3, 3.1, 0);
      this.ragdollParts.upperLeftArm.getTransform().setRotation(0, 0, -Math.PI / 4);
    }
    
    if (this.ragdollParts.upperRightArm) {
      this.ragdollParts.upperRightArm.getTransform().setPosition(0.3, 3.1, 0);
      this.ragdollParts.upperRightArm.getTransform().setRotation(0, 0, Math.PI / 4);
    }
    
    // 设置下臂位置
    if (this.ragdollParts.lowerLeftArm) {
      this.ragdollParts.lowerLeftArm.getTransform().setPosition(-0.5, 2.8, 0);
      this.ragdollParts.lowerLeftArm.getTransform().setRotation(0, 0, -Math.PI / 3);
    }
    
    if (this.ragdollParts.lowerRightArm) {
      this.ragdollParts.lowerRightArm.getTransform().setPosition(0.5, 2.8, 0);
      this.ragdollParts.lowerRightArm.getTransform().setRotation(0, 0, Math.PI / 3);
    }
    
    // 设置上腿位置
    if (this.ragdollParts.upperLeftLeg) {
      this.ragdollParts.upperLeftLeg.getTransform().setPosition(-0.15, 2.4, 0);
      this.ragdollParts.upperLeftLeg.getTransform().setRotation(0, 0, -Math.PI / 16);
    }
    
    if (this.ragdollParts.upperRightLeg) {
      this.ragdollParts.upperRightLeg.getTransform().setPosition(0.15, 2.4, 0);
      this.ragdollParts.upperRightLeg.getTransform().setRotation(0, 0, Math.PI / 16);
    }
    
    // 设置下腿位置
    if (this.ragdollParts.lowerLeftLeg) {
      this.ragdollParts.lowerLeftLeg.getTransform().setPosition(-0.15, 1.9, 0);
    }
    
    if (this.ragdollParts.lowerRightLeg) {
      this.ragdollParts.lowerRightLeg.getTransform().setPosition(0.15, 1.9, 0);
    }
  }
  
  /**
   * 创建布娃娃约束
   */
  private createRagdollConstraints(): void {
    // 头部与躯干的约束
    if (this.ragdollParts.head && this.ragdollParts.torso) {
      const neckConstraint = new PhysicsConstraintComponent({
        type: 'point',
        entityA: this.ragdollParts.torso,
        entityB: this.ragdollParts.head,
        pivotA: new Vector3(0, 0.3, 0),
        pivotB: new Vector3(0, -0.15, 0),
        collideConnected: false
      });
      this.ragdollParts.torso.addComponent(neckConstraint);
      this.ragdollConstraints.push(neckConstraint);
    }
    
    // 上臂与躯干的约束
    if (this.ragdollParts.upperLeftArm && this.ragdollParts.torso) {
      const leftShoulderConstraint = new PhysicsConstraintComponent({
        type: 'hinge',
        entityA: this.ragdollParts.torso,
        entityB: this.ragdollParts.upperLeftArm,
        pivotA: new Vector3(-0.2, 0.2, 0),
        pivotB: new Vector3(0, 0.2, 0),
        axisA: new Vector3(0, 0, 1),
        axisB: new Vector3(0, 0, 1),
        collideConnected: false
      });
      this.ragdollParts.torso.addComponent(leftShoulderConstraint);
      this.ragdollConstraints.push(leftShoulderConstraint);
    }
    
    if (this.ragdollParts.upperRightArm && this.ragdollParts.torso) {
      const rightShoulderConstraint = new PhysicsConstraintComponent({
        type: 'hinge',
        entityA: this.ragdollParts.torso,
        entityB: this.ragdollParts.upperRightArm,
        pivotA: new Vector3(0.2, 0.2, 0),
        pivotB: new Vector3(0, 0.2, 0),
        axisA: new Vector3(0, 0, 1),
        axisB: new Vector3(0, 0, 1),
        collideConnected: false
      });
      this.ragdollParts.torso.addComponent(rightShoulderConstraint);
      this.ragdollConstraints.push(rightShoulderConstraint);
    }
    
    // 下臂与上臂的约束
    if (this.ragdollParts.lowerLeftArm && this.ragdollParts.upperLeftArm) {
      const leftElbowConstraint = new PhysicsConstraintComponent({
        type: 'hinge',
        entityA: this.ragdollParts.upperLeftArm,
        entityB: this.ragdollParts.lowerLeftArm,
        pivotA: new Vector3(0, -0.2, 0),
        pivotB: new Vector3(0, 0.2, 0),
        axisA: new Vector3(0, 0, 1),
        axisB: new Vector3(0, 0, 1),
        collideConnected: false
      });
      this.ragdollParts.upperLeftArm.addComponent(leftElbowConstraint);
      this.ragdollConstraints.push(leftElbowConstraint);
    }
    
    if (this.ragdollParts.lowerRightArm && this.ragdollParts.upperRightArm) {
      const rightElbowConstraint = new PhysicsConstraintComponent({
        type: 'hinge',
        entityA: this.ragdollParts.upperRightArm,
        entityB: this.ragdollParts.lowerRightArm,
        pivotA: new Vector3(0, -0.2, 0),
        pivotB: new Vector3(0, 0.2, 0),
        axisA: new Vector3(0, 0, 1),
        axisB: new Vector3(0, 0, 1),
        collideConnected: false
      });
      this.ragdollParts.upperRightArm.addComponent(rightElbowConstraint);
      this.ragdollConstraints.push(rightElbowConstraint);
    }
    
    // 上腿与躯干的约束
    if (this.ragdollParts.upperLeftLeg && this.ragdollParts.torso) {
      const leftHipConstraint = new PhysicsConstraintComponent({
        type: 'hinge',
        entityA: this.ragdollParts.torso,
        entityB: this.ragdollParts.upperLeftLeg,
        pivotA: new Vector3(-0.1, -0.3, 0),
        pivotB: new Vector3(0, 0.25, 0),
        axisA: new Vector3(0, 0, 1),
        axisB: new Vector3(0, 0, 1),
        collideConnected: false
      });
      this.ragdollParts.torso.addComponent(leftHipConstraint);
      this.ragdollConstraints.push(leftHipConstraint);
    }
    
    if (this.ragdollParts.upperRightLeg && this.ragdollParts.torso) {
      const rightHipConstraint = new PhysicsConstraintComponent({
        type: 'hinge',
        entityA: this.ragdollParts.torso,
        entityB: this.ragdollParts.upperRightLeg,
        pivotA: new Vector3(0.1, -0.3, 0),
        pivotB: new Vector3(0, 0.25, 0),
        axisA: new Vector3(0, 0, 1),
        axisB: new Vector3(0, 0, 1),
        collideConnected: false
      });
      this.ragdollParts.torso.addComponent(rightHipConstraint);
      this.ragdollConstraints.push(rightHipConstraint);
    }
    
    // 下腿与上腿的约束
    if (this.ragdollParts.lowerLeftLeg && this.ragdollParts.upperLeftLeg) {
      const leftKneeConstraint = new PhysicsConstraintComponent({
        type: 'hinge',
        entityA: this.ragdollParts.upperLeftLeg,
        entityB: this.ragdollParts.lowerLeftLeg,
        pivotA: new Vector3(0, -0.25, 0),
        pivotB: new Vector3(0, 0.25, 0),
        axisA: new Vector3(0, 0, 1),
        axisB: new Vector3(0, 0, 1),
        collideConnected: false
      });
      this.ragdollParts.upperLeftLeg.addComponent(leftKneeConstraint);
      this.ragdollConstraints.push(leftKneeConstraint);
    }
    
    if (this.ragdollParts.lowerRightLeg && this.ragdollParts.upperRightLeg) {
      const rightKneeConstraint = new PhysicsConstraintComponent({
        type: 'hinge',
        entityA: this.ragdollParts.upperRightLeg,
        entityB: this.ragdollParts.lowerRightLeg,
        pivotA: new Vector3(0, -0.25, 0),
        pivotB: new Vector3(0, 0.25, 0),
        axisA: new Vector3(0, 0, 1),
        axisB: new Vector3(0, 0, 1),
        collideConnected: false
      });
      this.ragdollParts.upperRightLeg.addComponent(rightKneeConstraint);
      this.ragdollConstraints.push(rightKneeConstraint);
    }
  }
  
  /**
   * 创建障碍物
   */
  private createObstacles(): void {
    // 创建坡道
    this.createRamp(3, 0, 0, 3, 1, 3, Math.PI / 12);
    
    // 创建盒子障碍物
    for (let i = 0; i < 5; i++) {
      this.createBox(
        Math.random() * 6 - 3,
        1,
        Math.random() * 6 - 3,
        Math.random() * 0.3 + 0.2
      );
    }
  }
  
  /**
   * 创建坡道
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   * @param width 宽度
   * @param height 高度
   * @param length 长度
   * @param angle 角度
   */
  private createRamp(x: number, y: number, z: number, width: number, height: number, length: number, angle: number): void {
    // 创建坡道实体
    const ramp = new Entity('ramp');
    
    // 添加变换组件
    const transform = ramp.getTransform();
    transform.setPosition(x, y + height / 2, z);
    transform.setRotation(angle, 0, 0);
    transform.setScale(width, height, length);
    
    // 创建坡道网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ color: 0x8080a0 });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    ramp.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    ramp.addComponent(new PhysicsBodyComponent({
      type: BodyType.STATIC,
      mass: 0
    }));
    
    // 添加碰撞器组件
    ramp.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: width / 2, y: height / 2, z: length / 2 }
      }
    }));
    
    // 添加到场景
    this.scene.addEntity(ramp);
  }
  
  /**
   * 创建盒子
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   * @param size 盒子大小
   */
  private createBox(x: number, y: number, z: number, size: number): void {
    // 创建盒子实体
    const box = new Entity(`obstacle_box_${Math.random().toString(36).substr(2, 5)}`);
    
    // 添加变换组件
    const transform = box.getTransform();
    transform.setPosition(x, y, z);
    transform.setScale(size, size, size);
    
    // 创建盒子网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ 
      color: new THREE.Color(Math.random(), Math.random(), Math.random()) 
    });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    box.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    box.addComponent(new PhysicsBodyComponent({
      type: BodyType.DYNAMIC,
      mass: size * 10,
      material: PhysicsMaterialFactory.getMaterial('wood')
    }));
    
    // 添加碰撞器组件
    box.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: size / 2, y: size / 2, z: size / 2 }
      }
    }));
    
    // 添加到场景
    this.scene.addEntity(box);
  }
  
  /**
   * 应用随机力
   */
  private applyRandomForce(): void {
    if (!this.ragdollParts.torso) return;
    
    // 获取躯干物理体
    const torsoBody = this.ragdollParts.torso.getComponent(PhysicsBodyComponent);
    if (!torsoBody) return;
    
    // 应用随机力
    const force = new Vector3(
      (Math.random() - 0.5) * 500,
      Math.random() * 500,
      (Math.random() - 0.5) * 500
    );
    
    torsoBody.applyImpulse(force);
  }
  
  /**
   * 重置布娃娃位置
   */
  public resetRagdoll(): void {
    // 重置所有部件位置
    this.positionRagdollParts();
    
    // 重置所有部件速度
    for (const partName in this.ragdollParts) {
      const part = this.ragdollParts[partName];
      if (part) {
        const body = part.getComponent(PhysicsBodyComponent);
        if (body) {
          body.setLinearVelocity(new Vector3(0, 0, 0));
          body.setAngularVelocity(new Vector3(0, 0, 0));
        }
      }
    }
  }
}
