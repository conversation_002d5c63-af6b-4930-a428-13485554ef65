/**
 * 媒体流约束定义
 */

export interface VideoConstraints extends MediaTrackConstraints {
  /** 视频宽度 */
  width?: ConstrainULong;
  /** 视频高度 */
  height?: ConstrainULong;
  /** 帧率 */
  frameRate?: ConstrainDouble;
  /** 设备ID */
  deviceId?: ConstrainDOMString;
  /** 面向模式 */
  facingMode?: ConstrainDOMString;
}

export interface AudioConstraints extends MediaTrackConstraints {
  /** 设备ID */
  deviceId?: ConstrainDOMString;
  /** 自动增益控制 */
  autoGainControl?: ConstrainBoolean;
  /** 回声消除 */
  echoCancellation?: ConstrainBoolean;
  /** 噪声抑制 */
  noiseSuppression?: ConstrainBoolean;
  /** 采样率 */
  sampleRate?: ConstrainULong;
  /** 采样大小 */
  sampleSize?: ConstrainULong;
  /** 声道数 */
  channelCount?: ConstrainULong;
}

export interface CustomMediaStreamConstraints {
  /** 音频约束 */
  audio?: boolean | AudioConstraints;
  /** 视频约束 */
  video?: boolean | VideoConstraints;
  /** 屏幕共享约束 */
  screen?: boolean | {
    video?: boolean | VideoConstraints;
    audio?: boolean | AudioConstraints;
  };
}

export class MediaStreamConstraintsBuilder {
  private constraints: CustomMediaStreamConstraints = {};

  public setAudio(enabled: boolean, constraints?: AudioConstraints): this {
    this.constraints.audio = enabled ? (constraints || true) : false;
    return this;
  }

  public setVideo(enabled: boolean, constraints?: VideoConstraints): this {
    this.constraints.video = enabled ? (constraints || true) : false;
    return this;
  }

  public setScreenShare(enabled: boolean, options?: {
    video?: boolean | VideoConstraints;
    audio?: boolean | AudioConstraints;
  }): this {
    this.constraints.screen = enabled ? (options || { video: true, audio: false }) : false;
    return this;
  }

  public build(): MediaStreamConstraints {
    const result: MediaStreamConstraints = {};

    if (this.constraints.audio) {
      result.audio = this.constraints.audio;
    }

    if (this.constraints.video) {
      result.video = this.constraints.video;
    }

    return result;
  }

  public static createDefault(): MediaStreamConstraintsBuilder {
    return new MediaStreamConstraintsBuilder()
      .setAudio(true, {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      })
      .setVideo(true, {
        width: { ideal: 1280 },
        height: { ideal: 720 },
        frameRate: { ideal: 30 }
      });
  }

  public static createAudioOnly(): MediaStreamConstraintsBuilder {
    return new MediaStreamConstraintsBuilder()
      .setAudio(true, {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      })
      .setVideo(false);
  }

  public static createVideoOnly(): MediaStreamConstraintsBuilder {
    return new MediaStreamConstraintsBuilder()
      .setAudio(false)
      .setVideo(true, {
        width: { ideal: 1280 },
        height: { ideal: 720 },
        frameRate: { ideal: 30 }
      });
  }
}
