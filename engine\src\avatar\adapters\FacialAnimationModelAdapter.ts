/**
 * 面部动画模型适配器
 * 用于将面部动画系统与具体的3D模型绑定
 */
import * as THREE from 'three';

import { Component } from '../../core/Component';
import { FacialExpressionType, VisemeType } from '../components/FacialAnimationComponent';

/**
 * 面部动画模型类型
 */
export enum FacialAnimationModelType {
  /** 通用模型 */
  GENERIC = 'generic',
  /** VRM模型 */
  VRM = 'vrm',
  /** FBX模型 */
  FBX = 'fbx',
  /** GLTF模型 */
  GLTF = 'gltf'
}

/**
 * 表情映射
 */
export interface ExpressionMapping {
  /** 表情类型 */
  expression: FacialExpressionType;
  /** 混合形状名称 */
  blendShapeName: string;
  /** 混合形状索引 */
  blendShapeIndex?: number;
  /** 权重缩放 */
  weightScale?: number;
  /** 权重偏移 */
  weightOffset?: number;
}

/**
 * 口型映射
 */
export interface VisemeMapping {
  /** 口型类型 */
  viseme: VisemeType;
  /** 混合形状名称 */
  blendShapeName: string;
  /** 混合形状索引 */
  blendShapeIndex?: number;
  /** 权重缩放 */
  weightScale?: number;
  /** 权重偏移 */
  weightOffset?: number;
}

/**
 * 面部动画模型适配器组件类型
 */
export const FacialAnimationModelAdapterComponentType = 'FacialAnimationModelAdapterComponent';

/**
 * 面部动画模型适配器组件
 */
export class FacialAnimationModelAdapterComponent extends Component {
  /** 组件类型 */
  static readonly TYPE = FacialAnimationModelAdapterComponentType;

  /** 模型类型 */
  private modelType: FacialAnimationModelType = FacialAnimationModelType.GENERIC;

  /** 骨骼网格 */
  private skinnedMesh: THREE.SkinnedMesh | null = null;

  /** 表情映射 */
  private expressionMappings: Map<FacialExpressionType, ExpressionMapping> = new Map();

  /** 口型映射 */
  private visemeMappings: Map<VisemeType, VisemeMapping> = new Map();

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 调试模式 */
  private debug: boolean = false;

  /**
   * 构造函数
   * @param mesh 骨骼网格
   * @param modelType 模型类型
   * @param debug 调试模式
   */
  constructor(mesh: THREE.SkinnedMesh, modelType: FacialAnimationModelType = FacialAnimationModelType.GENERIC, debug: boolean = false) {
    super(FacialAnimationModelAdapterComponent.TYPE);

    this.skinnedMesh = mesh;
    this.modelType = modelType;
    this.debug = debug;

    // 初始化
    this.initialize();
  }

  /**
   * 获取组件类型
   */
  public getType(): string {
    return FacialAnimationModelAdapterComponent.TYPE;
  }

  /**
   * 初始化
   */
  private initialize(): void {
    if (!this.skinnedMesh) {
      console.warn('未设置骨骼网格');
      return;
    }

    // 检测混合形状
    if (this.skinnedMesh.morphTargetDictionary) {
      switch (this.modelType) {
        case FacialAnimationModelType.VRM:
          this.detectVRMBlendShapes(this.skinnedMesh.morphTargetDictionary);
          break;
        case FacialAnimationModelType.FBX:
          this.detectFBXBlendShapes(this.skinnedMesh.morphTargetDictionary);
          break;
        case FacialAnimationModelType.GLTF:
          this.detectGLTFBlendShapes(this.skinnedMesh.morphTargetDictionary);
          break;
        default:
          this.detectGenericBlendShapes(this.skinnedMesh.morphTargetDictionary);
          break;
      }
    }

    this.initialized = true;

    if (this.debug) {
      console.log('面部动画模型适配器初始化完成');
      console.log('表情映射:', this.expressionMappings);
      console.log('口型映射:', this.visemeMappings);
    }
  }

  /**
   * 检测通用混合形状
   * @param morphTargetDictionary 混合形状字典
   */
  private detectGenericBlendShapes(morphTargetDictionary: { [key: string]: number }): void {
    // 通用表情映射
    const genericExpressionMap: { [key: string]: FacialExpressionType } = {
      'neutral': FacialExpressionType.NEUTRAL,
      'happy': FacialExpressionType.HAPPY,
      'sad': FacialExpressionType.SAD,
      'angry': FacialExpressionType.ANGRY,
      'surprised': FacialExpressionType.SURPRISED,
      'fear': FacialExpressionType.FEARFUL,
      'disgust': FacialExpressionType.DISGUSTED,
      'contempt': FacialExpressionType.CONTEMPT,
      'smile': FacialExpressionType.HAPPY,
      'frown': FacialExpressionType.SAD
    };

    // 通用口型映射
    const genericVisemeMap: { [key: string]: VisemeType } = {
      'viseme_aa': VisemeType.AA,
      'viseme_ee': VisemeType.EE,
      'viseme_ih': VisemeType.IH,
      'viseme_oh': VisemeType.OH,
      'viseme_ou': VisemeType.OU,
      'viseme_pp': VisemeType.PP,
      'viseme_ff': VisemeType.FF,
      'viseme_th': VisemeType.TH,
      'viseme_dd': VisemeType.DD,
      'viseme_kk': VisemeType.KK,
      'viseme_ch': VisemeType.CH,
      'viseme_ss': VisemeType.SS,
      'viseme_nn': VisemeType.NN,
      'viseme_rr': VisemeType.RR,
      'viseme_mm': VisemeType.MM
    };

    // 检测表情混合形状
    for (const key in morphTargetDictionary) {
      const lowerKey = key.toLowerCase();

      // 检测表情
      for (const exprKey in genericExpressionMap) {
        if (lowerKey.includes(exprKey)) {
          const expression = genericExpressionMap[exprKey];
          this.expressionMappings.set(expression, {
            expression,
            blendShapeName: key,
            blendShapeIndex: morphTargetDictionary[key]
          });
        }
      }

      // 检测口型
      for (const visemeKey in genericVisemeMap) {
        if (lowerKey.includes(visemeKey)) {
          const viseme = genericVisemeMap[visemeKey];
          this.visemeMappings.set(viseme, {
            viseme,
            blendShapeName: key,
            blendShapeIndex: morphTargetDictionary[key]
          });
        }
      }
    }
  }

  /**
   * 检测VRM混合形状
   * @param morphTargetDictionary 混合形状字典
   */
  private detectVRMBlendShapes(morphTargetDictionary: { [key: string]: number }): void {
    // VRM表情映射
    const vrmExpressionMap: { [key: string]: FacialExpressionType } = {
      'neutral': FacialExpressionType.NEUTRAL,
      'happy': FacialExpressionType.HAPPY,
      'sad': FacialExpressionType.SAD,
      'angry': FacialExpressionType.ANGRY,
      'surprised': FacialExpressionType.SURPRISED,
      'relaxed': FacialExpressionType.NEUTRAL,
      'joy': FacialExpressionType.HAPPY,
      'sorrow': FacialExpressionType.SAD,
      'fun': FacialExpressionType.HAPPY,
      'aa': FacialExpressionType.SURPRISED,
      'ih': FacialExpressionType.HAPPY,
      'ou': FacialExpressionType.SURPRISED,
      'ee': FacialExpressionType.HAPPY,
      'oh': FacialExpressionType.SURPRISED
    };

    // VRM口型映射
    const vrmVisemeMap: { [key: string]: VisemeType } = {
      'a': VisemeType.AA,
      'i': VisemeType.IH,
      'u': VisemeType.OU,
      'e': VisemeType.EE,
      'o': VisemeType.OH
    };

    // 检测表情混合形状
    for (const key in morphTargetDictionary) {
      const lowerKey = key.toLowerCase();

      // 检测表情
      for (const exprKey in vrmExpressionMap) {
        if (lowerKey.includes(exprKey)) {
          const expression = vrmExpressionMap[exprKey];
          this.expressionMappings.set(expression, {
            expression,
            blendShapeName: key,
            blendShapeIndex: morphTargetDictionary[key]
          });
        }
      }

      // 检测口型
      for (const visemeKey in vrmVisemeMap) {
        if (lowerKey.includes(visemeKey)) {
          const viseme = vrmVisemeMap[visemeKey];
          this.visemeMappings.set(viseme, {
            viseme,
            blendShapeName: key,
            blendShapeIndex: morphTargetDictionary[key]
          });
        }
      }
    }
  }

  /**
   * 检测FBX混合形状
   * @param morphTargetDictionary 混合形状字典
   */
  private detectFBXBlendShapes(morphTargetDictionary: { [key: string]: number }): void {
    // FBX表情映射
    const fbxExpressionMap: { [key: string]: FacialExpressionType } = {
      'neutral': FacialExpressionType.NEUTRAL,
      'happy': FacialExpressionType.HAPPY,
      'sad': FacialExpressionType.SAD,
      'angry': FacialExpressionType.ANGRY,
      'surprised': FacialExpressionType.SURPRISED,
      'smile': FacialExpressionType.HAPPY,
      'frown': FacialExpressionType.SAD,
      'brow_up': FacialExpressionType.SURPRISED,
      'brow_down': FacialExpressionType.ANGRY
    };

    // FBX口型映射
    const fbxVisemeMap: { [key: string]: VisemeType } = {
      'mouth_open': VisemeType.AA,
      'mouth_wide': VisemeType.EE,
      'mouth_narrow': VisemeType.IH,
      'mouth_round': VisemeType.OH,
      'mouth_pucker': VisemeType.OU
    };

    // 检测表情混合形状
    for (const key in morphTargetDictionary) {
      const lowerKey = key.toLowerCase();

      // 检测表情
      for (const exprKey in fbxExpressionMap) {
        if (lowerKey.includes(exprKey)) {
          const expression = fbxExpressionMap[exprKey];
          this.expressionMappings.set(expression, {
            expression,
            blendShapeName: key,
            blendShapeIndex: morphTargetDictionary[key]
          });
        }
      }

      // 检测口型
      for (const visemeKey in fbxVisemeMap) {
        if (lowerKey.includes(visemeKey)) {
          const viseme = fbxVisemeMap[visemeKey];
          this.visemeMappings.set(viseme, {
            viseme,
            blendShapeName: key,
            blendShapeIndex: morphTargetDictionary[key]
          });
        }
      }
    }
  }

  /**
   * 检测GLTF混合形状
   * @param morphTargetDictionary 混合形状字典
   */
  private detectGLTFBlendShapes(morphTargetDictionary: { [key: string]: number }): void {
    // GLTF表情映射
    const gltfExpressionMap: { [key: string]: FacialExpressionType } = {
      'neutral': FacialExpressionType.NEUTRAL,
      'happy': FacialExpressionType.HAPPY,
      'sad': FacialExpressionType.SAD,
      'angry': FacialExpressionType.ANGRY,
      'surprised': FacialExpressionType.SURPRISED,
      'smile': FacialExpressionType.HAPPY,
      'frown': FacialExpressionType.SAD
    };

    // GLTF口型映射
    const gltfVisemeMap: { [key: string]: VisemeType } = {
      'viseme_aa': VisemeType.AA,
      'viseme_ee': VisemeType.EE,
      'viseme_ih': VisemeType.IH,
      'viseme_oh': VisemeType.OH,
      'viseme_ou': VisemeType.OU
    };

    // 检测表情混合形状
    for (const key in morphTargetDictionary) {
      const lowerKey = key.toLowerCase();

      // 检测表情
      for (const exprKey in gltfExpressionMap) {
        if (lowerKey.includes(exprKey)) {
          const expression = gltfExpressionMap[exprKey];
          this.expressionMappings.set(expression, {
            expression,
            blendShapeName: key,
            blendShapeIndex: morphTargetDictionary[key]
          });
        }
      }

      // 检测口型
      for (const visemeKey in gltfVisemeMap) {
        if (lowerKey.includes(visemeKey)) {
          const viseme = gltfVisemeMap[visemeKey];
          this.visemeMappings.set(viseme, {
            viseme,
            blendShapeName: key,
            blendShapeIndex: morphTargetDictionary[key]
          });
        }
      }
    }
  }

  /**
   * 应用表情
   * @param expression 表情类型
   * @param weight 权重
   * @returns 是否成功应用
   */
  public applyExpression(expression: FacialExpressionType, weight: number): boolean {
    if (!this.skinnedMesh || !this.initialized) return false;

    // 获取表情映射
    const mapping = this.expressionMappings.get(expression);
    if (!mapping) return false;

    // 应用混合形状权重
    const morphTargetInfluences = this.skinnedMesh.morphTargetInfluences;
    if (!morphTargetInfluences) return false;

    // 计算实际权重
    const actualWeight = weight * (mapping.weightScale || 1.0) + (mapping.weightOffset || 0.0);

    // 设置混合形状权重
    if (mapping.blendShapeIndex !== undefined && mapping.blendShapeIndex >= 0 && mapping.blendShapeIndex < morphTargetInfluences.length) {
      morphTargetInfluences[mapping.blendShapeIndex] = actualWeight;
      return true;
    }

    return false;
  }

  /**
   * 应用口型
   * @param viseme 口型类型
   * @param weight 权重
   * @returns 是否成功应用
   */
  public applyViseme(viseme: VisemeType, weight: number): boolean {
    if (!this.skinnedMesh || !this.initialized) return false;

    // 获取口型映射
    const mapping = this.visemeMappings.get(viseme);
    if (!mapping) return false;

    // 应用混合形状权重
    const morphTargetInfluences = this.skinnedMesh.morphTargetInfluences;
    if (!morphTargetInfluences) return false;

    // 计算实际权重
    const actualWeight = weight * (mapping.weightScale || 1.0) + (mapping.weightOffset || 0.0);

    // 设置混合形状权重
    if (mapping.blendShapeIndex !== undefined && mapping.blendShapeIndex >= 0 && mapping.blendShapeIndex < morphTargetInfluences.length) {
      morphTargetInfluences[mapping.blendShapeIndex] = actualWeight;
      return true;
    }

    return false;
  }
}
