/**
 * 动画遮罩
 * 用于控制动画对骨骼的影响
 */
import * as THREE from 'three';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 遮罩类型
 */
export enum MaskType {
  /** 包含 - 只影响列表中的骨骼 */
  INCLUDE = 'include',
  /** 排除 - 影响除列表外的所有骨骼 */
  EXCLUDE = 'exclude',
  /** 层级 - 影响列表中的骨骼及其子骨骼 */
  HIERARCHY = 'hierarchy',
  /** 反层级 - 影响除列表中的骨骼及其子骨骼外的所有骨骼 */
  INVERSE_HIERARCHY = 'inverseHierarchy',
  /** 组 - 影响预定义骨骼组中的骨骼 */
  GROUP = 'group',
  /** 混合 - 混合多个遮罩 */
  BLEND = 'blend',
  /** 过渡 - 在两个遮罩之间过渡 */
  TRANSITION = 'transition'
}

/**
 * 遮罩权重类型
 */
export enum MaskWeightType {
  /** 二进制 - 骨骼要么完全受影响，要么完全不受影响 */
  BINARY = 'binary',
  /** 平滑 - 骨骼可以部分受影响 */
  SMOOTH = 'smooth',
  /** 距离 - 根据骨骼与根骨骼的距离计算权重 */
  DISTANCE = 'distance',
  /** 渐变 - 根据骨骼在层级中的位置计算权重 */
  GRADIENT = 'gradient',
  /** 动态 - 根据动画状态动态计算权重 */
  DYNAMIC = 'dynamic',
  /** 曲线 - 使用曲线函数计算权重 */
  CURVE = 'curve',
  /** 混合 - 混合多个权重类型 */
  BLEND = 'blend'
}

/**
 * 骨骼组类型
 */
export enum BoneGroupType {
  /** 头部 */
  HEAD = 'head',
  /** 躯干 */
  TORSO = 'torso',
  /** 左手臂 */
  LEFT_ARM = 'leftArm',
  /** 右手臂 */
  RIGHT_ARM = 'rightArm',
  /** 左腿 */
  LEFT_LEG = 'leftLeg',
  /** 右腿 */
  RIGHT_LEG = 'rightLeg',
  /** 左手 */
  LEFT_HAND = 'leftHand',
  /** 右手 */
  RIGHT_HAND = 'rightHand',
  /** 左脚 */
  LEFT_FOOT = 'leftFoot',
  /** 右脚 */
  RIGHT_FOOT = 'rightFoot',
  /** 上半身 */
  UPPER_BODY = 'upperBody',
  /** 下半身 */
  LOWER_BODY = 'lowerBody',
  /** 左侧 */
  LEFT_SIDE = 'leftSide',
  /** 右侧 */
  RIGHT_SIDE = 'rightSide',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 动画遮罩事件类型
 */
export enum AnimationMaskEventType {
  /** 遮罩改变 */
  MASK_CHANGED = 'maskChanged',
  /** 权重改变 */
  WEIGHT_CHANGED = 'weightChanged',
  /** 遮罩类型改变 */
  TYPE_CHANGED = 'typeChanged',
  /** 权重类型改变 */
  WEIGHT_TYPE_CHANGED = 'weightTypeChanged',
  /** 遮罩应用 */
  MASK_APPLIED = 'maskApplied',
  /** 遮罩合并 */
  MASK_MERGED = 'maskMerged',
  /** 遮罩反转 */
  MASK_INVERTED = 'maskInverted',
  /** 遮罩层级更新 */
  HIERARCHY_UPDATED = 'hierarchyUpdated'
}

/**
 * 动画遮罩配置
 */
export interface AnimationMaskConfig {
  /** 遮罩名称 */
  name?: string;
  /** 遮罩类型 */
  type?: MaskType;
  /** 权重类型 */
  weightType?: MaskWeightType;
  /** 骨骼列表 */
  bones?: string[];
  /** 骨骼权重映射 */
  boneWeights?: Map<string, number>;
  /** 是否启用调试 */
  debug?: boolean;
  /** 骨骼层级映射 */
  boneHierarchy?: Map<string, string[]>;
  /** 根骨骼 */
  rootBone?: string;
  /** 距离权重配置 */
  distanceWeightConfig?: {
    /** 最大距离 */
    maxDistance: number;
    /** 最小权重 */
    minWeight: number;
    /** 衰减函数 */
    falloffFunction?: 'linear' | 'quadratic' | 'exponential' | 'custom';
    /** 自定义衰减函数 */
    customFalloff?: (distance: number, maxDistance: number) => number;
  };
  /** 渐变权重配置 */
  gradientWeightConfig?: {
    /** 开始权重 */
    startWeight: number;
    /** 结束权重 */
    endWeight: number;
    /** 渐变函数 */
    gradientFunction?: 'linear' | 'smooth' | 'custom';
    /** 自定义渐变函数 */
    customGradient?: (level: number, maxLevel: number) => number;
  };
  /** 动态权重配置 */
  dynamicWeightConfig?: {
    /** 权重更新函数 */
    updateFunction: (bone: string, time: number, clip: THREE.AnimationClip) => number;
    /** 更新频率（秒） */
    updateFrequency?: number;
  };
}

/**
 * 动画遮罩
 * 用于控制动画对骨骼的影响
 */
export class AnimationMask {
  /** 遮罩名称 */
  private name: string;
  /** 遮罩类型 */
  private type: MaskType;
  /** 权重类型 */
  private weightType: MaskWeightType;
  /** 骨骼列表 */
  private bones: Set<string> = new Set();
  /** 骨骼权重映射 */
  private boneWeights: Map<string, number> = new Map();
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 是否启用调试 */
  private debug: boolean;
  /** 骨骼层级映射 */
  private boneHierarchy: Map<string, string[]> = new Map();
  /** 根骨骼 */
  private rootBone: string = '';
  /** 距离权重配置 */
  private distanceWeightConfig?: {
    maxDistance: number;
    minWeight: number;
    falloffFunction: 'linear' | 'quadratic' | 'exponential' | 'custom';
    customFalloff?: (distance: number, maxDistance: number) => number;
  };
  /** 渐变权重配置 */
  private gradientWeightConfig?: {
    startWeight: number;
    endWeight: number;
    gradientFunction: 'linear' | 'smooth' | 'custom';
    customGradient?: (level: number, maxLevel: number) => number;
  };
  /** 动态权重配置 */
  private dynamicWeightConfig?: {
    updateFunction: (bone: string, time: number, clip: THREE.AnimationClip) => number;
    updateFrequency: number;
    lastUpdateTime: number;
  };

  /** 混合参数 */
  private blendParams?: {
    masks: AnimationMask[];
    weights: number[];
    normalize: boolean;
  };

  /** 过渡参数 */
  private transitionParams?: {
    fromMask: AnimationMask;
    toMask: AnimationMask;
    progress: number;
  };

  /**
   * 创建动画遮罩
   * @param config 配置
   */
  constructor(config: AnimationMaskConfig = {}) {
    this.name = config.name || 'mask';
    this.type = config.type || MaskType.INCLUDE;
    this.weightType = config.weightType || MaskWeightType.BINARY;
    this.debug = config.debug || false;
    this.rootBone = config.rootBone || '';

    // 初始化骨骼列表
    if (config.bones) {
      for (const bone of config.bones) {
        this.bones.add(bone);
      }
    }

    // 初始化骨骼权重
    if (config.boneWeights) {
      this.boneWeights = new Map(config.boneWeights);
    }

    // 初始化骨骼层级
    if (config.boneHierarchy) {
      this.boneHierarchy = new Map(config.boneHierarchy);
    }

    // 初始化距离权重配置
    if (config.distanceWeightConfig) {
      this.distanceWeightConfig = {
        maxDistance: config.distanceWeightConfig.maxDistance,
        minWeight: config.distanceWeightConfig.minWeight,
        falloffFunction: config.distanceWeightConfig.falloffFunction || 'linear',
        customFalloff: config.distanceWeightConfig.customFalloff
      };
    }

    // 初始化渐变权重配置
    if (config.gradientWeightConfig) {
      this.gradientWeightConfig = {
        startWeight: config.gradientWeightConfig.startWeight,
        endWeight: config.gradientWeightConfig.endWeight,
        gradientFunction: config.gradientWeightConfig.gradientFunction || 'linear',
        customGradient: config.gradientWeightConfig.customGradient
      };
    }

    // 初始化动态权重配置
    if (config.dynamicWeightConfig) {
      this.dynamicWeightConfig = {
        updateFunction: config.dynamicWeightConfig.updateFunction,
        updateFrequency: config.dynamicWeightConfig.updateFrequency || 0.1,
        lastUpdateTime: 0
      };
    }
  }

  /**
   * 获取遮罩名称
   * @returns 遮罩名称
   */
  public getName(): string {
    return this.name;
  }

  /**
   * 设置遮罩名称
   * @param name 遮罩名称
   */
  public setName(name: string): void {
    this.name = name;
  }

  /**
   * 获取遮罩类型
   * @returns 遮罩类型
   */
  public getType(): MaskType {
    return this.type;
  }

  /**
   * 设置遮罩类型
   * @param type 遮罩类型
   */
  public setType(type: MaskType): void {
    this.type = type;

    // 触发遮罩改变事件
    this.eventEmitter.emit(AnimationMaskEventType.MASK_CHANGED, { type });
  }

  /**
   * 获取权重类型
   * @returns 权重类型
   */
  public getWeightType(): MaskWeightType {
    return this.weightType;
  }

  /**
   * 设置权重类型
   * @param weightType 权重类型
   */
  public setWeightType(weightType: MaskWeightType): void {
    this.weightType = weightType;

    // 触发权重改变事件
    this.eventEmitter.emit(AnimationMaskEventType.WEIGHT_CHANGED, { weightType });
  }

  /**
   * 设置混合参数
   * @param masks 遮罩列表
   * @param weights 权重列表
   * @param normalize 是否归一化
   */
  public setBlendParams(masks: AnimationMask[], weights: number[], normalize: boolean = true): void {
    this.blendParams = { masks, weights, normalize };
  }

  /**
   * 获取混合参数
   */
  public getBlendParams(): { masks: AnimationMask[]; weights: number[]; normalize: boolean } | undefined {
    return this.blendParams;
  }

  /**
   * 设置过渡参数
   * @param fromMask 源遮罩
   * @param toMask 目标遮罩
   * @param progress 过渡进度
   */
  public setTransitionParams(fromMask: AnimationMask, toMask: AnimationMask, progress: number): void {
    this.transitionParams = { fromMask, toMask, progress };
  }

  /**
   * 获取过渡参数
   */
  public getTransitionParams(): { fromMask: AnimationMask; toMask: AnimationMask; progress: number } | undefined {
    return this.transitionParams;
  }

  /**
   * 添加骨骼
   * @param boneName 骨骼名称
   * @param weight 权重（仅在SMOOTH模式下有效）
   */
  public addBone(boneName: string, weight: number = 1.0): void {
    this.bones.add(boneName);

    // 如果是平滑权重类型，设置权重
    if (this.weightType === MaskWeightType.SMOOTH) {
      this.boneWeights.set(boneName, weight);
    }

    // 触发遮罩改变事件
    this.eventEmitter.emit(AnimationMaskEventType.MASK_CHANGED, { boneName, added: true });

    if (this.debug) {
      console.log(`添加骨骼到遮罩: ${boneName}, 权重: ${weight}`);
    }
  }

  /**
   * 移除骨骼
   * @param boneName 骨骼名称
   */
  public removeBone(boneName: string): void {
    this.bones.delete(boneName);
    this.boneWeights.delete(boneName);

    // 触发遮罩改变事件
    this.eventEmitter.emit(AnimationMaskEventType.MASK_CHANGED, { boneName, removed: true });

    if (this.debug) {
      console.log(`从遮罩移除骨骼: ${boneName}`);
    }
  }

  /**
   * 设置骨骼权重
   * @param boneName 骨骼名称
   * @param weight 权重
   */
  public setBoneWeight(boneName: string, weight: number): void {
    // 确保骨骼在列表中
    if (!this.bones.has(boneName)) {
      this.bones.add(boneName);
    }

    // 设置权重
    this.boneWeights.set(boneName, weight);

    // 触发权重改变事件
    this.eventEmitter.emit(AnimationMaskEventType.WEIGHT_CHANGED, { boneName, weight });

    if (this.debug) {
      console.log(`设置骨骼权重: ${boneName} = ${weight}`);
    }
  }

  /**
   * 获取骨骼权重
   * @param boneName 骨骼名称
   * @param skeleton 骨骼对象（用于层级和距离计算）
   * @param time 当前时间（用于动态权重计算）
   * @param clip 动画剪辑（用于动态权重计算）
   * @returns 权重
   */
  public getBoneWeight(
    boneName: string,
    skeleton?: THREE.Skeleton,
    time?: number,
    clip?: THREE.AnimationClip
  ): number {
    // 根据遮罩类型和权重类型计算权重
    switch (this.weightType) {
      case MaskWeightType.BINARY:
        return this.getBinaryWeight(boneName);

      case MaskWeightType.SMOOTH:
        return this.getSmoothWeight(boneName);

      case MaskWeightType.DISTANCE:
        return this.getDistanceWeight(boneName, skeleton);

      case MaskWeightType.GRADIENT:
        return this.getGradientWeight(boneName, skeleton);

      case MaskWeightType.DYNAMIC:
        return this.getDynamicWeight(boneName, time, clip);

      default:
        return this.getBinaryWeight(boneName);
    }
  }

  /**
   * 获取二进制权重
   * @param boneName 骨骼名称
   * @returns 权重
   */
  private getBinaryWeight(boneName: string): number {
    switch (this.type) {
      case MaskType.INCLUDE:
        return this.bones.has(boneName) ? 1.0 : 0.0;

      case MaskType.EXCLUDE:
        return this.bones.has(boneName) ? 0.0 : 1.0;

      case MaskType.HIERARCHY:
        return this.isInHierarchy(boneName) ? 1.0 : 0.0;

      case MaskType.INVERSE_HIERARCHY:
        return this.isInHierarchy(boneName) ? 0.0 : 1.0;

      case MaskType.GROUP:
        return this.isInBoneGroup(boneName) ? 1.0 : 0.0;

      case MaskType.BLEND:
        return this.getBlendedWeight(boneName);

      case MaskType.TRANSITION:
        return this.getTransitionWeight(boneName);

      default:
        return this.bones.has(boneName) ? 1.0 : 0.0;
    }
  }

  /**
   * 检查骨骼是否在骨骼组中
   * @param boneName 骨骼名称
   * @returns 是否在骨骼组中
   */
  private isInBoneGroup(boneName: string): boolean {
    // 遍历所有骨骼组
    for (const groupType of this.bones) {
      // 获取骨骼组
      const group = AnimationMask.getBoneGroup(groupType as BoneGroupType);

      // 如果骨骼在组中，返回true
      if (group && group.includes(boneName)) {
        return true;
      }
    }

    return false;
  }

  /** 骨骼组映射 */
  private static boneGroups: Map<BoneGroupType, string[]> = new Map();

  /**
   * 初始化骨骼组
   */
  private static initBoneGroups(): void {
    // 初始化预设骨骼组
    AnimationMask.boneGroups.set(BoneGroupType.UPPER_BODY, [
      'spine', 'spine1', 'spine2', 'spine3', 'spine4',
      'neck', 'head',
      'leftShoulder', 'leftArm', 'leftForeArm', 'leftHand',
      'rightShoulder', 'rightArm', 'rightForeArm', 'rightHand'
    ]);

    AnimationMask.boneGroups.set(BoneGroupType.LOWER_BODY, [
      'hips',
      'leftUpLeg', 'leftLeg', 'leftFoot', 'leftToeBase',
      'rightUpLeg', 'rightLeg', 'rightFoot', 'rightToeBase'
    ]);

    AnimationMask.boneGroups.set(BoneGroupType.LEFT_ARM, [
      'leftShoulder', 'leftArm', 'leftForeArm', 'leftHand'
    ]);

    AnimationMask.boneGroups.set(BoneGroupType.RIGHT_ARM, [
      'rightShoulder', 'rightArm', 'rightForeArm', 'rightHand'
    ]);
  }

  /**
   * 获取骨骼组
   * @param groupType 骨骼组类型
   * @returns 骨骼组
   */
  public static getBoneGroup(groupType: BoneGroupType): string[] {
    // 如果骨骼组映射为空，初始化
    if (AnimationMask.boneGroups.size === 0) {
      AnimationMask.initBoneGroups();
    }

    // 获取骨骼组
    const group = AnimationMask.boneGroups.get(groupType);

    return group || [];
  }

  /**
   * 获取混合权重
   * @param boneName 骨骼名称
   * @returns 混合权重
   */
  private getBlendedWeight(boneName: string): number {
    // 如果没有混合参数，返回0
    if (!this.blendParams) {
      return 0;
    }

    const { masks, weights, normalize } = this.blendParams;

    // 如果没有遮罩，返回0
    if (masks.length === 0) {
      return 0;
    }

    // 计算总权重
    let totalWeight = 0;
    if (normalize) {
      for (const weight of weights) {
        totalWeight += weight;
      }
    } else {
      totalWeight = 1;
    }

    // 如果总权重为0，返回0
    if (totalWeight === 0) {
      return 0;
    }

    // 计算混合权重
    let blendedWeight = 0;

    for (let i = 0; i < masks.length; i++) {
      const mask = masks[i];
      const weight = weights[i];

      // 获取遮罩权重
      const maskWeight = mask.getBoneWeight(boneName);

      // 累加权重
      blendedWeight += maskWeight * (weight / totalWeight);
    }

    return blendedWeight;
  }

  /**
   * 获取过渡权重
   * @param boneName 骨骼名称
   * @returns 过渡权重
   */
  private getTransitionWeight(boneName: string): number {
    // 如果没有过渡参数，返回0
    if (!this.transitionParams) {
      return 0;
    }

    const { fromMask, toMask, progress } = this.transitionParams;

    // 获取源遮罩权重
    const fromWeight = fromMask.getBoneWeight(boneName);

    // 获取目标遮罩权重
    const toWeight = toMask.getBoneWeight(boneName);

    // 计算过渡权重
    return fromWeight * (1 - progress) + toWeight * progress;
  }

  /**
   * 获取平滑权重
   * @param boneName 骨骼名称
   * @returns 权重
   */
  private getSmoothWeight(boneName: string): number {
    // 获取权重
    const weight = this.boneWeights.get(boneName);

    // 如果没有设置权重，使用二进制权重
    if (weight === undefined) {
      return this.getBinaryWeight(boneName);
    }

    return weight;
  }

  /**
   * 获取距离权重
   * @param boneName 骨骼名称
   * @param skeleton 骨骼对象
   * @returns 权重
   */
  private getDistanceWeight(boneName: string, skeleton?: THREE.Skeleton): number {
    // 如果没有骨骼对象或距离权重配置，使用二进制权重
    if (!skeleton || !this.distanceWeightConfig || !this.rootBone) {
      return this.getBinaryWeight(boneName);
    }

    // 获取根骨骼和目标骨骼
    const rootBone = this.findBone(skeleton, this.rootBone);
    const targetBone = this.findBone(skeleton, boneName);

    // 如果找不到骨骼，使用二进制权重
    if (!rootBone || !targetBone) {
      return this.getBinaryWeight(boneName);
    }

    // 计算距离
    const distance = rootBone.position.distanceTo(targetBone.position);

    // 如果距离为0，返回最大权重
    if (distance === 0) {
      return 1.0;
    }

    // 如果距离大于最大距离，返回最小权重
    if (distance >= this.distanceWeightConfig.maxDistance) {
      return this.distanceWeightConfig.minWeight;
    }

    // 计算权重
    const t = distance / this.distanceWeightConfig.maxDistance;
    const weight = this.calculateFalloff(t);

    return weight;
  }

  /**
   * 计算衰减
   * @param t 归一化距离（0-1）
   * @returns 权重
   */
  private calculateFalloff(t: number): number {
    if (!this.distanceWeightConfig) {
      return 1.0;
    }

    const { falloffFunction, customFalloff, minWeight } = this.distanceWeightConfig;

    // 使用自定义衰减函数
    if (falloffFunction === 'custom' && customFalloff) {
      return customFalloff(t, 1.0);
    }

    // 使用预设衰减函数
    switch (falloffFunction) {
      case 'linear':
        return 1.0 - t * (1.0 - minWeight);

      case 'quadratic':
        return 1.0 - t * t * (1.0 - minWeight);

      case 'exponential':
        return 1.0 - Math.pow(t, 4) * (1.0 - minWeight);

      default:
        return 1.0 - t * (1.0 - minWeight);
    }
  }

  /**
   * 获取渐变权重
   * @param boneName 骨骼名称
   * @param skeleton 骨骼对象
   * @returns 权重
   */
  private getGradientWeight(boneName: string, skeleton?: THREE.Skeleton): number {
    // 如果没有骨骼对象或渐变权重配置，使用二进制权重
    if (!skeleton || !this.gradientWeightConfig || !this.rootBone) {
      return this.getBinaryWeight(boneName);
    }

    // 获取骨骼层级
    const level = this.getBoneLevel(boneName, skeleton);

    // 如果找不到层级，使用二进制权重
    if (level === -1) {
      return this.getBinaryWeight(boneName);
    }

    // 获取最大层级
    const maxLevel = this.getMaxBoneLevel(skeleton);

    // 如果最大层级为0，返回开始权重
    if (maxLevel === 0) {
      return this.gradientWeightConfig.startWeight;
    }

    // 计算权重
    const t = level / maxLevel;
    const weight = this.calculateGradient(t);

    return weight;
  }

  /**
   * 计算渐变
   * @param t 归一化层级（0-1）
   * @returns 权重
   */
  private calculateGradient(t: number): number {
    if (!this.gradientWeightConfig) {
      return 1.0;
    }

    const { startWeight, endWeight, gradientFunction, customGradient } = this.gradientWeightConfig;

    // 使用自定义渐变函数
    if (gradientFunction === 'custom' && customGradient) {
      return customGradient(t, 1.0);
    }

    // 使用预设渐变函数
    switch (gradientFunction) {
      case 'linear':
        return startWeight + t * (endWeight - startWeight);

      case 'smooth':
        const smoothT = t * t * (3 - 2 * t);
        return startWeight + smoothT * (endWeight - startWeight);

      default:
        return startWeight + t * (endWeight - startWeight);
    }
  }

  /**
   * 获取动态权重
   * @param boneName 骨骼名称
   * @param time 当前时间
   * @param clip 动画剪辑
   * @returns 权重
   */
  private getDynamicWeight(boneName: string, time?: number, clip?: THREE.AnimationClip): number {
    // 如果没有时间、动画剪辑或动态权重配置，使用二进制权重
    if (time === undefined || !clip || !this.dynamicWeightConfig) {
      return this.getBinaryWeight(boneName);
    }

    // 检查是否需要更新
    const currentTime = Date.now() / 1000;
    if (currentTime - this.dynamicWeightConfig.lastUpdateTime < this.dynamicWeightConfig.updateFrequency) {
      // 使用缓存的权重
      const cachedWeight = this.boneWeights.get(boneName);
      if (cachedWeight !== undefined) {
        return cachedWeight;
      }
    }

    // 更新时间
    if (this.dynamicWeightConfig) {
      this.dynamicWeightConfig.lastUpdateTime = currentTime;
    }

    // 计算权重
    const weight = this.dynamicWeightConfig.updateFunction(boneName, time, clip);

    // 缓存权重
    this.boneWeights.set(boneName, weight);

    return weight;
  }

  /**
   * 清空骨骼列表
   */
  public clearBones(): void {
    this.bones.clear();
    this.boneWeights.clear();

    // 触发遮罩改变事件
    this.eventEmitter.emit(AnimationMaskEventType.MASK_CHANGED, { cleared: true });

    if (this.debug) {
      console.log('清空遮罩骨骼列表');
    }
  }

  /**
   * 获取骨骼列表
   * @returns 骨骼列表
   */
  public getBones(): string[] {
    return Array.from(this.bones);
  }

  /**
   * 获取骨骼权重映射
   * @returns 骨骼权重映射
   */
  public getBoneWeights(): Map<string, number> {
    return new Map(this.boneWeights);
  }

  /**
   * 应用遮罩到动画剪辑
   * @param clip 动画剪辑
   * @param skeleton 骨骼对象（用于层级和距离计算）
   * @param time 当前时间（用于动态权重计算）
   * @returns 遮罩后的动画剪辑
   */
  public applyToClip(
    clip: THREE.AnimationClip,
    skeleton?: THREE.Skeleton,
    time?: number
  ): THREE.AnimationClip {
    // 创建新的动画剪辑
    const maskedClip = THREE.AnimationClip.parse(THREE.AnimationClip.toJSON(clip));
    maskedClip.name = `${clip.name}_masked`;

    // 如果是层级类型且有骨骼对象，更新骨骼层级
    if ((this.type === MaskType.HIERARCHY || this.type === MaskType.INVERSE_HIERARCHY) && skeleton) {
      this.updateBoneHierarchy(skeleton);
    }

    // 遍历轨道
    for (let i = 0; i < maskedClip.tracks.length; i++) {
      const track = maskedClip.tracks[i];

      // 获取骨骼名称
      const boneName = track.name.split('.')[0];

      // 获取骨骼权重
      const weight = this.getBoneWeight(boneName, skeleton, time, clip);

      // 如果权重为0，移除轨道
      if (weight === 0) {
        maskedClip.tracks.splice(i, 1);
        i--;
      }
      // 如果权重不为1，缩放轨道值
      else if (weight !== 1) {
        // 对于位置和缩放轨道，缩放值
        if (track.name.endsWith('.position') || track.name.endsWith('.scale')) {
          const values = (track as THREE.VectorKeyframeTrack).values;
          for (let j = 0; j < values.length; j++) {
            values[j] *= weight;
          }
        }
        // 对于四元数轨道，使用球面线性插值
        else if (track.name.endsWith('.quaternion')) {
          const values = (track as THREE.QuaternionKeyframeTrack).values;
          const identity = new THREE.Quaternion();

          for (let j = 0; j < values.length; j += 4) {
            const q = new THREE.Quaternion(values[j], values[j + 1], values[j + 2], values[j + 3]);
            q.slerp(identity, 1 - weight);

            values[j] = q.x;
            values[j + 1] = q.y;
            values[j + 2] = q.z;
            values[j + 3] = q.w;
          }
        }
      }
    }

    // 触发遮罩应用事件
    this.eventEmitter.emit(AnimationMaskEventType.MASK_APPLIED, {
      mask: this,
      clip: maskedClip
    });

    return maskedClip;
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public addEventListener(event: AnimationMaskEventType, listener: (data: any) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public removeEventListener(event: AnimationMaskEventType, listener: (data: any) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 创建上半身遮罩
   * @param config 配置
   * @returns 上半身遮罩
   */
  public static createUpperBodyMask(config: Partial<AnimationMaskConfig> = {}): AnimationMask {
    const upperBodyBones = [
      'spine', 'spine1', 'spine2', 'spine3', 'spine4',
      'neck', 'head',
      'leftShoulder', 'leftArm', 'leftForeArm', 'leftHand',
      'rightShoulder', 'rightArm', 'rightForeArm', 'rightHand',
      'leftThumbProximal', 'leftThumbIntermediate', 'leftThumbDistal',
      'leftIndexProximal', 'leftIndexIntermediate', 'leftIndexDistal',
      'leftMiddleProximal', 'leftMiddleIntermediate', 'leftMiddleDistal',
      'leftRingProximal', 'leftRingIntermediate', 'leftRingDistal',
      'leftLittleProximal', 'leftLittleIntermediate', 'leftLittleDistal',
      'rightThumbProximal', 'rightThumbIntermediate', 'rightThumbDistal',
      'rightIndexProximal', 'rightIndexIntermediate', 'rightIndexDistal',
      'rightMiddleProximal', 'rightMiddleIntermediate', 'rightMiddleDistal',
      'rightRingProximal', 'rightRingIntermediate', 'rightRingDistal',
      'rightLittleProximal', 'rightLittleIntermediate', 'rightLittleDistal'
    ];

    return new AnimationMask({
      name: 'upperBody',
      type: MaskType.INCLUDE,
      weightType: config.weightType || MaskWeightType.BINARY,
      bones: upperBodyBones,
      debug: config.debug
    });
  }

  /**
   * 创建下半身遮罩
   * @param config 配置
   * @returns 下半身遮罩
   */
  public static createLowerBodyMask(config: Partial<AnimationMaskConfig> = {}): AnimationMask {
    const lowerBodyBones = [
      'hips',
      'leftUpLeg', 'leftLeg', 'leftFoot', 'leftToeBase',
      'rightUpLeg', 'rightLeg', 'rightFoot', 'rightToeBase'
    ];

    return new AnimationMask({
      name: 'lowerBody',
      type: MaskType.INCLUDE,
      weightType: config.weightType || MaskWeightType.BINARY,
      bones: lowerBodyBones,
      debug: config.debug
    });
  }

  /**
   * 创建左手遮罩
   * @param config 配置
   * @returns 左手遮罩
   */
  public static createLeftHandMask(config: Partial<AnimationMaskConfig> = {}): AnimationMask {
    const leftHandBones = [
      'leftHand',
      'leftThumbProximal', 'leftThumbIntermediate', 'leftThumbDistal',
      'leftIndexProximal', 'leftIndexIntermediate', 'leftIndexDistal',
      'leftMiddleProximal', 'leftMiddleIntermediate', 'leftMiddleDistal',
      'leftRingProximal', 'leftRingIntermediate', 'leftRingDistal',
      'leftLittleProximal', 'leftLittleIntermediate', 'leftLittleDistal'
    ];

    return new AnimationMask({
      name: 'leftHand',
      type: MaskType.INCLUDE,
      weightType: config.weightType || MaskWeightType.BINARY,
      bones: leftHandBones,
      debug: config.debug
    });
  }

  /**
   * 创建右手遮罩
   * @param config 配置
   * @returns 右手遮罩
   */
  public static createRightHandMask(config: Partial<AnimationMaskConfig> = {}): AnimationMask {
    const rightHandBones = [
      'rightHand',
      'rightThumbProximal', 'rightThumbIntermediate', 'rightThumbDistal',
      'rightIndexProximal', 'rightIndexIntermediate', 'rightIndexDistal',
      'rightMiddleProximal', 'rightMiddleIntermediate', 'rightMiddleDistal',
      'rightRingProximal', 'rightRingIntermediate', 'rightRingDistal',
      'rightLittleProximal', 'rightLittleIntermediate', 'rightLittleDistal'
    ];

    return new AnimationMask({
      name: 'rightHand',
      type: MaskType.INCLUDE,
      weightType: config.weightType || MaskWeightType.BINARY,
      bones: rightHandBones,
      debug: config.debug
    });
  }

  /**
   * 创建骨骼组遮罩
   * @param groupType 骨骼组类型
   * @param config 配置
   * @returns 骨骼组遮罩
   */
  public static createBoneGroupMask(groupType: BoneGroupType, config: Partial<AnimationMaskConfig> = {}): AnimationMask {
    return new AnimationMask({
      name: `group_${groupType}`,
      type: MaskType.GROUP,
      weightType: config.weightType || MaskWeightType.BINARY,
      bones: [groupType],
      debug: config.debug
    });
  }

  /**
   * 创建混合遮罩
   * @param masks 遮罩列表
   * @param weights 权重列表
   * @param normalize 是否归一化权重
   * @param config 配置
   * @returns 混合遮罩
   */
  public static createBlendMask(
    masks: AnimationMask[],
    weights: number[],
    normalize: boolean = true,
    config: Partial<AnimationMaskConfig> = {}
  ): AnimationMask {
    // 创建遮罩
    const mask = new AnimationMask({
      name: 'blendMask',
      type: MaskType.BLEND,
      weightType: config.weightType || MaskWeightType.BINARY,
      debug: config.debug
    });

    // 设置混合参数
    mask.setBlendParams(masks, weights, normalize);

    return mask;
  }

  /**
   * 创建过渡遮罩
   * @param fromMask 源遮罩
   * @param toMask 目标遮罩
   * @param progress 过渡进度（0-1）
   * @param config 配置
   * @returns 过渡遮罩
   */
  public static createTransitionMask(
    fromMask: AnimationMask,
    toMask: AnimationMask,
    progress: number = 0,
    config: Partial<AnimationMaskConfig> = {}
  ): AnimationMask {
    // 创建遮罩
    const mask = new AnimationMask({
      name: 'transitionMask',
      type: MaskType.TRANSITION,
      weightType: config.weightType || MaskWeightType.BINARY,
      debug: config.debug
    });

    // 设置过渡参数
    mask.setTransitionParams(fromMask, toMask, progress);

    return mask;
  }



  /**
   * 更新过渡进度
   * @param progress 过渡进度（0-1）
   */
  public updateTransitionProgress(progress: number): void {
    if (!this.transitionParams) return;

    this.transitionParams.progress = Math.max(0, Math.min(1, progress));

    // 触发遮罩改变事件
    this.eventEmitter.emit(AnimationMaskEventType.MASK_CHANGED, {
      type: MaskType.TRANSITION,
      progress
    });
  }

  /**
   * 检查骨骼是否在层级中
   * @param boneName 骨骼名称
   * @returns 是否在层级中
   */
  private isInHierarchy(boneName: string): boolean {
    // 如果骨骼在列表中，直接返回true
    if (this.bones.has(boneName)) {
      return true;
    }

    // 检查骨骼是否是列表中骨骼的子骨骼
    for (const [parent, children] of this.boneHierarchy.entries()) {
      if (this.bones.has(parent) && children.includes(boneName)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 查找骨骼
   * @param skeleton 骨骼对象
   * @param boneName 骨骼名称
   * @returns 骨骼对象
   */
  private findBone(skeleton: THREE.Skeleton, boneName: string): THREE.Bone | null {
    for (const bone of skeleton.bones) {
      if (bone.name === boneName) {
        return bone;
      }
    }

    return null;
  }

  /**
   * 获取骨骼层级
   * @param boneName 骨骼名称
   * @param skeleton 骨骼对象
   * @returns 骨骼层级（-1表示找不到）
   */
  private getBoneLevel(boneName: string, skeleton: THREE.Skeleton): number {
    // 如果没有根骨骼，返回-1
    if (!this.rootBone) {
      return -1;
    }

    // 获取根骨骼
    const rootBone = this.findBone(skeleton, this.rootBone);
    if (!rootBone) {
      return -1;
    }

    // 获取目标骨骼
    const targetBone = this.findBone(skeleton, boneName);
    if (!targetBone) {
      return -1;
    }

    // 计算层级
    let level = 0;
    let current = targetBone;

    while (current && current !== rootBone && current.parent) {
      level++;
      current = current.parent as THREE.Bone;
    }

    // 如果没有找到根骨骼，返回-1
    if (current !== rootBone) {
      return -1;
    }

    return level;
  }

  /**
   * 获取最大骨骼层级
   * @param skeleton 骨骼对象
   * @returns 最大骨骼层级
   */
  private getMaxBoneLevel(skeleton: THREE.Skeleton): number {
    // 如果没有根骨骼，返回0
    if (!this.rootBone) {
      return 0;
    }

    // 获取根骨骼
    const rootBone = this.findBone(skeleton, this.rootBone);
    if (!rootBone) {
      return 0;
    }

    // 计算最大层级
    let maxLevel = 0;

    for (const bone of skeleton.bones) {
      const level = this.getBoneLevel(bone.name, skeleton);
      if (level > maxLevel) {
        maxLevel = level;
      }
    }

    return maxLevel;
  }

  /**
   * 更新骨骼层级
   * @param skeleton 骨骼对象
   */
  public updateBoneHierarchy(skeleton: THREE.Skeleton): void {
    // 清空骨骼层级
    this.boneHierarchy.clear();

    // 遍历所有骨骼
    for (const bone of skeleton.bones) {
      if (bone.parent && bone.parent instanceof THREE.Bone) {
        const parentName = bone.parent.name;

        // 获取父骨骼的子骨骼列表
        let children = this.boneHierarchy.get(parentName);
        if (!children) {
          children = [];
          this.boneHierarchy.set(parentName, children);
        }

        // 添加子骨骼
        children.push(bone.name);
      }
    }

    // 触发层级更新事件
    this.eventEmitter.emit(AnimationMaskEventType.HIERARCHY_UPDATED, {
      mask: this,
      hierarchy: this.boneHierarchy
    });
  }
}
