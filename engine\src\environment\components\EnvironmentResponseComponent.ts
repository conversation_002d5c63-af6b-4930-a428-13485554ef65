/**
 * 环境响应组件
 *
 * 该组件定义实体如何响应环境变化，包括动画、特效、行为等。
 * 可以附加到角色或其他需要对环境做出响应的实体上。
 */

import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EnvironmentAwarenessData } from './EnvironmentAwarenessComponent';

/**
 * 响应类型枚举
 */
export enum ResponseType {
  /** 动画响应 */
  ANIMATION = 'animation',
  /** 特效响应 */
  EFFECT = 'effect',
  /** 声音响应 */
  SOUND = 'sound',
  /** 行为响应 */
  BEHAVIOR = 'behavior',
  /** 自定义响应 */
  CUSTOM = 'custom',
  /** 物理响应 */
  PHYSICS = 'physics',
  /** 姿势响应 */
  POSE = 'pose',
  /** 面部表情响应 */
  FACIAL = 'facial',
  /** 对话响应 */
  DIALOGUE = 'dialogue',
  /** 移动响应 */
  LOCOMOTION = 'locomotion',
  /** 状态变化响应 */
  STATE_CHANGE = 'state_change',
  /** 属性变化响应 */
  PROPERTY_CHANGE = 'property_change',
  /** 材质变化响应 */
  MATERIAL_CHANGE = 'material_change',
  /** 粒子响应 */
  PARTICLE = 'particle',
  /** 光照响应 */
  LIGHTING = 'lighting'
}

/**
 * 响应优先级枚举
 */
export enum ResponsePriority {
  LOW = 0,
  MEDIUM = 1,
  HIGH = 2,
  CRITICAL = 3
}

/**
 * 环境响应规则接口
 */
export interface EnvironmentResponseRule {
  // 规则ID
  id: string;
  // 规则名称
  name: string;
  // 规则描述
  description?: string;
  // 响应类型
  responseType: ResponseType;
  // 响应优先级
  priority: ResponsePriority;
  // 环境条件
  conditions: EnvironmentCondition[];
  // 响应动作
  actions: EnvironmentAction[];
  // 冷却时间 (毫秒)
  cooldown?: number;
  // 上次触发时间
  lastTriggeredTime?: number;
  // 是否启用
  enabled: boolean;
}

/**
 * 环境条件接口
 */
export interface EnvironmentCondition {
  // 条件类型
  type: string;
  // 条件参数
  params: any;
  // 条件评估函数
  evaluate: (data: EnvironmentAwarenessData) => boolean;
}

/**
 * 环境动作接口
 */
export interface EnvironmentAction {
  // 动作类型
  type: string;
  // 动作参数
  params: any;
  // 动作执行函数
  execute: (entity: Entity) => void;
  // 动作停止函数
  stop?: (entity: Entity) => void;
}

/**
 * 环境响应组件配置接口
 */
export interface EnvironmentResponseComponentConfig {
  // 是否自动响应
  autoRespond?: boolean;
  // 是否启用调试模式
  debug?: boolean;
  // 响应规则
  rules?: EnvironmentResponseRule[];
}

/**
 * 环境响应组件
 */
export class EnvironmentResponseComponent extends Component {
  // 配置
  public config: EnvironmentResponseComponentConfig;
  // 响应规则
  public rules: EnvironmentResponseRule[] = [];
  // 当前活动的响应
  private activeResponses: Map<string, EnvironmentAction[]> = new Map();
  // 响应变化回调函数
  private onResponseChangeCallbacks: Array<(activeResponses: Map<string, EnvironmentAction[]>) => void> = [];

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: EnvironmentResponseComponentConfig = {}) {
    super('EnvironmentResponseComponent');

    // 设置默认配置
    this.config = {
      autoRespond: true,
      debug: false,
      rules: [],
      ...config
    };

    // 初始化规则
    this.rules = this.config.rules || [];
  }

  /**
   * 添加响应规则
   * @param rule 响应规则
   */
  public addRule(rule: EnvironmentResponseRule): void {
    // 检查是否已存在相同ID的规则
    const existingRuleIndex = this.rules.findIndex(r => r.id === rule.id);
    if (existingRuleIndex !== -1) {
      this.rules[existingRuleIndex] = rule;
    } else {
      this.rules.push(rule);
    }

    if (this.config.debug) {
      console.log(`添加响应规则: ${rule.name} (${rule.id})`);
    }
  }

  /**
   * 移除响应规则
   * @param ruleId 规则ID
   */
  public removeRule(ruleId: string): void {
    const index = this.rules.findIndex(rule => rule.id === ruleId);
    if (index !== -1) {
      // 停止活动的响应
      this.stopResponse(ruleId);
      // 移除规则
      this.rules.splice(index, 1);

      if (this.config.debug) {
        console.log(`移除响应规则: ${ruleId}`);
      }
    }
  }

  /**
   * 启用响应规则
   * @param ruleId 规则ID
   */
  public enableRule(ruleId: string): void {
    const rule = this.rules.find(r => r.id === ruleId);
    if (rule) {
      rule.enabled = true;
    }
  }

  /**
   * 禁用响应规则
   * @param ruleId 规则ID
   */
  public disableRule(ruleId: string): void {
    const rule = this.rules.find(r => r.id === ruleId);
    if (rule) {
      rule.enabled = false;
      // 停止活动的响应
      this.stopResponse(ruleId);
    }
  }

  /**
   * 评估环境并触发响应
   * @param environmentData 环境数据
   */
  public evaluateAndRespond(environmentData: EnvironmentAwarenessData): void {
    if (!this.config.autoRespond) {
      return;
    }

    const now = Date.now();
    const triggeredRules: EnvironmentResponseRule[] = [];

    // 评估所有规则
    for (const rule of this.rules) {
      if (!rule.enabled) {
        continue;
      }

      // 检查冷却时间
      if (rule.cooldown && rule.lastTriggeredTime && (now - rule.lastTriggeredTime) < rule.cooldown) {
        continue;
      }

      // 评估所有条件
      const conditionsMet = rule.conditions.every(condition => condition.evaluate(environmentData));

      if (conditionsMet) {
        triggeredRules.push(rule);
        rule.lastTriggeredTime = now;
      } else {
        // 如果条件不满足但响应是活动的，停止响应
        this.stopResponse(rule.id);
      }
    }

    // 按优先级排序
    triggeredRules.sort((a, b) => b.priority - a.priority);

    // 触发响应
    for (const rule of triggeredRules) {
      this.triggerResponse(rule);
    }

    // 通知响应变化
    this.notifyResponseChange();
  }

  /**
   * 触发响应
   * @param rule 响应规则
   */
  private triggerResponse(rule: EnvironmentResponseRule): void {
    // 如果响应已经活动，不重复触发
    if (this.activeResponses.has(rule.id)) {
      return;
    }

    const actions = rule.actions;
    this.activeResponses.set(rule.id, actions);

    // 执行所有动作
    for (const action of actions) {
      action.execute(this.entity);
    }

    if (this.config.debug) {
      console.log(`触发响应: ${rule.name} (${rule.id})`);
    }
  }

  /**
   * 停止响应
   * @param ruleId 规则ID
   */
  private stopResponse(ruleId: string): void {
    const actions = this.activeResponses.get(ruleId);
    if (!actions) {
      return;
    }

    // 停止所有动作
    for (const action of actions) {
      if (action.stop) {
        action.stop(this.entity);
      }
    }

    this.activeResponses.delete(ruleId);

    if (this.config.debug) {
      console.log(`停止响应: ${ruleId}`);
    }
  }

  /**
   * 注册响应变化回调
   * @param callback 回调函数
   */
  public onResponseChange(callback: (activeResponses: Map<string, EnvironmentAction[]>) => void): void {
    this.onResponseChangeCallbacks.push(callback);
  }

  /**
   * 移除响应变化回调
   * @param callback 回调函数
   */
  public removeResponseChangeCallback(callback: (activeResponses: Map<string, EnvironmentAction[]>) => void): void {
    const index = this.onResponseChangeCallbacks.indexOf(callback);
    if (index !== -1) {
      this.onResponseChangeCallbacks.splice(index, 1);
    }
  }

  /**
   * 通知响应变化
   */
  private notifyResponseChange(): void {
    for (const callback of this.onResponseChangeCallbacks) {
      callback(this.activeResponses);
    }
  }

  /**
   * 获取当前活动的响应
   * @returns 活动的响应
   */
  public getActiveResponses(): Map<string, EnvironmentAction[]> {
    return new Map(this.activeResponses);
  }
}
