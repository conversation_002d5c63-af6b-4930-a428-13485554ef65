/**
 * 所有后处理效果示例
 * 展示所有可用的后处理效果
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { World } from '../../src/core/World';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Renderer } from '../../src/rendering/Renderer';
import { Camera } from '../../src/rendering/Camera';
import { Light, LightType } from '../../src/rendering/Light';
import { Transform } from '../../src/scene/Transform';
import { RenderSystem } from '../../src/rendering/RenderSystem';
import {
  PostProcessingSystem,
  FXAAEffect,
  BloomEffect,
  SSAOEffect,
  SSREffect,
  SSGIEffect,
  MotionBlurEffect,
  ToneMappingEffect,
  ToneMappingType,
  ColorCorrectionEffect,
  ChromaticAberrationEffect,
  NoiseEffect,
  VignetteEffect,
  GlitchEffect,
  PixelationEffect,
  ScanlineEffect
} from '../../src/rendering/postprocessing';

/**
 * 所有后处理效果示例
 */
export class AllEffectsExample {
  /** 引擎 */
  private engine: Engine;

  /** 世界 */
  private world: World;

  /** 场景 */
  private scene: Scene;

  /** 渲染系统 */
  private renderSystem: RenderSystem;

  /** 后处理系统 */
  private postProcessingSystem: PostProcessingSystem;

  /** 相机实体 */
  private cameraEntity: Entity;

  /** 效果列表 */
  private effects: any[] = [];

  /** 当前效果索引 */
  private currentEffectIndex: number = 0;

  /** 效果切换计时器 */
  private effectSwitchTimer: number = 0;

  /** 效果切换间隔（秒） */
  private effectSwitchInterval: number = 3;

  /** 效果名称列表 */
  private effectNames: string[] = [
    'FXAA抗锯齿',
    '泛光效果',
    '环境光遮蔽',
    '屏幕空间反射',
    '屏幕空间全局光照',
    '运动模糊',
    '色调映射',
    '颜色校正',
    '色差效果',
    '噪点效果',
    '暗角效果',
    '故障效果',
    '像素化效果',
    '扫描线效果',
    '所有效果'
  ];

  /** 旋转速度 */
  private rotationSpeed: number = 0.5;

  /** 是否自动切换效果 */
  private autoSwitchEffects: boolean = true;

  /** 信息元素 */
  private infoElement: HTMLDivElement;

  /**
   * 创建示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();

    // 创建世界
    this.world = new World();
    this.engine.setWorld(this.world);

    // 创建渲染器
    const renderer = new Renderer({
      width: window.innerWidth,
      height: window.innerHeight,
      antialias: true,
      shadows: true,
      shadowMapType: THREE.PCFSoftShadowMap
    });

    // 创建渲染系统
    this.renderSystem = new RenderSystem(renderer, {
      enableShadows: true,
      enablePostProcessing: true
    });

    // 添加渲染系统
    this.world.addSystem(this.renderSystem);

    // 创建场景
    this.scene = new Scene();
    this.world.addEntity(this.scene);

    // 创建相机
    this.cameraEntity = new Entity('Camera');
    const camera = new Camera({
      type: 'perspective',
      fov: 60,
      near: 0.1,
      far: 1000,
      position: new THREE.Vector3(0, 5, 10),
      lookAt: new THREE.Vector3(0, 0, 0)
    });
    this.cameraEntity.addComponent(camera);
    this.world.addEntity(this.cameraEntity);

    // 设置渲染系统的相机
    this.renderSystem.setCamera(camera);

    // 创建后处理系统
    this.postProcessingSystem = new PostProcessingSystem({
      renderer: renderer.getThreeRenderer(),
      scene: this.scene.getThreeScene(),
      camera: camera.getThreeCamera(),
      width: window.innerWidth,
      height: window.innerHeight
    });

    // 添加后处理系统
    this.world.addSystem(this.postProcessingSystem);

    // 创建场景内容
    this.createScene();

    // 创建后处理效果
    this.createPostProcessingEffects();

    // 创建信息元素
    this.createInfoElement();

    // 添加事件监听器
    this.addEventListeners();

    // 开始渲染循环
    this.engine.start();
  }

  /**
   * 创建场景内容
   */
  private createScene(): void {
    // 创建环境光
    const ambientLight = new Entity('AmbientLight');
    ambientLight.addComponent(new Light({
      type: LightType.Ambient,
      color: 0x404040,
      intensity: 0.5
    }));
    this.world.addEntity(ambientLight);

    // 创建平行光
    const directionalLight = new Entity('DirectionalLight');
    directionalLight.addComponent(new Light({
      type: LightType.Directional,
      color: 0xffffff,
      intensity: 1.0,
      position: new THREE.Vector3(5, 10, 5),
      castShadow: true,
      shadowMapSize: new THREE.Vector2(2048, 2048)
    }));
    this.world.addEntity(directionalLight);

    // 创建地面
    const ground = new Entity('Ground');
    ground.addComponent(new Transform({
      position: new THREE.Vector3(0, -2, 0),
      scale: new THREE.Vector3(20, 1, 20)
    }));

    // 添加地面网格
    const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
    const groundMaterial = new THREE.MeshStandardMaterial({
      color: 0x808080,
      roughness: 0.7,
      metalness: 0.0
    });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.receiveShadow = true;
    ground.addComponent({
      type: 'Mesh',
      value: groundMesh
    });
    this.world.addEntity(ground);

    // 创建多个几何体
    const geometries = [
      new THREE.BoxGeometry(1, 1, 1),
      new THREE.SphereGeometry(0.7, 32, 32),
      new THREE.ConeGeometry(0.7, 1.5, 32),
      new THREE.TorusGeometry(0.7, 0.3, 16, 32),
      new THREE.TetrahedronGeometry(0.8)
    ];

    const materials = [
      new THREE.MeshStandardMaterial({ color: 0xff0000, roughness: 0.3, metalness: 0.8 }),
      new THREE.MeshStandardMaterial({ color: 0x00ff00, roughness: 0.5, metalness: 0.2 }),
      new THREE.MeshStandardMaterial({ color: 0x0000ff, roughness: 0.7, metalness: 0.0 }),
      new THREE.MeshStandardMaterial({ color: 0xffff00, roughness: 0.2, metalness: 0.9 }),
      new THREE.MeshStandardMaterial({ color: 0xff00ff, roughness: 0.4, metalness: 0.5 })
    ];

    // 创建几何体实体
    for (let i = 0; i < 5; i++) {
      const angle = (i / 5) * Math.PI * 2;
      const radius = 3;
      const x = Math.cos(angle) * radius;
      const z = Math.sin(angle) * radius;

      const entity = new Entity(`Object_${i}`);
      entity.addComponent(new Transform({
        position: new THREE.Vector3(x, 0, z),
        rotation: new THREE.Euler(0, angle, 0)
      }));

      const mesh = new THREE.Mesh(geometries[i], materials[i]);
      mesh.castShadow = true;
      mesh.receiveShadow = true;

      entity.addComponent({
        type: 'Mesh',
        value: mesh
      });

      this.world.addEntity(entity);
    }
  }

  /**
   * 创建后处理效果
   */
  private createPostProcessingEffects(): void {
    // 创建FXAA抗锯齿效果
    const fxaaEffect = new FXAAEffect({
      name: 'FXAA',
      enabled: false,
      edgeThreshold: 0.1,
      edgeThresholdMin: 0.05
    });
    this.postProcessingSystem.addEffect(fxaaEffect);
    this.effects.push(fxaaEffect);

    // 创建泛光效果
    const bloomEffect = new BloomEffect({
      name: 'Bloom',
      enabled: false,
      strength: 1.5,
      radius: 0.5,
      threshold: 0.6
    });
    this.postProcessingSystem.addEffect(bloomEffect);
    this.effects.push(bloomEffect);

    // 创建环境光遮蔽效果
    const ssaoEffect = new SSAOEffect({
      name: 'SSAO',
      enabled: false,
      kernelRadius: 8,
      minDistance: 0.005,
      maxDistance: 0.1
    });
    ssaoEffect.setSceneAndCamera(this.scene.getThreeScene(), this.cameraEntity.getComponent('Camera')!.getThreeCamera());
    this.postProcessingSystem.addEffect(ssaoEffect);
    this.effects.push(ssaoEffect);

    // 创建屏幕空间反射效果
    const ssrEffect = new SSREffect({
      name: 'SSR',
      enabled: false,
      intensity: 1.0,
      maxSteps: 20,
      stride: 1,
      refineSteps: 5,
      importanceSampling: true,
      denoiseIterations: 1
    });
    ssrEffect.setSceneAndCamera(this.scene.getThreeScene(), this.cameraEntity.getComponent('Camera')!.getThreeCamera());
    this.postProcessingSystem.addEffect(ssrEffect);
    this.effects.push(ssrEffect);

    // 创建屏幕空间全局光照效果
    const ssgiEffect = new SSGIEffect({
      name: 'SSGI',
      enabled: false,
      intensity: 1.0,
      radius: 5.0,
      bias: 0.05,
      samples: 16,
      denoiseIterations: 1
    });
    ssgiEffect.setSceneAndCamera(this.scene.getThreeScene(), this.cameraEntity.getComponent('Camera')!.getThreeCamera());
    this.postProcessingSystem.addEffect(ssgiEffect);
    this.effects.push(ssgiEffect);

    // 创建运动模糊效果
    const motionBlurEffect = new MotionBlurEffect({
      name: 'MotionBlur',
      enabled: false,
      intensity: 0.5,
      samples: 32
    });
    motionBlurEffect.setSceneAndCamera(this.scene.getThreeScene(), this.cameraEntity.getComponent('Camera')!.getThreeCamera());
    this.postProcessingSystem.addEffect(motionBlurEffect);
    this.effects.push(motionBlurEffect);

    // 创建色调映射效果
    const toneMappingEffect = new ToneMappingEffect({
      name: 'ToneMapping',
      enabled: false,
      type: ToneMappingType.ACESFilmic,
      exposure: 1.0,
      gamma: 2.2,
      contrast: 1.0,
      saturation: 1.0,
      brightness: 1.0
    });
    this.postProcessingSystem.addEffect(toneMappingEffect);
    this.effects.push(toneMappingEffect);

    // 创建颜色校正效果
    const colorCorrectionEffect = new ColorCorrectionEffect({
      name: 'ColorCorrection',
      enabled: false,
      brightness: 1.1,
      contrast: 1.1,
      saturation: 1.2,
      gamma: 1.0,
      temperature: 0.1,
      tint: 0.05
    });
    this.postProcessingSystem.addEffect(colorCorrectionEffect);
    this.effects.push(colorCorrectionEffect);

    // 创建色差效果
    const chromaticAberrationEffect = new ChromaticAberrationEffect({
      name: 'ChromaticAberration',
      enabled: false,
      offset: new THREE.Vector2(0.005, 0.005),
      radialMode: true,
      intensity: 1.0
    });
    this.postProcessingSystem.addEffect(chromaticAberrationEffect);
    this.effects.push(chromaticAberrationEffect);

    // 创建噪点效果
    const noiseEffect = new NoiseEffect({
      name: 'Noise',
      enabled: false,
      intensity: 0.2,
      colored: true,
      animated: true
    });
    this.postProcessingSystem.addEffect(noiseEffect);
    this.effects.push(noiseEffect);

    // 创建暗角效果
    const vignetteEffect = new VignetteEffect({
      name: 'Vignette',
      enabled: false,
      offset: 1.0,
      darkness: 1.0,
      eskil: true
    });
    this.postProcessingSystem.addEffect(vignetteEffect);
    this.effects.push(vignetteEffect);

    // 创建故障效果
    const glitchEffect = new GlitchEffect({
      name: 'Glitch',
      enabled: false,
      goWild: false,
      amount: 1.0,
      interval: 3.0,
      duration: 0.5
    });
    this.postProcessingSystem.addEffect(glitchEffect);
    this.effects.push(glitchEffect);

    // 创建像素化效果
    const pixelationEffect = new PixelationEffect({
      name: 'Pixelation',
      enabled: false,
      pixelSize: 8,
      preserveAspect: true,
      pixelBorder: true,
      borderWidth: 0.1
    });
    this.postProcessingSystem.addEffect(pixelationEffect);
    this.effects.push(pixelationEffect);

    // 创建扫描线效果
    const scanlineEffect = new ScanlineEffect({
      name: 'Scanline',
      enabled: false,
      count: 1024,
      intensity: 0.3,
      animated: true,
      speed: 1.0,
      noise: true
    });
    this.postProcessingSystem.addEffect(scanlineEffect);
    this.effects.push(scanlineEffect);
  }

  /**
   * 创建信息元素
   */
  private createInfoElement(): void {
    // 创建信息元素
    this.infoElement = document.createElement('div');
    this.infoElement.style.position = 'absolute';
    this.infoElement.style.top = '10px';
    this.infoElement.style.left = '10px';
    this.infoElement.style.color = 'white';
    this.infoElement.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    this.infoElement.style.padding = '10px';
    this.infoElement.style.borderRadius = '5px';
    this.infoElement.style.fontFamily = 'Arial, sans-serif';
    this.infoElement.style.fontSize = '14px';
    this.infoElement.style.zIndex = '1000';

    // 更新信息元素内容
    this.updateInfoElement();

    // 添加到文档
    document.body.appendChild(this.infoElement);
  }

  /**
   * 更新信息元素内容
   */
  private updateInfoElement(): void {
    this.infoElement.innerHTML = `
      <h2>后处理效果示例</h2>
      <p>当前效果: ${this.effectNames[this.currentEffectIndex]}</p>
      <p>按键说明:</p>
      <ul>
        <li>空格键: 切换效果</li>
        <li>A键: ${this.autoSwitchEffects ? '关闭' : '开启'}自动切换效果</li>
        <li>R键: 增加旋转速度</li>
        <li>F键: 减少旋转速度</li>
      </ul>
    `;
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners(): void {
    // 添加键盘事件监听器
    window.addEventListener('keydown', this.onKeyDown.bind(this));

    // 添加窗口大小变化事件监听器
    window.addEventListener('resize', this.onResize.bind(this));

    // 添加更新事件监听器
    this.engine.onUpdate.add(this.onUpdate.bind(this));
  }

  /**
   * 键盘事件处理
   * @param event 键盘事件
   */
  private onKeyDown(event: KeyboardEvent): void {
    switch (event.key) {
      case ' ': // 空格键
        this.switchEffect();
        break;
      case 'a': // A键
      case 'A':
        this.autoSwitchEffects = !this.autoSwitchEffects;
        this.updateInfoElement();
        break;
      case 'r': // R键
      case 'R':
        this.rotationSpeed += 0.1;
        break;
      case 'f': // F键
      case 'F':
        this.rotationSpeed = Math.max(0, this.rotationSpeed - 0.1);
        break;
    }
  }

  /**
   * 窗口大小变化事件处理
   */
  private onResize(): void {
    // 更新渲染器大小
    this.renderSystem.resize(window.innerWidth, window.innerHeight);

    // 更新后处理系统大小
    this.postProcessingSystem.resize(window.innerWidth, window.innerHeight);

    // 更新相机宽高比
    const camera = this.cameraEntity.getComponent('Camera');
    if (camera) {
      camera.setAspect(window.innerWidth / window.innerHeight);
    }
  }

  /**
   * 更新事件处理
   * @param deltaTime 帧间隔时间（秒）
   */
  private onUpdate(deltaTime: number): void {
    // 旋转相机
    const cameraTransform = this.cameraEntity.getComponent('Transform');
    if (cameraTransform) {
      const position = cameraTransform.getPosition();
      const angle = this.rotationSpeed * deltaTime;
      const x = position.x * Math.cos(angle) - position.z * Math.sin(angle);
      const z = position.x * Math.sin(angle) + position.z * Math.cos(angle);
      cameraTransform.setPosition(new THREE.Vector3(x, position.y, z));
      cameraTransform.lookAt(new THREE.Vector3(0, 0, 0));
    }

    // 自动切换效果
    if (this.autoSwitchEffects) {
      this.effectSwitchTimer += deltaTime;
      if (this.effectSwitchTimer >= this.effectSwitchInterval) {
        this.switchEffect();
        this.effectSwitchTimer = 0;
      }
    }
  }

  /**
   * 切换效果
   */
  private switchEffect(): void {
    // 禁用所有效果
    for (const effect of this.effects) {
      effect.setEnabled(false);
    }

    // 切换到下一个效果
    this.currentEffectIndex = (this.currentEffectIndex + 1) % this.effectNames.length;

    // 如果是最后一个效果（所有效果），则启用所有效果
    if (this.currentEffectIndex === this.effectNames.length - 1) {
      for (const effect of this.effects) {
        effect.setEnabled(true);
      }
    } else {
      // 否则只启用当前效果
      this.effects[this.currentEffectIndex].setEnabled(true);
    }

    // 更新信息元素
    this.updateInfoElement();
  }
}
