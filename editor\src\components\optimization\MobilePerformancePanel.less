/**
 * 移动设备性能优化面板样式
 */
.mobile-performance-panel {
  .ant-card-body {
    max-height: 700px;
    overflow-y: auto;
  }

  .device-info {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;
  }

  .monitor-data {
    margin-bottom: 16px;

    .ant-statistic {
      .ant-statistic-title {
        font-size: 12px;
      }

      .ant-statistic-content {
        font-size: 20px;
      }
    }
  }

  .setting-label {
    font-weight: 500;
    margin-bottom: 8px;
  }

  .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  .ant-slider-mark-text {
    font-size: 12px;
  }

  .performance-level-selector {
    margin-bottom: 16px;
  }

  .optimization-settings {
    margin-bottom: 16px;
  }

  .custom-config-panel {
    margin-top: 16px;

    .ant-collapse {
      margin-bottom: 16px;
    }

    .custom-config-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      margin-top: 16px;
    }
  }
}

// 响应式调整
@media (max-width: 576px) {
  .mobile-performance-panel {
    .ant-card-body {
      padding: 12px;
    }

    .ant-row {
      margin-right: -8px !important;
      margin-left: -8px !important;
      
      .ant-col {
        padding-right: 8px !important;
        padding-left: 8px !important;
      }
    }

    .monitor-data {
      .ant-statistic {
        .ant-statistic-content {
          font-size: 16px;
        }
      }
    }
  }
}
