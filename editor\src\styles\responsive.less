/**
 * 响应式设计样式
 * 提供统一的响应式断点和样式
 */

// 响应式断点
@screen-xs: 480px;
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;
@screen-xxl: 1600px;

// 移动设备断点
@mobile-max-width: 768px;
@tablet-max-width: 1024px;

// 触控友好的尺寸
@touch-min-height: 44px;
@touch-min-width: 44px;
@touch-icon-size: 24px;
@touch-padding: 12px;
@touch-margin: 8px;
@touch-border-radius: 8px;

// 响应式间距
@spacing-xs: 4px;
@spacing-sm: 8px;
@spacing-md: 12px;
@spacing-lg: 16px;
@spacing-xl: 24px;

// 响应式字体大小
@font-size-xs: 12px;
@font-size-sm: 14px;
@font-size-md: 16px;
@font-size-lg: 18px;
@font-size-xl: 20px;

// 响应式混合器
.responsive-container() {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: @spacing-md;
  padding-left: @spacing-md;
  
  @media (min-width: @screen-sm) {
    max-width: 540px;
  }
  
  @media (min-width: @screen-md) {
    max-width: 720px;
  }
  
  @media (min-width: @screen-lg) {
    max-width: 960px;
  }
  
  @media (min-width: @screen-xl) {
    max-width: 1140px;
  }
  
  @media (min-width: @screen-xxl) {
    max-width: 1320px;
  }
}

// 触控友好的按钮
.touch-friendly-button() {
  min-height: @touch-min-height;
  min-width: @touch-min-width;
  padding: @touch-padding;
  border-radius: @touch-border-radius;
  
  &:active {
    transform: scale(0.98);
  }
}

// 触控友好的输入框
.touch-friendly-input() {
  min-height: @touch-min-height;
  padding: @touch-padding;
  border-radius: @touch-border-radius;
  font-size: @font-size-md;
}

// 触控友好的选择器
.touch-friendly-select() {
  min-height: @touch-min-height;
  padding: @touch-padding;
  border-radius: @touch-border-radius;
  font-size: @font-size-md;
}

// 触控友好的复选框和单选框
.touch-friendly-checkbox-radio() {
  min-height: @touch-min-height;
  min-width: @touch-min-width;
  
  .ant-checkbox-inner,
  .ant-radio-inner {
    width: 20px;
    height: 20px;
  }
}

// 响应式网格
.responsive-grid(@columns: 12, @gutter: @spacing-md) {
  display: grid;
  grid-template-columns: repeat(@columns, 1fr);
  gap: @gutter;
  
  @media (max-width: @screen-md) {
    grid-template-columns: repeat(6, 1fr);
  }
  
  @media (max-width: @screen-sm) {
    grid-template-columns: repeat(4, 1fr);
  }
  
  @media (max-width: @screen-xs) {
    grid-template-columns: repeat(2, 1fr);
  }
}

// 响应式弹性布局
.responsive-flex(@direction: row, @wrap: wrap, @justify: flex-start, @align: flex-start, @gap: @spacing-md) {
  display: flex;
  flex-direction: @direction;
  flex-wrap: @wrap;
  justify-content: @justify;
  align-items: @align;
  gap: @gap;
  
  @media (max-width: @screen-md) {
    flex-direction: column;
  }
}

// 响应式隐藏
.responsive-hide(@breakpoint) {
  @media (max-width: @breakpoint) {
    display: none !important;
  }
}

// 响应式显示
.responsive-show(@breakpoint) {
  display: none !important;
  
  @media (max-width: @breakpoint) {
    display: block !important;
  }
}

// 响应式文本
.responsive-text() {
  font-size: @font-size-md;
  
  @media (max-width: @screen-md) {
    font-size: @font-size-sm;
  }
  
  @media (max-width: @screen-sm) {
    font-size: @font-size-xs;
  }
}

// 响应式标题
.responsive-heading(@level: 1) {
  @size: @font-size-xl + (6 - @level) * 4px;
  
  font-size: @size;
  
  @media (max-width: @screen-md) {
    font-size: @size - 2px;
  }
  
  @media (max-width: @screen-sm) {
    font-size: @size - 4px;
  }
}

// 响应式边距
.responsive-margin(@direction: all, @size: @spacing-md) {
  @media (min-width: @screen-md) {
    .set-margin(@direction, @size);
  }
  
  @media (max-width: @screen-md) {
    .set-margin(@direction, @size * 0.75);
  }
  
  @media (max-width: @screen-sm) {
    .set-margin(@direction, @size * 0.5);
  }
}

.set-margin(@direction, @size) {
  & when (@direction = all) {
    margin: @size;
  }
  
  & when (@direction = top) {
    margin-top: @size;
  }
  
  & when (@direction = right) {
    margin-right: @size;
  }
  
  & when (@direction = bottom) {
    margin-bottom: @size;
  }
  
  & when (@direction = left) {
    margin-left: @size;
  }
  
  & when (@direction = horizontal) {
    margin-left: @size;
    margin-right: @size;
  }
  
  & when (@direction = vertical) {
    margin-top: @size;
    margin-bottom: @size;
  }
}

// 响应式内边距
.responsive-padding(@direction: all, @size: @spacing-md) {
  @media (min-width: @screen-md) {
    .set-padding(@direction, @size);
  }
  
  @media (max-width: @screen-md) {
    .set-padding(@direction, @size * 0.75);
  }
  
  @media (max-width: @screen-sm) {
    .set-padding(@direction, @size * 0.5);
  }
}

.set-padding(@direction, @size) {
  & when (@direction = all) {
    padding: @size;
  }
  
  & when (@direction = top) {
    padding-top: @size;
  }
  
  & when (@direction = right) {
    padding-right: @size;
  }
  
  & when (@direction = bottom) {
    padding-bottom: @size;
  }
  
  & when (@direction = left) {
    padding-left: @size;
  }
  
  & when (@direction = horizontal) {
    padding-left: @size;
    padding-right: @size;
  }
  
  & when (@direction = vertical) {
    padding-top: @size;
    padding-bottom: @size;
  }
}
