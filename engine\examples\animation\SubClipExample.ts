/**
 * 子片段示例
 * 展示子片段功能的使用方法
 */
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { SubClip } from '../../src/animation/SubClip';
import { AnimationSubClip } from '../../src/animation/AnimationSubClip';
import { SubClipSequence, SubClipSequenceEventType } from '../../src/animation/SubClipSequence';
import { SubClipTransition, TransitionType } from '../../src/animation/SubClipTransition';
import { SubClipModifier, ModifierType } from '../../src/animation/SubClipModifier';
import { SubClipEvent, EventTriggerType } from '../../src/animation/SubClipEvent';
import { GUI } from 'dat.gui';

// 场景类
class SubClipExample {
  // 场景
  private scene: THREE.Scene;
  // 相机
  private camera: THREE.PerspectiveCamera;
  // 渲染器
  private renderer: THREE.WebGLRenderer;
  // 控制器
  private controls: OrbitControls;
  // 时钟
  private clock: THREE.Clock;
  // 模型
  private model: THREE.Object3D | null = null;
  // 混合器
  private mixer: THREE.AnimationMixer | null = null;
  // 动画片段
  private animations: THREE.AnimationClip[] = [];
  // 子片段
  private subClips: Map<string, SubClip> = new Map();
  // 高级子片段
  private advancedSubClips: Map<string, AnimationSubClip> = new Map();
  // 子片段序列
  private sequences: Map<string, SubClipSequence> = new Map();
  // 子片段过渡
  private transitions: Map<string, SubClipTransition> = new Map();
  // 子片段变形器
  private modifiers: Map<string, SubClipModifier> = new Map();
  // 子片段事件
  private events: Map<string, SubClipEvent> = new Map();
  // 当前动作
  private currentAction: THREE.AnimationAction | null = null;
  // GUI
  private gui: GUI;
  // 变形类型选项
  private modifierTypeOptions = {
    TIME_SCALE: ModifierType.TIME_SCALE,
    REVERSE: ModifierType.REVERSE,
    LOOP: ModifierType.LOOP,
    MIRROR: ModifierType.MIRROR,
    JITTER: ModifierType.JITTER,
    DELAY: ModifierType.DELAY,
    SMOOTH: ModifierType.SMOOTH,
    NOISE: ModifierType.NOISE
  };
  // 过渡类型选项
  private transitionTypeOptions = {
    LINEAR: TransitionType.LINEAR,
    EASE_IN: TransitionType.EASE_IN,
    EASE_OUT: TransitionType.EASE_OUT,
    EASE_IN_OUT: TransitionType.EASE_IN_OUT
  };
  // 事件触发类型选项
  private eventTriggerTypeOptions = {
    TIME: EventTriggerType.TIME,
    PERCENT: EventTriggerType.PERCENT,
    FRAME: EventTriggerType.FRAME,
    START: EventTriggerType.START,
    END: EventTriggerType.END,
    LOOP: EventTriggerType.LOOP
  };

  /**
   * 构造函数
   */
  constructor() {
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0xa0a0a0);
    this.scene.fog = new THREE.Fog(0xa0a0a0, 10, 50);

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 100);
    this.camera.position.set(0, 2, 5);
    this.camera.lookAt(0, 1, 0);

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    document.body.appendChild(this.renderer.domElement);

    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 1, 0);
    this.controls.update();

    // 创建时钟
    this.clock = new THREE.Clock();

    // 创建灯光
    this.setupLights();

    // 创建地面
    this.setupGround();

    // 创建GUI
    this.gui = new GUI();
    this.gui.width = 300;

    // 加载模型
    this.loadModel();

    // 添加窗口大小调整监听器
    window.addEventListener('resize', this.onWindowResize.bind(this));

    // 开始动画循环
    this.animate();
  }

  /**
   * 设置灯光
   */
  private setupLights(): void {
    // 半球光
    const hemiLight = new THREE.HemisphereLight(0xffffff, 0x444444, 0.6);
    hemiLight.position.set(0, 20, 0);
    this.scene.add(hemiLight);

    // 方向光
    const dirLight = new THREE.DirectionalLight(0xffffff, 0.8);
    dirLight.position.set(3, 10, 10);
    dirLight.castShadow = true;
    dirLight.shadow.camera.top = 2;
    dirLight.shadow.camera.bottom = -2;
    dirLight.shadow.camera.left = -2;
    dirLight.shadow.camera.right = 2;
    dirLight.shadow.camera.near = 0.1;
    dirLight.shadow.camera.far = 40;
    this.scene.add(dirLight);
  }

  /**
   * 设置地面
   */
  private setupGround(): void {
    // 地面
    const ground = new THREE.Mesh(
      new THREE.PlaneGeometry(100, 100),
      new THREE.MeshPhongMaterial({ color: 0x999999, depthWrite: false })
    );
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    this.scene.add(ground);

    // 网格
    const grid = new THREE.GridHelper(100, 100, 0x000000, 0x000000);
    grid.material.opacity = 0.2;
    grid.material.transparent = true;
    this.scene.add(grid);
  }

  /**
   * 加载模型
   */
  private loadModel(): void {
    const loader = new GLTFLoader();
    loader.load(
      'models/gltf/Xbot.glb',
      (gltf) => {
        this.model = gltf.scene;
        this.model.traverse((object) => {
          if ((object as THREE.Mesh).isMesh) {
            object.castShadow = true;
          }
        });
        this.scene.add(this.model);

        // 获取动画
        this.animations = gltf.animations;

        // 创建混合器
        this.mixer = new THREE.AnimationMixer(this.model);

        // 创建子片段
        this.createSubClips();

        // 设置GUI
        this.setupGUI();
      },
      undefined,
      (error) => {
        console.error('加载模型时出错:', error);
      }
    );
  }

  /**
   * 创建子片段
   */
  private createSubClips(): void {
    if (!this.animations.length) return;

    // 获取动画
    const walkClip = this.animations.find(clip => clip.name === 'walk');
    const runClip = this.animations.find(clip => clip.name === 'run');
    const idleClip = this.animations.find(clip => clip.name === 'idle');
    const jumpClip = this.animations.find(clip => clip.name === 'jump');

    if (walkClip) {
      // 创建行走子片段
      const walkStart = new SubClip({
        name: 'walkStart',
        originalClip: walkClip,
        startTime: 0,
        endTime: walkClip.duration / 3,
        loop: false
      });
      this.subClips.set('walkStart', walkStart);

      const walkLoop = new SubClip({
        name: 'walkLoop',
        originalClip: walkClip,
        startTime: walkClip.duration / 3,
        endTime: walkClip.duration * 2 / 3,
        loop: true
      });
      this.subClips.set('walkLoop', walkLoop);

      const walkEnd = new SubClip({
        name: 'walkEnd',
        originalClip: walkClip,
        startTime: walkClip.duration * 2 / 3,
        endTime: walkClip.duration,
        loop: false
      });
      this.subClips.set('walkEnd', walkEnd);
    }

    if (runClip) {
      // 创建跑步子片段
      const runStart = new SubClip({
        name: 'runStart',
        originalClip: runClip,
        startTime: 0,
        endTime: runClip.duration / 3,
        loop: false
      });
      this.subClips.set('runStart', runStart);

      const runLoop = new SubClip({
        name: 'runLoop',
        originalClip: runClip,
        startTime: runClip.duration / 3,
        endTime: runClip.duration * 2 / 3,
        loop: true
      });
      this.subClips.set('runLoop', runLoop);

      const runEnd = new SubClip({
        name: 'runEnd',
        originalClip: runClip,
        startTime: runClip.duration * 2 / 3,
        endTime: runClip.duration,
        loop: false
      });
      this.subClips.set('runEnd', runEnd);

      // 创建高级子片段
      const runReverse = new AnimationSubClip({
        name: 'runReverse',
        originalClipName: 'run',
        startTime: 0,
        endTime: runClip.duration,
        loop: true,
        reverse: true
      });
      runReverse.setOriginalClip(runClip);
      this.advancedSubClips.set('runReverse', runReverse);

      const runSlow = new AnimationSubClip({
        name: 'runSlow',
        originalClipName: 'run',
        startTime: 0,
        endTime: runClip.duration,
        loop: true,
        timeScale: 0.5
      });
      runSlow.setOriginalClip(runClip);
      this.advancedSubClips.set('runSlow', runSlow);
    }

    // 创建子片段序列
    if (idleClip && walkClip && runClip) {
      const sequence = new SubClipSequence({
        name: 'walkToRun',
        loop: true,
        autoPlay: false
      });

      // 添加子片段到序列
      sequence.addSubClip(this.subClips.get('walkStart')!, 1.0, 0.3);
      sequence.addSubClip(this.subClips.get('walkLoop')!, 2.0, 0.3);
      sequence.addSubClip(this.subClips.get('runStart')!, 1.0, 0.3);
      sequence.addSubClip(this.subClips.get('runLoop')!, 2.0, 0.3);
      sequence.addSubClip(this.subClips.get('runEnd')!, 1.0, 0.3);
      sequence.addSubClip(this.subClips.get('walkEnd')!, 1.0, 0.3);

      this.sequences.set('walkToRun', sequence);
    }

    // 创建子片段过渡
    if (walkClip && runClip) {
      const transition = new SubClipTransition({
        name: 'walkToRun',
        fromClip: this.subClips.get('walkLoop')!,
        toClip: this.subClips.get('runLoop')!,
        duration: 1.0,
        type: TransitionType.EASE_IN_OUT
      });

      this.transitions.set('walkToRun', transition);
    }

    // 创建子片段变形器
    if (runClip) {
      // 时间缩放变形器
      const timeScaleModifier = new SubClipModifier({
        name: 'timeScale',
        type: ModifierType.TIME_SCALE,
        params: { scale: 2.0 },
        enabled: true
      });
      this.modifiers.set('timeScale', timeScaleModifier);

      // 反向播放变形器
      const reverseModifier = new SubClipModifier({
        name: 'reverse',
        type: ModifierType.REVERSE,
        enabled: true
      });
      this.modifiers.set('reverse', reverseModifier);

      // 循环变形器
      const loopModifier = new SubClipModifier({
        name: 'loop',
        type: ModifierType.LOOP,
        params: { count: 3, blendTime: 0.1 },
        enabled: true
      });
      this.modifiers.set('loop', loopModifier);

      // 镜像变形器
      const mirrorModifier = new SubClipModifier({
        name: 'mirror',
        type: ModifierType.MIRROR,
        params: { axis: 'x' },
        enabled: true
      });
      this.modifiers.set('mirror', mirrorModifier);
    }

    // 创建子片段事件
    if (jumpClip) {
      // 跳跃开始事件
      const jumpStartEvent = new SubClipEvent({
        name: 'jumpStart',
        triggerType: EventTriggerType.START,
        callback: (event) => {
          console.log('跳跃开始:', event);
        },
        once: false,
        enabled: true
      });
      this.events.set('jumpStart', jumpStartEvent);

      // 跳跃高点事件
      const jumpPeakEvent = new SubClipEvent({
        name: 'jumpPeak',
        triggerType: EventTriggerType.PERCENT,
        triggerValue: 0.5,
        callback: (event) => {
          console.log('跳跃高点:', event);
        },
        once: true,
        enabled: true
      });
      this.events.set('jumpPeak', jumpPeakEvent);

      // 跳跃结束事件
      const jumpEndEvent = new SubClipEvent({
        name: 'jumpEnd',
        triggerType: EventTriggerType.END,
        callback: (event) => {
          console.log('跳跃结束:', event);
        },
        once: false,
        enabled: true
      });
      this.events.set('jumpEnd', jumpEndEvent);
    }
  }

  /**
   * 设置GUI
   */
  private setupGUI(): void {
    // 清空GUI
    this.gui.destroy();
    this.gui = new GUI();
    this.gui.width = 300;

    // 添加动画列表
    const animationFolder = this.gui.addFolder('动画');
    const animationNames = this.animations.map(clip => clip.name);
    const animationController = {
      animation: animationNames[0],
      play: () => {
        // 停止当前动作
        if (this.currentAction) {
          this.currentAction.stop();
        }

        // 播放选中的动画
        const clip = this.animations.find(clip => clip.name === animationController.animation);
        if (clip && this.mixer) {
          this.currentAction = this.mixer.clipAction(clip);
          this.currentAction.play();
        }
      }
    };
    animationFolder.add(animationController, 'animation', animationNames);
    animationFolder.add(animationController, 'play');
    animationFolder.open();

    // 添加子片段列表
    const subClipFolder = this.gui.addFolder('子片段');
    const subClipNames = Array.from(this.subClips.keys());
    const subClipController = {
      subClip: subClipNames.length > 0 ? subClipNames[0] : '',
      play: () => {
        // 停止当前动作
        if (this.currentAction) {
          this.currentAction.stop();
        }

        // 播放选中的子片段
        const subClip = this.subClips.get(subClipController.subClip);
        if (subClip && this.mixer) {
          const clip = subClip.getClip();
          if (clip) {
            this.currentAction = this.mixer.clipAction(clip);
            this.currentAction.play();
          }
        }
      }
    };
    subClipFolder.add(subClipController, 'subClip', subClipNames);
    subClipFolder.add(subClipController, 'play');
    subClipFolder.open();

    // 添加高级子片段列表
    const advancedSubClipFolder = this.gui.addFolder('高级子片段');
    const advancedSubClipNames = Array.from(this.advancedSubClips.keys());
    const advancedSubClipController = {
      subClip: advancedSubClipNames.length > 0 ? advancedSubClipNames[0] : '',
      play: () => {
        // 停止当前动作
        if (this.currentAction) {
          this.currentAction.stop();
        }

        // 播放选中的高级子片段
        const subClip = this.advancedSubClips.get(advancedSubClipController.subClip);
        if (subClip && this.mixer) {
          const clip = subClip.getSubClip();
          if (clip) {
            this.currentAction = this.mixer.clipAction(clip);
            this.currentAction.play();
          }
        }
      }
    };
    advancedSubClipFolder.add(advancedSubClipController, 'subClip', advancedSubClipNames);
    advancedSubClipFolder.add(advancedSubClipController, 'play');
    advancedSubClipFolder.open();

    // 添加子片段序列列表
    const sequenceFolder = this.gui.addFolder('子片段序列');
    const sequenceNames = Array.from(this.sequences.keys());
    const sequenceController = {
      sequence: sequenceNames.length > 0 ? sequenceNames[0] : '',
      play: () => {
        // 停止当前动作
        if (this.currentAction) {
          this.currentAction.stop();
        }

        // 播放选中的序列
        const sequence = this.sequences.get(sequenceController.sequence);
        if (sequence && this.mixer) {
          sequence.play();
        }
      },
      stop: () => {
        // 停止选中的序列
        const sequence = this.sequences.get(sequenceController.sequence);
        if (sequence) {
          sequence.stop();
        }
      }
    };
    sequenceFolder.add(sequenceController, 'sequence', sequenceNames);
    sequenceFolder.add(sequenceController, 'play');
    sequenceFolder.add(sequenceController, 'stop');
    sequenceFolder.open();

    // 添加子片段过渡列表
    const transitionFolder = this.gui.addFolder('子片段过渡');
    const transitionNames = Array.from(this.transitions.keys());
    const transitionController = {
      transition: transitionNames.length > 0 ? transitionNames[0] : '',
      start: () => {
        // 停止当前动作
        if (this.currentAction) {
          this.currentAction.stop();
        }

        // 开始选中的过渡
        const transition = this.transitions.get(transitionController.transition);
        if (transition && this.mixer) {
          transition.start();
        }
      },
      stop: () => {
        // 停止选中的过渡
        const transition = this.transitions.get(transitionController.transition);
        if (transition) {
          transition.stop();
        }
      }
    };
    transitionFolder.add(transitionController, 'transition', transitionNames);
    transitionFolder.add(transitionController, 'start');
    transitionFolder.add(transitionController, 'stop');
    transitionFolder.open();

    // 添加子片段变形器列表
    const modifierFolder = this.gui.addFolder('子片段变形器');
    const modifierNames = Array.from(this.modifiers.keys());
    const modifierController = {
      modifier: modifierNames.length > 0 ? modifierNames[0] : '',
      clip: animationNames[0],
      apply: () => {
        // 停止当前动作
        if (this.currentAction) {
          this.currentAction.stop();
        }

        // 应用选中的变形器
        const modifier = this.modifiers.get(modifierController.modifier);
        const clip = this.animations.find(clip => clip.name === modifierController.clip);
        if (modifier && clip && this.mixer) {
          const modifiedClip = modifier.apply(clip);
          this.currentAction = this.mixer.clipAction(modifiedClip);
          this.currentAction.play();
        }
      }
    };
    modifierFolder.add(modifierController, 'modifier', modifierNames);
    modifierFolder.add(modifierController, 'clip', animationNames);
    modifierFolder.add(modifierController, 'apply');
    modifierFolder.open();

    // 添加示例
    const examplesFolder = this.gui.addFolder('示例');
    const exampleController = {
      showSubClipExample: () => this.showSubClipExample(),
      showSequenceExample: () => this.showSequenceExample(),
      showTransitionExample: () => this.showTransitionExample(),
      showModifierExample: () => this.showModifierExample(),
      showEventExample: () => this.showEventExample()
    };
    examplesFolder.add(exampleController, 'showSubClipExample').name('子片段示例');
    examplesFolder.add(exampleController, 'showSequenceExample').name('序列示例');
    examplesFolder.add(exampleController, 'showTransitionExample').name('过渡示例');
    examplesFolder.add(exampleController, 'showModifierExample').name('变形示例');
    examplesFolder.add(exampleController, 'showEventExample').name('事件示例');
    examplesFolder.open();
  }

  /**
   * 显示子片段示例
   */
  private showSubClipExample(): void {
    // 停止当前动作
    if (this.currentAction) {
      this.currentAction.stop();
    }

    // 播放行走循环子片段
    const walkLoop = this.subClips.get('walkLoop');
    if (walkLoop && this.mixer) {
      const clip = walkLoop.getClip();
      if (clip) {
        this.currentAction = this.mixer.clipAction(clip);
        this.currentAction.play();
      }
    }
  }

  /**
   * 显示序列示例
   */
  private showSequenceExample(): void {
    // 停止当前动作
    if (this.currentAction) {
      this.currentAction.stop();
    }

    // 播放行走到跑步序列
    const sequence = this.sequences.get('walkToRun');
    if (sequence && this.mixer) {
      sequence.play();
    }
  }

  /**
   * 显示过渡示例
   */
  private showTransitionExample(): void {
    // 停止当前动作
    if (this.currentAction) {
      this.currentAction.stop();
    }

    // 开始行走到跑步过渡
    const transition = this.transitions.get('walkToRun');
    if (transition && this.mixer) {
      transition.start();
    }
  }

  /**
   * 显示变形示例
   */
  private showModifierExample(): void {
    // 停止当前动作
    if (this.currentAction) {
      this.currentAction.stop();
    }

    // 应用时间缩放变形器
    const modifier = this.modifiers.get('timeScale');
    const clip = this.animations.find(clip => clip.name === 'run');
    if (modifier && clip && this.mixer) {
      const modifiedClip = modifier.apply(clip);
      this.currentAction = this.mixer.clipAction(modifiedClip);
      this.currentAction.play();
    }
  }

  /**
   * 显示事件示例
   */
  private showEventExample(): void {
    // 停止当前动作
    if (this.currentAction) {
      this.currentAction.stop();
    }

    // 播放跳跃动画并监听事件
    const clip = this.animations.find(clip => clip.name === 'jump');
    if (clip && this.mixer) {
      this.currentAction = this.mixer.clipAction(clip);
      this.currentAction.play();

      // 重置事件
      this.events.get('jumpStart')?.reset();
      this.events.get('jumpPeak')?.reset();
      this.events.get('jumpEnd')?.reset();

      // 监听动画更新
      const jumpStartEvent = this.events.get('jumpStart');
      const jumpPeakEvent = this.events.get('jumpPeak');
      const jumpEndEvent = this.events.get('jumpEnd');

      // 创建子片段
      const jumpSubClip = new SubClip({
        name: 'jumpFull',
        originalClip: clip,
        startTime: 0,
        endTime: clip.duration,
        loop: false
      });

      // 监听混合器更新
      const onUpdate = (event: any) => {
        const time = this.currentAction!.time;
        const progress = time / clip.duration;

        // 检查事件
        if (jumpStartEvent?.shouldTrigger(time, progress, jumpSubClip)) {
          jumpStartEvent.trigger(time, progress, jumpSubClip);
        }
        if (jumpPeakEvent?.shouldTrigger(time, progress, jumpSubClip)) {
          jumpPeakEvent.trigger(time, progress, jumpSubClip);
        }
        if (jumpEndEvent?.shouldTrigger(time, progress, jumpSubClip)) {
          jumpEndEvent.trigger(time, progress, jumpSubClip);
        }
      };

      this.mixer.addEventListener('loop', onUpdate);
      this.mixer.addEventListener('finished', onUpdate);
    }
  }

  /**
   * 窗口大小调整处理函数
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 动画循环
   */
  private animate(): void {
    requestAnimationFrame(this.animate.bind(this));

    const delta = this.clock.getDelta();

    // 更新控制器
    this.controls.update();

    // 更新混合器
    if (this.mixer) {
      this.mixer.update(delta);
    }

    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }
}

// 创建示例
new SubClipExample();
