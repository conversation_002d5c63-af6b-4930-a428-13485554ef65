/**
 * 软体切割示例
 * 展示如何使用软体物理系统的切割和撕裂功能
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Vector3 } from '../../src/math/Vector3';
import { Quaternion } from '../../src/math/Quaternion';
import { 
  PhysicsSystem, 
  PhysicsBodyComponent, 
  PhysicsColliderComponent,
  BodyType,
  ColliderType,
  PhysicsMaterialFactory
} from '../../src/physics';
import { SoftBodySystem } from '../../src/physics/softbody/SoftBodySystem';
import { SoftBodyComponent, SoftBodyType } from '../../src/physics/softbody/SoftBodyComponent';
import { InputSystem } from '../../src/input/InputSystem';
import { KeyCode } from '../../src/input/KeyCode';

/**
 * 软体切割示例
 */
export class CuttingExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  
  /** 软体物理系统 */
  private softBodySystem: SoftBodySystem;
  
  /** 输入系统 */
  private inputSystem: InputSystem;
  
  /** 地面实体 */
  private ground: Entity;
  
  /** 布料实体 */
  private cloth: Entity;
  
  /** 刀实体 */
  private knife: Entity;
  
  /** 是否已初始化 */
  private initialized: boolean = false;
  
  /** 撕裂阈值 */
  private tearingThreshold: number = 1.5;
  
  /** 是否启用撕裂 */
  private tearingEnabled: boolean = true;

  /**
   * 创建软体切割示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建场景
    this.scene = new Scene('软体切割示例场景');
    
    // 创建物理系统
    this.physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.82, z: 0 },
      debug: true
    });
    
    // 创建软体物理系统
    this.softBodySystem = new SoftBodySystem({
      physicsSystem: this.physicsSystem,
      debug: true,
      useSoftBodyCutter: true
    });
    
    // 创建输入系统
    this.inputSystem = new InputSystem();
    
    // 添加系统到引擎
    this.engine.addSystem(this.physicsSystem);
    this.engine.addSystem(this.softBodySystem);
    this.engine.addSystem(this.inputSystem);
    
    // 初始化物理材质工厂
    PhysicsMaterialFactory.initialize();
    
    // 创建地面
    this.ground = this.createGround();
    
    // 添加地面到场景
    this.scene.addEntity(this.ground);
    
    // 注册更新回调
    this.engine.onUpdate.add(this.update.bind(this));
  }

  /**
   * 初始化示例
   */
  public initialize(): void {
    if (this.initialized) return;
    
    // 创建布料
    this.cloth = this.createCloth();
    this.scene.addEntity(this.cloth);
    
    // 创建刀
    this.knife = this.createKnife();
    this.scene.addEntity(this.knife);
    
    // 设置撕裂阈值
    this.softBodySystem.setTearingThreshold(this.tearingThreshold);
    
    // 设置场景为活跃场景
    this.engine.getWorld().setActiveScene(this.scene);
    
    this.initialized = true;
  }

  /**
   * 更新
   * @param deltaTime 帧间隔时间（秒）
   */
  private update(deltaTime: number): void {
    // 处理输入
    this.handleInput();
    
    // 检查刀是否切割布料
    this.checkKnifeCutting();
  }

  /**
   * 处理输入
   */
  private handleInput(): void {
    // 移动刀
    if (this.inputSystem.isKeyPressed(KeyCode.W)) {
      this.moveKnife(0, 0, -0.1);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.S)) {
      this.moveKnife(0, 0, 0.1);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.A)) {
      this.moveKnife(-0.1, 0, 0);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.D)) {
      this.moveKnife(0.1, 0, 0);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.Q)) {
      this.moveKnife(0, 0.1, 0);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.E)) {
      this.moveKnife(0, -0.1, 0);
    }
    
    // 切割布料
    if (this.inputSystem.isKeyPressed(KeyCode.SPACE)) {
      this.cutCloth();
    }
    
    // 调整撕裂阈值
    if (this.inputSystem.isKeyPressed(KeyCode.UP)) {
      this.increaseTearingThreshold();
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.DOWN)) {
      this.decreaseTearingThreshold();
    }
    
    // 启用/禁用撕裂
    if (this.inputSystem.isKeyPressed(KeyCode.T)) {
      this.toggleTearing();
    }
  }

  /**
   * 移动刀
   * @param x X轴移动量
   * @param y Y轴移动量
   * @param z Z轴移动量
   */
  private moveKnife(x: number, y: number, z: number): void {
    const transform = this.knife.getTransform();
    const position = transform.getPosition();
    position.x += x;
    position.y += y;
    position.z += z;
    transform.setPosition(position.x, position.y, position.z);
    
    // 更新物理体位置
    const physicsBody = this.knife.getComponent(PhysicsBodyComponent);
    if (physicsBody) {
      physicsBody.setPosition(position);
    }
  }

  /**
   * 增加撕裂阈值
   */
  private increaseTearingThreshold(): void {
    this.tearingThreshold += 0.1;
    this.softBodySystem.setTearingThreshold(this.tearingThreshold);
    console.log(`撕裂阈值: ${this.tearingThreshold.toFixed(1)}`);
  }

  /**
   * 减少撕裂阈值
   */
  private decreaseTearingThreshold(): void {
    this.tearingThreshold = Math.max(1.0, this.tearingThreshold - 0.1);
    this.softBodySystem.setTearingThreshold(this.tearingThreshold);
    console.log(`撕裂阈值: ${this.tearingThreshold.toFixed(1)}`);
  }

  /**
   * 切换撕裂功能
   */
  private toggleTearing(): void {
    this.tearingEnabled = !this.tearingEnabled;
    this.softBodySystem.setTearingEnabled(this.tearingEnabled);
    console.log(`撕裂功能: ${this.tearingEnabled ? '启用' : '禁用'}`);
  }

  /**
   * 检查刀是否切割布料
   */
  private checkKnifeCutting(): void {
    // 这里可以实现自动检测切割，但为了简单起见，我们使用空格键手动触发切割
  }

  /**
   * 切割布料
   */
  private cutCloth(): void {
    console.log('尝试切割布料');
    
    // 创建切割射线
    const knifeTransform = this.knife.getTransform();
    const knifePosition = knifeTransform.getPosition();
    const knifeDirection = new THREE.Vector3(0, -1, 0); // 刀朝下
    
    // 使用刀的位置和方向创建切割射线
    const ray = {
      origin: knifePosition,
      direction: knifeDirection,
      length: 2.0 // 射线长度
    };
    
    // 切割布料
    const success = this.softBodySystem.cutSoftBodyWithRay(this.cloth.id, ray);
    
    if (success) {
      console.log('成功切割布料！');
    }
  }

  /**
   * 创建地面
   * @returns 地面实体
   */
  private createGround(): Entity {
    // 创建地面实体
    const ground = new Entity('ground');
    
    // 添加变换组件
    const transform = ground.getTransform();
    transform.setPosition(0, -2, 0);
    
    // 创建地面网格
    const geometry = new THREE.BoxGeometry(20, 1, 20);
    const material = new THREE.MeshStandardMaterial({ color: 0x999999 });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    ground.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    ground.addComponent(new PhysicsBodyComponent({
      type: BodyType.STATIC,
      mass: 0
    }));
    
    // 添加碰撞器组件
    ground.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: 10, y: 0.5, z: 10 }
      }
    }));
    
    return ground;
  }

  /**
   * 创建布料
   * @returns 布料实体
   */
  private createCloth(): Entity {
    // 创建布料实体
    const cloth = new Entity('cloth');
    
    // 添加变换组件
    const transform = cloth.getTransform();
    transform.setPosition(0, 5, 0);
    transform.setRotation(Math.PI / 2, 0, 0);
    
    // 添加软体组件
    const softBodyComponent = new SoftBodyComponent({
      type: SoftBodyType.CLOTH,
      mass: 1,
      stiffness: 100,
      damping: 0.1,
      fixedCorners: true,
      params: {
        gridSize: { x: 20, y: 20 },
        width: 10,
        height: 10
      }
    });
    
    cloth.addComponent(softBodyComponent);
    
    // 将软体组件添加到软体系统
    this.softBodySystem.addSoftBody(softBodyComponent);
    
    return cloth;
  }

  /**
   * 创建刀
   * @returns 刀实体
   */
  private createKnife(): Entity {
    // 创建刀实体
    const knife = new Entity('knife');
    
    // 添加变换组件
    const transform = knife.getTransform();
    transform.setPosition(0, 8, 0);
    
    // 创建刀网格
    const geometry = new THREE.BoxGeometry(0.1, 0.5, 1);
    const material = new THREE.MeshStandardMaterial({ color: 0xcccccc });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    knife.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    knife.addComponent(new PhysicsBodyComponent({
      type: BodyType.KINEMATIC,
      mass: 1
    }));
    
    // 添加碰撞器组件
    knife.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: 0.05, y: 0.25, z: 0.5 }
      }
    }));
    
    return knife;
  }
}
