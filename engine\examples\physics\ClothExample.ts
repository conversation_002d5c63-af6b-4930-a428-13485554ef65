/**
 * 布料物理示例
 * 展示如何使用软体物理系统创建布料效果
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Vector3 } from '../../src/math/Vector3';
import { Quaternion } from '../../src/math/Quaternion';
import { 
  PhysicsSystem, 
  PhysicsBodyComponent, 
  PhysicsColliderComponent,
  BodyType,
  ColliderType,
  PhysicsMaterialFactory
} from '../../src/physics';
import { SoftBodySystem } from '../../src/physics/softbody/SoftBodySystem';
import { SoftBodyComponent, SoftBodyType } from '../../src/physics/softbody/SoftBodyComponent';
import { InputSystem } from '../../src/input/InputSystem';
import { KeyCode } from '../../src/input/KeyCode';

/**
 * 布料物理示例
 */
export class ClothExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  
  /** 软体物理系统 */
  private softBodySystem: SoftBodySystem;
  
  /** 输入系统 */
  private inputSystem: InputSystem;
  
  /** 地面实体 */
  private ground: Entity;
  
  /** 布料实体 */
  private cloth: Entity;
  
  /** 球体实体 */
  private sphere: Entity;
  
  /** 是否已初始化 */
  private initialized: boolean = false;
  
  /** 风力大小 */
  private windForce: number = 0;
  
  /** 风力方向 */
  private windDirection: Vector3 = new Vector3(1, 0, 0);

  /**
   * 创建布料物理示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建场景
    this.scene = new Scene('布料物理示例场景');
    
    // 创建物理系统
    this.physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.82, z: 0 },
      debug: true
    });
    
    // 创建软体物理系统
    this.softBodySystem = new SoftBodySystem({
      physicsSystem: this.physicsSystem,
      debug: true
    });
    
    // 创建输入系统
    this.inputSystem = new InputSystem();
    
    // 添加系统到引擎
    this.engine.addSystem(this.physicsSystem);
    this.engine.addSystem(this.softBodySystem);
    this.engine.addSystem(this.inputSystem);
    
    // 初始化物理材质工厂
    PhysicsMaterialFactory.initialize();
    
    // 创建地面
    this.ground = this.createGround();
    
    // 添加地面到场景
    this.scene.addEntity(this.ground);
    
    // 注册更新回调
    this.engine.onUpdate.add(this.update.bind(this));
  }

  /**
   * 初始化示例
   */
  public initialize(): void {
    if (this.initialized) return;
    
    // 创建布料
    this.cloth = this.createCloth();
    this.scene.addEntity(this.cloth);
    
    // 创建球体
    this.sphere = this.createSphere();
    this.scene.addEntity(this.sphere);
    
    // 设置场景为活跃场景
    this.engine.getWorld().setActiveScene(this.scene);
    
    this.initialized = true;
  }

  /**
   * 更新
   * @param deltaTime 帧间隔时间（秒）
   */
  private update(deltaTime: number): void {
    // 处理输入
    this.handleInput();
    
    // 应用风力
    this.applyWind();
  }

  /**
   * 处理输入
   */
  private handleInput(): void {
    // 移动球体
    if (this.inputSystem.isKeyPressed(KeyCode.W)) {
      this.moveSphere(0, 0, -0.1);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.S)) {
      this.moveSphere(0, 0, 0.1);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.A)) {
      this.moveSphere(-0.1, 0, 0);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.D)) {
      this.moveSphere(0.1, 0, 0);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.Q)) {
      this.moveSphere(0, 0.1, 0);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.E)) {
      this.moveSphere(0, -0.1, 0);
    }
    
    // 控制风力
    if (this.inputSystem.isKeyPressed(KeyCode.UP)) {
      this.windForce += 0.1;
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.DOWN)) {
      this.windForce -= 0.1;
      if (this.windForce < 0) this.windForce = 0;
    }
    
    // 改变风向
    if (this.inputSystem.isKeyPressed(KeyCode.LEFT)) {
      this.windDirection.x -= 0.1;
      this.windDirection.normalize();
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.RIGHT)) {
      this.windDirection.x += 0.1;
      this.windDirection.normalize();
    }
  }

  /**
   * 移动球体
   * @param x X轴移动量
   * @param y Y轴移动量
   * @param z Z轴移动量
   */
  private moveSphere(x: number, y: number, z: number): void {
    const transform = this.sphere.getTransform();
    const position = transform.getPosition();
    position.x += x;
    position.y += y;
    position.z += z;
    transform.setPosition(position.x, position.y, position.z);
    
    // 更新物理体位置
    const physicsBody = this.sphere.getComponent(PhysicsBodyComponent);
    if (physicsBody) {
      physicsBody.setPosition(position);
    }
  }

  /**
   * 应用风力
   */
  private applyWind(): void {
    if (this.windForce <= 0) return;
    
    const softBody = this.cloth.getComponent(SoftBodyComponent);
    if (softBody) {
      // 应用风力到软体
      // 注意：这需要在SoftBodyComponent中添加applyForce方法
      // softBody.applyForce(this.windDirection.clone().multiplyScalar(this.windForce));
    }
  }

  /**
   * 创建地面
   * @returns 地面实体
   */
  private createGround(): Entity {
    // 创建地面实体
    const ground = new Entity('ground');
    
    // 添加变换组件
    const transform = ground.getTransform();
    transform.setPosition(0, -2, 0);
    
    // 创建地面网格
    const geometry = new THREE.BoxGeometry(20, 1, 20);
    const material = new THREE.MeshStandardMaterial({ color: 0x999999 });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    ground.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    ground.addComponent(new PhysicsBodyComponent({
      type: BodyType.STATIC,
      mass: 0
    }));
    
    // 添加碰撞器组件
    ground.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: 10, y: 0.5, z: 10 }
      }
    }));
    
    return ground;
  }

  /**
   * 创建布料
   * @returns 布料实体
   */
  private createCloth(): Entity {
    // 创建布料实体
    const cloth = new Entity('cloth');
    
    // 添加变换组件
    const transform = cloth.getTransform();
    transform.setPosition(0, 5, 0);
    transform.setRotation(Math.PI / 2, 0, 0);
    
    // 添加软体组件
    const softBodyComponent = new SoftBodyComponent({
      type: SoftBodyType.CLOTH,
      mass: 1,
      stiffness: 100,
      damping: 0.1,
      fixedCorners: true,
      params: {
        gridSize: { x: 20, y: 20 },
        width: 10,
        height: 10
      }
    });
    
    cloth.addComponent(softBodyComponent);
    
    // 将软体组件添加到软体系统
    this.softBodySystem.addSoftBody(softBodyComponent);
    
    return cloth;
  }

  /**
   * 创建球体
   * @returns 球体实体
   */
  private createSphere(): Entity {
    // 创建球体实体
    const sphere = new Entity('sphere');
    
    // 添加变换组件
    const transform = sphere.getTransform();
    transform.setPosition(0, 3, 0);
    
    // 创建球体网格
    const geometry = new THREE.SphereGeometry(1, 32, 32);
    const material = new THREE.MeshStandardMaterial({ color: 0xff0000 });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    sphere.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    sphere.addComponent(new PhysicsBodyComponent({
      type: BodyType.KINEMATIC,
      mass: 5
    }));
    
    // 添加碰撞器组件
    sphere.addComponent(new PhysicsColliderComponent({
      type: ColliderType.SPHERE,
      params: {
        radius: 1
      }
    }));
    
    return sphere;
  }
}
