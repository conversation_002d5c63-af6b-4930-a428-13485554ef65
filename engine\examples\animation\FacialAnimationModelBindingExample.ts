/**
 * 面部动画模型绑定示例
 * 演示如何将面部动画系统与具体的3D模型绑定
 */
import * as THREE from 'three';
import { Engine } from '../../core/Engine';
import { World } from '../../core/World';
import { Entity } from '../../core/Entity';
import { 
  FacialAnimationSystem, 
  FacialAnimationComponent, 
  FacialExpressionType, 
  VisemeType,
  FacialAnimationModelAdapterSystem,
  FacialAnimationModelType
} from '../../animation';
import { GLTFLoader } from '../../loaders/GLTFLoader';
import { OrbitControls } from '../../utils/OrbitControls';

/**
 * 面部动画模型绑定示例
 */
export class FacialAnimationModelBindingExample {
  /** 引擎 */
  private engine: Engine;
  /** 世界 */
  private world: World;
  /** 场景 */
  private scene: THREE.Scene;
  /** 相机 */
  private camera: THREE.PerspectiveCamera;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer;
  /** 控制器 */
  private controls: OrbitControls;
  /** 角色实体 */
  private characterEntity: Entity;
  /** 面部动画系统 */
  private facialAnimationSystem: FacialAnimationSystem;
  /** 面部动画模型适配器系统 */
  private modelAdapterSystem: FacialAnimationModelAdapterSystem;
  /** 是否运行中 */
  private running: boolean = false;
  /** 模型类型 */
  private modelType: FacialAnimationModelType = FacialAnimationModelType.GENERIC;

  /**
   * 构造函数
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 获取世界
    this.world = this.engine.getWorld();
    
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x333333);
    
    // 创建相机
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.camera.position.set(0, 1.6, 2);
    
    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    document.body.appendChild(this.renderer.domElement);
    
    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 1.6, 0);
    this.controls.update();
    
    // 创建角色实体
    this.characterEntity = this.world.createEntity();
    
    // 创建面部动画系统
    this.facialAnimationSystem = new FacialAnimationSystem(this.world, {
      debug: true
    });
    
    // 创建面部动画模型适配器系统
    this.modelAdapterSystem = new FacialAnimationModelAdapterSystem(this.world, {
      debug: true,
      autoDetectBlendShapes: true
    });
    
    // 设置模型适配器系统
    this.facialAnimationSystem.setModelAdapterSystem(this.modelAdapterSystem);
    
    // 添加系统到世界
    this.world.addSystem(this.facialAnimationSystem);
    this.world.addSystem(this.modelAdapterSystem);
    
    // 设置窗口大小变化监听
    window.addEventListener('resize', this.onWindowResize.bind(this));
  }

  /**
   * 窗口大小变化处理
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(ambientLight);
    
    // 添加方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 2, 3);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);
    
    // 添加地面
    const ground = new THREE.Mesh(
      new THREE.PlaneGeometry(10, 10),
      new THREE.MeshStandardMaterial({ color: 0x999999 })
    );
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    this.scene.add(ground);
    
    // 添加坐标轴辅助
    const axesHelper = new THREE.AxesHelper(1);
    this.scene.add(axesHelper);
  }

  /**
   * 加载模型
   * @param modelPath 模型路径
   */
  private loadModel(modelPath: string): void {
    // 创建GLTF加载器
    const loader = new GLTFLoader();
    
    // 加载角色模型
    loader.load(modelPath, (gltf) => {
      // 添加模型到场景
      this.scene.add(gltf.scene);
      
      // 设置阴影
      gltf.scene.traverse((object) => {
        if (object instanceof THREE.Mesh) {
          object.castShadow = true;
          object.receiveShadow = true;
        }
      });
      
      // 查找骨骼网格
      let skinnedMesh: THREE.SkinnedMesh | null = null;
      gltf.scene.traverse((object) => {
        if (object instanceof THREE.SkinnedMesh && !skinnedMesh) {
          skinnedMesh = object;
        }
      });
      
      if (skinnedMesh) {
        // 创建面部动画组件
        const facialAnimation = this.facialAnimationSystem.createFacialAnimation(this.characterEntity);
        
        // 将面部动画组件与模型绑定
        this.facialAnimationSystem.linkToModel(this.characterEntity, skinnedMesh);
        
        console.log('模型加载完成，已绑定面部动画组件');
        console.log('混合形状字典:', skinnedMesh.morphTargetDictionary);
        console.log('混合形状影响:', skinnedMesh.morphTargetInfluences);
      } else {
        console.warn('未找到骨骼网格');
      }
    }, undefined, (error) => {
      console.error('加载模型失败:', error);
    });
  }

  /**
   * 测试表情
   */
  private testExpressions(): void {
    // 获取面部动画组件
    const facialAnimation = this.facialAnimationSystem.getFacialAnimation(this.characterEntity);
    if (!facialAnimation) return;
    
    // 定义表情序列
    const expressions = [
      FacialExpressionType.NEUTRAL,
      FacialExpressionType.HAPPY,
      FacialExpressionType.SAD,
      FacialExpressionType.ANGRY,
      FacialExpressionType.SURPRISED
    ];
    
    // 循环播放表情
    let index = 0;
    setInterval(() => {
      facialAnimation.setExpression(expressions[index], 1.0, 0.5);
      console.log(`表情: ${expressions[index]}`);
      
      index = (index + 1) % expressions.length;
    }, 2000);
  }

  /**
   * 测试口型
   */
  private testVisemes(): void {
    // 获取面部动画组件
    const facialAnimation = this.facialAnimationSystem.getFacialAnimation(this.characterEntity);
    if (!facialAnimation) return;
    
    // 定义口型序列
    const visemes = [
      VisemeType.SILENT,
      VisemeType.AA,
      VisemeType.EE,
      VisemeType.IH,
      VisemeType.OH,
      VisemeType.OU
    ];
    
    // 循环播放口型
    let index = 0;
    setInterval(() => {
      facialAnimation.setViseme(visemes[index], 1.0, 0.2);
      console.log(`口型: ${visemes[index]}`);
      
      index = (index + 1) % visemes.length;
    }, 1000);
  }

  /**
   * 设置模型类型
   * @param type 模型类型
   */
  public setModelType(type: FacialAnimationModelType): void {
    this.modelType = type;
  }

  /**
   * 启动示例
   * @param modelPath 模型路径
   */
  public start(modelPath: string = 'models/character.glb'): void {
    // 创建场景
    this.createScene();
    
    // 加载模型
    this.loadModel(modelPath);
    
    // 启动引擎
    this.engine.start();
    
    // 开始动画循环
    this.running = true;
    this.animate();
    
    // 延迟测试表情和口型
    setTimeout(() => {
      this.testExpressions();
      this.testVisemes();
    }, 3000);
    
    console.log('面部动画模型绑定示例已启动');
  }

  /**
   * 停止示例
   */
  public stop(): void {
    this.running = false;
    this.engine.stop();
    console.log('面部动画模型绑定示例已停止');
  }

  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;
    
    requestAnimationFrame(this.animate.bind(this));
    
    // 更新控制器
    this.controls.update();
    
    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }
}

// 创建示例
const example = new FacialAnimationModelBindingExample();

// 设置模型类型（根据实际模型选择）
example.setModelType(FacialAnimationModelType.GLTF);

// 启动示例
example.start('models/character_with_blendshapes.glb');
