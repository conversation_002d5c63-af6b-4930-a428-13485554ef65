# 动画系统

DL（Digital Learning）引擎编辑器的动画系统提供了强大而灵活的工具，用于创建、编辑和管理各种类型的动画。本文档将详细介绍如何使用动画系统创建关键帧动画、骨骼动画、混合动画等，以及如何使用动画状态机控制复杂的动画逻辑。

## 动画系统概述

### 主要功能

DL（Digital Learning）引擎的动画系统支持以下主要功能：

- **关键帧动画**：通过设置关键帧创建属性动画
- **骨骼动画**：导入和编辑骨骼动画，支持蒙皮网格
- **动画混合**：混合多个动画，创建平滑过渡
- **动画重定向**：将一个角色的动画应用到另一个角色
- **动画状态机**：创建复杂的动画逻辑和过渡
- **程序化动画**：通过代码或视觉脚本创建动态动画
- **物理驱动动画**：结合物理系统创建真实的动画效果
- **面部动画**：支持面部表情和口型同步

### 动画组件

动画系统主要包括以下组件：

- **动画组件（Animation Component）**：附加到实体上，包含动画数据和控制逻辑
- **动画控制器（Animation Controller）**：管理动画状态和过渡
- **动画混合器（Animation Mixer）**：混合多个动画片段
- **骨骼组件（Skeleton Component）**：定义骨骼层次结构
- **蒙皮组件（Skinned Mesh Component）**：将网格绑定到骨骼

## 动画面板

### 打开动画面板

1. 点击顶部菜单栏的"视图 > 动画面板"
2. 或使用快捷键Ctrl+5（Windows）/Command+5（Mac）

![动画面板](../../assets/images/animation-panel.png)

### 动画面板界面

动画面板包含以下主要部分：

- **动画列表**：显示当前选中对象的所有动画片段
- **时间轴**：显示和编辑动画关键帧
- **属性列表**：显示可以动画化的属性
- **预览控制**：控制动画播放和预览
- **工具栏**：提供各种动画编辑工具

## 关键帧动画

### 创建关键帧动画

1. 在场景中选择要添加动画的对象
2. 在属性面板中，点击"添加组件"按钮，选择"动画组件"
3. 在动画面板中，点击"创建动画"按钮
4. 输入动画名称，点击"创建"按钮
5. 在时间轴上，移动时间指示器到起始位置
6. 调整对象的属性（位置、旋转、缩放等）
7. 点击"添加关键帧"按钮或按下K键
8. 移动时间指示器到新位置
9. 调整对象的属性
10. 再次点击"添加关键帧"按钮
11. 重复步骤8-10，创建更多关键帧
12. 点击"保存"按钮保存动画

![创建关键帧动画](../../assets/images/keyframe-animation.png)

### 编辑关键帧

1. 在时间轴上，选择要编辑的关键帧
2. 调整对象的属性
3. 或直接在时间轴上拖动关键帧调整其时间位置
4. 按住Shift键拖动可以复制关键帧
5. 右键点击关键帧可以访问更多选项（删除、复制、粘贴等）

### 调整动画曲线

1. 在时间轴下方，点击"曲线编辑器"按钮
2. 在曲线编辑器中，选择要编辑的属性曲线
3. 调整曲线控制点，修改插值方式
4. 可以选择不同的插值类型：线性、平滑、阶梯等
5. 点击"应用"按钮保存更改

![动画曲线编辑器](../../assets/images/animation-curve-editor.png)

## 骨骼动画

### 导入骨骼动画

1. 在资产面板中，点击"导入"按钮
2. 选择包含骨骼和动画的模型文件（FBX、glTF等）
3. 在导入选项中，确保选中"导入动画"选项
4. 点击"导入"按钮
5. 导入的模型将包含骨骼层次结构和动画数据

### 预览骨骼动画

1. 在场景中选择导入的骨骼模型
2. 在动画面板中，选择要预览的动画片段
3. 点击"播放"按钮或按下空格键
4. 使用时间轴下方的控制按钮调整播放速度、循环等选项

### 编辑骨骼动画

1. 在动画面板中，选择要编辑的骨骼动画
2. 点击"编辑"按钮
3. 在骨骼动画编辑器中，可以：
   - 调整骨骼位置和旋转
   - 添加或删除关键帧
   - 修改动画曲线
   - 调整动画速度和时长
4. 点击"保存"按钮保存更改

## 动画混合

### 创建动画混合器

1. 在场景中选择带有多个动画的对象
2. 在属性面板中，点击"添加组件"按钮，选择"动画混合器组件"
3. 在动画混合器设置中，添加要混合的动画片段
4. 设置每个动画的权重和过渡参数
5. 点击"应用"按钮

### 设置动画过渡

1. 在动画混合器中，选择两个要设置过渡的动画
2. 点击"添加过渡"按钮
3. 设置过渡参数：
   - **过渡时间**：过渡的持续时间
   - **过渡曲线**：控制过渡的速度变化
   - **过渡条件**：触发过渡的条件
4. 点击"保存"按钮

![动画过渡设置](../../assets/images/animation-transition.png)

### 运行时控制动画混合

在运行时，可以通过以下方式控制动画混合：

```javascript
// 获取动画混合器组件
const mixer = entity.getComponent('AnimationMixer');

// 设置动画权重
mixer.setWeight('Walk', 0.7);
mixer.setWeight('Run', 0.3);

// 触发过渡
mixer.crossFade('Walk', 'Run', 0.5); // 0.5秒内从Walk过渡到Run
```

## 动画状态机

### 创建动画状态机

1. 在场景中选择对象
2. 在属性面板中，点击"添加组件"按钮，选择"动画控制器组件"
3. 点击"编辑状态机"按钮
4. 在状态机编辑器中，点击"添加状态"按钮
5. 为每个状态分配一个动画片段
6. 创建状态之间的过渡
7. 设置过渡条件和参数
8. 点击"保存"按钮

![动画状态机编辑器](../../assets/images/animation-state-machine.png)

### 设置状态过渡

1. 在状态机编辑器中，点击两个状态之间的空白区域
2. 在弹出的菜单中，选择"创建过渡"
3. 设置过渡参数：
   - **过渡时间**：过渡的持续时间
   - **过渡条件**：触发过渡的条件（参数值、时间等）
   - **过渡优先级**：当多个过渡条件满足时的优先级
4. 点击"保存"按钮

### 添加状态机参数

1. 在状态机编辑器中，点击"参数"标签
2. 点击"添加参数"按钮
3. 选择参数类型（布尔、整数、浮点数、触发器）
4. 输入参数名称
5. 设置默认值
6. 点击"添加"按钮

### 运行时控制状态机

在运行时，可以通过以下方式控制动画状态机：

```javascript
// 获取动画控制器组件
const controller = entity.getComponent('AnimationController');

// 设置参数值
controller.setParameter('Speed', 5.0);
controller.setParameter('IsJumping', true);
controller.setTrigger('Attack');

// 直接切换状态
controller.setState('Jump');
```

## 动画重定向

### 设置动画重定向

1. 在场景中选择目标角色
2. 在属性面板中，点击"添加组件"按钮，选择"动画重定向组件"
3. 设置源骨骼和目标骨骼的映射关系
4. 调整骨骼对应关系和缩放因子
5. 点击"应用"按钮

![动画重定向设置](../../assets/images/animation-retargeting.png)

### 骨骼映射

1. 在动画重定向组件中，点击"编辑骨骼映射"按钮
2. 在骨骼映射编辑器中，手动设置源骨骼和目标骨骼的对应关系
3. 或使用"自动映射"功能，根据骨骼名称或层次结构自动创建映射
4. 调整映射参数，如位置偏移、旋转偏移和缩放因子
5. 点击"保存"按钮应用映射

## 面部动画

### 设置面部动画

1. 在场景中选择带有面部骨骼或变形目标的角色
2. 在属性面板中，点击"添加组件"按钮，选择"面部动画组件"
3. 设置面部动画参数：
   - **变形目标**：定义面部表情的变形目标
   - **骨骼控制**：控制面部骨骼的动画
   - **口型同步**：设置语音与口型的同步参数
4. 点击"应用"按钮

### 创建口型同步

1. 在面部动画组件中，点击"设置口型同步"按钮
2. 导入音频文件
3. 系统会自动分析音频，生成口型关键帧
4. 调整口型参数和时间对齐
5. 预览口型同步效果
6. 点击"保存"按钮

## 程序化动画

### 创建程序化动画

1. 在场景中选择对象
2. 在属性面板中，点击"添加组件"按钮，选择"程序化动画组件"
3. 选择程序化动画类型：
   - **程序化行走**：自动生成行走动画
   - **程序化看向**：角色自动看向目标
   - **程序化手部IK**：手部反向动力学
   - **程序化物理反应**：基于物理的动画反应
4. 设置相应参数
5. 点击"应用"按钮

### 使用视觉脚本创建动画

1. 打开视觉脚本编辑器
2. 添加动画节点（播放动画、混合动画、设置参数等）
3. 连接节点，创建动画逻辑
4. 设置触发条件和参数
5. 保存视觉脚本
6. 将视觉脚本分配给对象

## 最佳实践

### 性能优化

- 使用适当的骨骼数量，避免过于复杂的骨骼层次结构
- 对不重要的骨骼使用更低的更新频率
- 对远处的角色使用简化的动画或更低的帧率
- 使用动画LOD（细节层次）系统
- 预计算复杂的动画曲线

### 动画组织

- 使用清晰的命名约定
- 将相关动画分组到动画库中
- 创建可重用的动画片段
- 使用动画状态机管理复杂的动画逻辑
- 记录特殊动画的用途和设置

## 故障排除

### 动画不播放

如果动画不播放：

1. 检查动画组件是否正确添加到对象
2. 确保动画片段已正确导入和分配
3. 检查动画控制器的状态和参数
4. 确保没有其他组件覆盖或禁用动画
5. 检查动画的播放速度是否为零

### 骨骼动画问题

如果骨骼动画显示不正确：

1. 检查骨骼层次结构是否正确
2. 确保蒙皮权重设置合理
3. 检查骨骼方向和缩放
4. 确保使用了正确的骨骼空间（局部/全局）
5. 尝试重新导入模型，确保选择正确的导入选项

## 下一步

现在您已经了解了动画系统的基本功能，可以继续学习其他相关功能：

- [物理系统](./physics-system.md)
- [交互系统](./interaction-system.md)
- [视觉脚本](./visual-scripting.md)
- [动画高级技巧](../advanced/advanced-animation.md)
