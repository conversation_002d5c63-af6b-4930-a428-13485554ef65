/**
 * 资源更新配置面板组件
 * 用于配置资源热更新的各种选项
 */
import React, { useState, useEffect } from 'react';
import { 
  Form, 
  Input, 
  InputNumber, 
  Switch, 
  Select, 
  Button, 
  Card, 
  Divider, 
  Tooltip, 
  Space, 
  Typography, 
  Collapse,
  Alert
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  QuestionCircleOutlined,
  SettingOutlined,
  CloudOutlined,
  DownloadOutlined,
  SafetyOutlined,
  NotificationOutlined,
  FileZipOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { resourceHotUpdateService } from '../../services/ResourceHotUpdateService';
import { ResourceUpdateType } from '../../store/resources/resourceHotUpdateSlice';

const { Option } = Select;
const { Text, Title } = Typography;
const { Panel } = Collapse;

/**
 * 资源更新配置面板组件属性
 */
interface ResourceUpdateConfigPanelProps {
  /** 是否紧凑模式 */
  compact?: boolean;
  /** 关闭回调 */
  onClose?: () => void;
}

/**
 * 资源更新配置面板组件
 */
const ResourceUpdateConfigPanel: React.FC<ResourceUpdateConfigPanelProps> = ({
  compact = false,
  onClose
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  
  // 从Redux状态获取配置
  const { config } = useSelector((state: RootState) => state.resourceHotUpdate);
  
  // 本地状态
  const [isAdvancedMode, setIsAdvancedMode] = useState<boolean>(false);
  
  // 初始化表单
  useEffect(() => {
    form.setFieldsValue({
      ...config,
      updateTypes: config.updateTypes || []
    });
  }, [config, form]);
  
  // 保存配置
  const handleSaveConfig = () => {
    form.validateFields()
      .then(values => {
        resourceHotUpdateService.updateConfig(values);
      })
      .catch(error => {
        console.error('表单验证失败:', error);
      });
  };
  
  // 重置配置
  const handleResetConfig = () => {
    form.resetFields();
  };
  
  // 渲染基本配置
  const renderBasicConfig = () => {
    return (
      <div className="basic-config">
        <Form.Item
          label={t('resources.updateConfig.autoCheckInterval')}
          name="autoCheckInterval"
          tooltip={t('resources.updateConfig.autoCheckIntervalTip')}
        >
          <InputNumber
            min={0}
            max={86400}
            addonAfter={t('resources.updateConfig.seconds')}
            style={{ width: '100%' }}
          />
        </Form.Item>
        
        <Form.Item
          label={t('resources.updateConfig.autoDownloadUpdates')}
          name="autoDownloadUpdates"
          valuePropName="checked"
          tooltip={t('resources.updateConfig.autoDownloadUpdatesTip')}
        >
          <Switch />
        </Form.Item>
        
        <Form.Item
          label={t('resources.updateConfig.autoApplyUpdates')}
          name="autoApplyUpdates"
          valuePropName="checked"
          tooltip={t('resources.updateConfig.autoApplyUpdatesTip')}
        >
          <Switch />
        </Form.Item>
        
        <Form.Item
          label={t('resources.updateConfig.updateTypes')}
          name="updateTypes"
          tooltip={t('resources.updateConfig.updateTypesTip')}
        >
          <Select
            mode="multiple"
            placeholder={t('resources.updateConfig.selectUpdateTypes')}
            style={{ width: '100%' }}
          >
            <Option value={ResourceUpdateType.ASSETS}>{t('resources.updateConfig.typeAssets')}</Option>
            <Option value={ResourceUpdateType.SCRIPTS}>{t('resources.updateConfig.typeScripts')}</Option>
            <Option value={ResourceUpdateType.SHADERS}>{t('resources.updateConfig.typeShaders')}</Option>
            <Option value={ResourceUpdateType.CONFIGS}>{t('resources.updateConfig.typeConfigs')}</Option>
            <Option value={ResourceUpdateType.SYSTEM}>{t('resources.updateConfig.typeSystem')}</Option>
          </Select>
        </Form.Item>
        
        <Form.Item
          label={t('resources.updateConfig.updateChannel')}
          name="updateChannel"
          tooltip={t('resources.updateConfig.updateChannelTip')}
        >
          <Select>
            <Option value="stable">{t('resources.updateConfig.channelStable')}</Option>
            <Option value="beta">{t('resources.updateConfig.channelBeta')}</Option>
            <Option value="dev">{t('resources.updateConfig.channelDev')}</Option>
          </Select>
        </Form.Item>
        
        <Form.Item
          label={t('resources.updateConfig.notifyOnUpdateAvailable')}
          name="notifyOnUpdateAvailable"
          valuePropName="checked"
          tooltip={t('resources.updateConfig.notifyOnUpdateAvailableTip')}
        >
          <Switch />
        </Form.Item>
      </div>
    );
  };
  
  // 渲染高级配置
  const renderAdvancedConfig = () => {
    if (!isAdvancedMode) return null;
    
    return (
      <div className="advanced-config">
        <Divider orientation="left">{t('resources.updateConfig.advancedSettings')}</Divider>
        
        <Collapse bordered={false} defaultActiveKey={['download', 'backup']}>
          <Panel 
            header={
              <Space>
                <DownloadOutlined />
                <Text strong>{t('resources.updateConfig.downloadSettings')}</Text>
              </Space>
            } 
            key="download"
          >
            <Form.Item
              label={t('resources.updateConfig.maxConcurrentDownloads')}
              name="maxConcurrentDownloads"
              tooltip={t('resources.updateConfig.maxConcurrentDownloadsTip')}
            >
              <InputNumber min={1} max={10} />
            </Form.Item>
            
            <Form.Item
              label={t('resources.updateConfig.retryCount')}
              name="retryCount"
              tooltip={t('resources.updateConfig.retryCountTip')}
            >
              <InputNumber min={0} max={10} />
            </Form.Item>
            
            <Form.Item
              label={t('resources.updateConfig.retryDelay')}
              name="retryDelay"
              tooltip={t('resources.updateConfig.retryDelayTip')}
            >
              <InputNumber
                min={100}
                max={10000}
                step={100}
                addonAfter={t('resources.updateConfig.milliseconds')}
                style={{ width: '100%' }}
              />
            </Form.Item>
            
            <Form.Item
              label={t('resources.updateConfig.enableDeltaUpdates')}
              name="enableDeltaUpdates"
              valuePropName="checked"
              tooltip={t('resources.updateConfig.enableDeltaUpdatesTip')}
            >
              <Switch />
            </Form.Item>
            
            <Form.Item
              label={t('resources.updateConfig.compressionType')}
              name="compressionType"
              tooltip={t('resources.updateConfig.compressionTypeTip')}
            >
              <Select>
                <Option value="gzip">{t('resources.updateConfig.compressionGzip')}</Option>
                <Option value="brotli">{t('resources.updateConfig.compressionBrotli')}</Option>
                <Option value="none">{t('resources.updateConfig.compressionNone')}</Option>
              </Select>
            </Form.Item>
          </Panel>
          
          <Panel 
            header={
              <Space>
                <SafetyOutlined />
                <Text strong>{t('resources.updateConfig.backupSettings')}</Text>
              </Space>
            } 
            key="backup"
          >
            <Form.Item
              label={t('resources.updateConfig.backupBeforeUpdate')}
              name="backupBeforeUpdate"
              valuePropName="checked"
              tooltip={t('resources.updateConfig.backupBeforeUpdateTip')}
            >
              <Switch />
            </Form.Item>
            
            <Form.Item
              label={t('resources.updateConfig.maxBackupCount')}
              name="maxBackupCount"
              tooltip={t('resources.updateConfig.maxBackupCountTip')}
            >
              <InputNumber min={1} max={20} />
            </Form.Item>
            
            <Form.Item
              label={t('resources.updateConfig.cleanupAfterUpdate')}
              name="cleanupAfterUpdate"
              valuePropName="checked"
              tooltip={t('resources.updateConfig.cleanupAfterUpdateTip')}
            >
              <Switch />
            </Form.Item>
          </Panel>
          
          <Panel 
            header={
              <Space>
                <CloudOutlined />
                <Text strong>{t('resources.updateConfig.serverSettings')}</Text>
              </Space>
            } 
            key="server"
          >
            <Form.Item
              label={t('resources.updateConfig.updateServer')}
              name="updateServer"
              tooltip={t('resources.updateConfig.updateServerTip')}
            >
              <Input placeholder="https://example.com/api/updates" />
            </Form.Item>
          </Panel>
        </Collapse>
      </div>
    );
  };
  
  return (
    <div className={`resource-update-config ${compact ? 'compact' : ''}`}>
      <Card
        title={
          <div className="config-header">
            <Title level={4}>{t('resources.updateConfig.title')}</Title>
            {onClose && (
              <Button 
                type="text" 
                icon={<CloseCircleOutlined />} 
                onClick={onClose}
              />
            )}
          </div>
        }
        bordered={!compact}
        extra={
          <Button
            type="link"
            icon={<SettingOutlined />}
            onClick={() => setIsAdvancedMode(!isAdvancedMode)}
          >
            {isAdvancedMode ? t('resources.updateConfig.basicMode') : t('resources.updateConfig.advancedMode')}
          </Button>
        }
      >
        <Alert
          message={t('resources.updateConfig.configInfo')}
          description={t('resources.updateConfig.configDescription')}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Form
          form={form}
          layout="vertical"
          initialValues={config}
        >
          {renderBasicConfig()}
          {renderAdvancedConfig()}
          
          <Divider />
          
          <Form.Item>
            <Space>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSaveConfig}
              >
                {t('resources.updateConfig.saveConfig')}
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleResetConfig}
              >
                {t('resources.updateConfig.resetConfig')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default ResourceUpdateConfigPanel;
