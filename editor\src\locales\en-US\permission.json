{"title": "Permission Management", "description": "Manage user permissions and roles", "userPermissions": "User Permissions", "rolePermissions": "Role Permissions", "permissionName": "Permission Name", "category": "Category", "status": "Status", "selectUser": "Select User", "selectPermission": "Select Permission", "granted": "Granted", "denied": "Denied", "inherited": "Inherited", "custom": "Custom", "enabled": "Enabled", "disabled": "Disabled", "actions": {"addPermission": "Add Permission", "removePermission": "Remove Permission", "setRole": "Set Role", "saveChanges": "Save Changes", "resetToDefault": "Reset to De<PERSON>ult", "applyToAll": "Apply to All Users"}, "roles": {"title": "Roles", "owner": "Owner", "admin": "Admin", "editor": "Editor", "viewer": "Viewer", "description": {"owner": "Has all permissions, including project management and deletion", "admin": "Has most permissions, including user management and role assignment", "editor": "Can edit scenes, create and modify entities", "viewer": "Can only view scenes, cannot edit"}}, "types": {"view_scene": "View Scene", "edit_scene": "Edit Scene", "create_entity": "Create Entity", "update_entity": "Update Entity", "delete_entity": "Delete Entity", "add_component": "Add Component", "update_component": "Update Component", "remove_component": "Remove Component", "upload_asset": "Upload Asset", "delete_asset": "Delete Asset", "save_scene": "Save Scene", "export_scene": "Export Scene", "import_scene": "Import Scene", "manage_users": "Manage Users", "assign_roles": "Assign Roles", "manage_permissions": "Manage Permissions", "manage_project": "Manage Project", "delete_project": "Delete Project"}, "categories": {"basic": "Basic Permissions", "entity": "Entity Operations", "component": "Component Operations", "asset": "Asset Operations", "scene": "Scene Operations", "user": "User Management", "project": "Project Management"}, "errors": {"notLoggedIn": "You are not logged in", "noPermission": "You don't have {permission} permission", "cannotChangeOwner": "Cannot change the role of the project owner", "cannotRemoveOwner": "There must be at least one project owner", "saveFailed": "Failed to save permissions", "loadFailed": "Failed to load permissions"}, "success": {"roleChanged": "Role has been changed", "permissionAdded": "Permission has been added", "permissionRemoved": "Permission has been removed", "settingsSaved": "Settings have been saved", "settingsReset": "Settings have been reset to default"}, "confirmations": {"resetPermissions": "Are you sure you want to reset all permission settings to default? This will affect all users.", "removePermission": "Are you sure you want to remove this permission?", "changeRole": "Are you sure you want to change the user's role? This will reset their custom permissions."}, "tooltips": {"inheritedPermission": "This permission is inherited from the user's role", "customPermission": "This permission is custom to the user", "overriddenPermission": "This permission overrides the role's default permission", "roleInfo": "View role details", "permissionInfo": "View permission details"}, "settings": {"title": "Permission Settings", "enablePermissionCheck": "Enable Permission Checking", "showPermissionErrors": "Show Permission Error Messages", "permissionInheritance": "Enable Permission Inheritance", "permissionOverride": "Allow Permission Override", "defaultRole": "Default Role"}}