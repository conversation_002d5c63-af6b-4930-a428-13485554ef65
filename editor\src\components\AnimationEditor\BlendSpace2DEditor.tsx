/**
 * 2D混合空间编辑器组件
 * 用于编辑2D动画混合空间
 */
import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Button,
  Form,
  Input,
  InputNumber,
  Switch,
  Space,
  Table,
  Modal,
  Select,
  Tooltip,
  message,
  Divider,
  Row,
  Col,
  Tabs,
  Collapse
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SettingOutlined,
  CloseOutlined,
  EnvironmentOutlined,
  LineChartOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  HeatMapOutlined,
  NodeIndexOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { RootState } from '../../store';
import {
  loadBlendSpace2D,
  updateBlendSpace2D,
  addBlendSpace2DNode,
  updateBlendSpace2DNode,
  removeBlendSpace2DNode,
  setBlendSpace2DPosition
} from '../../store/animations/blendSpaceSlice';
import { blendSpaceService } from '../../services/blendSpaceService';
import { BlendCurveType } from '../../../engine/src/animation/AnimationBlender';
import BlendCurveEditor from './BlendCurveEditor';
import './AnimationEditor.less';

const { Option } = Select;
const { confirm } = Modal;
const { TabPane } = Tabs;
const { Panel } = Collapse;

/**
 * 2D混合空间编辑器属性
 */
interface BlendSpace2DEditorProps {
  /** 实体ID */
  entityId: string;
  /** 混合空间ID */
  blendSpaceId: string;
  /** 关闭回调 */
  onClose?: () => void;
}

/**
 * 2D混合空间编辑器组件
 */
const BlendSpace2DEditor: React.FC<BlendSpace2DEditorProps> = ({ entityId, blendSpaceId, onClose }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 从Redux获取状态
  const { blendSpace, loading, error } = useSelector((state: RootState) => state.blendSpace);

  // 本地状态
  const [form] = Form.useForm();
  const [nodeForm] = Form.useForm();
  const [isEditing, setIsEditing] = useState(false);
  const [isAddingNode, setIsAddingNode] = useState(false);
  const [editingNode, setEditingNode] = useState<any>(null);
  const [availableClips, setAvailableClips] = useState<string[]>([]);
  const [currentPosition, setCurrentPosition] = useState({ x: 0, y: 0 });
  const [isPlaying, setIsPlaying] = useState(false);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [showCurveEditor, setShowCurveEditor] = useState(false);
  const [curveType, setCurveType] = useState<BlendCurveType>(BlendCurveType.LINEAR);
  const [activeTab, setActiveTab] = useState<string>('basic');
  const [isDragging, setIsDragging] = useState(false);
  const [dragNodeId, setDragNodeId] = useState<string | null>(null);
  const [showNodeWeights, setShowNodeWeights] = useState(true);
  const [showHeatmap, setShowHeatmap] = useState(false);
  const [showTriangulation, setShowTriangulation] = useState(true);

  // 引用
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number | null>(null);

  // 加载混合空间
  useEffect(() => {
    if (entityId && blendSpaceId) {
      dispatch(loadBlendSpace2D({ entityId, blendSpaceId }));
      loadAvailableClips();
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [dispatch, entityId, blendSpaceId]);

  // 加载可用的动画片段
  const loadAvailableClips = async () => {
    try {
      const clips = await blendSpaceService.getAvailableAnimationClips(entityId);
      setAvailableClips(clips);
    } catch (error) {
      console.error('加载动画片段失败:', error);
      message.error(t('editor.animation.loadClipsFailed'));
    }
  };

  // 当混合空间加载完成后，设置表单值
  useEffect(() => {
    if (blendSpace && blendSpace.id === blendSpaceId) {
      form.setFieldsValue({
        name: blendSpace.name,
        description: blendSpace.description,
        minX: blendSpace.config.minX,
        maxX: blendSpace.config.maxX,
        minY: blendSpace.config.minY,
        maxY: blendSpace.config.maxY,
        normalizeInput: blendSpace.config.normalizeInput,
        useTriangulation: blendSpace.config.useTriangulation,
        xLabel: blendSpace.config.xLabel || 'X',
        yLabel: blendSpace.config.yLabel || 'Y',
        blendCurveType: blendSpace.config.blendCurveType || BlendCurveType.LINEAR
      });

      setCurrentPosition(blendSpace.position || { x: 0, y: 0 });

      // 设置混合曲线类型
      if (blendSpace.config.blendCurveType) {
        setCurveType(blendSpace.config.blendCurveType);
      }
    }
  }, [blendSpace, blendSpaceId, form]);

  // 绘制混合空间
  useEffect(() => {
    if (canvasRef.current && blendSpace && blendSpace.id === blendSpaceId) {
      drawBlendSpace();
    }
  }, [blendSpace, currentPosition, selectedNode]);

  // 绘制混合空间
  const drawBlendSpace = () => {
    if (!canvasRef.current || !blendSpace) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 绘制背景
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 获取配置
    const { minX, maxX, minY, maxY } = blendSpace.config;

    // 计算缩放和偏移
    const padding = 40;
    const width = canvas.width - padding * 2;
    const height = canvas.height - padding * 2;

    // 绘制热图（如果启用）
    if (showHeatmap && blendSpace.nodes && blendSpace.nodes.length >= 3 && blendSpace.config.useTriangulation) {
      // 绘制热图
      const resolution = 40; // 热图分辨率
      const stepX = width / resolution;
      const stepY = height / resolution;

      for (let i = 0; i < resolution; i++) {
        for (let j = 0; j < resolution; j++) {
          const x = padding + i * stepX;
          const y = padding + j * stepY;

          // 计算该位置对应的混合空间坐标
          const posX = minX + (i / resolution) * (maxX - minX);
          const posY = maxY - (j / resolution) * (maxY - minY);

          // 计算该位置的权重
          const weights = calculateWeights(posX, posY);

          if (weights) {
            // 混合颜色
            let r = 0, g = 0, b = 0;
            for (const nodeId in weights) {
              const weight = weights[nodeId];
              if (weight > 0) {
                // 为每个节点分配一个颜色
                const node = blendSpace.nodes.find(n => n.id === nodeId);
                if (node) {
                  const index = blendSpace.nodes.indexOf(node);
                  const hue = (index * 137) % 360; // 使用黄金角来分散颜色
                  const [nr, ng, nb] = hslToRgb(hue / 360, 0.7, 0.5);
                  r += nr * weight;
                  g += ng * weight;
                  b += nb * weight;
                }
              }
            }

            ctx.fillStyle = `rgba(${Math.round(r)}, ${Math.round(g)}, ${Math.round(b)}, 0.7)`;
            ctx.fillRect(x, y, stepX, stepY);
          }
        }
      }
    }

    // 绘制网格
    ctx.strokeStyle = '#dddddd';
    ctx.lineWidth = 1;

    // 绘制水平网格线
    for (let i = 0; i <= 10; i++) {
      const y = padding + (height * i) / 10;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(padding + width, y);
      ctx.stroke();
    }

    // 绘制垂直网格线
    for (let i = 0; i <= 10; i++) {
      const x = padding + (width * i) / 10;
      ctx.beginPath();
      ctx.moveTo(x, padding);
      ctx.lineTo(x, padding + height);
      ctx.stroke();
    }

    // 绘制三角剖分（如果启用）
    if (showTriangulation && blendSpace.nodes && blendSpace.nodes.length >= 3 && blendSpace.config.useTriangulation) {
      // 计算三角形
      const triangles = calculateTriangles();

      if (triangles && triangles.length > 0) {
        ctx.strokeStyle = 'rgba(0, 128, 255, 0.5)';
        ctx.lineWidth = 1.5;

        for (const triangle of triangles) {
          const [a, b, c] = triangle;

          // 获取节点坐标
          const nodeA = blendSpace.nodes.find(n => n.id === a);
          const nodeB = blendSpace.nodes.find(n => n.id === b);
          const nodeC = blendSpace.nodes.find(n => n.id === c);

          if (nodeA && nodeB && nodeC) {
            // 计算画布坐标
            const xA = padding + ((nodeA.position.x - minX) / (maxX - minX)) * width;
            const yA = padding + ((maxY - nodeA.position.y) / (maxY - minY)) * height;

            const xB = padding + ((nodeB.position.x - minX) / (maxX - minX)) * width;
            const yB = padding + ((maxY - nodeB.position.y) / (maxY - minY)) * height;

            const xC = padding + ((nodeC.position.x - minX) / (maxX - minX)) * width;
            const yC = padding + ((maxY - nodeC.position.y) / (maxY - minY)) * height;

            // 绘制三角形
            ctx.beginPath();
            ctx.moveTo(xA, yA);
            ctx.lineTo(xB, yB);
            ctx.lineTo(xC, yC);
            ctx.closePath();
            ctx.stroke();
          }
        }
      }
    }

    // 绘制坐标轴
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;

    // X轴
    ctx.beginPath();
    ctx.moveTo(padding, padding + height / 2);
    ctx.lineTo(padding + width, padding + height / 2);
    ctx.stroke();

    // Y轴
    ctx.beginPath();
    ctx.moveTo(padding + width / 2, padding);
    ctx.lineTo(padding + width / 2, padding + height);
    ctx.stroke();

    // 绘制刻度标签
    ctx.fillStyle = '#000000';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.font = '12px Arial';

    // X轴标签
    ctx.fillText(minX.toString(), padding, padding + height / 2 + 20);
    ctx.fillText(maxX.toString(), padding + width, padding + height / 2 + 20);
    ctx.fillText(blendSpace.config.xLabel || 'X', padding + width / 2, padding + height + 20);

    // Y轴标签
    ctx.textAlign = 'right';
    ctx.fillText(maxY.toString(), padding - 10, padding);
    ctx.fillText(minY.toString(), padding - 10, padding + height);
    ctx.textAlign = 'center';
    ctx.fillText(blendSpace.config.yLabel || 'Y', padding - 25, padding + height / 2);

    // 绘制节点
    if (blendSpace.nodes) {
      for (const node of blendSpace.nodes) {
        // 计算节点位置
        const x = padding + ((node.position.x - minX) / (maxX - minX)) * width;
        const y = padding + ((maxY - node.position.y) / (maxY - minY)) * height;

        // 绘制节点
        ctx.beginPath();
        ctx.arc(x, y, 10, 0, Math.PI * 2);

        // 设置节点颜色
        if (node.weight > 0) {
          // 为每个节点分配一个颜色
          const index = blendSpace.nodes.indexOf(node);
          const hue = (index * 137) % 360; // 使用黄金角来分散颜色
          const [r, g, b] = hslToRgb(hue / 360, 0.7, 0.5);
          ctx.fillStyle = `rgba(${r}, ${g}, ${b}, ${node.weight})`;
        } else {
          ctx.fillStyle = '#cccccc';
        }

        // 如果是选中的节点或正在拖动的节点，则高亮显示
        if (node.id === selectedNode || node.id === dragNodeId) {
          ctx.strokeStyle = '#f5222d';
          ctx.lineWidth = 3;
        } else {
          ctx.strokeStyle = '#000000';
          ctx.lineWidth = 1;
        }

        ctx.fill();
        ctx.stroke();

        // 绘制节点名称
        ctx.fillStyle = '#000000';
        ctx.fillText(node.clipName, x, y - 20);

        // 绘制节点权重（如果启用）
        if (showNodeWeights && node.weight > 0) {
          ctx.fillText(node.weight.toFixed(2), x, y + 20);
        }
      }
    }

    // 绘制当前位置
    if (currentPosition) {
      const x = padding + ((currentPosition.x - minX) / (maxX - minX)) * width;
      const y = padding + ((maxY - currentPosition.y) / (maxY - minY)) * height;

      ctx.beginPath();
      ctx.arc(x, y, 8, 0, Math.PI * 2);
      ctx.fillStyle = '#f5222d';
      ctx.fill();

      // 绘制十字线
      ctx.strokeStyle = '#f5222d';
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(x - 15, y);
      ctx.lineTo(x + 15, y);
      ctx.moveTo(x, y - 15);
      ctx.lineTo(x, y + 15);
      ctx.stroke();

      // 绘制当前位置坐标
      ctx.fillStyle = '#000000';
      ctx.textAlign = 'left';
      ctx.fillText(`(${currentPosition.x.toFixed(2)}, ${currentPosition.y.toFixed(2)})`, x + 15, y - 15);
    }
  };

  // 计算三角形
  const calculateTriangles = () => {
    if (!blendSpace || !blendSpace.nodes || blendSpace.nodes.length < 3) return [];

    // 简单的三角剖分算法（Delaunay三角剖分的简化版）
    const triangles: string[][] = [];

    // 获取所有节点
    const nodes = blendSpace.nodes;

    // 对于每三个节点，形成一个三角形
    for (let i = 0; i < nodes.length - 2; i++) {
      for (let j = i + 1; j < nodes.length - 1; j++) {
        for (let k = j + 1; k < nodes.length; k++) {
          // 检查三角形是否有效（不是共线的）
          const a = nodes[i];
          const b = nodes[j];
          const c = nodes[k];

          // 计算三角形面积
          const area = Math.abs(
            (a.position.x * (b.position.y - c.position.y) +
             b.position.x * (c.position.y - a.position.y) +
             c.position.x * (a.position.y - b.position.y)) / 2
          );

          // 如果面积接近于0，则三点共线
          if (area > 0.0001) {
            triangles.push([a.id, b.id, c.id]);
          }
        }
      }
    }

    return triangles;
  };

  // 计算权重
  const calculateWeights = (x: number, y: number) => {
    if (!blendSpace || !blendSpace.nodes || blendSpace.nodes.length < 3) return null;

    // 获取所有三角形
    const triangles = calculateTriangles();
    if (!triangles || triangles.length === 0) return null;

    // 对于每个三角形，检查点是否在其中
    for (const triangle of triangles) {
      const [aId, bId, cId] = triangle;

      // 获取节点
      const a = blendSpace.nodes.find(n => n.id === aId);
      const b = blendSpace.nodes.find(n => n.id === bId);
      const c = blendSpace.nodes.find(n => n.id === cId);

      if (a && b && c) {
        // 检查点是否在三角形内
        if (isPointInTriangle(x, y, a.position.x, a.position.y, b.position.x, b.position.y, c.position.x, c.position.y)) {
          // 计算重心坐标
          const weights = calculateBarycentricWeights(
            x, y,
            a.position.x, a.position.y,
            b.position.x, b.position.y,
            c.position.x, c.position.y
          );

          // 返回权重
          return {
            [aId]: weights[0],
            [bId]: weights[1],
            [cId]: weights[2]
          };
        }
      }
    }

    // 如果点不在任何三角形内，则使用最近的节点
    const weights: { [key: string]: number } = {};
    let minDistance = Infinity;
    let closestNodeId = '';

    for (const node of blendSpace.nodes) {
      const dx = x - node.position.x;
      const dy = y - node.position.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance < minDistance) {
        minDistance = distance;
        closestNodeId = node.id;
      }
    }

    // 设置最近节点的权重为1
    for (const node of blendSpace.nodes) {
      weights[node.id] = node.id === closestNodeId ? 1 : 0;
    }

    return weights;
  };

  // 检查点是否在三角形内
  const isPointInTriangle = (px: number, py: number, ax: number, ay: number, bx: number, by: number, cx: number, cy: number) => {
    const v0x = cx - ax;
    const v0y = cy - ay;
    const v1x = bx - ax;
    const v1y = by - ay;
    const v2x = px - ax;
    const v2y = py - ay;

    const dot00 = v0x * v0x + v0y * v0y;
    const dot01 = v0x * v1x + v0y * v1y;
    const dot02 = v0x * v2x + v0y * v2y;
    const dot11 = v1x * v1x + v1y * v1y;
    const dot12 = v1x * v2x + v1y * v2y;

    const invDenom = 1 / (dot00 * dot11 - dot01 * dot01);
    const u = (dot11 * dot02 - dot01 * dot12) * invDenom;
    const v = (dot00 * dot12 - dot01 * dot02) * invDenom;

    return u >= 0 && v >= 0 && u + v <= 1;
  };

  // 计算重心坐标
  const calculateBarycentricWeights = (
    px: number, py: number,
    ax: number, ay: number,
    bx: number, by: number,
    cx: number, cy: number
  ) => {
    const v0x = bx - ax;
    const v0y = by - ay;
    const v1x = cx - ax;
    const v1y = cy - ay;
    const v2x = px - ax;
    const v2y = py - ay;

    const d00 = v0x * v0x + v0y * v0y;
    const d01 = v0x * v1x + v0y * v1y;
    const d11 = v1x * v1x + v1y * v1y;
    const d20 = v2x * v0x + v2y * v0y;
    const d21 = v2x * v1x + v2y * v1y;

    const denom = d00 * d11 - d01 * d01;

    const v = (d11 * d20 - d01 * d21) / denom;
    const w = (d00 * d21 - d01 * d20) / denom;
    const u = 1.0 - v - w;

    return [u, v, w];
  };

  // HSL转RGB辅助函数
  const hslToRgb = (h: number, s: number, l: number): [number, number, number] => {
    let r, g, b;

    if (s === 0) {
      r = g = b = l; // 灰色
    } else {
      const hue2rgb = (p: number, q: number, t: number) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1/6) return p + (q - p) * 6 * t;
        if (t < 1/2) return q;
        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
        return p;
      };

      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1/3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1/3);
    }

    return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
  };

  // 处理画布点击
  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || !blendSpace) return;

    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // 计算缩放和偏移
    const padding = 40;
    const width = canvas.width - padding * 2;
    const height = canvas.height - padding * 2;

    // 获取配置
    const { minX, maxX, minY, maxY } = blendSpace.config;

    // 检查是否点击了节点
    if (blendSpace.nodes) {
      for (const node of blendSpace.nodes) {
        // 计算节点位置
        const nodeX = padding + ((node.position.x - minX) / (maxX - minX)) * width;
        const nodeY = padding + ((maxY - node.position.y) / (maxY - minY)) * height;

        // 计算点击位置与节点的距离
        const distance = Math.sqrt(Math.pow(x - nodeX, 2) + Math.pow(y - nodeY, 2));

        // 如果点击了节点
        if (distance <= 10) {
          setSelectedNode(node.id);
          setDragNodeId(node.id);
          return;
        }
      }
    }

    // 计算位置
    const posX = minX + ((x - padding) / width) * (maxX - minX);
    const posY = maxY - ((y - padding) / height) * (maxY - minY);

    // 更新位置
    setCurrentPosition({ x: posX, y: posY });

    // 更新混合空间位置
    if (entityId && blendSpaceId) {
      dispatch(setBlendSpace2DPosition({
        entityId,
        blendSpaceId,
        position: { x: posX, y: posY }
      }));
    }

    // 清除选中的节点
    setSelectedNode(null);
  };

  // 处理画布鼠标移动
  const handleCanvasMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!dragNodeId || !canvasRef.current || !blendSpace) return;

    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // 计算缩放和偏移
    const padding = 40;
    const width = canvas.width - padding * 2;
    const height = canvas.height - padding * 2;

    // 获取配置
    const { minX, maxX, minY, maxY } = blendSpace.config;

    // 计算新位置
    const posX = minX + ((x - padding) / width) * (maxX - minX);
    const posY = maxY - ((y - padding) / height) * (maxY - minY);

    // 限制在范围内
    const newX = Math.max(minX, Math.min(maxX, posX));
    const newY = Math.max(minY, Math.min(maxY, posY));

    // 更新节点位置
    dispatch(updateBlendSpace2DNode({
      entityId,
      blendSpaceId,
      nodeId: dragNodeId,
      data: {
        position: { x: newX, y: newY }
      }
    }));

    setIsDragging(true);
  };

  // 处理画布鼠标按下
  const handleCanvasMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    handleCanvasClick(e);
  };

  // 处理画布鼠标抬起
  const handleCanvasMouseUp = () => {
    setDragNodeId(null);
    setIsDragging(false);
  };

  // 处理画布鼠标离开
  const handleCanvasMouseLeave = () => {
    if (isDragging) {
      setDragNodeId(null);
      setIsDragging(false);
    }
  };

  // 切换显示节点权重
  const toggleNodeWeights = () => {
    setShowNodeWeights(!showNodeWeights);
  };

  // 切换显示热图
  const toggleHeatmap = () => {
    setShowHeatmap(!showHeatmap);
  };

  // 切换显示三角剖分
  const toggleTriangulation = () => {
    setShowTriangulation(!showTriangulation);
  };

  // 处理播放/暂停
  const handlePlayPause = () => {
    if (isPlaying) {
      // 停止播放
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    } else {
      // 开始播放
      let startTime = Date.now();
      let directionX = 1;
      let directionY = 1;

      const animate = () => {
        const now = Date.now();
        const deltaTime = (now - startTime) / 1000;
        startTime = now;

        // 更新位置
        let newX = currentPosition.x + directionX * deltaTime;
        let newY = currentPosition.y + directionY * deltaTime * 0.7;

        // 检查边界
        if (blendSpace && blendSpace.config) {
          if (newX >= blendSpace.config.maxX) {
            newX = blendSpace.config.maxX;
            directionX = -1;
          } else if (newX <= blendSpace.config.minX) {
            newX = blendSpace.config.minX;
            directionX = 1;
          }

          if (newY >= blendSpace.config.maxY) {
            newY = blendSpace.config.maxY;
            directionY = -1;
          } else if (newY <= blendSpace.config.minY) {
            newY = blendSpace.config.minY;
            directionY = 1;
          }
        }

        const newPosition = { x: newX, y: newY };
        setCurrentPosition(newPosition);

        // 更新混合空间位置
        if (entityId && blendSpaceId) {
          dispatch(setBlendSpace2DPosition({
            entityId,
            blendSpaceId,
            position: newPosition
          }));
        }

        animationFrameRef.current = requestAnimationFrame(animate);
      };

      animationFrameRef.current = requestAnimationFrame(animate);
    }

    setIsPlaying(!isPlaying);
  };

  // 处理曲线类型变更
  const handleCurveTypeChange = (type: BlendCurveType) => {
    setCurveType(type);

    // 更新混合空间配置
    if (blendSpace) {
      dispatch(updateBlendSpace2D({
        entityId,
        blendSpaceId,
        data: {
          ...blendSpace,
          config: {
            ...blendSpace.config,
            blendCurveType: type
          }
        }
      }));
    }
  };

  // 处理自定义曲线变更
  const handleCustomCurveChange = (curve: (t: number) => number) => {
    // 更新混合空间配置
    if (blendSpace) {
      dispatch(updateBlendSpace2D({
        entityId,
        blendSpaceId,
        data: {
          ...blendSpace,
          config: {
            ...blendSpace.config,
            blendCurveType: BlendCurveType.CUSTOM,
            customBlendCurve: curve.toString()
          }
        }
      }));
    }
  };

  // 处理预设曲线变更
  const handlePresetCurveChange = (presetName: string) => {
    // 更新混合空间配置
    if (blendSpace) {
      dispatch(updateBlendSpace2D({
        entityId,
        blendSpaceId,
        data: {
          ...blendSpace,
          config: {
            ...blendSpace.config,
            blendCurveType: BlendCurveType.CUSTOM,
            blendCurvePreset: presetName
          }
        }
      }));
    }
  };

  // 处理贝塞尔曲线变更
  const handleBezierCurveChange = (x1: number, y1: number, x2: number, y2: number) => {
    // 更新混合空间配置
    if (blendSpace) {
      dispatch(updateBlendSpace2D({
        entityId,
        blendSpaceId,
        data: {
          ...blendSpace,
          config: {
            ...blendSpace.config,
            blendCurveType: BlendCurveType.CUSTOM,
            bezierPoints: { x1, y1, x2, y2 }
          }
        }
      }));
    }
  };

  // 处理表单提交
  const handleFormSubmit = (values: any) => {
    if (!blendSpace) return;

    dispatch(updateBlendSpace2D({
      entityId,
      blendSpaceId,
      data: {
        name: values.name,
        description: values.description,
        config: {
          minX: values.minX,
          maxX: values.maxX,
          minY: values.minY,
          maxY: values.maxY,
          normalizeInput: values.normalizeInput,
          useTriangulation: values.useTriangulation,
          xLabel: values.xLabel,
          yLabel: values.yLabel,
          blendCurveType: values.blendCurveType || BlendCurveType.LINEAR,
          // 保留之前的自定义曲线设置
          customBlendCurve: blendSpace.config.customBlendCurve,
          blendCurvePreset: blendSpace.config.blendCurvePreset,
          bezierPoints: blendSpace.config.bezierPoints
        }
      }
    }));

    // 更新本地状态
    setCurveType(values.blendCurveType || BlendCurveType.LINEAR);

    setIsEditing(false);
    message.success(t('editor.animation.updateBlendSpaceSuccess'));
  };

  // 处理添加节点
  const handleAddNode = () => {
    nodeForm.resetFields();
    setEditingNode(null);
    setIsAddingNode(true);
  };

  // 处理编辑节点
  const handleEditNode = (node: any) => {
    nodeForm.setFieldsValue({
      clipName: node.clipName,
      positionX: node.position.x,
      positionY: node.position.y
    });
    setEditingNode(node);
    setIsAddingNode(true);
  };

  // 处理删除节点
  const handleDeleteNode = (node: any) => {
    confirm({
      title: t('editor.animation.confirmDeleteNode'),
      content: t('editor.animation.confirmDeleteNodeContent'),
      onOk: () => {
        dispatch(removeBlendSpace2DNode({
          entityId,
          blendSpaceId,
          nodeId: node.id
        }));
      }
    });
  };

  // 处理节点表单提交
  const handleNodeFormSubmit = (values: any) => {
    const position = { x: values.positionX, y: values.positionY };

    if (editingNode) {
      // 更新节点
      dispatch(updateBlendSpace2DNode({
        entityId,
        blendSpaceId,
        nodeId: editingNode.id,
        data: {
          clipName: values.clipName,
          position
        }
      }));
    } else {
      // 添加节点
      dispatch(addBlendSpace2DNode({
        entityId,
        blendSpaceId,
        data: {
          clipName: values.clipName,
          position
        }
      }));
    }

    setIsAddingNode(false);
    message.success(editingNode ? t('editor.animation.updateNodeSuccess') : t('editor.animation.addNodeSuccess'));
  };

  // 处理位置变化
  const handlePositionChange = (x: number, y: number) => {
    const newPosition = { x, y };
    setCurrentPosition(newPosition);

    // 更新混合空间位置
    if (entityId && blendSpaceId) {
      dispatch(setBlendSpace2DPosition({
        entityId,
        blendSpaceId,
        position: newPosition
      }));
    }
  };

  // 渲染节点表格
  const renderNodeTable = () => {
    if (!blendSpace) return null;

    const columns = [
      {
        title: t('editor.animation.clipName'),
        dataIndex: 'clipName',
        key: 'clipName'
      },
      {
        title: `${t('editor.animation.position')} X`,
        dataIndex: ['position', 'x'],
        key: 'positionX',
        render: (text: number) => text.toFixed(2)
      },
      {
        title: `${t('editor.animation.position')} Y`,
        dataIndex: ['position', 'y'],
        key: 'positionY',
        render: (text: number) => text.toFixed(2)
      },
      {
        title: t('editor.animation.weight'),
        dataIndex: 'weight',
        key: 'weight',
        render: (text: number) => text.toFixed(2)
      },
      {
        title: t('editor.actions'),
        key: 'actions',
        render: (_: any, record: any) => (
          <Space>
            <Tooltip title={t('editor.edit')}>
              <Button
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEditNode(record)}
              />
            </Tooltip>
            <Tooltip title={t('editor.delete')}>
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
                onClick={() => handleDeleteNode(record)}
              />
            </Tooltip>
          </Space>
        )
      }
    ];

    return (
      <Table
        dataSource={blendSpace.nodes}
        columns={columns}
        rowKey="id"
        size="small"
        pagination={false}
        onRow={(record) => ({
          onClick: () => setSelectedNode(record.id === selectedNode ? null : record.id)
        })}
        rowClassName={(record) => record.id === selectedNode ? 'selected-row' : ''}
      />
    );
  };

  return (
    <div className="blend-space-2d-editor">
      <div className="editor-header">
        <h3>{blendSpace ? blendSpace.name : t('editor.animation.blendSpace2D')}</h3>

        <Space>
          {isEditing ? (
            <Button
              icon={<SaveOutlined />}
              type="primary"
              onClick={() => form.submit()}
            >
              {t('editor.save')}
            </Button>
          ) : (
            <Button
              icon={<EditOutlined />}
              onClick={() => setIsEditing(true)}
            >
              {t('editor.edit')}
            </Button>
          )}

          <Button
            icon={<CloseOutlined />}
            onClick={onClose}
          >
            {t('editor.close')}
          </Button>
        </Space>
      </div>

      <div className="editor-content">
        {isEditing ? (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleFormSubmit}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label={t('editor.animation.name')}
                  rules={[{ required: true, message: t('editor.animation.nameRequired') }]}
                >
                  <Input />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="description"
                  label={t('editor.animation.description')}
                >
                  <Input.TextArea rows={1} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={6}>
                <Form.Item
                  name="minX"
                  label={t('editor.animation.minX')}
                  rules={[{ required: true, message: t('editor.animation.minXRequired') }]}
                >
                  <InputNumber style={{ width: '100%' }} />
                </Form.Item>
              </Col>

              <Col span={6}>
                <Form.Item
                  name="maxX"
                  label={t('editor.animation.maxX')}
                  rules={[{ required: true, message: t('editor.animation.maxXRequired') }]}
                >
                  <InputNumber style={{ width: '100%' }} />
                </Form.Item>
              </Col>

              <Col span={6}>
                <Form.Item
                  name="minY"
                  label={t('editor.animation.minY')}
                  rules={[{ required: true, message: t('editor.animation.minYRequired') }]}
                >
                  <InputNumber style={{ width: '100%' }} />
                </Form.Item>
              </Col>

              <Col span={6}>
                <Form.Item
                  name="maxY"
                  label={t('editor.animation.maxY')}
                  rules={[{ required: true, message: t('editor.animation.maxYRequired') }]}
                >
                  <InputNumber style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={6}>
                <Form.Item
                  name="xLabel"
                  label={t('editor.animation.xLabel')}
                >
                  <Input />
                </Form.Item>
              </Col>

              <Col span={6}>
                <Form.Item
                  name="yLabel"
                  label={t('editor.animation.yLabel')}
                >
                  <Input />
                </Form.Item>
              </Col>

              <Col span={6}>
                <Form.Item
                  name="normalizeInput"
                  label={t('editor.animation.normalizeInput')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>

              <Col span={6}>
                <Form.Item
                  name="useTriangulation"
                  label={t('editor.animation.useTriangulation')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>

            <Divider>{t('editor.animation.curve.settings')}</Divider>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="blendCurveType"
                  label={t('editor.animation.curve.type')}
                >
                  <Select>
                    <Option value={BlendCurveType.LINEAR}>{t('editor.animation.curve.linear')}</Option>
                    <Option value={BlendCurveType.EASE_IN}>{t('editor.animation.curve.easeIn')}</Option>
                    <Option value={BlendCurveType.EASE_OUT}>{t('editor.animation.curve.easeOut')}</Option>
                    <Option value={BlendCurveType.EASE_IN_OUT}>{t('editor.animation.curve.easeInOut')}</Option>
                    <Option value={BlendCurveType.ELASTIC}>{t('editor.animation.curve.elastic')}</Option>
                    <Option value={BlendCurveType.BOUNCE}>{t('editor.animation.curve.bounce')}</Option>
                    <Option value={BlendCurveType.SINE}>{t('editor.animation.curve.sine')}</Option>
                    <Option value={BlendCurveType.EXPONENTIAL}>{t('editor.animation.curve.exponential')}</Option>
                    <Option value={BlendCurveType.CIRCULAR}>{t('editor.animation.curve.circular')}</Option>
                    <Option value={BlendCurveType.QUADRATIC}>{t('editor.animation.curve.quadratic')}</Option>
                    <Option value={BlendCurveType.CUBIC}>{t('editor.animation.curve.cubic')}</Option>
                    <Option value={BlendCurveType.QUARTIC}>{t('editor.animation.curve.quartic')}</Option>
                    <Option value={BlendCurveType.QUINTIC}>{t('editor.animation.curve.quintic')}</Option>
                    <Option value={BlendCurveType.CUSTOM}>{t('editor.animation.curve.custom')}</Option>
                  </Select>
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item>
                  <Button
                    type="default"
                    icon={<LineChartOutlined />}
                    onClick={() => setShowCurveEditor(true)}
                  >
                    {t('editor.animation.curve.advancedSettings')}
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        ) : (
          <div className="blend-space-info">
            {blendSpace && (
              <>
                <p><strong>{t('editor.animation.description')}:</strong> {blendSpace.description || t('editor.animation.noDescription')}</p>
                <p><strong>X {t('editor.animation.range')}:</strong> {blendSpace.config.minX} - {blendSpace.config.maxX} ({blendSpace.config.xLabel || 'X'})</p>
                <p><strong>Y {t('editor.animation.range')}:</strong> {blendSpace.config.minY} - {blendSpace.config.maxY} ({blendSpace.config.yLabel || 'Y'})</p>
                <p><strong>{t('editor.animation.normalizeInput')}:</strong> {blendSpace.config.normalizeInput ? t('editor.yes') : t('editor.no')}</p>
                <p><strong>{t('editor.animation.useTriangulation')}:</strong> {blendSpace.config.useTriangulation ? t('editor.yes') : t('editor.no')}</p>
                <Divider>{t('editor.animation.curve.settings')}</Divider>
                <p><strong>{t('editor.animation.curve.type')}:</strong> {t(`editor.animation.curve.${blendSpace.config.blendCurveType || 'linear'}`)}</p>
                {blendSpace.config.blendCurveType === BlendCurveType.CUSTOM && blendSpace.config.blendCurvePreset && (
                  <p><strong>{t('editor.animation.curve.preset')}:</strong> {blendSpace.config.blendCurvePreset}</p>
                )}
                <Button
                  type="default"
                  icon={<LineChartOutlined />}
                  onClick={() => setShowCurveEditor(true)}
                  style={{ marginTop: '8px' }}
                >
                  {t('editor.animation.curve.advancedSettings')}
                </Button>
              </>
            )}
          </div>
        )}

        <Divider>{t('editor.animation.visualization')}</Divider>

        <div className="blend-space-visualization">
          <div className="blend-space-toolbar">
            <Space>
              <Button
                icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={handlePlayPause}
                title={isPlaying ? t('editor.animation.pause') : t('editor.animation.play')}
              >
                {isPlaying ? t('editor.animation.pause') : t('editor.animation.play')}
              </Button>

              <Button
                icon={<HeatMapOutlined />}
                onClick={toggleHeatmap}
                type={showHeatmap ? 'primary' : 'default'}
                title={t('editor.animation.toggleHeatmap')}
              >
                {t('editor.animation.heatmap')}
              </Button>

              <Button
                icon={<NodeIndexOutlined />}
                onClick={toggleTriangulation}
                type={showTriangulation ? 'primary' : 'default'}
                title={t('editor.animation.toggleTriangulation')}
              >
                {t('editor.animation.triangulation')}
              </Button>

              <Tooltip title={t('editor.animation.toggleWeights')}>
                <Button
                  icon={showNodeWeights ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                  onClick={toggleNodeWeights}
                  type={showNodeWeights ? 'primary' : 'default'}
                >
                  {t('editor.animation.weights')}
                </Button>
              </Tooltip>

              <Tooltip title={t('editor.animation.curve.settings')}>
                <Button
                  icon={<LineChartOutlined />}
                  onClick={() => setShowCurveEditor(true)}
                >
                  {t('editor.animation.curve.settings')}
                </Button>
              </Tooltip>
            </Space>

            <div className="position-display">
              X: {currentPosition.x.toFixed(2)}, Y: {currentPosition.y.toFixed(2)}
            </div>
          </div>

          <canvas
            ref={canvasRef}
            width={600}
            height={400}
            className="blend-space-canvas"
            onClick={handleCanvasClick}
            onMouseDown={handleCanvasMouseDown}
            onMouseMove={handleCanvasMouseMove}
            onMouseUp={handleCanvasMouseUp}
            onMouseLeave={handleCanvasMouseLeave}
          />

          <div className="blend-space-controls">
            <Space>
              <span>X:</span>
              <InputNumber
                value={currentPosition.x}
                min={blendSpace ? blendSpace.config.minX : -1}
                max={blendSpace ? blendSpace.config.maxX : 1}
                step={0.01}
                style={{ width: 80 }}
                onChange={(value) => handlePositionChange(value as number, currentPosition.y)}
              />

              <span>Y:</span>
              <InputNumber
                value={currentPosition.y}
                min={blendSpace ? blendSpace.config.minY : -1}
                max={blendSpace ? blendSpace.config.maxY : 1}
                step={0.01}
                style={{ width: 80 }}
                onChange={(value) => handlePositionChange(currentPosition.x, value as number)}
              />

              <Tooltip title={t('editor.animation.setPosition')}>
                <Button
                  icon={<EnvironmentOutlined />}
                  onClick={() => {
                    if (!blendSpace) return;

                    // 设置为当前位置
                    const x = (blendSpace.config.minX + blendSpace.config.maxX) / 2;
                    const y = (blendSpace.config.minY + blendSpace.config.maxY) / 2;
                    handlePositionChange(x, y);
                  }}
                />
              </Tooltip>
            </Space>
          </div>

          <div className="blend-space-instructions">
            {t('editor.animation.dragNodesInstructions')}
          </div>
        </div>

        <Divider>{t('editor.animation.nodes')}</Divider>

        <div className="blend-space-nodes">
          <div className="nodes-header">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddNode}
            >
              {t('editor.animation.addNode')}
            </Button>
          </div>

          <div className="nodes-content">
            {renderNodeTable()}
          </div>
        </div>
      </div>

      <Modal
        title={t('editor.animation.curve.editor')}
        open={showCurveEditor}
        onCancel={() => setShowCurveEditor(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setShowCurveEditor(false)}>
            {t('editor.close')}
          </Button>
        ]}
      >
        <BlendCurveEditor
          initialCurveType={curveType}
          onCurveTypeChange={handleCurveTypeChange}
          onCustomCurveChange={handleCustomCurveChange}
          onPresetCurveChange={handlePresetCurveChange}
          onBezierCurveChange={handleBezierCurveChange}
        />
      </Modal>

      <Modal
        title={editingNode ? t('editor.animation.editNode') : t('editor.animation.addNode')}
        open={isAddingNode}
        onCancel={() => setIsAddingNode(false)}
        footer={null}
      >
        <Form
          form={nodeForm}
          layout="vertical"
          onFinish={handleNodeFormSubmit}
        >
          <Form.Item
            name="clipName"
            label={t('editor.animation.clipName')}
            rules={[{ required: true, message: t('editor.animation.clipNameRequired') }]}
          >
            <Select>
              {availableClips.map(clip => (
                <Option key={clip} value={clip}>{clip}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="positionX"
            label={`${t('editor.animation.position')} X`}
            rules={[{ required: true, message: t('editor.animation.positionRequired') }]}
          >
            <InputNumber
              min={blendSpace ? blendSpace.config.minX : -1}
              max={blendSpace ? blendSpace.config.maxX : 1}
              step={0.01}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="positionY"
            label={`${t('editor.animation.position')} Y`}
            rules={[{ required: true, message: t('editor.animation.positionRequired') }]}
          >
            <InputNumber
              min={blendSpace ? blendSpace.config.minY : -1}
              max={blendSpace ? blendSpace.config.maxY : 1}
              step={0.01}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingNode ? t('editor.update') : t('editor.add')}
              </Button>
              <Button onClick={() => setIsAddingNode(false)}>
                {t('editor.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default BlendSpace2DEditor;
