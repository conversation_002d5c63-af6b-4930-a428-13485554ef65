# 物理系统

DL（Digital Learning）引擎编辑器的物理系统提供了强大的物理模拟功能，使您能够创建真实的物理交互、碰撞检测和动力学效果。本文档将详细介绍如何使用物理系统设置刚体、碰撞体、约束和软体物理，以及如何调整物理参数以获得理想的模拟效果。

## 物理系统概述

### 主要功能

DL（Digital Learning）引擎的物理系统支持以下主要功能：

- **刚体物理**：模拟刚性物体的运动和碰撞
- **软体物理**：模拟布料、绳索、气球等可变形物体
- **碰撞检测**：检测物体之间的碰撞和接触
- **物理约束**：创建铰链、弹簧、滑轮等物理连接
- **物理材质**：定义物体的摩擦、弹性等物理属性
- **射线检测**：检测射线与物体的交点
- **车辆物理**：模拟车辆的物理行为
- **破碎效果**：模拟物体的破碎和分裂

### 物理组件

物理系统主要包括以下组件：

- **刚体组件（Rigidbody Component）**：定义物体的物理属性和行为
- **碰撞体组件（Collider Component）**：定义物体的碰撞形状
- **物理材质组件（Physics Material Component）**：定义物体的物理材质属性
- **约束组件（Constraint Component）**：创建物体之间的物理连接
- **软体组件（Softbody Component）**：定义软体的物理属性和行为

## 刚体物理

### 添加刚体组件

1. 在场景中选择对象
2. 在属性面板中，点击"添加组件"按钮
3. 选择"物理 > 刚体"
4. 设置刚体属性

![刚体组件](../../assets/images/rigidbody-component.png)

### 刚体属性

刚体组件包含以下主要属性：

- **质量（Mass）**：物体的质量，影响物体受力时的加速度
- **阻力（Drag）**：线性运动阻力，模拟空气阻力
- **角阻力（Angular Drag）**：旋转运动阻力
- **使用重力（Use Gravity）**：是否受重力影响
- **是否运动学（Is Kinematic）**：是否由动画或脚本控制，而不受物理引擎影响
- **插值（Interpolation）**：物理更新之间的位置插值方式
- **碰撞检测模式（Collision Detection）**：碰撞检测的精度模式
- **约束（Constraints）**：限制物体在特定轴上的移动或旋转

### 刚体类型

DL（Digital Learning）引擎支持以下刚体类型：

- **动态（Dynamic）**：完全受物理引擎控制，受力、扭矩和碰撞影响
- **静态（Static）**：不移动，但其他物体可以与之碰撞
- **运动学（Kinematic）**：由动画或脚本控制，不受物理引擎影响，但可以影响其他动态物体

### 设置刚体类型

1. 选择带有刚体组件的对象
2. 在刚体组件属性中，找到"刚体类型"选项
3. 从下拉菜单中选择所需的类型
4. 根据选择的类型，调整相关参数

## 碰撞体

### 添加碰撞体组件

1. 在场景中选择对象
2. 在属性面板中，点击"添加组件"按钮
3. 选择"物理 > 碰撞体"下的特定碰撞体类型
4. 设置碰撞体属性

![碰撞体组件](../../assets/images/collider-component.png)

### 碰撞体类型

DL（Digital Learning）引擎支持以下碰撞体类型：

- **盒体碰撞体（Box Collider）**：盒形碰撞体，适用于方形或长方形物体
- **球体碰撞体（Sphere Collider）**：球形碰撞体，适用于球形物体
- **胶囊体碰撞体（Capsule Collider）**：胶囊形碰撞体，适用于角色等圆柱形物体
- **网格碰撞体（Mesh Collider）**：基于3D模型网格的碰撞体，适用于复杂形状
- **地形碰撞体（Terrain Collider）**：基于地形高度图的碰撞体
- **复合碰撞体（Compound Collider）**：由多个基本碰撞体组合而成的复杂碰撞体

### 碰撞体属性

碰撞体组件包含以下主要属性：

- **是否触发器（Is Trigger）**：是否作为触发器使用，触发器不产生物理碰撞，但可以检测进入/退出事件
- **物理材质（Physics Material）**：定义碰撞表面的物理属性
- **中心（Center）**：碰撞体相对于对象的位置偏移
- **尺寸（Size）/半径（Radius）**：碰撞体的尺寸参数
- **凸包（Convex）**：对于网格碰撞体，是否使用凸包近似

### 调整碰撞体形状

1. 选择带有碰撞体组件的对象
2. 在场景视图中，可以看到碰撞体的可视化轮廓
3. 使用变换工具调整碰撞体的位置、旋转和缩放
4. 或在属性面板中直接修改碰撞体的参数

## 物理材质

### 创建物理材质

1. 在资产面板中，点击"创建"按钮
2. 选择"物理 > 物理材质"
3. 输入材质名称
4. 点击"创建"按钮

![物理材质](../../assets/images/physics-material.png)

### 物理材质属性

物理材质包含以下主要属性：

- **动态摩擦力（Dynamic Friction）**：物体运动时的摩擦系数
- **静态摩擦力（Static Friction）**：物体静止时的摩擦系数
- **弹性（Bounciness）**：物体碰撞后反弹的程度
- **摩擦组合（Friction Combine）**：与其他物理材质摩擦力的组合方式
- **弹性组合（Bounce Combine）**：与其他物理材质弹性的组合方式

### 应用物理材质

1. 选择带有碰撞体组件的对象
2. 在碰撞体组件属性中，找到"物理材质"选项
3. 点击材质槽，选择要应用的物理材质
4. 或点击"新建"按钮创建并应用新的物理材质

## 物理约束

### 添加约束组件

1. 在场景中选择对象
2. 在属性面板中，点击"添加组件"按钮
3. 选择"物理 > 约束"下的特定约束类型
4. 设置约束属性和连接对象

![物理约束](../../assets/images/physics-constraint.png)

### 约束类型

DL（Digital Learning）引擎支持以下约束类型：

- **固定约束（Fixed Joint）**：将两个物体刚性连接在一起
- **铰链约束（Hinge Joint）**：创建一个铰链，允许物体绕一个轴旋转
- **弹簧约束（Spring Joint）**：使用弹簧连接两个物体
- **角色约束（Character Joint）**：用于角色骨骼的约束，限制旋转范围
- **可配置约束（Configurable Joint）**：高度可定制的约束，可以模拟各种连接类型
- **距离约束（Distance Joint）**：保持两个物体之间的距离
- **滑动约束（Slider Joint）**：允许物体沿一个轴滑动

### 约束属性

约束组件包含以下主要属性：

- **连接物体（Connected Body）**：要连接的另一个刚体
- **锚点（Anchor）**：约束在当前物体上的连接点
- **连接锚点（Connected Anchor）**：约束在连接物体上的连接点
- **轴（Axis）**：约束的主轴方向
- **弹簧（Spring）**：弹簧的刚度和阻尼参数
- **限制（Limits）**：运动或旋转的限制范围
- **断裂力（Break Force）**：使约束断裂的力的大小

### 设置铰链约束示例

1. 创建两个带有刚体和碰撞体的对象
2. 选择第一个对象
3. 添加"铰链约束"组件
4. 将"连接物体"设置为第二个对象
5. 设置"锚点"和"连接锚点"位置
6. 设置"轴"方向为旋转轴
7. 设置"限制"参数，如需要限制旋转范围
8. 设置"弹簧"参数，如需要添加弹性效果

## 软体物理

### 添加软体组件

1. 在场景中选择对象
2. 在属性面板中，点击"添加组件"按钮
3. 选择"物理 > 软体"
4. 设置软体类型和属性

![软体组件](../../assets/images/softbody-component.png)

### 软体类型

DL（Digital Learning）引擎支持以下软体类型：

- **布料（Cloth）**：模拟布料、旗帜、窗帘等
- **绳索（Rope）**：模拟绳子、链条等一维软体
- **体积软体（Volume）**：模拟果冻、橡胶等三维软体
- **气球（Balloon）**：模拟充气物体，具有内部压力
- **果冻（Jelly）**：模拟高弹性的体积软体

### 软体属性

软体组件包含以下主要属性：

- **质量（Mass）**：软体的总质量
- **刚度（Stiffness）**：软体的刚性程度
- **阻尼（Damping）**：软体的能量损失率
- **弹性（Elasticity）**：软体的弹性程度
- **压力（Pressure）**：对于气球类型，内部压力大小
- **撕裂阈值（Tearing Threshold）**：软体开始撕裂的应力阈值
- **固定点（Fixed Points）**：固定在空间中的点

### 创建布料示例

1. 创建一个平面对象
2. 添加"软体"组件
3. 将软体类型设置为"布料"
4. 设置网格分辨率（细分数量）
5. 设置质量、刚度和阻尼参数
6. 设置固定点（如布料的四个角）
7. 设置外部力（如重力、风力）
8. 点击"应用"按钮

## 射线检测

### 使用射线检测

射线检测用于检测从一点发射的射线与物理对象的交点，常用于实现射击、拾取、传感器等功能。

```javascript
// 创建射线
const rayOrigin = new Vector3(0, 1, 0);
const rayDirection = new Vector3(0, -1, 0);
const maxDistance = 10;
const layerMask = 1; // 物理层掩码

// 执行射线检测
const hitResult = PhysicsSystem.raycast(rayOrigin, rayDirection, maxDistance, layerMask);

// 处理检测结果
if (hitResult.hit) {
    console.log("射线击中物体:", hitResult.entity);
    console.log("击中点:", hitResult.point);
    console.log("表面法线:", hitResult.normal);
    console.log("距离:", hitResult.distance);
}
```

### 射线检测可视化

在编辑器中，可以可视化射线检测：

1. 选择带有射线检测组件的对象
2. 在属性面板中，启用"调试可视化"选项
3. 在场景视图中，可以看到射线和击中点的可视化效果

## 车辆物理

### 设置车辆物理

1. 创建车辆模型，包括车身和车轮
2. 为车身添加刚体和碰撞体组件
3. 添加"车辆"组件
4. 设置车轮对象和车轮碰撞体
5. 设置车辆参数：
   - **车轮悬挂**：悬挂距离、弹簧、阻尼
   - **车轮摩擦**：前进摩擦、侧向摩擦
   - **发动机**：最大扭矩、最大转速
   - **传动系统**：变速箱、差速器
6. 点击"应用"按钮

![车辆物理](../../assets/images/vehicle-physics.png)

### 控制车辆

```javascript
// 获取车辆组件
const vehicle = entity.getComponent('Vehicle');

// 设置油门和刹车
vehicle.setThrottle(0.8); // 80%油门
vehicle.setBrake(0.0);    // 不刹车

// 设置转向
vehicle.setSteer(-0.3);   // 向左转30%

// 设置手刹
vehicle.setHandbrake(false);

// 切换档位
vehicle.setGear(2);       // 二档
```

## 物理调试

### 启用物理调试可视化

1. 点击顶部菜单栏的"视图 > 物理调试"
2. 或使用快捷键Ctrl+F6（Windows）/Command+F6（Mac）
3. 在物理调试面板中，可以启用以下可视化选项：
   - **碰撞体**：显示碰撞体轮廓
   - **刚体**：显示刚体信息
   - **约束**：显示物理约束
   - **接触点**：显示物体之间的接触点
   - **力和扭矩**：显示作用于物体的力和扭矩

![物理调试](../../assets/images/physics-debug.png)

### 物理性能监控

1. 打开性能面板（"视图 > 性能面板"）
2. 切换到"物理"标签
3. 监控物理模拟的性能指标：
   - **物理更新时间**：物理模拟每帧的计算时间
   - **碰撞检测时间**：碰撞检测的计算时间
   - **约束求解时间**：约束求解的计算时间
   - **活动物体数量**：当前活动的物理对象数量
   - **接触点数量**：当前的物体接触点数量

## 物理设置

### 全局物理设置

1. 点击顶部菜单栏的"编辑 > 项目设置"
2. 在项目设置对话框中，选择"物理"选项卡
3. 设置以下全局物理参数：
   - **重力**：场景中的重力向量
   - **默认材质**：默认物理材质
   - **物理时间步长**：物理模拟的时间步长
   - **物理迭代次数**：求解器的迭代次数
   - **碰撞层**：定义物理碰撞层和层之间的交互

![物理设置](../../assets/images/physics-settings.png)

### 物理层设置

物理层用于控制哪些物体可以相互碰撞：

1. 在物理设置中，点击"编辑层"按钮
2. 在层矩阵中，勾选或取消勾选层之间的交互
3. 点击"应用"按钮保存设置

## 最佳实践

### 性能优化

- 使用简单的碰撞体（盒体、球体、胶囊体）而不是复杂的网格碰撞体
- 对于静态物体，使用静态碰撞体而不是刚体
- 使用物理层控制碰撞检测范围
- 对远处或不重要的物体使用简化的物理模拟
- 使用物理LOD（细节层次）系统
- 调整物理时间步长和迭代次数平衡精度和性能

### 稳定性提升

- 避免非常小或非常大的刚体
- 避免非常小或非常大的质量差异
- 使用连续碰撞检测处理高速移动的物体
- 适当增加约束的迭代次数
- 使用复合碰撞体而不是单个复杂碰撞体
- 避免物体堆叠过多

## 故障排除

### 物体穿透问题

如果物体相互穿透：

1. 检查碰撞体是否正确设置
2. 增加连续碰撞检测的精度
3. 减小物理时间步长
4. 增加物理迭代次数
5. 检查物体的相对速度是否过高

### 物体抖动问题

如果物体不稳定或抖动：

1. 增加刚体的质量或调整质量中心
2. 增加阻尼参数
3. 调整约束的弹簧和阻尼参数
4. 减小物理时间步长
5. 使用固定时间步长而不是可变时间步长

## 下一步

现在您已经了解了物理系统的基本功能，可以继续学习其他相关功能：

- [交互系统](./interaction-system.md)
- [动画系统](./animation-system.md)
- [视觉脚本](./visual-scripting.md)
- [物理高级技巧](../advanced/advanced-physics.md)
