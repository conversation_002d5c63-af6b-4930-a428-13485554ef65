/**
 * 材质系统示例
 * 展示如何使用材质系统创建、优化和转换材质
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { World } from '../../src/core/World';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Renderer } from '../../src/rendering/Renderer';
import { Camera } from '../../src/rendering/Camera';
import { Light, LightType } from '../../src/rendering/Light';
import { Transform } from '../../src/scene/Transform';
import { RenderSystem } from '../../src/rendering/RenderSystem';
import { MaterialSystem } from '../../src/rendering/materials/MaterialSystem';
import { MaterialType } from '../../src/rendering/materials/MaterialFactory';
import { DevicePerformanceLevel } from '../../src/utils/DeviceCapabilities';

/**
 * 材质系统示例类
 */
export class MaterialSystemExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 世界实例 */
  private world: World;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 相机实体 */
  private cameraEntity: Entity;
  
  /** 方向光实体 */
  private directionalLightEntity: Entity;
  
  /** 环境光实体 */
  private ambientLightEntity: Entity;
  
  /** 渲染系统 */
  private renderSystem: RenderSystem;
  
  /** 材质系统 */
  private materialSystem: MaterialSystem;
  
  /** 球体实体列表 */
  private sphereEntities: Entity[] = [];
  
  /** 立方体实体列表 */
  private boxEntities: Entity[] = [];
  
  /** 动画ID */
  private animationId: number = 0;
  
  /** 是否正在运行 */
  private running: boolean = false;
  
  /** 当前性能级别 */
  private currentPerformanceLevel: DevicePerformanceLevel = DevicePerformanceLevel.HIGH;
  
  /** 性能级别切换计时器 */
  private performanceLevelSwitchTimer: number = 0;
  
  /** 性能级别切换间隔（秒） */
  private performanceLevelSwitchInterval: number = 5;
  
  /**
   * 创建示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建世界
    this.world = new World();
    this.engine.setWorld(this.world);
    
    // 创建渲染器
    const renderer = new Renderer({
      width: window.innerWidth,
      height: window.innerHeight,
      antialias: true,
      shadows: true,
      shadowMapType: THREE.PCFSoftShadowMap
    });
    
    // 创建渲染系统
    this.renderSystem = new RenderSystem(renderer, {
      enableShadows: true,
      enablePostProcessing: false
    });
    this.world.addSystem(this.renderSystem);
    
    // 创建材质系统
    this.materialSystem = new MaterialSystem({
      autoOptimize: true,
      autoDowngrade: false,
      enableCache: true
    });
    this.world.addSystem(this.materialSystem);
    
    // 创建场景
    this.scene = new Scene('材质系统示例场景');
    this.world.addEntity(this.scene);
    
    // 设置活跃场景
    this.renderSystem.setActiveScene(this.scene);
    
    // 创建相机
    this.cameraEntity = new Entity('相机');
    this.cameraEntity.addComponent(new Camera({
      type: 'perspective',
      fov: 60,
      near: 0.1,
      far: 1000,
      aspect: window.innerWidth / window.innerHeight
    }));
    this.cameraEntity.addComponent(new Transform({
      position: { x: 0, y: 5, z: 15 },
      rotation: { x: -0.3, y: 0, z: 0 }
    }));
    this.world.addEntity(this.cameraEntity);
    this.scene.addEntity(this.cameraEntity);
    
    // 设置活跃相机
    this.renderSystem.setActiveCamera(this.cameraEntity.getComponent('Camera') as Camera);
    
    // 创建方向光
    this.directionalLightEntity = new Entity('方向光');
    this.directionalLightEntity.addComponent(new Light({
      type: LightType.DIRECTIONAL,
      color: 0xffffff,
      intensity: 1,
      castShadow: true,
      shadowMapSize: 2048,
      shadowCameraNear: 0.1,
      shadowCameraFar: 500,
      shadowCameraLeft: -50,
      shadowCameraRight: 50,
      shadowCameraTop: 50,
      shadowCameraBottom: -50,
      shadowBias: -0.0005
    }));
    this.directionalLightEntity.addComponent(new Transform({
      position: { x: 20, y: 30, z: 20 },
      rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 }
    }));
    this.world.addEntity(this.directionalLightEntity);
    this.scene.addEntity(this.directionalLightEntity);
    
    // 创建环境光
    this.ambientLightEntity = new Entity('环境光');
    this.ambientLightEntity.addComponent(new Light({
      type: LightType.AMBIENT,
      color: 0x404040,
      intensity: 0.5
    }));
    this.world.addEntity(this.ambientLightEntity);
    this.scene.addEntity(this.ambientLightEntity);
    
    // 创建场景内容
    this.createSceneContent();
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', this.handleResize.bind(this));
    
    // 添加DOM元素
    this.addDomElements();
  }
  
  /**
   * 创建场景内容
   */
  private createSceneContent(): void {
    // 创建地面
    const groundEntity = new Entity('地面');
    groundEntity.addComponent(new Transform({
      position: { x: 0, y: -0.5, z: 0 },
      scale: { x: 100, y: 1, z: 100 }
    }));
    
    // 创建地面网格
    const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
    const groundMaterial = this.materialSystem.createMaterial(MaterialType.STANDARD, {
      color: 0x808080,
      roughness: 0.7,
      metalness: 0.1
    });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.receiveShadow = true;
    
    // 添加到实体
    groundEntity.getComponent('Transform')?.getObject3D().add(groundMesh);
    
    // 添加到场景
    this.world.addEntity(groundEntity);
    this.scene.addEntity(groundEntity);
    
    // 创建不同材质类型的球体
    const materialTypes = [
      MaterialType.BASIC,
      MaterialType.LAMBERT,
      MaterialType.PHONG,
      MaterialType.STANDARD,
      MaterialType.PHYSICAL,
      MaterialType.TOON,
      MaterialType.MATCAP,
      MaterialType.NORMAL
    ];
    
    const rows = 2;
    const cols = 4;
    const spacing = 3;
    
    for (let i = 0; i < materialTypes.length; i++) {
      const row = Math.floor(i / cols);
      const col = i % cols;
      
      const x = (col - (cols - 1) / 2) * spacing;
      const z = (row - (rows - 1) / 2) * spacing;
      
      const sphereEntity = new Entity(`球体${i}`);
      sphereEntity.addComponent(new Transform({
        position: { x, y: 1, z },
        scale: { x: 1, y: 1, z: 1 }
      }));
      
      // 创建球体网格
      const sphereGeometry = new THREE.SphereGeometry(1, 32, 32);
      const materialType = materialTypes[i];
      const material = this.createMaterialByType(materialType);
      const sphereMesh = new THREE.Mesh(sphereGeometry, material);
      sphereMesh.castShadow = true;
      sphereMesh.receiveShadow = true;
      
      // 添加到实体
      sphereEntity.getComponent('Transform')?.getObject3D().add(sphereMesh);
      
      // 添加到场景
      this.world.addEntity(sphereEntity);
      this.scene.addEntity(sphereEntity);
      
      // 添加到列表
      this.sphereEntities.push(sphereEntity);
      
      // 创建材质类型标签
      this.createMaterialTypeLabel(materialType, x, z);
    }
  }
  
  /**
   * 创建材质类型标签
   * @param materialType 材质类型
   * @param x X坐标
   * @param z Z坐标
   */
  private createMaterialTypeLabel(materialType: string, x: number, z: number): void {
    const canvas = document.createElement('canvas');
    canvas.width = 256;
    canvas.height = 128;
    const context = canvas.getContext('2d');
    if (!context) return;
    
    context.fillStyle = 'rgba(0, 0, 0, 0.5)';
    context.fillRect(0, 0, canvas.width, canvas.height);
    context.font = 'bold 24px Arial';
    context.fillStyle = 'white';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillText(materialType, canvas.width / 2, canvas.height / 2);
    
    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      side: THREE.DoubleSide
    });
    
    const geometry = new THREE.PlaneGeometry(2, 1);
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.set(x, -0.2, z);
    mesh.rotation.x = -Math.PI / 2;
    
    this.scene.getThreeScene().add(mesh);
  }
  
  /**
   * 根据类型创建材质
   * @param materialType 材质类型
   * @returns 材质
   */
  private createMaterialByType(materialType: string): THREE.Material {
    const color = new THREE.Color(Math.random(), Math.random(), Math.random());
    
    switch (materialType) {
      case MaterialType.BASIC:
        return this.materialSystem.createMaterial(materialType, {
          color,
          wireframe: false
        });
      case MaterialType.LAMBERT:
        return this.materialSystem.createMaterial(materialType, {
          color,
          emissive: new THREE.Color(0x111111)
        });
      case MaterialType.PHONG:
        return this.materialSystem.createMaterial(materialType, {
          color,
          shininess: 30,
          specular: new THREE.Color(0x444444)
        });
      case MaterialType.STANDARD:
        return this.materialSystem.createMaterial(materialType, {
          color,
          roughness: 0.5,
          metalness: 0.5
        });
      case MaterialType.PHYSICAL:
        return this.materialSystem.createMaterial(materialType, {
          color,
          roughness: 0.5,
          metalness: 0.5,
          clearcoat: 0.3,
          clearcoatRoughness: 0.25
        });
      case MaterialType.TOON:
        return this.materialSystem.createMaterial(materialType, {
          color,
          gradientMap: this.createToonGradientTexture()
        });
      case MaterialType.MATCAP:
        return this.materialSystem.createMaterial(materialType, {
          color,
          matcap: this.createMatcapTexture()
        });
      case MaterialType.NORMAL:
        return this.materialSystem.createMaterial(materialType, {});
      default:
        return this.materialSystem.createMaterial(MaterialType.BASIC, {
          color
        });
    }
  }
  
  /**
   * 创建卡通渐变纹理
   * @returns 纹理
   */
  private createToonGradientTexture(): THREE.Texture {
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 1;
    const context = canvas.getContext('2d');
    if (!context) return new THREE.Texture();
    
    const gradient = context.createLinearGradient(0, 0, canvas.width, 0);
    gradient.addColorStop(0, '#000000');
    gradient.addColorStop(0.33, '#888888');
    gradient.addColorStop(0.67, '#cccccc');
    gradient.addColorStop(1, '#ffffff');
    context.fillStyle = gradient;
    context.fillRect(0, 0, canvas.width, canvas.height);
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    
    return texture;
  }
  
  /**
   * 创建Matcap纹理
   * @returns 纹理
   */
  private createMatcapTexture(): THREE.Texture {
    const canvas = document.createElement('canvas');
    canvas.width = 256;
    canvas.height = 256;
    const context = canvas.getContext('2d');
    if (!context) return new THREE.Texture();
    
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = canvas.width / 2;
    
    const gradient = context.createRadialGradient(
      centerX, centerY, 0,
      centerX, centerY, radius
    );
    gradient.addColorStop(0, '#ffffff');
    gradient.addColorStop(0.5, '#cccccc');
    gradient.addColorStop(1, '#000000');
    
    context.fillStyle = gradient;
    context.fillRect(0, 0, canvas.width, canvas.height);
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    
    return texture;
  }
  
  /**
   * 添加DOM元素
   */
  private addDomElements(): void {
    // 创建信息面板
    const infoPanel = document.createElement('div');
    infoPanel.style.position = 'absolute';
    infoPanel.style.top = '10px';
    infoPanel.style.left = '10px';
    infoPanel.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    infoPanel.style.color = 'white';
    infoPanel.style.padding = '10px';
    infoPanel.style.borderRadius = '5px';
    infoPanel.style.fontFamily = 'Arial, sans-serif';
    infoPanel.innerHTML = `
      <h2>材质系统示例</h2>
      <p>这个示例展示了如何使用材质系统创建、优化和转换材质。</p>
      <p>每隔5秒会自动切换性能级别，模拟在不同性能设备上的材质降级和升级。</p>
      <p>当前性能级别: <span id="performance-level">高</span></p>
    `;
    document.body.appendChild(infoPanel);
  }
  
  /**
   * 处理窗口大小变化
   */
  private handleResize(): void {
    // 更新相机宽高比
    const camera = this.cameraEntity.getComponent('Camera') as Camera;
    camera.setAspect(window.innerWidth / window.innerHeight);
  }
  
  /**
   * 更新场景
   * @param deltaTime 帧间隔时间（秒）
   */
  private update(deltaTime: number): void {
    // 旋转相机
    const cameraTransform = this.cameraEntity.getComponent('Transform');
    if (cameraTransform) {
      const rotation = cameraTransform.getRotation();
      rotation.y += deltaTime * 0.1;
      cameraTransform.setRotation(rotation.x, rotation.y, rotation.z);
    }
    
    // 更新性能级别切换计时器
    this.performanceLevelSwitchTimer += deltaTime;
    if (this.performanceLevelSwitchTimer >= this.performanceLevelSwitchInterval) {
      this.performanceLevelSwitchTimer = 0;
      this.switchPerformanceLevel();
    }
    
    // 更新世界
    this.world.update(deltaTime);
  }
  
  /**
   * 切换性能级别
   */
  private switchPerformanceLevel(): void {
    // 切换性能级别
    switch (this.currentPerformanceLevel) {
      case DevicePerformanceLevel.HIGH:
        this.currentPerformanceLevel = DevicePerformanceLevel.MEDIUM;
        this.downgradeMaterials();
        document.getElementById('performance-level')!.textContent = '中';
        break;
      case DevicePerformanceLevel.MEDIUM:
        this.currentPerformanceLevel = DevicePerformanceLevel.LOW;
        this.downgradeMaterials();
        document.getElementById('performance-level')!.textContent = '低';
        break;
      case DevicePerformanceLevel.LOW:
        this.currentPerformanceLevel = DevicePerformanceLevel.HIGH;
        this.upgradeMaterials();
        document.getElementById('performance-level')!.textContent = '高';
        break;
    }
  }
  
  /**
   * 降级材质
   */
  private downgradeMaterials(): void {
    // 遍历所有球体实体
    for (const entity of this.sphereEntities) {
      const transform = entity.getComponent('Transform');
      if (!transform) continue;
      
      const object3D = transform.getObject3D();
      object3D.traverse((object) => {
        if (object instanceof THREE.Mesh && object.material) {
          // 降级材质
          object.material = this.materialSystem.downgradeMaterial(object.material);
        }
      });
    }
  }
  
  /**
   * 升级材质
   */
  private upgradeMaterials(): void {
    // 遍历所有球体实体
    for (const entity of this.sphereEntities) {
      const transform = entity.getComponent('Transform');
      if (!transform) continue;
      
      const object3D = transform.getObject3D();
      object3D.traverse((object) => {
        if (object instanceof THREE.Mesh && object.material) {
          // 升级材质
          object.material = this.materialSystem.upgradeMaterial(object.material);
        }
      });
    }
  }
  
  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;
    
    const now = performance.now();
    const deltaTime = Math.min((now - (this._lastTime || now)) / 1000, 0.1);
    this._lastTime = now;
    
    this.update(deltaTime);
    
    this.animationId = requestAnimationFrame(this.animate.bind(this));
  }
  
  /** 上一帧时间 */
  private _lastTime: number = 0;
  
  /**
   * 启动示例
   */
  public start(): void {
    if (this.running) return;
    
    this.running = true;
    this._lastTime = performance.now();
    this.animate();
  }
  
  /**
   * 停止示例
   */
  public stop(): void {
    if (!this.running) return;
    
    this.running = false;
    cancelAnimationFrame(this.animationId);
  }
  
  /**
   * 销毁示例
   */
  public dispose(): void {
    this.stop();
    
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize.bind(this));
    
    // 销毁引擎
    this.engine.dispose();
  }
}
