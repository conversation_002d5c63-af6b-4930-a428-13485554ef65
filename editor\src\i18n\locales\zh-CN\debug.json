{"debug": {"title": "调试工具", "description": "高级调试和性能分析工具", "debugView": "调试视图", "tabs": {"performance": "性能", "performanceComparison": "性能对比", "performanceTest": "性能测试", "memory": "内存", "rendering": "渲染", "optimization": "优化", "performanceOptimization": "性能优化", "resourceOptimization": "资源优化", "uiOptimization": "UI优化"}, "performance": {"title": "性能分析", "description": "分析和监控引擎性能", "startMonitoring": "开始监控", "stopMonitoring": "停止监控", "reset": "重置", "sampleInterval": "采样间隔", "historyLength": "历史长度", "noData": "暂无性能数据", "fps": "帧率", "memory": "内存使用", "renderTime": "渲染时间", "fpsChart": "帧率图表", "memoryChart": "内存使用图表", "renderTimeChart": "渲染时间图表", "overview": "概览", "details": "详情", "statistics": "统计", "warnings": "警告", "cpuUsage": "CPU使用率", "gpuUsage": "GPU使用率", "drawCalls": "绘制调用", "triangles": "三角形数量", "vertices": "顶点数量", "textures": "纹理数量", "entities": "实体数量", "components": "组件数量", "systems": "系统数量", "analyzing": "分析中..."}, "memory": {"title": "内存分析", "description": "分析内存使用情况", "refresh": "刷新", "overview": "概览", "total": "总内存", "textures": "纹理内存", "geometries": "几何体内存", "js": "JavaScript内存", "distribution": "内存分布", "usage": "内存使用", "details": "详细信息", "name": "名称", "size": "大小", "count": "数量", "type": "类型", "warning": "内存使用过高", "warningDescription": "内存使用超过阈值，可能导致性能问题"}, "rendering": {"title": "渲染分析", "description": "分析渲染性能和统计", "refresh": "刷新", "overview": "概览", "drawCalls": "绘制调用", "triangles": "三角形数量", "vertices": "顶点数量", "renderTime": "渲染时间", "performance": "性能", "details": "详细信息", "bottlenecks": "瓶颈", "name": "名称", "visible": "可见", "yes": "是", "no": "否", "highDrawCalls": "绘制调用过多 ({count})", "highDrawCallsTip": "考虑使用批处理或合并网格来减少绘制调用", "highRenderTime": "渲染时间过长 ({time}ms)", "highRenderTimeTip": "考虑优化着色器或减少后处理效果", "highTriangles": "三角形数量过多 ({count}M)", "highTrianglesTip": "考虑使用LOD或简化模型来减少三角形数量", "noBottlenecks": "未检测到明显的渲染瓶颈"}, "optimization": {"title": "场景优化", "description": "分析场景并提供优化建议", "analyze": "分析场景", "analyzing": "分析中...", "overallScore": "总体得分", "scoreGood": "场景性能良好", "scoreWarning": "场景性能一般，有优化空间", "scoreError": "场景性能较差，需要优化", "recommendations": "优化建议", "tips": "优化提示", "low": "低", "medium": "中", "high": "高", "drawCalls": "绘制调用", "drawCallsDesc": "绘制调用是GPU渲染场景的指令数量，过多的绘制调用会导致CPU瓶颈", "drawCallsTip1": "使用材质实例化减少材质切换", "drawCallsTip2": "合并静态网格减少绘制调用", "drawCallsTip3": "使用GPU实例化渲染重复对象", "triangles": "三角形数量", "trianglesDesc": "三角形数量直接影响GPU渲染性能，过多的三角形会导致GPU瓶颈", "trianglesTip1": "使用LOD（细节层次）技术减少远处物体的三角形数量", "trianglesTip2": "简化复杂模型，移除不可见的三角形", "trianglesTip3": "使用法线贴图代替高模细节", "textures": "纹理优化", "texturesDesc": "纹理大小和数量会影响内存使用和加载时间", "texturesTip1": "压缩纹理减少内存使用", "texturesTip2": "使用纹理图集合并多个小纹理", "texturesTip3": "为远处物体使用较小的纹理", "lighting": "光照优化", "lightingDesc": "复杂的光照计算会显著影响性能", "lightingTip1": "减少动态光源数量", "lightingTip2": "使用烘焙光照代替实时光照", "lightingTip3": "优化阴影设置，减少阴影分辨率或距离", "memory": "内存优化", "memoryDesc": "过高的内存使用会导致性能下降甚至崩溃", "memoryTip1": "释放未使用的资源", "memoryTip2": "使用资源池复用对象", "memoryTip3": "延迟加载不立即需要的资源"}, "performanceOptimization": {"title": "性能优化", "startMonitoring": "开始监控", "stopMonitoring": "停止监控", "optimize": "优化", "autoOptimize": "自动优化", "optimizationStarted": "优化开始", "optimizationCompleted": "优化完成", "optimizationFailed": "优化失败", "overview": "概览", "charts": "图表", "optimizationResults": "优化结果", "settings": "设置", "fps": "帧率", "memory": "内存使用", "renderTime": "渲染时间", "fpsChart": "帧率图表", "memoryChart": "内存使用图表", "renderTimeChart": "渲染时间图表", "noWarnings": "没有性能警告", "current": "当前", "threshold": "阈值", "generalSettings": "常规设置", "warningThresholds": "警告阈值", "optimizationSettings": "优化设置", "monitoringInterval": "监控间隔", "historyLength": "历史长度", "enableWarnings": "启用警告", "autoOptimizeInterval": "自动优化间隔", "seconds": "秒", "samples": "样本", "fpsWarningThreshold": "帧率警告阈值", "memoryWarningThreshold": "内存警告阈值", "renderTimeWarningThreshold": "渲染时间警告阈值", "enableRenderingOptimization": "启用渲染优化", "enableMemoryOptimization": "启用内存优化", "enableUIOptimization": "启用UI优化", "enableResourceOptimization": "启用资源优化", "lodGenerated": "生成的LOD数量", "materialOptimized": "优化的材质数量", "batchesCreated": "创建的批处理数量", "instancesCreated": "创建的实例数量", "cullingEnabled": "启用剔除", "texturesOptimized": "优化的纹理数量", "geometriesOptimized": "优化的几何体数量", "resourcesReleased": "释放的资源数量", "componentsOptimized": "优化的组件数量", "eventListenersOptimized": "优化的事件监听器数量", "resourcesOptimized": "优化的资源数量", "loadingStrategyUpdated": "更新的加载策略"}, "resource": {"title": "资源优化", "refresh": "刷新", "unloadSelected": "卸载选中", "cleanupCache": "清理缓存", "optimizeResources": "优化资源", "searchPlaceholder": "搜索资源...", "noResourceSelected": "未选择资源", "unloadedResources": "已卸载资源", "unloadFailed": "卸载失败", "cacheCleanupSuccess": "缓存清理成功", "cacheCleanupFailed": "缓存清理失败", "settingsApplied": "设置已应用", "settingsApplyFailed": "设置应用失败", "optimizationSuccess": "资源优化成功", "optimizationFailed": "资源优化失败", "name": "名称", "type": "类型", "size": "大小", "state": "状态", "priority": "优先级", "lastAccessed": "最后访问时间", "refCount": "引用计数", "texture": "纹理", "model": "模型", "audio": "音频", "material": "材质", "shader": "着色器", "other": "其他", "loaded": "已加载", "loading": "加载中", "unloaded": "未加载", "error": "错误", "unknown": "未知", "highPriority": "高优先级", "mediumPriority": "中优先级", "lowPriority": "低优先级", "unknownPriority": "未知优先级", "memoryUsage": "内存使用", "totalMemory": "总内存", "resourceList": "资源列表", "settings": "设置", "optimizationSettings": "优化设置", "cacheSettings": "缓存设置", "loadingSettings": "加载设置", "maxCacheSize": "最大缓存大小", "cleanupThreshold": "清理阈值", "cleanupInterval": "清理间隔", "maxConcurrentLoads": "最大并发加载数", "preloadDistance": "预加载距离", "unloadDistance": "卸载距离", "priorityBasedLoading": "基于优先级的加载", "compressTextures": "压缩纹理", "useTextureAtlas": "使用纹理图集", "useGeometryInstancing": "使用几何体实例化", "useMaterialInstancing": "使用材质实例化", "applySettings": "应用设置", "resources": "资源", "units": "单位"}, "ui": {"title": "UI优化", "refresh": "刷新", "optimizeSelected": "优化选中", "optimizeAll": "优化全部", "searchPlaceholder": "搜索组件...", "noComponentSelected": "未选择组件", "optimizationSuccess": "UI优化成功", "optimizationFailed": "UI优化失败", "settingsApplied": "设置已应用", "settingsApplyFailed": "设置应用失败", "name": "名称", "type": "类型", "renderTime": "渲染时间", "updateTime": "更新时间", "eventCount": "事件数量", "visible": "可见", "children": "子元素", "depth": "深度", "memoryUsage": "内存使用", "panel": "面板", "button": "按钮", "text": "文本", "image": "图像", "input": "输入框", "list": "列表", "table": "表格", "chart": "图表", "other": "其他", "components": "组件", "performance": "性能", "optimization": "优化", "settings": "设置", "totalRenderTime": "总渲染时间", "averageRenderTime": "平均渲染时间", "totalComponents": "组件总数", "eventListeners": "事件监听器", "renderTimeChart": "渲染时间图表", "componentsChart": "组件数量图表", "noOptimizationResults": "暂无优化结果", "optimizationResults": "优化结果", "optimizedComponents": "优化的组件数量", "renderTimeReduction": "渲染时间减少", "batchedComponents": "批处理的组件数量", "memoizedComponents": "记忆化的组件数量", "virtualizedComponents": "虚拟化的组件数量", "lazyLoadedComponents": "懒加载的组件数量", "culledComponents": "剔除的组件数量", "delegatedEvents": "委托的事件数量", "removedEventListeners": "移除的事件监听器数量", "memoryReduction": "内存减少", "renderingSettings": "渲染设置", "eventSettings": "事件设置", "advancedSettings": "高级设置", "enableBatchRendering": "启用批处理渲染", "enableBatchRenderingDesc": "将多个UI组件合并为一个绘制调用", "enableMemoization": "启用记忆化", "enableMemoizationDesc": "缓存组件渲染结果，避免不必要的重新渲染", "batchSize": "批处理大小", "updateInterval": "更新间隔", "enableEventDelegation": "启用事件委托", "enableEventDelegationDesc": "将事件监听器附加到父元素，减少事件监听器数量", "maxEventListeners": "最大事件监听器数量", "enableVirtualization": "启用虚拟化", "enableVirtualizationDesc": "只渲染可见区域内的组件", "enableLazyLoading": "启用懒加载", "enableLazyLoadingDesc": "延迟加载不立即可见的组件", "enableCulling": "启用剔除", "enableCullingDesc": "不渲染视口外的组件", "cullingDistance": "剔除距离", "useRequestAnimationFrame": "使用requestAnimationFrame", "useWebGL": "使用WebGL", "useWebWorkers": "使用Web Workers", "useOffscreenCanvas": "使用离屏Canvas", "units": "单位"}, "comparison": {"title": "性能对比", "description": "比较不同性能快照之间的差异", "selectBaseSnapshot": "选择基准快照", "selectCompareSnapshot": "选择比较快照", "compare": "比较", "createSnapshot": "创建快照", "noComparisonData": "暂无对比数据", "scoreChange": "性能评分变化", "baseScore": "基准评分", "compareScore": "比较评分", "keyMetrics": "关键指标", "before": "优化前", "after": "优化后", "change": "变化", "percentChange": "百分比变化", "bottleneckChanges": "瓶颈变化", "resolvedBottlenecks": "已解决的瓶颈", "newBottlenecks": "新增的瓶颈", "changedBottlenecks": "变化的瓶颈", "noResolvedBottlenecks": "没有解决的瓶颈", "noNewBottlenecks": "没有新增的瓶颈", "noChangedBottlenecks": "没有变化的瓶颈", "metric": "指标", "renderingMetrics": "渲染指标", "memoryMetrics": "内存指标", "systemMetrics": "系统指标", "physicsMetrics": "物理指标", "networkMetrics": "网络指标", "otherMetrics": "其他指标", "keyMetricsComparison": "关键指标对比", "name": "名称", "date": "日期", "tags": "标签", "actions": "操作", "snapshots": "快照管理", "snapshotName": "快照名称", "snapshotDescription": "快照描述", "snapshotTags": "快照标签", "tagsHint": "使用逗号分隔多个标签", "nameRequired": "请输入快照名称", "confirmDelete": "确认删除", "deleteWarning": "确定要删除此快照吗？此操作无法撤销。", "overview": "概览", "metrics": "指标", "charts": "图表"}, "test": {"title": "性能测试", "description": "描述", "selectTest": "选择测试", "runTest": "运行测试", "stopTest": "停止测试", "newTest": "新建测试", "noTests": "暂无测试配置", "createTest": "创建测试", "noResults": "暂无测试结果", "exportResults": "导出结果", "saveAsSnapshot": "保存为快照", "name": "名称", "scenes": "场景", "repetitions": "重复次数", "actions": "操作", "testName": "测试名称", "testDescription": "测试描述", "sceneName": "场景名称", "scenePath": "场景路径", "duration": "持续时间", "warmupDuration": "预热时间", "nameRequired": "请输入测试名称", "sceneNameRequired": "请输入场景名称", "scenePathRequired": "请输入场景路径", "durationRequired": "请输入持续时间", "warmupDurationRequired": "请输入预热时间", "repetitionsRequired": "请输入重复次数", "addScene": "添加场景", "removeScene": "移除场景", "editTest": "编辑测试", "confirmDelete": "确认删除", "deleteWarning": "确定要删除此测试配置吗？此操作无法撤销。", "exportFormat": "导出格式", "totalDuration": "总持续时间", "scenesTested": "测试场景数", "sceneResults": "场景测试结果", "noBottlenecks": "未检测到性能瓶颈", "snapshotCreated": "快照已创建", "snapshotCreatedMessage": "性能测试结果已成功保存为快照", "predefinedTests": "预定义测试", "results": "测试结果", "autoSaveReport": "自动保存报告"}}}