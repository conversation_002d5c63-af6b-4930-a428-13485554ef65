# EngineService API文档

## 概述

`EngineService`是编辑器与引擎交互的核心服务，负责初始化引擎、加载场景、管理实体等。它是编辑器与底层引擎之间的桥梁，提供了一系列方法来操作引擎。

## 导入

```typescript
import { EngineService } from '../services/EngineService';
```

## 实例化

`EngineService`是一个单例服务，可以通过以下方式获取实例：

```typescript
// 获取EngineService实例
const engineService = EngineService.getInstance();
```

## 属性

| 属性名 | 类型 | 描述 |
|-------|------|------|
| engine | Engine | 引擎实例 |
| activeScene | Scene | 当前活动场景 |
| activeCamera | Camera | 当前活动相机 |
| selectedEntities | Entity[] | 当前选中的实体列表 |
| isRunning | boolean | 引擎是否正在运行 |

## 方法

### 初始化引擎

```typescript
/**
 * 初始化引擎
 * @param canvas 画布元素
 * @param options 引擎选项
 */
public async initialize(canvas: HTMLCanvasElement, options: Partial<EngineOptions> = {}): Promise<void>
```

**参数：**

- `canvas`：HTMLCanvasElement - 用于渲染的画布元素
- `options`：Partial<EngineOptions> - 引擎初始化选项（可选）

**返回值：**

- Promise<void> - 初始化完成的Promise

**示例：**

```typescript
// 获取画布元素
const canvas = document.getElementById('viewport-canvas') as HTMLCanvasElement;

// 初始化引擎
await engineService.initialize(canvas, {
  autoStart: false,
  antialias: true,
  shadows: true,
  physicsEnabled: true,
});
```

### 启动引擎

```typescript
/**
 * 启动引擎
 */
public start(): void
```

**示例：**

```typescript
// 启动引擎
engineService.start();
```

### 停止引擎

```typescript
/**
 * 停止引擎
 */
public stop(): void
```

**示例：**

```typescript
// 停止引擎
engineService.stop();
```

### 加载场景

```typescript
/**
 * 加载场景
 * @param sceneId 场景ID
 */
public async loadScene(sceneId: string): Promise<Scene>
```

**参数：**

- `sceneId`：string - 要加载的场景ID

**返回值：**

- Promise<Scene> - 加载完成的场景对象

**示例：**

```typescript
// 加载场景
const scene = await engineService.loadScene('scene-123');
```

### 保存场景

```typescript
/**
 * 保存场景
 * @param scene 场景对象
 */
public async saveScene(scene: Scene): Promise<void>
```

**参数：**

- `scene`：Scene - 要保存的场景对象

**返回值：**

- Promise<void> - 保存完成的Promise

**示例：**

```typescript
// 保存当前场景
await engineService.saveScene(engineService.activeScene);
```

### 创建实体

```typescript
/**
 * 创建实体
 * @param options 实体选项
 */
public createEntity(options?: EntityOptions): Entity
```

**参数：**

- `options`：EntityOptions - 实体创建选项（可选）

**返回值：**

- Entity - 创建的实体对象

**示例：**

```typescript
// 创建一个实体
const entity = engineService.createEntity({
  name: '新实体',
  position: { x: 0, y: 0, z: 0 },
  rotation: { x: 0, y: 0, z: 0 },
  scale: { x: 1, y: 1, z: 1 },
});
```

### 删除实体

```typescript
/**
 * 删除实体
 * @param entity 实体对象或ID
 */
public deleteEntity(entity: Entity | string): void
```

**参数：**

- `entity`：Entity | string - 要删除的实体对象或ID

**示例：**

```typescript
// 删除一个实体
engineService.deleteEntity(entity);

// 或者通过ID删除
engineService.deleteEntity('entity-123');
```

### 选择实体

```typescript
/**
 * 选择实体
 * @param entity 实体对象或ID
 */
public selectEntity(entity: Entity | string): void
```

**参数：**

- `entity`：Entity | string - 要选择的实体对象或ID

**示例：**

```typescript
// 选择一个实体
engineService.selectEntity(entity);

// 或者通过ID选择
engineService.selectEntity('entity-123');
```

### 获取选中的实体

```typescript
/**
 * 获取选中的实体
 */
public getSelectedEntities(): Entity[]
```

**返回值：**

- Entity[] - 当前选中的实体列表

**示例：**

```typescript
// 获取选中的实体
const selectedEntities = engineService.getSelectedEntities();
```

### 添加组件到实体

```typescript
/**
 * 添加组件到实体
 * @param entity 实体对象或ID
 * @param componentType 组件类型
 * @param options 组件选项
 */
public addComponent(entity: Entity | string, componentType: string, options?: any): Component
```

**参数：**

- `entity`：Entity | string - 目标实体对象或ID
- `componentType`：string - 要添加的组件类型
- `options`：any - 组件初始化选项（可选）

**返回值：**

- Component - 添加的组件对象

**示例：**

```typescript
// 添加一个变换组件
const transformComponent = engineService.addComponent(entity, 'TransformComponent', {
  position: { x: 0, y: 0, z: 0 },
  rotation: { x: 0, y: 0, z: 0 },
  scale: { x: 1, y: 1, z: 1 },
});

// 添加一个模型组件
const modelComponent = engineService.addComponent(entity, 'ModelComponent', {
  url: 'models/cube.glb',
  castShadow: true,
  receiveShadow: true,
});
```

### 移除实体的组件

```typescript
/**
 * 移除实体的组件
 * @param entity 实体对象或ID
 * @param componentType 组件类型
 */
public removeComponent(entity: Entity | string, componentType: string): void
```

**参数：**

- `entity`：Entity | string - 目标实体对象或ID
- `componentType`：string - 要移除的组件类型

**示例：**

```typescript
// 移除一个组件
engineService.removeComponent(entity, 'ModelComponent');
```

### 获取实体的组件

```typescript
/**
 * 获取实体的组件
 * @param entity 实体对象或ID
 * @param componentType 组件类型
 */
public getComponent(entity: Entity | string, componentType: string): Component | null
```

**参数：**

- `entity`：Entity | string - 目标实体对象或ID
- `componentType`：string - 要获取的组件类型

**返回值：**

- Component | null - 获取到的组件对象，如果不存在则返回null

**示例：**

```typescript
// 获取一个组件
const transformComponent = engineService.getComponent(entity, 'TransformComponent');
if (transformComponent) {
  // 使用组件
  transformComponent.position.set(0, 1, 0);
}
```

### 调用引擎方法

```typescript
/**
 * 调用引擎方法
 * @param method 方法名称
 * @param args 参数
 */
public async callEngineMethod(method: string, ...args: any[]): Promise<any>
```

**参数：**

- `method`：string - 要调用的引擎方法名称
- `args`：any[] - 方法参数

**返回值：**

- Promise<any> - 方法调用结果

**示例：**

```typescript
// 调用引擎方法
const result = await engineService.callEngineMethod('raycast', {
  origin: { x: 0, y: 0, z: 0 },
  direction: { x: 0, y: -1, z: 0 },
  maxDistance: 100,
});
```

## 事件

`EngineService`使用事件系统来通知状态变化。

### 事件类型

```typescript
enum EngineEventType {
  INITIALIZED = 'engine:initialized',
  STARTED = 'engine:started',
  STOPPED = 'engine:stopped',
  SCENE_LOADED = 'engine:scene:loaded',
  SCENE_UNLOADED = 'engine:scene:unloaded',
  ENTITY_CREATED = 'engine:entity:created',
  ENTITY_DELETED = 'engine:entity:deleted',
  ENTITY_SELECTED = 'engine:entity:selected',
  ENTITY_DESELECTED = 'engine:entity:deselected',
  COMPONENT_ADDED = 'engine:component:added',
  COMPONENT_REMOVED = 'engine:component:removed',
  COMPONENT_CHANGED = 'engine:component:changed',
}
```

### 监听事件

```typescript
/**
 * 监听事件
 * @param event 事件类型
 * @param callback 回调函数
 */
public on(event: EngineEventType, callback: (data: any) => void): void
```

**示例：**

```typescript
// 监听实体选择事件
engineService.on(EngineEventType.ENTITY_SELECTED, (entity) => {
  console.log('实体被选中:', entity);
});
```

### 移除事件监听

```typescript
/**
 * 移除事件监听
 * @param event 事件类型
 * @param callback 回调函数
 */
public off(event: EngineEventType, callback: (data: any) => void): void
```

**示例：**

```typescript
// 移除事件监听
engineService.off(EngineEventType.ENTITY_SELECTED, callback);
```

## 销毁

```typescript
/**
 * 销毁引擎服务
 */
public dispose(): void
```

**示例：**

```typescript
// 销毁引擎服务
engineService.dispose();
```
