"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Transform = void 0;
/**
 * 变换组件
 * 处理实体的位置、旋转和缩放
 */
var THREE = require("three");
var Component_1 = require("../core/Component");
var Transform = exports.Transform = /** @class */ (function (_super) {
    __extends(Transform, _super);
    /**
     * 创建变换组件
     */
    function Transform() {
        var _this = _super.call(this, Transform.type) || this;
        /** 本地位置 */
        _this.localPosition = new THREE.Vector3();
        /** 本地旋转 */
        _this.localRotation = new THREE.Euler();
        /** 本地旋转四元数 */
        _this.localQuaternion = new THREE.Quaternion();
        /** 本地缩放 */
        _this.localScale = new THREE.Vector3(1, 1, 1);
        /** 世界位置 */
        _this.worldPosition = new THREE.Vector3();
        /** 世界旋转 */
        _this.worldRotation = new THREE.Euler();
        /** 世界旋转四元数 */
        _this.worldQuaternion = new THREE.Quaternion();
        /** 世界缩放 */
        _this.worldScale = new THREE.Vector3(1, 1, 1);
        /** 父变换 */
        _this.parent = null;
        /** 子变换列表 */
        _this.children = [];
        /** 是否需要更新 */
        _this.dirty = true;
        _this.object3D = new THREE.Object3D();
        return _this;
    }
    /**
     * 当组件附加到实体时调用
     */
    Transform.prototype.onAttach = function () {
        // 设置对象名称
        if (this.entity) {
            this.object3D.name = this.entity.name;
        }
    };
    /**
     * 设置本地位置
     * @param x X坐标或位置向量
     * @param y Y坐标
     * @param z Z坐标
     */
    Transform.prototype.setPosition = function (x, y, z) {
        if (x instanceof THREE.Vector3) {
            this.localPosition.copy(x);
        }
        else {
            this.localPosition.set(x, y || 0, z || 0);
        }
        this.object3D.position.copy(this.localPosition);
        this.dirty = true;
        // 发出位置变更事件
        this.emit('positionChanged', this.localPosition);
    };
    /**
     * 获取本地位置
     * @returns 本地位置
     */
    Transform.prototype.getPosition = function () {
        return this.localPosition.clone();
    };
    /**
     * 设置世界位置
     * @param x X坐标或位置向量
     * @param y Y坐标
     * @param z Z坐标
     */
    Transform.prototype.setWorldPosition = function (x, y, z) {
        if (x instanceof THREE.Vector3) {
            this.worldPosition.copy(x);
        }
        else {
            this.worldPosition.set(x, y || 0, z || 0);
        }
        // 如果有父变换，转换为本地位置
        if (this.parent) {
            var parentWorldMatrix = this.parent.getWorldMatrix();
            var parentWorldMatrixInverse = new THREE.Matrix4().copy(parentWorldMatrix).invert();
            var localPosition = this.worldPosition.clone()
                .applyMatrix4(parentWorldMatrixInverse);
            this.setPosition(localPosition);
        }
        else {
            this.setPosition(this.worldPosition);
        }
    };
    /**
     * 获取世界位置
     * @returns 世界位置
     */
    Transform.prototype.getWorldPosition = function () {
        this.updateWorldMatrix();
        return this.worldPosition.clone();
    };
    /**
     * 设置本地旋转
     * @param x X轴旋转角度（弧度）或欧拉角
     * @param y Y轴旋转角度（弧度）
     * @param z Z轴旋转角度（弧度）
     * @param order 旋转顺序
     */
    Transform.prototype.setRotation = function (x, y, z, order) {
        if (x instanceof THREE.Euler) {
            this.localRotation.copy(x);
        }
        else {
            this.localRotation.set(x, y || 0, z || 0, order || this.localRotation.order);
        }
        // 更新四元数
        this.localQuaternion.setFromEuler(this.localRotation);
        this.object3D.rotation.copy(this.localRotation);
        this.dirty = true;
        // 发出旋转变更事件
        this.emit('rotationChanged', this.localRotation);
    };
    /**
     * 设置本地旋转四元数
     * @param x X分量或四元数
     * @param y Y分量
     * @param z Z分量
     * @param w W分量
     */
    Transform.prototype.setRotationQuaternion = function (x, y, z, w) {
        if (x instanceof THREE.Quaternion) {
            this.localQuaternion.copy(x);
        }
        else {
            this.localQuaternion.set(x, y || 0, z || 0, w || 1);
        }
        // 更新欧拉角
        this.localRotation.setFromQuaternion(this.localQuaternion);
        this.object3D.quaternion.copy(this.localQuaternion);
        this.dirty = true;
        // 发出旋转变更事件
        this.emit('rotationChanged', this.localRotation);
    };
    /**
     * 获取本地旋转四元数
     * @returns 本地旋转四元数
     */
    Transform.prototype.getRotationQuaternion = function () {
        return this.localQuaternion.clone();
    };
    /**
     * 获取本地旋转
     * @returns 本地旋转
     */
    Transform.prototype.getRotation = function () {
        return this.localRotation.clone();
    };
    /**
     * 设置世界旋转
     * @param x X轴旋转角度（弧度）或欧拉角
     * @param y Y轴旋转角度（弧度）
     * @param z Z轴旋转角度（弧度）
     * @param order 旋转顺序
     */
    Transform.prototype.setWorldRotation = function (x, y, z, order) {
        if (x instanceof THREE.Euler) {
            this.worldRotation.copy(x);
        }
        else {
            this.worldRotation.set(x, y || 0, z || 0, order || this.worldRotation.order);
        }
        // 更新世界四元数
        this.worldQuaternion.setFromEuler(this.worldRotation);
        // 如果有父变换，转换为本地旋转
        if (this.parent) {
            var parentWorldQuaternion = new THREE.Quaternion();
            var parentWorldMatrix = this.parent.getWorldMatrix();
            var parentRotationMatrix = new THREE.Matrix4().extractRotation(parentWorldMatrix);
            parentWorldQuaternion.setFromRotationMatrix(parentRotationMatrix);
            var worldQuaternion = new THREE.Quaternion().setFromEuler(this.worldRotation);
            var localQuaternion = worldQuaternion.clone().premultiply(parentWorldQuaternion.invert());
            var localRotation = new THREE.Euler().setFromQuaternion(localQuaternion);
            this.setRotation(localRotation);
        }
        else {
            this.setRotation(this.worldRotation);
        }
    };
    /**
     * 设置世界旋转四元数
     * @param x X分量或四元数
     * @param y Y分量
     * @param z Z分量
     * @param w W分量
     */
    Transform.prototype.setWorldRotationQuaternion = function (x, y, z, w) {
        if (x instanceof THREE.Quaternion) {
            this.worldQuaternion.copy(x);
        }
        else {
            this.worldQuaternion.set(x, y || 0, z || 0, w || 1);
        }
        // 更新世界欧拉角
        this.worldRotation.setFromQuaternion(this.worldQuaternion);
        // 如果有父变换，转换为本地旋转
        if (this.parent) {
            var parentWorldQuaternion = new THREE.Quaternion();
            var parentWorldMatrix = this.parent.getWorldMatrix();
            var parentRotationMatrix = new THREE.Matrix4().extractRotation(parentWorldMatrix);
            parentWorldQuaternion.setFromRotationMatrix(parentRotationMatrix);
            var localQuaternion = this.worldQuaternion.clone().premultiply(parentWorldQuaternion.invert());
            this.setRotationQuaternion(localQuaternion);
        }
        else {
            this.setRotationQuaternion(this.worldQuaternion);
        }
    };
    /**
     * 获取世界旋转四元数
     * @returns 世界旋转四元数
     */
    Transform.prototype.getWorldRotationQuaternion = function () {
        this.updateWorldMatrix();
        return this.worldQuaternion.clone();
    };
    /**
     * 获取世界旋转
     * @returns 世界旋转
     */
    Transform.prototype.getWorldRotation = function () {
        this.updateWorldMatrix();
        return this.worldRotation.clone();
    };
    /**
     * 设置本地缩放
     * @param x X轴缩放或缩放向量
     * @param y Y轴缩放
     * @param z Z轴缩放
     */
    Transform.prototype.setScale = function (x, y, z) {
        if (x instanceof THREE.Vector3) {
            this.localScale.copy(x);
        }
        else {
            this.localScale.set(x, y || x, z || x);
        }
        this.object3D.scale.copy(this.localScale);
        this.dirty = true;
        // 发出缩放变更事件
        this.emit('scaleChanged', this.localScale);
    };
    /**
     * 获取本地缩放
     * @returns 本地缩放
     */
    Transform.prototype.getScale = function () {
        return this.localScale.clone();
    };
    /**
     * 设置世界缩放
     * @param x X轴缩放或缩放向量
     * @param y Y轴缩放
     * @param z Z轴缩放
     */
    Transform.prototype.setWorldScale = function (x, y, z) {
        if (x instanceof THREE.Vector3) {
            this.worldScale.copy(x);
        }
        else {
            this.worldScale.set(x, y || x, z || x);
        }
        // 如果有父变换，转换为本地缩放
        if (this.parent) {
            var parentWorldScale = this.parent.getWorldScale();
            var localScale = new THREE.Vector3(this.worldScale.x / parentWorldScale.x, this.worldScale.y / parentWorldScale.y, this.worldScale.z / parentWorldScale.z);
            this.setScale(localScale);
        }
        else {
            this.setScale(this.worldScale);
        }
    };
    /**
     * 获取世界缩放
     * @returns 世界缩放
     */
    Transform.prototype.getWorldScale = function () {
        this.updateWorldMatrix();
        return this.worldScale.clone();
    };
    /**
     * 向前移动
     * @param distance 距离
     */
    Transform.prototype.moveForward = function (distance) {
        var direction = new THREE.Vector3(0, 0, -1);
        direction.applyEuler(this.localRotation);
        this.localPosition.addScaledVector(direction, distance);
        this.object3D.position.copy(this.localPosition);
        this.dirty = true;
        // 发出位置变更事件
        this.emit('positionChanged', this.localPosition);
    };
    /**
     * 向右移动
     * @param distance 距离
     */
    Transform.prototype.moveRight = function (distance) {
        var direction = new THREE.Vector3(1, 0, 0);
        direction.applyEuler(this.localRotation);
        this.localPosition.addScaledVector(direction, distance);
        this.object3D.position.copy(this.localPosition);
        this.dirty = true;
        // 发出位置变更事件
        this.emit('positionChanged', this.localPosition);
    };
    /**
     * 向上移动
     * @param distance 距离
     */
    Transform.prototype.moveUp = function (distance) {
        var direction = new THREE.Vector3(0, 1, 0);
        direction.applyEuler(this.localRotation);
        this.localPosition.addScaledVector(direction, distance);
        this.object3D.position.copy(this.localPosition);
        this.dirty = true;
        // 发出位置变更事件
        this.emit('positionChanged', this.localPosition);
    };
    /**
     * 绕X轴旋转
     * @param angle 角度（弧度）
     */
    Transform.prototype.rotateX = function (angle) {
        this.localRotation.x += angle;
        this.object3D.rotation.copy(this.localRotation);
        this.dirty = true;
        // 发出旋转变更事件
        this.emit('rotationChanged', this.localRotation);
    };
    /**
     * 绕Y轴旋转
     * @param angle 角度（弧度）
     */
    Transform.prototype.rotateY = function (angle) {
        this.localRotation.y += angle;
        this.object3D.rotation.copy(this.localRotation);
        this.dirty = true;
        // 发出旋转变更事件
        this.emit('rotationChanged', this.localRotation);
    };
    /**
     * 绕Z轴旋转
     * @param angle 角度（弧度）
     */
    Transform.prototype.rotateZ = function (angle) {
        this.localRotation.z += angle;
        this.object3D.rotation.copy(this.localRotation);
        this.dirty = true;
        // 发出旋转变更事件
        this.emit('rotationChanged', this.localRotation);
    };
    /**
     * 注视点
     * @param target 目标位置
     * @param up 上方向
     */
    Transform.prototype.lookAt = function (target, up) {
        if (up === void 0) { up = new THREE.Vector3(0, 1, 0); }
        // 计算世界位置
        var worldPosition = this.getWorldPosition();
        // 创建旋转矩阵
        var matrix = new THREE.Matrix4();
        matrix.lookAt(worldPosition, target, up);
        // 从旋转矩阵提取欧拉角
        var quaternion = new THREE.Quaternion();
        quaternion.setFromRotationMatrix(matrix);
        // 设置世界旋转
        var euler = new THREE.Euler().setFromQuaternion(quaternion);
        this.setWorldRotation(euler);
    };
    /**
     * 设置父变换
     * @param parent 父变换
     */
    Transform.prototype.setParent = function (parent) {
        // 如果已经是父变换，则不做任何操作
        if (this.parent === parent) {
            return;
        }
        // 从旧父变换中移除
        if (this.parent) {
            var index = this.parent.children.indexOf(this);
            if (index !== -1) {
                this.parent.children.splice(index, 1);
            }
            this.parent.object3D.remove(this.object3D);
        }
        // 设置新父变换
        this.parent = parent;
        // 添加到新父变换
        if (parent) {
            parent.children.push(this);
            parent.object3D.add(this.object3D);
        }
        this.dirty = true;
    };
    /**
     * 获取父变换
     * @returns 父变换
     */
    Transform.prototype.getParent = function () {
        return this.parent;
    };
    /**
     * 获取子变换列表
     * @returns 子变换数组
     */
    Transform.prototype.getChildren = function () {
        return __spreadArray([], this.children, true);
    };
    /**
     * 获取本地矩阵
     * @returns 本地矩阵
     */
    Transform.prototype.getLocalMatrix = function () {
        return this.object3D.matrix.clone();
    };
    /**
     * 获取世界矩阵
     * @returns 世界矩阵
     */
    Transform.prototype.getWorldMatrix = function () {
        this.updateWorldMatrix();
        return this.object3D.matrixWorld.clone();
    };
    /**
     * 更新世界矩阵
     */
    Transform.prototype.updateWorldMatrix = function () {
        if (!this.dirty) {
            return;
        }
        // 更新Three.js对象的世界矩阵
        this.object3D.updateMatrixWorld(true);
        // 提取世界位置
        this.worldPosition.setFromMatrixPosition(this.object3D.matrixWorld);
        // 提取世界旋转
        var quaternion = new THREE.Quaternion();
        var position = new THREE.Vector3();
        var scale = new THREE.Vector3();
        this.object3D.matrixWorld.decompose(position, quaternion, scale);
        this.worldQuaternion.copy(quaternion);
        this.worldRotation.setFromQuaternion(quaternion);
        // 提取世界缩放
        this.worldScale.copy(scale);
        this.dirty = false;
    };
    /**
     * 获取Three.js对象
     * @returns Three.js对象
     */
    Transform.prototype.getObject3D = function () {
        return this.object3D;
    };
    /**
     * 销毁组件
     */
    Transform.prototype.dispose = function () {
        // 从父变换中移除
        this.setParent(null);
        // 移除所有子变换
        while (this.children.length > 0) {
            this.children[0].setParent(null);
        }
        _super.prototype.dispose.call(this);
    };
    /** 组件类型 */
    Transform.type = 'Transform';
    return Transform;
}(Component_1.Component));
