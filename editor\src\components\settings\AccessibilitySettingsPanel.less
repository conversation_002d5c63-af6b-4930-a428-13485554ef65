/**
 * 辅助功能设置面板样式
 */
.accessibility-settings-panel {
  .ant-card-body {
    max-height: 600px;
    overflow-y: auto;
  }

  .ant-form-item-label {
    font-weight: 500;
  }

  .ant-slider-mark-text {
    font-size: 12px;
  }

  .ant-alert {
    margin-bottom: 16px;
  }

  .ant-divider {
    margin: 12px 0;
  }

  .ant-typography {
    margin-bottom: 0;
  }
}

// 响应式调整
@media (max-width: 576px) {
  .accessibility-settings-panel {
    .ant-card-body {
      padding: 12px;
    }

    .ant-form-item {
      margin-bottom: 12px;
    }
  }
}
