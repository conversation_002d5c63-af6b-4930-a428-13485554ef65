/**
 * 基于物理的面部动画示例
 * 演示如何使用物理引擎驱动面部动画
 */
import * as THREE from 'three';
import { Engine } from '../../core/Engine';
import { World } from '../../core/World';
import { Entity } from '../../core/Entity';
import { 
  FacialAnimationSystem, 
  FacialAnimationComponent, 
  FacialExpressionType, 
  FacialAnimationModelAdapterSystem,
  FacialAnimationModelType,
  FacialMuscleSimulationSystem
} from '../../animation';
import { GLTFLoader } from '../../loaders/GLTFLoader';
import { OrbitControls } from '../../utils/OrbitControls';

/**
 * 基于物理的面部动画示例
 */
export class PhysicsBasedFacialAnimationExample {
  /** 引擎 */
  private engine: Engine;
  /** 世界 */
  private world: World;
  /** 场景 */
  private scene: THREE.Scene;
  /** 相机 */
  private camera: THREE.PerspectiveCamera;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer;
  /** 控制器 */
  private controls: OrbitControls;
  /** 角色实体 */
  private characterEntity: Entity;
  /** 面部动画系统 */
  private facialAnimationSystem: FacialAnimationSystem;
  /** 面部动画模型适配器系统 */
  private modelAdapterSystem: FacialAnimationModelAdapterSystem;
  /** 面部肌肉模拟系统 */
  private muscleSimulationSystem: FacialMuscleSimulationSystem;
  /** 是否运行中 */
  private running: boolean = false;
  /** 模型类型 */
  private modelType: FacialAnimationModelType = FacialAnimationModelType.GENERIC;
  /** 表情选择元素 */
  private expressionSelect: HTMLSelectElement | null = null;
  /** 权重滑块 */
  private weightSlider: HTMLInputElement | null = null;
  /** 调试模式复选框 */
  private debugCheckbox: HTMLInputElement | null = null;
  /** 调试网格 */
  private debugMeshes: THREE.Mesh[] = [];
  /** 是否显示调试 */
  private showDebug: boolean = false;

  /**
   * 构造函数
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 获取世界
    this.world = this.engine.getWorld();
    
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x333333);
    
    // 创建相机
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.camera.position.set(0, 1.6, 2);
    
    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    document.body.appendChild(this.renderer.domElement);
    
    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 1.6, 0);
    this.controls.update();
    
    // 创建角色实体
    this.characterEntity = this.world.createEntity();
    
    // 创建面部动画系统
    this.facialAnimationSystem = new FacialAnimationSystem(this.world, {
      debug: true
    });
    
    // 创建面部动画模型适配器系统
    this.modelAdapterSystem = new FacialAnimationModelAdapterSystem(this.world, {
      debug: true,
      autoDetectBlendShapes: true
    });
    
    // 创建面部肌肉模拟系统
    this.muscleSimulationSystem = new FacialMuscleSimulationSystem(this.world, {
      debug: true,
      gravity: new THREE.Vector3(0, -9.81, 0),
      useSoftBodies: false
    });
    
    // 设置模型适配器系统
    this.facialAnimationSystem.setModelAdapterSystem(this.modelAdapterSystem);
    
    // 添加系统到世界
    this.world.addSystem(this.facialAnimationSystem);
    this.world.addSystem(this.modelAdapterSystem);
    this.world.addSystem(this.muscleSimulationSystem);
    
    // 设置窗口大小变化监听
    window.addEventListener('resize', this.onWindowResize.bind(this));
    
    // 创建UI
    this.createUI();
  }

  /**
   * 窗口大小变化处理
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    const uiContainer = document.createElement('div');
    uiContainer.style.position = 'absolute';
    uiContainer.style.top = '10px';
    uiContainer.style.left = '10px';
    uiContainer.style.padding = '10px';
    uiContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    uiContainer.style.borderRadius = '5px';
    uiContainer.style.color = 'white';
    document.body.appendChild(uiContainer);
    
    // 创建标题
    const title = document.createElement('h3');
    title.textContent = '基于物理的面部动画';
    title.style.margin = '0 0 10px 0';
    uiContainer.appendChild(title);
    
    // 创建表情选择
    const expressionContainer = document.createElement('div');
    expressionContainer.style.marginBottom = '10px';
    uiContainer.appendChild(expressionContainer);
    
    const expressionLabel = document.createElement('label');
    expressionLabel.textContent = '表情: ';
    expressionContainer.appendChild(expressionLabel);
    
    this.expressionSelect = document.createElement('select');
    this.expressionSelect.style.width = '150px';
    this.expressionSelect.style.marginLeft = '5px';
    
    // 添加表情选项
    const expressions = [
      { value: FacialExpressionType.NEUTRAL, label: '中性' },
      { value: FacialExpressionType.HAPPY, label: '开心' },
      { value: FacialExpressionType.SAD, label: '悲伤' },
      { value: FacialExpressionType.ANGRY, label: '愤怒' },
      { value: FacialExpressionType.SURPRISED, label: '惊讶' },
      { value: FacialExpressionType.FEARFUL, label: '恐惧' },
      { value: FacialExpressionType.DISGUSTED, label: '厌恶' },
      { value: FacialExpressionType.CONTEMPT, label: '蔑视' }
    ];
    
    for (const expr of expressions) {
      const option = document.createElement('option');
      option.value = expr.value;
      option.textContent = expr.label;
      this.expressionSelect.appendChild(option);
    }
    
    this.expressionSelect.addEventListener('change', this.applyExpression.bind(this));
    expressionContainer.appendChild(this.expressionSelect);
    
    // 创建权重滑块
    const weightContainer = document.createElement('div');
    weightContainer.style.marginBottom = '10px';
    uiContainer.appendChild(weightContainer);
    
    const weightLabel = document.createElement('label');
    weightLabel.textContent = '权重: ';
    weightContainer.appendChild(weightLabel);
    
    this.weightSlider = document.createElement('input');
    this.weightSlider.type = 'range';
    this.weightSlider.min = '0';
    this.weightSlider.max = '1';
    this.weightSlider.step = '0.01';
    this.weightSlider.value = '0.5';
    this.weightSlider.style.width = '150px';
    this.weightSlider.style.marginLeft = '5px';
    this.weightSlider.addEventListener('input', this.applyExpression.bind(this));
    weightContainer.appendChild(this.weightSlider);
    
    // 创建调试模式复选框
    const debugContainer = document.createElement('div');
    uiContainer.appendChild(debugContainer);
    
    this.debugCheckbox = document.createElement('input');
    this.debugCheckbox.type = 'checkbox';
    this.debugCheckbox.id = 'debug-checkbox';
    this.debugCheckbox.addEventListener('change', this.toggleDebug.bind(this));
    debugContainer.appendChild(this.debugCheckbox);
    
    const debugLabel = document.createElement('label');
    debugLabel.htmlFor = 'debug-checkbox';
    debugLabel.textContent = ' 显示调试网格';
    debugLabel.style.marginLeft = '5px';
    debugContainer.appendChild(debugLabel);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(ambientLight);
    
    // 添加方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 2, 3);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);
    
    // 添加地面
    const ground = new THREE.Mesh(
      new THREE.PlaneGeometry(10, 10),
      new THREE.MeshStandardMaterial({ color: 0x999999 })
    );
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    this.scene.add(ground);
    
    // 添加坐标轴辅助
    const axesHelper = new THREE.AxesHelper(1);
    this.scene.add(axesHelper);
  }

  /**
   * 加载模型
   * @param modelPath 模型路径
   */
  private loadModel(modelPath: string): void {
    // 创建GLTF加载器
    const loader = new GLTFLoader();
    
    // 加载角色模型
    loader.load(modelPath, (gltf) => {
      // 添加模型到场景
      this.scene.add(gltf.scene);
      
      // 设置阴影
      gltf.scene.traverse((object) => {
        if (object instanceof THREE.Mesh) {
          object.castShadow = true;
          object.receiveShadow = true;
        }
      });
      
      // 查找骨骼网格
      let skinnedMesh: THREE.SkinnedMesh | null = null;
      gltf.scene.traverse((object) => {
        if (object instanceof THREE.SkinnedMesh && !skinnedMesh) {
          skinnedMesh = object;
        }
      });
      
      if (skinnedMesh) {
        // 创建面部动画组件
        const facialAnimation = this.facialAnimationSystem.createFacialAnimation(this.characterEntity);
        
        // 将面部动画组件与模型绑定
        this.facialAnimationSystem.linkToModel(this.characterEntity, skinnedMesh);
        
        // 创建面部肌肉模拟组件
        const muscleSimulation = this.muscleSimulationSystem.createFacialMuscleSimulation(this.characterEntity);
        
        // 创建默认肌肉
        this.muscleSimulationSystem.createDefaultMuscles(this.characterEntity);
        
        // 创建调试网格
        this.createDebugMeshes();
        
        console.log('模型加载完成，已绑定面部动画组件和肌肉模拟组件');
        console.log('混合形状字典:', skinnedMesh.morphTargetDictionary);
        console.log('混合形状影响:', skinnedMesh.morphTargetInfluences);
      } else {
        console.warn('未找到骨骼网格');
      }
    }, undefined, (error) => {
      console.error('加载模型失败:', error);
    });
  }

  /**
   * 创建调试网格
   */
  private createDebugMeshes(): void {
    // 获取肌肉模拟组件
    const muscleSimulation = this.muscleSimulationSystem.getFacialMuscleSimulation(this.characterEntity);
    if (!muscleSimulation) return;
    
    // 获取所有肌肉
    const muscles = muscleSimulation.getMuscles();
    
    // 创建调试材质
    const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });
    
    // 为每个肌肉创建调试网格
    for (const muscle of muscles) {
      // 创建起点球体
      const startSphere = new THREE.Mesh(
        new THREE.SphereGeometry(muscle.radius || 0.01, 8, 8),
        material
      );
      startSphere.position.copy(muscle.start);
      startSphere.visible = this.showDebug;
      this.scene.add(startSphere);
      this.debugMeshes.push(startSphere);
      
      // 创建终点球体
      const endSphere = new THREE.Mesh(
        new THREE.SphereGeometry(muscle.radius || 0.01, 8, 8),
        material
      );
      endSphere.position.copy(muscle.end);
      endSphere.visible = this.showDebug;
      this.scene.add(endSphere);
      this.debugMeshes.push(endSphere);
      
      // 创建连接线
      const lineGeometry = new THREE.BufferGeometry().setFromPoints([
        muscle.start,
        muscle.end
      ]);
      const line = new THREE.Line(
        lineGeometry,
        new THREE.LineBasicMaterial({ color: 0xff0000 })
      );
      line.visible = this.showDebug;
      this.scene.add(line);
      this.debugMeshes.push(line);
    }
  }

  /**
   * 更新调试网格
   */
  private updateDebugMeshes(): void {
    // 获取物理引擎
    const physicsEngine = this.muscleSimulationSystem.getPhysicsEngine();
    
    // 获取所有肌肉
    const muscleSimulation = this.muscleSimulationSystem.getFacialMuscleSimulation(this.characterEntity);
    if (!muscleSimulation) return;
    
    const muscles = muscleSimulation.getMuscles();
    
    // 更新调试网格位置
    let meshIndex = 0;
    for (const muscle of muscles) {
      // 获取物理对象
      const startBody = physicsEngine.getBody(`${this.characterEntity.id}_muscle_${muscle.name}_start`);
      const endBody = physicsEngine.getBody(`${this.characterEntity.id}_muscle_${muscle.name}_end`);
      
      if (startBody && endBody) {
        // 更新起点球体
        const startSphere = this.debugMeshes[meshIndex++];
        startSphere.position.set(
          startBody.position.x,
          startBody.position.y,
          startBody.position.z
        );
        
        // 更新终点球体
        const endSphere = this.debugMeshes[meshIndex++];
        endSphere.position.set(
          endBody.position.x,
          endBody.position.y,
          endBody.position.z
        );
        
        // 更新连接线
        const line = this.debugMeshes[meshIndex++];
        const lineGeometry = new THREE.BufferGeometry().setFromPoints([
          new THREE.Vector3(startBody.position.x, startBody.position.y, startBody.position.z),
          new THREE.Vector3(endBody.position.x, endBody.position.y, endBody.position.z)
        ]);
        line.geometry.dispose();
        line.geometry = lineGeometry;
      } else {
        // 跳过三个网格（起点、终点、连接线）
        meshIndex += 3;
      }
    }
  }

  /**
   * 应用表情
   */
  private applyExpression(): void {
    // 获取表情类型和权重
    const expression = this.expressionSelect ? this.expressionSelect.value as FacialExpressionType : FacialExpressionType.NEUTRAL;
    const weight = this.weightSlider ? parseFloat(this.weightSlider.value) : 0.5;
    
    // 应用表情到面部动画组件
    const facialAnimation = this.facialAnimationSystem.getFacialAnimation(this.characterEntity);
    if (facialAnimation) {
      facialAnimation.setExpression(expression, weight);
    }
    
    // 应用表情到肌肉模拟组件
    this.muscleSimulationSystem.applyExpression(this.characterEntity, expression, weight);
    
    console.log(`应用表情: ${expression}, 权重: ${weight}`);
  }

  /**
   * 切换调试模式
   */
  private toggleDebug(): void {
    this.showDebug = this.debugCheckbox ? this.debugCheckbox.checked : false;
    
    // 更新调试网格可见性
    for (const mesh of this.debugMeshes) {
      mesh.visible = this.showDebug;
    }
    
    console.log(`调试模式: ${this.showDebug ? '开启' : '关闭'}`);
  }

  /**
   * 设置模型类型
   * @param type 模型类型
   */
  public setModelType(type: FacialAnimationModelType): void {
    this.modelType = type;
  }

  /**
   * 启动示例
   * @param modelPath 模型路径
   */
  public start(modelPath: string = 'models/character.glb'): void {
    // 创建场景
    this.createScene();
    
    // 加载模型
    this.loadModel(modelPath);
    
    // 启动引擎
    this.engine.start();
    
    // 开始动画循环
    this.running = true;
    this.animate();
    
    console.log('基于物理的面部动画示例已启动');
  }

  /**
   * 停止示例
   */
  public stop(): void {
    this.running = false;
    this.engine.stop();
    console.log('基于物理的面部动画示例已停止');
  }

  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;
    
    requestAnimationFrame(this.animate.bind(this));
    
    // 更新控制器
    this.controls.update();
    
    // 更新调试网格
    if (this.showDebug) {
      this.updateDebugMeshes();
    }
    
    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }
}

// 创建示例
const example = new PhysicsBasedFacialAnimationExample();

// 设置模型类型（根据实际模型选择）
example.setModelType(FacialAnimationModelType.GLTF);

// 启动示例
example.start('models/character_with_blendshapes.glb');
