/**
 * 资产状态切片
 */
import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { assetService } from '../../services/assetService';

// 定义资产类型
export enum AssetType {
  MODEL = 'model',
  TEXTURE = 'texture',
  AUDIO = 'audio',
  MATERIAL = 'material',
  PREFAB = 'prefab',
  SCRIPT = 'script',
}

// 定义资产接口
export interface Asset {
  id: string;
  name: string;
  type: AssetType;
  tags: string[];
  thumbnailUrl: string;
  url: string;
  size: number;
  createdAt: string;
  updatedAt: string;
  metadata: Record<string, any>;
}

// 定义资产状态
interface AssetState {
  assets: Asset[];
  selectedAssetId: string | null;
  loading: boolean;
  error: string | null;
  filters: {
    type: AssetType | null;
    tags: string[];
    search: string;
  };
}

// 初始状态
const initialState: AssetState = {
  assets: [],
  selectedAssetId: null,
  loading: false,
  error: null,
  filters: {
    type: null,
    tags: [],
    search: '',
  },
};

// 异步操作：获取所有资产
export const fetchAssets = createAsyncThunk(
  'assets/fetchAssets',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { assets: AssetState };
      const { type, tags, search } = state.assets.filters;
      
      const response = await assetService.getAssets({ type, tags, search });
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：获取单个资产
export const fetchAsset = createAsyncThunk(
  'assets/fetchAsset',
  async (assetId: string, { rejectWithValue }) => {
    try {
      const response = await assetService.getAsset(assetId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：上传资产
export const uploadAsset = createAsyncThunk(
  'assets/uploadAsset',
  async (formData: FormData, { rejectWithValue }) => {
    try {
      const response = await assetService.uploadAsset(formData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：删除资产
export const removeAsset = createAsyncThunk(
  'assets/removeAsset',
  async (assetId: string, { rejectWithValue }) => {
    try {
      await assetService.deleteAsset(assetId);
      return assetId;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 创建资产切片
const assetsSlice = createSlice({
  name: 'assets',
  initialState,
  reducers: {
    // 选择资产
    selectAsset: (state, action: PayloadAction<string | null>) => {
      state.selectedAssetId = action.payload;
    },
    
    // 设置过滤器
    setFilter: (state, action: PayloadAction<{ type?: AssetType | null; tags?: string[]; search?: string }>) => {
      state.filters = {
        ...state.filters,
        ...action.payload,
      };
    },
    
    // 清除过滤器
    clearFilters: (state) => {
      state.filters = {
        type: null,
        tags: [],
        search: '',
      };
    },
    
    // 添加资产（本地操作，不发送请求）
    addAsset: (state, action: PayloadAction<Asset>) => {
      state.assets.push(action.payload);
    },
    
    // 更新资产（本地操作，不发送请求）
    updateAsset: (state, action: PayloadAction<{ id: string; changes: Partial<Asset> }>) => {
      const { id, changes } = action.payload;
      const assetIndex = state.assets.findIndex(asset => asset.id === id);
      
      if (assetIndex !== -1) {
        state.assets[assetIndex] = {
          ...state.assets[assetIndex],
          ...changes,
          metadata: changes.metadata
            ? { ...state.assets[assetIndex].metadata, ...changes.metadata }
            : state.assets[assetIndex].metadata,
        };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取所有资产
      .addCase(fetchAssets.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAssets.fulfilled, (state, action) => {
        state.loading = false;
        state.assets = action.payload;
      })
      .addCase(fetchAssets.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 获取单个资产
      .addCase(fetchAsset.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAsset.fulfilled, (state, action) => {
        state.loading = false;
        const assetIndex = state.assets.findIndex(asset => asset.id === action.payload.id);
        
        if (assetIndex !== -1) {
          state.assets[assetIndex] = action.payload;
        } else {
          state.assets.push(action.payload);
        }
      })
      .addCase(fetchAsset.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 上传资产
      .addCase(uploadAsset.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(uploadAsset.fulfilled, (state, action) => {
        state.loading = false;
        state.assets.push(action.payload);
      })
      .addCase(uploadAsset.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 删除资产
      .addCase(removeAsset.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(removeAsset.fulfilled, (state, action) => {
        state.loading = false;
        state.assets = state.assets.filter(asset => asset.id !== action.payload);
        
        if (state.selectedAssetId === action.payload) {
          state.selectedAssetId = null;
        }
      })
      .addCase(removeAsset.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  selectAsset,
  setFilter,
  clearFilters,
  addAsset,
  updateAsset,
} = assetsSlice.actions;

export default assetsSlice.reducer;
