/**
 * 控制台面板组件
 */
import React, { useState, useEffect, useRef } from 'react';
import { List, Button, Input, Space, Select, Tag, Tooltip, Typography } from 'antd';
import {
  InfoCircleOutlined,
  WarningOutlined,
  CloseCircleOutlined,
  BugOutlined,
  ClearOutlined,
  SearchOutlined,
  DownOutlined,
  UpOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Search } = Input;
const { Option } = Select;
const { Text } = Typography;

// 日志类型
enum LogType {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  DEBUG = 'debug',
}

// 日志接口
interface LogEntry {
  id: string;
  type: LogType;
  message: string;
  details?: string;
  timestamp: Date;
  count?: number;
}

const ConsolePanel: React.FC = () => {
  const { t } = useTranslation();
  
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [logTypeFilter, setLogTypeFilter] = useState<LogType[]>([
    LogType.INFO,
    LogType.WARNING,
    LogType.ERROR,
    LogType.DEBUG,
  ]);
  const [autoScroll, setAutoScroll] = useState(true);
  const [selectedLog, setSelectedLog] = useState<LogEntry | null>(null);
  
  const listRef = useRef<HTMLDivElement>(null);
  
  // 示例日志数据
  useEffect(() => {
    const sampleLogs: LogEntry[] = [
      {
        id: '1',
        type: LogType.INFO,
        message: '场景加载完成',
        timestamp: new Date(),
      },
      {
        id: '2',
        type: LogType.WARNING,
        message: '纹理分辨率不是2的幂',
        details: '纹理 "grass.jpg" 的分辨率为 1920x1080，这可能会导致在某些设备上的渲染问题。建议使用2的幂分辨率，如2048x1024。',
        timestamp: new Date(Date.now() - 5000),
      },
      {
        id: '3',
        type: LogType.ERROR,
        message: '无法加载模型',
        details: '加载模型 "character.glb" 时发生错误: 404 Not Found',
        timestamp: new Date(Date.now() - 10000),
      },
      {
        id: '4',
        type: LogType.DEBUG,
        message: '物理引擎初始化',
        details: '物理引擎已初始化，重力设置为 (0, -9.8, 0)',
        timestamp: new Date(Date.now() - 15000),
      },
      {
        id: '5',
        type: LogType.INFO,
        message: '音频系统初始化',
        timestamp: new Date(Date.now() - 20000),
      },
    ];
    
    setLogs(sampleLogs);
  }, []);
  
  // 过滤日志
  useEffect(() => {
    let filtered = logs;
    
    // 按类型过滤
    if (logTypeFilter.length > 0) {
      filtered = filtered.filter((log) => logTypeFilter.includes(log.type));
    }
    
    // 按搜索词过滤
    if (searchValue) {
      const lowerCaseSearch = searchValue.toLowerCase();
      filtered = filtered.filter(
        (log) =>
          log.message.toLowerCase().includes(lowerCaseSearch) ||
          (log.details && log.details.toLowerCase().includes(lowerCaseSearch))
      );
    }
    
    setFilteredLogs(filtered);
  }, [logs, logTypeFilter, searchValue]);
  
  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && listRef.current) {
      listRef.current.scrollTop = listRef.current.scrollHeight;
    }
  }, [filteredLogs, autoScroll]);
  
  // 清除所有日志
  const handleClearLogs = () => {
    setLogs([]);
    setSelectedLog(null);
  };
  
  // 复制日志
  const handleCopyLog = (log: LogEntry) => {
    const logText = `[${log.type.toUpperCase()}] ${log.message}${log.details ? '\n' + log.details : ''}`;
    navigator.clipboard.writeText(logText);
  };
  
  // 复制所有日志
  const handleCopyAllLogs = () => {
    const logsText = filteredLogs
      .map((log) => `[${log.type.toUpperCase()}] ${log.message}${log.details ? '\n' + log.details : ''}`)
      .join('\n');
    navigator.clipboard.writeText(logsText);
  };
  
  // 获取日志图标
  const getLogIcon = (type: LogType) => {
    switch (type) {
      case LogType.INFO:
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      case LogType.WARNING:
        return <WarningOutlined style={{ color: '#faad14' }} />;
      case LogType.ERROR:
        return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
      case LogType.DEBUG:
        return <BugOutlined style={{ color: '#52c41a' }} />;
      default:
        return <InfoCircleOutlined />;
    }
  };
  
  // 获取日志类型标签
  const getLogTypeTag = (type: LogType) => {
    switch (type) {
      case LogType.INFO:
        return <Tag color="blue">{t('editor.info')}</Tag>;
      case LogType.WARNING:
        return <Tag color="orange">{t('editor.warning')}</Tag>;
      case LogType.ERROR:
        return <Tag color="red">{t('editor.error')}</Tag>;
      case LogType.DEBUG:
        return <Tag color="green">{t('editor.debug')}</Tag>;
      default:
        return <Tag>{type}</Tag>;
    }
  };
  
  // 切换日志类型过滤
  const toggleLogTypeFilter = (type: LogType) => {
    if (logTypeFilter.includes(type)) {
      setLogTypeFilter(logTypeFilter.filter((t) => t !== type));
    } else {
      setLogTypeFilter([...logTypeFilter, type]);
    }
  };
  
  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <div style={{ padding: '8px', borderBottom: '1px solid #f0f0f0' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
          <Space>
            <Tooltip title={t('editor.toggleInfo')}>
              <Button
                type={logTypeFilter.includes(LogType.INFO) ? 'primary' : 'default'}
                icon={<InfoCircleOutlined />}
                size="small"
                onClick={() => toggleLogTypeFilter(LogType.INFO)}
              />
            </Tooltip>
            <Tooltip title={t('editor.toggleWarning')}>
              <Button
                type={logTypeFilter.includes(LogType.WARNING) ? 'primary' : 'default'}
                icon={<WarningOutlined />}
                size="small"
                onClick={() => toggleLogTypeFilter(LogType.WARNING)}
              />
            </Tooltip>
            <Tooltip title={t('editor.toggleError')}>
              <Button
                type={logTypeFilter.includes(LogType.ERROR) ? 'primary' : 'default'}
                icon={<CloseCircleOutlined />}
                size="small"
                onClick={() => toggleLogTypeFilter(LogType.ERROR)}
              />
            </Tooltip>
            <Tooltip title={t('editor.toggleDebug')}>
              <Button
                type={logTypeFilter.includes(LogType.DEBUG) ? 'primary' : 'default'}
                icon={<BugOutlined />}
                size="small"
                onClick={() => toggleLogTypeFilter(LogType.DEBUG)}
              />
            </Tooltip>
          </Space>
          <Space>
            <Tooltip title={t('editor.clearConsole')}>
              <Button icon={<ClearOutlined />} size="small" onClick={handleClearLogs} />
            </Tooltip>
            <Tooltip title={t('editor.copyAllLogs')}>
              <Button icon={<CopyOutlined />} size="small" onClick={handleCopyAllLogs} />
            </Tooltip>
            <Tooltip title={autoScroll ? t('editor.disableAutoScroll') : t('editor.enableAutoScroll')}>
              <Button
                icon={autoScroll ? <DownOutlined /> : <UpOutlined />}
                size="small"
                onClick={() => setAutoScroll(!autoScroll)}
              />
            </Tooltip>
          </Space>
        </div>
        <Search
          placeholder={t('editor.searchLogs')}
          allowClear
          onChange={(e) => setSearchValue(e.target.value)}
          style={{ width: '100%' }}
        />
      </div>
      
      <div
        ref={listRef}
        style={{
          flex: 1,
          overflow: 'auto',
          padding: '0 8px',
        }}
      >
        <List
          itemLayout="horizontal"
          dataSource={filteredLogs}
          renderItem={(log) => (
            <List.Item
              key={log.id}
              actions={[
                <Button type="text" icon={<CopyOutlined />} size="small" onClick={() => handleCopyLog(log)} />,
              ]}
              style={{
                background: selectedLog?.id === log.id ? 'rgba(24, 144, 255, 0.1)' : 'transparent',
                cursor: 'pointer',
              }}
              onClick={() => setSelectedLog(log.id === selectedLog?.id ? null : log)}
            >
              <List.Item.Meta
                avatar={getLogIcon(log.type)}
                title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    {getLogTypeTag(log.type)}
                    <Text style={{ marginLeft: 8 }}>{log.message}</Text>
                    {log.count && log.count > 1 && (
                      <Tag color="blue" style={{ marginLeft: 8 }}>
                        {log.count}
                      </Tag>
                    )}
                  </div>
                }
                description={
                  <div>
                    <Text type="secondary">
                      {log.timestamp.toLocaleTimeString()}
                    </Text>
                    {selectedLog?.id === log.id && log.details && (
                      <div style={{ marginTop: 8, padding: 8, background: '#f5f5f5', borderRadius: 4 }}>
                        {log.details}
                      </div>
                    )}
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </div>
    </div>
  );
};

export default ConsolePanel;
