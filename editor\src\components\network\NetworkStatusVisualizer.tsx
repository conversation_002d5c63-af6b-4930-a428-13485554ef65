/**
 * 网络状态可视化组件
 * 用于实时显示网络状态数据和指标
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Row, Col, Statistic, Progress, Tooltip, Button, Space, Divider, Select } from 'antd';
import {
  InfoCircleOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  DownloadOutlined,
  LineChartOutlined,
  DashboardOutlined,
  ApiOutlined,
  WifiOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import { Gauge, Liquid, RingProgress, DualAxes, Heatmap } from '@ant-design/charts';
import { useTranslation } from 'react-i18next';
import { NetworkQualityData, NetworkQualityLevel } from '../../../engine/src/network/NetworkQualityMonitor';
import './NetworkStatusVisualizer.css';

const { Option } = Select;

interface NetworkStatusVisualizerProps {
  /** 当前网络质量数据 */
  currentQuality?: NetworkQualityData;
  /** 历史网络质量数据 */
  qualityHistory?: NetworkQualityData[];
  /** 是否连接 */
  connected?: boolean;
  /** 是否正在加载数据 */
  loading?: boolean;
  /** 刷新数据回调 */
  onRefresh?: () => void;
  /** 导出数据回调 */
  onExportData?: () => void;
}

/**
 * 网络状态可视化组件
 */
const NetworkStatusVisualizer: React.FC<NetworkStatusVisualizerProps> = ({
  currentQuality,
  qualityHistory = [],
  connected = false,
  loading = false,
  onRefresh,
  onExportData,
}) => {
  const { t } = useTranslation();
  const [visualizationType, setVisualizationType] = useState<'realtime' | 'heatmap' | 'correlation'>('realtime');
  const [timeRange, setTimeRange] = useState<'5min' | '15min' | '30min' | '1hour' | 'all'>('5min');
  const chartRef = useRef<any>(null);

  // 格式化带宽显示
  const formatBandwidth = (bytes: number): string => {
    if (bytes < 1024) {
      return `${bytes.toFixed(0)} B/s`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB/s`;
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(2)} MB/s`;
    }
  };

  // 获取网络质量等级对应的颜色
  const getQualityLevelColor = (level: NetworkQualityLevel): string => {
    switch (level) {
      case NetworkQualityLevel.EXCELLENT:
        return '#52c41a'; // 绿色
      case NetworkQualityLevel.GOOD:
        return '#1890ff'; // 蓝色
      case NetworkQualityLevel.MEDIUM:
        return '#faad14'; // 黄色
      case NetworkQualityLevel.BAD:
        return '#fa8c16'; // 橙色
      case NetworkQualityLevel.VERY_BAD:
        return '#f5222d'; // 红色
      default:
        return '#d9d9d9'; // 灰色
    }
  };

  // 获取网络质量等级对应的文本
  const getQualityLevelText = (level: NetworkQualityLevel): string => {
    switch (level) {
      case NetworkQualityLevel.EXCELLENT:
        return t('network.status.excellent');
      case NetworkQualityLevel.GOOD:
        return t('network.status.good');
      case NetworkQualityLevel.MEDIUM:
        return t('network.status.medium');
      case NetworkQualityLevel.BAD:
        return t('network.status.bad');
      case NetworkQualityLevel.VERY_BAD:
        return t('network.status.veryBad');
      default:
        return t('network.status.unknown');
    }
  };

  // 准备图表数据
  const prepareChartData = () => {
    // 根据时间范围过滤数据
    let filteredData = qualityHistory;
    const now = Date.now();
    
    if (timeRange !== 'all') {
      let timeRangeMs = 0;
      switch (timeRange) {
        case '5min':
          timeRangeMs = 5 * 60 * 1000;
          break;
        case '15min':
          timeRangeMs = 15 * 60 * 1000;
          break;
        case '30min':
          timeRangeMs = 30 * 60 * 1000;
          break;
        case '1hour':
          timeRangeMs = 60 * 60 * 1000;
          break;
      }
      
      filteredData = qualityHistory.filter(data => now - data.timestamp < timeRangeMs);
    }
    
    return filteredData.map((data, index) => ({
      index,
      timestamp: data.timestamp,
      time: new Date(data.timestamp).toLocaleTimeString(),
      rtt: data.rtt,
      packetLoss: data.packetLoss * 100,
      jitter: data.jitter,
      bandwidth: data.bandwidth / 1024, // KB/s
      stability: data.stability || 1,
      congestion: data.congestion || 0,
      qualityScore: data.qualityScore || 100,
    }));
  };

  // 渲染实时网络状态
  const renderRealtimeStatus = () => {
    if (!currentQuality) {
      return (
        <div className="no-data-message">
          <ApiOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
          <p>{t('network.visualizer.noData')}</p>
        </div>
      );
    }

    return (
      <div className="realtime-status">
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <h3>{t('network.visualizer.qualityScore')}</h3>
                <Gauge
                  percent={(currentQuality.qualityScore || 0) / 100}
                  range={{
                    color: ['#F4664A', '#FAAD14', '#30BF78'],
                  }}
                  indicator={{
                    pointer: {
                      style: {
                        stroke: '#D0D0D0',
                      },
                    },
                    pin: {
                      style: {
                        stroke: '#D0D0D0',
                      },
                    },
                  }}
                  statistic={{
                    content: {
                      formatter: () => `${currentQuality.qualityScore || 0}`,
                      style: {
                        fontSize: '24px',
                        lineHeight: '24px',
                      },
                    },
                  }}
                />
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <h3>{t('network.visualizer.reliability')}</h3>
                <Liquid
                  percent={currentQuality.reliability || 1}
                  outline={{
                    border: 2,
                    distance: 4,
                  }}
                  wave={{
                    length: 128,
                  }}
                />
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <h3>{t('network.visualizer.networkType')}</h3>
                <div className="network-type-icon">
                  {currentQuality.networkType === 'wifi' ? (
                    <WifiOutlined style={{ fontSize: 48, color: '#1890ff' }} />
                  ) : currentQuality.networkType === 'ethernet' ? (
                    <ApiOutlined style={{ fontSize: 48, color: '#52c41a' }} />
                  ) : (
                    <GlobalOutlined style={{ fontSize: 48, color: '#faad14' }} />
                  )}
                </div>
                <div className="network-type-text">
                  <p>{currentQuality.networkType || t('network.visualizer.unknown')}</p>
                  {currentQuality.connectionType && (
                    <p className="connection-type">{currentQuality.connectionType}</p>
                  )}
                </div>
              </div>
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('network.visualizer.latency')}
                value={currentQuality.rtt.toFixed(0)}
                suffix="ms"
                valueStyle={{
                  color: currentQuality.rtt > 200 ? '#f5222d' :
                         currentQuality.rtt > 100 ? '#faad14' : '#52c41a'
                }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('network.visualizer.packetLoss')}
                value={(currentQuality.packetLoss * 100).toFixed(1)}
                suffix="%"
                valueStyle={{
                  color: currentQuality.packetLoss > 0.1 ? '#f5222d' :
                         currentQuality.packetLoss > 0.05 ? '#faad14' : '#52c41a'
                }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('network.visualizer.jitter')}
                value={currentQuality.jitter.toFixed(1)}
                suffix="ms"
                valueStyle={{
                  color: currentQuality.jitter > 50 ? '#f5222d' :
                         currentQuality.jitter > 20 ? '#faad14' : '#52c41a'
                }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('network.visualizer.bandwidth')}
                value={formatBandwidth(currentQuality.bandwidth)}
                valueStyle={{
                  color: currentQuality.bandwidth < 100000 ? '#f5222d' :
                         currentQuality.bandwidth < 500000 ? '#faad14' : '#52c41a'
                }}
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  return (
    <div className="network-status-visualizer">
      <div className="visualizer-header">
        <Space>
          <h2>
            <DashboardOutlined /> {t('network.visualizer.title')}
          </h2>
          <Tooltip title={t('network.visualizer.info')}>
            <InfoCircleOutlined />
          </Tooltip>
        </Space>
        
        <Space>
          <Select 
            value={visualizationType} 
            onChange={setVisualizationType}
            style={{ width: 150 }}
          >
            <Option value="realtime">{t('network.visualizer.realtime')}</Option>
            <Option value="heatmap">{t('network.visualizer.heatmap')}</Option>
            <Option value="correlation">{t('network.visualizer.correlation')}</Option>
          </Select>
          
          <Select 
            value={timeRange} 
            onChange={setTimeRange}
            style={{ width: 120 }}
          >
            <Option value="5min">{t('network.visualizer.5min')}</Option>
            <Option value="15min">{t('network.visualizer.15min')}</Option>
            <Option value="30min">{t('network.visualizer.30min')}</Option>
            <Option value="1hour">{t('network.visualizer.1hour')}</Option>
            <Option value="all">{t('network.visualizer.all')}</Option>
          </Select>
          
          <Button 
            icon={<ReloadOutlined />} 
            onClick={onRefresh}
            loading={loading}
          >
            {t('network.visualizer.refresh')}
          </Button>
          
          <Button 
            icon={<DownloadOutlined />} 
            onClick={onExportData}
          >
            {t('network.visualizer.export')}
          </Button>
          
          <Button 
            icon={<FullscreenOutlined />} 
          >
            {t('network.visualizer.fullscreen')}
          </Button>
        </Space>
      </div>
      
      <Divider />
      
      <div className="visualizer-content">
        {visualizationType === 'realtime' && renderRealtimeStatus()}
        {/* 其他可视化类型将在后续实现 */}
      </div>
    </div>
  );
};

export default NetworkStatusVisualizer;
