/**
 * 物理编辑器页面
 * 用于展示和编辑物理系统和组件
 */
import React, { useState, useEffect } from 'react';
import { Layout, Tabs, Card, Row, Col, Button, Space, Divider, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import {
  PhysicsSystemEditor,
  PhysicsBodyEditor,
  PhysicsColliderEditor,
  PhysicsConstraintEditor,
  CharacterControllerEditor,
  PhysicsMaterialEditor,
  PhysicsPresetEditor,
  PhysicsDebuggerEditor,
  PhysicsInteractionEditor
} from '../components/physics';
import { EntityList } from '../components/entity/EntityList';
import { EntityDetails } from '../components/entity/EntityDetails';

const { Header, Content, Sider } = Layout;
const { TabPane } = Tabs;
const { Title, Text } = Typography;

/**
 * 物理编辑器页面
 */
const PhysicsEditorPage: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 获取选中的实体
  const selectedEntityId = useSelector((state: RootState) => state.editor.selectedEntityId);

  return (
    <Layout className="physics-editor-page">
      <Header className="page-header">
        <Title level={3}>{t('editor.physics.physicsEditor')}</Title>
      </Header>

      <Layout>
        <Sider width={300} className="editor-sider">
          <Card title={t('editor.common.entities')} className="entity-list-card">
            <EntityList />
          </Card>
        </Sider>

        <Content className="editor-content">
          <Tabs defaultActiveKey="system" className="editor-tabs">
            <TabPane tab={t('editor.physics.system')} key="system">
              <PhysicsSystemEditor />
            </TabPane>

            <TabPane tab={t('editor.physics.components')} key="components">
              {selectedEntityId ? (
                <Row gutter={[16, 16]}>
                  <Col span={24}>
                    <EntityDetails entityId={selectedEntityId} />
                  </Col>

                  <Col span={12}>
                    <PhysicsBodyEditor entityId={selectedEntityId} />
                  </Col>

                  <Col span={12}>
                    <PhysicsColliderEditor entityId={selectedEntityId} />
                  </Col>

                  <Col span={12}>
                    <PhysicsConstraintEditor entityId={selectedEntityId} />
                  </Col>

                  <Col span={12}>
                    <CharacterControllerEditor entityId={selectedEntityId} />
                  </Col>

                  <Col span={12}>
                    <PhysicsInteractionEditor selectedEntityId={selectedEntityId} />
                  </Col>
                </Row>
              ) : (
                <div className="empty-state">
                  <Text>{t('editor.common.selectEntityPrompt')}</Text>
                </div>
              )}
            </TabPane>

            <TabPane tab={t('editor.physics.materials')} key="materials">
              <PhysicsMaterialEditor />
            </TabPane>

            <TabPane tab={t('editor.physics.presets')} key="presets">
              <PhysicsPresetEditor />
            </TabPane>

            <TabPane tab={t('editor.physics.debugger')} key="debugger">
              <PhysicsDebuggerEditor />
            </TabPane>

            <TabPane tab={t('editor.physics.examples')} key="examples">
              <Card title={t('editor.physics.physicsExamples')}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button block>{t('editor.physics.examples.basicPhysics')}</Button>
                  <Button block>{t('editor.physics.examples.constraints')}</Button>
                  <Button block>{t('editor.physics.examples.characterController')}</Button>
                  <Button block>{t('editor.physics.examples.continuousCollisionDetection')}</Button>
                  <Button block>{t('editor.physics.examples.collisionVisualization')}</Button>
                  <Button block>{t('editor.physics.examples.physicsPresets')}</Button>
                  <Button block>{t('editor.physics.examples.vehiclePhysics')}</Button>
                  <Button block>{t('editor.physics.examples.ragdoll')}</Button>
                  <Button block>{t('editor.physics.examples.softBody')}</Button>
                </Space>
              </Card>
            </TabPane>

            <TabPane tab={t('editor.physics.import_export')} key="import_export">
              <Card title={t('editor.physics.sceneImportExport')}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button type="primary" block>{t('editor.physics.exportScene')}</Button>
                  <Button block>{t('editor.physics.importScene')}</Button>
                  <Divider />
                  <Button type="primary" block>{t('editor.physics.exportPresets')}</Button>
                  <Button block>{t('editor.physics.importPresets')}</Button>
                </Space>
              </Card>
            </TabPane>
          </Tabs>
        </Content>
      </Layout>
    </Layout>
  );
};

export default PhysicsEditorPage;
