/**
 * 输入相关的可视化脚本节点
 */

import { VisualScriptNode } from '../VisualScriptNode';
import { NodeRegistry } from '../NodeRegistry';

/**
 * 键盘输入节点
 */
export class KeyboardInputNode extends VisualScriptNode {
  constructor() {
    super('KeyboardInput', '键盘输入');
    this.addInput('key', 'string', '按键');
    this.addOutput('pressed', 'boolean', '是否按下');
    this.addOutput('justPressed', 'boolean', '刚按下');
    this.addOutput('justReleased', 'boolean', '刚释放');
  }

  public execute(inputs: any): any {
    const key = inputs.key;
    if (!key) return { pressed: false, justPressed: false, justReleased: false };

    // 这里应该连接到实际的输入系统
    // 暂时返回模拟数据
    return {
      pressed: false,
      justPressed: false,
      justReleased: false
    };
  }
}

/**
 * 鼠标输入节点
 */
export class MouseInputNode extends VisualScriptNode {
  constructor() {
    super('MouseInput', '鼠标输入');
    this.addInput('button', 'number', '鼠标按钮');
    this.addOutput('pressed', 'boolean', '是否按下');
    this.addOutput('position', 'vector2', '鼠标位置');
    this.addOutput('delta', 'vector2', '移动增量');
  }

  public execute(inputs: any): any {
    // 这里应该连接到实际的输入系统
    // 暂时返回模拟数据
    return {
      pressed: false,
      position: { x: 0, y: 0 },
      delta: { x: 0, y: 0 }
    };
  }
}

/**
 * 触摸输入节点
 */
export class TouchInputNode extends VisualScriptNode {
  constructor() {
    super('TouchInput', '触摸输入');
    this.addOutput('touching', 'boolean', '是否触摸');
    this.addOutput('position', 'vector2', '触摸位置');
    this.addOutput('touchCount', 'number', '触摸点数量');
  }

  public execute(): any {
    // 这里应该连接到实际的输入系统
    // 暂时返回模拟数据
    return {
      touching: false,
      position: { x: 0, y: 0 },
      touchCount: 0
    };
  }
}

/**
 * 游戏手柄输入节点
 */
export class GamepadInputNode extends VisualScriptNode {
  constructor() {
    super('GamepadInput', '游戏手柄输入');
    this.addInput('gamepadIndex', 'number', '手柄索引');
    this.addInput('button', 'number', '按钮');
    this.addOutput('pressed', 'boolean', '是否按下');
    this.addOutput('leftStick', 'vector2', '左摇杆');
    this.addOutput('rightStick', 'vector2', '右摇杆');
  }

  public execute(inputs: any): any {
    // 这里应该连接到实际的输入系统
    // 暂时返回模拟数据
    return {
      pressed: false,
      leftStick: { x: 0, y: 0 },
      rightStick: { x: 0, y: 0 }
    };
  }
}

/**
 * 注册输入节点
 */
export function registerInputNodes(): void {
  NodeRegistry.register('KeyboardInput', KeyboardInputNode);
  NodeRegistry.register('MouseInput', MouseInputNode);
  NodeRegistry.register('TouchInput', TouchInputNode);
  NodeRegistry.register('GamepadInput', GamepadInputNode);
}
