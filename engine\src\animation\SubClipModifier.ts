/**
 * 子片段变形器
 * 用于修改子片段的播放方式
 */
import * as THREE from 'three';
import { EventEmitter } from '../utils/EventEmitter';
import { SubClip } from './SubClip';
import { AnimationSubClip } from './AnimationSubClip';

/**
 * 子片段变形事件类型
 */
export enum SubClipModifierEventType {
  /** 变形应用 */
  MODIFIER_APPLIED = 'modifierApplied',
  /** 变形更新 */
  MODIFIER_UPDATED = 'modifierUpdated',
  /** 变形重置 */
  MODIFIER_RESET = 'modifierReset'
}

/**
 * 变形类型
 */
export enum ModifierType {
  /** 时间缩放 */
  TIME_SCALE = 'timeScale',
  /** 反向播放 */
  REVERSE = 'reverse',
  /** 循环 */
  LOOP = 'loop',
  /** 镜像 */
  MIRROR = 'mirror',
  /** 抖动 */
  JITTER = 'jitter',
  /** 延迟 */
  DELAY = 'delay',
  /** 平滑 */
  SMOOTH = 'smooth',
  /** 噪声 */
  NOISE = 'noise',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 子片段变形配置
 */
export interface SubClipModifierConfig {
  /** 变形名称 */
  name?: string;
  /** 变形类型 */
  type?: ModifierType;
  /** 参数 */
  params?: any;
  /** 是否启用 */
  enabled?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 子片段变形器
 */
export class SubClipModifier {
  /** 变形名称 */
  private name: string;
  /** 变形类型 */
  private type: ModifierType;
  /** 参数 */
  private params: any;
  /** 是否启用 */
  private enabled: boolean;
  /** 是否启用调试 */
  private debug: boolean;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 自定义变形函数 */
  private customModifier?: (clip: THREE.AnimationClip, params: any) => THREE.AnimationClip;

  /**
   * 创建子片段变形器
   * @param config 配置
   */
  constructor(config: SubClipModifierConfig = {}) {
    this.name = config.name || 'modifier';
    this.type = config.type || ModifierType.TIME_SCALE;
    this.params = config.params || {};
    this.enabled = config.enabled !== undefined ? config.enabled : true;
    this.debug = config.debug !== undefined ? config.debug : false;
  }

  /**
   * 获取变形名称
   * @returns 变形名称
   */
  public getName(): string {
    return this.name;
  }

  /**
   * 设置变形名称
   * @param name 变形名称
   */
  public setName(name: string): void {
    this.name = name;
  }

  /**
   * 获取变形类型
   * @returns 变形类型
   */
  public getType(): ModifierType {
    return this.type;
  }

  /**
   * 设置变形类型
   * @param type 变形类型
   */
  public setType(type: ModifierType): void {
    this.type = type;
  }

  /**
   * 获取参数
   * @returns 参数
   */
  public getParams(): any {
    return { ...this.params };
  }

  /**
   * 设置参数
   * @param params 参数
   */
  public setParams(params: any): void {
    this.params = { ...params };
  }

  /**
   * 是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 设置是否启用
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * 设置自定义变形函数
   * @param func 自定义变形函数
   */
  public setCustomModifier(func: (clip: THREE.AnimationClip, params: any) => THREE.AnimationClip): void {
    this.customModifier = func;
    this.type = ModifierType.CUSTOM;
  }

  /**
   * 应用变形
   * @param clip 动画片段
   * @returns 变形后的动画片段
   */
  public apply(clip: THREE.AnimationClip): THREE.AnimationClip {
    if (!this.enabled) return clip;

    // 创建新的动画片段
    const modifiedClip = THREE.AnimationClip.parse(THREE.AnimationClip.toJSON(clip));
    modifiedClip.name = `${clip.name}_${this.name}`;

    // 根据变形类型应用变形
    switch (this.type) {
      case ModifierType.TIME_SCALE:
        this.applyTimeScale(modifiedClip);
        break;
      case ModifierType.REVERSE:
        this.applyReverse(modifiedClip);
        break;
      case ModifierType.LOOP:
        this.applyLoop(modifiedClip);
        break;
      case ModifierType.MIRROR:
        this.applyMirror(modifiedClip);
        break;
      case ModifierType.JITTER:
        this.applyJitter(modifiedClip);
        break;
      case ModifierType.DELAY:
        this.applyDelay(modifiedClip);
        break;
      case ModifierType.SMOOTH:
        this.applySmooth(modifiedClip);
        break;
      case ModifierType.NOISE:
        this.applyNoise(modifiedClip);
        break;
      case ModifierType.CUSTOM:
        if (this.customModifier) {
          const customClip = this.customModifier(modifiedClip, this.params);
          if (customClip) {
            return customClip;
          }
        }
        break;
    }

    // 触发变形应用事件
    this.eventEmitter.emit(SubClipModifierEventType.MODIFIER_APPLIED, {
      modifier: this,
      originalClip: clip,
      modifiedClip
    });

    if (this.debug) {
      console.log(`应用变形: ${this.name}, 类型: ${this.type}, 片段: ${clip.name}`);
    }

    return modifiedClip;
  }

  /**
   * 应用时间缩放变形
   * @param clip 动画片段
   */
  private applyTimeScale(clip: THREE.AnimationClip): void {
    const scale = this.params.scale !== undefined ? this.params.scale : 1.0;

    // 缩放时间
    for (const track of clip.tracks) {
      const times = track.times;
      for (let i = 0; i < times.length; i++) {
        times[i] /= scale;
      }
    }

    // 更新持续时间
    clip.duration /= scale;
  }

  /**
   * 应用反向播放变形
   * @param clip 动画片段
   */
  private applyReverse(clip: THREE.AnimationClip): void {
    // 遍历所有轨道
    for (const track of clip.tracks) {
      // 获取时间和值
      const times = track.times.slice();
      const values = track.values.slice();

      // 反转时间
      for (let i = 0; i < times.length; i++) {
        times[i] = clip.duration - times[times.length - 1 - i];
      }
      track.times = times;

      // 反转值
      const valueSize = track.getValueSize();
      const valueCount = values.length / valueSize;

      const newValues = new Float32Array(values.length);

      for (let i = 0; i < valueCount; i++) {
        const srcOffset = i * valueSize;
        const dstOffset = (valueCount - i - 1) * valueSize;

        for (let j = 0; j < valueSize; j++) {
          newValues[dstOffset + j] = values[srcOffset + j];
        }
      }

      track.values = newValues;
    }
  }

  /**
   * 应用循环变形
   * @param clip 动画片段
   */
  private applyLoop(clip: THREE.AnimationClip): void {
    const loopCount = this.params.count !== undefined ? this.params.count : 2;
    const blendTime = this.params.blendTime !== undefined ? this.params.blendTime : 0.1;

    // 原始持续时间
    const originalDuration = clip.duration;

    // 新的持续时间
    const newDuration = originalDuration * loopCount;

    // 遍历所有轨道
    for (const track of clip.tracks) {
      const originalTimes = track.times.slice();
      const originalValues = track.values.slice();
      const valueSize = track.getValueSize();
      const keyframeCount = originalTimes.length;

      // 创建新的时间和值数组
      const newTimes = new Float32Array(keyframeCount * loopCount);
      const newValues = new Float32Array(originalValues.length * loopCount);

      // 复制原始关键帧
      for (let i = 0; i < loopCount; i++) {
        for (let j = 0; j < keyframeCount; j++) {
          const newIndex = i * keyframeCount + j;
          newTimes[newIndex] = originalTimes[j] + i * originalDuration;

          for (let k = 0; k < valueSize; k++) {
            newValues[newIndex * valueSize + k] = originalValues[j * valueSize + k];
          }
        }
      }

      // 更新轨道
      track.times = newTimes;
      track.values = newValues;
    }

    // 更新持续时间
    clip.duration = newDuration;
  }

  /**
   * 应用镜像变形
   * @param clip 动画片段
   */
  private applyMirror(clip: THREE.AnimationClip): void {
    const axis = this.params.axis !== undefined ? this.params.axis : 'x';

    // 遍历所有轨道
    for (const track of clip.tracks) {
      // 只处理位置和旋转轨道
      if (track.name.endsWith('.position') || track.name.endsWith('.quaternion')) {
        const values = track.values;
        const valueSize = track.getValueSize();

        // 镜像变换
        for (let i = 0; i < values.length; i += valueSize) {
          if (track.name.endsWith('.position')) {
            // 位置轨道
            if (axis === 'x' || axis === 'all') values[i] *= -1;
            if (axis === 'y' || axis === 'all') values[i + 1] *= -1;
            if (axis === 'z' || axis === 'all') values[i + 2] *= -1;
          } else if (track.name.endsWith('.quaternion')) {
            // 四元数轨道
            if (axis === 'x' || axis === 'all') {
              values[i] *= -1;
              values[i + 3] *= -1;
            }
            if (axis === 'y' || axis === 'all') {
              values[i + 1] *= -1;
              values[i + 3] *= -1;
            }
            if (axis === 'z' || axis === 'all') {
              values[i + 2] *= -1;
              values[i + 3] *= -1;
            }
          }
        }
      }
    }
  }

  /**
   * 应用抖动变形
   * @param clip 动画片段
   */
  private applyJitter(clip: THREE.AnimationClip): void {
    const amount = this.params.amount !== undefined ? this.params.amount : 0.1;
    const frequency = this.params.frequency !== undefined ? this.params.frequency : 10;

    // 遍历所有轨道
    for (const track of clip.tracks) {
      // 只处理位置和旋转轨道
      if (track.name.endsWith('.position') || track.name.endsWith('.quaternion')) {
        const values = track.values;
        const valueSize = track.getValueSize();

        // 添加抖动
        for (let i = 0; i < values.length; i += valueSize) {
          if (track.name.endsWith('.position')) {
            // 位置轨道
            values[i] += (Math.random() * 2 - 1) * amount;
            values[i + 1] += (Math.random() * 2 - 1) * amount;
            values[i + 2] += (Math.random() * 2 - 1) * amount;
          } else if (track.name.endsWith('.quaternion')) {
            // 四元数轨道
            const jitterQuat = new THREE.Quaternion(
              (Math.random() * 2 - 1) * amount,
              (Math.random() * 2 - 1) * amount,
              (Math.random() * 2 - 1) * amount,
              1
            ).normalize();

            const q = new THREE.Quaternion(
              values[i],
              values[i + 1],
              values[i + 2],
              values[i + 3]
            );

            q.multiply(jitterQuat);

            values[i] = q.x;
            values[i + 1] = q.y;
            values[i + 2] = q.z;
            values[i + 3] = q.w;
          }
        }
      }
    }
  }

  /**
   * 应用延迟变形
   * @param clip 动画片段
   */
  private applyDelay(clip: THREE.AnimationClip): void {
    const delay = this.params.delay !== undefined ? this.params.delay : 1.0;

    // 遍历所有轨道
    for (const track of clip.tracks) {
      const times = track.times;
      for (let i = 0; i < times.length; i++) {
        times[i] += delay;
      }
    }

    // 更新持续时间
    clip.duration += delay;
  }

  /**
   * 应用平滑变形
   * @param clip 动画片段
   */
  private applySmooth(clip: THREE.AnimationClip): void {
    const strength = this.params.strength !== undefined ? this.params.strength : 0.5;

    // 遍历所有轨道
    for (const track of clip.tracks) {
      const values = track.values;
      const valueSize = track.getValueSize();
      const keyframeCount = values.length / valueSize;

      // 至少需要3个关键帧才能平滑
      if (keyframeCount < 3) continue;

      // 创建新的值数组
      const newValues = new Float32Array(values.length);

      // 复制第一个和最后一个关键帧
      for (let i = 0; i < valueSize; i++) {
        newValues[i] = values[i];
        newValues[values.length - valueSize + i] = values[values.length - valueSize + i];
      }

      // 平滑中间关键帧
      for (let i = 1; i < keyframeCount - 1; i++) {
        for (let j = 0; j < valueSize; j++) {
          const prev = values[(i - 1) * valueSize + j];
          const curr = values[i * valueSize + j];
          const next = values[(i + 1) * valueSize + j];

          // 计算平滑值
          const smoothed = curr + (prev + next - 2 * curr) * strength;
          newValues[i * valueSize + j] = smoothed;
        }
      }

      // 更新轨道
      track.values = newValues;
    }
  }

  /**
   * 应用噪声变形
   * @param clip 动画片段
   */
  private applyNoise(clip: THREE.AnimationClip): void {
    const amplitude = this.params.amplitude !== undefined ? this.params.amplitude : 0.1;
    const frequency = this.params.frequency !== undefined ? this.params.frequency : 1.0;
    const seed = this.params.seed !== undefined ? this.params.seed : Math.random() * 1000;

    // 简单的噪声函数
    const noise = (x: number, y: number, z: number) => {
      const X = Math.floor(x) & 255;
      const Y = Math.floor(y) & 255;
      const Z = Math.floor(z) & 255;

      x -= Math.floor(x);
      y -= Math.floor(y);
      z -= Math.floor(z);

      const u = this.fade(x);
      const v = this.fade(y);
      const w = this.fade(z);

      const A = (X + seed) & 255;
      const B = (Y + seed) & 255;
      const C = (Z + seed) & 255;

      return this.lerp(w,
        this.lerp(v,
          this.lerp(u, this.grad(A, x, y, z), this.grad(B, x - 1, y, z)),
          this.lerp(u, this.grad(C, x, y - 1, z), this.grad(A, x - 1, y - 1, z))
        ),
        this.lerp(v,
          this.lerp(u, this.grad(B, x, y, z - 1), this.grad(C, x - 1, y, z - 1)),
          this.lerp(u, this.grad(A, x, y - 1, z - 1), this.grad(B, x - 1, y - 1, z - 1))
        )
      );
    };

    // 遍历所有轨道
    for (const track of clip.tracks) {
      // 只处理位置轨道
      if (track.name.endsWith('.position')) {
        const values = track.values;
        const times = track.times;

        // 添加噪声
        for (let i = 0; i < values.length; i += 3) {
          const time = times[i / 3] * frequency;
          values[i] += noise(time, 0, 0) * amplitude;
          values[i + 1] += noise(0, time, 0) * amplitude;
          values[i + 2] += noise(0, 0, time) * amplitude;
        }
      }
    }
  }

  /**
   * 淡入淡出函数
   * @param t 参数
   * @returns 结果
   */
  private fade(t: number): number {
    return t * t * t * (t * (t * 6 - 15) + 10);
  }

  /**
   * 线性插值函数
   * @param t 参数
   * @param a 值1
   * @param b 值2
   * @returns 结果
   */
  private lerp(t: number, a: number, b: number): number {
    return a + t * (b - a);
  }

  /**
   * 梯度函数
   * @param hash 哈希值
   * @param x x坐标
   * @param y y坐标
   * @param z z坐标
   * @returns 结果
   */
  private grad(hash: number, x: number, y: number, z: number): number {
    const h = hash & 15;
    const u = h < 8 ? x : y;
    const v = h < 4 ? y : h === 12 || h === 14 ? x : z;
    return ((h & 1) === 0 ? u : -u) + ((h & 2) === 0 ? v : -v);
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public addEventListener(event: SubClipModifierEventType, listener: (data: any) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public removeEventListener(event: SubClipModifierEventType, listener: (data: any) => void): void {
    this.eventEmitter.off(event, listener);
  }
}
