# 组件参考文档

本文档提供了DL（Digital Learning）引擎编辑器中所有内置组件的详细参考信息，包括组件的功能、属性、方法和使用示例。

## 目录

- [变换组件](#变换组件)
- [渲染组件](#渲染组件)
- [物理组件](#物理组件)
- [动画组件](#动画组件)
- [音频组件](#音频组件)
- [交互组件](#交互组件)
- [UI组件](#ui组件)
- [脚本组件](#脚本组件)
- [特效组件](#特效组件)
- [网络组件](#网络组件)

## 变换组件

### TransformComponent

变换组件是最基本的组件，定义了实体在3D空间中的位置、旋转和缩放。所有实体默认都有一个变换组件，它是构建场景层次结构的基础。

#### 属性

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| position | Vector3 | (0, 0, 0) | 实体在世界空间中的位置 |
| rotation | Quaternion | (0, 0, 0, 1) | 实体在世界空间中的旋转 |
| eulerAngles | Vector3 | (0, 0, 0) | 实体在世界空间中的欧拉角旋转（度） |
| scale | Vector3 | (1, 1, 1) | 实体在世界空间中的缩放 |
| localPosition | Vector3 | (0, 0, 0) | 实体在局部空间中的位置 |
| localRotation | Quaternion | (0, 0, 0, 1) | 实体在局部空间中的旋转 |
| localEulerAngles | Vector3 | (0, 0, 0) | 实体在局部空间中的欧拉角旋转（度） |
| localScale | Vector3 | (1, 1, 1) | 实体在局部空间中的缩放 |
| parent | Entity | null | 实体的父级 |
| children | Entity[] | [] | 实体的子级列表 |
| forward | Vector3 | (0, 0, 1) | 实体的前方向（只读） |
| right | Vector3 | (1, 0, 0) | 实体的右方向（只读） |
| up | Vector3 | (0, 1, 0) | 实体的上方向（只读） |
| worldMatrix | Matrix4 | Identity | 世界变换矩阵（只读） |
| localMatrix | Matrix4 | Identity | 局部变换矩阵（只读） |
| hasChanged | boolean | false | 变换是否已更改（只读） |

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| translate | (translation: Vector3, relativeTo?: Space) | void | 移动实体 |
| rotate | (eulerAngles: Vector3, relativeTo?: Space) | void | 旋转实体 |
| rotateAround | (point: Vector3, axis: Vector3, angle: number) | void | 绕指定点和轴旋转实体 |
| lookAt | (target: Vector3, up?: Vector3) | void | 使实体朝向目标点 |
| setParent | (parent: Entity, worldPositionStays?: boolean) | void | 设置实体的父级 |
| getLocalToWorldMatrix | () | Matrix4 | 获取局部到世界的变换矩阵 |
| getWorldToLocalMatrix | () | Matrix4 | 获取世界到局部的变换矩阵 |
| transformPoint | (point: Vector3) | Vector3 | 将点从局部空间转换到世界空间 |
| inverseTransformPoint | (point: Vector3) | Vector3 | 将点从世界空间转换到局部空间 |
| transformDirection | (direction: Vector3) | Vector3 | 将方向从局部空间转换到世界空间 |
| inverseTransformDirection | (direction: Vector3) | Vector3 | 将方向从世界空间转换到局部空间 |
| detachChildren | () | void | 分离所有子级 |
| resetLocalTransform | () | void | 重置局部变换为默认值 |

#### 事件

| 事件名 | 参数 | 描述 |
|-------|------|------|
| onTransformChanged | transform | 变换发生变化时触发 |
| onParentChanged | oldParent, newParent | 父级变化时触发 |
| onChildAdded | child | 添加子级时触发 |
| onChildRemoved | child | 移除子级时触发 |

#### 使用示例

```typescript
// 创建一个实体并添加变换组件
const entity = world.createEntity('MyEntity');
const transform = entity.transform; // 所有实体默认都有变换组件

// 设置位置、旋转和缩放
transform.position = new Vector3(0, 1, 0);
transform.eulerAngles = new Vector3(0, 45, 0);
transform.scale = new Vector3(2, 2, 2);

// 移动和旋转实体
transform.translate(new Vector3(0, 0, 1), Space.World);
transform.rotate(new Vector3(0, 90, 0), Space.Self);

// 使实体朝向目标
transform.lookAt(new Vector3(10, 0, 0));

// 绕点旋转
transform.rotateAround(new Vector3(0, 0, 0), Vector3.up, 45);

// 创建父子关系
const childEntity = world.createEntity('ChildEntity');
childEntity.transform.setParent(entity.transform);
childEntity.transform.localPosition = new Vector3(0, 1, 0); // 相对于父级的位置

// 坐标转换
const worldPoint = transform.transformPoint(new Vector3(1, 0, 0));
const localPoint = transform.inverseTransformPoint(new Vector3(10, 0, 0));

// 监听变换事件
transform.onTransformChanged.add((transform) => {
  console.log('变换已更改:', transform.position);
});
```

![变换组件](../../assets/images/transform-component.png)

#### 最佳实践

- **使用局部变换**：在处理父子关系时，优先使用localPosition、localRotation和localScale，这样可以更容易地相对于父级进行定位。
- **避免缩放不均匀**：非均匀缩放（xyz不同值）可能导致物理和碰撞检测问题，尽量使用均匀缩放。
- **使用欧拉角注意事项**：欧拉角容易出现万向节锁定问题，对于复杂旋转，考虑使用四元数。
- **性能考虑**：频繁更改变换会触发多次事件和矩阵重计算，可以批量更改后再通知系统。
- **缓存变换结果**：如果需要多次使用同一个变换结果，应该缓存结果而不是重复计算。

#### 常见问题

**问题**：为什么我的子对象位置不正确？
**解答**：确保您使用的是localPosition而不是position来设置相对于父级的位置。

**问题**：旋转对象时出现奇怪的翻转？
**解答**：这可能是欧拉角的万向节锁定问题，尝试使用四元数旋转或调整旋转顺序。

**问题**：变换组件的更改没有立即反映在场景中？
**解答**：某些情况下，变换更改可能需要等到下一帧才会更新。如果需要立即更新，可以调用`transform.markAsChanged()`。

## 渲染组件

### MeshRendererComponent

网格渲染器组件负责渲染3D模型。它将网格几何体与材质结合，在场景中显示3D对象。

#### 属性

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| mesh | Mesh | null | 要渲染的网格 |
| materials | Material[] | [] | 用于渲染的材质数组 |
| castShadows | boolean | true | 是否投射阴影 |
| receiveShadows | boolean | true | 是否接收阴影 |
| visible | boolean | true | 是否可见 |
| renderOrder | number | 0 | 渲染顺序（数值越大越后渲染） |
| frustumCulled | boolean | true | 是否进行视锥体剔除 |
| renderLayer | number | 0 | 渲染层（用于选择性渲染） |
| lightmapIndex | number | -1 | 光照贴图索引 |
| lightmapScaleOffset | Vector4 | (1,1,0,0) | 光照贴图的缩放和偏移 |
| motionVectorGenerationMode | enum | Object | 运动矢量生成模式 |
| reflectionProbeUsage | enum | BlendProbes | 反射探针使用方式 |
| probeAnchor | Transform | null | 反射探针锚点 |
| staticShadowCaster | boolean | false | 是否为静态阴影投射器 |
| skinnedMotionVectors | boolean | true | 是否生成蒙皮运动矢量 |

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| setMaterial | (material: Material, index?: number) | void | 设置指定索引的材质 |
| getMaterial | (index?: number) | Material | 获取指定索引的材质 |
| setMaterialProperty | (propertyName: string, value: any, materialIndex?: number) | void | 设置材质属性 |
| getMaterialProperty | (propertyName: string, materialIndex?: number) | any | 获取材质属性 |
| getBounds | () | Bounds | 获取渲染边界 |
| getSubMeshCount | () | number | 获取子网格数量 |
| getSharedMaterials | () | Material[] | 获取共享材质数组 |
| setSharedMaterials | (materials: Material[]) | void | 设置共享材质数组 |
| getSharedMaterial | (index: number) | Material | 获取指定索引的共享材质 |
| setSharedMaterial | (index: number, material: Material) | void | 设置指定索引的共享材质 |
| getLightmapTilingOffset | () | Vector4 | 获取光照贴图的平铺和偏移 |
| setLightmapTilingOffset | (tiling: Vector2, offset: Vector2) | void | 设置光照贴图的平铺和偏移 |
| forceRenderingOff | (off: boolean) | void | 强制关闭渲染 |
| updateGIMaterials | () | void | 更新全局光照材质 |

#### 事件

| 事件名 | 参数 | 描述 |
|-------|------|------|
| onVisibilityChanged | visible | 可见性变化时触发 |
| onMaterialChanged | materialIndex, oldMaterial, newMaterial | 材质变化时触发 |
| onMeshChanged | oldMesh, newMesh | 网格变化时触发 |
| onBoundsChanged | bounds | 边界变化时触发 |

#### 使用示例

```typescript
// 创建一个实体并添加网格渲染器组件
const entity = world.createEntity('MyModel');
const meshRenderer = entity.addComponent(MeshRendererComponent);

// 设置网格和材质
meshRenderer.mesh = AssetManager.getMesh('models/cube.mesh');
meshRenderer.setMaterial(AssetManager.getMaterial('materials/standard.mat'));

// 配置渲染属性
meshRenderer.castShadows = true;
meshRenderer.receiveShadows = true;
meshRenderer.renderLayer = 1;

// 设置材质属性
meshRenderer.setMaterialProperty('color', new Color(1, 0, 0), 0);

// 处理多材质模型
if (meshRenderer.getSubMeshCount() > 1) {
  // 为每个子网格设置不同的材质
  for (let i = 0; i < meshRenderer.getSubMeshCount(); i++) {
    const material = AssetManager.getMaterial('materials/standard.mat').clone();
    material.setColor('color', new Color(Math.random(), Math.random(), Math.random()));
    meshRenderer.setMaterial(material, i);
  }
}

// 监听材质变化事件
meshRenderer.onMaterialChanged.add((materialIndex, oldMaterial, newMaterial) => {
  console.log(`材质${materialIndex}已从${oldMaterial.name}更改为${newMaterial.name}`);
});

// 获取渲染边界
const bounds = meshRenderer.getBounds();
console.log(`模型边界: 中心=${bounds.center}, 大小=${bounds.size}`);
```

![网格渲染器组件](../../assets/images/mesh-renderer-component.png)

#### 最佳实践

- **材质实例化**：如果需要修改共享材质的属性，应该先克隆材质再修改，避免影响其他使用相同材质的对象。
- **批处理优化**：对于静态对象，尽量使用相同的材质，以便引擎可以进行批处理优化。
- **LOD设置**：对于远距离可见的复杂模型，考虑使用LOD（细节层次）系统来提高性能。
- **光照贴图**：对于静态场景，使用光照贴图可以显著提高渲染性能。
- **视锥体剔除**：对于大型场景，确保启用视锥体剔除，避免渲染不可见对象。

### SkinnedMeshRendererComponent

蒙皮网格渲染器组件用于渲染带有骨骼动画的3D模型。

#### 属性

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| mesh | Mesh | null | 要渲染的蒙皮网格 |
| materials | Material[] | [] | 用于渲染的材质数组 |
| bones | Transform[] | [] | 骨骼变换数组 |
| rootBone | Transform | null | 根骨骼 |
| quality | enum | Auto | 蒙皮质量 |
| updateWhenOffscreen | boolean | false | 不可见时是否更新 |
| forceMatrixRecalculationPerRender | boolean | false | 每帧强制重新计算矩阵 |
| castShadows | boolean | true | 是否投射阴影 |
| receiveShadows | boolean | true | 是否接收阴影 |
| visible | boolean | true | 是否可见 |
| renderOrder | number | 0 | 渲染顺序 |
| frustumCulled | boolean | true | 是否进行视锥体剔除 |
| renderLayer | number | 0 | 渲染层 |
| localBounds | Bounds | null | 局部边界 |
| sharedMesh | Mesh | null | 共享网格 |

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| setMaterial | (material: Material, index?: number) | void | 设置指定索引的材质 |
| getMaterial | (index?: number) | Material | 获取指定索引的材质 |
| getBounds | () | Bounds | 获取渲染边界 |
| getBlendShapeWeight | (index: number) | number | 获取混合形状权重 |
| setBlendShapeWeight | (index: number, value: number) | void | 设置混合形状权重 |
| getBoneTransform | (index: number) | Transform | 获取骨骼变换 |
| setBoneTransform | (index: number, transform: Transform) | void | 设置骨骼变换 |
| updateBoneMatrices | () | void | 更新骨骼矩阵 |
| calculateBounds | () | void | 计算边界 |

#### 使用示例

```typescript
// 创建一个实体并添加蒙皮网格渲染器组件
const entity = world.createEntity('Character');
const skinnedMeshRenderer = entity.addComponent(SkinnedMeshRendererComponent);

// 设置网格和材质
skinnedMeshRenderer.mesh = AssetManager.getMesh('models/character.mesh');
skinnedMeshRenderer.setMaterial(AssetManager.getMaterial('materials/character.mat'));

// 设置骨骼
const skeleton = entity.findChild('Skeleton');
if (skeleton) {
  // 设置根骨骼
  skinnedMeshRenderer.rootBone = skeleton.transform;

  // 收集所有骨骼
  const bones: Transform[] = [];
  const collectBones = (transform: Transform) => {
    bones.push(transform);
    transform.children.forEach(child => collectBones(child));
  };
  collectBones(skeleton.transform);

  // 设置骨骼数组
  skinnedMeshRenderer.bones = bones;
}

// 设置混合形状权重（表情等）
if (skinnedMeshRenderer.mesh.blendShapeCount > 0) {
  // 设置"微笑"表情的权重
  const smileIndex = skinnedMeshRenderer.mesh.getBlendShapeIndex('Smile');
  if (smileIndex !== -1) {
    skinnedMeshRenderer.setBlendShapeWeight(smileIndex, 100); // 0-100的值
  }
}
```

![蒙皮网格渲染器组件](../../assets/images/skinned-mesh-renderer-component.png)

#### 最佳实践

- **骨骼层次结构**：确保骨骼层次结构正确，根骨骼应该是骨架的最顶层节点。
- **性能优化**：蒙皮网格渲染比普通网格渲染更消耗性能，对于远处的角色考虑使用LOD或降低骨骼数量。
- **混合形状使用**：混合形状（BlendShape）适合用于面部表情和小的形变，不适合大幅度的形变。
- **骨骼数量限制**：尽量控制骨骼数量，过多的骨骼会影响性能，特别是在移动平台上。
- **GPU蒙皮**：在支持的平台上，启用GPU蒙皮可以显著提高性能。

### ParticleSystemComponent

粒子系统组件用于创建各种粒子效果，如火焰、烟雾、水花等。

#### 属性

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| duration | number | 5.0 | 粒子系统持续时间（秒） |
| looping | boolean | true | 是否循环播放 |
| prewarm | boolean | false | 是否预热（从已运行状态开始） |
| startDelay | number | 0.0 | 开始延迟时间（秒） |
| startLifetime | number | 5.0 | 粒子生命周期（秒） |
| startSpeed | number | 5.0 | 粒子初始速度 |
| startSize | number | 1.0 | 粒子初始大小 |
| startRotation | Vector3 | (0,0,0) | 粒子初始旋转 |
| startColor | Color | (1,1,1,1) | 粒子初始颜色 |
| gravityModifier | number | 0.0 | 重力修改器 |
| simulationSpace | enum | Local | 模拟空间（局部/世界） |
| simulationSpeed | number | 1.0 | 模拟速度 |
| playOnAwake | boolean | true | 是否在唤醒时自动播放 |
| maxParticles | number | 1000 | 最大粒子数量 |
| emissionRate | number | 10 | 每秒发射的粒子数量 |
| shape | enum | Cone | 发射形状 |
| material | Material | null | 粒子材质 |
| renderMode | enum | Billboard | 渲染模式 |
| sortingMode | enum | None | 排序模式 |
| maskInteraction | enum | None | 遮罩交互方式 |
| useAutoRandomSeed | boolean | true | 是否使用自动随机种子 |
| randomSeed | number | 0 | 随机种子 |

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
|-------|------|-------|------|
| play | (withChildren?: boolean) | void | 播放粒子系统 |
| pause | (withChildren?: boolean) | void | 暂停粒子系统 |
| stop | (withChildren?: boolean, clearParticles?: boolean) | void | 停止粒子系统 |
| clear | (withChildren?: boolean) | void | 清除所有粒子 |
| isPlaying | () | boolean | 检查是否正在播放 |
| isPaused | () | boolean | 检查是否已暂停 |
| isStopped | () | boolean | 检查是否已停止 |
| emit | (count: number) | void | 立即发射指定数量的粒子 |
| setEmissionRate | (rate: number) | void | 设置发射速率 |
| getParticleCount | () | number | 获取当前粒子数量 |
| setCustomParticleData | (data: Float32Array, dimension: number) | void | 设置自定义粒子数据 |
| getCustomParticleData | (data: Float32Array, dimension: number) | void | 获取自定义粒子数据 |
| setParticles | (particles: Particle[], size: number, offset: number) | number | 设置粒子数组 |
| getParticles | (particles: Particle[], size: number, offset: number) | number | 获取粒子数组 |
| trigger | (name: string) | void | 触发子发射器 |

#### 使用示例

```typescript
// 创建一个实体并添加粒子系统组件
const entity = world.createEntity('FireEffect');
const particleSystem = entity.addComponent(ParticleSystemComponent);

// 配置基本属性
particleSystem.duration = 3.0;
particleSystem.looping = true;
particleSystem.startLifetime = 2.0;
particleSystem.startSpeed = 2.0;
particleSystem.startSize = 0.5;
particleSystem.startColor = new Color(1, 0.5, 0, 0.8); // 橙色，半透明

// 设置发射器
particleSystem.shape = ParticleSystemShapeType.Cone;
particleSystem.emissionRate = 20; // 每秒发射20个粒子

// 设置材质
particleSystem.material = AssetManager.getMaterial('materials/particle/fire.mat');
particleSystem.renderMode = ParticleSystemRenderMode.Billboard;

// 添加颜色随时间变化模块
const colorOverLifetime = particleSystem.colorOverLifetime;
colorOverLifetime.enabled = true;
colorOverLifetime.color = new GradientColor();
colorOverLifetime.color.addKey(0.0, new Color(1, 0.5, 0, 0.8)); // 开始：橙色
colorOverLifetime.color.addKey(0.5, new Color(1, 0.2, 0, 0.6)); // 中间：红色
colorOverLifetime.color.addKey(1.0, new Color(0.3, 0.3, 0.3, 0)); // 结束：灰色，透明

// 添加大小随时间变化模块
const sizeOverLifetime = particleSystem.sizeOverLifetime;
sizeOverLifetime.enabled = true;
sizeOverLifetime.size = new CurveFloat();
sizeOverLifetime.size.addKey(0.0, 0.2); // 开始：小
sizeOverLifetime.size.addKey(0.3, 1.0); // 中间：最大
sizeOverLifetime.size.addKey(1.0, 0.0); // 结束：消失

// 播放粒子系统
particleSystem.play();

// 监听事件
entity.onUpdate.add((deltaTime) => {
  // 根据游戏逻辑动态调整粒子系统
  if (isPlayerNearby()) {
    particleSystem.emissionRate = 40; // 增加发射率
  } else {
    particleSystem.emissionRate = 20; // 恢复正常发射率
  }
});
```

![粒子系统组件](../../assets/images/particle-system-component.png)
