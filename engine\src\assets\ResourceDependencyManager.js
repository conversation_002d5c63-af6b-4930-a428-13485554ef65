"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResourceDependencyManager = exports.DependencyType = void 0;
/**
 * 资源依赖管理器
 * 用于管理资源之间的依赖关系
 */
var EventEmitter_1 = require("../utils/EventEmitter");
/**
 * 依赖类型
 */
var DependencyType;
(function (DependencyType) {
    /** 强依赖（必须加载） */
    DependencyType["STRONG"] = "strong";
    /** 弱依赖（可选加载） */
    DependencyType["WEAK"] = "weak";
})(DependencyType || (exports.DependencyType = DependencyType = {}));
/**
 * 资源依赖管理器
 */
var ResourceDependencyManager = /** @class */ (function (_super) {
    __extends(ResourceDependencyManager, _super);
    /**
     * 创建资源依赖管理器实例
     */
    function ResourceDependencyManager() {
        var _this = _super.call(this) || this;
        /** 依赖映射（资源ID -> 依赖资源ID数组） */
        _this.dependencies = new Map();
        /** 反向依赖映射（被依赖资源ID -> 依赖它的资源ID数组） */
        _this.reverseDependencies = new Map();
        /** 是否已初始化 */
        _this.initialized = false;
        return _this;
    }
    /**
     * 初始化资源依赖管理器
     */
    ResourceDependencyManager.prototype.initialize = function () {
        if (this.initialized) {
            return;
        }
        this.initialized = true;
        this.emit('initialized');
    };
    /**
     * 添加依赖关系
     * @param resourceId 资源ID
     * @param dependencyId 依赖资源ID
     * @param type 依赖类型
     */
    ResourceDependencyManager.prototype.addDependency = function (resourceId, dependencyId, type) {
        if (type === void 0) { type = DependencyType.STRONG; }
        // 如果资源ID和依赖资源ID相同，则忽略
        if (resourceId === dependencyId) {
            return;
        }
        // 获取资源的依赖列表
        var deps = this.dependencies.get(resourceId);
        if (!deps) {
            deps = [];
            this.dependencies.set(resourceId, deps);
        }
        // 检查是否已存在相同依赖
        var existingDep = deps.find(function (dep) { return dep.id === dependencyId; });
        if (existingDep) {
            // 如果已存在，则更新依赖类型（强依赖优先）
            if (type === DependencyType.STRONG) {
                existingDep.type = DependencyType.STRONG;
            }
        }
        else {
            // 添加新依赖
            deps.push({ id: dependencyId, type: type });
            // 更新反向依赖
            var revDeps = this.reverseDependencies.get(dependencyId);
            if (!revDeps) {
                revDeps = [];
                this.reverseDependencies.set(dependencyId, revDeps);
            }
            if (!revDeps.includes(resourceId)) {
                revDeps.push(resourceId);
            }
            // 发出依赖添加事件
            this.emit('dependencyAdded', { resourceId: resourceId, dependencyId: dependencyId, type: type });
        }
    };
    /**
     * 移除依赖关系
     * @param resourceId 资源ID
     * @param dependencyId 依赖资源ID
     * @returns 是否成功移除
     */
    ResourceDependencyManager.prototype.removeDependency = function (resourceId, dependencyId) {
        // 获取资源的依赖列表
        var deps = this.dependencies.get(resourceId);
        if (!deps) {
            return false;
        }
        // 查找依赖索引
        var index = deps.findIndex(function (dep) { return dep.id === dependencyId; });
        if (index === -1) {
            return false;
        }
        // 移除依赖
        deps.splice(index, 1);
        // 如果依赖列表为空，则移除映射
        if (deps.length === 0) {
            this.dependencies.delete(resourceId);
        }
        // 更新反向依赖
        var revDeps = this.reverseDependencies.get(dependencyId);
        if (revDeps) {
            var revIndex = revDeps.indexOf(resourceId);
            if (revIndex !== -1) {
                revDeps.splice(revIndex, 1);
                // 如果反向依赖列表为空，则移除映射
                if (revDeps.length === 0) {
                    this.reverseDependencies.delete(dependencyId);
                }
            }
        }
        // 发出依赖移除事件
        this.emit('dependencyRemoved', { resourceId: resourceId, dependencyId: dependencyId });
        return true;
    };
    /**
     * 获取资源的依赖
     * @param resourceId 资源ID
     * @returns 依赖信息数组
     */
    ResourceDependencyManager.prototype.getDependencies = function (resourceId) {
        return this.dependencies.get(resourceId) || [];
    };
    /**
     * 获取资源的强依赖
     * @param resourceId 资源ID
     * @returns 依赖资源ID数组
     */
    ResourceDependencyManager.prototype.getStrongDependencies = function (resourceId) {
        var deps = this.getDependencies(resourceId);
        return deps.filter(function (dep) { return dep.type === DependencyType.STRONG; }).map(function (dep) { return dep.id; });
    };
    /**
     * 获取资源的弱依赖
     * @param resourceId 资源ID
     * @returns 依赖资源ID数组
     */
    ResourceDependencyManager.prototype.getWeakDependencies = function (resourceId) {
        var deps = this.getDependencies(resourceId);
        return deps.filter(function (dep) { return dep.type === DependencyType.WEAK; }).map(function (dep) { return dep.id; });
    };
    /**
     * 获取依赖资源的资源
     * @param dependencyId 依赖资源ID
     * @returns 资源ID数组
     */
    ResourceDependencyManager.prototype.getReverseDependencies = function (dependencyId) {
        return this.reverseDependencies.get(dependencyId) || [];
    };
    /**
     * 获取资源的所有依赖（包括间接依赖）
     * @param resourceId 资源ID
     * @param includeWeak 是否包含弱依赖
     * @returns 依赖资源ID数组
     */
    ResourceDependencyManager.prototype.getAllDependencies = function (resourceId, includeWeak) {
        var _this = this;
        if (includeWeak === void 0) { includeWeak = false; }
        var result = new Set();
        // 递归获取依赖
        var collectDependencies = function (id) {
            var deps = _this.getDependencies(id);
            for (var _i = 0, deps_1 = deps; _i < deps_1.length; _i++) {
                var dep = deps_1[_i];
                // 如果不包含弱依赖且当前依赖是弱依赖，则跳过
                if (!includeWeak && dep.type === DependencyType.WEAK) {
                    continue;
                }
                // 如果已经添加过，则跳过
                if (result.has(dep.id)) {
                    continue;
                }
                // 添加依赖
                result.add(dep.id);
                // 递归获取依赖的依赖
                collectDependencies(dep.id);
            }
        };
        collectDependencies(resourceId);
        return Array.from(result);
    };
    /**
     * 检查是否存在循环依赖
     * @param resourceId 资源ID
     * @returns 是否存在循环依赖
     */
    ResourceDependencyManager.prototype.hasCircularDependency = function (resourceId) {
        var _this = this;
        var visited = new Set();
        var path = new Set();
        // 深度优先搜索检查循环依赖
        var dfs = function (id) {
            // 如果已经在当前路径中，则存在循环依赖
            if (path.has(id)) {
                return true;
            }
            // 如果已经访问过且不存在循环依赖，则跳过
            if (visited.has(id)) {
                return false;
            }
            // 标记为已访问
            visited.add(id);
            // 添加到当前路径
            path.add(id);
            // 检查所有依赖
            var deps = _this.getDependencies(id);
            for (var _i = 0, deps_2 = deps; _i < deps_2.length; _i++) {
                var dep = deps_2[_i];
                if (dfs(dep.id)) {
                    return true;
                }
            }
            // 从当前路径中移除
            path.delete(id);
            return false;
        };
        return dfs(resourceId);
    };
    /**
     * 获取资源的依赖树
     * @param resourceId 资源ID
     * @param includeWeak 是否包含弱依赖
     * @returns 依赖树
     */
    ResourceDependencyManager.prototype.getDependencyTree = function (resourceId, includeWeak) {
        var _this = this;
        if (includeWeak === void 0) { includeWeak = false; }
        var tree = {
            id: resourceId,
            dependencies: [],
        };
        // 已处理的资源ID集合，用于避免循环依赖
        var processed = new Set();
        // 递归构建依赖树
        var buildTree = function (node, id) {
            // 如果已经处理过，则跳过
            if (processed.has(id)) {
                return;
            }
            // 标记为已处理
            processed.add(id);
            // 获取依赖
            var deps = _this.getDependencies(id);
            for (var _i = 0, deps_3 = deps; _i < deps_3.length; _i++) {
                var dep = deps_3[_i];
                // 如果不包含弱依赖且当前依赖是弱依赖，则跳过
                if (!includeWeak && dep.type === DependencyType.WEAK) {
                    continue;
                }
                // 创建依赖节点
                var depNode = {
                    id: dep.id,
                    type: dep.type,
                    dependencies: [],
                };
                // 添加到当前节点的依赖列表
                node.dependencies.push(depNode);
                // 递归构建依赖的依赖
                buildTree(depNode, dep.id);
            }
        };
        buildTree(tree, resourceId);
        return tree;
    };
    /**
     * 获取资源的依赖排序（拓扑排序）
     * @param resourceId 资源ID
     * @param includeWeak 是否包含弱依赖
     * @returns 排序后的资源ID数组（从依赖到被依赖）
     */
    ResourceDependencyManager.prototype.getDependencyOrder = function (resourceId, includeWeak) {
        var _this = this;
        if (includeWeak === void 0) { includeWeak = false; }
        var result = [];
        var visited = new Set();
        // 深度优先搜索进行拓扑排序
        var dfs = function (id) {
            // 如果已经访问过，则跳过
            if (visited.has(id)) {
                return;
            }
            // 标记为已访问
            visited.add(id);
            // 获取依赖
            var deps = _this.getDependencies(id);
            for (var _i = 0, deps_4 = deps; _i < deps_4.length; _i++) {
                var dep = deps_4[_i];
                // 如果不包含弱依赖且当前依赖是弱依赖，则跳过
                if (!includeWeak && dep.type === DependencyType.WEAK) {
                    continue;
                }
                // 递归处理依赖
                dfs(dep.id);
            }
            // 将当前资源添加到结果中
            result.push(id);
        };
        dfs(resourceId);
        return result;
    };
    /**
     * 清空所有依赖关系
     */
    ResourceDependencyManager.prototype.clear = function () {
        this.dependencies.clear();
        this.reverseDependencies.clear();
        // 发出清空事件
        this.emit('cleared');
    };
    /**
     * 销毁资源依赖管理器
     */
    ResourceDependencyManager.prototype.dispose = function () {
        // 清空所有依赖关系
        this.clear();
        // 移除所有事件监听器
        this.removeAllListeners();
        this.initialized = false;
    };
    return ResourceDependencyManager;
}(EventEmitter_1.EventEmitter));
exports.ResourceDependencyManager = ResourceDependencyManager;
