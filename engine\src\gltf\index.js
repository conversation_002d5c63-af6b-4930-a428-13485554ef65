"use strict";
/**
 * GLTF模块
 * 导出所有GLTF相关的类和接口
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.GLTFSystem = exports.AnimationEventType = exports.AnimationBlendMode = exports.AnimationLoopMode = exports.AnimationState = exports.GLTFAnimationComponent = exports.GLTFNodeComponent = exports.GLTFModelComponent = exports.GLTFExporter = exports.GLTFLoader = void 0;
// 导出GLTF加载器
var GLTFLoader_1 = require("./GLTFLoader");
Object.defineProperty(exports, "GLTFLoader", { enumerable: true, get: function () { return GLTFLoader_1.GLTFLoader; } });
// 导出GLTF导出器
var GLTFExporter_1 = require("./GLTFExporter");
Object.defineProperty(exports, "GLTFExporter", { enumerable: true, get: function () { return GLTFExporter_1.GLTFExporter; } });
// 导出GLTF组件
var GLTFModelComponent_1 = require("./components/GLTFModelComponent");
Object.defineProperty(exports, "GLTFModelComponent", { enumerable: true, get: function () { return GLTFModelComponent_1.GLTFModelComponent; } });
var GLTFNodeComponent_1 = require("./components/GLTFNodeComponent");
Object.defineProperty(exports, "GLTFNodeComponent", { enumerable: true, get: function () { return GLTFNodeComponent_1.GLTFNodeComponent; } });
var GLTFAnimationComponent_1 = require("./components/GLTFAnimationComponent");
Object.defineProperty(exports, "GLTFAnimationComponent", { enumerable: true, get: function () { return GLTFAnimationComponent_1.GLTFAnimationComponent; } });
var GLTFAnimationComponent_2 = require("./components/GLTFAnimationComponent");
Object.defineProperty(exports, "AnimationState", { enumerable: true, get: function () { return GLTFAnimationComponent_2.AnimationState; } });
Object.defineProperty(exports, "AnimationLoopMode", { enumerable: true, get: function () { return GLTFAnimationComponent_2.AnimationLoopMode; } });
Object.defineProperty(exports, "AnimationBlendMode", { enumerable: true, get: function () { return GLTFAnimationComponent_2.AnimationBlendMode; } });
Object.defineProperty(exports, "AnimationEventType", { enumerable: true, get: function () { return GLTFAnimationComponent_2.AnimationEventType; } });
// 导出GLTF系统
var GLTFSystem_1 = require("./systems/GLTFSystem");
Object.defineProperty(exports, "GLTFSystem", { enumerable: true, get: function () { return GLTFSystem_1.GLTFSystem; } });
