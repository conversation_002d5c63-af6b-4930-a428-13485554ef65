/**
 * 基于情感的动画生成器
 * 根据文本情感分析生成面部动画
 */
import { FacialExpressionType, VisemeType } from '../FacialAnimation';
import { FacialAnimationClip, FacialAnimationKeyframe } from '../FacialAnimationEditor';
import { IAIAnimationModel, AnimationGenerationRequest, AnimationGenerationResult, EmotionAnalysisResult } from './IAIAnimationModel';

/**
 * 情感到表情的映射
 */
const EMOTION_TO_EXPRESSION_MAP: { [key: string]: FacialExpressionType } = {
  'happy': FacialExpressionType.HAPPY,
  'sad': FacialExpressionType.SAD,
  'angry': FacialExpressionType.ANGRY,
  'surprised': FacialExpressionType.SURPRISED,
  'fear': FacialExpressionType.FEAR,
  'disgust': FacialExpressionType.DISGUST,
  'neutral': FacialExpressionType.NEUTRAL,
  'joy': FacialExpressionType.HAPPY,
  'sorrow': FacialExpressionType.SAD,
  'rage': FacialExpressionType.ANGRY,
  'shock': FacialExpressionType.SURPRISED,
  'terror': FacialExpressionType.FEAR
};

/**
 * 基于情感的动画生成器
 */
export class EmotionBasedAnimationGenerator {
  /** AI模型 */
  private aiModel: IAIAnimationModel;
  /** 是否启用调试 */
  private debug: boolean;

  /**
   * 构造函数
   * @param aiModel AI模型
   * @param debug 是否启用调试
   */
  constructor(aiModel: IAIAnimationModel, debug: boolean = false) {
    this.aiModel = aiModel;
    this.debug = debug;
  }

  /**
   * 生成面部动画
   * @param request 生成请求
   * @returns 生成结果
   */
  public async generateFacialAnimation(request: AnimationGenerationRequest): Promise<AnimationGenerationResult> {
    try {
      // 分析文本情感
      const emotionResult = await this.aiModel.analyzeEmotion(request.prompt);

      if (this.debug) {
        console.log('情感分析结果:', emotionResult);
      }

      // 创建面部动画片段
      const clip = await this.createEmotionBasedFacialClip(request, emotionResult);

      return {
        id: request.id,
        success: true,
        clip,
        generationTime: 0,
        userData: request.userData
      };
    } catch (error) {
      if (this.debug) {
        console.error('生成面部动画失败:', error);
      }

      return {
        id: request.id,
        success: false,
        error: error instanceof Error ? error.message : String(error),
        userData: request.userData
      };
    }
  }

  /**
   * 创建基于情感的面部动画片段
   * @param request 生成请求
   * @param emotionResult 情感分析结果
   * @returns 面部动画片段
   */
  private async createEmotionBasedFacialClip(
    request: AnimationGenerationRequest,
    emotionResult: EmotionAnalysisResult
  ): Promise<FacialAnimationClip> {
    // 获取主要表情
    const primaryExpression = this.mapEmotionToExpression(emotionResult.primaryEmotion);

    // 创建动画片段
    const clip: FacialAnimationClip = {
      name: request.prompt,
      duration: request.duration,
      loop: request.loop,
      keyframes: []
    };

    // 根据情感强度设置表情权重
    const expressionWeight = Math.min(1.0, emotionResult.intensity);

    // 创建关键帧
    const keyframeCount = Math.max(3, Math.ceil(request.duration * 2)); // 每秒至少2帧

    // 添加起始关键帧（中性表情）
    clip.keyframes.push({
      time: 0,
      expression: FacialExpressionType.NEUTRAL,
      expressionWeight: 1.0
    });

    // 添加中间关键帧（主要表情）
    const middleTime = request.duration / 2;
    clip.keyframes.push({
      time: middleTime,
      expression: primaryExpression,
      expressionWeight: expressionWeight
    });

    // 添加结束关键帧（回到中性表情）
    if (request.loop) {
      clip.keyframes.push({
        time: request.duration,
        expression: FacialExpressionType.NEUTRAL,
        expressionWeight: 1.0
      });
    } else {
      // 如果不循环，保持最后的表情
      clip.keyframes.push({
        time: request.duration,
        expression: primaryExpression,
        expressionWeight: expressionWeight * 0.8 // 稍微减弱
      });
    }

    // 添加口型关键帧（如果有）
    await this.addVisemeKeyframes(clip, request);

    // 按时间排序
    clip.keyframes.sort((a, b) => a.time - b.time);

    return clip;
  }

  /**
   * 添加口型关键帧
   * @param clip 动画片段
   * @param request 生成请求
   */
  private async addVisemeKeyframes(clip: FacialAnimationClip, request: AnimationGenerationRequest): Promise<void> {
    // 简单的口型序列
    const visemeSequence = [
      VisemeType.SILENT,
      VisemeType.AA,
      VisemeType.EE,
      VisemeType.IH,
      VisemeType.OH,
      VisemeType.OU,
      VisemeType.SILENT
    ];

    // 计算每个口型的持续时间
    const visemeDuration = request.duration / (visemeSequence.length - 1);

    // 添加口型关键帧
    for (let i = 0; i < visemeSequence.length; i++) {
      const time = i * visemeDuration;
      const viseme = visemeSequence[i];

      // 查找是否已存在相同时间的关键帧
      const existingKeyframe = clip.keyframes.find(k => Math.abs(k.time - time) < 0.01);

      if (existingKeyframe) {
        // 更新现有关键帧
        existingKeyframe.viseme = viseme;
        existingKeyframe.visemeWeight = viseme === VisemeType.SILENT ? 0.0 : 1.0;
      } else {
        // 添加新关键帧
        clip.keyframes.push({
          time,
          viseme,
          visemeWeight: viseme === VisemeType.SILENT ? 0.0 : 1.0
        });
      }
    }
  }

  /**
   * 将情感映射到表情
   * @param emotion 情感
   * @returns 表情类型
   */
  private mapEmotionToExpression(emotion: string): FacialExpressionType {
    const lowerEmotion = emotion.toLowerCase();
    return EMOTION_TO_EXPRESSION_MAP[lowerEmotion] || FacialExpressionType.NEUTRAL;
  }
}
