/**
 * 粒子系统服务
 */
import axios from 'axios';
import { ParticleSystem } from '../store/particles/particlesSlice';

const API_URL = '/api/particles';

export const particleService = {
  /**
   * 获取所有粒子系统
   */
  async getParticleSystems() {
    const response = await axios.get(API_URL);
    return response.data;
  },

  /**
   * 获取单个粒子系统
   */
  async getParticleSystem(particleSystemId: string) {
    const response = await axios.get(`${API_URL}/${particleSystemId}`);
    return response.data;
  },

  /**
   * 创建粒子系统
   */
  async createParticleSystem(particleSystemData: Omit<ParticleSystem, 'id' | 'createdAt' | 'updatedAt'>) {
    const response = await axios.post(API_URL, particleSystemData);
    return response.data;
  },

  /**
   * 更新粒子系统
   */
  async updateParticleSystem(particleSystemId: string, particleSystemData: Partial<ParticleSystem>) {
    const response = await axios.patch(`${API_URL}/${particleSystemId}`, particleSystemData);
    return response.data;
  },

  /**
   * 删除粒子系统
   */
  async deleteParticleSystem(particleSystemId: string) {
    await axios.delete(`${API_URL}/${particleSystemId}`);
  },

  /**
   * 导出粒子系统
   */
  async exportParticleSystem(particleSystemId: string) {
    const response = await axios.get(`${API_URL}/${particleSystemId}/export`);
    return response.data;
  },

  /**
   * 导入粒子系统
   */
  async importParticleSystem(particleSystemData: any) {
    const response = await axios.post(`${API_URL}/import`, particleSystemData);
    return response.data;
  },

  /**
   * 复制粒子系统
   */
  async duplicateParticleSystem(particleSystemId: string, newName?: string) {
    const response = await axios.post(`${API_URL}/${particleSystemId}/duplicate`, { name: newName });
    return response.data;
  },

  /**
   * 应用粒子系统到实体
   */
  async applyParticleSystem(particleSystemId: string, entityId: string) {
    const response = await axios.post(`${API_URL}/${particleSystemId}/apply`, { entityId });
    return response.data;
  },
};
