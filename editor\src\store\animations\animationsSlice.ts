/**
 * 动画状态切片
 */
import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { animationService } from '../../services/animationService';

// 关键帧接口
export interface Keyframe {
  time: number;
  value: any;
}

// 动画轨道接口
export interface AnimationTrack {
  name: string;
  type: 'position' | 'rotation' | 'scale' | 'visibility' | 'color' | 'intensity';
  interpolation: 'linear' | 'step' | 'cubicBezier' | 'catmullRom';
  keyframes: Keyframe[];
}

// 动画接口
export interface Animation {
  id: string;
  name: string;
  duration: number;
  tracks: AnimationTrack[];
  createdAt: string;
  updatedAt: string;
}

// 动画状态
interface AnimationsState {
  animations: Animation[];
  selectedAnimationId: string | null;
  loading: boolean;
  error: string | null;
}

// 初始状态
const initialState: AnimationsState = {
  animations: [],
  selectedAnimationId: null,
  loading: false,
  error: null,
};

// 异步操作：获取所有动画
export const fetchAnimations = createAsyncThunk(
  'animations/fetchAnimations',
  async (_, { rejectWithValue }) => {
    try {
      const response = await animationService.getAnimations();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：获取单个动画
export const fetchAnimation = createAsyncThunk(
  'animations/fetchAnimation',
  async (animationId: string, { rejectWithValue }) => {
    try {
      const response = await animationService.getAnimation(animationId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：创建动画
export const createAnimation = createAsyncThunk(
  'animations/createAnimation',
  async (animationData: Omit<Animation, 'id' | 'createdAt' | 'updatedAt'>, { rejectWithValue }) => {
    try {
      const response = await animationService.createAnimation(animationData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：更新动画
export const updateAnimation = createAsyncThunk(
  'animations/updateAnimation',
  async (animationData: Partial<Animation> & { id: string }, { rejectWithValue }) => {
    try {
      const response = await animationService.updateAnimation(animationData.id, animationData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：删除动画
export const deleteAnimation = createAsyncThunk(
  'animations/deleteAnimation',
  async (animationId: string, { rejectWithValue }) => {
    try {
      await animationService.deleteAnimation(animationId);
      return animationId;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 创建动画切片
const animationsSlice = createSlice({
  name: 'animations',
  initialState,
  reducers: {
    // 选择动画
    selectAnimation: (state, action: PayloadAction<string | null>) => {
      state.selectedAnimationId = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取所有动画
      .addCase(fetchAnimations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAnimations.fulfilled, (state, action) => {
        state.loading = false;
        state.animations = action.payload;
      })
      .addCase(fetchAnimations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 获取单个动画
      .addCase(fetchAnimation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAnimation.fulfilled, (state, action) => {
        state.loading = false;
        const animationIndex = state.animations.findIndex(animation => animation.id === action.payload.id);
        
        if (animationIndex !== -1) {
          state.animations[animationIndex] = action.payload;
        } else {
          state.animations.push(action.payload);
        }
      })
      .addCase(fetchAnimation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 创建动画
      .addCase(createAnimation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createAnimation.fulfilled, (state, action) => {
        state.loading = false;
        state.animations.push(action.payload);
      })
      .addCase(createAnimation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 更新动画
      .addCase(updateAnimation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateAnimation.fulfilled, (state, action) => {
        state.loading = false;
        const animationIndex = state.animations.findIndex(animation => animation.id === action.payload.id);
        
        if (animationIndex !== -1) {
          state.animations[animationIndex] = action.payload;
        }
      })
      .addCase(updateAnimation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // 删除动画
      .addCase(deleteAnimation.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteAnimation.fulfilled, (state, action) => {
        state.loading = false;
        state.animations = state.animations.filter(animation => animation.id !== action.payload);
        
        if (state.selectedAnimationId === action.payload) {
          state.selectedAnimationId = null;
        }
      })
      .addCase(deleteAnimation.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { selectAnimation } = animationsSlice.actions;

export default animationsSlice.reducer;
