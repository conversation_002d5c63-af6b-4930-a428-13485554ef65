.character-network-sync {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  padding: 16px;
  background-color: #f5f5f5;

  .sync-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .ant-typography {
      margin-bottom: 0;
    }

    .connection-status {
      display: flex;
      align-items: center;
      gap: 16px;

      .status-item {
        display: flex;
        align-items: center;
      }
    }
  }

  .sync-content {
    flex: 1;
    overflow: auto;
    background-color: #fff;
    border-radius: 4px;
    padding: 16px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

    .connection-actions {
      margin: 16px 0;
    }

    .sync-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 16px;
    }

    .empty-list {
      padding: 16px;
      text-align: center;
      background-color: #f5f5f5;
      border-radius: 4px;
    }

    .user-list {
      margin-top: 8px;

      .user-item {
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }
      }
    }

    .ant-table-wrapper {
      margin-top: 8px;
    }
  }
}
