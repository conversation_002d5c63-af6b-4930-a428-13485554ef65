/**
 * 教程难度级别面板样式
 */
.tutorial-difficulty-panel {
  padding: 0 10px;

  .difficulty-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;

    .help-icon {
      color: #1890ff;
      font-size: 16px;
      cursor: pointer;
    }
  }

  .overall-progress {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .difficulty-content {
    .difficulty-description {
      margin-bottom: 15px;
    }

    .difficulty-stats {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f9f9f9;
      border-radius: 4px;

      .stats-item {
        display: flex;
        flex-direction: column;
        gap: 5px;
      }
    }

    .categories-collapse {
      margin-bottom: 20px;

      .category-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
  }

  .tutorial-item-card {
    height: 100%;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .tutorial-item-cover {
      position: relative;
      height: 160px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s;
      }

      &:hover img {
        transform: scale(1.05);
      }

      .tutorial-completed-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #52c41a;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 16px;
      }
    }

    .tutorial-item-title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .popular-icon {
        color: #ff4d4f;
        font-size: 16px;
        margin-left: 8px;
      }
    }

    .tutorial-item-description {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .tutorial-item-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-top: 5px;
    }

    .tutorial-progress {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-top: 5px;

      .progress-bar {
        flex: 1;
        height: 6px;
        background-color: #f0f0f0;
        border-radius: 3px;
        overflow: hidden;

        .progress-fill {
          height: 100%;
          background-color: #1890ff;
          border-radius: 3px;
        }
      }
    }

    .tutorial-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-top: 8px;
      align-items: center;

      .tutorial-tag {
        margin-right: 0;
      }
    }

    .start-tutorial-button {
      margin-top: 10px;
      align-self: flex-end;
    }
  }

  .tutorial-skeleton {
    padding: 20px 0;
  }
}
