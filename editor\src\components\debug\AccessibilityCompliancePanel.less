/**
 * 辅助功能合规性测试面板样式
 */
.accessibility-compliance-panel {
  .ant-card-body {
    max-height: 700px;
    overflow-y: auto;
  }

  .progress-container {
    margin-top: 20px;
    
    .progress-text {
      text-align: center;
      margin-top: 8px;
      color: rgba(0, 0, 0, 0.65);
    }
  }

  .results-summary {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
    
    .summary-card {
      text-align: center;
      border-radius: 4px;
      
      &.pass {
        background-color: #f6ffed;
        border-color: #b7eb8f;
      }
      
      &.fail {
        background-color: #fff2f0;
        border-color: #ffccc7;
      }
      
      &.warning {
        background-color: #fffbe6;
        border-color: #ffe58f;
      }
      
      &.not-applicable {
        background-color: #f5f5f5;
        border-color: #d9d9d9;
      }
      
      .summary-number {
        font-size: 24px;
        font-weight: bold;
      }
      
      .summary-label {
        margin-top: 4px;
        color: rgba(0, 0, 0, 0.65);
      }
    }
    
    .compliance-score {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 20px;
    }
  }

  .result-details {
    margin-top: 20px;
    
    .result-info {
      margin-top: 16px;
      
      > div {
        margin-bottom: 8px;
      }
      
      .fix-suggestion {
        margin-top: 16px;
        padding: 12px;
        background-color: #fffbe6;
        border-left: 3px solid #faad14;
        border-radius: 2px;
      }
    }
  }

  .option-label {
    font-weight: 500;
    margin-bottom: 8px;
  }
}
