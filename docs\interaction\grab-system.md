# 抓取系统文档

## 概述

抓取系统（Grab System）是DL（Digital Learning）引擎交互系统的一部分，用于处理3D场景中对象的抓取和释放操作。它提供了一套完整的抓取机制，包括不同类型的抓取方式、物理抓取、网络同步等功能。最新版本的抓取系统进行了全面优化，增加了手势识别、VR/AR支持、性能优化等功能。

## 主要特性

- **多种抓取类型**：支持直接抓取、距离抓取和弹簧抓取
- **物理抓取**：与物理系统集成，支持物理对象的抓取和交互
- **双手抓取**：支持左右手分别抓取不同对象
- **网络同步**：支持多用户环境下的抓取同步
- **事件系统**：完整的事件系统，支持抓取和释放事件的监听和处理
- **手势识别**：支持基于手势的抓取交互
- **VR/AR支持**：针对VR/AR设备优化的抓取体验
- **抛掷功能**：支持物理抛掷、速度抛掷和弹道抛掷
- **性能优化**：空间分区、对象池、LOD系统等优化技术

## 系统组件

抓取系统由以下主要组件组成：

### 基础组件
1. **GrabSystem / OptimizedGrabSystem**：抓取系统的核心，负责管理和协调所有抓取组件
2. **GrabbableComponent**：标记可被抓取的对象的组件，定义抓取行为
3. **GrabberComponent**：标记可以抓取其他对象的实体的组件
4. **GrabbedComponent**：标记当前被抓取状态的组件
5. **PhysicsGrabComponent**：处理物理对象抓取的组件
6. **GrabState**：管理抓取状态的状态管理器

### 高级组件
7. **ThrowableComponent**：处理对象的抛掷行为
8. **GestureGrabComponent**：处理基于手势的抓取交互
9. **XRGrabComponent**：处理VR/AR环境中的抓取交互
10. **GrabNetworkComponent**：处理多用户环境下的抓取同步

## 抓取类型

抓取系统支持以下几种抓取类型：

1. **直接抓取（DIRECT）**：被抓取对象直接附加到抓取者的手上，跟随手的移动和旋转
2. **距离抓取（DISTANCE）**：被抓取对象保持一定距离，但仍然跟随手的移动和旋转
3. **弹簧抓取（SPRING）**：被抓取对象通过弹簧力连接到手上，有一定的弹性和物理效果

## 使用方法

### 1. 初始化抓取系统

首先，需要创建并初始化抓取系统，并将其添加到世界中：

```typescript
// 创建抓取系统
const grabSystem = new GrabSystem(world, {
  debug: true,                  // 是否启用调试模式
  enablePhysicsGrab: true,      // 是否启用物理抓取
  enableNetworkSync: true,      // 是否启用网络同步
  enableGestureGrab: true,      // 是否启用手势抓取
  defaultGrabType: GrabType.DIRECT  // 默认抓取类型
});

// 添加抓取系统到世界
world.addSystem(grabSystem);
```

### 2. 创建可抓取对象

然后，创建可抓取对象并添加相关组件：

```typescript
// 创建实体
const entity = new Entity(world);

// 添加变换组件
entity.addComponent(new Transform({
  position: new Vector3(0, 1, 0),
  rotation: new Vector3(0, 0, 0),
  scale: new Vector3(1, 1, 1)
}));

// 添加可抓取组件
entity.addComponent(new GrabbableComponent(entity, {
  grabType: GrabType.DIRECT,
  allowedHands: [Hand.ANY],
  grabDistance: 1.0,
  onGrab: (entity, grabber) => {
    console.log(`实体 ${entity.id} 被抓取`);
  },
  onRelease: (entity, grabber) => {
    console.log(`实体 ${entity.id} 被释放`);
  }
}));

// 如果需要物理抓取，还需要添加物理体组件和物理抓取组件
entity.addComponent(new PhysicsBodyComponent(entity, {
  type: BodyType.DYNAMIC,
  mass: 1.0
}));

entity.addComponent(new PhysicsGrabComponent(entity, {
  grabForce: 10.0,
  grabDamping: 0.5,
  grabBodyType: BodyType.KINEMATIC
}));

// 添加到场景
scene.addEntity(entity);
```

### 3. 创建抓取者

接下来，创建抓取者（通常是玩家或角色）并添加抓取者组件：

```typescript
// 创建玩家实体
const playerEntity = new Entity(world);

// 添加变换组件
playerEntity.addComponent(new Transform({
  position: new Vector3(0, 1.8, 0),
  rotation: new Vector3(0, 0, 0),
  scale: new Vector3(1, 1, 1)
}));

// 添加抓取者组件
playerEntity.addComponent(new GrabberComponent(playerEntity, {
  maxGrabDistance: 3.0,
  onGrab: (grabber, grabbed, hand) => {
    console.log(`玩家抓取了对象 ${grabbed.id}，使用${hand === Hand.LEFT ? '左手' : '右手'}`);
  },
  onRelease: (grabber, released, hand) => {
    console.log(`玩家释放了对象 ${released.id}，使用${hand === Hand.LEFT ? '左手' : '右手'}`);
  }
}));

// 添加到场景
scene.addEntity(playerEntity);
```

### 4. 实现抓取和释放逻辑

最后，实现抓取和释放的输入处理逻辑：

```typescript
// 获取抓取者组件
const grabberComponent = playerEntity.getComponent<GrabberComponent>(GrabberComponent.TYPE);
if (!grabberComponent) return;

// 检测左键点击 - 左手抓取
if (inputSystem.isMouseButtonPressed(MouseButton.LEFT)) {
  // 获取最近的可抓取对象
  const nearestEntity = getNearestGrabbableEntity();
  if (nearestEntity) {
    // 尝试抓取
    grabberComponent.grab(nearestEntity, Hand.LEFT);
  }
}

// 检测右键点击 - 右手抓取
if (inputSystem.isMouseButtonPressed(MouseButton.RIGHT)) {
  // 获取最近的可抓取对象
  const nearestEntity = getNearestGrabbableEntity();
  if (nearestEntity) {
    // 尝试抓取
    grabberComponent.grab(nearestEntity, Hand.RIGHT);
  }
}

// 检测Q键 - 左手释放
if (inputSystem.isKeyPressed(KeyCode.KEY_Q)) {
  grabberComponent.release(Hand.LEFT);
}

// 检测E键 - 右手释放
if (inputSystem.isKeyPressed(KeyCode.KEY_E)) {
  grabberComponent.release(Hand.RIGHT);
}
```

## 物理抓取

物理抓取是抓取系统的一个重要特性，它允许与物理系统集成，实现更真实的抓取效果。要使用物理抓取，需要以下步骤：

1. 确保物理系统已初始化并添加到世界中
2. 为可抓取对象添加物理体组件（PhysicsBodyComponent）
3. 为可抓取对象添加物理抓取组件（PhysicsGrabComponent）
4. 在抓取系统配置中启用物理抓取（enablePhysicsGrab: true）

物理抓取组件提供了以下配置选项：

- **grabForce**：抓取力大小，影响非运动学物体的抓取强度
- **grabDamping**：抓取阻尼，影响非运动学物体的稳定性
- **keepOriginalBodyType**：是否保持原始物理类型，如果为false，则在抓取时更改物理类型
- **grabBodyType**：抓取时的物理类型，默认为KINEMATIC

## 网络同步

抓取系统支持网络同步，使多用户环境下的抓取操作能够同步到所有客户端。要启用网络同步，需要以下步骤：

1. 确保网络系统已初始化并添加到世界中
2. 在抓取系统配置中启用网络同步（enableNetworkSync: true）
3. 使用GrabState状态管理器来管理和同步抓取状态

## 事件系统

抓取系统提供了完整的事件系统，允许监听和处理抓取相关的事件。主要事件包括：

- **grab**：当对象被抓取时触发
- **release**：当对象被释放时触发
- **grabStart**：当开始抓取时触发（GrabState事件）
- **grabEnd**：当结束抓取时触发（GrabState事件）
- **grabUpdate**：当抓取状态更新时触发（GrabState事件）
- **stateChange**：当抓取状态发生变化时触发（GrabState事件）

可以通过组件的on方法或GrabState的addEventListener方法来监听这些事件：

```typescript
// 监听组件事件
grabbableComponent.on('grab', (entity, grabber) => {
  console.log(`实体 ${entity.id} 被抓取`);
});

// 监听GrabState事件
GrabState.getInstance().addEventListener(GrabEventType.GRAB_START, (data) => {
  console.log(`实体 ${data.grabbed.id} 被 ${data.grabber.id} 抓取，使用${data.hand}手`);
});
```

## 优化抓取系统

最新版本的DL（Digital Learning）引擎引入了优化抓取系统（OptimizedGrabSystem），它在保持原有功能的基础上，增加了更多高级特性和性能优化。

### 优化抓取系统的特点

1. **空间分区**：使用网格分区优化抓取检测，提高大场景中的性能
2. **对象池**：减少对象创建和销毁，降低内存压力和GC开销
3. **LOD系统**：根据距离调整抓取对象的复杂度，优化远距离对象的性能
4. **更新频率控制**：可控制系统更新频率，平衡性能和响应性
5. **多线程支持**：可选的多线程处理，提高多核CPU利用率

### 使用优化抓取系统

```typescript
// 创建优化的抓取系统
const grabSystem = new OptimizedGrabSystem(world, {
  debug: true,                    // 是否启用调试模式
  enablePhysicsGrab: true,        // 是否启用物理抓取
  enableNetworkSync: true,        // 是否启用网络同步
  enableGestureGrab: true,        // 是否启用手势抓取
  enableXRGrab: true,             // 是否启用XR抓取
  defaultGrabType: GrabType.DIRECT,  // 默认抓取类型
  enableSpatialPartitioning: true,   // 是否启用空间分区
  enableObjectPool: true,         // 是否启用对象池
  enableMultithreading: false,    // 是否启用多线程
  enableLOD: true,                // 是否启用LOD系统
  updateFrequency: 1              // 更新频率（每帧更新）
});

// 添加系统到世界
world.addSystem(grabSystem);
```

### 抛掷功能

优化抓取系统增加了抛掷功能，通过ThrowableComponent实现：

```typescript
// 添加可抛掷组件
entity.addComponent(new ThrowableComponent(entity, {
  throwable: true,
  throwType: 'physics',  // 'physics', 'velocity', 'ballistic'
  throwForceMultiplier: 1.5,
  throwAngularForceMultiplier: 1.0,
  throwVelocitySmoothingFactor: 0.5,
  throwHistoryLength: 5,
  preserveRotationOnThrow: true,
  onThrow: (entity, velocity, angularVelocity) => {
    console.log(`对象 ${entity.id} 被抛掷，速度=${velocity.toArray()}`);
  }
}));
```

### 手势抓取

优化抓取系统支持基于手势的抓取交互：

```typescript
// 添加手势抓取组件
playerEntity.addComponent(new GestureGrabComponent(playerEntity, {
  enabled: true,
  grabGestureType: 'pinch',
  releaseGestureType: 'release',
  rotateGestureType: 'rotate',
  scaleGestureType: 'pinch',
  grabDistance: 3.0,
  useRaycasting: true,
  useHandTracking: false,
  useGesturePrediction: true,
  gestureSensitivity: 1.0
}));
```

### VR/AR支持

优化抓取系统针对VR/AR设备提供了专门的支持：

```typescript
// 添加XR抓取组件
playerEntity.addComponent(new XRGrabComponent(playerEntity, {
  enabled: true,
  controllerType: 'right',
  grabButtonIndex: 0,
  releaseButtonIndex: 0,
  grabDistance: 0.1,
  useRaycasting: true,
  useHapticFeedback: true,
  hapticFeedbackIntensity: 1.0,
  hapticFeedbackDuration: 100
}));
```

### 网络同步

优化抓取系统提供了更高效的网络同步功能：

```typescript
// 添加网络抓取组件
entity.addComponent(new GrabNetworkComponent(entity, {
  enabled: true,
  syncInterval: 100,
  useCompression: true,
  usePrediction: true,
  useAuthorityControl: true,
  useAdaptiveSync: true
}));
```

## 示例

完整的抓取系统示例可以在以下文件中找到：
- 基础抓取系统：`examples/interaction/GrabExample.ts`
- 优化抓取系统：`newsystem/examples/interaction/OptimizedGrabExample.ts`

这些示例展示了如何创建和使用抓取系统，包括不同类型的抓取、物理抓取、手势抓取、VR/AR支持等功能。

## 高级用法

### 自定义抓取行为

可以通过继承GrabbableComponent或实现自定义组件来扩展抓取行为：

```typescript
// 自定义可抓取组件
class CustomGrabbableComponent extends GrabbableComponent {
  constructor(entity: Entity, config: GrabbableComponentConfig = {}) {
    super(entity, config);
    // 自定义初始化
  }

  // 重写抓取方法
  grab(grabber: Entity, hand: Hand = Hand.RIGHT): boolean {
    // 自定义抓取逻辑
    console.log('自定义抓取逻辑');
    return super.grab(grabber, hand);
  }

  // 重写释放方法
  release(): boolean {
    // 自定义释放逻辑
    console.log('自定义释放逻辑');
    return super.release();
  }
}
```

### 与其他系统集成

抓取系统可以与其他系统集成，例如交互系统、动画系统等：

```typescript
// 与交互系统集成
interactableComponent.on('interact', (entity) => {
  // 当交互时尝试抓取
  const grabberComponent = playerEntity.getComponent<GrabberComponent>(GrabberComponent.TYPE);
  if (grabberComponent) {
    grabberComponent.grab(entity, Hand.RIGHT);
  }
});

// 与动画系统集成
grabbableComponent.on('grab', (entity, grabber) => {
  // 播放抓取动画
  const animationComponent = grabber.getComponent('AnimationComponent');
  if (animationComponent) {
    animationComponent.playAnimation('grab');
  }
});
```

### 性能优化建议

使用优化抓取系统时，可以考虑以下性能优化建议：

1. **启用空间分区**：对于大场景或大量可抓取对象，启用空间分区可以显著提高性能
2. **使用LOD系统**：对于远距离对象，使用LOD系统可以减少计算量
3. **调整更新频率**：根据需要调整更新频率，不需要高精度的场景可以降低更新频率
4. **使用对象池**：对于频繁创建和销毁的对象，使用对象池可以减少内存压力
5. **选择合适的抓取类型**：不同的抓取类型有不同的性能特性，选择最适合场景的类型

## 故障排除

### 常见问题

1. **对象无法抓取**
   - 检查对象是否有 `GrabbableComponent`
   - 检查抓取者是否有 `GrabberComponent`
   - 检查抓取距离是否合适
   - 确保物理系统正确配置（如果使用物理抓取）

2. **抓取后对象行为异常**
   - 检查抓取类型是否适合该对象
   - 检查物理设置是否合理
   - 确保没有其他系统干扰对象位置

3. **性能问题**
   - 启用空间分区和LOD系统
   - 减少同时可抓取的对象数量
   - 增加更新频率值（减少更新频率）
   - 检查是否有不必要的组件

## 总结

抓取系统是DL（Digital Learning）引擎交互系统的重要组成部分，提供了丰富的抓取功能和灵活的配置选项。最新的优化抓取系统增加了更多高级特性和性能优化，使其能够应对更复杂的场景和更高的性能要求。通过本文档的介绍，您应该能够理解和使用抓取系统的基本功能和高级特性，并根据需要进行扩展和定制。
