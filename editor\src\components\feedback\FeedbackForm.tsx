/**
 * 反馈表单组件
 * 用于收集用户对系统功能的反馈
 */
import React, { useState } from 'react';
import { Form, Input, Button, Rate, Select, Radio, Checkbox, Upload, Modal, message } from 'antd';
import { UploadOutlined, SendOutlined, CloseOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { FeedbackService } from '../../services/FeedbackService';
import './FeedbackForm.less';

const { TextArea } = Input;
const { Option } = Select;

export interface FeedbackFormProps {
  /** 反馈类型 */
  type: 'animation' | 'physics' | 'rendering' | 'editor' | 'general';
  /** 反馈子类型 */
  subType?: string;
  /** 相关实体ID */
  entityId?: string;
  /** 相关资源ID */
  resourceId?: string;
  /** 关闭回调 */
  onClose?: () => void;
  /** 提交成功回调 */
  onSuccess?: () => void;
}

/**
 * 反馈表单组件
 */
const FeedbackForm: React.FC<FeedbackFormProps> = ({
  type,
  subType,
  entityId,
  resourceId,
  onClose,
  onSuccess
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  // 反馈类型选项
  const feedbackTypeOptions = [
    { label: t('feedback.type.bug'), value: 'bug' },
    { label: t('feedback.type.feature'), value: 'feature' },
    { label: t('feedback.type.improvement'), value: 'improvement' },
    { label: t('feedback.type.performance'), value: 'performance' },
    { label: t('feedback.type.other'), value: 'other' }
  ];

  // 严重程度选项
  const severityOptions = [
    { label: t('feedback.severity.critical'), value: 'critical' },
    { label: t('feedback.severity.high'), value: 'high' },
    { label: t('feedback.severity.medium'), value: 'medium' },
    { label: t('feedback.severity.low'), value: 'low' }
  ];

  // 提交表单
  const handleSubmit = async (values: any) => {
    try {
      setSubmitting(true);
      
      // 准备反馈数据
      const feedbackData = {
        ...values,
        type,
        subType,
        entityId,
        resourceId,
        timestamp: new Date().toISOString(),
        browser: navigator.userAgent,
        screenSize: `${window.innerWidth}x${window.innerHeight}`
      };
      
      // 提交反馈
      await FeedbackService.submitFeedback(feedbackData);
      
      // 重置表单
      form.resetFields();
      
      // 显示成功提示
      setShowSuccessModal(true);
      
      // 调用成功回调
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('提交反馈失败:', error);
      message.error(t('feedback.submitError'));
    } finally {
      setSubmitting(false);
    }
  };

  // 关闭成功提示
  const handleCloseSuccessModal = () => {
    setShowSuccessModal(false);
    if (onClose) {
      onClose();
    }
  };

  // 获取表单标题
  const getFormTitle = () => {
    switch (type) {
      case 'animation':
        return t('feedback.title.animation');
      case 'physics':
        return t('feedback.title.physics');
      case 'rendering':
        return t('feedback.title.rendering');
      case 'editor':
        return t('feedback.title.editor');
      default:
        return t('feedback.title.general');
    }
  };

  // 获取子类型标签
  const getSubTypeLabel = () => {
    if (!subType) return '';
    
    switch (type) {
      case 'animation':
        return subType === 'blend' 
          ? t('feedback.subType.animation.blend')
          : subType === 'state-machine'
          ? t('feedback.subType.animation.stateMachine')
          : subType;
      default:
        return subType;
    }
  };

  return (
    <div className="feedback-form">
      <div className="feedback-form-header">
        <h2>{getFormTitle()}</h2>
        {subType && <div className="feedback-subtype">{getSubTypeLabel()}</div>}
        {onClose && (
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={onClose}
            className="close-button"
          />
        )}
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          feedbackType: 'improvement',
          severity: 'medium',
          satisfaction: 3,
          allowContact: true
        }}
      >
        {/* 反馈类型 */}
        <Form.Item
          name="feedbackType"
          label={t('feedback.form.feedbackType')}
          rules={[{ required: true, message: t('feedback.form.required') }]}
        >
          <Radio.Group options={feedbackTypeOptions} />
        </Form.Item>

        {/* 严重程度 */}
        <Form.Item
          name="severity"
          label={t('feedback.form.severity')}
          rules={[{ required: true, message: t('feedback.form.required') }]}
        >
          <Radio.Group options={severityOptions} />
        </Form.Item>

        {/* 标题 */}
        <Form.Item
          name="title"
          label={t('feedback.form.title')}
          rules={[{ required: true, message: t('feedback.form.required') }]}
        >
          <Input placeholder={t('feedback.form.titlePlaceholder')} maxLength={100} />
        </Form.Item>

        {/* 描述 */}
        <Form.Item
          name="description"
          label={t('feedback.form.description')}
          rules={[{ required: true, message: t('feedback.form.required') }]}
        >
          <TextArea
            placeholder={t('feedback.form.descriptionPlaceholder')}
            autoSize={{ minRows: 4, maxRows: 8 }}
            maxLength={2000}
          />
        </Form.Item>

        {/* 重现步骤 */}
        <Form.Item
          name="reproductionSteps"
          label={t('feedback.form.reproductionSteps')}
        >
          <TextArea
            placeholder={t('feedback.form.reproductionStepsPlaceholder')}
            autoSize={{ minRows: 3, maxRows: 6 }}
          />
        </Form.Item>

        {/* 满意度评分 */}
        <Form.Item
          name="satisfaction"
          label={t('feedback.form.satisfaction')}
        >
          <Rate allowHalf />
        </Form.Item>

        {/* 改进建议 */}
        <Form.Item
          name="suggestions"
          label={t('feedback.form.suggestions')}
        >
          <TextArea
            placeholder={t('feedback.form.suggestionsPlaceholder')}
            autoSize={{ minRows: 3, maxRows: 6 }}
          />
        </Form.Item>

        {/* 截图上传 */}
        <Form.Item
          name="screenshots"
          label={t('feedback.form.screenshots')}
        >
          <Upload
            listType="picture"
            maxCount={3}
            beforeUpload={() => false} // 阻止自动上传
          >
            <Button icon={<UploadOutlined />}>{t('feedback.form.uploadScreenshot')}</Button>
          </Upload>
        </Form.Item>

        {/* 联系许可 */}
        <Form.Item
          name="allowContact"
          valuePropName="checked"
        >
          <Checkbox>{t('feedback.form.allowContact')}</Checkbox>
        </Form.Item>

        {/* 提交按钮 */}
        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            icon={<SendOutlined />}
            loading={submitting}
            block
          >
            {t('feedback.form.submit')}
          </Button>
        </Form.Item>
      </Form>

      {/* 成功提示 */}
      <Modal
        title={t('feedback.success.title')}
        open={showSuccessModal}
        onCancel={handleCloseSuccessModal}
        footer={[
          <Button key="close" type="primary" onClick={handleCloseSuccessModal}>
            {t('feedback.success.close')}
          </Button>
        ]}
      >
        <p>{t('feedback.success.message')}</p>
      </Modal>
    </div>
  );
};

export default FeedbackForm;
