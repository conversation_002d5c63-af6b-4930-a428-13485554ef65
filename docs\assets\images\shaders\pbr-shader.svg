<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="600" height="500" xmlns="http://www.w3.org/2000/svg">
  <style>
    .panel {
      fill: #f5f5f5;
      stroke: #ddd;
      stroke-width: 1;
    }
    .header {
      fill: #e0e0e0;
      stroke: #ddd;
      stroke-width: 1;
    }
    .title {
      font-family: 'Arial', sans-serif;
      font-size: 16px;
      font-weight: bold;
    }
    .label {
      font-family: 'Arial', sans-serif;
      font-size: 14px;
    }
    .value {
      font-family: 'Consolas', monospace;
      font-size: 14px;
    }
    .input {
      fill: white;
      stroke: #ccc;
      stroke-width: 1;
    }
    .slider-bg {
      fill: #e0e0e0;
      stroke: #ccc;
      stroke-width: 1;
      rx: 3;
      ry: 3;
    }
    .slider {
      fill: #4a90e2;
      stroke: #3a80d2;
      stroke-width: 1;
      rx: 3;
      ry: 3;
    }
    .color-swatch {
      stroke: #ccc;
      stroke-width: 1;
    }
    .texture-slot {
      fill: #ddd;
      stroke: #ccc;
      stroke-width: 1;
    }
    .button {
      fill: #4a90e2;
      stroke: #3a80d2;
      stroke-width: 1;
      rx: 4;
      ry: 4;
    }
    .button-text {
      font-family: 'Arial', sans-serif;
      font-size: 12px;
      fill: white;
      text-anchor: middle;
      dominant-baseline: middle;
    }
    .preview {
      fill: #333;
      stroke: #ccc;
      stroke-width: 1;
    }
    .sphere {
      fill: url(#sphere-gradient);
      stroke: none;
    }
  </style>
  
  <defs>
    <radialGradient id="sphere-gradient" cx="0.5" cy="0.5" r="0.5">
      <stop offset="0%" stop-color="#f0f0f0" />
      <stop offset="70%" stop-color="#c0c0c0" />
      <stop offset="100%" stop-color="#808080" />
    </radialGradient>
  </defs>
  
  <!-- Material Panel -->
  <rect x="50" y="50" width="500" height="400" class="panel" rx="5" ry="5" />
  
  <!-- Header -->
  <rect x="50" y="50" width="500" height="40" class="header" rx="5" ry="5" />
  <text x="70" y="75" class="title">PBR材质编辑器</text>
  
  <!-- Preview -->
  <rect x="380" y="110" width="150" height="150" class="preview" rx="5" ry="5" />
  <circle cx="455" cy="185" r="60" class="sphere" />
  
  <!-- Albedo -->
  <text x="70" y="120" class="label">反照率 (Albedo)</text>
  <rect x="70" y="130" width="30" height="20" class="color-swatch" fill="#cccccc" />
  <rect x="110" y="130" width="100" height="20" class="texture-slot" />
  <text x="160" y="145" class="value" text-anchor="middle">albedo.png</text>
  <rect x="220" y="130" width="60" height="20" class="button" />
  <text x="250" y="140" class="button-text">浏览</text>
  
  <!-- Metallic -->
  <text x="70" y="180" class="label">金属度 (Metallic)</text>
  <rect x="70" y="190" width="210" height="10" class="slider-bg" />
  <rect x="70" y="190" width="168" height="10" class="slider" />
  <text x="290" y="200" class="value">0.8</text>
  <rect x="110" y="210" width="100" height="20" class="texture-slot" />
  <text x="160" y="225" class="value" text-anchor="middle">metal.png</text>
  <rect x="220" y="210" width="60" height="20" class="button" />
  <text x="250" y="220" class="button-text">浏览</text>
  
  <!-- Roughness -->
  <text x="70" y="260" class="label">粗糙度 (Roughness)</text>
  <rect x="70" y="270" width="210" height="10" class="slider-bg" />
  <rect x="70" y="270" width="42" height="10" class="slider" />
  <text x="290" y="280" class="value">0.2</text>
  <rect x="110" y="290" width="100" height="20" class="texture-slot" />
  <text x="160" y="305" class="value" text-anchor="middle">rough.png</text>
  <rect x="220" y="290" width="60" height="20" class="button" />
  <text x="250" y="300" class="button-text">浏览</text>
  
  <!-- Normal Map -->
  <text x="70" y="340" class="label">法线贴图 (Normal Map)</text>
  <rect x="110" y="350" width="100" height="20" class="texture-slot" />
  <text x="160" y="365" class="value" text-anchor="middle">normal.png</text>
  <rect x="220" y="350" width="60" height="20" class="button" />
  <text x="250" y="360" class="button-text">浏览</text>
  
  <!-- Environment -->
  <text x="70" y="400" class="label">环境反射强度</text>
  <rect x="70" y="410" width="210" height="10" class="slider-bg" />
  <rect x="70" y="410" width="105" height="10" class="slider" />
  <text x="290" y="420" class="value">0.5</text>
  
  <!-- Lighting Preview Controls -->
  <text x="380" y="290" class="label">光照预览</text>
  <circle cx="400" cy="320" r="10" fill="#ffcc00" stroke="#ccc" />
  <text x="420" y="325" class="label">主光源</text>
  <rect x="380" y="340" width="150" height="20" class="input" />
  <text x="455" y="355" class="value" text-anchor="middle">室内HDR</text>
  <rect x="380" y="370" width="150" height="20" class="button" />
  <text x="455" y="380" class="button-text">旋转环境光</text>
</svg>
