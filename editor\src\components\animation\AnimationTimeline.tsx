/**
 * 动画时间轴
 */
import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { Button, Tooltip } from 'antd';
import { ZoomInOutlined, ZoomOutOutlined } from '@ant-design/icons';
import { useDrag, useDrop } from 'react-dnd';
import './AnimationEditorPanel.css';

/**
 * 动画时间轴属性
 */
interface AnimationTimelineProps {
  /** 当前时间 */
  currentTime: number;
  /** 持续时间 */
  duration: number;
  /** 时间更新回调 */
  onTimeUpdate: (time: number) => void;
  /** 混合层 */
  layers: any[];
  /** 子片段 */
  subClips: Record<string, any>;
  /** 序列 */
  sequences: Record<string, any>;
  /** 过渡 */
  transitions: Record<string, any>;
  /** 事件 */
  events: Record<string, any>;
}

/**
 * 动画时间轴
 */
export const AnimationTimeline = forwardRef<any, AnimationTimelineProps>(({
  currentTime,
  duration,
  onTimeUpdate,
  layers,
  subClips,
  sequences,
  transitions,
  events
}, ref) => {
  // 状态
  const [zoom, setZoom] = useState(100); // 缩放比例（像素/秒）
  const [scrollLeft, setScrollLeft] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [selectedClip, setSelectedClip] = useState<string | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<string | null>(null);
  
  // 引用
  const timelineRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const playheadRef = useRef<HTMLDivElement>(null);
  
  // 暴露方法
  useImperativeHandle(ref, () => ({
    setCurrentTime: (time: number) => {
      if (playheadRef.current) {
        playheadRef.current.style.left = `${time * zoom}px`;
      }
    }
  }));
  
  // 效果
  useEffect(() => {
    if (playheadRef.current) {
      playheadRef.current.style.left = `${currentTime * zoom}px`;
    }
  }, [currentTime, zoom]);
  
  // 缩放
  const handleZoomIn = () => {
    setZoom(Math.min(zoom * 1.2, 500));
  };
  
  const handleZoomOut = () => {
    setZoom(Math.max(zoom / 1.2, 10));
  };
  
  // 点击时间轴
  const handleTimelineClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (timelineRef.current) {
      const rect = timelineRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left + scrollLeft;
      const time = x / zoom;
      
      onTimeUpdate(Math.max(0, Math.min(time, duration)));
    }
  };
  
  // 拖动时间轴
  const handleTimelineMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.button !== 0) return; // 只处理左键
    
    setIsDragging(true);
    
    const handleMouseMove = (e: MouseEvent) => {
      if (timelineRef.current) {
        const rect = timelineRef.current.getBoundingClientRect();
        const x = e.clientX - rect.left + scrollLeft;
        const time = x / zoom;
        
        onTimeUpdate(Math.max(0, Math.min(time, duration)));
      }
    };
    
    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };
  
  // 滚动时间轴
  const handleTimelineScroll = (e: React.UIEvent<HTMLDivElement>) => {
    if (contentRef.current) {
      setScrollLeft(contentRef.current.scrollLeft);
    }
  };
  
  // 渲染刻度
  const renderTicks = () => {
    const ticks = [];
    const majorTickInterval = 1; // 主刻度间隔（秒）
    const minorTickInterval = 0.1; // 次刻度间隔（秒）
    
    // 主刻度
    for (let i = 0; i <= duration; i += majorTickInterval) {
      ticks.push(
        <div
          key={`major-${i}`}
          className="animation-timeline-ruler-tick major"
          style={{ left: `${i * zoom}px` }}
        />
      );
      
      ticks.push(
        <div
          key={`label-${i}`}
          className="animation-timeline-ruler-label"
          style={{ left: `${i * zoom}px` }}
        >
          {i}s
        </div>
      );
    }
    
    // 次刻度
    for (let i = 0; i <= duration; i += minorTickInterval) {
      if (i % majorTickInterval !== 0) {
        ticks.push(
          <div
            key={`minor-${i}`}
            className="animation-timeline-ruler-tick"
            style={{ left: `${i * zoom}px` }}
          />
        );
      }
    }
    
    return ticks;
  };
  
  // 渲染混合层
  const renderLayers = () => {
    return layers.map((layer, index) => {
      const clipName = layer.clipName;
      const clip = subClips[clipName] || { startTime: 0, endTime: 1 };
      const clipDuration = clip.endTime - clip.startTime;
      
      return (
        <div key={index} className="animation-timeline-layer">
          <div className="animation-timeline-layer-header">
            {`层 ${index + 1}: ${clipName}`}
          </div>
          <div className="animation-timeline-layer-content">
            <div
              className={`animation-timeline-clip ${selectedClip === `layer-${index}` ? 'selected' : ''}`}
              style={{
                left: `${clip.startTime * zoom}px`,
                width: `${clipDuration * zoom}px`
              }}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedClip(`layer-${index}`);
              }}
            >
              <div className="animation-timeline-clip-handle left" />
              <div className="animation-timeline-clip-handle right" />
            </div>
          </div>
        </div>
      );
    });
  };
  
  // 渲染事件
  const renderEvents = () => {
    return Object.entries(events).map(([name, event]: [string, any]) => {
      const time = event.triggerValue || 0;
      
      return (
        <Tooltip key={name} title={name}>
          <div
            className={`animation-timeline-event ${selectedEvent === name ? 'selected' : ''}`}
            style={{ left: `${time * zoom}px` }}
            onClick={(e) => {
              e.stopPropagation();
              setSelectedEvent(name);
            }}
          />
        </Tooltip>
      );
    });
  };
  
  // 渲染
  return (
    <div className="animation-timeline">
      <div className="animation-timeline-header">
        <Button icon={<ZoomInOutlined />} onClick={handleZoomIn} />
        <Button icon={<ZoomOutOutlined />} onClick={handleZoomOut} />
      </div>
      
      <div
        ref={timelineRef}
        className="animation-timeline-ruler"
        onClick={handleTimelineClick}
        onMouseDown={handleTimelineMouseDown}
        style={{ width: `${duration * zoom}px` }}
      >
        {renderTicks()}
      </div>
      
      <div
        ref={contentRef}
        className="animation-timeline-content"
        onScroll={handleTimelineScroll}
      >
        <div
          className="animation-timeline-layers"
          style={{ width: `${duration * zoom}px` }}
        >
          {renderLayers()}
          
          <div
            ref={playheadRef}
            className="animation-timeline-playhead"
            style={{ left: `${currentTime * zoom}px` }}
          />
          
          {renderEvents()}
        </div>
      </div>
    </div>
  );
});
