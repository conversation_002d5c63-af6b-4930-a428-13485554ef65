"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventEmitter = void 0;
var EventEmitter = /** @class */ (function () {
    function EventEmitter() {
        /** 事件监听器映射 */
        this._listeners = new Map();
    }
    /**
     * 添加事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @returns 当前实例，用于链式调用
     */
    EventEmitter.prototype.on = function (event, callback) {
        if (!this._listeners.has(event)) {
            this._listeners.set(event, []);
        }
        this._listeners.get(event).push(callback);
        return this;
    };
    /**
     * 添加一次性事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @returns 当前实例，用于链式调用
     */
    EventEmitter.prototype.once = function (event, callback) {
        var _this = this;
        var onceCallback = function () {
            var args = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                args[_i] = arguments[_i];
            }
            _this.off(event, onceCallback);
            callback.apply(void 0, args);
        };
        return this.on(event, onceCallback);
    };
    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数（可选，如果不提供则移除该事件的所有监听器）
     * @returns 当前实例，用于链式调用
     */
    EventEmitter.prototype.off = function (event, callback) {
        if (!this._listeners.has(event)) {
            return this;
        }
        if (!callback) {
            // 移除该事件的所有监听器
            this._listeners.delete(event);
            return this;
        }
        // 移除特定的回调函数
        var callbacks = this._listeners.get(event);
        var index = callbacks.indexOf(callback);
        if (index !== -1) {
            callbacks.splice(index, 1);
            // 如果没有监听器了，则删除该事件
            if (callbacks.length === 0) {
                this._listeners.delete(event);
            }
        }
        return this;
    };
    /**
     * 发射事件
     * @param event 事件名称
     * @param args 事件参数
     * @returns 是否有监听器处理了该事件
     */
    EventEmitter.prototype.emit = function (event) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        if (!this._listeners.has(event)) {
            return false;
        }
        var callbacks = this._listeners.get(event).slice();
        for (var _a = 0, callbacks_1 = callbacks; _a < callbacks_1.length; _a++) {
            var callback = callbacks_1[_a];
            try {
                callback.apply(void 0, args);
            }
            catch (error) {
                console.error("\u4E8B\u4EF6\u5904\u7406\u5668\u9519\u8BEF: ".concat(error));
            }
        }
        return callbacks.length > 0;
    };
    /**
     * 获取事件监听器数量
     * @param event 事件名称（可选，如果不提供则返回所有事件的监听器总数）
     * @returns 监听器数量
     */
    EventEmitter.prototype.listenerCount = function (event) {
        if (event) {
            return this._listeners.has(event) ? this._listeners.get(event).length : 0;
        }
        var count = 0;
        this._listeners.forEach(function (callbacks) {
            count += callbacks.length;
        });
        return count;
    };
    /**
     * 获取事件名称列表
     * @returns 事件名称数组
     */
    EventEmitter.prototype.eventNames = function () {
        return Array.from(this._listeners.keys());
    };
    /**
     * 获取事件监听器列表
     * @param event 事件名称
     * @returns 监听器数组
     */
    EventEmitter.prototype.listeners = function (event) {
        return this._listeners.has(event) ? this._listeners.get(event).slice() : [];
    };
    /**
     * 移除所有事件监听器
     * @returns 当前实例，用于链式调用
     */
    EventEmitter.prototype.removeAllListeners = function () {
        this._listeners.clear();
        return this;
    };
    return EventEmitter;
}());
exports.EventEmitter = EventEmitter;
