/**
 * 面部动画编辑器示例
 * 演示如何使用面部动画编辑器系统
 */
import * as THREE from 'three';
import { Engine } from '../../core/Engine';
import { World } from '../../core/World';
import { Entity } from '../../core/Entity';
import {
  FacialAnimationSystem,
  FacialAnimationComponent,
  FacialExpressionType,
  VisemeType
} from '../../animation/FacialAnimation';
import {
  FacialAnimationEditorSystem,
  FacialAnimationEditorComponent
} from '../../animation/FacialAnimationEditorSystem';
import {
  FacialAnimationModelAdapterSystem,
  FacialAnimationModelType
} from '../../animation/adapters/FacialAnimationModelAdapterSystem';
import { GPUFacialAnimationSystem } from '../../animation/GPUFacialAnimation';
import { GLTFLoader } from '../../loaders/GLTFLoader';
import { OrbitControls } from '../../utils/OrbitControls';

/**
 * 面部动画编辑器示例
 */
export class FacialAnimationEditorExample {
  /** 引擎 */
  private engine: Engine;
  /** 世界 */
  private world: World;
  /** 场景 */
  private scene: THREE.Scene;
  /** 相机 */
  private camera: THREE.PerspectiveCamera;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer;
  /** 控制器 */
  private controls: OrbitControls;
  /** 角色实体 */
  private characterEntity: Entity;
  /** 面部动画系统 */
  private facialAnimationSystem: FacialAnimationSystem;
  /** 面部动画编辑器系统 */
  private facialAnimationEditorSystem: FacialAnimationEditorSystem;
  /** 面部动画模型适配器系统 */
  private modelAdapterSystem: FacialAnimationModelAdapterSystem;
  /** GPU面部动画系统 */
  private gpuFacialAnimationSystem: GPUFacialAnimationSystem;
  /** 是否运行中 */
  private running: boolean = false;
  /** 编辑器UI容器 */
  private editorContainer: HTMLDivElement | null = null;
  /** 播放按钮 */
  private playButton: HTMLButtonElement | null = null;
  /** 时间轴容器 */
  private timelineContainer: HTMLDivElement | null = null;
  /** 当前时间 */
  private currentTime: number = 0;
  /** 动画持续时间 */
  private duration: number = 5.0;
  /** 是否播放中 */
  private isPlaying: boolean = false;
  /** 动画定时器ID */
  private animationTimerId: number | null = null;
  /** 上次更新时间 */
  private lastUpdateTime: number = 0;

  /**
   * 构造函数
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();

    // 创建世界
    this.world = this.engine.createWorld();

    // 获取场景
    this.scene = this.world.getScene();

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(
      50,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    this.camera.position.set(0, 1.6, 2);

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    document.body.appendChild(this.renderer.domElement);

    // 创建角色实体
    this.characterEntity = this.world.createEntity();

    // 创建面部动画系统
    this.facialAnimationSystem = new FacialAnimationSystem({
      debug: true,
      autoDetectAudio: true
    });

    // 创建面部动画模型适配器系统
    this.modelAdapterSystem = new FacialAnimationModelAdapterSystem({
      debug: true,
      defaultModelType: FacialAnimationModelType.GLTF,
      autoDetectBlendShapes: true
    });

    // 设置模型适配器系统
    this.facialAnimationSystem.setModelAdapterSystem(this.modelAdapterSystem);

    // 创建面部动画编辑器系统
    this.facialAnimationEditorSystem = new FacialAnimationEditorSystem({
      debug: true,
      defaultFrameRate: 30,
      defaultDuration: 5.0
    });

    // 创建GPU面部动画系统
    this.gpuFacialAnimationSystem = new GPUFacialAnimationSystem({
      debug: true,
      useComputeShader: false
    });

    // 添加系统到世界
    this.world.addSystem(this.facialAnimationSystem);
    this.world.addSystem(this.modelAdapterSystem);
    this.world.addSystem(this.facialAnimationEditorSystem);
    this.world.addSystem(this.gpuFacialAnimationSystem);

    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 1.6, 0);
    this.controls.update();

    // 添加灯光
    this.setupLights();

    // 处理窗口大小变化
    window.addEventListener('resize', this.onWindowResize.bind(this));
  }

  /**
   * 设置灯光
   */
  private setupLights(): void {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(ambientLight);

    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 2, 3);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);

    // 设置阴影
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.1;
    directionalLight.shadow.camera.far = 10;
    directionalLight.shadow.camera.left = -5;
    directionalLight.shadow.camera.right = 5;
    directionalLight.shadow.camera.top = 5;
    directionalLight.shadow.camera.bottom = -5;
  }

  /**
   * 窗口大小变化处理
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 加载模型
   */
  private loadModel(): void {
    const loader = new GLTFLoader();

    // 加载模型
    loader.load('models/character.glb', (gltf) => {
      // 添加到场景
      this.scene.add(gltf.scene);

      // 查找骨骼网格
      let skinnedMesh: THREE.SkinnedMesh | null = null;
      gltf.scene.traverse((object) => {
        if (object instanceof THREE.SkinnedMesh) {
          skinnedMesh = object;
        }
      });

      if (skinnedMesh) {
        // 创建面部动画组件
        const facialAnimation = this.facialAnimationSystem.createFacialAnimation(this.characterEntity);

        // 将面部动画组件与模型绑定
        this.facialAnimationSystem.linkToModel(this.characterEntity, skinnedMesh);

        // 创建面部动画编辑器组件
        const editor = this.facialAnimationEditorSystem.createEditor(this.characterEntity);

        // 创建GPU面部动画组件
        this.gpuFacialAnimationSystem.createGPUFacialAnimation(this.characterEntity, skinnedMesh);

        // 创建默认动画片段
        this.createDefaultAnimationClips();

        console.log('模型加载完成，已绑定面部动画组件和编辑器组件');
        console.log('混合形状字典:', skinnedMesh.morphTargetDictionary);
        console.log('混合形状影响:', skinnedMesh.morphTargetInfluences);
      } else {
        console.warn('未找到骨骼网格');
      }
    }, undefined, (error) => {
      console.error('加载模型失败:', error);
    });
  }

  /**
   * 创建默认动画片段
   */
  private createDefaultAnimationClips(): void {
    const editor = this.facialAnimationEditorSystem.getEditor(this.characterEntity);
    if (!editor) return;

    // 创建表情动画
    const expressionClip = editor.createClip('表情动画', 5.0, true);

    // 添加关键帧
    editor.addKeyframe(0.0, {
      expression: FacialExpressionType.NEUTRAL,
      expressionWeight: 0.0
    });

    editor.addKeyframe(1.0, {
      expression: FacialExpressionType.HAPPY,
      expressionWeight: 1.0
    });

    editor.addKeyframe(2.0, {
      expression: FacialExpressionType.SURPRISED,
      expressionWeight: 1.0
    });

    editor.addKeyframe(3.0, {
      expression: FacialExpressionType.ANGRY,
      expressionWeight: 1.0
    });

    editor.addKeyframe(4.0, {
      expression: FacialExpressionType.SAD,
      expressionWeight: 1.0
    });

    editor.addKeyframe(5.0, {
      expression: FacialExpressionType.NEUTRAL,
      expressionWeight: 0.0
    });

    // 创建口型动画
    const visemeClip = editor.createClip('口型动画', 5.0, true);

    // 添加关键帧
    editor.addKeyframe(0.0, {
      viseme: VisemeType.SILENT,
      visemeWeight: 0.0
    });

    editor.addKeyframe(0.5, {
      viseme: VisemeType.AA,
      visemeWeight: 1.0
    });

    editor.addKeyframe(1.5, {
      viseme: VisemeType.EE,
      visemeWeight: 1.0
    });

    editor.addKeyframe(2.5, {
      viseme: VisemeType.OH,
      visemeWeight: 1.0
    });

    editor.addKeyframe(3.5, {
      viseme: VisemeType.FF,
      visemeWeight: 1.0
    });

    editor.addKeyframe(4.5, {
      viseme: VisemeType.SILENT,
      visemeWeight: 0.0
    });

    // 创建组合动画
    const combinedClip = editor.createClip('组合动画', 5.0, true);

    // 添加关键帧
    editor.addKeyframe(0.0, {
      expression: FacialExpressionType.NEUTRAL,
      expressionWeight: 0.0,
      viseme: VisemeType.SILENT,
      visemeWeight: 0.0
    });

    editor.addKeyframe(1.0, {
      expression: FacialExpressionType.HAPPY,
      expressionWeight: 1.0,
      viseme: VisemeType.AA,
      visemeWeight: 1.0
    });

    editor.addKeyframe(2.0, {
      expression: FacialExpressionType.SURPRISED,
      expressionWeight: 1.0,
      viseme: VisemeType.OH,
      visemeWeight: 1.0
    });

    editor.addKeyframe(3.0, {
      expression: FacialExpressionType.ANGRY,
      expressionWeight: 1.0,
      viseme: VisemeType.EE,
      visemeWeight: 1.0
    });

    editor.addKeyframe(4.0, {
      expression: FacialExpressionType.SAD,
      expressionWeight: 1.0,
      viseme: VisemeType.FF,
      visemeWeight: 1.0
    });

    editor.addKeyframe(5.0, {
      expression: FacialExpressionType.NEUTRAL,
      expressionWeight: 0.0,
      viseme: VisemeType.SILENT,
      visemeWeight: 0.0
    });

    // 设置当前片段
    editor.setCurrentClip('组合动画');
  }

  /**
   * 创建编辑器UI
   */
  private createEditorUI(): void {
    // 创建编辑器容器
    this.editorContainer = document.createElement('div');
    this.editorContainer.style.position = 'absolute';
    this.editorContainer.style.bottom = '0';
    this.editorContainer.style.left = '0';
    this.editorContainer.style.width = '100%';
    this.editorContainer.style.height = '200px';
    this.editorContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    this.editorContainer.style.color = 'white';
    this.editorContainer.style.padding = '10px';
    document.body.appendChild(this.editorContainer);

    // 创建控制栏
    const controlBar = document.createElement('div');
    controlBar.style.display = 'flex';
    controlBar.style.alignItems = 'center';
    controlBar.style.marginBottom = '10px';
    this.editorContainer.appendChild(controlBar);

    // 创建播放按钮
    this.playButton = document.createElement('button');
    this.playButton.textContent = '播放';
    this.playButton.onclick = this.togglePlayback.bind(this);
    controlBar.appendChild(this.playButton);

    // 创建时间显示
    const timeDisplay = document.createElement('div');
    timeDisplay.style.marginLeft = '10px';
    timeDisplay.textContent = '0.00 / 5.00';
    controlBar.appendChild(timeDisplay);

    // 创建时间轴容器
    this.timelineContainer = document.createElement('div');
    this.timelineContainer.style.height = '150px';
    this.timelineContainer.style.backgroundColor = 'rgba(50, 50, 50, 0.7)';
    this.timelineContainer.style.position = 'relative';
    this.editorContainer.appendChild(this.timelineContainer);

    // 创建时间指示器
    const timeIndicator = document.createElement('div');
    timeIndicator.style.position = 'absolute';
    timeIndicator.style.top = '0';
    timeIndicator.style.left = '0';
    timeIndicator.style.width = '2px';
    timeIndicator.style.height = '100%';
    timeIndicator.style.backgroundColor = 'red';
    this.timelineContainer.appendChild(timeIndicator);

    // 更新UI
    const updateUI = () => {
      // 更新时间显示
      timeDisplay.textContent = `${this.currentTime.toFixed(2)} / ${this.duration.toFixed(2)}`;

      // 更新时间指示器位置
      const position = (this.currentTime / this.duration) * 100;
      timeIndicator.style.left = `${position}%`;
    };

    // 时间轴点击处理
    this.timelineContainer.onclick = (event) => {
      const rect = this.timelineContainer.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const width = rect.width;
      const time = (x / width) * this.duration;

      this.setCurrentTime(time);
      updateUI();
    };

    // 初始更新UI
    updateUI();

    // 设置动画更新回调
    const editor = this.facialAnimationEditorSystem.getEditor(this.characterEntity);
    if (editor) {
      editor.addEventListener('timeChanged', (data) => {
        this.currentTime = data.time;
        updateUI();
      });

      editor.addEventListener('completed', () => {
        this.isPlaying = false;
        this.playButton!.textContent = '播放';
      });
    }
  }

  /**
   * 切换播放状态
   */
  private togglePlayback(): void {
    const editor = this.facialAnimationEditorSystem.getEditor(this.characterEntity);
    if (!editor) return;

    this.isPlaying = !this.isPlaying;

    if (this.isPlaying) {
      this.playButton!.textContent = '暂停';
      editor.play();
    } else {
      this.playButton!.textContent = '播放';
      editor.pause();
    }
  }

  /**
   * 设置当前时间
   * @param time 时间
   */
  private setCurrentTime(time: number): void {
    const editor = this.facialAnimationEditorSystem.getEditor(this.characterEntity);
    if (!editor) return;

    this.currentTime = Math.max(0, Math.min(this.duration, time));
    editor.setCurrentTime(this.currentTime);
  }

  /**
   * 启动示例
   */
  public start(): void {
    // 加载模型
    this.loadModel();

    // 创建编辑器UI
    this.createEditorUI();

    // 启动引擎
    this.engine.start();

    // 开始动画循环
    this.running = true;
    this.animate();

    console.log('面部动画编辑器示例已启动');
  }

  /**
   * 停止示例
   */
  public stop(): void {
    this.running = false;
    this.engine.stop();

    // 移除编辑器UI
    if (this.editorContainer) {
      document.body.removeChild(this.editorContainer);
      this.editorContainer = null;
    }

    console.log('面部动画编辑器示例已停止');
  }

  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;

    requestAnimationFrame(this.animate.bind(this));

    // 更新控制器
    this.controls.update();

    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }
}
