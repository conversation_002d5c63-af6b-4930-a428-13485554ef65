/**
 * 用户行为回放组件
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Button,
  Slider,
  Space,
  Typography,
  List,
  Tag,
  Tooltip,
  Divider,
  Empty,
  Statistic,
  Row,
  Col
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StepForwardOutlined,
  StepBackwardOutlined,
  ReloadOutlined,
  FieldTimeOutlined,
  EyeOutlined,
  MouseOutlined,
  KeyboardOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { UserAction, UserActionType } from './UserBehaviorAnalyzer';

const { Text, Title, Paragraph } = Typography;

/**
 * 用户行为回放组件属性
 */
interface UserActionPlayerProps {
  /** 用户行为数据 */
  actions: UserAction[];
  /** 回放速度 (1.0 = 正常速度) */
  speed?: number;
  /** 是否自动播放 */
  autoPlay?: boolean;
  /** 是否显示详细信息 */
  showDetails?: boolean;
  /** 是否显示统计信息 */
  showStats?: boolean;
  /** 回放完成回调 */
  onPlaybackComplete?: () => void;
}

/**
 * 用户行为回放组件
 */
const UserActionPlayer: React.FC<UserActionPlayerProps> = ({
  actions,
  speed = 1.0,
  autoPlay = false,
  showDetails = true,
  showStats = true,
  onPlaybackComplete
}) => {
  // 本地状态
  const [isPlaying, setIsPlaying] = useState<boolean>(autoPlay);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [playbackSpeed, setPlaybackSpeed] = useState<number>(speed);
  const [progress, setProgress] = useState<number>(0);
  const [currentTime, setCurrentTime] = useState<number>(0);
  
  // 引用
  const timerRef = useRef<number | null>(null);
  const cursorRef = useRef<HTMLDivElement | null>(null);
  
  // 计算总时长
  const totalDuration = actions.length > 1 
    ? actions[actions.length - 1].timestamp - actions[0].timestamp 
    : 0;
  
  // 开始回放
  const startPlayback = () => {
    if (actions.length === 0 || currentIndex >= actions.length - 1) {
      setCurrentIndex(0);
    }
    
    setIsPlaying(true);
  };
  
  // 暂停回放
  const pausePlayback = () => {
    setIsPlaying(false);
  };
  
  // 重置回放
  const resetPlayback = () => {
    setIsPlaying(false);
    setCurrentIndex(0);
    setProgress(0);
    setCurrentTime(0);
    
    // 重置光标位置
    if (cursorRef.current) {
      cursorRef.current.style.left = '0px';
      cursorRef.current.style.top = '0px';
    }
  };
  
  // 下一步
  const nextStep = () => {
    if (currentIndex < actions.length - 1) {
      setCurrentIndex(prev => prev + 1);
    } else if (onPlaybackComplete) {
      onPlaybackComplete();
    }
  };
  
  // 上一步
  const prevStep = () => {
    if (currentIndex > 0) {
      setCurrentIndex(prev => prev - 1);
    }
  };
  
  // 跳转到指定进度
  const jumpToProgress = (value: number) => {
    if (actions.length === 0) return;
    
    // 计算对应的索引
    const targetIndex = Math.floor((value / 100) * (actions.length - 1));
    setCurrentIndex(targetIndex);
    
    // 更新当前时间
    if (actions.length > 0) {
      setCurrentTime(actions[targetIndex].timestamp - actions[0].timestamp);
    }
  };
  
  // 格式化时间
  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  
  // 获取操作类型文本
  const getActionTypeText = (type: UserActionType) => {
    switch (type) {
      case UserActionType.MOUSE_MOVE:
        return '鼠标移动';
      case UserActionType.MOUSE_CLICK:
        return '鼠标点击';
      case UserActionType.KEY_PRESS:
        return '键盘输入';
      case UserActionType.SCROLL:
        return '滚动';
      case UserActionType.COMPONENT_INTERACTION:
        return '组件交互';
      case UserActionType.MENU_SELECTION:
        return '菜单选择';
      case UserActionType.TOOL_USAGE:
        return '工具使用';
      case UserActionType.PANEL_SWITCH:
        return '面板切换';
      case UserActionType.DIALOG_INTERACTION:
        return '对话框交互';
      case UserActionType.SCENE_INTERACTION:
        return '场景交互';
      case UserActionType.OBJECT_SELECTION:
        return '对象选择';
      case UserActionType.PROPERTY_CHANGE:
        return '属性修改';
      case UserActionType.NAVIGATION:
        return '导航';
      case UserActionType.SEARCH:
        return '搜索';
      case UserActionType.UNDO_REDO:
        return '撤销/重做';
      case UserActionType.SAVE_LOAD:
        return '保存/加载';
      case UserActionType.COLLABORATION:
        return '协作';
      case UserActionType.ERROR:
        return '错误';
      default:
        return type;
    }
  };
  
  // 获取操作类型图标
  const getActionTypeIcon = (type: UserActionType) => {
    switch (type) {
      case UserActionType.MOUSE_MOVE:
      case UserActionType.MOUSE_CLICK:
        return <MouseOutlined />;
      case UserActionType.KEY_PRESS:
        return <KeyboardOutlined />;
      default:
        return <EyeOutlined />;
    }
  };
  
  // 渲染统计信息
  const renderStats = () => {
    if (!showStats || actions.length === 0) return null;
    
    // 计算各类型操作数量
    const actionCounts: Record<string, number> = {};
    actions.forEach(action => {
      actionCounts[action.type] = (actionCounts[action.type] || 0) + 1;
    });
    
    return (
      <Card size="small" title="操作统计" bordered={false}>
        <Row gutter={16}>
          <Col span={8}>
            <Statistic
              title="总操作数"
              value={actions.length}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="总时长"
              value={formatTime(totalDuration)}
              prefix={<FieldTimeOutlined />}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="平均操作间隔"
              value={actions.length > 1 ? formatTime(totalDuration / (actions.length - 1)) : '0:00'}
            />
          </Col>
        </Row>
        
        <Divider />
        
        <List
          size="small"
          header={<Text strong>操作类型分布</Text>}
          dataSource={Object.entries(actionCounts)}
          renderItem={([type, count]) => (
            <List.Item>
              <Space>
                {getActionTypeIcon(type as UserActionType)}
                <Text>{getActionTypeText(type as UserActionType)}</Text>
              </Space>
              <Text>{count} ({Math.round((count / actions.length) * 100)}%)</Text>
            </List.Item>
          )}
        />
      </Card>
    );
  };
  
  // 渲染操作列表
  const renderActionList = () => {
    if (!showDetails || actions.length === 0) return null;
    
    return (
      <List
        size="small"
        header={<Text strong>操作详情</Text>}
        dataSource={actions}
        renderItem={(action, index) => (
          <List.Item style={{ backgroundColor: index === currentIndex ? '#f0f5ff' : undefined }}>
            <List.Item.Meta
              avatar={getActionTypeIcon(action.type)}
              title={
                <Space>
                  <Text>{getActionTypeText(action.type)}</Text>
                  {index === currentIndex && (
                    <Tag color="blue">当前</Tag>
                  )}
                </Space>
              }
              description={
                <Space direction="vertical">
                  <Text type="secondary">
                    时间: {formatTime(action.timestamp - actions[0].timestamp)}
                  </Text>
                  {action.type === UserActionType.MOUSE_MOVE && (
                    <Text type="secondary">
                      位置: ({action.data.x}, {action.data.y})
                    </Text>
                  )}
                </Space>
              }
            />
          </List.Item>
        )}
      />
    );
  };
  
  // 回放效果
  useEffect(() => {
    if (actions.length === 0) return;
    
    // 更新进度
    setProgress(Math.round((currentIndex / (actions.length - 1)) * 100) || 0);
    
    // 更新当前时间
    if (currentIndex < actions.length) {
      setCurrentTime(actions[currentIndex].timestamp - actions[0].timestamp);
    }
    
    // 模拟操作
    const currentAction = actions[currentIndex];
    if (currentAction) {
      // 移动光标
      if (cursorRef.current && (
        currentAction.type === UserActionType.MOUSE_MOVE || 
        currentAction.type === UserActionType.MOUSE_CLICK
      )) {
        cursorRef.current.style.left = `${currentAction.data.x}px`;
        cursorRef.current.style.top = `${currentAction.data.y}px`;
      }
    }
    
    // 检查是否播放完成
    if (currentIndex >= actions.length - 1 && isPlaying) {
      setIsPlaying(false);
      if (onPlaybackComplete) {
        onPlaybackComplete();
      }
    }
  }, [currentIndex, actions]);
  
  // 播放控制
  useEffect(() => {
    if (isPlaying && actions.length > 0) {
      // 清除现有定时器
      if (timerRef.current !== null) {
        window.clearTimeout(timerRef.current);
        timerRef.current = null;
      }
      
      // 设置新定时器
      if (currentIndex < actions.length - 1) {
        const currentAction = actions[currentIndex];
        const nextAction = actions[currentIndex + 1];
        const delay = (nextAction.timestamp - currentAction.timestamp) / playbackSpeed;
        
        timerRef.current = window.setTimeout(() => {
          setCurrentIndex(prev => prev + 1);
        }, delay);
      }
    }
    
    // 组件卸载时清除定时器
    return () => {
      if (timerRef.current !== null) {
        window.clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [isPlaying, currentIndex, actions, playbackSpeed]);
  
  // 自动播放
  useEffect(() => {
    if (autoPlay && actions.length > 0) {
      startPlayback();
    }
    
    return () => {
      if (timerRef.current !== null) {
        window.clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    };
  }, []);
  
  if (actions.length === 0) {
    return (
      <Empty description="没有可回放的操作记录" />
    );
  }
  
  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Card size="small" title="操作回放" bordered={false}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ position: 'relative', height: '300px', border: '1px solid #d9d9d9', overflow: 'hidden' }}>
            {/* 回放区域 */}
            <div
              ref={cursorRef}
              style={{
                position: 'absolute',
                width: '20px',
                height: '20px',
                borderRadius: '50%',
                backgroundColor: 'rgba(24, 144, 255, 0.5)',
                transform: 'translate(-50%, -50%)',
                pointerEvents: 'none',
                transition: 'left 0.1s, top 0.1s'
              }}
            />
          </div>
          
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <Text>{formatTime(currentTime)}</Text>
            <Text>{formatTime(totalDuration)}</Text>
          </Space>
          
          <Slider
            value={progress}
            onChange={jumpToProgress}
            tooltipVisible={false}
          />
          
          <Space style={{ width: '100%', justifyContent: 'center' }}>
            <Button
              icon={<StepBackwardOutlined />}
              onClick={prevStep}
              disabled={currentIndex <= 0}
            />
            <Button
              type="primary"
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={isPlaying ? pausePlayback : startPlayback}
            >
              {isPlaying ? '暂停' : '播放'}
            </Button>
            <Button
              icon={<StepForwardOutlined />}
              onClick={nextStep}
              disabled={currentIndex >= actions.length - 1}
            />
            <Button
              icon={<ReloadOutlined />}
              onClick={resetPlayback}
            >
              重置
            </Button>
          </Space>
          
          <Space style={{ width: '100%' }}>
            <Text>速度:</Text>
            <Slider
              min={0.25}
              max={4}
              step={0.25}
              value={playbackSpeed}
              onChange={setPlaybackSpeed}
              style={{ width: 200 }}
              marks={{
                0.25: '0.25x',
                1: '1x',
                2: '2x',
                4: '4x'
              }}
            />
          </Space>
        </Space>
      </Card>
      
      {showStats && renderStats()}
      
      {showDetails && renderActionList()}
    </Space>
  );
};

export default UserActionPlayer;
