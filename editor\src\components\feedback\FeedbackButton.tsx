/**
 * 反馈按钮组件
 * 用于在编辑器中添加反馈入口
 */
import React, { useState } from 'react';
import { <PERSON><PERSON>, Toolt<PERSON>, Drawer, Badge, Popover } from 'antd';
import { CommentOutlined, BulbOutlined, BugOutlined, RocketOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import FeedbackForm from './FeedbackForm';
import AnimationFeedbackForm from './AnimationFeedbackForm';
import './FeedbackButton.less';

export interface FeedbackButtonProps {
  /** 反馈类型 */
  type: 'animation' | 'physics' | 'rendering' | 'editor' | 'general';
  /** 反馈子类型 */
  subType?: string;
  /** 相关实体ID */
  entityId?: string;
  /** 相关资源ID */
  resourceId?: string;
  /** 性能数据 */
  performanceData?: any;
  /** 按钮类型 */
  buttonType?: 'primary' | 'default' | 'text' | 'link' | 'dashed';
  /** 按钮大小 */
  buttonSize?: 'large' | 'middle' | 'small';
  /** 是否显示文本 */
  showText?: boolean;
  /** 是否使用抽屉 */
  useDrawer?: boolean;
  /** 是否使用气泡卡片 */
  usePopover?: boolean;
  /** 是否显示徽标 */
  showBadge?: boolean;
  /** 徽标数量 */
  badgeCount?: number;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
}

/**
 * 反馈按钮组件
 */
const FeedbackButton: React.FC<FeedbackButtonProps> = ({
  type,
  subType,
  entityId,
  resourceId,
  performanceData,
  buttonType = 'default',
  buttonSize = 'middle',
  showText = true,
  useDrawer = true,
  usePopover = false,
  showBadge = false,
  badgeCount = 0,
  style,
  className
}) => {
  const { t } = useTranslation();
  const [visible, setVisible] = useState(false);

  // 获取图标
  const getIcon = () => {
    switch (type) {
      case 'animation':
        return <RocketOutlined />;
      case 'physics':
        return <BugOutlined />;
      case 'rendering':
        return <BulbOutlined />;
      default:
        return <CommentOutlined />;
    }
  };

  // 获取文本
  const getText = () => {
    switch (type) {
      case 'animation':
        return t('feedback.button.animation');
      case 'physics':
        return t('feedback.button.physics');
      case 'rendering':
        return t('feedback.button.rendering');
      case 'editor':
        return t('feedback.button.editor');
      default:
        return t('feedback.button.general');
    }
  };

  // 打开反馈表单
  const showFeedback = () => {
    setVisible(true);
  };

  // 关闭反馈表单
  const closeFeedback = () => {
    setVisible(false);
  };

  // 提交成功回调
  const handleSuccess = () => {
    // 可以在这里添加提交成功后的逻辑
  };

  // 渲染反馈表单
  const renderFeedbackForm = () => {
    if (type === 'animation' && subType) {
      return (
        <AnimationFeedbackForm
          subType={subType as any}
          entityId={entityId}
          resourceId={resourceId}
          performanceData={performanceData}
          onClose={closeFeedback}
          onSuccess={handleSuccess}
        />
      );
    }

    return (
      <FeedbackForm
        type={type}
        subType={subType}
        entityId={entityId}
        resourceId={resourceId}
        onClose={closeFeedback}
        onSuccess={handleSuccess}
      />
    );
  };

  // 渲染按钮
  const renderButton = () => {
    const button = (
      <Button
        type={buttonType}
        size={buttonSize}
        icon={getIcon()}
        onClick={showFeedback}
        className={`feedback-button ${className || ''}`}
        style={style}
      >
        {showText && getText()}
      </Button>
    );

    // 如果需要显示徽标
    if (showBadge) {
      return (
        <Badge count={badgeCount} dot={badgeCount === 0}>
          {button}
        </Badge>
      );
    }

    return button;
  };

  // 如果使用气泡卡片
  if (usePopover) {
    return (
      <Popover
        content={renderFeedbackForm()}
        title={t('feedback.title.general')}
        trigger="click"
        open={visible}
        onOpenChange={setVisible}
        overlayClassName="feedback-popover"
      >
        <Tooltip title={t('feedback.tooltip')}>
          {renderButton()}
        </Tooltip>
      </Popover>
    );
  }

  // 如果使用抽屉
  if (useDrawer) {
    return (
      <>
        <Tooltip title={t('feedback.tooltip')}>
          {renderButton()}
        </Tooltip>

        <Drawer
          title={t('feedback.title.general')}
          placement="right"
          onClose={closeFeedback}
          open={visible}
          width={500}
          destroyOnClose
        >
          {renderFeedbackForm()}
        </Drawer>
      </>
    );
  }

  // 默认使用模态框
  return (
    <>
      <Tooltip title={t('feedback.tooltip')}>
        {renderButton()}
      </Tooltip>

      {visible && renderFeedbackForm()}
    </>
  );
};

export default FeedbackButton;
