/**
 * 优化历史记录面板组件
 * 用于显示和比较场景优化历史
 */
import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Tabs, Empty, Tooltip, Modal, Descriptions, Tag, Typography, Row, Col, Statistic, Timeline, List, Divider } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  HistoryOutlined,
  DeleteOutlined,
  CompareOutlined,
  InfoCircleOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LineChartOutlined,
  BarChartOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { OptimizationHistory, OptimizationHistoryEntry, OptimizationComparisonResult } from 'ir-engine-core/debug/OptimizationHistory';
import { OptimizationType } from 'ir-engine-core/debug/SceneOptimizer';
import './HistoryPanel.less';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { confirm } = Modal;

interface HistoryPanelProps {
  className?: string;
}

/**
 * 优化历史记录面板组件
 */
const HistoryPanel: React.FC<HistoryPanelProps> = ({ className }) => {
  const { t } = useTranslation();

  // 从Redux获取当前场景
  const activeScene = useSelector((state: RootState) => state.scene.activeScene);

  // 状态
  const [history, setHistory] = useState<OptimizationHistoryEntry[]>([]);
  const [selectedEntryIds, setSelectedEntryIds] = useState<string[]>([]);
  const [comparisonResult, setComparisonResult] = useState<OptimizationComparisonResult | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState<boolean>(false);
  const [detailEntry, setDetailEntry] = useState<OptimizationHistoryEntry | null>(null);

  // 初始化
  useEffect(() => {
    loadHistory();
  }, [activeScene]);

  // 加载历史记录
  const loadHistory = () => {
    const historyManager = OptimizationHistory.getInstance();
    const sceneId = activeScene?.getId();

    if (sceneId) {
      // 加载当前场景的历史记录
      setHistory(historyManager.getHistory(sceneId));
    } else {
      // 加载所有历史记录
      setHistory(historyManager.getHistory());
    }

    // 清除选择和比较结果
    setSelectedEntryIds([]);
    setComparisonResult(null);
  };

  // 处理选择变化
  const handleSelectionChange = (selectedRowKeys: React.Key[]) => {
    setSelectedEntryIds(selectedRowKeys as string[]);

    // 如果选择了两个记录，自动比较
    if (selectedRowKeys.length === 2) {
      compareEntries(selectedRowKeys[0] as string, selectedRowKeys[1] as string);
    } else {
      setComparisonResult(null);
    }
  };

  // 比较记录
  const compareEntries = (id1: string, id2: string) => {
    const historyManager = OptimizationHistory.getInstance();
    const result = historyManager.compareEntries(id1, id2);

    if (result) {
      setComparisonResult(result);
    }
  };

  // 查看详情
  const viewDetails = (entry: OptimizationHistoryEntry) => {
    setDetailEntry(entry);
    setDetailModalVisible(true);
  };

  // 删除记录
  const deleteEntry = (id: string) => {
    confirm({
      title: t('debug.history.confirmDelete'),
      icon: <ExclamationCircleOutlined />,
      content: t('debug.history.confirmDeleteContent'),
      onOk() {
        const historyManager = OptimizationHistory.getInstance();
        historyManager.deleteEntry(id);
        loadHistory();
      }
    });
  };

  // 清空历史记录
  const clearHistory = () => {
    confirm({
      title: t('debug.history.confirmClearAll'),
      icon: <ExclamationCircleOutlined />,
      content: t('debug.history.confirmClearAllContent'),
      onOk() {
        const historyManager = OptimizationHistory.getInstance();
        historyManager.clearHistory();
        loadHistory();
      }
    });
  };

  // 获取优化类型标签
  const getOptimizationTypeTag = (type: OptimizationType) => {
    const typeLabels: Record<OptimizationType, { label: string, color: string }> = {
      [OptimizationType.LOD]: { label: 'LOD优化', color: 'blue' },
      [OptimizationType.FRUSTUM_CULLING]: { label: '视锥体剔除', color: 'green' },
      [OptimizationType.INSTANCED_RENDERING]: { label: '实例化渲染', color: 'purple' },
      [OptimizationType.BATCHING]: { label: '批处理', color: 'cyan' },
      [OptimizationType.MATERIAL_OPTIMIZATION]: { label: '材质优化', color: 'magenta' },
      [OptimizationType.TEXTURE_OPTIMIZATION]: { label: '纹理优化', color: 'orange' },
      [OptimizationType.GEOMETRY_OPTIMIZATION]: { label: '几何体优化', color: 'lime' },
      [OptimizationType.LIGHT_OPTIMIZATION]: { label: '灯光优化', color: 'gold' },
      [OptimizationType.SHADOW_OPTIMIZATION]: { label: '阴影优化', color: 'geekblue' },
      [OptimizationType.MEMORY_OPTIMIZATION]: { label: '内存优化', color: 'red' },
      [OptimizationType.OCCLUSION_CULLING]: { label: '遮挡剔除', color: 'volcano' },
      [OptimizationType.DETAIL_TEXTURE]: { label: '细节纹理', color: 'pink' },
      [OptimizationType.MESH_SIMPLIFICATION]: { label: '网格简化', color: 'purple' },
      [OptimizationType.PARTICLE_OPTIMIZATION]: { label: '粒子优化', color: 'cyan' },
      [OptimizationType.ANIMATION_OPTIMIZATION]: { label: '动画优化', color: 'blue' }
    };

    const typeInfo = typeLabels[type] || { label: type, color: 'default' };

    return <Tag color={typeInfo.color}>{typeInfo.label}</Tag>;
  };

  // 表格列定义
  const columns = [
    {
      title: t('debug.history.date'),
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp: number) => new Date(timestamp).toLocaleString(),
      sorter: (a: OptimizationHistoryEntry, b: OptimizationHistoryEntry) => a.timestamp - b.timestamp,
      defaultSortOrder: 'descend' as 'descend'
    },
    {
      title: t('debug.history.scene'),
      dataIndex: 'sceneName',
      key: 'sceneName'
    },
    {
      title: t('debug.history.description'),
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: t('debug.history.optimizations'),
      dataIndex: 'appliedOptimizations',
      key: 'appliedOptimizations',
      render: (optimizations: OptimizationType[]) => (
        <Space size={[0, 4]} wrap>
          {optimizations.map((type, index) => (
            <span key={index}>{getOptimizationTypeTag(type)}</span>
          ))}
        </Space>
      )
    },
    {
      title: t('debug.history.scoreBefore'),
      dataIndex: 'scoreBefore',
      key: 'scoreBefore',
      render: (score: number) => Math.round(score),
      sorter: (a: OptimizationHistoryEntry, b: OptimizationHistoryEntry) => a.scoreBefore - b.scoreBefore
    },
    {
      title: t('debug.history.scoreAfter'),
      dataIndex: 'scoreAfter',
      key: 'scoreAfter',
      render: (score: number) => Math.round(score),
      sorter: (a: OptimizationHistoryEntry, b: OptimizationHistoryEntry) => a.scoreAfter - b.scoreAfter
    },
    {
      title: t('debug.history.improvement'),
      key: 'improvement',
      render: (text: string, record: OptimizationHistoryEntry) => {
        const improvement = record.scoreAfter - record.scoreBefore;
        const percent = record.scoreBefore !== 0 ? (improvement / record.scoreBefore) * 100 : 0;

        return (
          <span style={{ color: improvement >= 0 ? '#52c41a' : '#f5222d' }}>
            {improvement >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
            {Math.abs(Math.round(percent))}%
          </span>
        );
      },
      sorter: (a: OptimizationHistoryEntry, b: OptimizationHistoryEntry) =>
        (a.scoreAfter - a.scoreBefore) - (b.scoreAfter - b.scoreBefore)
    },
    {
      title: t('debug.history.actions'),
      key: 'actions',
      render: (text: string, record: OptimizationHistoryEntry) => (
        <Space>
          <Tooltip title={t('debug.history.viewDetails')}>
            <Button
              icon={<InfoCircleOutlined />}
              size="small"
              onClick={() => viewDetails(record)}
            />
          </Tooltip>
          <Tooltip title={t('debug.history.delete')}>
            <Button
              icon={<DeleteOutlined />}
              size="small"
              danger
              onClick={() => deleteEntry(record.id)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 渲染比较结果
  const renderComparisonResult = () => {
    if (!comparisonResult) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={t('debug.history.selectTwoEntries')}
        />
      );
    }

    const { baseEntry, compareEntry, scoreDifference, scorePercentChange, statsChanges } = comparisonResult;

    return (
      <div className="comparison-result">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Card>
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title={t('debug.history.baseScore')}
                    value={Math.round(baseEntry.scoreAfter)}
                    suffix={<Tooltip title={baseEntry.description}><InfoCircleOutlined /></Tooltip>}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title={t('debug.history.compareScore')}
                    value={Math.round(compareEntry.scoreAfter)}
                    suffix={<Tooltip title={compareEntry.description}><InfoCircleOutlined /></Tooltip>}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title={t('debug.history.improvement')}
                    value={Math.round(scorePercentChange)}
                    precision={1}
                    valueStyle={{ color: scoreDifference >= 0 ? '#3f8600' : '#cf1322' }}
                    prefix={scoreDifference >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                    suffix="%"
                  />
                </Col>
              </Row>
            </Card>
          </Col>

          <Col span={24}>
            <Card title={t('debug.history.statsComparison')}>
              <Table
                dataSource={statsChanges.map((change, index) => ({ ...change, key: index }))}
                pagination={false}
                size="small"
                columns={[
                  {
                    title: t('debug.history.metric'),
                    dataIndex: 'displayName',
                    key: 'displayName'
                  },
                  {
                    title: t('debug.history.baseValue'),
                    dataIndex: 'baseValue',
                    key: 'baseValue',
                    render: (value: number) => Math.round(value).toLocaleString()
                  },
                  {
                    title: t('debug.history.compareValue'),
                    dataIndex: 'compareValue',
                    key: 'compareValue',
                    render: (value: number) => Math.round(value).toLocaleString()
                  },
                  {
                    title: t('debug.history.change'),
                    dataIndex: 'difference',
                    key: 'difference',
                    render: (difference: number, record: any) => {
                      // 对于某些指标，减少是好的（如三角形数量、内存使用等）
                      // 对于其他指标，增加是好的（如总体得分）
                      const isPositiveGood = record.field === 'overallScore';
                      const isPositive = difference > 0;
                      const isGood = isPositiveGood ? isPositive : !isPositive;

                      return (
                        <span style={{ color: isGood ? '#52c41a' : '#f5222d' }}>
                          {difference > 0 ? '+' : ''}{Math.round(difference).toLocaleString()}
                        </span>
                      );
                    }
                  },
                  {
                    title: t('debug.history.percentChange'),
                    dataIndex: 'percentChange',
                    key: 'percentChange',
                    render: (percentChange: number, record: any) => {
                      const isPositiveGood = record.field === 'overallScore';
                      const isPositive = percentChange > 0;
                      const isGood = isPositiveGood ? isPositive : !isPositive;

                      return (
                        <span style={{ color: isGood ? '#52c41a' : '#f5222d' }}>
                          {percentChange > 0 ? '+' : ''}{Math.round(percentChange)}%
                        </span>
                      );
                    }
                  }
                ]}
              />
            </Card>
          </Col>

          <Col span={24}>
            <Card title={t('debug.history.appliedOptimizations')}>
              <Timeline>
                {compareEntry.appliedOptimizations.map((type, index) => (
                  <Timeline.Item key={index} color="blue">
                    {getOptimizationTypeTag(type)}
                  </Timeline.Item>
                ))}
              </Timeline>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染详情模态框
  const renderDetailModal = () => {
    if (!detailEntry) {
      return null;
    }

    const { analysisResult, appliedOptimizations, scoreBefore, scoreAfter } = detailEntry;

    return (
      <Modal
        title={t('debug.history.optimizationDetails')}
        visible={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            {t('debug.history.close')}
          </Button>
        ]}
        width={800}
      >
        <Descriptions bordered column={2}>
          <Descriptions.Item label={t('debug.history.date')} span={2}>
            {new Date(detailEntry.timestamp).toLocaleString()}
          </Descriptions.Item>
          <Descriptions.Item label={t('debug.history.scene')} span={2}>
            {detailEntry.sceneName}
          </Descriptions.Item>
          <Descriptions.Item label={t('debug.history.description')} span={2}>
            {detailEntry.description}
          </Descriptions.Item>
          <Descriptions.Item label={t('debug.history.scoreBefore')}>
            {Math.round(scoreBefore)}
          </Descriptions.Item>
          <Descriptions.Item label={t('debug.history.scoreAfter')}>
            {Math.round(scoreAfter)}
          </Descriptions.Item>
        </Descriptions>

        <Divider orientation="left">{t('debug.history.appliedOptimizations')}</Divider>
        <div className="detail-optimizations">
          {appliedOptimizations.map((type, index) => (
            <div key={index} className="detail-optimization-item">
              {getOptimizationTypeTag(type)}
            </div>
          ))}
        </div>

        <Divider orientation="left">{t('debug.history.sceneStatistics')}</Divider>
        <List
          size="small"
          bordered
          dataSource={[
            { label: t('debug.history.entityCount'), value: analysisResult.entityCount },
            { label: t('debug.history.renderableCount'), value: analysisResult.renderableCount },
            { label: t('debug.history.triangleCount'), value: Math.round(analysisResult.triangleCount).toLocaleString() },
            { label: t('debug.history.vertexCount'), value: Math.round(analysisResult.vertexCount).toLocaleString() },
            { label: t('debug.history.materialCount'), value: analysisResult.materialCount },
            { label: t('debug.history.textureCount'), value: analysisResult.textureCount },
            { label: t('debug.history.textureMemory'), value: `${Math.round(analysisResult.textureMemory)} MB` },
            { label: t('debug.history.lightCount'), value: analysisResult.lightCount },
            { label: t('debug.history.drawCalls'), value: analysisResult.drawCalls },
            { label: t('debug.history.memoryUsage'), value: `${Math.round(analysisResult.memoryUsage)} MB` }
          ]}
          renderItem={item => (
            <List.Item>
              <Text strong>{item.label}:</Text> {item.value}
            </List.Item>
          )}
        />
      </Modal>
    );
  };

  return (
    <div className={`history-panel ${className || ''}`}>
      <div className="history-toolbar">
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadHistory}
          >
            {t('debug.history.refresh')}
          </Button>

          <Button
            icon={<DeleteOutlined />}
            danger
            onClick={clearHistory}
            disabled={history.length === 0}
          >
            {t('debug.history.clearAll')}
          </Button>

          <Button
            icon={<CompareOutlined />}
            disabled={selectedEntryIds.length !== 2}
            onClick={() => {
              if (selectedEntryIds.length === 2) {
                compareEntries(selectedEntryIds[0], selectedEntryIds[1]);
              }
            }}
          >
            {t('debug.history.compare')}
          </Button>
        </Space>
      </div>

      <div className="history-content">
        <Tabs defaultActiveKey="history" type="card">
          <TabPane
            tab={
              <span>
                <HistoryOutlined />
                {t('debug.history.historyTab')}
              </span>
            }
            key="history"
          >
            {history.length === 0 ? (
              <Empty
                description={t('debug.history.noHistory')}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ) : (
              <Table
                dataSource={history.map(entry => ({ ...entry, key: entry.id }))}
                columns={columns}
                pagination={{ pageSize: 10 }}
                rowSelection={{
                  type: 'checkbox',
                  selectedRowKeys: selectedEntryIds,
                  onChange: handleSelectionChange,
                  selections: [
                    Table.SELECTION_ALL,
                    Table.SELECTION_INVERT,
                    Table.SELECTION_NONE,
                    {
                      key: 'latest-two',
                      text: t('debug.history.latestTwo'),
                      onSelect: () => {
                        if (history.length >= 2) {
                          const latestTwo = history.slice(0, 2).map(entry => entry.id);
                          setSelectedEntryIds(latestTwo);
                          compareEntries(latestTwo[0], latestTwo[1]);
                        }
                      }
                    }
                  ]
                }}
              />
            )}
          </TabPane>

          <TabPane
            tab={
              <span>
                <CompareOutlined />
                {t('debug.history.comparisonTab')}
              </span>
            }
            key="comparison"
          >
            {renderComparisonResult()}
          </TabPane>
        </Tabs>
      </div>

      {renderDetailModal()}
    </div>
  );
};

export default HistoryPanel;
