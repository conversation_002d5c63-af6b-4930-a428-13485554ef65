/**
 * 资源热更新状态切片
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

/**
 * 资源更新状态
 */
export enum ResourceUpdateStatus {
  NONE = 'none',
  CHECKING = 'checking',
  AVAILABLE = 'available',
  DOWNLOADING = 'downloading',
  DOWNLOADED = 'downloaded',
  APPLYING = 'applying',
  APPLIED = 'applied',
  ERROR = 'error',
  CANCELLED = 'cancelled',
  UP_TO_DATE = 'upToDate',
  ROLLED_BACK = 'rolledBack'
}

/**
 * 资源更新类型
 */
export enum ResourceUpdateType {
  ASSETS = 'assets',
  SCRIPTS = 'scripts',
  SHADERS = 'shaders',
  CONFIGS = 'configs',
  SYSTEM = 'system'
}

/**
 * 资源更新配置
 */
export interface ResourceUpdateConfig {
  autoCheckInterval: number; // 自动检查间隔（秒）
  autoDownloadUpdates: boolean; // 自动下载更新
  autoApplyUpdates: boolean; // 自动应用更新
  updateTypes: ResourceUpdateType[]; // 更新类型
  maxConcurrentDownloads: number; // 最大并发下载数
  retryCount: number; // 重试次数
  retryDelay: number; // 重试延迟（毫秒）
  backupBeforeUpdate: boolean; // 更新前备份
  notifyOnUpdateAvailable: boolean; // 有更新时通知
  updateServer: string; // 更新服务器地址
  updateChannel: string; // 更新渠道
  enableDeltaUpdates: boolean; // 启用增量更新
  compressionType: string; // 压缩类型
  maxBackupCount: number; // 最大备份数量
  cleanupAfterUpdate: boolean; // 更新后清理
}

/**
 * 资源更新信息
 */
export interface ResourceUpdateInfo {
  id: string; // 更新ID
  version: string; // 版本
  timestamp: number; // 时间戳
  status: ResourceUpdateStatus; // 状态
  progress: number; // 进度（0-100）
  details: any[]; // 详情
  size: number; // 大小（字节）
  type: ResourceUpdateType; // 类型
  description: string; // 描述
  changelogs?: string[]; // 更新日志
  retryCount?: number; // 重试次数
}

/**
 * 资源热更新状态
 */
export interface ResourceHotUpdateState {
  status: ResourceUpdateStatus; // 更新状态
  progress: number; // 更新进度（0-100）
  updateHistory: ResourceUpdateInfo[]; // 更新历史
  currentUpdate: ResourceUpdateInfo | null; // 当前更新
  config: ResourceUpdateConfig; // 更新配置
  showUpdatePanel: boolean; // 显示更新面板
  showConfigPanel: boolean; // 显示配置面板
  isRollingBack: boolean; // 是否正在回滚
}

/**
 * 初始状态
 */
const initialState: ResourceHotUpdateState = {
  status: ResourceUpdateStatus.NONE,
  progress: 0,
  updateHistory: [],
  currentUpdate: null,
  config: {
    autoCheckInterval: 3600, // 默认每小时自动检查更新
    autoDownloadUpdates: false, // 默认不自动下载更新
    autoApplyUpdates: false, // 默认不自动应用更新
    updateTypes: [ResourceUpdateType.ASSETS, ResourceUpdateType.SCRIPTS], // 默认更新资源和脚本
    maxConcurrentDownloads: 3, // 默认最大并发下载数
    retryCount: 3, // 默认重试次数
    retryDelay: 1000, // 默认重试延迟（毫秒）
    backupBeforeUpdate: true, // 默认更新前备份
    notifyOnUpdateAvailable: true, // 默认有更新时通知
    updateServer: '/api/updates', // 默认更新服务器地址
    updateChannel: 'stable', // 默认更新渠道
    enableDeltaUpdates: true, // 默认启用增量更新
    compressionType: 'gzip', // 默认压缩类型
    maxBackupCount: 5, // 默认最大备份数量
    cleanupAfterUpdate: true, // 默认更新后清理
  },
  showUpdatePanel: false,
  showConfigPanel: false,
  isRollingBack: false
};

/**
 * 创建切片
 */
export const resourceHotUpdateSlice = createSlice({
  name: 'resourceHotUpdate',
  initialState,
  reducers: {
    // 设置更新状态
    setUpdateStatus: (state, action: PayloadAction<ResourceUpdateStatus>) => {
      state.status = action.payload;
    },
    
    // 设置更新进度
    setUpdateProgress: (state, action: PayloadAction<number>) => {
      state.progress = action.payload;
    },
    
    // 添加更新历史
    addUpdateHistory: (state, action: PayloadAction<ResourceUpdateInfo>) => {
      // 检查是否已存在相同ID的更新
      const existingIndex = state.updateHistory.findIndex(u => u.id === action.payload.id);
      
      if (existingIndex >= 0) {
        // 更新现有更新
        state.updateHistory[existingIndex] = action.payload;
      } else {
        // 添加新更新
        state.updateHistory.push(action.payload);
      }
    },
    
    // 设置当前更新
    setCurrentUpdate: (state, action: PayloadAction<ResourceUpdateInfo | null>) => {
      state.currentUpdate = action.payload;
    },
    
    // 设置更新配置
    setUpdateConfig: (state, action: PayloadAction<ResourceUpdateConfig>) => {
      state.config = action.payload;
    },
    
    // 设置显示更新面板
    setShowUpdatePanel: (state, action: PayloadAction<boolean>) => {
      state.showUpdatePanel = action.payload;
    },
    
    // 设置显示配置面板
    setShowConfigPanel: (state, action: PayloadAction<boolean>) => {
      state.showConfigPanel = action.payload;
    },
    
    // 设置是否正在回滚
    setIsRollingBack: (state, action: PayloadAction<boolean>) => {
      state.isRollingBack = action.payload;
    },
    
    // 清除更新历史
    clearUpdateHistory: (state) => {
      state.updateHistory = [];
    }
  }
});

// 导出操作
export const {
  setUpdateStatus,
  setUpdateProgress,
  addUpdateHistory,
  setCurrentUpdate,
  setUpdateConfig,
  setShowUpdatePanel,
  setShowConfigPanel,
  setIsRollingBack,
  clearUpdateHistory
} = resourceHotUpdateSlice.actions;

// 导出reducer
export default resourceHotUpdateSlice.reducer;
