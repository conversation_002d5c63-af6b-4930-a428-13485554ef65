/**
 * 资源依赖服务
 * 用于管理资源依赖关系，提供依赖分析和优化功能
 */
import { DependencyType, DependencyInfo } from '../../engine/src/assets/EnhancedResourceDependencyManager';
import { AssetType } from '../../engine/src/assets/AssetTypes';
import { message } from 'antd';

// 资源信息接口
export interface ResourceInfo {
  id: string;
  name: string;
  type: AssetType;
  url: string;
  size: number;
  lastModified: number;
  metadata?: Record<string, any>;
}

// 资源使用信息接口
export interface ResourceUsageInfo {
  refCount: number;
  lastAccessed: number;
  loadCount: number;
  loadTime: number;
  usedIn: string[];
}

// 依赖分析结果接口
export interface DependencyAnalysisResult {
  // 循环依赖
  circularDependencies: string[][];
  // 未使用资源
  unusedResources: string[];
  // 重复资源
  duplicateResources: string[][];
  // 大型资源
  largeResources: string[];
  // 资源类型统计
  resourcesByType: Record<string, number>;
  // 依赖链
  dependencyChains: string[][];
  // 加载顺序
  loadOrder: string[];
  // 内存使用
  memoryUsage: number;
}

// 优化建议接口
export interface OptimizationSuggestion {
  id: string;
  type: 'circular' | 'unused' | 'duplicate' | 'large' | 'lazy' | 'preload' | 'merge' | 'split';
  severity: 'low' | 'medium' | 'high';
  description: string;
  resources: string[];
  impact: string;
  solution: string;
}

// 优化操作接口
export interface OptimizationOperation {
  id: string;
  type: string;
  description: string;
  resources: string[];
  apply: () => Promise<boolean>;
}

/**
 * 资源依赖服务类
 */
export class ResourceDependencyService {
  private static instance: ResourceDependencyService;
  private engineDependencyManager: any; // 实际项目中应使用引擎的依赖管理器类型
  private resources: Map<string, ResourceInfo> = new Map();
  private dependencies: Map<string, DependencyInfo[]> = new Map();
  private dependents: Map<string, string[]> = new Map();
  private usageInfo: Map<string, ResourceUsageInfo> = new Map();

  /**
   * 获取单例实例
   * @returns 资源依赖服务实例
   */
  public static getInstance(): ResourceDependencyService {
    if (!ResourceDependencyService.instance) {
      ResourceDependencyService.instance = new ResourceDependencyService();
    }
    return ResourceDependencyService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    // 在实际项目中，应该从引擎获取依赖管理器实例
    this.engineDependencyManager = null;
    this.initMockData(); // 临时使用模拟数据
  }

  /**
   * 初始化模拟数据（临时使用）
   */
  private initMockData(): void {
    // 模拟资源数据
    const mockResources: ResourceInfo[] = [
      { id: 'texture1', name: '地面纹理', type: 'texture', url: '/textures/ground.jpg', size: 1024 * 1024 * 2, lastModified: Date.now() },
      { id: 'texture2', name: '墙壁纹理', type: 'texture', url: '/textures/wall.jpg', size: 1024 * 1024 * 1.5, lastModified: Date.now() },
      { id: 'model1', name: '角色模型', type: 'model', url: '/models/character.glb', size: 1024 * 1024 * 5, lastModified: Date.now() },
      { id: 'material1', name: '地面材质', type: 'material', url: '/materials/ground.mat', size: 1024 * 10, lastModified: Date.now() },
      { id: 'material2', name: '墙壁材质', type: 'material', url: '/materials/wall.mat', size: 1024 * 12, lastModified: Date.now() },
      { id: 'shader1', name: '基础着色器', type: 'shader', url: '/shaders/basic.shader', size: 1024 * 5, lastModified: Date.now() },
      { id: 'audio1', name: '背景音乐', type: 'audio', url: '/audio/background.mp3', size: 1024 * 1024 * 3, lastModified: Date.now() },
      { id: 'scene1', name: '主场景', type: 'scene', url: '/scenes/main.scene', size: 1024 * 50, lastModified: Date.now() },
    ];

    // 添加到资源映射
    mockResources.forEach(resource => {
      this.resources.set(resource.id, resource);
    });

    // 模拟依赖关系
    this.dependencies.set('material1', [
      { id: 'texture1', type: DependencyType.STRONG, priority: 10 },
      { id: 'shader1', type: DependencyType.STRONG, priority: 5 }
    ]);
    this.dependencies.set('material2', [
      { id: 'texture2', type: DependencyType.STRONG, priority: 10 },
      { id: 'shader1', type: DependencyType.STRONG, priority: 5 }
    ]);
    this.dependencies.set('model1', [
      { id: 'material1', type: DependencyType.STRONG, priority: 8 },
      { id: 'material2', type: DependencyType.WEAK, priority: 3 }
    ]);
    this.dependencies.set('scene1', [
      { id: 'model1', type: DependencyType.STRONG, priority: 10 },
      { id: 'audio1', type: DependencyType.LAZY, priority: 2 }
    ]);

    // 模拟被依赖关系
    this.dependents.set('texture1', ['material1']);
    this.dependents.set('texture2', ['material2']);
    this.dependents.set('shader1', ['material1', 'material2']);
    this.dependents.set('material1', ['model1']);
    this.dependents.set('material2', ['model1']);
    this.dependents.set('model1', ['scene1']);
    this.dependents.set('audio1', ['scene1']);

    // 模拟使用信息
    mockResources.forEach(resource => {
      this.usageInfo.set(resource.id, {
        refCount: Math.floor(Math.random() * 10),
        lastAccessed: Date.now() - Math.floor(Math.random() * 1000000),
        loadCount: Math.floor(Math.random() * 20),
        loadTime: Math.floor(Math.random() * 500),
        usedIn: ['场景1', '场景2']
      });
    });
  }

  /**
   * 设置引擎依赖管理器
   * @param manager 引擎依赖管理器
   */
  public setEngineDependencyManager(manager: any): void {
    this.engineDependencyManager = manager;
  }

  /**
   * 获取所有资源
   * @returns 资源信息数组
   */
  public async getAllResources(): Promise<ResourceInfo[]> {
    try {
      // 在实际项目中，应该从引擎获取资源列表
      // 这里使用模拟数据
      return Array.from(this.resources.values());
    } catch (error) {
      console.error('获取资源列表失败:', error);
      message.error('获取资源列表失败');
      return [];
    }
  }

  /**
   * 获取资源信息
   * @param resourceId 资源ID
   * @returns 资源信息
   */
  public async getResourceById(resourceId: string): Promise<ResourceInfo | null> {
    try {
      // 在实际项目中，应该从引擎获取资源信息
      // 这里使用模拟数据
      return this.resources.get(resourceId) || null;
    } catch (error) {
      console.error(`获取资源信息失败: ${resourceId}`, error);
      message.error(`获取资源信息失败: ${resourceId}`);
      return null;
    }
  }

  /**
   * 获取资源依赖
   * @param resourceId 资源ID
   * @returns 依赖信息数组
   */
  public async getResourceDependencies(resourceId: string): Promise<DependencyInfo[]> {
    try {
      // 在实际项目中，应该从引擎获取依赖信息
      // 这里使用模拟数据
      return this.dependencies.get(resourceId) || [];
    } catch (error) {
      console.error(`获取资源依赖失败: ${resourceId}`, error);
      message.error(`获取资源依赖失败: ${resourceId}`);
      return [];
    }
  }

  /**
   * 获取资源被依赖
   * @param resourceId 资源ID
   * @returns 被依赖资源ID数组
   */
  public async getResourceDependents(resourceId: string): Promise<string[]> {
    try {
      // 在实际项目中，应该从引擎获取被依赖信息
      // 这里使用模拟数据
      return this.dependents.get(resourceId) || [];
    } catch (error) {
      console.error(`获取资源被依赖失败: ${resourceId}`, error);
      message.error(`获取资源被依赖失败: ${resourceId}`);
      return [];
    }
  }

  /**
   * 获取资源使用信息
   * @param resourceId 资源ID
   * @returns 资源使用信息
   */
  public async getResourceUsage(resourceId: string): Promise<ResourceUsageInfo | null> {
    try {
      // 在实际项目中，应该从引擎获取使用信息
      // 这里使用模拟数据
      return this.usageInfo.get(resourceId) || null;
    } catch (error) {
      console.error(`获取资源使用信息失败: ${resourceId}`, error);
      message.error(`获取资源使用信息失败: ${resourceId}`);
      return null;
    }
  }

  /**
   * 添加依赖关系
   * @param resourceId 资源ID
   * @param dependencyId 依赖资源ID
   * @param type 依赖类型
   * @param priority 依赖优先级
   * @returns 是否成功添加
   */
  public async addDependency(
    resourceId: string,
    dependencyId: string,
    type: DependencyType = DependencyType.STRONG,
    priority: number = 0
  ): Promise<boolean> {
    try {
      // 在实际项目中，应该调用引擎的依赖管理器添加依赖
      // 这里使用模拟数据
      if (!this.resources.has(resourceId) || !this.resources.has(dependencyId)) {
        message.error('资源不存在');
        return false;
      }

      // 获取现有依赖
      let deps = this.dependencies.get(resourceId) || [];

      // 检查是否已存在相同依赖
      const existingDepIndex = deps.findIndex(dep => dep.id === dependencyId);

      if (existingDepIndex >= 0) {
        // 更新现有依赖
        deps[existingDepIndex] = {
          ...deps[existingDepIndex],
          type,
          priority: Math.max(deps[existingDepIndex].priority || 0, priority)
        };
      } else {
        // 添加新依赖
        deps.push({ id: dependencyId, type, priority });
      }

      // 更新依赖映射
      this.dependencies.set(resourceId, deps);

      // 更新被依赖映射
      let dependents = this.dependents.get(dependencyId) || [];
      if (!dependents.includes(resourceId)) {
        dependents.push(resourceId);
        this.dependents.set(dependencyId, dependents);
      }

      message.success('添加依赖关系成功');
      return true;
    } catch (error) {
      console.error(`添加依赖关系失败: ${resourceId} -> ${dependencyId}`, error);
      message.error(`添加依赖关系失败: ${resourceId} -> ${dependencyId}`);
      return false;
    }
  }

  /**
   * 移除依赖关系
   * @param resourceId 资源ID
   * @param dependencyId 依赖资源ID
   * @returns 是否成功移除
   */
  public async removeDependency(resourceId: string, dependencyId: string): Promise<boolean> {
    try {
      // 在实际项目中，应该调用引擎的依赖管理器移除依赖
      // 这里使用模拟数据
      if (!this.resources.has(resourceId) || !this.resources.has(dependencyId)) {
        message.error('资源不存在');
        return false;
      }

      // 获取现有依赖
      let deps = this.dependencies.get(resourceId) || [];

      // 移除依赖
      deps = deps.filter(dep => dep.id !== dependencyId);

      // 更新依赖映射
      this.dependencies.set(resourceId, deps);

      // 更新被依赖映射
      let dependents = this.dependents.get(dependencyId) || [];
      dependents = dependents.filter(id => id !== resourceId);
      this.dependents.set(dependencyId, dependents);

      message.success('移除依赖关系成功');
      return true;
    } catch (error) {
      console.error(`移除依赖关系失败: ${resourceId} -> ${dependencyId}`, error);
      message.error(`移除依赖关系失败: ${resourceId} -> ${dependencyId}`);
      return false;
    }
  }

  /**
   * 分析依赖关系
   * @param resourceId 资源ID
   * @returns 依赖分析结果
   */
  public async analyzeDependencies(resourceId?: string): Promise<DependencyAnalysisResult> {
    try {
      // 在实际项目中，应该调用引擎的依赖管理器分析依赖
      // 这里使用模拟数据
      const resources = await this.getAllResources();
      
      // 如果指定了资源ID，则只分析该资源的依赖
      const targetResources = resourceId ? [resourceId] : resources.map(r => r.id);
      
      // 模拟分析结果
      return {
        circularDependencies: [['material1', 'shader1', 'material2', 'material1']],
        unusedResources: ['texture2'],
        duplicateResources: [['texture1', 'texture2']],
        largeResources: ['model1', 'audio1'],
        resourcesByType: {
          texture: 2,
          model: 1,
          material: 2,
          shader: 1,
          audio: 1,
          scene: 1
        },
        dependencyChains: [
          ['scene1', 'model1', 'material1', 'texture1'],
          ['scene1', 'model1', 'material1', 'shader1'],
          ['scene1', 'model1', 'material2', 'texture2'],
          ['scene1', 'model1', 'material2', 'shader1'],
          ['scene1', 'audio1']
        ],
        loadOrder: ['texture1', 'texture2', 'shader1', 'material1', 'material2', 'model1', 'audio1', 'scene1'],
        memoryUsage: resources.reduce((sum, r) => sum + r.size, 0)
      };
    } catch (error) {
      console.error(`分析依赖关系失败`, error);
      message.error(`分析依赖关系失败`);
      return {
        circularDependencies: [],
        unusedResources: [],
        duplicateResources: [],
        largeResources: [],
        resourcesByType: {},
        dependencyChains: [],
        loadOrder: [],
        memoryUsage: 0
      };
    }
  }

  /**
   * 生成优化建议
   * @param analysisResult 依赖分析结果
   * @returns 优化建议数组
   */
  public async generateOptimizationSuggestions(
    analysisResult: DependencyAnalysisResult
  ): Promise<OptimizationSuggestion[]> {
    try {
      const suggestions: OptimizationSuggestion[] = [];
      
      // 处理循环依赖
      analysisResult.circularDependencies.forEach((cycle, index) => {
        suggestions.push({
          id: `circular-${index}`,
          type: 'circular',
          severity: 'high',
          description: `检测到循环依赖: ${cycle.join(' -> ')}`,
          resources: cycle,
          impact: '循环依赖可能导致加载问题和内存泄漏',
          solution: '重构资源关系，打破循环依赖，或使用弱依赖替代强依赖'
        });
      });
      
      // 处理未使用资源
      if (analysisResult.unusedResources.length > 0) {
        suggestions.push({
          id: 'unused',
          type: 'unused',
          severity: 'medium',
          description: `检测到 ${analysisResult.unusedResources.length} 个未使用资源`,
          resources: analysisResult.unusedResources,
          impact: '未使用资源占用存储空间，可能导致不必要的加载',
          solution: '考虑移除未使用资源，或将其标记为可选资源'
        });
      }
      
      // 处理重复资源
      analysisResult.duplicateResources.forEach((group, index) => {
        suggestions.push({
          id: `duplicate-${index}`,
          type: 'duplicate',
          severity: 'medium',
          description: `检测到重复资源: ${group.join(', ')}`,
          resources: group,
          impact: '重复资源导致存储和内存浪费',
          solution: '合并重复资源，使用单一实例'
        });
      });
      
      // 处理大型资源
      if (analysisResult.largeResources.length > 0) {
        suggestions.push({
          id: 'large',
          type: 'large',
          severity: 'low',
          description: `检测到 ${analysisResult.largeResources.length} 个大型资源`,
          resources: analysisResult.largeResources,
          impact: '大型资源可能导致加载时间延长和内存压力',
          solution: '考虑压缩资源，或使用延迟加载策略'
        });
      }
      
      return suggestions;
    } catch (error) {
      console.error(`生成优化建议失败`, error);
      message.error(`生成优化建议失败`);
      return [];
    }
  }

  /**
   * 应用优化操作
   * @param operation 优化操作
   * @returns 是否成功应用
   */
  public async applyOptimization(operation: OptimizationOperation): Promise<boolean> {
    try {
      // 在实际项目中，应该调用引擎的依赖管理器应用优化
      // 这里使用模拟实现
      return await operation.apply();
    } catch (error) {
      console.error(`应用优化操作失败: ${operation.id}`, error);
      message.error(`应用优化操作失败: ${operation.id}`);
      return false;
    }
  }
}
