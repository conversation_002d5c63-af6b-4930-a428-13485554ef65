/**
 * 口型同步系统
 * 用于根据音频实时生成口型动画
 */
import * as THREE from 'three';
import type { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { System } from '../core/System';
import type { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';
import { VisemeType } from './FacialAnimation';
import { LipSyncAdvancedAnalyzer } from './analysis/LipSyncAdvancedAnalyzer';
import { LipSyncAIPredictor } from './ai/LipSyncAIPredictor';

/**
 * 口型同步配置
 */
export interface LipSyncConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** FFT大小 */
  fftSize?: number;
  /** 采样频率 */
  sampleRate?: number;
  /** 音量阈值 */
  volumeThreshold?: number;
  /** 平滑因子 */
  smoothingFactor?: number;
  /** 是否使用AI预测 */
  useAIPrediction?: boolean;
  /** 分析间隔（毫秒），控制分析频率，降低CPU使用率 */
  analysisInterval?: number;
  /** 是否使用工作线程进行音频分析 */
  useWorker?: boolean;
  /** 是否使用GPU加速 */
  useGPU?: boolean;
  /** 频率带数量 */
  numFrequencyBands?: number;
  /** 是否使用高级分析器 */
  useAdvancedAnalyzer?: boolean;
  /** 是否使用MFCC分析 */
  useMFCC?: boolean;
  /** 是否使用LPC分析 */
  useLPC?: boolean;
  /** 是否使用上下文预测 */
  useContextPrediction?: boolean;
  /** 上下文窗口大小 */
  contextWindowSize?: number;
  /** 梅尔滤波器数量 */
  numMelFilters?: number;
  /** 倒谱系数数量 */
  numCepstralCoeffs?: number;
  /** 是否使用频谱图分析 */
  useSpectrogram?: boolean;
  /** 是否使用音素识别 */
  usePhonemeRecognition?: boolean;
  /** 是否使用语音模式识别 */
  useSpeechPatternRecognition?: boolean;
  /** 是否使用在线学习 */
  useOnlineLearning?: boolean;
  /** AI模型路径 */
  aiModelPath?: string;
}

/**
 * 口型同步组件
 */
export class LipSyncComponent extends Component {
  /** 组件类型 */
  static readonly type = 'LipSync';

  /** 当前口型 */
  private currentViseme: VisemeType = VisemeType.SILENT;
  /** 口型权重 */
  private visemeWeight: number = 0;
  /** 口型混合速度 */
  private visemeBlendSpeed: number = 10.0;
  /** 口型混合映射 */
  private visemeBlendMap: Map<string, number> = new Map();
  /** 是否启用 */
  protected enabled: boolean = true;
  /** 是否使用平滑 */
  private useSmoothing: boolean = true;
  /** 平滑因子 */
  private smoothingFactor: number = 0.5;
  /** 音频源 */
  private audioSource: HTMLAudioElement | null = null;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   */
  constructor() {
    super('LipSync');

    // 初始化口型混合映射
    for (const viseme in VisemeType) {
      this.visemeBlendMap.set(VisemeType[viseme], 0);
    }
  }

  /**
   * 设置口型
   * @param viseme 口型类型
   * @param weight 权重
   * @param blendTime 混合时间（秒）
   */
  public setViseme(viseme: VisemeType, weight: number = 1.0, blendTime?: number): void {
    if (!this.enabled) return;

    this.currentViseme = viseme;
    this.visemeWeight = weight;

    if (blendTime !== undefined) {
      this.visemeBlendSpeed = 1.0 / Math.max(0.001, blendTime);
    }

    // 更新混合映射
    for (const [key, _] of this.visemeBlendMap.entries()) {
      if (key === viseme) {
        this.visemeBlendMap.set(key, weight);
      } else {
        this.visemeBlendMap.set(key, 0);
      }
    }

    this.eventEmitter.emit('visemeChange', { viseme, weight });
  }

  /**
   * 获取当前口型
   * @returns 当前口型和权重
   */
  public getCurrentViseme(): { viseme: VisemeType, weight: number } {
    return {
      viseme: this.currentViseme,
      weight: this.visemeWeight
    };
  }

  /**
   * 获取口型混合映射
   * @returns 口型混合映射
   */
  public getVisemeBlendMap(): Map<string, number> {
    return new Map(this.visemeBlendMap);
  }

  /**
   * 设置音频源
   * @param audio 音频元素
   */
  public setAudioSource(audio: HTMLAudioElement): void {
    this.audioSource = audio;
  }

  /**
   * 获取音频源
   * @returns 音频元素
   */
  public getAudioSource(): HTMLAudioElement | null {
    return this.audioSource;
  }

  /**
   * 重置口型
   */
  public resetViseme(): void {
    this.setViseme(VisemeType.SILENT, 0);
  }

  /**
   * 启用组件
   */
  public enable(): void {
    this.enabled = true;
  }

  /**
   * 禁用组件
   */
  public disable(): void {
    this.enabled = false;
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled) return;

    // 口型动画逻辑在LipSyncSystem中处理
  }
}

/**
 * 口型同步系统
 */
export class LipSyncSystem extends System {
  /** 系统类型 */
  static readonly type = 'LipSync';

  /** 口型同步组件 */
  private components: Map<Entity, LipSyncComponent> = new Map();

  /** 配置 */
  private config: LipSyncConfig;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: LipSyncConfig = {
    debug: false,
    fftSize: 1024,
    sampleRate: 44100,
    volumeThreshold: 0.01,
    smoothingFactor: 0.5,
    useAIPrediction: false,
    analysisInterval: 100, // 每100毫秒分析一次，降低CPU使用率
    useWorker: true,       // 默认使用工作线程
    useGPU: false,         // 默认不使用GPU加速
    numFrequencyBands: 15, // 默认使用15个频率带，提高精度
    useAdvancedAnalyzer: false, // 默认不使用高级分析器
    useMFCC: false,        // 默认不使用MFCC分析
    useLPC: false,         // 默认不使用LPC分析
    useContextPrediction: true, // 默认使用上下文预测
    contextWindowSize: 5,  // 默认上下文窗口大小
    numMelFilters: 26,     // 默认梅尔滤波器数量
    numCepstralCoeffs: 13, // 默认倒谱系数数量
    useSpectrogram: false, // 默认不使用频谱图分析
    usePhonemeRecognition: false, // 默认不使用音素识别
    useSpeechPatternRecognition: false, // 默认不使用语音模式识别
    useOnlineLearning: false, // 默认不使用在线学习
    aiModelPath: './models/lipsync-model.json' // 默认AI模型路径
  };

  /** 音频上下文 */
  private audioContext: AudioContext | null = null;
  /** 音频分析器 */
  private audioAnalyser: AnalyserNode | null = null;
  /** 频谱数据 */
  private spectrum: Float32Array | null = null;
  /** 是否正在跟踪口型 */
  private lipsyncTracking: boolean = false;
  /** 音频处理器 */
  private audioProcessor: ScriptProcessorNode | null = null;
  /** 工作线程 */
  private worker: Worker | null = null;
  /** 上次分析时间 */
  private lastAnalysisTime: number = 0;
  /** 频率带能量 */
  private frequencyBands: Float32Array | null = null;
  /** 频率带边界 */
  private frequencyBandBoundaries: number[] = [];
  /** 分析定时器ID */
  private analysisTimerId: number | null = null;
  /** 是否支持GPU加速 */
  private supportsGPU: boolean = false;
  /** GPU计算着色器 */
  private gpuComputeShader: any = null;
  /** 高级分析器 */
  private advancedAnalyzer: LipSyncAdvancedAnalyzer | null = null;
  /** AI预测器 */
  private aiPredictor: LipSyncAIPredictor | null = null;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config?: Partial<LipSyncConfig>) {
    super(100); // 设置优先级
    this.config = { ...LipSyncSystem.DEFAULT_CONFIG, ...config };

    // 初始化频率带
    this.initFrequencyBands();

    // 检查GPU支持
    if (this.config.useGPU) {
      this.checkGPUSupport();
    }

    // 初始化高级分析器
    if (this.config.useAdvancedAnalyzer) {
      this.initAdvancedAnalyzer();
    }

    // 初始化AI预测器
    if (this.config.useAIPrediction) {
      this.initAIPredictor();
    }
  }

  /**
   * 初始化高级分析器
   */
  private async initAdvancedAnalyzer(): Promise<void> {
    try {
      this.advancedAnalyzer = new LipSyncAdvancedAnalyzer({
        debug: this.config.debug,
        fftSize: this.config.fftSize,
        sampleRate: this.config.sampleRate,
        volumeThreshold: this.config.volumeThreshold,
        smoothingFactor: this.config.smoothingFactor,
        useMFCC: this.config.useMFCC,
        useLPC: this.config.useLPC,
        useContextPrediction: this.config.useContextPrediction,
        contextWindowSize: this.config.contextWindowSize,
        numFrequencyBands: this.config.numFrequencyBands,
        numMelFilters: this.config.numMelFilters,
        numCepstralCoeffs: this.config.numCepstralCoeffs,
        useAIPrediction: this.config.useAIPrediction,
        useSpectrogram: this.config.useSpectrogram,
        usePhonemeRecognition: this.config.usePhonemeRecognition,
        useSpeechPatternRecognition: this.config.useSpeechPatternRecognition,
        useOnlineLearning: this.config.useOnlineLearning
      });

      if (this.config.debug) {
        console.log('高级口型同步分析器已初始化');
      }
    } catch (error) {
      console.error('初始化高级分析器失败:', error);
      this.advancedAnalyzer = null;
    }
  }

  /**
   * 初始化AI预测器
   */
  private async initAIPredictor(): Promise<void> {
    try {
      this.aiPredictor = new LipSyncAIPredictor({
        debug: this.config.debug,
        modelPath: this.config.aiModelPath,
        useLocalModel: true,
        useOnlineLearning: this.config.useOnlineLearning,
        batchSize: 4,
        contextWindowSize: this.config.contextWindowSize,
        useMFCC: this.config.useMFCC,
        useSpectrogram: this.config.useSpectrogram,
        useContext: this.config.useContextPrediction
      });

      // 初始化AI预测器
      const success = await this.aiPredictor.initialize();

      if (success) {
        if (this.config.debug) {
          console.log('AI口型预测器已初始化');
        }
      } else {
        console.warn('AI预测器初始化失败，将使用传统分析方法');
        this.aiPredictor = null;
      }
    } catch (error) {
      console.error('初始化AI预测器失败:', error);
      this.aiPredictor = null;
    }
  }

  /**
   * 初始化频率带
   */
  private initFrequencyBands(): void {
    const numBands = this.config.numFrequencyBands!;
    this.frequencyBands = new Float32Array(numBands);

    // 计算频率带边界
    // 使用对数分布，低频区域更精细
    const minFreq = 80;    // 人声最低频率约80Hz
    const maxFreq = 8000;  // 人声最高频率约8kHz

    this.frequencyBandBoundaries = [];
    for (let i = 0; i <= numBands; i++) {
      // 对数分布
      const t = i / numBands;
      const freq = minFreq * Math.pow(maxFreq / minFreq, t);
      this.frequencyBandBoundaries.push(freq);
    }

    if (this.config.debug) {
      console.log('频率带边界:', this.frequencyBandBoundaries);
    }
  }

  /**
   * 检查GPU支持
   */
  private checkGPUSupport(): void {
    // 检查WebGL2和相关扩展
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2');

    if (gl) {
      // WebGL2支持
      this.supportsGPU = true;

      if (this.config.debug) {
        console.log('GPU音频分析支持: WebGL2');
      }
    } else {
      this.supportsGPU = false;

      if (this.config.debug) {
        console.warn('GPU音频分析不支持: WebGL2不可用');
      }
    }
  }

  /**
   * 创建口型同步组件
   * @param entity 实体
   * @returns 口型同步组件
   */
  public createLipSync(entity: Entity): LipSyncComponent {
    // 检查是否已存在组件
    if (this.components.has(entity)) {
      return this.components.get(entity)!;
    }

    // 创建新组件
    const component = new LipSyncComponent();
    entity.addComponent(component);
    this.components.set(entity, component);

    if (this.config.debug) {
      console.log(`创建口型同步组件: ${entity.id}`);
    }

    return component;
  }

  /**
   * 移除口型同步组件
   * @param entity 实体
   */
  public removeLipSync(entity: Entity): void {
    if (this.components.has(entity)) {
      this.components.delete(entity);

      if (this.config.debug) {
        console.log(`移除口型同步组件: ${entity.id}`);
      }
    }
  }

  /**
   * 获取口型同步组件
   * @param entity 实体
   * @returns 口型同步组件，如果不存在则返回null
   */
  public getLipSync(entity: Entity): LipSyncComponent | null {
    return this.components.get(entity) || null;
  }

  /**
   * 启动口型同步跟踪
   * @param audioElement 音频元素
   */
  public startTracking(audioElement?: HTMLAudioElement): void {
    if (this.lipsyncTracking) return;

    this.lipsyncTracking = true;
    this.lastAnalysisTime = performance.now();

    // 创建音频上下文
    this.audioContext = new AudioContext();
    const fftSize = this.config.fftSize!;

    // 创建音频分析器
    const analyzer = this.audioContext.createAnalyser();
    analyzer.fftSize = fftSize;
    analyzer.smoothingTimeConstant = 0.5; // 平滑处理，减少抖动
    this.audioAnalyser = analyzer;

    // 如果提供了音频元素，则连接它
    if (audioElement) {
      const source = this.audioContext.createMediaElementSource(audioElement);
      source.connect(analyzer);
      source.connect(this.audioContext.destination);

      // 保存音频源到组件
      for (const component of this.components.values()) {
        component.setAudioSource(audioElement);
      }
    }

    // 初始化频率带数据
    if (!this.frequencyBands) {
      this.initFrequencyBands();
    }

    // 使用工作线程
    if (this.config.useWorker) {
      try {
        // 创建工作线程
        this.worker = new Worker(new URL('./workers/LipSyncWorker.js', import.meta.url));

        // 设置工作线程消息处理
        this.worker.onmessage = this.onWorkerMessage.bind(this);

        // 初始化工作线程
        this.worker.postMessage({
          command: 'init',
          config: {
            fftSize: this.config.fftSize,
            sampleRate: this.config.sampleRate,
            volumeThreshold: this.config.volumeThreshold,
            frequencyBandBoundaries: this.frequencyBandBoundaries,
            useMFCC: this.config.useMFCC,
            useLPC: this.config.useLPC,
            useSpectrogram: this.config.useSpectrogram,
            useContextPrediction: this.config.useContextPrediction,
            contextWindowSize: this.config.contextWindowSize,
            numMelFilters: this.config.numMelFilters,
            numCepstralCoeffs: this.config.numCepstralCoeffs,
            usePhonemeRecognition: this.config.usePhonemeRecognition
          }
        });

        if (this.config.debug) {
          console.log('口型同步工作线程已创建');
        }
      } catch (error) {
        console.warn('无法创建工作线程:', error);
        this.config.useWorker = false;
      }
    }

    // 使用GPU加速
    if (this.config.useGPU && this.supportsGPU) {
      // 初始化GPU计算
      // TODO: 实现GPU计算初始化
    }

    // 创建音频处理器
    this.audioProcessor = this.audioContext.createScriptProcessor(fftSize, 1, 1);
    analyzer.connect(this.audioProcessor);
    this.audioProcessor.connect(this.audioContext.destination);

    // 处理音频数据
    this.audioProcessor.onaudioprocess = this.processAudio.bind(this);

    // 使用分析间隔定时器代替每帧处理
    if (this.config.analysisInterval! > 0) {
      this.analysisTimerId = window.setInterval(() => {
        if (this.lipsyncTracking && this.audioAnalyser) {
          if (this.config.useWorker && this.worker) {
            // 工作线程处理
            this.processAudio();
          } else if (this.config.useGPU && this.supportsGPU) {
            // GPU处理
            this.processAudioWithGPU();
          } else {
            // CPU处理
            this.processAudioWithCPU();
          }
        }
      }, this.config.analysisInterval!);
    }

    if (this.config.debug) {
      console.log('口型同步跟踪已启动');
      console.log('配置:', {
        fftSize: this.config.fftSize,
        sampleRate: this.config.sampleRate,
        useWorker: this.config.useWorker,
        useGPU: this.config.useGPU && this.supportsGPU,
        analysisInterval: this.config.analysisInterval
      });
    }
  }

  /**
   * 停止口型同步跟踪
   */
  public stopTracking(): void {
    if (!this.lipsyncTracking) return;

    this.lipsyncTracking = false;

    // 清除分析定时器
    if (this.analysisTimerId !== null) {
      window.clearInterval(this.analysisTimerId);
      this.analysisTimerId = null;
    }

    // 终止工作线程
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }

    // 释放GPU资源
    if (this.gpuComputeShader) {
      // TODO: 释放GPU资源
      this.gpuComputeShader = null;
    }

    // 断开音频处理器
    if (this.audioProcessor) {
      this.audioProcessor.disconnect();
      this.audioProcessor = null;
    }

    // 关闭音频上下文
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.audioAnalyser = null;
    this.spectrum = null;

    // 清除频率带数据
    this.frequencyBands = null;

    // 释放高级分析器资源
    this.advancedAnalyzer = null;

    // 释放AI预测器资源
    this.aiPredictor = null;

    // 重置所有实体的口型
    for (const component of this.components.values()) {
      component.resetViseme();
      component.setAudioSource(null);
    }

    if (this.config.debug) {
      console.log('口型同步跟踪已停止');
    }
  }

  /**
   * 处理音频数据
   */
  private processAudio(): void {
    if (!this.lipsyncTracking || !this.audioAnalyser) return;

    const now = performance.now();
    const elapsed = now - this.lastAnalysisTime;

    // 控制分析频率，降低CPU使用率
    if (elapsed < this.config.analysisInterval!) {
      return;
    }

    this.lastAnalysisTime = now;

    // 使用工作线程进行音频分析
    if (this.config.useWorker && this.worker) {
      // 获取频率数据
      this.spectrum = new Float32Array(this.audioAnalyser.frequencyBinCount);
      this.audioAnalyser.getFloatFrequencyData(this.spectrum);

      // 发送数据到工作线程
      this.worker.postMessage({
        command: 'analyze',
        spectrum: this.spectrum.buffer,
        volumeThreshold: this.config.volumeThreshold
      }, [this.spectrum.buffer]);

      // 工作线程会返回结果，在onWorkerMessage中处理
    }
    // 使用GPU加速
    else if (this.config.useGPU && this.supportsGPU && this.gpuComputeShader) {
      this.processAudioWithGPU();
    }
    // 使用CPU处理
    else {
      this.processAudioWithCPU();
    }
  }

  /**
   * 使用CPU处理音频数据
   */
  private processAudioWithCPU(): void {
    if (!this.audioAnalyser) return;

    // 获取频率数据
    this.spectrum = new Float32Array(this.audioAnalyser.frequencyBinCount);
    this.audioAnalyser.getFloatFrequencyData(this.spectrum);

    // 使用AI预测
    if (this.config.useAIPrediction && this.aiPredictor) {
      try {
        const prediction = this.aiPredictor.predict(this.spectrum);

        // 如果置信度足够高，使用AI预测结果
        if (prediction.confidence > 0.7) {
          this.setVisemeForAllEntities(prediction.viseme, prediction.confidence);
          return;
        }
      } catch (error) {
        if (this.config.debug) {
          console.warn('AI预测失败，回退到传统方法:', error);
        }
      }
    }

    // 使用高级分析器
    if (this.config.useAdvancedAnalyzer && this.advancedAnalyzer) {
      try {
        const viseme = this.advancedAnalyzer.analyzeAudio(this.spectrum);
        this.setVisemeForAllEntities(viseme);
        return;
      } catch (error) {
        if (this.config.debug) {
          console.warn('高级分析器失败，回退到传统方法:', error);
        }
      }
    }

    // 计算RMS
    const rms = this.calculateRMS(this.spectrum);

    // 如果音量太小，则设置为静默
    if (rms < this.config.volumeThreshold!) {
      this.setVisemeForAllEntities(VisemeType.SILENT);
      return;
    }

    // 计算频率带能量
    this.calculateFrequencyBands();

    // 分析频谱并确定口型
    const viseme = this.analyzeFrequencyBands();
    this.setVisemeForAllEntities(viseme);
  }

  /**
   * 使用GPU处理音频数据
   */
  private processAudioWithGPU(): void {
    if (!this.audioAnalyser || !this.gpuComputeShader) return;

    // 获取频率数据
    this.spectrum = new Float32Array(this.audioAnalyser.frequencyBinCount);
    this.audioAnalyser.getFloatFrequencyData(this.spectrum);

    // 使用AI预测
    if (this.config.useAIPrediction && this.aiPredictor) {
      try {
        const prediction = this.aiPredictor.predict(this.spectrum);

        // 如果置信度足够高，使用AI预测结果
        if (prediction.confidence > 0.7) {
          this.setVisemeForAllEntities(prediction.viseme, prediction.confidence);
          return;
        }
      } catch (error) {
        if (this.config.debug) {
          console.warn('AI预测失败，回退到GPU方法:', error);
        }
      }
    }

    // 使用高级分析器
    if (this.config.useAdvancedAnalyzer && this.advancedAnalyzer) {
      try {
        const viseme = this.advancedAnalyzer.analyzeAudio(this.spectrum);
        this.setVisemeForAllEntities(viseme);
        return;
      } catch (error) {
        if (this.config.debug) {
          console.warn('高级分析器失败，回退到GPU方法:', error);
        }
      }
    }

    // 计算RMS
    const rms = this.calculateRMS(this.spectrum);

    // 如果音量太小，则设置为静默
    if (rms < this.config.volumeThreshold!) {
      this.setVisemeForAllEntities(VisemeType.SILENT);
      return;
    }

    // 使用GPU计算频率带能量
    // 这里是GPU计算的占位代码，实际实现需要根据WebGL环境
    // TODO: 实现GPU计算

    // 分析频谱并确定口型
    const viseme = this.analyzeFrequencyBands();
    this.setVisemeForAllEntities(viseme);
  }

  /**
   * 工作线程消息处理
   * @param event 消息事件
   */
  private onWorkerMessage(event: MessageEvent): void {
    const data = event.data;

    if (data.command === 'analyzeResult') {
      const viseme = data.viseme;
      this.setVisemeForAllEntities(viseme as VisemeType);
    }
  }

  /**
   * 计算频率带能量
   */
  private calculateFrequencyBands(): void {
    if (!this.spectrum || !this.frequencyBands) return;

    const sampleRate = this.config.sampleRate!;
    const binCount = this.spectrum.length;
    const binWidth = sampleRate / (binCount * 2);

    // 重置频率带
    for (let i = 0; i < this.frequencyBands.length; i++) {
      this.frequencyBands[i] = 0;
    }

    // 计算每个频率带的能量
    for (let i = 0; i < binCount; i++) {
      const frequency = i * binWidth;

      // 找到对应的频率带
      for (let j = 0; j < this.frequencyBandBoundaries.length - 1; j++) {
        if (frequency >= this.frequencyBandBoundaries[j] && frequency < this.frequencyBandBoundaries[j + 1]) {
          // 转换dB到线性刻度并累加
          this.frequencyBands[j] += Math.pow(10, this.spectrum[i] / 20);
          break;
        }
      }
    }

    // 归一化
    for (let i = 0; i < this.frequencyBands.length; i++) {
      // 计算该频率带包含的bin数量
      const startBin = Math.floor(this.frequencyBandBoundaries[i] / binWidth);
      const endBin = Math.floor(this.frequencyBandBoundaries[i + 1] / binWidth);
      const binCount = endBin - startBin;

      if (binCount > 0) {
        this.frequencyBands[i] /= binCount;
      }
    }
  }

  /**
   * 计算RMS（均方根）
   * @param spectrum 频谱数据
   * @returns RMS值
   */
  private calculateRMS(spectrum: Float32Array): number {
    let sum = 0;
    for (let i = 0; i < spectrum.length; i++) {
      sum += spectrum[i] * spectrum[i];
    }
    return Math.sqrt(sum / spectrum.length);
  }

  /**
   * 分析频率带并确定口型
   * @returns 口型类型
   */
  private analyzeFrequencyBands(): VisemeType {
    if (!this.frequencyBands || this.frequencyBands.length === 0) {
      return VisemeType.SILENT;
    }

    // 找出能量最大的频率带
    let maxEnergy = 0;
    let maxBandIndex = -1;

    for (let i = 0; i < this.frequencyBands.length; i++) {
      if (this.frequencyBands[i] > maxEnergy) {
        maxEnergy = this.frequencyBands[i];
        maxBandIndex = i;
      }
    }

    // 根据主导频率带确定口型
    if (maxBandIndex === -1) {
      return VisemeType.SILENT;
    }

    // 低频带 (80-250Hz)
    if (maxBandIndex === 0) {
      return VisemeType.PP; // 闭合口型，如 p, b, m
    }
    // 中低频带 (250-500Hz)
    else if (maxBandIndex === 1) {
      return VisemeType.FF; // 半开口型，如 f, v
    }
    // 中频带 (500-1000Hz)
    else if (maxBandIndex === 2) {
      return VisemeType.TH; // 舌齿音，如 th
    }
    // 中高频带 (1000-2000Hz)
    else if (maxBandIndex === 3) {
      return VisemeType.DD; // 舌尖音，如 d, t, n
    }
    // 高频带 (2000-4000Hz)
    else if (maxBandIndex === 4) {
      return VisemeType.SS; // 摩擦音，如 s, z
    }
    // 更高频带 (4000-8000Hz)
    else {
      return VisemeType.AA; // 开口元音，如 a
    }
  }

  /**
   * 分析频谱并确定口型（旧方法，保留用于兼容）
   * @param spectrum 频谱数据
   * @returns 口型类型
   */
  private analyzeSpectrum(spectrum: Float32Array): VisemeType {
    // 简单的频谱分析，可以根据需要扩展
    const lowFreq = this.getAverageEnergy(spectrum, 0, 500);
    const midFreq = this.getAverageEnergy(spectrum, 500, 2000);
    const highFreq = this.getAverageEnergy(spectrum, 2000, 8000);

    // 根据频率能量确定口型
    if (lowFreq > midFreq && lowFreq > highFreq) {
      return VisemeType.PP; // 低频主导，闭合口型
    } else if (midFreq > lowFreq && midFreq > highFreq) {
      return VisemeType.FF; // 中频主导，半开口型
    } else if (highFreq > lowFreq && highFreq > midFreq) {
      return VisemeType.AA; // 高频主导，开口型
    } else {
      return VisemeType.SILENT; // 默认静默
    }
  }

  /**
   * 获取频段平均能量
   * @param spectrum 频谱数据
   * @param minFreq 最小频率
   * @param maxFreq 最大频率
   * @returns 平均能量
   */
  private getAverageEnergy(spectrum: Float32Array, minFreq: number, maxFreq: number): number {
    const minIndex = Math.floor(minFreq / (this.config.sampleRate! / 2) * spectrum.length);
    const maxIndex = Math.floor(maxFreq / (this.config.sampleRate! / 2) * spectrum.length);

    let sum = 0;
    for (let i = minIndex; i < maxIndex && i < spectrum.length; i++) {
      sum += Math.pow(10, spectrum[i] / 20); // 转换dB到线性刻度
    }

    return sum / (maxIndex - minIndex);
  }

  /**
   * 为所有实体设置口型
   * @param viseme 口型类型
   * @param weight 权重
   */
  private setVisemeForAllEntities(viseme: VisemeType, weight: number = 1.0): void {
    for (const component of this.components.values()) {
      component.setViseme(viseme, weight);
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新所有口型同步组件
    for (const component of this.components.values()) {
      component.update(deltaTime);
    }
  }
}
