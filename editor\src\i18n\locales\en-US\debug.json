{"debug": {"title": "Debug Tools", "description": "Advanced debugging and performance analysis tools", "debugView": "Debug View", "tabs": {"performance": "Performance", "performanceComparison": "Performance Comparison", "performanceTest": "Performance Test", "memory": "Memory", "rendering": "Rendering", "optimization": "Optimization", "performanceOptimization": "Performance Optimization", "resourceOptimization": "Resource Optimization", "uiOptimization": "UI Optimization"}, "performance": {"title": "Performance Analysis", "description": "Analyze and monitor engine performance", "startMonitoring": "Start Monitoring", "stopMonitoring": "Stop Monitoring", "reset": "Reset", "sampleInterval": "Sample Interval", "historyLength": "History Length", "noData": "No performance data available", "fps": "FPS", "memory": "Memory Usage", "renderTime": "Render Time", "fpsChart": "FPS Chart", "memoryChart": "Memory Usage Chart", "renderTimeChart": "Render Time Chart", "overview": "Overview", "details": "Details", "statistics": "Statistics", "warnings": "Warnings", "cpuUsage": "CPU Usage", "gpuUsage": "GPU Usage", "drawCalls": "Draw Calls", "triangles": "Triangles", "vertices": "Vertices", "textures": "Textures", "entities": "Entities", "components": "Components", "systems": "Systems", "analyzing": "Analyzing..."}, "memory": {"title": "Memory Analysis", "description": "Analyze memory usage", "refresh": "Refresh", "overview": "Overview", "total": "Total Memory", "textures": "Texture Memory", "geometries": "Geometry Memory", "js": "JavaScript Memory", "distribution": "Memory Distribution", "usage": "Memory Usage", "details": "Details", "name": "Name", "size": "Size", "count": "Count", "type": "Type", "warning": "High Memory Usage", "warningDescription": "Memory usage exceeds threshold, may cause performance issues"}, "rendering": {"title": "Rendering Analysis", "description": "Analyze rendering performance and statistics", "refresh": "Refresh", "overview": "Overview", "drawCalls": "Draw Calls", "triangles": "Triangles", "vertices": "Vertices", "renderTime": "Render Time", "performance": "Performance", "details": "Details", "bottlenecks": "Bottlenecks", "name": "Name", "visible": "Visible", "yes": "Yes", "no": "No", "highDrawCalls": "High Draw Call Count ({count})", "highDrawCallsTip": "Consider using batching or merging meshes to reduce draw calls", "highRenderTime": "High Render Time ({time}ms)", "highRenderTimeTip": "Consider optimizing shaders or reducing post-processing effects", "highTriangles": "High Triangle Count ({count}M)", "highTrianglesTip": "Consider using LOD or simplifying models to reduce triangle count", "noBottlenecks": "No significant rendering bottlenecks detected"}, "optimization": {"title": "Scene Optimization", "description": "Analyze scene and provide optimization suggestions", "analyze": "Analyze Scene", "analyzing": "Analyzing...", "overallScore": "Overall Score", "scoreGood": "Scene performance is good", "scoreWarning": "Scene performance is average, room for improvement", "scoreError": "Scene performance is poor, optimization needed", "recommendations": "Optimization Recommendations", "tips": "Optimization Tips", "low": "Low", "medium": "Medium", "high": "High", "drawCalls": "Draw Calls", "drawCallsDesc": "Draw calls are instructions to the GPU to render the scene, too many can cause CPU bottlenecks", "drawCallsTip1": "Use material instancing to reduce material switches", "drawCallsTip2": "Merge static meshes to reduce draw calls", "drawCallsTip3": "Use GPU instancing to render repeated objects", "triangles": "Triangle Count", "trianglesDesc": "Triangle count directly affects GPU rendering performance, too many can cause GPU bottlenecks", "trianglesTip1": "Use LOD (Level of Detail) to reduce triangle count for distant objects", "trianglesTip2": "Simplify complex models, remove invisible triangles", "trianglesTip3": "Use normal maps instead of high-poly details", "textures": "Texture Optimization", "texturesDesc": "Texture size and count affect memory usage and loading times", "texturesTip1": "Compress textures to reduce memory usage", "texturesTip2": "Use texture atlases to combine multiple small textures", "texturesTip3": "Use smaller textures for distant objects", "lighting": "Lighting Optimization", "lightingDesc": "Complex lighting calculations can significantly impact performance", "lightingTip1": "Reduce the number of dynamic lights", "lightingTip2": "Use baked lighting instead of real-time lighting", "lightingTip3": "Optimize shadow settings, reduce shadow resolution or distance", "memory": "Memory Optimization", "memoryDesc": "High memory usage can lead to performance degradation or crashes", "memoryTip1": "Release unused resources", "memoryTip2": "Use object pooling to reuse objects", "memoryTip3": "Lazy load resources that aren't immediately needed"}, "performanceOptimization": {"title": "Performance Optimization", "startMonitoring": "Start Monitoring", "stopMonitoring": "Stop Monitoring", "optimize": "Optimize", "autoOptimize": "Auto Optimize", "optimizationStarted": "Optimization Started", "optimizationCompleted": "Optimization Completed", "optimizationFailed": "Optimization Failed", "overview": "Overview", "charts": "Charts", "optimizationResults": "Optimization Results", "settings": "Settings", "fps": "FPS", "memory": "Memory Usage", "renderTime": "Render Time", "fpsChart": "FPS Chart", "memoryChart": "Memory Usage Chart", "renderTimeChart": "Render Time Chart", "noWarnings": "No Performance Warnings", "current": "Current", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "generalSettings": "General Settings", "warningThresholds": "Warning Thresholds", "optimizationSettings": "Optimization Settings", "monitoringInterval": "Monitoring Interval", "historyLength": "History Length", "enableWarnings": "Enable Warnings", "autoOptimizeInterval": "Auto Optimize Interval", "seconds": "seconds", "samples": "samples", "fpsWarningThreshold": "FPS Warning Threshold", "memoryWarningThreshold": "Memory Warning Threshold", "renderTimeWarningThreshold": "Render Time Warning Threshold", "enableRenderingOptimization": "Enable Rendering Optimization", "enableMemoryOptimization": "Enable Memory Optimization", "enableUIOptimization": "Enable UI Optimization", "enableResourceOptimization": "Enable Resource Optimization", "lodGenerated": "LOD Generated", "materialOptimized": "Materials Optimized", "batchesCreated": "Batches Created", "instancesCreated": "Instances Created", "cullingEnabled": "Culling Enabled", "texturesOptimized": "Textures Optimized", "geometriesOptimized": "Geometries Optimized", "resourcesReleased": "Resources Released", "componentsOptimized": "Components Optimized", "eventListenersOptimized": "Event Listeners Optimized", "resourcesOptimized": "Resources Optimized", "loadingStrategyUpdated": "Loading Strategy Updated"}, "resource": {"title": "Resource Optimization", "refresh": "Refresh", "unloadSelected": "Unload Selected", "cleanupCache": "Cleanup <PERSON>", "optimizeResources": "Optimize Resources", "searchPlaceholder": "Search resources...", "noResourceSelected": "No resource selected", "unloadedResources": "Unloaded Resources", "unloadFailed": "Unload Failed", "cacheCleanupSuccess": "<PERSON><PERSON> Cleanup Success", "cacheCleanupFailed": "<PERSON><PERSON>up Failed", "settingsApplied": "Settings Applied", "settingsApplyFailed": "Settings Apply Failed", "optimizationSuccess": "Resource Optimization Success", "optimizationFailed": "Resource Optimization Failed", "name": "Name", "type": "Type", "size": "Size", "state": "State", "priority": "Priority", "lastAccessed": "Last Accessed", "refCount": "Ref Count", "texture": "Texture", "model": "Model", "audio": "Audio", "material": "Material", "shader": "Shader", "other": "Other", "loaded": "Loaded", "loading": "Loading", "unloaded": "Unloaded", "error": "Error", "unknown": "Unknown", "highPriority": "High Priority", "mediumPriority": "Medium Priority", "lowPriority": "Low Priority", "unknownPriority": "Unknown Priority", "memoryUsage": "Memory Usage", "totalMemory": "Total Memory", "resourceList": "Resource List", "settings": "Settings", "optimizationSettings": "Optimization Settings", "cacheSettings": "<PERSON><PERSON>", "loadingSettings": "Loading Settings", "maxCacheSize": "<PERSON>", "cleanupThreshold": "Cleanup Threshold", "cleanupInterval": "Cleanup Interval", "maxConcurrentLoads": "Max Concurrent Loads", "preloadDistance": "Preload Distance", "unloadDistance": "Unload Distance", "priorityBasedLoading": "Priority Based Loading", "compressTextures": "Compress Textures", "useTextureAtlas": "Use Texture Atlas", "useGeometryInstancing": "Use Geometry Instancing", "useMaterialInstancing": "Use Material Instancing", "applySettings": "Apply Settings", "resources": "Resources", "units": "units"}, "ui": {"title": "UI Optimization", "refresh": "Refresh", "optimizeSelected": "Optimize Selected", "optimizeAll": "Optimize All", "searchPlaceholder": "Search components...", "noComponentSelected": "No component selected", "optimizationSuccess": "UI Optimization Success", "optimizationFailed": "UI Optimization Failed", "settingsApplied": "Settings Applied", "settingsApplyFailed": "Settings Apply Failed", "name": "Name", "type": "Type", "renderTime": "Render Time", "updateTime": "Update Time", "eventCount": "Event Count", "visible": "Visible", "children": "Children", "depth": "De<PERSON><PERSON>", "memoryUsage": "Memory Usage", "panel": "Panel", "button": "<PERSON><PERSON>", "text": "Text", "image": "Image", "input": "Input", "list": "List", "table": "Table", "chart": "Chart", "other": "Other", "components": "Components", "performance": "Performance", "optimization": "Optimization", "settings": "Settings", "totalRenderTime": "Total Render Time", "averageRenderTime": "Average Render Time", "totalComponents": "Total Components", "eventListeners": "Event Listeners", "renderTimeChart": "Render Time Chart", "componentsChart": "Components Chart", "noOptimizationResults": "No Optimization Results", "optimizationResults": "Optimization Results", "optimizedComponents": "Optimized Components", "renderTimeReduction": "Render Time Reduction", "batchedComponents": "Batched Components", "memoizedComponents": "Memoized Components", "virtualizedComponents": "Virtualized Components", "lazyLoadedComponents": "Lazy Loaded Components", "culledComponents": "Culled Components", "delegatedEvents": "Delegated Events", "removedEventListeners": "Removed Event Listeners", "memoryReduction": "Memory Reduction", "renderingSettings": "Rendering Settings", "eventSettings": "Event Settings", "advancedSettings": "Advanced Settings", "enableBatchRendering": "Enable <PERSON><PERSON> Rendering", "enableBatchRenderingDesc": "Combine multiple UI components into a single draw call", "enableMemoization": "Enable Memoization", "enableMemoizationDesc": "Cache component render results to avoid unnecessary re-renders", "batchSize": "<PERSON><PERSON> Si<PERSON>", "updateInterval": "Update Interval", "enableEventDelegation": "Enable Event Delegation", "enableEventDelegationDesc": "Attach event listeners to parent elements to reduce the number of event listeners", "maxEventListeners": "Max Event Listeners", "enableVirtualization": "Enable Virtualization", "enableVirtualizationDesc": "Only render components that are visible in the viewport", "enableLazyLoading": "Enable Lazy Loading", "enableLazyLoadingDesc": "Delay loading components that are not immediately visible", "enableCulling": "Enable Culling", "enableCullingDesc": "Don't render components outside the viewport", "cullingDistance": "Culling Distance", "useRequestAnimationFrame": "Use Request Animation Frame", "useWebGL": "Use WebGL", "useWebWorkers": "Use Web Workers", "useOffscreenCanvas": "Use Offscreen Canvas", "units": "units"}, "comparison": {"title": "Performance Comparison", "description": "Compare differences between performance snapshots", "selectBaseSnapshot": "Select Base Snapshot", "selectCompareSnapshot": "Select Compare Snapshot", "compare": "Compare", "createSnapshot": "Create Snapshot", "noComparisonData": "No comparison data available", "scoreChange": "Performance Score Change", "baseScore": "Base Score", "compareScore": "Compare Score", "keyMetrics": "Key Metrics", "before": "Before", "after": "After", "change": "Change", "percentChange": "Percent Change", "bottleneckChanges": "Bottleneck Changes", "resolvedBottlenecks": "Resolved Bottlenecks", "newBottlenecks": "New Bottlenecks", "changedBottlenecks": "Changed Bottlenecks", "noResolvedBottlenecks": "No resolved bottlenecks", "noNewBottlenecks": "No new bottlenecks", "noChangedBottlenecks": "No changed bottlenecks", "metric": "Metric", "renderingMetrics": "Rendering Metrics", "memoryMetrics": "Memory Metrics", "systemMetrics": "System Metrics", "physicsMetrics": "Physics Metrics", "networkMetrics": "Network Metrics", "otherMetrics": "Other Metrics", "keyMetricsComparison": "Key Metrics Comparison", "name": "Name", "date": "Date", "tags": "Tags", "actions": "Actions", "snapshots": "Snapshots", "snapshotName": "Snapshot Name", "snapshotDescription": "Snapshot Description", "snapshotTags": "Snapshot Tags", "tagsHint": "Separate multiple tags with commas", "nameRequired": "Please enter a snapshot name", "confirmDelete": "Confirm Delete", "deleteWarning": "Are you sure you want to delete this snapshot? This action cannot be undone.", "overview": "Overview", "metrics": "Metrics", "charts": "Charts"}, "test": {"title": "Performance Test", "description": "Description", "selectTest": "Select Test", "runTest": "Run Test", "stopTest": "Stop Test", "newTest": "New Test", "noTests": "No test configurations available", "createTest": "Create Test", "noResults": "No test results available", "exportResults": "Export Results", "saveAsSnapshot": "Save as <PERSON><PERSON><PERSON>", "name": "Name", "scenes": "Scenes", "repetitions": "Repetitions", "actions": "Actions", "testName": "Test Name", "testDescription": "Test Description", "sceneName": "Scene Name", "scenePath": "Scene Path", "duration": "Duration", "warmupDuration": "Warmup Duration", "nameRequired": "Please enter a test name", "sceneNameRequired": "Please enter a scene name", "scenePathRequired": "Please enter a scene path", "durationRequired": "Please enter a duration", "warmupDurationRequired": "Please enter a warmup duration", "repetitionsRequired": "Please enter repetitions", "addScene": "Add Scene", "removeScene": "Remove Scene", "editTest": "Edit Test", "confirmDelete": "Confirm Delete", "deleteWarning": "Are you sure you want to delete this test configuration? This action cannot be undone.", "exportFormat": "Export Format", "totalDuration": "Total Duration", "scenesTested": "Scenes Tested", "sceneResults": "Scene Results", "noBottlenecks": "No performance bottlenecks detected", "snapshotCreated": "Snapshot Created", "snapshotCreatedMessage": "Performance test results have been successfully saved as snapshots", "predefinedTests": "Predefined Tests", "results": "Results", "autoSaveReport": "Auto Save Report"}}}