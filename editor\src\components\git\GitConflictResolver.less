.git-conflict-resolver {
  position: absolute;
  top: 50px;
  right: 20px;
  width: 800px;
  z-index: 1000;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

.git-conflict-card {
  border-radius: 4px;
  overflow: hidden;
}

.git-conflict-count {
  font-size: 12px;
  background-color: rgba(0, 0, 0, 0.06);
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
}

.git-conflict-content {
  margin-bottom: 16px;
}

.git-conflict-file {
  margin-bottom: 16px;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.git-conflict-file-path {
  margin-left: 8px;
  font-weight: 500;
}

.git-conflict-comparison {
  display: flex;
  gap: 16px;
}

.git-conflict-side {
  flex: 1;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.git-conflict-side-header {
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.02);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.git-conflict-side-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.git-conflict-code {
  padding: 8px;
  margin: 0;
  max-height: 300px;
  overflow: auto;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
}

.git-conflict-editor {
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.git-conflict-textarea {
  width: 100%;
  min-height: 300px;
  padding: 8px;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.5;
  border: none;
  resize: vertical;
}

.git-conflict-editor-actions {
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.02);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: flex-end;
}

.git-conflict-resolution {
  margin-top: 16px;
  padding: 16px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
}

.git-conflict-resolution h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
}

.git-conflict-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.git-conflict-empty {
  padding: 40px 0;
  text-align: center;
  color: rgba(0, 0, 0, 0.45);
}
