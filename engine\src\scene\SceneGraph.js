"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SceneGraph = void 0;
var EventEmitter_1 = require("../utils/EventEmitter");
/**
 * 场景图类
 */
var SceneGraph = /** @class */ (function (_super) {
    __extends(SceneGraph, _super);
    /**
     * 创建场景图实例
     * @param scene 场景实例
     */
    function SceneGraph(scene) {
        var _this = _super.call(this) || this;
        /** 根节点 */
        _this.rootNode = null;
        /** 节点映射 */
        _this.nodeMap = new Map();
        /** 实体映射 */
        _this.entityMap = new Map();
        /** 是否已初始化 */
        _this.initialized = false;
        _this.scene = scene;
        return _this;
    }
    /**
     * 初始化场景图
     */
    SceneGraph.prototype.initialize = function () {
        var _this = this;
        if (this.initialized) {
            return;
        }
        // 构建场景图
        this.buildSceneGraph();
        // 监听场景实体变化事件
        this.scene.on('entityAdded', function (entity) {
            _this.onEntityAdded(entity);
        });
        this.scene.on('entityRemoved', function (entity) {
            _this.onEntityRemoved(entity);
        });
        this.initialized = true;
        this.emit('initialized');
    };
    /**
     * 构建场景图
     */
    SceneGraph.prototype.buildSceneGraph = function () {
        // 清空节点映射
        this.nodeMap.clear();
        this.entityMap.clear();
        // 获取场景中的所有实体
        var entities = this.scene.getEntities();
        // 找出根实体（没有父实体的实体）
        var rootEntities = entities.filter(function (entity) { return !entity.getParent(); });
        // 创建根节点
        this.rootNode = {
            id: 'root',
            name: '场景根节点',
            type: 'Root',
            children: [],
            components: [],
            visible: true,
            locked: false,
            expanded: true
        };
        // 添加到节点映射
        this.nodeMap.set('root', this.rootNode);
        // 递归构建场景图
        for (var _i = 0, rootEntities_1 = rootEntities; _i < rootEntities_1.length; _i++) {
            var entity = rootEntities_1[_i];
            var node = this.buildSceneGraphNode(entity);
            this.rootNode.children.push(node);
        }
        // 发出场景图构建完成事件
        this.emit('built', this.rootNode);
    };
    /**
     * 构建场景图节点
     * @param entity 实体实例
     * @returns 场景图节点
     */
    SceneGraph.prototype.buildSceneGraphNode = function (entity) {
        // 获取组件列表
        var components = entity.getAllComponents().map(function (component) { return component.getType(); });
        // 创建节点
        var node = {
            id: entity.id,
            name: entity.name,
            type: 'Entity',
            children: [],
            components: components,
            visible: entity.isActive(),
            locked: false,
            expanded: false // 默认不展开
        };
        // 添加到节点映射
        this.nodeMap.set(entity.id, node);
        this.entityMap.set(entity.id, entity);
        // 递归处理子实体
        var children = entity.getChildren();
        for (var _i = 0, children_1 = children; _i < children_1.length; _i++) {
            var child = children_1[_i];
            var childNode = this.buildSceneGraphNode(child);
            node.children.push(childNode);
        }
        return node;
    };
    /**
     * 实体添加事件处理
     * @param entity 添加的实体
     */
    SceneGraph.prototype.onEntityAdded = function (entity) {
        // 获取父实体
        var parent = entity.getParent();
        if (parent) {
            // 如果有父实体，则添加到父节点
            var parentNode = this.nodeMap.get(parent.id);
            if (parentNode) {
                var node = this.buildSceneGraphNode(entity);
                parentNode.children.push(node);
            }
        }
        else {
            // 如果没有父实体，则添加到根节点
            if (this.rootNode) {
                var node = this.buildSceneGraphNode(entity);
                this.rootNode.children.push(node);
            }
        }
        // 发出节点添加事件
        this.emit('nodeAdded', this.nodeMap.get(entity.id));
    };
    /**
     * 实体移除事件处理
     * @param entity 移除的实体
     */
    SceneGraph.prototype.onEntityRemoved = function (entity) {
        var node = this.nodeMap.get(entity.id);
        if (!node) {
            return;
        }
        // 从节点映射中移除
        this.nodeMap.delete(entity.id);
        this.entityMap.delete(entity.id);
        // 从父节点中移除
        var parent = entity.getParent();
        if (parent) {
            var parentNode = this.nodeMap.get(parent.id);
            if (parentNode) {
                var index = parentNode.children.findIndex(function (child) { return child.id === entity.id; });
                if (index !== -1) {
                    parentNode.children.splice(index, 1);
                }
            }
        }
        else if (this.rootNode) {
            // 如果没有父实体，则从根节点移除
            var index = this.rootNode.children.findIndex(function (child) { return child.id === entity.id; });
            if (index !== -1) {
                this.rootNode.children.splice(index, 1);
            }
        }
        // 发出节点移除事件
        this.emit('nodeRemoved', node);
    };
    /**
     * 获取根节点
     * @returns 根节点
     */
    SceneGraph.prototype.getRootNode = function () {
        return this.rootNode;
    };
    /**
     * 获取节点
     * @param id 节点ID
     * @returns 节点实例
     */
    SceneGraph.prototype.getNode = function (id) {
        return this.nodeMap.get(id) || null;
    };
    /**
     * 获取实体
     * @param id 节点ID
     * @returns 实体实例
     */
    SceneGraph.prototype.getEntity = function (id) {
        return this.entityMap.get(id) || null;
    };
    /**
     * 查询节点
     * @param options 查询选项
     * @returns 匹配的节点数组
     */
    SceneGraph.prototype.queryNodes = function (options) {
        if (options === void 0) { options = {}; }
        // 合并选项
        var mergedOptions = {
            includeInvisible: options.includeInvisible !== undefined ? options.includeInvisible : false,
            includeLocked: options.includeLocked !== undefined ? options.includeLocked : true,
            recursive: options.recursive !== undefined ? options.recursive : true,
            maxDepth: options.maxDepth !== undefined ? options.maxDepth : Infinity,
            componentFilter: options.componentFilter || [],
            nameFilter: options.nameFilter,
            tagFilter: options.tagFilter || [],
            customFilter: options.customFilter
        };
        // 从根节点开始查询
        if (!this.rootNode) {
            return [];
        }
        return this.queryNodeRecursive(this.rootNode, mergedOptions, 0);
    };
    /**
     * 递归查询节点
     * @param node 当前节点
     * @param options 查询选项
     * @param depth 当前深度
     * @returns 匹配的节点数组
     */
    SceneGraph.prototype.queryNodeRecursive = function (node, options, depth) {
        var result = [];
        // 检查深度
        if (depth > (options.maxDepth || Infinity)) {
            return result;
        }
        // 检查可见性
        if (!options.includeInvisible && !node.visible) {
            return result;
        }
        // 检查锁定状态
        if (!options.includeLocked && node.locked) {
            return result;
        }
        // 检查组件过滤器
        if (options.componentFilter && options.componentFilter.length > 0) {
            var hasAllComponents = options.componentFilter.every(function (component) {
                return node.components.includes(component);
            });
            if (!hasAllComponents) {
                return result;
            }
        }
        // 检查名称过滤器
        if (options.nameFilter) {
            if (typeof options.nameFilter === 'string') {
                if (!node.name.includes(options.nameFilter)) {
                    return result;
                }
            }
            else if (options.nameFilter instanceof RegExp) {
                if (!options.nameFilter.test(node.name)) {
                    return result;
                }
            }
        }
        // 检查标签过滤器
        if (options.tagFilter && options.tagFilter.length > 0) {
            var entity_1 = this.entityMap.get(node.id);
            if (entity_1) {
                var hasAnyTag = options.tagFilter.some(function (tag) { return entity_1.hasTag(tag); });
                if (!hasAnyTag) {
                    return result;
                }
            }
        }
        // 检查自定义过滤器
        if (options.customFilter && !options.customFilter(node)) {
            return result;
        }
        // 添加当前节点
        result.push(node);
        // 递归处理子节点
        if (options.recursive && node.children.length > 0) {
            for (var _i = 0, _a = node.children; _i < _a.length; _i++) {
                var child = _a[_i];
                var childResults = this.queryNodeRecursive(child, options, depth + 1);
                result.push.apply(result, childResults);
            }
        }
        return result;
    };
    /**
     * 更新场景图
     */
    SceneGraph.prototype.update = function () {
        this.buildSceneGraph();
    };
    /**
     * 销毁场景图
     */
    SceneGraph.prototype.dispose = function () {
        // 清空节点映射
        this.nodeMap.clear();
        this.entityMap.clear();
        // 清空根节点
        this.rootNode = null;
        // 移除所有事件监听器
        this.removeAllListeners();
        this.initialized = false;
    };
    return SceneGraph;
}(EventEmitter_1.EventEmitter));
exports.SceneGraph = SceneGraph;
