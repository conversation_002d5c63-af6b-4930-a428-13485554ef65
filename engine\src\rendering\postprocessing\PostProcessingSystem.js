"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostProcessingSystem = exports.PostProcessingEventType = void 0;
/**
 * 后处理系统
 * 用于管理和应用后处理效果
 */
var THREE = require("three");
// 使用类型断言导入后处理模块
// @ts-ignore
var EffectComposer_js_1 = require("three/examples/jsm/postprocessing/EffectComposer.js");
// @ts-ignore
var RenderPass_js_1 = require("three/examples/jsm/postprocessing/RenderPass.js");
var System_1 = require("../../core/System");
var EventEmitter_1 = require("../../utils/EventEmitter");
/**
 * 后处理系统事件类型
 */
var PostProcessingEventType;
(function (PostProcessingEventType) {
    /** 效果添加 */
    PostProcessingEventType["EFFECT_ADDED"] = "effectAdded";
    /** 效果移除 */
    PostProcessingEventType["EFFECT_REMOVED"] = "effectRemoved";
    /** 效果启用 */
    PostProcessingEventType["EFFECT_ENABLED"] = "effectEnabled";
    /** 效果禁用 */
    PostProcessingEventType["EFFECT_DISABLED"] = "effectDisabled";
    /** 效果顺序变更 */
    PostProcessingEventType["EFFECT_ORDER_CHANGED"] = "effectOrderChanged";
    /** 系统启用 */
    PostProcessingEventType["SYSTEM_ENABLED"] = "systemEnabled";
    /** 系统禁用 */
    PostProcessingEventType["SYSTEM_DISABLED"] = "systemDisabled";
    /** 渲染目标调整大小 */
    PostProcessingEventType["RESIZE"] = "resize";
})(PostProcessingEventType || (exports.PostProcessingEventType = PostProcessingEventType = {}));
/**
 * 后处理系统
 */
var PostProcessingSystem = exports.PostProcessingSystem = /** @class */ (function (_super) {
    __extends(PostProcessingSystem, _super);
    /**
     * 创建后处理系统
     * @param options 后处理系统选项
     */
    function PostProcessingSystem(options) {
        if (options === void 0) { options = {}; }
        var _this = 
        // 使用数字ID代替字符串名称
        _super.call(this, 0) || this;
        /** 系统名称（实例属性） */
        _this.name = PostProcessingSystem.NAME;
        /** 是否启用后处理 */
        _this.postProcessingEnabled = true;
        /** 渲染器 */
        _this.renderer = null;
        /** 场景 */
        _this.scene = null;
        /** 相机 */
        _this.camera = null;
        /** 效果合成器 */
        _this.composer = null;
        /** 渲染通道 */
        _this.renderPass = null;
        /** 效果列表 */
        _this.effects = [];
        /** 事件发射器 */
        _this.eventEmitter = new EventEmitter_1.EventEmitter();
        /** 是否已初始化 */
        _this.initialized = false;
        /** 是否已销毁 */
        _this.destroyed = false;
        /** 渲染目标宽度 */
        _this.width = 1;
        /** 渲染目标高度 */
        _this.height = 1;
        /** 渲染目标采样级别 */
        _this.samples = 0;
        /** 是否使用深度纹理 */
        _this.useDepthTexture = false;
        /** 是否使用浮点纹理 */
        _this.useFloatTexture = false;
        /** 渲染目标 */
        _this.renderTarget = null;
        // 设置系统名称（仅用于日志和调试）
        _this.name = PostProcessingSystem.NAME;
        // 设置是否启用后处理
        _this.postProcessingEnabled = options.enabled !== undefined ? options.enabled : true;
        // 调用基类的 setEnabled 方法设置系统是否启用
        _super.prototype.setEnabled.call(_this, _this.postProcessingEnabled);
        _this.autoResize = options.autoResize !== undefined ? options.autoResize : true;
        // 设置渲染目标选项
        var renderTargetOptions = options.renderTargetOptions || {};
        _this.width = renderTargetOptions.width || 1;
        _this.height = renderTargetOptions.height || 1;
        _this.samples = renderTargetOptions.samples || 0;
        _this.useDepthTexture = renderTargetOptions.depthTexture || false;
        _this.useFloatTexture = renderTargetOptions.floatTexture || false;
        return _this;
    }
    /**
     * 初始化系统
     */
    PostProcessingSystem.prototype.initialize = function () {
        if (this.initialized || this.destroyed)
            return;
        // 基类初始化
        _super.prototype.initialize.call(this);
    };
    /**
     * 检查是否已初始化
     * @returns 是否已初始化
     */
    PostProcessingSystem.prototype.isInitialized = function () {
        return this.initialized;
    };
    /**
     * 设置渲染器、场景和相机
     * @param renderer 渲染器
     * @param scene 场景
     * @param camera 相机
     */
    PostProcessingSystem.prototype.setup = function (renderer, scene, camera) {
        if (this.initialized || this.destroyed)
            return;
        this.renderer = renderer;
        this.scene = scene;
        this.camera = camera;
        // 获取渲染器尺寸
        var size = new THREE.Vector2();
        renderer.getSize(size);
        this.width = size.width;
        this.height = size.height;
        // 创建渲染目标
        this.createRenderTarget();
        // 创建效果合成器
        this.composer = new EffectComposer_js_1.EffectComposer(renderer, this.renderTarget);
        // 创建渲染通道
        this.renderPass = new RenderPass_js_1.RenderPass(scene, camera);
        this.composer.addPass(this.renderPass);
        // 添加已有效果
        for (var _i = 0, _a = this.effects; _i < _a.length; _i++) {
            var effect = _a[_i];
            this.addEffectPass(effect);
        }
        // 如果启用自动调整大小，则添加窗口大小变化事件监听器
        if (this.autoResize) {
            window.addEventListener('resize', this.handleResize.bind(this));
        }
        this.initialized = true;
    };
    /**
     * 创建渲染目标
     */
    PostProcessingSystem.prototype.createRenderTarget = function () {
        var _a;
        // 创建渲染目标参数
        var parameters = {
            minFilter: THREE.LinearFilter,
            magFilter: THREE.LinearFilter,
            format: THREE.RGBAFormat,
            // 使用 colorSpace 替代已弃用的 encoding
            colorSpace: THREE.SRGBColorSpace
        };
        // 如果使用浮点纹理，则设置类型
        if (this.useFloatTexture) {
            parameters.type = THREE.FloatType;
        }
        // 创建渲染目标
        this.renderTarget = new THREE.WebGLRenderTarget(this.width, this.height, parameters);
        // 如果支持多重采样，设置采样级别
        if (this.samples > 0 && ((_a = this.renderer) === null || _a === void 0 ? void 0 : _a.capabilities.isWebGL2)) {
            // 注意：WebGLMultisampleRenderTarget 已被弃用，但我们可以设置 samples 属性
            this.renderTarget.samples = this.samples;
        }
        // 如果使用深度纹理，则创建深度纹理
        if (this.useDepthTexture && this.renderTarget) {
            // 创建深度纹理
            var depthTexture = new THREE.DepthTexture(this.width, this.height);
            depthTexture.format = THREE.DepthFormat;
            depthTexture.type = THREE.UnsignedShortType;
            // 设置渲染目标的深度纹理
            this.renderTarget.depthTexture = depthTexture;
        }
    };
    /**
     * 处理窗口大小变化
     */
    PostProcessingSystem.prototype.handleResize = function () {
        if (!this.initialized || !this.renderer || !this.composer)
            return;
        // 获取渲染器尺寸
        var size = new THREE.Vector2();
        this.renderer.getSize(size);
        // 调整大小
        this.resize(size.width, size.height);
    };
    /**
     * 调整大小
     * @param width 宽度
     * @param height 高度
     */
    PostProcessingSystem.prototype.resize = function (width, height) {
        var _a;
        if (!this.initialized || !this.renderer || !this.composer)
            return;
        this.width = width;
        this.height = height;
        // 调整渲染目标大小
        (_a = this.renderTarget) === null || _a === void 0 ? void 0 : _a.setSize(width, height);
        // 调整效果合成器大小
        this.composer.setSize(width, height);
        // 调整效果大小
        for (var _i = 0, _b = this.effects; _i < _b.length; _i++) {
            var effect = _b[_i];
            effect.resize(width, height);
        }
        // 触发调整大小事件
        this.eventEmitter.emit(PostProcessingEventType.RESIZE, { width: width, height: height });
    };
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    PostProcessingSystem.prototype.update = function (deltaTime) {
        // 使用 super.isEnabled() 检查系统是否启用
        if (!this.initialized || !_super.prototype.isEnabled.call(this) || !this.postProcessingEnabled || !this.composer || this.destroyed)
            return;
        // 更新效果
        for (var _i = 0, _a = this.effects; _i < _a.length; _i++) {
            var effect = _a[_i];
            if (effect.isEnabled()) {
                effect.update(deltaTime);
            }
        }
        // 渲染效果合成器
        this.composer.render(deltaTime);
    };
    /**
     * 添加效果
     * @param effect 后处理效果
     * @returns 是否成功添加
     */
    PostProcessingSystem.prototype.addEffect = function (effect) {
        // 如果已经存在，则返回false
        if (this.effects.includes(effect))
            return false;
        // 添加效果
        this.effects.push(effect);
        // 如果已初始化，则添加效果通道
        if (this.initialized && this.composer) {
            this.addEffectPass(effect);
        }
        // 触发效果添加事件
        this.eventEmitter.emit(PostProcessingEventType.EFFECT_ADDED, { effect: effect });
        return true;
    };
    /**
     * 添加效果通道
     * @param effect 后处理效果
     */
    PostProcessingSystem.prototype.addEffectPass = function (effect) {
        if (!this.composer || !this.renderer)
            return;
        // 初始化效果
        effect.initialize(this.renderer, this.width, this.height);
        // 获取效果通道
        var pass = effect.getPass();
        if (pass) {
            // 添加通道
            this.composer.addPass(pass);
        }
    };
    /**
     * 移除效果
     * @param effect 后处理效果
     * @returns 是否成功移除
     */
    PostProcessingSystem.prototype.removeEffect = function (effect) {
        // 查找效果索引
        var index = this.effects.indexOf(effect);
        if (index === -1)
            return false;
        // 如果已初始化，则移除效果通道
        if (this.initialized && this.composer) {
            this.removeEffectPass(effect);
        }
        // 移除效果
        this.effects.splice(index, 1);
        // 触发效果移除事件
        this.eventEmitter.emit(PostProcessingEventType.EFFECT_REMOVED, { effect: effect });
        return true;
    };
    /**
     * 移除效果通道
     * @param effect 后处理效果
     */
    PostProcessingSystem.prototype.removeEffectPass = function (effect) {
        if (!this.composer)
            return;
        // 获取效果通道
        var pass = effect.getPass();
        if (pass) {
            // 移除通道
            var passes = this.composer.passes;
            var passIndex = passes.indexOf(pass);
            if (passIndex !== -1) {
                passes.splice(passIndex, 1);
            }
        }
        // 销毁效果
        effect.dispose();
    };
    /**
     * 获取效果
     * @param name 效果名称
     * @returns 后处理效果
     */
    PostProcessingSystem.prototype.getEffect = function (name) {
        return this.effects.find(function (effect) { return effect.getName() === name; }) || null;
    };
    /**
     * 获取所有效果
     * @returns 后处理效果列表
     */
    PostProcessingSystem.prototype.getEffects = function () {
        return __spreadArray([], this.effects, true);
    };
    /**
     * 启用效果
     * @param name 效果名称
     * @returns 是否成功启用
     */
    PostProcessingSystem.prototype.enableEffect = function (name) {
        var effect = this.getEffect(name);
        if (!effect)
            return false;
        effect.setEnabled(true);
        // 触发效果启用事件
        this.eventEmitter.emit(PostProcessingEventType.EFFECT_ENABLED, { effect: effect });
        return true;
    };
    /**
     * 禁用效果
     * @param name 效果名称
     * @returns 是否成功禁用
     */
    PostProcessingSystem.prototype.disableEffect = function (name) {
        var effect = this.getEffect(name);
        if (!effect)
            return false;
        effect.setEnabled(false);
        // 触发效果禁用事件
        this.eventEmitter.emit(PostProcessingEventType.EFFECT_DISABLED, { effect: effect });
        return true;
    };
    /**
     * 设置效果顺序
     * @param names 效果名称列表
     * @returns 是否成功设置
     */
    PostProcessingSystem.prototype.setEffectOrder = function (names) {
        if (!this.initialized || !this.composer)
            return false;
        // 检查所有名称是否存在
        for (var _i = 0, names_1 = names; _i < names_1.length; _i++) {
            var name_1 = names_1[_i];
            if (!this.getEffect(name_1))
                return false;
        }
        // 重新排序效果
        var newEffects = [];
        for (var _a = 0, names_2 = names; _a < names_2.length; _a++) {
            var name_2 = names_2[_a];
            var effect = this.getEffect(name_2);
            if (effect) {
                newEffects.push(effect);
            }
        }
        // 添加未包含在名称列表中的效果
        for (var _b = 0, _c = this.effects; _b < _c.length; _b++) {
            var effect = _c[_b];
            if (!names.includes(effect.getName())) {
                newEffects.push(effect);
            }
        }
        // 更新效果列表
        this.effects = newEffects;
        // 重新创建通道
        this.rebuildPasses();
        // 触发效果顺序变更事件
        this.eventEmitter.emit(PostProcessingEventType.EFFECT_ORDER_CHANGED, { effects: this.effects });
        return true;
    };
    /**
     * 重新构建通道
     */
    PostProcessingSystem.prototype.rebuildPasses = function () {
        if (!this.initialized || !this.composer || !this.renderPass)
            return;
        // 清除通道
        this.composer.passes = [this.renderPass];
        // 添加效果通道
        for (var _i = 0, _a = this.effects; _i < _a.length; _i++) {
            var effect = _a[_i];
            var pass = effect.getPass();
            if (pass) {
                this.composer.addPass(pass);
            }
        }
    };
    /**
     * 设置是否启用
     * @param enabled 是否启用
     */
    PostProcessingSystem.prototype.setEnabled = function (enabled) {
        // 检查状态是否变化
        if (_super.prototype.isEnabled.call(this) === enabled)
            return this;
        // 设置后处理启用状态
        this.postProcessingEnabled = enabled;
        // 调用基类方法设置系统启用状态
        _super.prototype.setEnabled.call(this, enabled);
        // 触发系统启用/禁用事件
        if (enabled) {
            this.eventEmitter.emit(PostProcessingEventType.SYSTEM_ENABLED);
        }
        else {
            this.eventEmitter.emit(PostProcessingEventType.SYSTEM_DISABLED);
        }
        return this;
    };
    /**
     * 获取是否启用
     * @returns 是否启用
     */
    PostProcessingSystem.prototype.isEnabled = function () {
        // 同时检查系统启用状态和后处理启用状态
        return _super.prototype.isEnabled.call(this) && this.postProcessingEnabled;
    };
    /**
     * 获取渲染目标
     * @returns 渲染目标
     */
    PostProcessingSystem.prototype.getRenderTarget = function () {
        return this.renderTarget;
    };
    /**
     * 获取效果合成器
     * @returns 效果合成器
     */
    PostProcessingSystem.prototype.getComposer = function () {
        return this.composer;
    };
    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param callback 监听器函数
     * @returns this 实例
     */
    PostProcessingSystem.prototype.on = function (event, callback) {
        // 使用自定义事件发射器
        this.eventEmitter.on(event, callback);
        return this;
    };
    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param callback 监听器函数
     * @returns this 实例
     */
    PostProcessingSystem.prototype.off = function (event, callback) {
        // 使用自定义事件发射器
        this.eventEmitter.off(event, callback);
        return this;
    };
    /**
     * 销毁系统
     */
    PostProcessingSystem.prototype.dispose = function () {
        var _a;
        if (this.destroyed)
            return;
        // 移除窗口大小变化事件监听器
        if (this.autoResize) {
            window.removeEventListener('resize', this.handleResize.bind(this));
        }
        // 销毁效果
        for (var _i = 0, _b = this.effects; _i < _b.length; _i++) {
            var effect = _b[_i];
            effect.dispose();
        }
        // 清空效果列表
        this.effects = [];
        // 销毁渲染目标
        (_a = this.renderTarget) === null || _a === void 0 ? void 0 : _a.dispose();
        // 清空引用
        this.renderTarget = null;
        this.composer = null;
        this.renderPass = null;
        this.renderer = null;
        this.scene = null;
        this.camera = null;
        // 移除所有事件监听器
        this.eventEmitter.removeAllListeners();
        this.initialized = false;
        this.destroyed = true;
    };
    /** 系统名称 */
    PostProcessingSystem.NAME = 'PostProcessingSystem';
    return PostProcessingSystem;
}(System_1.System));
