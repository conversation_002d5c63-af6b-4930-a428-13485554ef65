"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Time = void 0;
/**
 * 时间工具类
 * 提供时间相关的功能
 */
var Time = exports.Time = /** @class */ (function () {
    function Time() {
    }
    /**
     * 初始化时间系统
     */
    Time.initialize = function () {
        this.startTime = performance.now();
        this.previousTime = 0;
        this.currentTime = 0;
        this.deltaTime = 0;
        this.frameCount = 0;
        this.fps = 0;
        this.fpsUpdateTimer = 0;
        this.fpsCounter = 0;
    };
    /**
     * 更新时间系统
     * @param deltaTime 帧间隔时间（秒）
     */
    Time.update = function (deltaTime) {
        // 更新时间
        this.previousTime = this.currentTime;
        this.currentTime += deltaTime;
        this.deltaTime = deltaTime * this.timeScale;
        // 更新帧数
        this.frameCount++;
        this.fpsCounter++;
        // 更新FPS
        this.fpsUpdateTimer += deltaTime;
        if (this.fpsUpdateTimer >= this.fpsUpdateInterval) {
            this.fps = Math.round(this.fpsCounter / this.fpsUpdateTimer);
            this.fpsCounter = 0;
            this.fpsUpdateTimer = 0;
        }
    };
    /**
     * 获取游戏运行时间（秒）
     * @returns 游戏运行时间
     */
    Time.getTime = function () {
        return this.currentTime;
    };
    /**
     * 获取游戏开始以来的真实时间（秒）
     * @returns 真实时间
     */
    Time.getRealTime = function () {
        return (performance.now() - this.startTime) / 1000;
    };
    /**
     * 获取帧间隔时间（秒）
     * @returns 帧间隔时间
     */
    Time.getDeltaTime = function () {
        return this.deltaTime;
    };
    /**
     * 获取未缩放的帧间隔时间（秒）
     * @returns 未缩放的帧间隔时间
     */
    Time.getUnscaledDeltaTime = function () {
        return this.deltaTime / this.timeScale;
    };
    /**
     * 获取固定帧间隔时间（秒）
     * @returns 固定帧间隔时间
     */
    Time.getFixedDeltaTime = function () {
        return this.fixedDeltaTime;
    };
    /**
     * 设置固定帧间隔时间（秒）
     * @param fixedDeltaTime 固定帧间隔时间
     */
    Time.setFixedDeltaTime = function (fixedDeltaTime) {
        this.fixedDeltaTime = fixedDeltaTime;
    };
    /**
     * 获取时间缩放
     * @returns 时间缩放
     */
    Time.getTimeScale = function () {
        return this.timeScale;
    };
    /**
     * 设置时间缩放
     * @param timeScale 时间缩放
     */
    Time.setTimeScale = function (timeScale) {
        this.timeScale = Math.max(0, timeScale);
    };
    /**
     * 获取帧数
     * @returns 帧数
     */
    Time.getFrameCount = function () {
        return this.frameCount;
    };
    /**
     * 获取每秒帧数
     * @returns 每秒帧数
     */
    Time.getFPS = function () {
        return this.fps;
    };
    /**
     * 获取当前时间戳（毫秒）
     * @returns 当前时间戳
     */
    Time.now = function () {
        return performance.now();
    };
    /** 游戏开始时间（毫秒） */
    Time.startTime = 0;
    /** 上一帧时间（秒） */
    Time.previousTime = 0;
    /** 当前时间（秒） */
    Time.currentTime = 0;
    /** 帧间隔时间（秒） */
    Time.deltaTime = 0;
    /** 固定帧间隔时间（秒） */
    Time.fixedDeltaTime = 1 / 60;
    /** 时间缩放 */
    Time.timeScale = 1;
    /** 帧数 */
    Time.frameCount = 0;
    /** 每秒帧数 */
    Time.fps = 0;
    /** FPS更新间隔（秒） */
    Time.fpsUpdateInterval = 0.5;
    /** FPS更新计时器 */
    Time.fpsUpdateTimer = 0;
    /** FPS计数器 */
    Time.fpsCounter = 0;
    return Time;
}());
