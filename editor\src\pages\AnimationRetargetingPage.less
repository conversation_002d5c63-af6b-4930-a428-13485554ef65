/**
 * 动画重定向页面样式
 */
.animation-retargeting-page {
  height: 100vh;
  display: flex;
  flex-direction: column;

  .page-header {
    padding: 0 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
    height: 64px;
    line-height: 64px;

    .header-title {
      font-size: 18px;
      font-weight: bold;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .page-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 64px 0;
      text-align: center;

      .empty-icon {
        margin-bottom: 16px;
        color: #bfbfbf;
      }

      .empty-text {
        margin-bottom: 24px;
        color: #8c8c8c;
        font-size: 16px;
      }
    }

    .retargeting-card {
      .retargeting-card-content {
        min-height: 100px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .retargeting-info {
          margin-top: 16px;

          .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;

            .info-label {
              color: #8c8c8c;
            }

            .info-value {
              font-weight: bold;
            }
          }
        }
      }
    }
  }
}

// 暗色主题适配
.dark-theme {
  .animation-retargeting-page {
    .page-header {
      background-color: #1e1e1e;
      border-bottom-color: #303030;
    }

    .page-content {
      .empty-state {
        .empty-icon {
          color: #434343;
        }

        .empty-text {
          color: #8c8c8c;
        }
      }

      .retargeting-card {
        .retargeting-card-content {
          .retargeting-info {
            .info-item {
              .info-label {
                color: #8c8c8c;
              }
            }
          }
        }
      }
    }
  }
}
