/**
 * 动画生成类型定义
 * 用于定义AI动画生成相关的类型
 */
import { FacialAnimationClip } from '../animation/FacialAnimationClip';

/**
 * 动画生成请求
 */
export interface AnimationGenerationRequest {
  /** 请求ID */
  id: string;
  /** 提示文本 */
  prompt: string;
  /** 持续时间（秒） */
  duration: number;
  /** 动画类型 */
  type: 'facial' | 'body' | 'combined';
  /** 是否循环 */
  loop?: boolean;
  /** 参考动画片段 */
  referenceClip?: FacialAnimationClip;
  /** 风格 */
  style?: string;
  /** 强度 */
  intensity?: number;
  /** 随机种子 */
  seed?: number;
  /** 自定义数据 */
  userData?: any;
}

/**
 * 动画生成结果
 */
export interface AnimationGenerationResult {
  /** 请求ID */
  id: string;
  /** 是否成功 */
  success: boolean;
  /** 动画片段 */
  clip?: FacialAnimationClip;
  /** 生成时间（毫秒） */
  generationTime?: number;
  /** 错误信息 */
  error?: string;
  /** 自定义数据 */
  userData?: any;
}

/**
 * 情感类型
 */
export enum EmotionType {
  /** 快乐 */
  HAPPY = 'happy',
  /** 悲伤 */
  SAD = 'sad',
  /** 愤怒 */
  ANGRY = 'angry',
  /** 惊讶 */
  SURPRISED = 'surprised',
  /** 恐惧 */
  FEARFUL = 'fearful',
  /** 厌恶 */
  DISGUSTED = 'disgusted',
  /** 中性 */
  NEUTRAL = 'neutral',
  /** 蔑视 */
  CONTEMPT = 'contempt'
}
