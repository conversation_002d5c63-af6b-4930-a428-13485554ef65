/**
 * 环境预设面板
 * 
 * 该面板提供预定义的环境预设，用户可以快速应用这些预设到角色上。
 */
import React, { useState, useEffect } from 'react';
import { 
  Card, 
  List, 
  Button, 
  Space, 
  Tag, 
  Tooltip, 
  Divider, 
  Input, 
  Modal, 
  Form,
  Select,
  message,
  Collapse,
  Badge
} from 'antd';
import { 
  AppstoreOutlined, 
  PlusOutlined, 
  SearchOutlined, 
  SaveOutlined, 
  DeleteOutlined,
  EditOutlined,
  CopyOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { 
  EnvironmentResponseRule, 
  ResponseType, 
  ResponsePriority 
} from '../../../../engine/src/environment/components/EnvironmentResponseComponent';
import { getAllEnvironmentPresets } from '../../../../engine/src/environment/presets/EnvironmentPresets';

const { Search } = Input;
const { Option } = Select;
const { Panel } = Collapse;

/**
 * 环境预设面板属性接口
 */
interface EnvironmentPresetPanelProps {
  entityId?: string;
  onApplyPreset?: (preset: EnvironmentResponseRule) => void;
  onSaveCustomPreset?: (preset: EnvironmentResponseRule) => void;
  onDeleteCustomPreset?: (presetId: string) => void;
  customPresets?: EnvironmentResponseRule[];
}

/**
 * 环境预设面板组件
 */
const EnvironmentPresetPanel: React.FC<EnvironmentPresetPanelProps> = ({
  entityId,
  onApplyPreset,
  onSaveCustomPreset,
  onDeleteCustomPreset,
  customPresets = []
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [searchValue, setSearchValue] = useState<string>('');
  const [systemPresets, setSystemPresets] = useState<EnvironmentResponseRule[]>([]);
  const [filteredPresets, setFilteredPresets] = useState<EnvironmentResponseRule[]>([]);
  const [filteredCustomPresets, setFilteredCustomPresets] = useState<EnvironmentResponseRule[]>([]);
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [editingPreset, setEditingPreset] = useState<EnvironmentResponseRule | null>(null);
  const [activeCategory, setActiveCategory] = useState<string>('all');

  // 加载系统预设
  useEffect(() => {
    const presets = getAllEnvironmentPresets();
    setSystemPresets(presets);
    filterPresets(presets, customPresets, searchValue, activeCategory);
  }, []);

  // 当自定义预设变化时更新过滤
  useEffect(() => {
    filterPresets(systemPresets, customPresets, searchValue, activeCategory);
  }, [customPresets]);

  /**
   * 过滤预设
   * @param systemPresets 系统预设
   * @param customPresets 自定义预设
   * @param searchValue 搜索值
   * @param category 分类
   */
  const filterPresets = (
    systemPresets: EnvironmentResponseRule[], 
    customPresets: EnvironmentResponseRule[], 
    searchValue: string,
    category: string
  ) => {
    // 过滤系统预设
    let filtered = systemPresets;
    
    // 按分类过滤
    if (category !== 'all') {
      filtered = filtered.filter(preset => {
        if (category === 'animation' && preset.responseType === ResponseType.ANIMATION) return true;
        if (category === 'sound' && preset.responseType === ResponseType.SOUND) return true;
        if (category === 'effect' && preset.responseType === ResponseType.EFFECT) return true;
        if (category === 'behavior' && preset.responseType === ResponseType.BEHAVIOR) return true;
        if (category === 'high' && preset.priority === ResponsePriority.HIGH) return true;
        if (category === 'critical' && preset.priority === ResponsePriority.CRITICAL) return true;
        return false;
      });
    }
    
    // 按搜索值过滤
    if (searchValue) {
      const lowerSearchValue = searchValue.toLowerCase();
      filtered = filtered.filter(preset => 
        preset.name.toLowerCase().includes(lowerSearchValue) || 
        (preset.description && preset.description.toLowerCase().includes(lowerSearchValue))
      );
    }
    
    setFilteredPresets(filtered);
    
    // 过滤自定义预设
    let filteredCustom = customPresets;
    
    // 按分类过滤
    if (category !== 'all') {
      filteredCustom = filteredCustom.filter(preset => {
        if (category === 'animation' && preset.responseType === ResponseType.ANIMATION) return true;
        if (category === 'sound' && preset.responseType === ResponseType.SOUND) return true;
        if (category === 'effect' && preset.responseType === ResponseType.EFFECT) return true;
        if (category === 'behavior' && preset.responseType === ResponseType.BEHAVIOR) return true;
        if (category === 'high' && preset.priority === ResponsePriority.HIGH) return true;
        if (category === 'critical' && preset.priority === ResponsePriority.CRITICAL) return true;
        return false;
      });
    }
    
    // 按搜索值过滤
    if (searchValue) {
      const lowerSearchValue = searchValue.toLowerCase();
      filteredCustom = filteredCustom.filter(preset => 
        preset.name.toLowerCase().includes(lowerSearchValue) || 
        (preset.description && preset.description.toLowerCase().includes(lowerSearchValue))
      );
    }
    
    setFilteredCustomPresets(filteredCustom);
  };

  /**
   * 处理搜索
   * @param value 搜索值
   */
  const handleSearch = (value: string) => {
    setSearchValue(value);
    filterPresets(systemPresets, customPresets, value, activeCategory);
  };

  /**
   * 处理分类变更
   * @param category 分类
   */
  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    filterPresets(systemPresets, customPresets, searchValue, category);
  };

  /**
   * 处理应用预设
   * @param preset 预设
   */
  const handleApplyPreset = (preset: EnvironmentResponseRule) => {
    if (onApplyPreset) {
      onApplyPreset(preset);
      message.success(t('environment.presetApplied'));
    }
  };

  /**
   * 处理创建自定义预设
   */
  const handleCreateCustomPreset = () => {
    setEditingPreset(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  /**
   * 处理编辑自定义预设
   * @param preset 预设
   */
  const handleEditCustomPreset = (preset: EnvironmentResponseRule) => {
    setEditingPreset(preset);
    setIsModalVisible(true);
    form.setFieldsValue({
      name: preset.name,
      description: preset.description,
      responseType: preset.responseType,
      priority: preset.priority
    });
  };

  /**
   * 处理复制预设
   * @param preset 预设
   */
  const handleDuplicatePreset = (preset: EnvironmentResponseRule) => {
    const newPreset: EnvironmentResponseRule = {
      ...preset,
      id: `custom_preset_${Date.now()}`,
      name: `${preset.name} (${t('environment.copy')})`
    };
    
    if (onSaveCustomPreset) {
      onSaveCustomPreset(newPreset);
      message.success(t('environment.presetDuplicated'));
    }
  };

  /**
   * 处理删除自定义预设
   * @param presetId 预设ID
   */
  const handleDeleteCustomPreset = (presetId: string) => {
    Modal.confirm({
      title: t('environment.confirmDelete'),
      content: t('environment.confirmDeletePresetMessage'),
      okText: t('environment.delete'),
      okType: 'danger',
      cancelText: t('environment.cancel'),
      onOk: () => {
        if (onDeleteCustomPreset) {
          onDeleteCustomPreset(presetId);
          message.success(t('environment.presetDeleted'));
        }
      }
    });
  };

  /**
   * 处理保存自定义预设
   */
  const handleSaveCustomPreset = () => {
    form.validateFields().then(values => {
      const newPreset: EnvironmentResponseRule = {
        id: editingPreset ? editingPreset.id : `custom_preset_${Date.now()}`,
        name: values.name,
        description: values.description,
        responseType: values.responseType,
        priority: values.priority,
        conditions: editingPreset ? editingPreset.conditions : [],
        actions: editingPreset ? editingPreset.actions : [],
        enabled: true
      };
      
      if (onSaveCustomPreset) {
        onSaveCustomPreset(newPreset);
        setIsModalVisible(false);
        message.success(t('environment.presetSaved'));
      }
    });
  };

  /**
   * 渲染预设列表项
   * @param preset 预设
   * @param isCustom 是否自定义预设
   */
  const renderPresetItem = (preset: EnvironmentResponseRule, isCustom: boolean = false) => {
    return (
      <List.Item
        key={preset.id}
        actions={[
          <Tooltip title={t('environment.apply')}>
            <Button
              type="primary"
              icon={<CheckCircleOutlined />}
              onClick={() => handleApplyPreset(preset)}
            >
              {t('environment.apply')}
            </Button>
          </Tooltip>,
          <Tooltip title={t('environment.duplicate')}>
            <Button
              type="text"
              icon={<CopyOutlined />}
              onClick={() => handleDuplicatePreset(preset)}
            />
          </Tooltip>,
          isCustom && (
            <Tooltip title={t('environment.edit')}>
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => handleEditCustomPreset(preset)}
              />
            </Tooltip>
          ),
          isCustom && (
            <Tooltip title={t('environment.delete')}>
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDeleteCustomPreset(preset.id)}
              />
            </Tooltip>
          )
        ].filter(Boolean)}
      >
        <List.Item.Meta
          title={
            <Space>
              <span>{preset.name}</span>
              {isCustom && <Tag color="green">{t('environment.custom')}</Tag>}
              <Tag color={getResponseTypeColor(preset.responseType)}>{preset.responseType}</Tag>
              <Tag color={getPriorityColor(preset.priority)}>{getPriorityLabel(preset.priority)}</Tag>
            </Space>
          }
          description={preset.description}
        />
        <div className="preset-details">
          <Space>
            <Badge count={preset.conditions.length} overflowCount={99} style={{ backgroundColor: '#108ee9' }}>
              <Tag>{t('environment.conditions')}</Tag>
            </Badge>
            <Badge count={preset.actions.length} overflowCount={99} style={{ backgroundColor: '#87d068' }}>
              <Tag>{t('environment.actions')}</Tag>
            </Badge>
          </Space>
        </div>
      </List.Item>
    );
  };

  /**
   * 获取响应类型颜色
   * @param type 响应类型
   * @returns 颜色
   */
  const getResponseTypeColor = (type: string): string => {
    switch (type) {
      case ResponseType.ANIMATION:
        return 'blue';
      case ResponseType.EFFECT:
        return 'purple';
      case ResponseType.SOUND:
        return 'cyan';
      case ResponseType.BEHAVIOR:
        return 'orange';
      default:
        return 'default';
    }
  };

  /**
   * 获取优先级颜色
   * @param priority 优先级
   * @returns 颜色
   */
  const getPriorityColor = (priority: number): string => {
    switch (priority) {
      case ResponsePriority.LOW:
        return 'green';
      case ResponseType.MEDIUM:
        return 'blue';
      case ResponsePriority.HIGH:
        return 'orange';
      case ResponsePriority.CRITICAL:
        return 'red';
      default:
        return 'default';
    }
  };

  /**
   * 获取优先级标签
   * @param priority 优先级
   * @returns 标签
   */
  const getPriorityLabel = (priority: number): string => {
    switch (priority) {
      case ResponsePriority.LOW:
        return t('environment.priorities.low');
      case ResponsePriority.MEDIUM:
        return t('environment.priorities.medium');
      case ResponsePriority.HIGH:
        return t('environment.priorities.high');
      case ResponsePriority.CRITICAL:
        return t('environment.priorities.critical');
      default:
        return '';
    }
  };

  return (
    <div className="environment-preset-panel">
      <Card
        title={
          <Space>
            <AppstoreOutlined />
            <span>{t('environment.presetPanel')}</span>
          </Space>
        }
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateCustomPreset}
          >
            {t('environment.createCustomPreset')}
          </Button>
        }
      >
        <div className="preset-filters">
          <Search
            placeholder={t('environment.searchPresets')}
            allowClear
            enterButton={<SearchOutlined />}
            onSearch={handleSearch}
            style={{ marginBottom: 16 }}
          />
          
          <div className="category-filters">
            <Space wrap>
              <Button
                type={activeCategory === 'all' ? 'primary' : 'default'}
                onClick={() => handleCategoryChange('all')}
              >
                {t('environment.all')}
              </Button>
              <Button
                type={activeCategory === 'animation' ? 'primary' : 'default'}
                onClick={() => handleCategoryChange('animation')}
              >
                {t('environment.responseTypes.animation')}
              </Button>
              <Button
                type={activeCategory === 'sound' ? 'primary' : 'default'}
                onClick={() => handleCategoryChange('sound')}
              >
                {t('environment.responseTypes.sound')}
              </Button>
              <Button
                type={activeCategory === 'effect' ? 'primary' : 'default'}
                onClick={() => handleCategoryChange('effect')}
              >
                {t('environment.responseTypes.effect')}
              </Button>
              <Button
                type={activeCategory === 'behavior' ? 'primary' : 'default'}
                onClick={() => handleCategoryChange('behavior')}
              >
                {t('environment.responseTypes.behavior')}
              </Button>
              <Button
                type={activeCategory === 'high' ? 'primary' : 'default'}
                onClick={() => handleCategoryChange('high')}
              >
                {t('environment.priorities.high')}
              </Button>
              <Button
                type={activeCategory === 'critical' ? 'primary' : 'default'}
                onClick={() => handleCategoryChange('critical')}
              >
                {t('environment.priorities.critical')}
              </Button>
            </Space>
          </div>
        </div>
        
        <Divider orientation="left">{t('environment.customPresets')}</Divider>
        
        {filteredCustomPresets.length > 0 ? (
          <List
            itemLayout="horizontal"
            dataSource={filteredCustomPresets}
            renderItem={preset => renderPresetItem(preset, true)}
          />
        ) : (
          <div className="empty-custom-presets">
            <p>{t('environment.noCustomPresets')}</p>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateCustomPreset}
            >
              {t('environment.createCustomPreset')}
            </Button>
          </div>
        )}
        
        <Divider orientation="left">{t('environment.systemPresets')}</Divider>
        
        <Collapse defaultActiveKey={['1']}>
          <Panel header={t('environment.systemPresets')} key="1">
            <List
              itemLayout="horizontal"
              dataSource={filteredPresets}
              renderItem={preset => renderPresetItem(preset)}
            />
          </Panel>
        </Collapse>
      </Card>
      
      <Modal
        title={editingPreset ? t('environment.editPreset') : t('environment.createPreset')}
        open={isModalVisible}
        onOk={handleSaveCustomPreset}
        onCancel={() => setIsModalVisible(false)}
        okText={t('environment.save')}
        cancelText={t('environment.cancel')}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label={t('environment.presetName')}
            rules={[{ required: true, message: t('environment.presetNameRequired') }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="description"
            label={t('environment.presetDescription')}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          
          <Form.Item
            name="responseType"
            label={t('environment.responseType')}
            rules={[{ required: true, message: t('environment.responseTypeRequired') }]}
          >
            <Select>
              <Option value={ResponseType.ANIMATION}>{t('environment.responseTypes.animation')}</Option>
              <Option value={ResponseType.EFFECT}>{t('environment.responseTypes.effect')}</Option>
              <Option value={ResponseType.SOUND}>{t('environment.responseTypes.sound')}</Option>
              <Option value={ResponseType.BEHAVIOR}>{t('environment.responseTypes.behavior')}</Option>
              <Option value={ResponseType.CUSTOM}>{t('environment.responseTypes.custom')}</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="priority"
            label={t('environment.priority')}
            rules={[{ required: true, message: t('environment.priorityRequired') }]}
          >
            <Select>
              <Option value={ResponsePriority.LOW}>{t('environment.priorities.low')}</Option>
              <Option value={ResponsePriority.MEDIUM}>{t('environment.priorities.medium')}</Option>
              <Option value={ResponsePriority.HIGH}>{t('environment.priorities.high')}</Option>
              <Option value={ResponsePriority.CRITICAL}>{t('environment.priorities.critical')}</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default EnvironmentPresetPanel;
