/**
 * 锁定状态切片
 * 管理协作编辑中的锁定状态
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Lock, LockStatus } from '../../services/SmartLockingService';

/**
 * 锁定状态接口
 */
export interface LockState {
  locks: { [id: string]: Lock };
  activeLocks: string[];
  myLocks: string[];
  loading: boolean;
  error: string | null;
}

/**
 * 初始状态
 */
const initialState: LockState = {
  locks: {},
  activeLocks: [],
  myLocks: [],
  loading: false,
  error: null
};

/**
 * 锁定状态切片
 */
const lockSlice = createSlice({
  name: 'lock',
  initialState,
  reducers: {
    /**
     * 添加锁定
     */
    addLock: (state, action: PayloadAction<Lock>) => {
      const lock = action.payload;
      state.locks[lock.id] = lock;

      // 如果是活动状态，添加到活动锁定列表
      if (lock.status === LockStatus.ACTIVE) {
        if (!state.activeLocks.includes(lock.id)) {
          state.activeLocks.push(lock.id);
        }

        // 如果是当前用户的锁定，添加到我的锁定列表
        const currentUserId = localStorage.getItem('userId');
        if (lock.userId === currentUserId && !state.myLocks.includes(lock.id)) {
          state.myLocks.push(lock.id);
        }
      }
    },

    /**
     * 更新锁定
     */
    updateLock: (state, action: PayloadAction<{
      id: string;
      expiresAt?: number;
      status?: LockStatus;
    }>) => {
      const { id, expiresAt, status } = action.payload;
      const lock = state.locks[id];

      if (lock) {
        if (expiresAt !== undefined) {
          lock.expiresAt = expiresAt;
        }

        if (status !== undefined) {
          lock.status = status;

          // 如果状态变为非活动状态，从活动锁定列表中移除
          if (status !== LockStatus.ACTIVE) {
            state.activeLocks = state.activeLocks.filter(lockId => lockId !== id);
            state.myLocks = state.myLocks.filter(lockId => lockId !== id);
          }
        }
      }
    },

    /**
     * 移除锁定
     */
    removeLock: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      delete state.locks[id];
      state.activeLocks = state.activeLocks.filter(lockId => lockId !== id);
      state.myLocks = state.myLocks.filter(lockId => lockId !== id);
    },

    /**
     * 清除所有锁定
     */
    clearLocks: (state) => {
      state.locks = {};
      state.activeLocks = [];
      state.myLocks = [];
    },

    /**
     * 设置加载状态
     */
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },

    /**
     * 设置错误
     */
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    }
  }
});

export const {
  addLock,
  updateLock,
  removeLock,
  clearLocks,
  setLoading,
  setError
} = lockSlice.actions;

export default lockSlice.reducer;
