/**
 * 场景版本比较面板样式
 */
.scene-version-compare-panel {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .compare-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .ant-card-body {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
    }

    .compare-content {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
    }

    .versions-container {
      display: flex;
      margin-bottom: 16px;

      .version-column {
        flex: 1;
        padding: 16px;

        .version-info {
          h4 {
            margin-bottom: 16px;
          }

          .version-actions {
            margin-top: 16px;
            text-align: center;
          }
        }
      }

      .version-divider {
        height: auto;
        margin: 0 16px;
      }
    }

    .differences-container {
      margin-bottom: 16px;

      .ant-tabs-content {
        padding: 16px;
      }

      .difference-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 16px;

        .header-title {
          display: flex;
          align-items: center;

          h4 {
            margin: 0;
            margin-right: 16px;
          }
        }

        .header-actions {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }
      }

      .entity-tree {
        margin-top: 16px;
        border: 1px solid #f0f0f0;
        border-radius: 2px;
        padding: 8px;
        max-height: 400px;
        overflow: auto;

        .entity-tree-node {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;

          .entity-actions {
            opacity: 0.6;
            transition: opacity 0.3s;

            &:hover {
              opacity: 1;
            }
          }
        }
      }

      .entity-selection-info {
        margin-top: 8px;
        padding: 8px;
        background-color: #f9f9f9;
        border-radius: 4px;
      }

      .setting-section {
        margin-bottom: 24px;

        h5 {
          margin-bottom: 8px;
        }
      }

      .different-value {
        background-color: #fff1f0;
        padding: 2px 4px;
        border-radius: 2px;
      }

      .added-entity {
        color: #52c41a;
      }

      .removed-entity {
        color: #ff4d4f;
        text-decoration: line-through;
      }

      .modified-entity {
        color: #1890ff;
      }
    }

    .compare-footer {
      padding: 16px;
      background-color: #f9f9f9;
    }
  }

  .rollback-options {
    margin-top: 16px;

    .ant-checkbox-wrapper {
      margin-bottom: 8px;
    }
  }

  .merge-options {
    margin: 16px 0;

    .merge-option-item {
      margin-bottom: 16px;

      .ant-radio-group {
        display: flex;
        flex-direction: column;
        margin-top: 8px;

        .ant-radio-wrapper {
          margin-bottom: 8px;
        }
      }
    }
  }
}

/* 实体树节点样式 */
.entity-node {
  display: flex;
  align-items: center;

  .entity-icon {
    margin-right: 8px;
  }

  .entity-name {
    flex: 1;
  }

  &.added {
    color: #52c41a;
  }

  &.removed {
    color: #ff4d4f;
    text-decoration: line-through;
  }

  &.modified {
    color: #1890ff;
  }
}

/* 组件差异样式 */
.component-diff {
  margin-top: 8px;

  .component-header {
    font-weight: bold;
    margin-bottom: 4px;
  }

  .property-diff {
    padding-left: 16px;

    .property-name {
      display: inline-block;
      width: 120px;
    }

    .property-value {
      &.added {
        color: #52c41a;
      }

      &.removed {
        color: #ff4d4f;
        text-decoration: line-through;
      }

      &.modified {
        color: #1890ff;
      }
    }
  }
}

/* 抽屉样式 */
.ant-drawer-content-wrapper {
  .ant-drawer-body {
    padding: 0;

    .entity-component-compare-panel,
    .three-d-view-compare-panel {
      margin: 0;

      .compare-card {
        box-shadow: none;
        border-radius: 0;
      }
    }
  }
}
