/**
 * 帮助系统
 * 管理帮助内容和显示
 */
import { EventEmitter } from 'events';

// 帮助内容类型
export interface HelpContent {
  id: string;           // 帮助内容ID
  title: string;        // 标题
  content: string;      // 内容（支持Markdown）
  category: string;     // 分类
  tags: string[];       // 标签
  relatedTopics?: string[]; // 相关主题
  videoUrl?: string;    // 相关视频URL
  images?: string[];    // 相关图片URL
}

// 帮助上下文类型
export interface HelpContext {
  componentId?: string; // 组件ID
  featureId?: string;   // 功能ID
  pageId?: string;      // 页面ID
  searchQuery?: string; // 搜索查询
}

// 帮助系统配置
export interface HelpSystemConfig {
  defaultLanguage?: string;  // 默认语言
  enableSearch?: boolean;    // 是否启用搜索
  enableHistory?: boolean;   // 是否启用历史记录
  maxHistoryItems?: number;  // 最大历史记录数量
}

/**
 * 帮助系统类
 * 单例模式
 */
export class HelpSystem extends EventEmitter {
  private static instance: HelpSystem;
  private helpContents: Map<string, HelpContent> = new Map();
  private helpHistory: string[] = [];
  private currentHelpId: string | null = null;
  private config: HelpSystemConfig;

  /**
   * 获取帮助系统实例
   */
  public static getInstance(): HelpSystem {
    if (!HelpSystem.instance) {
      HelpSystem.instance = new HelpSystem();
    }
    return HelpSystem.instance;
  }

  /**
   * 构造函数
   * @param config 配置
   */
  private constructor(config: HelpSystemConfig = {}) {
    super();
    this.config = {
      defaultLanguage: 'zh-CN',
      enableSearch: true,
      enableHistory: true,
      maxHistoryItems: 10,
      ...config
    };
  }

  /**
   * 初始化帮助系统
   * @param helpContents 帮助内容数组
   */
  public initialize(helpContents: HelpContent[]): void {
    // 清空现有内容
    this.helpContents.clear();
    
    // 添加新内容
    helpContents.forEach(content => {
      this.helpContents.set(content.id, content);
    });
    
    // 触发初始化完成事件
    this.emit('initialized', { count: helpContents.length });
  }

  /**
   * 添加帮助内容
   * @param content 帮助内容
   */
  public addHelpContent(content: HelpContent): void {
    this.helpContents.set(content.id, content);
    this.emit('contentAdded', content);
  }

  /**
   * 获取帮助内容
   * @param id 帮助内容ID
   * @returns 帮助内容
   */
  public getHelpContent(id: string): HelpContent | undefined {
    return this.helpContents.get(id);
  }

  /**
   * 显示帮助内容
   * @param id 帮助内容ID
   */
  public showHelp(id: string): void {
    const content = this.helpContents.get(id);
    if (!content) {
      console.warn(`帮助内容不存在: ${id}`);
      return;
    }

    // 更新当前帮助ID
    this.currentHelpId = id;

    // 添加到历史记录
    if (this.config.enableHistory) {
      // 如果已经在历史记录中，先移除
      const index = this.helpHistory.indexOf(id);
      if (index !== -1) {
        this.helpHistory.splice(index, 1);
      }

      // 添加到历史记录开头
      this.helpHistory.unshift(id);

      // 限制历史记录数量
      if (this.helpHistory.length > (this.config.maxHistoryItems || 10)) {
        this.helpHistory.pop();
      }
    }

    // 触发显示事件
    this.emit('helpShown', content);
  }

  /**
   * 根据上下文显示帮助
   * @param context 帮助上下文
   */
  public showHelpByContext(context: HelpContext): void {
    // 根据上下文查找最匹配的帮助内容
    const matchedContent = this.findHelpByContext(context);
    
    if (matchedContent) {
      this.showHelp(matchedContent.id);
    } else {
      console.warn('未找到匹配的帮助内容', context);
      this.emit('helpNotFound', context);
    }
  }

  /**
   * 根据上下文查找帮助内容
   * @param context 帮助上下文
   * @returns 匹配的帮助内容
   */
  private findHelpByContext(context: HelpContext): HelpContent | undefined {
    // 如果有组件ID，优先匹配组件ID
    if (context.componentId) {
      const content = this.helpContents.get(context.componentId);
      if (content) return content;
    }

    // 如果有功能ID，匹配功能ID
    if (context.featureId) {
      const content = this.helpContents.get(context.featureId);
      if (content) return content;
    }

    // 如果有页面ID，匹配页面ID
    if (context.pageId) {
      const content = this.helpContents.get(context.pageId);
      if (content) return content;
    }

    // 如果有搜索查询，执行搜索
    if (context.searchQuery && this.config.enableSearch) {
      return this.searchHelp(context.searchQuery)[0];
    }

    return undefined;
  }

  /**
   * 搜索帮助内容
   * @param query 搜索查询
   * @returns 匹配的帮助内容数组
   */
  public searchHelp(query: string): HelpContent[] {
    if (!query || !this.config.enableSearch) return [];

    const results: HelpContent[] = [];
    const lowerQuery = query.toLowerCase();

    // 遍历所有帮助内容
    this.helpContents.forEach(content => {
      // 检查标题、内容、分类和标签
      if (
        content.title.toLowerCase().includes(lowerQuery) ||
        content.content.toLowerCase().includes(lowerQuery) ||
        content.category.toLowerCase().includes(lowerQuery) ||
        content.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
      ) {
        results.push(content);
      }
    });

    // 触发搜索完成事件
    this.emit('searchCompleted', { query, results });

    return results;
  }

  /**
   * 获取帮助历史记录
   * @returns 帮助历史记录
   */
  public getHelpHistory(): HelpContent[] {
    if (!this.config.enableHistory) return [];

    return this.helpHistory
      .map(id => this.helpContents.get(id))
      .filter((content): content is HelpContent => !!content);
  }

  /**
   * 清除帮助历史记录
   */
  public clearHelpHistory(): void {
    this.helpHistory = [];
    this.emit('historyCleared');
  }

  /**
   * 获取当前帮助内容
   * @returns 当前帮助内容
   */
  public getCurrentHelp(): HelpContent | undefined {
    if (!this.currentHelpId) return undefined;
    return this.helpContents.get(this.currentHelpId);
  }

  /**
   * 获取所有帮助内容
   * @returns 所有帮助内容
   */
  public getAllHelpContents(): HelpContent[] {
    return Array.from(this.helpContents.values());
  }

  /**
   * 获取帮助分类
   * @returns 帮助分类数组
   */
  public getHelpCategories(): string[] {
    const categories = new Set<string>();
    this.helpContents.forEach(content => {
      categories.add(content.category);
    });
    return Array.from(categories);
  }

  /**
   * 获取帮助标签
   * @returns 帮助标签数组
   */
  public getHelpTags(): string[] {
    const tags = new Set<string>();
    this.helpContents.forEach(content => {
      content.tags.forEach(tag => tags.add(tag));
    });
    return Array.from(tags);
  }
}

// 导出单例实例
export default HelpSystem.getInstance();
