/**
 * 编辑区域状态管理
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { EditingZone } from '../../services/ConflictPreventionService';

// 状态接口
interface EditingZonesState {
  zones: EditingZone[];
  showEditingZones: boolean;
  highlightEditingZones: boolean;
}

// 初始状态
const initialState: EditingZonesState = {
  zones: [],
  showEditingZones: true,
  highlightEditingZones: true
};

// 创建Slice
export const editingZonesSlice = createSlice({
  name: 'editingZones',
  initialState,
  reducers: {
    // 添加编辑区域
    addEditingZone: (state, action: PayloadAction<EditingZone>) => {
      // 检查是否已存在相同ID的区域
      const existingIndex = state.zones.findIndex(z => z.id === action.payload.id);
      
      if (existingIndex >= 0) {
        // 更新现有区域
        state.zones[existingIndex] = action.payload;
      } else {
        // 添加新区域
        state.zones.push(action.payload);
      }
    },
    
    // 更新编辑区域
    updateEditingZone: (state, action: PayloadAction<{ id: string, lastUpdateTime: number }>) => {
      const { id, lastUpdateTime } = action.payload;
      const zone = state.zones.find(z => z.id === id);
      
      if (zone) {
        zone.lastUpdateTime = lastUpdateTime;
      }
    },
    
    // 移除编辑区域
    removeEditingZone: (state, action: PayloadAction<string>) => {
      state.zones = state.zones.filter(z => z.id !== action.payload);
    },
    
    // 清除所有编辑区域
    clearEditingZones: (state) => {
      state.zones = [];
    },
    
    // 设置是否显示编辑区域
    setShowEditingZones: (state, action: PayloadAction<boolean>) => {
      state.showEditingZones = action.payload;
    },
    
    // 设置是否高亮编辑区域
    setHighlightEditingZones: (state, action: PayloadAction<boolean>) => {
      state.highlightEditingZones = action.payload;
    }
  }
});

// 导出Actions
export const {
  addEditingZone,
  updateEditingZone,
  removeEditingZone,
  clearEditingZones,
  setShowEditingZones,
  setHighlightEditingZones
} = editingZonesSlice.actions;

// 导出Reducer
export default editingZonesSlice.reducer;
