/**
 * 权限服务
 * 负责管理用户权限和角色
 */
import { EventEmitter } from 'events';
import { message } from 'antd';
import { store } from '../store';
import {
  setUserRole,
  setUserPermissions,
  setRolePermissions,
  addCustomPermission,
  removeCustomPermission,
  setEnableLogging
} from '../store/collaboration/permissionSlice';
import { CollaborationRole } from './CollaborationService';
import { permissionLogService, PermissionLogType } from './PermissionLogService';
import { organizationPermissionService } from './OrganizationPermissionService';

// 权限枚举
export enum Permission {
  // 基本权限
  VIEW_SCENE = 'view_scene',
  EDIT_SCENE = 'edit_scene',

  // 实体操作权限
  CREATE_ENTITY = 'create_entity',
  UPDATE_ENTITY = 'update_entity',
  DELETE_ENTITY = 'delete_entity',

  // 组件操作权限
  ADD_COMPONENT = 'add_component',
  UPDATE_COMPONENT = 'update_component',
  REMOVE_COMPONENT = 'remove_component',

  // 资源操作权限
  UPLOAD_ASSET = 'upload_asset',
  DELETE_ASSET = 'delete_asset',

  // 场景操作权限
  SAVE_SCENE = 'save_scene',
  EXPORT_SCENE = 'export_scene',
  IMPORT_SCENE = 'import_scene',

  // 用户管理权限
  MANAGE_USERS = 'manage_users',
  ASSIGN_ROLES = 'assign_roles',
  MANAGE_PERMISSIONS = 'manage_permissions',

  // 项目管理权限
  MANAGE_PROJECT = 'manage_project',
  DELETE_PROJECT = 'delete_project',
}

// 默认角色权限映射
export const DEFAULT_ROLE_PERMISSIONS = {
  [CollaborationRole.VIEWER]: [
    Permission.VIEW_SCENE,
  ],
  [CollaborationRole.EDITOR]: [
    Permission.VIEW_SCENE,
    Permission.EDIT_SCENE,
    Permission.CREATE_ENTITY,
    Permission.UPDATE_ENTITY,
    Permission.DELETE_ENTITY,
    Permission.ADD_COMPONENT,
    Permission.UPDATE_COMPONENT,
    Permission.REMOVE_COMPONENT,
    Permission.UPLOAD_ASSET,
    Permission.SAVE_SCENE,
    Permission.EXPORT_SCENE,
    Permission.IMPORT_SCENE,
  ],
  [CollaborationRole.ADMIN]: [
    Permission.VIEW_SCENE,
    Permission.EDIT_SCENE,
    Permission.CREATE_ENTITY,
    Permission.UPDATE_ENTITY,
    Permission.DELETE_ENTITY,
    Permission.ADD_COMPONENT,
    Permission.UPDATE_COMPONENT,
    Permission.REMOVE_COMPONENT,
    Permission.UPLOAD_ASSET,
    Permission.DELETE_ASSET,
    Permission.SAVE_SCENE,
    Permission.EXPORT_SCENE,
    Permission.IMPORT_SCENE,
    Permission.MANAGE_USERS,
    Permission.ASSIGN_ROLES,
  ],
  [CollaborationRole.OWNER]: [
    Permission.VIEW_SCENE,
    Permission.EDIT_SCENE,
    Permission.CREATE_ENTITY,
    Permission.UPDATE_ENTITY,
    Permission.DELETE_ENTITY,
    Permission.ADD_COMPONENT,
    Permission.UPDATE_COMPONENT,
    Permission.REMOVE_COMPONENT,
    Permission.UPLOAD_ASSET,
    Permission.DELETE_ASSET,
    Permission.SAVE_SCENE,
    Permission.EXPORT_SCENE,
    Permission.IMPORT_SCENE,
    Permission.MANAGE_USERS,
    Permission.ASSIGN_ROLES,
    Permission.MANAGE_PERMISSIONS,
    Permission.MANAGE_PROJECT,
    Permission.DELETE_PROJECT,
  ],
};

/**
 * 权限服务类
 */
class PermissionService extends EventEmitter {
  private rolePermissions: Map<CollaborationRole, Set<Permission>>;
  private userCustomPermissions: Map<string, Set<Permission>>;
  private userRoles: Map<string, CollaborationRole>;
  private negativePermissions: Map<string, Set<Permission>>; // 用于存储被明确拒绝的权限
  private useOrganizationPermissions: boolean = false; // 是否使用组织权限
  private enableInheritance: boolean = true; // 是否启用权限继承

  constructor() {
    super();

    // 初始化角色权限映射
    this.rolePermissions = new Map();
    for (const [role, permissions] of Object.entries(DEFAULT_ROLE_PERMISSIONS)) {
      this.rolePermissions.set(role as CollaborationRole, new Set(permissions));
    }

    // 初始化用户自定义权限和角色
    this.userCustomPermissions = new Map();
    this.userRoles = new Map();
    this.negativePermissions = new Map();
  }

  /**
   * 设置是否启用权限继承
   * @param enabled 是否启用
   */
  public setEnableInheritance(enabled: boolean): void {
    this.enableInheritance = enabled;
  }

  /**
   * 设置是否使用组织权限
   * @param enabled 是否启用
   */
  public setUseOrganizationPermissions(enabled: boolean): void {
    this.useOrganizationPermissions = enabled;
    organizationPermissionService.setEnabled(enabled);
  }

  /**
   * 初始化权限服务
   */
  public initialize(): void {
    // 将角色权限映射同步到Redux
    for (const [role, permissions] of this.rolePermissions.entries()) {
      store.dispatch(setRolePermissions({
        role,
        permissions: Array.from(permissions)
      }));
    }
  }

  /**
   * 检查用户是否有指定权限
   * @param userId 用户ID
   * @param permission 权限
   * @returns 是否有权限
   */
  public hasPermission(userId: string, permission: Permission): boolean {
    // 获取用户角色
    const role = this.userRoles.get(userId);
    if (!role) {
      return false;
    }

    // 检查是否有明确拒绝的权限
    const negativePermissions = this.negativePermissions.get(userId);
    if (negativePermissions && negativePermissions.has(permission)) {
      return false;
    }

    // 检查用户自定义权限
    const customPermissions = this.userCustomPermissions.get(userId);
    if (customPermissions && customPermissions.has(permission)) {
      return true;
    }

    // 如果启用了权限继承，检查角色权限
    if (this.enableInheritance) {
      const rolePermissions = this.rolePermissions.get(role);
      if (rolePermissions?.has(permission)) {
        return true;
      }
    }

    // 如果启用了组织权限，检查组织权限
    if (this.useOrganizationPermissions) {
      if (organizationPermissionService.hasPermission(userId, permission)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 明确拒绝用户权限
   * @param userId 用户ID
   * @param permission 权限
   * @param operatorId 操作者ID
   */
  public denyUserPermission(userId: string, permission: Permission, operatorId: string): void {
    let negativePermissions = this.negativePermissions.get(userId);
    if (!negativePermissions) {
      negativePermissions = new Set();
      this.negativePermissions.set(userId, negativePermissions);
    }

    negativePermissions.add(permission);

    // 记录权限变更日志
    permissionLogService.logPermissionRevoked(operatorId, userId, permission);

    // 触发事件
    this.emit('userPermissionDenied', userId, permission);
  }

  /**
   * 设置用户角色
   * @param userId 用户ID
   * @param role 角色
   */
  public setUserRole(userId: string, role: CollaborationRole, operatorId?: string): void {
    const oldRole = this.userRoles.get(userId);
    this.userRoles.set(userId, role);

    // 更新Redux状态
    store.dispatch(setUserRole({
      userId,
      role
    }));

    // 更新用户权限
    const permissions = this.rolePermissions.get(role);
    if (permissions) {
      store.dispatch(setUserPermissions({
        userId,
        permissions: Array.from(permissions)
      }));
    }

    // 记录权限变更日志
    if (operatorId) {
      permissionLogService.logRoleChanged(operatorId, userId, role, oldRole);
    }

    // 触发事件
    this.emit('userRoleChanged', userId, role);
  }

  /**
   * 添加用户自定义权限
   * @param userId 用户ID
   * @param permission 权限
   */
  public addUserCustomPermission(userId: string, permission: Permission, operatorId?: string): void {
    let customPermissions = this.userCustomPermissions.get(userId);
    if (!customPermissions) {
      customPermissions = new Set();
      this.userCustomPermissions.set(userId, customPermissions);
    }

    customPermissions.add(permission);

    // 如果有明确拒绝的权限，移除它
    const negativePermissions = this.negativePermissions.get(userId);
    if (negativePermissions) {
      negativePermissions.delete(permission);
    }

    // 更新Redux状态
    store.dispatch(addCustomPermission({
      userId,
      permission
    }));

    // 记录权限变更日志
    if (operatorId) {
      permissionLogService.logPermissionGranted(operatorId, userId, permission);
    }

    // 触发事件
    this.emit('userPermissionChanged', userId, Array.from(customPermissions));
  }

  /**
   * 移除用户自定义权限
   * @param userId 用户ID
   * @param permission 权限
   */
  public removeUserCustomPermission(userId: string, permission: Permission, operatorId?: string): void {
    const customPermissions = this.userCustomPermissions.get(userId);
    if (customPermissions) {
      customPermissions.delete(permission);

      // 更新Redux状态
      store.dispatch(removeCustomPermission({
        userId,
        permission
      }));

      // 记录权限变更日志
      if (operatorId) {
        permissionLogService.logPermissionRevoked(operatorId, userId, permission);
      }

      // 触发事件
      this.emit('userPermissionChanged', userId, Array.from(customPermissions));
    }
  }

  /**
   * 设置角色权限
   * @param role 角色
   * @param permissions 权限列表
   */
  public setRolePermissions(role: CollaborationRole, permissions: Permission[], operatorId?: string): void {
    const oldPermissions = this.rolePermissions.get(role) ? Array.from(this.rolePermissions.get(role)!) : [];
    this.rolePermissions.set(role, new Set(permissions));

    // 更新Redux状态
    store.dispatch(setRolePermissions({
      role,
      permissions
    }));

    // 记录权限变更日志
    if (operatorId) {
      permissionLogService.logRolePermissionsChanged(operatorId, role, permissions, oldPermissions);
    }

    // 触发事件
    this.emit('rolePermissionsChanged', role, permissions);

    // 更新所有具有该角色的用户的权限
    for (const [userId, userRole] of this.userRoles.entries()) {
      if (userRole === role) {
        store.dispatch(setUserPermissions({
          userId,
          permissions
        }));
      }
    }
  }

  /**
   * 设置是否启用权限日志记录
   * @param enabled 是否启用
   */
  public setEnableLogging(enabled: boolean): void {
    store.dispatch(setEnableLogging(enabled));
    permissionLogService.setEnabled(enabled);
  }

  /**
   * 获取用户角色
   * @param userId 用户ID
   * @returns 用户角色
   */
  public getUserRole(userId: string): CollaborationRole | undefined {
    return this.userRoles.get(userId);
  }

  /**
   * 获取角色权限
   * @param role 角色
   * @returns 权限列表
   */
  public getRolePermissions(role: CollaborationRole): Permission[] {
    const permissions = this.rolePermissions.get(role);
    return permissions ? Array.from(permissions) : [];
  }

  /**
   * 获取用户自定义权限
   * @param userId 用户ID
   * @returns 权限列表
   */
  public getUserCustomPermissions(userId: string): Permission[] {
    const permissions = this.userCustomPermissions.get(userId);
    return permissions ? Array.from(permissions) : [];
  }
}

// 创建权限服务实例
export const permissionService = new PermissionService();
