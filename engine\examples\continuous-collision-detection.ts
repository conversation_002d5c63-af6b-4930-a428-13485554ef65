/**
 * 连续碰撞检测示例
 * 展示如何使用连续碰撞检测解决高速移动物体的穿透问题
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { Engine } from '../src/core/Engine';
import { World } from '../src/core/World';
import { Entity } from '../src/core/Entity';
import { PhysicsSystem } from '../src/physics/PhysicsSystem';
import { PhysicsBody } from '../src/physics/PhysicsBody';
import { PhysicsCollider } from '../src/physics/PhysicsCollider';
import { InputSystem } from '../src/input/InputSystem';
import { Renderer } from '../src/rendering/Renderer';
import { Camera } from '../src/rendering/Camera';
import { Scene } from '../src/scene/Scene';
import { Transform } from '../src/scene/Transform';

// 创建引擎
const engine = new Engine();

// 创建世界
const world = new World();
engine.setWorld(world);

// 创建物理系统（启用连续碰撞检测）
const physicsSystem = new PhysicsSystem({
  gravity: { x: 0, y: -9.81, z: 0 },
  debug: true,
  enableCCD: true,
  ccdOptions: {
    maxSubSteps: 5,
    minSubStepTime: 1 / 240,
    velocityThreshold: 5,
    enableForAll: false
  }
});
world.addSystem(physicsSystem);

// 创建输入系统
const inputSystem = new InputSystem();
world.addSystem(inputSystem);

// 创建渲染系统
const renderer = new Renderer({
  width: window.innerWidth,
  height: window.innerHeight,
  antialias: true
});
document.body.appendChild(renderer.getDomElement());

// 创建场景
const scene = new Scene();
world.addEntity(scene);

// 创建相机
const camera = new Camera({
  type: 'perspective',
  fov: 75,
  aspect: window.innerWidth / window.innerHeight,
  near: 0.1,
  far: 1000
});
camera.getTransform().setPosition(0, 5, 20);
camera.getTransform().lookAt(new THREE.Vector3(0, 0, 0));
world.addEntity(camera);

// 创建地面
const ground = new Entity('ground');
ground.addComponent(new Transform());
ground.addComponent(new PhysicsBody({
  type: 'static',
  mass: 0
}));
ground.addComponent(new PhysicsCollider({
  type: 'box',
  params: {
    halfExtents: { x: 50, y: 0.5, z: 50 }
  }
}));
world.addEntity(ground);

// 创建墙壁
const createWall = (position: THREE.Vector3, rotation: THREE.Vector3, size: THREE.Vector3) => {
  const wall = new Entity(`wall_${position.x}_${position.y}_${position.z}`);
  const transform = new Transform();
  transform.setPosition(position.x, position.y, position.z);
  transform.setRotation(rotation.x, rotation.y, rotation.z);
  wall.addComponent(transform);
  wall.addComponent(new PhysicsBody({
    type: 'static',
    mass: 0
  }));
  wall.addComponent(new PhysicsCollider({
    type: 'box',
    params: {
      halfExtents: { x: size.x / 2, y: size.y / 2, z: size.z / 2 }
    }
  }));
  world.addEntity(wall);
  return wall;
};

// 创建四面墙
createWall(new THREE.Vector3(0, 5, -10), new THREE.Vector3(0, 0, 0), new THREE.Vector3(20, 10, 1));
createWall(new THREE.Vector3(0, 5, 10), new THREE.Vector3(0, 0, 0), new THREE.Vector3(20, 10, 1));
createWall(new THREE.Vector3(-10, 5, 0), new THREE.Vector3(0, Math.PI / 2, 0), new THREE.Vector3(20, 10, 1));
createWall(new THREE.Vector3(10, 5, 0), new THREE.Vector3(0, Math.PI / 2, 0), new THREE.Vector3(20, 10, 1));

// 创建一些障碍物
for (let i = 0; i < 5; i++) {
  const obstacle = new Entity(`obstacle_${i}`);
  const transform = new Transform();
  transform.setPosition(
    Math.random() * 16 - 8,
    1 + i * 2,
    Math.random() * 16 - 8
  );
  obstacle.addComponent(transform);
  obstacle.addComponent(new PhysicsBody({
    type: 'static',
    mass: 0
  }));
  obstacle.addComponent(new PhysicsCollider({
    type: 'box',
    params: {
      halfExtents: { x: 1, y: 1, z: 1 }
    }
  }));
  world.addEntity(obstacle);
}

// 创建一个高速球体（启用CCD）
const fastBall = new Entity('fast_ball');
const fastBallTransform = new Transform();
fastBallTransform.setPosition(0, 10, 0);
fastBall.addComponent(fastBallTransform);
fastBall.addComponent(new PhysicsBody({
  type: 'dynamic',
  mass: 1,
  linearDamping: 0.1,
  angularDamping: 0.1
}));
fastBall.addComponent(new PhysicsCollider({
  type: 'sphere',
  params: {
    radius: 0.5
  }
}));
world.addEntity(fastBall);

// 启用球体的CCD
physicsSystem.enableEntityCCD(fastBall);

// 创建一个普通球体（不启用CCD）
const normalBall = new Entity('normal_ball');
const normalBallTransform = new Transform();
normalBallTransform.setPosition(3, 10, 0);
normalBall.addComponent(normalBallTransform);
normalBall.addComponent(new PhysicsBody({
  type: 'dynamic',
  mass: 1,
  linearDamping: 0.1,
  angularDamping: 0.1
}));
normalBall.addComponent(new PhysicsCollider({
  type: 'sphere',
  params: {
    radius: 0.5
  }
}));
world.addEntity(normalBall);

// 球体的初始速度
let initialImpulseApplied = false;

// 更新函数
engine.on('update', (deltaTime: number) => {
  // 应用初始冲量
  if (!initialImpulseApplied) {
    // 获取物理体
    const fastBallBody = fastBall.getComponent<PhysicsBody>(PhysicsBody.type);
    const normalBallBody = normalBall.getComponent<PhysicsBody>(PhysicsBody.type);
    
    if (fastBallBody && normalBallBody) {
      const fastCannonBody = fastBallBody.getCannonBody();
      const normalCannonBody = normalBallBody.getCannonBody();
      
      if (fastCannonBody && normalCannonBody) {
        // 应用高速冲量
        const impulse = new CANNON.Vec3(0, -5, -50); // 非常高的速度
        fastCannonBody.applyImpulse(impulse);
        normalCannonBody.applyImpulse(impulse);
        
        initialImpulseApplied = true;
        
        console.log('应用初始冲量');
        console.log('启用CCD的球体：', physicsSystem.isEntityCCDEnabled(fastBall));
        console.log('普通球体：', physicsSystem.isEntityCCDEnabled(normalBall));
      }
    }
  }
  
  // 如果球体掉出场景，重置位置
  const fastBallPos = fastBallTransform.getPosition();
  const normalBallPos = normalBallTransform.getPosition();
  
  if (fastBallPos.y < -10) {
    fastBallTransform.setPosition(0, 10, 0);
    const fastBallBody = fastBall.getComponent<PhysicsBody>(PhysicsBody.type);
    if (fastBallBody) {
      const cannonBody = fastBallBody.getCannonBody();
      if (cannonBody) {
        cannonBody.velocity.set(0, 0, 0);
        cannonBody.angularVelocity.set(0, 0, 0);
      }
    }
  }
  
  if (normalBallPos.y < -10) {
    normalBallTransform.setPosition(3, 10, 0);
    const normalBallBody = normalBall.getComponent<PhysicsBody>(PhysicsBody.type);
    if (normalBallBody) {
      const cannonBody = normalBallBody.getCannonBody();
      if (cannonBody) {
        cannonBody.velocity.set(0, 0, 0);
        cannonBody.angularVelocity.set(0, 0, 0);
      }
    }
    
    // 重新应用冲量
    initialImpulseApplied = false;
  }
  
  // 更新相机位置
  camera.getTransform().lookAt(new THREE.Vector3(0, 0, 0));
});

// 启动引擎
engine.start();
