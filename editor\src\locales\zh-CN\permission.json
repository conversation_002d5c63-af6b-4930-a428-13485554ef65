{"title": "权限管理", "description": "管理用户权限和角色", "userPermissions": "用户权限", "rolePermissions": "角色权限", "permissionName": "权限名称", "category": "分类", "status": "状态", "selectUser": "选择用户", "selectPermission": "选择权限", "granted": "已授权", "denied": "已拒绝", "inherited": "继承的", "custom": "自定义的", "enabled": "已启用", "disabled": "已禁用", "actions": {"addPermission": "添加权限", "removePermission": "移除权限", "setRole": "设置角色", "saveChanges": "保存更改", "resetToDefault": "重置为默认值", "applyToAll": "应用到所有用户"}, "roles": {"title": "角色", "owner": "所有者", "admin": "管理员", "editor": "编辑者", "viewer": "查看者", "description": {"owner": "拥有所有权限，包括项目管理和删除", "admin": "拥有大部分权限，包括用户管理和角色分配", "editor": "可以编辑场景、创建和修改实体", "viewer": "只能查看场景，无法编辑"}}, "types": {"view_scene": "查看场景", "edit_scene": "编辑场景", "create_entity": "创建实体", "update_entity": "更新实体", "delete_entity": "删除实体", "add_component": "添加组件", "update_component": "更新组件", "remove_component": "移除组件", "upload_asset": "上传资源", "delete_asset": "删除资源", "save_scene": "保存场景", "export_scene": "导出场景", "import_scene": "导入场景", "manage_users": "管理用户", "assign_roles": "分配角色", "manage_permissions": "管理权限", "manage_project": "管理项目", "delete_project": "删除项目"}, "categories": {"basic": "基本权限", "entity": "实体操作", "component": "组件操作", "asset": "资源操作", "scene": "场景操作", "user": "用户管理", "project": "项目管理"}, "errors": {"notLoggedIn": "您尚未登录", "noPermission": "您没有 {permission} 权限", "cannotChangeOwner": "无法更改项目所有者的角色", "cannotRemoveOwner": "必须至少有一个项目所有者", "saveFailed": "保存权限失败", "loadFailed": "加载权限失败"}, "success": {"roleChanged": "角色已更改", "permissionAdded": "权限已添加", "permissionRemoved": "权限已移除", "settingsSaved": "设置已保存", "settingsReset": "设置已重置为默认值"}, "confirmations": {"resetPermissions": "确定要重置所有权限设置为默认值吗？这将影响所有用户。", "removePermission": "确定要移除此权限吗？", "changeRole": "确定要更改用户角色吗？这将重置其自定义权限。"}, "tooltips": {"inheritedPermission": "此权限继承自用户角色", "customPermission": "此权限是用户自定义的", "overriddenPermission": "此权限覆盖了角色默认权限", "roleInfo": "查看角色详细信息", "permissionInfo": "查看权限详细信息"}, "settings": {"title": "权限设置", "enablePermissionCheck": "启用权限检查", "showPermissionErrors": "显示权限错误消息", "permissionInheritance": "启用权限继承", "permissionOverride": "允许权限覆盖", "defaultRole": "默认角色"}, "log": {"title": "权限日志", "time": "时间", "type": "类型", "user": "操作用户", "target": "目标用户", "details": "详情", "permission": "权限", "role": "角色", "fullDetails": "完整详情", "clearLogs": "清空日志", "confirmClear": "确定要清空所有权限日志吗？", "noLogs": "暂无权限日志", "search": "搜索日志", "filterUser": "按用户筛选", "filterType": "按类型筛选", "detailTitle": "日志详情", "enableLogging": "日志记录", "none": "无", "roleChangedDetail": "角色从 {oldRole} 变更为 {role}", "permissionGrantedDetail": "授予权限 {permission}", "permissionRevokedDetail": "撤销权限 {permission}", "rolePermissionsChangedDetail": "修改角色 {role} 的权限 ({count} 个)", "permissionCheckFailedDetail": "权限检查失败: {permission}"}, "logTypes": {"role_changed": "角色变更", "permission_granted": "授予权限", "permission_revoked": "撤销权限", "role_permissions_changed": "角色权限变更", "permission_check_failed": "权限检查失败", "permission_check_enabled": "启用权限检查", "permission_check_disabled": "禁用权限检查"}}