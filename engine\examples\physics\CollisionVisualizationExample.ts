/**
 * 碰撞可视化示例
 * 展示如何使用增强型物理调试器可视化碰撞点、碰撞法线和碰撞力
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Vector3 } from '../../src/math/Vector3';
import { Quaternion } from '../../src/math/Quaternion';
import { 
  PhysicsSystem, 
  PhysicsBodyComponent, 
  PhysicsColliderComponent,
  BodyType,
  ColliderType,
  PhysicsMaterialFactory
} from '../../src/physics';
import { EnhancedPhysicsDebugger } from '../../src/physics/debug/EnhancedPhysicsDebugger';

/**
 * 碰撞可视化示例
 */
export class CollisionVisualizationExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  
  /** 增强型物理调试器 */
  private physicsDebugger: EnhancedPhysicsDebugger;
  
  /** 地面实体 */
  private ground: Entity;
  
  /** 盒子实体列表 */
  private boxes: Entity[] = [];
  
  /** 球体实体列表 */
  private spheres: Entity[] = [];
  
  /** 是否已初始化 */
  private initialized: boolean = false;
  
  /** 调试选项 */
  private debugOptions = {
    showBodies: true,
    showConstraints: true,
    showContactPoints: true,
    showAABBs: false,
    showVelocities: true,
    showForces: true,
    showCenterOfMass: true,
    showSleepState: true,
    showPerformanceStats: true,
    showContactNormals: true,
    showContactForces: true,
    showFrictionForces: true
  };

  /**
   * 创建碰撞可视化示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建场景
    this.scene = new Scene('碰撞可视化示例场景');
    
    // 创建物理系统
    this.physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.82, z: 0 },
      debug: true,
      useEnhancedDebugger: true,
      debuggerOptions: {
        showBodies: this.debugOptions.showBodies,
        showConstraints: this.debugOptions.showConstraints,
        showContactPoints: this.debugOptions.showContactPoints,
        showAABBs: this.debugOptions.showAABBs,
        showVelocities: this.debugOptions.showVelocities,
        showForces: this.debugOptions.showForces,
        showCenterOfMass: this.debugOptions.showCenterOfMass,
        showSleepState: this.debugOptions.showSleepState,
        showPerformanceStats: this.debugOptions.showPerformanceStats,
        showContactNormals: this.debugOptions.showContactNormals,
        showContactForces: this.debugOptions.showContactForces,
        showFrictionForces: this.debugOptions.showFrictionForces,
        bodyColor: 0x3333ff,
        contactPointColor: 0xff3333,
        contactNormalColor: 0x00ff00,
        contactForceColor: 0xff0000,
        frictionForceColor: 0xffa500,
        vectorScale: 0.2,
        contactForceScale: 0.01
      }
    });
    
    // 添加物理系统到引擎
    this.engine.addSystem(this.physicsSystem);
    
    // 获取增强型物理调试器
    this.physicsDebugger = this.physicsSystem.getDebugger() as EnhancedPhysicsDebugger;
    
    // 初始化物理材质工厂
    PhysicsMaterialFactory.initialize();
    
    // 创建地面
    this.ground = this.createGround();
    
    // 添加地面到场景
    this.scene.addEntity(this.ground);
  }

  /**
   * 初始化示例
   */
  public initialize(): void {
    if (this.initialized) return;
    
    // 创建一些物理对象
    this.createBoxes();
    this.createSpheres();
    this.createRamp();
    
    // 设置场景为活跃场景
    this.engine.getWorld().setActiveScene(this.scene);
    
    this.initialized = true;
  }

  /**
   * 创建地面
   * @returns 地面实体
   */
  private createGround(): Entity {
    // 创建地面实体
    const ground = new Entity('ground');
    
    // 添加变换组件
    const transform = ground.getTransform();
    transform.setPosition(0, -0.5, 0);
    transform.setScale(20, 1, 20);
    
    // 创建地面网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ color: 0x808080 });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    ground.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    ground.addComponent(new PhysicsBodyComponent({
      type: BodyType.STATIC,
      mass: 0
    }));
    
    // 添加碰撞器组件
    ground.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: 10, y: 0.5, z: 10 }
      }
    }));
    
    return ground;
  }
  
  /**
   * 创建斜坡
   * @returns 斜坡实体
   */
  private createRamp(): Entity {
    // 创建斜坡实体
    const ramp = new Entity('ramp');
    
    // 添加变换组件
    const transform = ramp.getTransform();
    transform.setPosition(0, 1, -5);
    transform.setRotation(Math.PI / 10, 0, 0);
    transform.setScale(8, 0.5, 6);
    
    // 创建斜坡网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ color: 0x8080a0 });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    ramp.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    ramp.addComponent(new PhysicsBodyComponent({
      type: BodyType.STATIC,
      mass: 0
    }));
    
    // 添加碰撞器组件
    ramp.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: 4, y: 0.25, z: 3 }
      }
    }));
    
    // 添加到场景
    this.scene.addEntity(ramp);
    
    return ramp;
  }

  /**
   * 创建盒子
   */
  private createBoxes(): void {
    // 创建多个盒子
    for (let i = 0; i < 5; i++) {
      const box = this.createBox(
        Math.random() * 6 - 3,
        5 + i * 1.2,
        Math.random() * 6 - 3,
        Math.random() * 0.5 + 0.5
      );
      
      this.boxes.push(box);
      this.scene.addEntity(box);
    }
  }

  /**
   * 创建单个盒子
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   * @param size 盒子大小
   * @returns 盒子实体
   */
  private createBox(x: number, y: number, z: number, size: number): Entity {
    // 创建盒子实体
    const box = new Entity(`box_${this.boxes.length}`);
    
    // 添加变换组件
    const transform = box.getTransform();
    transform.setPosition(x, y, z);
    transform.setRotation(
      Math.random() * Math.PI,
      Math.random() * Math.PI,
      Math.random() * Math.PI
    );
    transform.setScale(size, size, size);
    
    // 创建盒子网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ 
      color: new THREE.Color(Math.random(), Math.random(), Math.random()) 
    });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    box.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    box.addComponent(new PhysicsBodyComponent({
      type: BodyType.DYNAMIC,
      mass: size * 2,
      material: PhysicsMaterialFactory.getMaterial('wood')
    }));
    
    // 添加碰撞器组件
    box.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: size / 2, y: size / 2, z: size / 2 }
      }
    }));
    
    return box;
  }

  /**
   * 创建球体
   */
  private createSpheres(): void {
    // 创建多个球体
    for (let i = 0; i < 5; i++) {
      const sphere = this.createSphere(
        Math.random() * 6 - 3,
        8 + i * 1.2,
        Math.random() * 6 - 3,
        Math.random() * 0.3 + 0.3
      );
      
      this.spheres.push(sphere);
      this.scene.addEntity(sphere);
    }
  }

  /**
   * 创建单个球体
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   * @param radius 球体半径
   * @returns 球体实体
   */
  private createSphere(x: number, y: number, z: number, radius: number): Entity {
    // 创建球体实体
    const sphere = new Entity(`sphere_${this.spheres.length}`);
    
    // 添加变换组件
    const transform = sphere.getTransform();
    transform.setPosition(x, y, z);
    transform.setScale(radius * 2, radius * 2, radius * 2);
    
    // 创建球体网格
    const geometry = new THREE.SphereGeometry(0.5, 16, 16);
    const material = new THREE.MeshStandardMaterial({ 
      color: new THREE.Color(Math.random(), Math.random(), Math.random()) 
    });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    sphere.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    sphere.addComponent(new PhysicsBodyComponent({
      type: BodyType.DYNAMIC,
      mass: radius * 4,
      material: PhysicsMaterialFactory.getMaterial('rubber')
    }));
    
    // 添加碰撞器组件
    sphere.addComponent(new PhysicsColliderComponent({
      type: ColliderType.SPHERE,
      params: {
        radius: radius
      }
    }));
    
    return sphere;
  }

  /**
   * 添加力到随机物体
   */
  public addRandomForce(): void {
    // 随机选择一个物体
    const entities = [...this.boxes, ...this.spheres];
    const entity = entities[Math.floor(Math.random() * entities.length)];
    
    // 获取物理体组件
    const physicsBody = entity.getComponent(PhysicsBodyComponent);
    if (!physicsBody) return;
    
    // 添加随机力
    const force = new Vector3(
      (Math.random() - 0.5) * 20,
      Math.random() * 15,
      (Math.random() - 0.5) * 20
    );
    
    physicsBody.applyImpulse(force);
  }

  /**
   * 重置场景
   */
  public reset(): void {
    // 移除所有盒子和球体
    for (const box of this.boxes) {
      this.scene.removeEntity(box);
    }
    
    for (const sphere of this.spheres) {
      this.scene.removeEntity(sphere);
    }
    
    this.boxes = [];
    this.spheres = [];
    
    // 重新创建物体
    this.createBoxes();
    this.createSpheres();
  }
  
  /**
   * 切换调试选项
   * @param option 选项名称
   */
  public toggleDebugOption(option: string): void {
    if (option in this.debugOptions) {
      // 切换选项
      this.debugOptions[option] = !this.debugOptions[option];
      
      // 更新调试器
      switch (option) {
        case 'showBodies':
          this.physicsDebugger.setShowBodies(this.debugOptions.showBodies);
          break;
        case 'showConstraints':
          this.physicsDebugger.setShowConstraints(this.debugOptions.showConstraints);
          break;
        case 'showContactPoints':
          this.physicsDebugger.setShowContactPoints(this.debugOptions.showContactPoints);
          break;
        case 'showAABBs':
          this.physicsDebugger.setShowAABBs(this.debugOptions.showAABBs);
          break;
        case 'showVelocities':
          this.physicsDebugger.setShowVelocities(this.debugOptions.showVelocities);
          break;
        case 'showForces':
          this.physicsDebugger.setShowForces(this.debugOptions.showForces);
          break;
        case 'showCenterOfMass':
          this.physicsDebugger.setShowCenterOfMass(this.debugOptions.showCenterOfMass);
          break;
        case 'showSleepState':
          this.physicsDebugger.setShowSleepState(this.debugOptions.showSleepState);
          break;
        case 'showPerformanceStats':
          this.physicsDebugger.setShowPerformanceStats(this.debugOptions.showPerformanceStats);
          break;
        case 'showContactNormals':
          this.physicsDebugger.setShowContactNormals(this.debugOptions.showContactNormals);
          break;
        case 'showContactForces':
          this.physicsDebugger.setShowContactForces(this.debugOptions.showContactForces);
          break;
        case 'showFrictionForces':
          this.physicsDebugger.setShowFrictionForces(this.debugOptions.showFrictionForces);
          break;
      }
    }
  }
  
  /**
   * 获取调试选项
   * @returns 调试选项
   */
  public getDebugOptions(): any {
    return { ...this.debugOptions };
  }
}
