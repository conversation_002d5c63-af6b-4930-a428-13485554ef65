/**
 * 辅助功能合规性测试报告生成器
 * 用于生成WCAG 2.1 AA标准合规性测试报告
 */
import { TestReport, TestResult, TestResultType } from './AccessibilityComplianceTester';

/**
 * 报告格式
 */
export enum ReportFormat {
  HTML = 'html',
  JSON = 'json',
  CSV = 'csv',
  PDF = 'pdf',
  MARKDOWN = 'markdown'
}

/**
 * 报告选项
 */
export interface ReportOptions {
  /** 报告格式 */
  format?: ReportFormat;
  /** 报告标题 */
  title?: string;
  /** 是否包含详细信息 */
  includeDetails?: boolean;
  /** 是否包含修复建议 */
  includeFixSuggestions?: boolean;
  /** 是否包含元素信息 */
  includeElements?: boolean;
  /** 是否包含图表 */
  includeCharts?: boolean;
  /** 是否包含WCAG标准信息 */
  includeWcagInfo?: boolean;
  /** 是否包含合规性得分 */
  includeComplianceScore?: boolean;
  /** 是否包含测试时间 */
  includeTimestamp?: boolean;
  /** 是否包含测试摘要 */
  includeSummary?: boolean;
}

/**
 * 辅助功能合规性测试报告生成器
 */
export class AccessibilityComplianceReporter {
  private static instance: AccessibilityComplianceReporter;

  /**
   * 获取单例实例
   * @returns 辅助功能合规性测试报告生成器实例
   */
  public static getInstance(): AccessibilityComplianceReporter {
    if (!AccessibilityComplianceReporter.instance) {
      AccessibilityComplianceReporter.instance = new AccessibilityComplianceReporter();
    }
    return AccessibilityComplianceReporter.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {}

  /**
   * 生成报告
   * @param report 测试报告
   * @param options 报告选项
   * @returns 报告内容
   */
  public generateReport(report: TestReport, options: ReportOptions = {}): string {
    const {
      format = ReportFormat.HTML,
      title = '辅助功能合规性测试报告',
      includeDetails = true,
      includeFixSuggestions = true,
      includeElements = false,
      includeCharts = true,
      includeWcagInfo = true,
      includeComplianceScore = true,
      includeTimestamp = true,
      includeSummary = true
    } = options;

    switch (format) {
      case ReportFormat.HTML:
        return this.generateHtmlReport(report, {
          title,
          includeDetails,
          includeFixSuggestions,
          includeElements,
          includeCharts,
          includeWcagInfo,
          includeComplianceScore,
          includeTimestamp,
          includeSummary
        });
      case ReportFormat.JSON:
        return this.generateJsonReport(report);
      case ReportFormat.CSV:
        return this.generateCsvReport(report, {
          includeDetails,
          includeFixSuggestions
        });
      case ReportFormat.MARKDOWN:
        return this.generateMarkdownReport(report, {
          title,
          includeDetails,
          includeFixSuggestions,
          includeWcagInfo,
          includeComplianceScore,
          includeTimestamp,
          includeSummary
        });
      case ReportFormat.PDF:
        throw new Error('PDF格式报告生成需要额外的库支持，请使用HTML格式然后转换为PDF');
      default:
        throw new Error(`不支持的报告格式: ${format}`);
    }
  }

  /**
   * 生成HTML报告
   * @param report 测试报告
   * @param options 报告选项
   * @returns HTML报告
   */
  private generateHtmlReport(report: TestReport, options: ReportOptions): string {
    const {
      title,
      includeDetails,
      includeFixSuggestions,
      includeElements,
      includeCharts,
      includeWcagInfo,
      includeComplianceScore,
      includeTimestamp,
      includeSummary
    } = options;

    // 生成HTML报告头部
    let html = `
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
          }
          h1, h2, h3 {
            color: #2c3e50;
          }
          .report-header {
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
          }
          .summary {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            margin-bottom: 30px;
          }
          .summary-item {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin: 10px;
            flex: 1;
            min-width: 200px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          }
          .summary-item h3 {
            margin-top: 0;
          }
          .test-results {
            margin-bottom: 30px;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
          }
          th {
            background-color: #f8f9fa;
          }
          .pass {
            color: #28a745;
          }
          .fail {
            color: #dc3545;
          }
          .warning {
            color: #ffc107;
          }
          .not-applicable {
            color: #6c757d;
          }
          .chart-container {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            margin-bottom: 30px;
          }
          .chart {
            width: 45%;
            min-width: 300px;
            height: 300px;
            margin: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          }
          .details {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
          }
          .wcag-info {
            margin-top: 10px;
            font-size: 0.9em;
            color: #6c757d;
          }
          .fix-suggestion {
            margin-top: 10px;
            padding: 10px;
            background: #e9ecef;
            border-left: 3px solid #ffc107;
          }
          .compliance-score {
            font-size: 2em;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
          }
          .score-container {
            width: 150px;
            height: 150px;
            margin: 0 auto;
            position: relative;
            border-radius: 50%;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          }
          @media print {
            body {
              font-size: 12pt;
            }
            .chart {
              break-inside: avoid;
            }
          }
        </style>
      </head>
      <body>
        <div class="report-header">
          <h1>${title}</h1>
          ${includeTimestamp ? `<p>测试时间: ${new Date(report.timestamp).toLocaleString()}</p>` : ''}
        </div>
    `;

    // 生成摘要部分
    if (includeSummary) {
      html += `
        <div class="summary">
          <div class="summary-item">
            <h3>测试结果摘要</h3>
            <p>通过: <span class="pass">${report.passCount}</span></p>
            <p>失败: <span class="fail">${report.failCount}</span></p>
            <p>警告: <span class="warning">${report.warningCount}</span></p>
            <p>不适用: <span class="not-applicable">${report.notApplicableCount}</span></p>
            <p>总计: ${report.results.length}</p>
          </div>
      `;

      if (includeComplianceScore) {
        const scoreColor = report.complianceScore >= 90 ? '#28a745' : (report.complianceScore >= 70 ? '#ffc107' : '#dc3545');
        html += `
          <div class="summary-item">
            <h3>合规性得分</h3>
            <div class="score-container" style="border: 10px solid ${scoreColor}">
              <div class="compliance-score">${report.complianceScore}%</div>
            </div>
          </div>
        `;
      }

      html += `</div>`;
    }

    // 生成图表部分
    if (includeCharts) {
      html += `
        <h2>测试结果图表</h2>
        <div class="chart-container">
          <div class="chart" id="results-chart">
            <!-- 这里可以使用Chart.js等库生成图表 -->
            <p style="text-align: center; padding-top: 120px;">结果分布图表</p>
          </div>
          <div class="chart" id="wcag-chart">
            <!-- 这里可以使用Chart.js等库生成图表 -->
            <p style="text-align: center; padding-top: 120px;">WCAG标准覆盖图表</p>
          </div>
        </div>
      `;
    }

    // 生成测试结果表格
    html += `
      <h2>测试结果详情</h2>
      <div class="test-results">
        <table>
          <thead>
            <tr>
              <th>测试ID</th>
              <th>测试名称</th>
              <th>结果</th>
              ${includeWcagInfo ? '<th>WCAG标准</th>' : ''}
              <th>描述</th>
            </tr>
          </thead>
          <tbody>
    `;

    // 添加每个测试结果
    report.results.forEach(result => {
      const resultClass = this.getResultClass(result.resultType);
      const resultText = this.getResultText(result.resultType);

      html += `
        <tr>
          <td>${result.id}</td>
          <td>${result.name}</td>
          <td class="${resultClass}">${resultText}</td>
          ${includeWcagInfo ? `<td>${result.wcagStandard}</td>` : ''}
          <td>${result.description}</td>
        </tr>
      `;

      // 添加详细信息
      if (includeDetails && (result.resultType === TestResultType.FAIL || result.resultType === TestResultType.WARNING)) {
        html += `
          <tr>
            <td colspan="${includeWcagInfo ? '5' : '4'}">
              <div class="details">
                ${includeFixSuggestions && result.fixSuggestion ? `
                  <div class="fix-suggestion">
                    <strong>修复建议:</strong> ${result.fixSuggestion}
                  </div>
                ` : ''}
                
                ${includeElements && result.elements && result.elements.length > 0 ? `
                  <div class="elements">
                    <strong>相关元素:</strong>
                    <ul>
                      ${result.elements.map(element => `<li>${this.elementToString(element)}</li>`).join('')}
                    </ul>
                  </div>
                ` : ''}
              </div>
            </td>
          </tr>
        `;
      }
    });

    html += `
          </tbody>
        </table>
      </div>
    `;

    // 添加WCAG标准信息
    if (includeWcagInfo) {
      html += `
        <h2>WCAG 2.1 AA标准参考</h2>
        <div class="wcag-reference">
          <p>Web内容无障碍指南(WCAG) 2.1定义了使Web内容对残障人士更易于访问的方法。以下是本测试涵盖的主要标准：</p>
          <ul>
            <li><strong>1.1.1 非文本内容</strong>: 为所有非文本内容提供替代文本</li>
            <li><strong>1.3.1 信息和关系</strong>: 以编程方式确定信息、结构和关系</li>
            <li><strong>1.4.3 对比度</strong>: 文本和图像的视觉呈现具有至少4.5:1的对比度</li>
            <li><strong>1.4.4 调整文本大小</strong>: 文本可以调整至200%而不丢失内容或功能</li>
            <li><strong>1.4.10 重排</strong>: 内容可以在不需要水平滚动的情况下呈现</li>
            <li><strong>1.4.11 非文本对比度</strong>: 用户界面组件和图形对象的视觉呈现具有至少3:1的对比度</li>
            <li><strong>2.1.1 键盘</strong>: 所有功能可通过键盘操作</li>
            <li><strong>2.4.3 焦点顺序</strong>: 可聚焦组件的焦点顺序有意义</li>
            <li><strong>2.4.7 焦点可见</strong>: 键盘焦点指示器可见</li>
            <li><strong>2.5.3 标签名称</strong>: 具有标签的用户界面组件，其名称包含可见文本</li>
            <li><strong>3.3.1 错误识别</strong>: 输入错误自动检测并向用户描述</li>
            <li><strong>4.1.2 名称、角色、值</strong>: 所有用户界面组件的名称和角色可以以编程方式确定</li>
          </ul>
          <p>完整的WCAG 2.1标准可在<a href="https://www.w3.org/TR/WCAG21/" target="_blank">W3C网站</a>上查看。</p>
        </div>
      `;
    }

    // 结束HTML
    html += `
      </body>
      </html>
    `;

    return html;
  }

  /**
   * 生成JSON报告
   * @param report 测试报告
   * @returns JSON报告
   */
  private generateJsonReport(report: TestReport): string {
    return JSON.stringify(report, null, 2);
  }

  /**
   * 生成CSV报告
   * @param report 测试报告
   * @param options 报告选项
   * @returns CSV报告
   */
  private generateCsvReport(report: TestReport, options: ReportOptions): string {
    const { includeDetails, includeFixSuggestions } = options;
    
    // 生成CSV头
    let csv = 'ID,Name,Result,WCAG Standard,Description';
    if (includeFixSuggestions) {
      csv += ',Fix Suggestion';
    }
    csv += '\n';
    
    // 添加每个测试结果
    report.results.forEach(result => {
      const resultText = this.getResultText(result.resultType);
      
      // 转义CSV字段中的逗号和引号
      const escapeCsv = (field: string) => {
        if (field && (field.includes(',') || field.includes('"') || field.includes('\n'))) {
          return `"${field.replace(/"/g, '""')}"`;
        }
        return field || '';
      };
      
      csv += `${escapeCsv(result.id)},${escapeCsv(result.name)},${escapeCsv(resultText)},${escapeCsv(result.wcagStandard)},${escapeCsv(result.description)}`;
      
      if (includeFixSuggestions) {
        csv += `,${escapeCsv(result.fixSuggestion || '')}`;
      }
      
      csv += '\n';
    });
    
    return csv;
  }

  /**
   * 生成Markdown报告
   * @param report 测试报告
   * @param options 报告选项
   * @returns Markdown报告
   */
  private generateMarkdownReport(report: TestReport, options: ReportOptions): string {
    const {
      title,
      includeDetails,
      includeFixSuggestions,
      includeWcagInfo,
      includeComplianceScore,
      includeTimestamp,
      includeSummary
    } = options;
    
    let markdown = `# ${title}\n\n`;
    
    if (includeTimestamp) {
      markdown += `测试时间: ${new Date(report.timestamp).toLocaleString()}\n\n`;
    }
    
    if (includeSummary) {
      markdown += `## 测试结果摘要\n\n`;
      markdown += `- 通过: ${report.passCount}\n`;
      markdown += `- 失败: ${report.failCount}\n`;
      markdown += `- 警告: ${report.warningCount}\n`;
      markdown += `- 不适用: ${report.notApplicableCount}\n`;
      markdown += `- 总计: ${report.results.length}\n\n`;
      
      if (includeComplianceScore) {
        markdown += `## 合规性得分\n\n`;
        markdown += `**${report.complianceScore}%**\n\n`;
      }
    }
    
    markdown += `## 测试结果详情\n\n`;
    
    // 表格头
    markdown += `| ID | 测试名称 | 结果 | ${includeWcagInfo ? 'WCAG标准 | ' : ''}描述 |\n`;
    markdown += `| --- | --- | --- | ${includeWcagInfo ? '--- | ' : ''}--- |\n`;
    
    // 表格内容
    report.results.forEach(result => {
      const resultText = this.getResultText(result.resultType);
      
      markdown += `| ${result.id} | ${result.name} | ${resultText} | ${includeWcagInfo ? `${result.wcagStandard} | ` : ''}${result.description} |\n`;
      
      // 添加详细信息
      if (includeDetails && (result.resultType === TestResultType.FAIL || result.resultType === TestResultType.WARNING)) {
        if (includeFixSuggestions && result.fixSuggestion) {
          markdown += `\n**修复建议:** ${result.fixSuggestion}\n\n`;
        }
      }
    });
    
    // 添加WCAG标准信息
    if (includeWcagInfo) {
      markdown += `\n## WCAG 2.1 AA标准参考\n\n`;
      markdown += `Web内容无障碍指南(WCAG) 2.1定义了使Web内容对残障人士更易于访问的方法。以下是本测试涵盖的主要标准：\n\n`;
      markdown += `- **1.1.1 非文本内容**: 为所有非文本内容提供替代文本\n`;
      markdown += `- **1.3.1 信息和关系**: 以编程方式确定信息、结构和关系\n`;
      markdown += `- **1.4.3 对比度**: 文本和图像的视觉呈现具有至少4.5:1的对比度\n`;
      markdown += `- **1.4.4 调整文本大小**: 文本可以调整至200%而不丢失内容或功能\n`;
      markdown += `- **1.4.10 重排**: 内容可以在不需要水平滚动的情况下呈现\n`;
      markdown += `- **1.4.11 非文本对比度**: 用户界面组件和图形对象的视觉呈现具有至少3:1的对比度\n`;
      markdown += `- **2.1.1 键盘**: 所有功能可通过键盘操作\n`;
      markdown += `- **2.4.3 焦点顺序**: 可聚焦组件的焦点顺序有意义\n`;
      markdown += `- **2.4.7 焦点可见**: 键盘焦点指示器可见\n`;
      markdown += `- **2.5.3 标签名称**: 具有标签的用户界面组件，其名称包含可见文本\n`;
      markdown += `- **3.3.1 错误识别**: 输入错误自动检测并向用户描述\n`;
      markdown += `- **4.1.2 名称、角色、值**: 所有用户界面组件的名称和角色可以以编程方式确定\n\n`;
      markdown += `完整的WCAG 2.1标准可在[W3C网站](https://www.w3.org/TR/WCAG21/)上查看。\n`;
    }
    
    return markdown;
  }

  /**
   * 获取结果类名
   * @param resultType 结果类型
   * @returns 结果类名
   */
  private getResultClass(resultType: TestResultType): string {
    switch (resultType) {
      case TestResultType.PASS:
        return 'pass';
      case TestResultType.FAIL:
        return 'fail';
      case TestResultType.WARNING:
        return 'warning';
      case TestResultType.NOT_APPLICABLE:
        return 'not-applicable';
      default:
        return '';
    }
  }

  /**
   * 获取结果文本
   * @param resultType 结果类型
   * @returns 结果文本
   */
  private getResultText(resultType: TestResultType): string {
    switch (resultType) {
      case TestResultType.PASS:
        return '通过';
      case TestResultType.FAIL:
        return '失败';
      case TestResultType.WARNING:
        return '警告';
      case TestResultType.NOT_APPLICABLE:
        return '不适用';
      default:
        return '';
    }
  }

  /**
   * 元素转字符串
   * @param element HTML元素
   * @returns 元素字符串表示
   */
  private elementToString(element: HTMLElement): string {
    const tagName = element.tagName.toLowerCase();
    const id = element.id ? `#${element.id}` : '';
    const className = element.className ? `.${element.className.replace(/\s+/g, '.')}` : '';
    
    return `${tagName}${id}${className}`;
  }
}

export default AccessibilityComplianceReporter.getInstance();
