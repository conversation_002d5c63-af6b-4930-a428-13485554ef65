.video-tutorial-player {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #000;
  position: relative;
  
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    
    .video-container {
      height: 100%;
    }
    
    .tutorial-info {
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      width: 300px;
      background-color: rgba(255, 255, 255, 0.9);
      z-index: 1;
      overflow-y: auto;
      transition: transform 0.3s ease;
      
      &.collapsed {
        transform: translateX(300px);
      }
    }
  }
  
  .video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; // 16:9 aspect ratio
    background-color: #000;
    
    .video-element {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    
    .video-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.3);
      cursor: pointer;
      
      .play-button {
        font-size: 64px;
        color: white;
        opacity: 0.8;
        transition: opacity 0.2s ease;
        
        &:hover {
          opacity: 1;
        }
      }
    }
    
    .chapter-indicator {
      position: absolute;
      top: 20px;
      left: 20px;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      max-width: 80%;
      
      .ant-typography {
        color: white;
        margin: 0;
      }
    }
    
    .video-controls {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
      padding: 10px;
      opacity: 0;
      transition: opacity 0.3s ease;
      
      &.visible {
        opacity: 1;
      }
      
      .progress-bar {
        padding: 0 10px;
        margin-bottom: 10px;
        
        .ant-slider {
          margin: 0;
          
          .ant-slider-rail {
            background-color: rgba(255, 255, 255, 0.2);
          }
          
          .ant-slider-track {
            background-color: #1890ff;
          }
          
          .ant-slider-handle {
            border-color: #1890ff;
          }
        }
      }
      
      .controls-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .left-controls, .right-controls {
          display: flex;
          align-items: center;
        }
        
        .ant-btn {
          color: white;
          
          &:hover {
            color: #1890ff;
          }
        }
        
        .volume-control {
          display: flex;
          align-items: center;
          
          .ant-slider {
            margin: 0 10px;
            
            .ant-slider-rail {
              background-color: rgba(255, 255, 255, 0.2);
            }
            
            .ant-slider-track {
              background-color: white;
            }
            
            .ant-slider-handle {
              border-color: white;
            }
          }
        }
        
        .time-display {
          margin-left: 10px;
          
          .ant-typography {
            color: white;
            font-size: 12px;
          }
        }
      }
    }
  }
  
  .tutorial-info {
    flex: 1;
    padding: 16px;
    background-color: white;
    overflow-y: auto;
    transition: height 0.3s ease;
    
    &.collapsed {
      height: 0;
      padding: 0;
      overflow: hidden;
    }
    
    .tutorial-header {
      .ant-typography {
        margin-top: 0;
        margin-bottom: 8px;
      }
      
      .tutorial-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
    }
    
    .chapters-list {
      margin-top: 16px;
      
      .ant-list-item {
        cursor: pointer;
        transition: background-color 0.2s ease;
        
        &:hover {
          background-color: #f5f5f5;
        }
        
        &.active-chapter {
          background-color: #e6f7ff;
          border-left: 3px solid #1890ff;
        }
        
        .chapter-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
        }
      }
    }
  }
  
  &-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    
    .ant-typography {
      color: #999;
    }
  }
}
