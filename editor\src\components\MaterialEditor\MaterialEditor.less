/**
 * 材质编辑器样式
 */
.material-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .material-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
    
    h2 {
      margin: 0;
    }
    
    .material-editor-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .material-editor-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    
    .material-editor-preview {
      width: 40%;
      padding: 16px;
      display: flex;
      flex-direction: column;
      
      .preview-canvas {
        flex: 1;
        width: 100%;
        background-color: #1e1e1e;
        border-radius: 4px;
      }
      
      .preview-controls {
        display: flex;
        justify-content: center;
        gap: 8px;
        margin-top: 16px;
      }
    }
    
    .material-editor-form {
      width: 60%;
      padding: 16px;
      overflow-y: auto;
      
      .ant-form-item {
        margin-bottom: 16px;
      }
      
      .color-picker-field {
        display: flex;
        align-items: center;
        gap: 8px;
        position: relative;
        
        .color-preview {
          width: 32px;
          height: 32px;
          border-radius: 4px;
          border: 1px solid #d9d9d9;
          cursor: pointer;
        }
        
        .color-picker-popover {
          position: absolute;
          z-index: 2;
          top: 40px;
          left: 0;
          
          .color-picker-cover {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
          }
        }
      }
      
      .texture-item {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 16px;
        padding: 16px;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        
        .ant-form-item {
          flex: 1;
          margin-bottom: 0;
        }
      }
    }
  }
}
