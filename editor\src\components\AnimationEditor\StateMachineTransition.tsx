/**
 * 状态机转换组件
 * 用于显示和交互状态机中的转换
 */
import React, { useMemo } from 'react';
import { Tooltip } from 'antd';
import { NodePosition } from '../../store/animations/stateMachineSlice';
import './StateMachineEditor.less';

/**
 * 状态机转换属性
 */
interface StateMachineTransitionProps {
  /** 源状态名称 */
  from: string;
  /** 目标状态名称 */
  to: string;
  /** 源状态位置 */
  fromPosition: NodePosition;
  /** 目标状态位置 */
  toPosition: NodePosition;
  /** 是否选中 */
  selected?: boolean;
  /** 标签 */
  label?: string;
  /** 曲线类型 */
  curveType?: string;
  /** 点击回调 */
  onClick?: () => void;
}

/**
 * 状态机转换组件
 */
const StateMachineTransition: React.FC<StateMachineTransitionProps> = ({
  from,
  to,
  fromPosition,
  toPosition,
  selected = false,
  label,
  curveType = 'bezier',
  onClick
}) => {
  // 节点半径
  const NODE_RADIUS = 50;
  
  // 计算路径
  const path = useMemo(() => {
    // 计算起点和终点
    const startX = fromPosition.x + NODE_RADIUS / 2;
    const startY = fromPosition.y + NODE_RADIUS / 2;
    const endX = toPosition.x + NODE_RADIUS / 2;
    const endY = toPosition.y + NODE_RADIUS / 2;
    
    // 计算方向向量
    const dx = endX - startX;
    const dy = endY - startY;
    const length = Math.sqrt(dx * dx + dy * dy);
    
    // 如果长度为0，返回空路径
    if (length === 0) return '';
    
    // 计算单位向量
    const unitX = dx / length;
    const unitY = dy / length;
    
    // 计算起点和终点的偏移
    const offsetX = unitX * (NODE_RADIUS / 2);
    const offsetY = unitY * (NODE_RADIUS / 2);
    
    // 计算实际起点和终点
    const actualStartX = startX + offsetX;
    const actualStartY = startY + offsetY;
    const actualEndX = endX - offsetX;
    const actualEndY = endY - offsetY;
    
    // 计算控制点
    let controlPoint1X, controlPoint1Y, controlPoint2X, controlPoint2Y;
    
    if (curveType === 'straight') {
      // 直线
      return `M ${actualStartX} ${actualStartY} L ${actualEndX} ${actualEndY}`;
    } else if (curveType === 'arc') {
      // 弧线
      const midX = (actualStartX + actualEndX) / 2;
      const midY = (actualStartY + actualEndY) / 2;
      const perpX = -unitY;
      const perpY = unitX;
      const curvature = 0.5;
      
      const controlX = midX + perpX * length * curvature;
      const controlY = midY + perpY * length * curvature;
      
      return `M ${actualStartX} ${actualStartY} Q ${controlX} ${controlY} ${actualEndX} ${actualEndY}`;
    } else {
      // 贝塞尔曲线
      const midX = (actualStartX + actualEndX) / 2;
      const midY = (actualStartY + actualEndY) / 2;
      const perpX = -unitY;
      const perpY = unitX;
      const curvature = 0.5;
      
      controlPoint1X = actualStartX + dx * 0.25 + perpX * length * curvature;
      controlPoint1Y = actualStartY + dy * 0.25 + perpY * length * curvature;
      controlPoint2X = actualStartX + dx * 0.75 + perpX * length * curvature;
      controlPoint2Y = actualStartY + dy * 0.75 + perpY * length * curvature;
      
      return `M ${actualStartX} ${actualStartY} C ${controlPoint1X} ${controlPoint1Y}, ${controlPoint2X} ${controlPoint2Y}, ${actualEndX} ${actualEndY}`;
    }
  }, [fromPosition, toPosition, NODE_RADIUS, curveType]);
  
  // 计算箭头
  const arrowhead = useMemo(() => {
    // 计算起点和终点
    const startX = fromPosition.x + NODE_RADIUS / 2;
    const startY = fromPosition.y + NODE_RADIUS / 2;
    const endX = toPosition.x + NODE_RADIUS / 2;
    const endY = toPosition.y + NODE_RADIUS / 2;
    
    // 计算方向向量
    const dx = endX - startX;
    const dy = endY - startY;
    const length = Math.sqrt(dx * dx + dy * dy);
    
    // 如果长度为0，返回空
    if (length === 0) return null;
    
    // 计算单位向量
    const unitX = dx / length;
    const unitY = dy / length;
    
    // 计算终点的偏移
    const offsetX = unitX * (NODE_RADIUS / 2);
    const offsetY = unitY * (NODE_RADIUS / 2);
    
    // 计算实际终点
    const actualEndX = endX - offsetX;
    const actualEndY = endY - offsetY;
    
    // 计算箭头的两个点
    const arrowSize = 10;
    const arrowAngle = Math.PI / 6; // 30度
    
    const arrowPoint1X = actualEndX - arrowSize * Math.cos(Math.atan2(dy, dx) - arrowAngle);
    const arrowPoint1Y = actualEndY - arrowSize * Math.sin(Math.atan2(dy, dx) - arrowAngle);
    const arrowPoint2X = actualEndX - arrowSize * Math.cos(Math.atan2(dy, dx) + arrowAngle);
    const arrowPoint2Y = actualEndY - arrowSize * Math.sin(Math.atan2(dy, dx) + arrowAngle);
    
    return (
      <polygon
        points={`${actualEndX},${actualEndY} ${arrowPoint1X},${arrowPoint1Y} ${arrowPoint2X},${arrowPoint2Y}`}
        fill={selected ? '#f5222d' : '#1890ff'}
        stroke={selected ? '#f5222d' : '#1890ff'}
        strokeWidth="2"
      />
    );
  }, [fromPosition, toPosition, NODE_RADIUS, selected]);
  
  // 计算标签位置
  const labelPosition = useMemo(() => {
    const midX = (fromPosition.x + toPosition.x) / 2 + NODE_RADIUS / 2;
    const midY = (fromPosition.y + toPosition.y) / 2 + NODE_RADIUS / 2;
    
    // 计算方向向量
    const dx = toPosition.x - fromPosition.x;
    const dy = toPosition.y - fromPosition.y;
    const length = Math.sqrt(dx * dx + dy * dy);
    
    // 如果长度为0，返回中点
    if (length === 0) return { x: midX, y: midY };
    
    // 计算单位向量
    const unitX = dx / length;
    const unitY = dy / length;
    
    // 计算垂直向量
    const perpX = -unitY;
    const perpY = unitX;
    
    // 计算标签偏移
    const labelOffsetX = perpX * 15;
    const labelOffsetY = perpY * 15;
    
    return {
      x: midX + labelOffsetX,
      y: midY + labelOffsetY
    };
  }, [fromPosition, toPosition, NODE_RADIUS]);
  
  // 处理点击
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onClick) {
      onClick();
    }
  };
  
  return (
    <g className={`state-machine-transition ${selected ? 'selected' : ''}`} onClick={handleClick}>
      <Tooltip title={`${from} → ${to}${label ? `: ${label}` : ''}`}>
        <path
          d={path}
          fill="none"
          stroke={selected ? '#f5222d' : '#1890ff'}
          strokeWidth={selected ? 3 : 2}
          strokeDasharray={curveType === 'dashed' ? '5,5' : 'none'}
        />
      </Tooltip>
      
      {arrowhead}
      
      {label && (
        <text
          x={labelPosition.x}
          y={labelPosition.y}
          textAnchor="middle"
          dominantBaseline="middle"
          fill={selected ? '#f5222d' : '#1890ff'}
          fontSize="12"
          fontWeight={selected ? 'bold' : 'normal'}
          className="transition-label"
        >
          {label}
        </text>
      )}
    </g>
  );
};

export default StateMachineTransition;
