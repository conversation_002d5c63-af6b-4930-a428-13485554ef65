/**
 * 资源管理示例
 * 展示如何使用资源管理系统
 */
import * as THREE from 'three';
import { 
  AssetManager, 
  AssetType, 
  ResourcePreloader, 
  DependencyType 
} from '../../src/assets';

/**
 * 资源管理示例
 */
export class AssetManagementExample {
  /** 资产管理器 */
  private assetManager: AssetManager;
  
  /** 资源预加载器 */
  private preloader: ResourcePreloader;
  
  /** Three.js场景 */
  private scene: THREE.Scene;
  
  /** Three.js相机 */
  private camera: THREE.PerspectiveCamera;
  
  /** Three.js渲染器 */
  private renderer: THREE.WebGLRenderer;
  
  /** 加载状态文本 */
  private loadingText: HTMLElement | null = null;
  
  /** 加载进度条 */
  private progressBar: HTMLElement | null = null;
  
  /** 是否已初始化 */
  private initialized: boolean = false;
  
  /** 是否已加载 */
  private loaded: boolean = false;
  
  /** 模型对象 */
  private model: THREE.Object3D | null = null;
  
  /** 纹理对象 */
  private texture: THREE.Texture | null = null;

  /**
   * 创建资源管理示例
   */
  constructor() {
    // 创建资产管理器
    this.assetManager = new AssetManager({
      baseUrl: 'assets/',
      enableDependencyManagement: true
    });
    
    // 创建资源预加载器
    this.preloader = new ResourcePreloader({
      assetManager: this.assetManager,
      autoRegisterAssets: true,
      autoLoadDependencies: true,
      maxConcurrentLoads: 4,
      retryCount: 2
    });
    
    // 创建Three.js场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x000000);
    
    // 创建相机
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    this.camera.position.set(0, 0, 5);
    
    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    
    // 添加窗口大小变化事件监听器
    window.addEventListener('resize', this.handleResize.bind(this));
  }

  /**
   * 初始化示例
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }
    
    // 初始化资产管理器
    this.assetManager.initialize();
    
    // 初始化资源预加载器
    this.preloader.initialize();
    
    // 创建UI
    this.createUI();
    
    // 注册资源
    this.registerAssets();
    
    // 添加资源依赖关系
    this.addDependencies();
    
    // 创建预加载组
    this.createPreloadGroups();
    
    // 添加事件监听器
    this.addEventListeners();
    
    // 将渲染器添加到DOM
    document.body.appendChild(this.renderer.domElement);
    
    this.initialized = true;
    
    // 开始加载资源
    await this.loadAssets();
    
    // 创建场景
    this.createScene();
    
    this.loaded = true;
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建加载状态文本
    this.loadingText = document.createElement('div');
    this.loadingText.style.position = 'absolute';
    this.loadingText.style.top = '20px';
    this.loadingText.style.left = '20px';
    this.loadingText.style.color = 'white';
    this.loadingText.style.fontFamily = 'Arial, sans-serif';
    this.loadingText.style.fontSize = '16px';
    this.loadingText.textContent = '加载中...';
    document.body.appendChild(this.loadingText);
    
    // 创建加载进度条容器
    const progressContainer = document.createElement('div');
    progressContainer.style.position = 'absolute';
    progressContainer.style.top = '50px';
    progressContainer.style.left = '20px';
    progressContainer.style.width = '300px';
    progressContainer.style.height = '20px';
    progressContainer.style.backgroundColor = '#333';
    progressContainer.style.borderRadius = '5px';
    document.body.appendChild(progressContainer);
    
    // 创建加载进度条
    this.progressBar = document.createElement('div');
    this.progressBar.style.width = '0%';
    this.progressBar.style.height = '100%';
    this.progressBar.style.backgroundColor = '#0095dd';
    this.progressBar.style.borderRadius = '5px';
    this.progressBar.style.transition = 'width 0.3s ease-in-out';
    progressContainer.appendChild(this.progressBar);
  }

  /**
   * 注册资源
   */
  private registerAssets(): void {
    // 注册纹理
    this.assetManager.registerAsset(
      'texture1',
      '纹理1',
      AssetType.TEXTURE,
      'textures/texture1.jpg'
    );
    
    this.assetManager.registerAsset(
      'texture2',
      '纹理2',
      AssetType.TEXTURE,
      'textures/texture2.jpg'
    );
    
    // 注册模型
    this.assetManager.registerAsset(
      'model1',
      '模型1',
      AssetType.MODEL,
      'models/model1.gltf'
    );
    
    // 注册音频
    this.assetManager.registerAsset(
      'sound1',
      '音效1',
      AssetType.AUDIO,
      'sounds/sound1.mp3'
    );
    
    this.assetManager.registerAsset(
      'music1',
      '音乐1',
      AssetType.AUDIO,
      'sounds/music1.mp3'
    );
    
    // 注册JSON数据
    this.assetManager.registerAsset(
      'config',
      '配置',
      AssetType.JSON,
      'data/config.json'
    );
  }

  /**
   * 添加资源依赖关系
   */
  private addDependencies(): void {
    // 模型依赖纹理
    this.assetManager.addDependency('model1', 'texture1', DependencyType.STRONG);
    this.assetManager.addDependency('model1', 'texture2', DependencyType.WEAK);
  }

  /**
   * 创建预加载组
   */
  private createPreloadGroups(): void {
    // 创建基础组
    this.preloader.addGroup({
      name: 'base',
      priority: 100,
      resources: [
        { id: 'config', type: AssetType.JSON, url: 'data/config.json', priority: 90 },
        { id: 'texture1', type: AssetType.TEXTURE, url: 'textures/texture1.jpg', priority: 80 }
      ]
    });
    
    // 创建模型组
    this.preloader.addGroup({
      name: 'models',
      priority: 80,
      dependencies: ['base'],
      resources: [
        { id: 'model1', type: AssetType.MODEL, url: 'models/model1.gltf', priority: 70 },
        { id: 'texture2', type: AssetType.TEXTURE, url: 'textures/texture2.jpg', priority: 60 }
      ]
    });
    
    // 创建音频组
    this.preloader.addGroup({
      name: 'audio',
      priority: 60,
      resources: [
        { id: 'sound1', type: AssetType.AUDIO, url: 'sounds/sound1.mp3', priority: 50 },
        { id: 'music1', type: AssetType.AUDIO, url: 'sounds/music1.mp3', priority: 40 }
      ]
    });
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners(): void {
    // 监听预加载进度
    this.preloader.on('progress', (progress) => {
      if (this.progressBar) {
        this.progressBar.style.width = `${progress.progress * 100}%`;
      }
      
      if (this.loadingText) {
        this.loadingText.textContent = `加载中... ${Math.floor(progress.progress * 100)}%`;
      }
    });
    
    // 监听预加载完成
    this.preloader.on('loadComplete', (progress) => {
      if (this.loadingText) {
        this.loadingText.textContent = `加载完成: ${progress.group}`;
      }
    });
    
    // 监听预加载错误
    this.preloader.on('loadError', (error) => {
      console.error('加载错误:', error);
      
      if (this.loadingText) {
        this.loadingText.textContent = `加载错误: ${error.group}`;
      }
    });
  }

  /**
   * 加载资源
   */
  private async loadAssets(): Promise<void> {
    try {
      // 加载基础组
      await this.preloader.loadGroup('base');
      
      // 加载模型组
      await this.preloader.loadGroup('models');
      
      // 加载音频组
      await this.preloader.loadGroup('audio');
      
      // 隐藏加载UI
      if (this.loadingText) {
        this.loadingText.style.display = 'none';
      }
      
      if (this.progressBar && this.progressBar.parentElement) {
        this.progressBar.parentElement.style.display = 'none';
      }
    } catch (error) {
      console.error('加载资源失败:', error);
    }
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0x404040);
    this.scene.add(ambientLight);
    
    // 添加平行光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(1, 1, 1);
    this.scene.add(directionalLight);
    
    // 获取纹理
    this.texture = this.assetManager.getAsset('texture1');
    
    // 创建立方体
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ 
      map: this.texture,
      roughness: 0.5,
      metalness: 0.5
    });
    const cube = new THREE.Mesh(geometry, material);
    this.scene.add(cube);
    
    // 获取模型
    this.model = this.assetManager.getAsset('model1');
    
    // 如果模型存在，则添加到场景
    if (this.model) {
      this.model.position.set(2, 0, 0);
      this.scene.add(this.model);
    }
  }

  /**
   * 处理窗口大小变化
   */
  private handleResize(): void {
    // 更新相机宽高比
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    
    // 更新渲染器大小
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 更新示例
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized || !this.loaded) {
      return;
    }
    
    // 旋转立方体
    const cube = this.scene.children.find(child => child instanceof THREE.Mesh && child.geometry instanceof THREE.BoxGeometry);
    if (cube) {
      cube.rotation.x += deltaTime * 0.5;
      cube.rotation.y += deltaTime * 0.2;
    }
    
    // 旋转模型
    if (this.model) {
      this.model.rotation.y += deltaTime * 0.3;
    }
  }

  /**
   * 渲染示例
   */
  public render(): void {
    if (!this.initialized || !this.loaded) {
      return;
    }
    
    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }

  /**
   * 获取资产管理器
   * @returns 资产管理器
   */
  public getAssetManager(): AssetManager {
    return this.assetManager;
  }

  /**
   * 获取资源预加载器
   * @returns 资源预加载器
   */
  public getPreloader(): ResourcePreloader {
    return this.preloader;
  }

  /**
   * 销毁示例
   */
  public dispose(): void {
    // 移除窗口大小变化事件监听器
    window.removeEventListener('resize', this.handleResize.bind(this));
    
    // 移除渲染器
    if (this.renderer.domElement.parentNode) {
      this.renderer.domElement.parentNode.removeChild(this.renderer.domElement);
    }
    
    // 移除UI
    if (this.loadingText && this.loadingText.parentNode) {
      this.loadingText.parentNode.removeChild(this.loadingText);
    }
    
    if (this.progressBar && this.progressBar.parentElement && this.progressBar.parentElement.parentNode) {
      this.progressBar.parentElement.parentNode.removeChild(this.progressBar.parentElement);
    }
    
    // 销毁资源预加载器
    this.preloader.dispose();
    
    // 销毁资产管理器
    this.assetManager.dispose();
  }
}
