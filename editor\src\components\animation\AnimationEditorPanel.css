/**
 * 动画编辑器面板样式
 */

.animation-editor-panel {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f0f2f5;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.animation-editor-header {
  height: 48px;
  background-color: #001529;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.animation-editor-title {
  font-size: 16px;
  font-weight: bold;
}

.animation-editor-controls {
  display: flex;
  gap: 8px;
}

.animation-editor-content {
  flex: 1;
  overflow: hidden;
}

.animation-editor-sider {
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
  overflow-y: auto;
}

.animation-editor-tabs {
  height: 100%;
}

.animation-editor-tab-header {
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.animation-editor-tab-content {
  padding: 16px;
}

.animation-editor-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: #f5f5f5;
  cursor: pointer;
  transition: all 0.3s;
}

.animation-editor-item:hover {
  background-color: #e6f7ff;
}

.animation-editor-item.selected {
  background-color: #1890ff;
  color: #fff;
}

.animation-editor-item-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.animation-editor-item-controls {
  display: flex;
  gap: 4px;
}

.animation-editor-main {
  background-color: #fff;
  padding: 16px;
  overflow: hidden;
}

.animation-editor-main-tabs {
  height: 100%;
}

.animation-editor-properties {
  padding: 16px;
  overflow-y: auto;
  height: calc(100% - 48px);
}

.animation-editor-form-item {
  margin-bottom: 16px;
}

.animation-editor-form-label {
  margin-bottom: 8px;
  font-weight: bold;
}

.animation-editor-form-control {
  width: 100%;
}

/* 时间轴样式 */
.animation-timeline {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.animation-timeline-header {
  height: 32px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
}

.animation-timeline-ruler {
  height: 24px;
  position: relative;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
}

.animation-timeline-ruler-tick {
  position: absolute;
  top: 0;
  width: 1px;
  height: 8px;
  background-color: #ccc;
}

.animation-timeline-ruler-tick.major {
  height: 16px;
  background-color: #999;
}

.animation-timeline-ruler-label {
  position: absolute;
  top: 16px;
  transform: translateX(-50%);
  font-size: 10px;
  color: #666;
}

.animation-timeline-content {
  flex: 1;
  overflow-y: auto;
  position: relative;
}

.animation-timeline-layers {
  position: relative;
}

.animation-timeline-layer {
  height: 32px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
}

.animation-timeline-layer-header {
  width: 150px;
  padding: 0 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background-color: #f5f5f5;
  border-right: 1px solid #e8e8e8;
}

.animation-timeline-layer-content {
  flex: 1;
  height: 100%;
  position: relative;
}

.animation-timeline-clip {
  position: absolute;
  top: 4px;
  height: 24px;
  background-color: #1890ff;
  border-radius: 4px;
  cursor: pointer;
}

.animation-timeline-clip:hover {
  background-color: #40a9ff;
}

.animation-timeline-clip.selected {
  background-color: #096dd9;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.animation-timeline-clip-handle {
  position: absolute;
  top: 0;
  width: 8px;
  height: 100%;
  cursor: ew-resize;
}

.animation-timeline-clip-handle.left {
  left: 0;
}

.animation-timeline-clip-handle.right {
  right: 0;
}

.animation-timeline-playhead {
  position: absolute;
  top: 0;
  width: 2px;
  height: 100%;
  background-color: #f5222d;
  z-index: 10;
}

.animation-timeline-event {
  position: absolute;
  top: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #faad14;
  transform: translateX(-50%);
  cursor: pointer;
  z-index: 5;
}

.animation-timeline-event:hover {
  background-color: #ffc53d;
}

.animation-timeline-event.selected {
  background-color: #d48806;
  box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
}

/* 预览样式 */
.animation-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.animation-preview-viewport {
  flex: 1;
  background-color: #000;
  position: relative;
}

.animation-preview-controls {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 0 16px;
  background-color: #f5f5f5;
  border-top: 1px solid #e8e8e8;
}

.animation-preview-time {
  font-family: monospace;
  font-size: 14px;
  color: #666;
}

.animation-preview-slider {
  flex: 1;
  margin: 0 16px;
}

/* 性能监控样式 */
.animation-performance {
  margin-top: 24px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.animation-performance-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 16px;
}

.animation-performance-chart {
  height: 200px;
  margin-bottom: 16px;
}

.animation-performance-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.animation-performance-stat {
  padding: 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.animation-performance-stat-label {
  font-size: 12px;
  color: #666;
}

.animation-performance-stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

/* 反馈样式 */
.animation-editor-feedback-header {
  margin-bottom: 16px;
}

.animation-editor-feedback-header h3 {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.animation-editor-feedback-header p {
  color: #666;
  margin-bottom: 16px;
}

.animation-editor-feedback-stats {
  margin-top: 16px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .animation-editor-sider {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
  }

  .animation-editor-content {
    flex-direction: column;
  }

  .animation-editor-main {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
  }
}
