/**
 * 场景面板组件
 */
import React, { useRef, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setViewportSize,
  setActiveCamera,
  setSelectedObject,
  setSelectedObjects,
  setSceneGraph
} from '../../store/editor/editorSlice';

// 导入服务
import EngineService, { EngineEventType, TransformMode, TransformSpace } from '../../services/EngineService';
import SceneService, { SceneEventType } from '../../services/SceneService';

// 导入引擎相关类型和函数
import { Entity } from '../../../engine/src/core/Entity';
import { Camera } from '../../../engine/src/rendering/Camera';
import { Vector3 } from '../../../engine/src/math/Vector3';

const ScenePanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const {
    showGrid,
    showAxes,
    isPlaying,
    transformMode,
    transformSpace,
    snapMode,
    gridSize,
  } = useAppSelector((state) => state.editor);

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const [isEngineInitialized, setIsEngineInitialized] = useState(false);
  const [isSceneLoaded, setIsSceneLoaded] = useState(false);

  // 初始化引擎
  useEffect(() => {
    if (!canvasRef.current || isEngineInitialized) return;

    const initEngine = async () => {
      try {
        // 初始化引擎
        await EngineService.initialize(canvasRef.current, {
          autoStart: false,
          debug: true,
        });

        setIsEngineInitialized(true);

        // 监听引擎事件
        EngineService.on(EngineEventType.OBJECT_SELECTED, handleObjectSelected);
        EngineService.on(EngineEventType.OBJECT_DESELECTED, handleObjectDeselected);

        console.log('引擎初始化成功');
      } catch (error) {
        console.error('引擎初始化失败:', error);
      }
    };

    initEngine();

    // 清理函数
    return () => {
      EngineService.off(EngineEventType.OBJECT_SELECTED, handleObjectSelected);
      EngineService.off(EngineEventType.OBJECT_DESELECTED, handleObjectDeselected);
    };
  }, [canvasRef]);

  // 监听场景服务事件
  useEffect(() => {
    // 监听场景加载事件
    SceneService.on(SceneEventType.LOADING_COMPLETE, handleSceneLoaded);
    SceneService.on(SceneEventType.SCENE_GRAPH_CHANGED, handleSceneGraphChanged);

    // 清理函数
    return () => {
      SceneService.off(SceneEventType.LOADING_COMPLETE, handleSceneLoaded);
      SceneService.off(SceneEventType.SCENE_GRAPH_CHANGED, handleSceneGraphChanged);
    };
  }, []);

  // 处理窗口大小变化
  useEffect(() => {
    if (!containerRef.current || !isEngineInitialized) return;

    const handleResize = () => {
      if (!containerRef.current) return;

      const { width, height } = containerRef.current.getBoundingClientRect();

      // 更新Redux状态
      dispatch(setViewportSize({ width, height }));
    };

    // 初始调整大小
    handleResize();

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);

    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [containerRef, isEngineInitialized, dispatch]);

  // 处理网格显示
  useEffect(() => {
    if (!isSceneLoaded) return;

    const scene = EngineService.getActiveScene();
    if (scene) {
      scene.setGridVisible(showGrid);
    }
  }, [isSceneLoaded, showGrid]);

  // 处理坐标轴显示
  useEffect(() => {
    if (!isSceneLoaded) return;

    const scene = EngineService.getActiveScene();
    if (scene) {
      scene.setAxesVisible(showAxes);
    }
  }, [isSceneLoaded, showAxes]);

  // 处理播放状态
  useEffect(() => {
    if (!isEngineInitialized) return;

    if (isPlaying) {
      EngineService.start();
    } else {
      EngineService.stop();
    }
  }, [isEngineInitialized, isPlaying]);

  // 处理变换模式
  useEffect(() => {
    if (!isEngineInitialized) return;

    EngineService.setTransformMode(transformMode as TransformMode);
  }, [isEngineInitialized, transformMode]);

  // 处理变换空间
  useEffect(() => {
    if (!isEngineInitialized) return;

    EngineService.setTransformSpace(transformSpace as TransformSpace);
  }, [isEngineInitialized, transformSpace]);

  // 处理对象选择
  const handleObjectSelected = (entity: Entity) => {
    // 更新Redux状态
    dispatch(setSelectedObject(entity));

    // 如果是相机，设置为活动相机
    const camera = entity.getComponent<Camera>('Camera');
    if (camera) {
      dispatch(setActiveCamera(camera));
      EngineService.setActiveCamera(camera);
    }
  };

  // 处理对象取消选择
  const handleObjectDeselected = (entity: Entity) => {
    // 更新Redux状态
    const selectedEntities = EngineService.getSelectedEntities();
    if (selectedEntities.length === 0) {
      dispatch(setSelectedObject(null));
    } else {
      dispatch(setSelectedObjects(selectedEntities));
    }
  };

  // 处理场景加载
  const handleSceneLoaded = ({ scene }: any) => {
    setIsSceneLoaded(true);

    // 获取活动相机
    const camera = EngineService.getActiveCamera();
    if (camera) {
      dispatch(setActiveCamera(camera));
    }

    // 开始引擎渲染
    EngineService.start();
  };

  // 处理场景图变化
  const handleSceneGraphChanged = (sceneGraph: any) => {
    dispatch(setSceneGraph(sceneGraph));
  };

  // 处理鼠标事件
  const handleMouseDown = (e: React.MouseEvent) => {
    // 实现对象选择逻辑
    if (!isSceneLoaded) return;

    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // 将坐标转换为归一化设备坐标 (-1 到 1)
    const normalizedX = (x / rect.width) * 2 - 1;
    const normalizedY = -(y / rect.height) * 2 + 1;

    // 使用引擎的射线投射功能选择对象
    const scene = EngineService.getActiveScene();
    if (scene) {
      const camera = EngineService.getActiveCamera();
      if (camera) {
        // 这里应该调用场景的射线投射方法，但由于我们没有完整实现引擎，所以这里只是示例
        // const hitResult = scene.raycast(normalizedX, normalizedY, camera);
        // if (hitResult && hitResult.entity) {
        //   EngineService.selectEntity(hitResult.entity);
        // } else {
        //   EngineService.clearSelection();
        // }
      }
    }
  };

  return (
    <div
      ref={containerRef}
      style={{
        width: '100%',
        height: '100%',
        position: 'relative',
        overflow: 'hidden',
        backgroundColor: '#1e1e1e',
      }}
    >
      <canvas
        ref={canvasRef}
        style={{
          width: '100%',
          height: '100%',
          display: 'block',
        }}
        onMouseDown={handleMouseDown}
      />
      {!isEngineInitialized && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            color: '#fff',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
          }}
        >
          {t('editor.loadingEngine')}
        </div>
      )}
    </div>
  );
};

export default ScenePanel;
