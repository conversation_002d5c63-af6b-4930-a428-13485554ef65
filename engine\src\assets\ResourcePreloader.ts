/**
 * 资源预加载器
 * 用于预加载资源，提高游戏性能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { AssetManager } from './AssetManager';
import { AssetType } from './ResourceManager';

/**
 * 预加载资源信息
 */
export interface PreloadResourceInfo {
  /** 资源ID */
  id: string;
  /** 资源类型 */
  type: AssetType;
  /** 资源URL */
  url: string;
  /** 资源优先级（0-100，数值越大优先级越高） */
  priority?: number;
  /** 资源组 */
  group?: string;
  /** 资源元数据 */
  metadata?: Record<string, any>;
}

/**
 * 预加载组信息
 */
export interface PreloadGroupInfo {
  /** 组名 */
  name: string;
  /** 组优先级（0-100，数值越大优先级越高） */
  priority: number;
  /** 组依赖 */
  dependencies?: string[];
  /** 组资源 */
  resources: PreloadResourceInfo[];
}

/**
 * 预加载进度信息
 */
export interface PreloadProgressInfo {
  /** 组名 */
  group: string;
  /** 已加载资源数 */
  loaded: number;
  /** 总资源数 */
  total: number;
  /** 加载进度（0-1） */
  progress: number;
  /** 已加载资源 */
  loadedResources: string[];
  /** 失败资源 */
  failedResources: string[];
}

/**
 * 资源预加载器选项
 */
export interface ResourcePreloaderOptions {
  /** 资产管理器 */
  assetManager: AssetManager;
  /** 是否自动注册资源 */
  autoRegisterAssets?: boolean;
  /** 是否自动加载依赖组 */
  autoLoadDependencies?: boolean;
  /** 最大并发加载数 */
  maxConcurrentLoads?: number;
  /** 重试次数 */
  retryCount?: number;
  /** 重试延迟（毫秒） */
  retryDelay?: number;
}

/**
 * 资源预加载器
 */
export class ResourcePreloader extends EventEmitter {
  /** 资产管理器 */
  private assetManager: AssetManager;

  /** 是否自动注册资源 */
  private autoRegisterAssets: boolean;

  /** 是否自动加载依赖组 */
  private autoLoadDependencies: boolean;

  /** 最大并发加载数 */
  private maxConcurrentLoads: number;

  /** 重试次数 */
  private retryCount: number;

  /** 重试延迟（毫秒） */
  private retryDelay: number;

  /** 预加载组映射 */
  private groups: Map<string, PreloadGroupInfo> = new Map();

  /** 预加载进度映射 */
  private progress: Map<string, PreloadProgressInfo> = new Map();

  /** 当前加载组 */
  private currentGroup: string | null = null;

  /** 加载队列 */
  private loadQueue: PreloadResourceInfo[] = [];

  /** 当前并发加载数 */
  private currentConcurrentLoads: number = 0;

  /** 是否正在加载 */
  private loading: boolean = false;

  /** 是否已暂停 */
  private paused: boolean = false;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建资源预加载器实例
   * @param options 资源预加载器选项
   */
  constructor(options: ResourcePreloaderOptions) {
    super();

    this.assetManager = options.assetManager;
    this.autoRegisterAssets = options.autoRegisterAssets !== undefined ? options.autoRegisterAssets : true;
    this.autoLoadDependencies = options.autoLoadDependencies !== undefined ? options.autoLoadDependencies : true;
    this.maxConcurrentLoads = options.maxConcurrentLoads || 6;
    this.retryCount = options.retryCount || 3;
    this.retryDelay = options.retryDelay || 1000;
  }

  /**
   * 初始化资源预加载器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 添加预加载组
   * @param group 预加载组信息
   * @returns 是否成功添加
   */
  public addGroup(group: PreloadGroupInfo): boolean {
    // 检查组名是否已存在
    if (this.groups.has(group.name)) {
      return false;
    }

    // 添加组
    this.groups.set(group.name, {
      ...group,
      priority: group.priority || 0,
      dependencies: group.dependencies || [],
      resources: [...group.resources],
    });

    // 初始化进度信息
    this.progress.set(group.name, {
      group: group.name,
      loaded: 0,
      total: group.resources.length,
      progress: 0,
      loadedResources: [],
      failedResources: [],
    });

    // 如果启用自动注册资源，则注册组中的资源
    if (this.autoRegisterAssets) {
      this.registerGroupAssets(group.name);
    }

    // 发出组添加事件
    this.emit('groupAdded', group);

    return true;
  }

  /**
   * 移除预加载组
   * @param name 组名
   * @returns 是否成功移除
   */
  public removeGroup(name: string): boolean {
    // 检查组名是否存在
    if (!this.groups.has(name)) {
      return false;
    }

    // 如果正在加载此组，则无法移除
    if (this.currentGroup === name) {
      return false;
    }

    // 获取组信息
    const group = this.groups.get(name)!;

    // 移除组
    this.groups.delete(name);

    // 移除进度信息
    this.progress.delete(name);

    // 发出组移除事件
    this.emit('groupRemoved', group);

    return true;
  }

  /**
   * 注册组中的资源
   * @param name 组名
   * @returns 是否成功注册
   */
  private registerGroupAssets(name: string): boolean {
    // 检查组名是否存在
    if (!this.groups.has(name)) {
      return false;
    }

    // 获取组信息
    const group = this.groups.get(name)!;

    // 注册组中的资源
    for (const resource of group.resources) {
      try {
        // 检查资源是否已注册
        if (this.assetManager.getAssetInfo(resource.id)) {
          continue;
        }

        // 注册资源
        this.assetManager.registerAsset(
          resource.id,
          resource.id,
          resource.type,
          resource.url,
          resource.metadata
        );
      } catch (error) {
        console.error(`注册资源 ${resource.id} 失败:`, error);
      }
    }

    return true;
  }

  /**
   * 加载预加载组
   * @param name 组名
   * @param onProgress 进度回调
   * @returns Promise，解析为加载结果
   */
  public async loadGroup(
    name: string,
    onProgress?: (progress: PreloadProgressInfo) => void
  ): Promise<PreloadProgressInfo> {
    // 检查组名是否存在
    if (!this.groups.has(name)) {
      throw new Error(`找不到预加载组: ${name}`);
    }

    // 如果正在加载，则返回
    if (this.loading && !this.paused) {
      throw new Error('已有预加载组正在加载');
    }

    // 获取组信息
    const group = this.groups.get(name)!;

    // 重置进度信息
    this.resetProgress(name);

    // 设置当前加载组
    this.currentGroup = name;
    this.loading = true;
    this.paused = false;

    // 发出加载开始事件
    this.emit('loadStart', { group: name });

    try {
      // 如果启用自动加载依赖组，则先加载依赖组
      if (this.autoLoadDependencies && group.dependencies && group.dependencies.length > 0) {
        for (const depName of group.dependencies) {
          await this.loadGroup(depName);
        }
      }

      // 准备加载队列
      this.prepareLoadQueue(name);

      // 开始加载
      await this.startLoading(onProgress);

      // 获取最终进度信息
      const progressInfo = this.progress.get(name)!;

      // 发出加载完成事件
      this.emit('loadComplete', progressInfo);

      return progressInfo;
    } catch (error) {
      // 发出加载错误事件
      this.emit('loadError', { group: name, error });

      throw error;
    } finally {
      // 重置状态
      this.currentGroup = null;
      this.loading = false;
      this.paused = false;
      this.loadQueue = [];
      this.currentConcurrentLoads = 0;
    }
  }

  /**
   * 重置进度信息
   * @param name 组名
   */
  private resetProgress(name: string): void {
    // 检查组名是否存在
    if (!this.groups.has(name)) {
      return;
    }

    // 获取组信息
    const group = this.groups.get(name)!;

    // 重置进度信息
    this.progress.set(name, {
      group: name,
      loaded: 0,
      total: group.resources.length,
      progress: 0,
      loadedResources: [],
      failedResources: [],
    });
  }

  /**
   * 准备加载队列
   * @param name 组名
   */
  private prepareLoadQueue(name: string): void {
    // 检查组名是否存在
    if (!this.groups.has(name)) {
      return;
    }

    // 获取组信息
    const group = this.groups.get(name)!;

    // 清空加载队列
    this.loadQueue = [];

    // 添加资源到加载队列
    for (const resource of group.resources) {
      // 检查资源是否已加载
      if (this.assetManager.getAsset(resource.id)) {
        // 更新进度信息
        this.updateProgress(name, resource.id, true);
        continue;
      }

      // 添加到加载队列
      this.loadQueue.push(resource);
    }

    // 按优先级排序
    this.loadQueue.sort((a, b) => (b.priority || 0) - (a.priority || 0));
  }

  /**
   * 开始加载
   * @param onProgress 进度回调
   * @returns Promise，解析为加载结果
   */
  private async startLoading(
    onProgress?: (progress: PreloadProgressInfo) => void
  ): Promise<void> {
    // 如果没有当前加载组，则返回
    if (!this.currentGroup) {
      return;
    }

    // 获取进度信息
    const progressInfo = this.progress.get(this.currentGroup)!;

    // 如果已加载完成，则返回
    if (progressInfo.loaded === progressInfo.total) {
      return;
    }

    // 创建加载Promise
    return new Promise<void>((resolve, _reject) => {
      // 加载完成回调
      const onComplete = () => {
        resolve();
      };

      // 注意：onError 回调在此处未使用，但保留为注释以便将来可能的扩展
      // const onError = (error: Error) => {
      //   reject(error);
      // };

      // 加载下一个资源
      const loadNext = () => {
        // 如果已暂停，则返回
        if (this.paused) {
          return;
        }

        // 如果没有当前加载组，则返回
        if (!this.currentGroup) {
          return;
        }

        // 获取进度信息
        const progressInfo = this.progress.get(this.currentGroup)!;

        // 如果已加载完成，则调用完成回调
        if (progressInfo.loaded === progressInfo.total) {
          onComplete();
          return;
        }

        // 如果加载队列为空，则返回
        if (this.loadQueue.length === 0) {
          return;
        }

        // 如果当前并发加载数达到最大值，则返回
        if (this.currentConcurrentLoads >= this.maxConcurrentLoads) {
          return;
        }

        // 增加并发加载计数
        this.currentConcurrentLoads++;

        // 获取下一个资源
        const resource = this.loadQueue.shift()!;

        // 加载资源
        this.loadResource(resource, 0)
          .then(() => {
            // 更新进度信息
            this.updateProgress(this.currentGroup!, resource.id, true);

            // 调用进度回调
            if (onProgress) {
              onProgress(this.progress.get(this.currentGroup!)!);
            }

            // 减少并发加载计数
            this.currentConcurrentLoads--;

            // 加载下一个资源
            loadNext();
          })
          .catch(_error => {
            // 更新进度信息
            this.updateProgress(this.currentGroup!, resource.id, false);

            // 调用进度回调
            if (onProgress) {
              onProgress(this.progress.get(this.currentGroup!)!);
            }

            // 减少并发加载计数
            this.currentConcurrentLoads--;

            // 如果所有资源都已处理，则调用完成回调
            if (progressInfo.loaded + progressInfo.failedResources.length === progressInfo.total) {
              onComplete();
            } else {
              // 否则加载下一个资源
              loadNext();
            }
          });

        // 继续加载下一个资源
        loadNext();
      };

      // 开始加载
      for (let i = 0; i < this.maxConcurrentLoads; i++) {
        loadNext();
      }
    });
  }

  /**
   * 加载资源
   * @param resource 资源信息
   * @param retryCount 当前重试次数
   * @returns Promise
   */
  private async loadResource(
    resource: PreloadResourceInfo,
    retryCount: number
  ): Promise<void> {
    try {
      // 加载资源
      await this.assetManager.loadAsset(resource.id);
    } catch (error) {
      // 如果重试次数未达到最大值，则重试
      if (retryCount < this.retryCount) {
        // 等待重试延迟
        await new Promise(resolve => setTimeout(resolve, this.retryDelay));

        // 重试加载
        return this.loadResource(resource, retryCount + 1);
      }

      // 否则抛出错误
      throw error;
    }
  }

  /**
   * 更新进度信息
   * @param group 组名
   * @param resourceId 资源ID
   * @param success 是否成功
   */
  private updateProgress(group: string, resourceId: string, success: boolean): void {
    // 检查组名是否存在
    if (!this.groups.has(group)) {
      return;
    }

    // 获取进度信息
    const progressInfo = this.progress.get(group)!;

    // 更新进度信息
    if (success) {
      // 如果资源已在已加载列表中，则返回
      if (progressInfo.loadedResources.includes(resourceId)) {
        return;
      }

      // 添加到已加载列表
      progressInfo.loadedResources.push(resourceId);
      progressInfo.loaded++;
    } else {
      // 如果资源已在失败列表中，则返回
      if (progressInfo.failedResources.includes(resourceId)) {
        return;
      }

      // 添加到失败列表
      progressInfo.failedResources.push(resourceId);
    }

    // 更新进度
    progressInfo.progress = progressInfo.loaded / progressInfo.total;

    // 发出进度更新事件
    this.emit('progress', progressInfo);
  }

  /**
   * 暂停加载
   */
  public pause(): void {
    if (!this.loading || this.paused) {
      return;
    }

    this.paused = true;

    // 发出暂停事件
    this.emit('pause', { group: this.currentGroup });
  }

  /**
   * 恢复加载
   */
  public resume(): void {
    if (!this.loading || !this.paused) {
      return;
    }

    this.paused = false;

    // 发出恢复事件
    this.emit('resume', { group: this.currentGroup });

    // 继续加载
    this.startLoading();
  }

  /**
   * 获取预加载组
   * @param name 组名
   * @returns 预加载组信息
   */
  public getGroup(name: string): PreloadGroupInfo | null {
    return this.groups.get(name) || null;
  }

  /**
   * 获取所有预加载组
   * @returns 预加载组信息数组
   */
  public getAllGroups(): PreloadGroupInfo[] {
    return Array.from(this.groups.values());
  }

  /**
   * 获取预加载进度
   * @param name 组名
   * @returns 预加载进度信息
   */
  public getProgress(name: string): PreloadProgressInfo | null {
    return this.progress.get(name) || null;
  }

  /**
   * 获取当前加载组
   * @returns 当前加载组名
   */
  public getCurrentGroup(): string | null {
    return this.currentGroup;
  }

  /**
   * 是否正在加载
   * @returns 是否正在加载
   */
  public isLoading(): boolean {
    return this.loading;
  }

  /**
   * 是否已暂停
   * @returns 是否已暂停
   */
  public isPaused(): boolean {
    return this.paused;
  }

  /**
   * 清空所有预加载组
   */
  public clear(): void {
    // 如果正在加载，则先暂停
    if (this.loading && !this.paused) {
      this.pause();
    }

    // 清空预加载组
    this.groups.clear();

    // 清空进度信息
    this.progress.clear();

    // 重置状态
    this.currentGroup = null;
    this.loading = false;
    this.paused = false;
    this.loadQueue = [];
    this.currentConcurrentLoads = 0;

    // 发出清空事件
    this.emit('cleared');
  }

  /**
   * 销毁资源预加载器
   */
  public dispose(): void {
    // 清空所有预加载组
    this.clear();

    // 移除所有事件监听器
    this.removeAllListeners();

    this.initialized = false;
  }
}
