/**
 * 辅助功能服务
 * 提供编辑器的无障碍功能支持
 */
import { EventEmitter } from '../../engine/src/utils/EventEmitter';
import i18n from '../i18n';

/**
 * 辅助功能配置
 */
export interface AccessibilityConfig {
  /** 是否启用辅助功能 */
  enabled?: boolean;
  /** 是否启用屏幕阅读器支持 */
  enableScreenReader?: boolean;
  /** 是否启用键盘导航 */
  enableKeyboardNavigation?: boolean;
  /** 是否启用高对比度模式 */
  enableHighContrast?: boolean;
  /** 是否启用文本缩放 */
  enableTextScaling?: boolean;
  /** 是否启用动画减弱 */
  enableReducedMotion?: boolean;
  /** 是否启用自动朗读 */
  enableAutoAnnounce?: boolean;
  /** 是否启用焦点指示器 */
  enableFocusIndicator?: boolean;
  /** 是否启用颜色调整 */
  enableColorAdjustment?: boolean;
  /** 是否启用调试模式 */
  debug?: boolean;
}

/**
 * 辅助功能事件类型
 */
export enum AccessibilityEventType {
  /** 配置变更 */
  CONFIG_CHANGED = 'configChanged',
  /** 屏幕阅读器状态变更 */
  SCREEN_READER_CHANGED = 'screenReaderChanged',
  /** 高对比度模式状态变更 */
  HIGH_CONTRAST_CHANGED = 'highContrastChanged',
  /** 文本缩放状态变更 */
  TEXT_SCALING_CHANGED = 'textScalingChanged',
  /** 动画减弱状态变更 */
  REDUCED_MOTION_CHANGED = 'reducedMotionChanged',
  /** 焦点指示器状态变更 */
  FOCUS_INDICATOR_CHANGED = 'focusIndicatorChanged',
  /** 颜色调整状态变更 */
  COLOR_ADJUSTMENT_CHANGED = 'colorAdjustmentChanged',
  /** 朗读消息 */
  ANNOUNCE = 'announce'
}

/**
 * 辅助功能服务
 */
export class AccessibilityService extends EventEmitter {
  /** 单例实例 */
  private static instance: AccessibilityService;

  /** 配置 */
  private config: AccessibilityConfig;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 文本缩放比例 */
  private textScaleFactor: number = 1.0;

  /** 高对比度模式颜色 */
  private highContrastColors: Map<string, string> = new Map();

  /** 颜色调整设置 */
  private colorAdjustments: {
    brightness: number;
    contrast: number;
    saturation: number;
  } = {
    brightness: 1.0,
    contrast: 1.0,
    saturation: 1.0
  };

  /**
   * 获取单例实例
   */
  public static getInstance(): AccessibilityService {
    if (!AccessibilityService.instance) {
      AccessibilityService.instance = new AccessibilityService();
    }
    return AccessibilityService.instance;
  }

  /**
   * 构造函数
   */
  private constructor() {
    super();

    // 默认配置
    this.config = {
      enabled: true,
      enableScreenReader: true,
      enableKeyboardNavigation: true,
      enableHighContrast: false,
      enableTextScaling: false,
      enableReducedMotion: false,
      enableAutoAnnounce: true,
      enableFocusIndicator: true,
      enableColorAdjustment: false,
      debug: false
    };

    // 初始化
    this.initialize();
  }

  /**
   * 初始化
   */
  private initialize(): void {
    if (this.initialized) return;

    // 检测系统辅助功能设置
    this.detectSystemAccessibilitySettings();

    // 添加媒体查询监听器
    this.setupMediaQueryListeners();

    // 设置键盘导航
    if (this.config.enableKeyboardNavigation) {
      this.setupKeyboardNavigation();
    }

    // 设置焦点指示器
    if (this.config.enableFocusIndicator) {
      this.setupFocusIndicator();
    }

    // 设置高对比度模式
    if (this.config.enableHighContrast) {
      this.applyHighContrastMode();
    }

    // 设置文本缩放
    if (this.config.enableTextScaling) {
      this.applyTextScaling();
    }

    // 设置动画减弱
    if (this.config.enableReducedMotion) {
      this.applyReducedMotion();
    }

    // 设置颜色调整
    if (this.config.enableColorAdjustment) {
      this.applyColorAdjustment();
    }

    this.initialized = true;

    if (this.config.debug) {
      console.log('辅助功能服务已初始化', this.config);
    }
  }

  /**
   * 配置服务
   * @param config 配置
   */
  public configure(config: Partial<AccessibilityConfig>): void {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...config };

    // 处理配置变更
    if (oldConfig.enableScreenReader !== this.config.enableScreenReader) {
      this.emit(AccessibilityEventType.SCREEN_READER_CHANGED, {
        enabled: this.config.enableScreenReader
      });
    }

    if (oldConfig.enableHighContrast !== this.config.enableHighContrast) {
      this.applyHighContrastMode();
      this.emit(AccessibilityEventType.HIGH_CONTRAST_CHANGED, {
        enabled: this.config.enableHighContrast
      });
    }

    if (oldConfig.enableTextScaling !== this.config.enableTextScaling) {
      this.applyTextScaling();
      this.emit(AccessibilityEventType.TEXT_SCALING_CHANGED, {
        enabled: this.config.enableTextScaling,
        factor: this.textScaleFactor
      });
    }

    if (oldConfig.enableReducedMotion !== this.config.enableReducedMotion) {
      this.applyReducedMotion();
      this.emit(AccessibilityEventType.REDUCED_MOTION_CHANGED, {
        enabled: this.config.enableReducedMotion
      });
    }

    if (oldConfig.enableFocusIndicator !== this.config.enableFocusIndicator) {
      this.setupFocusIndicator();
      this.emit(AccessibilityEventType.FOCUS_INDICATOR_CHANGED, {
        enabled: this.config.enableFocusIndicator
      });
    }

    if (oldConfig.enableColorAdjustment !== this.config.enableColorAdjustment) {
      this.applyColorAdjustment();
      this.emit(AccessibilityEventType.COLOR_ADJUSTMENT_CHANGED, {
        enabled: this.config.enableColorAdjustment,
        adjustments: this.colorAdjustments
      });
    }

    // 发出配置变更事件
    this.emit(AccessibilityEventType.CONFIG_CHANGED, {
      oldConfig,
      newConfig: this.config
    });

    if (this.config.debug) {
      console.log('辅助功能配置已更新', this.config);
    }
  }

  /**
   * 检测系统辅助功能设置
   */
  private detectSystemAccessibilitySettings(): void {
    // 检测系统减弱动画设置
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
      this.config.enableReducedMotion = true;
    }

    // 检测系统高对比度设置
    const prefersHighContrast = window.matchMedia('(prefers-contrast: more)').matches;
    if (prefersHighContrast) {
      this.config.enableHighContrast = true;
    }

    if (this.config.debug) {
      console.log('系统辅助功能设置检测结果', {
        prefersReducedMotion,
        prefersHighContrast
      });
    }
  }

  /**
   * 设置媒体查询监听器
   */
  private setupMediaQueryListeners(): void {
    // 监听减弱动画设置变化
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    reducedMotionQuery.addEventListener('change', (e) => {
      if (this.config.enabled) {
        this.config.enableReducedMotion = e.matches;
        this.applyReducedMotion();
        this.emit(AccessibilityEventType.REDUCED_MOTION_CHANGED, {
          enabled: e.matches
        });
      }
    });

    // 监听高对比度设置变化
    const highContrastQuery = window.matchMedia('(prefers-contrast: more)');
    highContrastQuery.addEventListener('change', (e) => {
      if (this.config.enabled) {
        this.config.enableHighContrast = e.matches;
        this.applyHighContrastMode();
        this.emit(AccessibilityEventType.HIGH_CONTRAST_CHANGED, {
          enabled: e.matches
        });
      }
    });
  }

  /**
   * 设置键盘导航
   */
  private setupKeyboardNavigation(): void {
    // 实现键盘导航逻辑
    document.addEventListener('keydown', (e) => {
      if (!this.config.enabled || !this.config.enableKeyboardNavigation) return;

      // 处理Tab键导航
      if (e.key === 'Tab') {
        // 可以在这里添加自定义的Tab键导航逻辑
      }

      // 处理方向键导航
      if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
        // 可以在这里添加自定义的方向键导航逻辑
      }
    });
  }

  /**
   * 设置焦点指示器
   */
  private setupFocusIndicator(): void {
    if (this.config.enabled && this.config.enableFocusIndicator) {
      // 添加全局焦点样式
      const style = document.createElement('style');
      style.id = 'accessibility-focus-style';
      style.textContent = `
        :focus {
          outline: 3px solid #1890ff !important;
          outline-offset: 2px !important;
        }
      `;
      document.head.appendChild(style);
    } else {
      // 移除全局焦点样式
      const style = document.getElementById('accessibility-focus-style');
      if (style) {
        style.remove();
      }
    }
  }

  /**
   * 应用高对比度模式
   */
  private applyHighContrastMode(): void {
    if (this.config.enabled && this.config.enableHighContrast) {
      // 添加高对比度样式
      const style = document.createElement('style');
      style.id = 'accessibility-high-contrast-style';
      style.textContent = `
        body {
          background-color: #000000 !important;
          color: #ffffff !important;
        }
        a, button, .ant-btn {
          color: #ffff00 !important;
          border-color: #ffff00 !important;
        }
        .ant-btn-primary {
          background-color: #ffff00 !important;
          color: #000000 !important;
        }
        input, textarea, select, .ant-input, .ant-select-selector {
          background-color: #000000 !important;
          color: #ffffff !important;
          border-color: #ffffff !important;
        }
      `;
      document.head.appendChild(style);
    } else {
      // 移除高对比度样式
      const style = document.getElementById('accessibility-high-contrast-style');
      if (style) {
        style.remove();
      }
    }
  }

  /**
   * 应用文本缩放
   */
  private applyTextScaling(): void {
    if (this.config.enabled && this.config.enableTextScaling) {
      // 添加文本缩放样式
      const style = document.createElement('style');
      style.id = 'accessibility-text-scaling-style';
      style.textContent = `
        body {
          font-size: ${this.textScaleFactor * 100}% !important;
        }
      `;
      document.head.appendChild(style);
    } else {
      // 移除文本缩放样式
      const style = document.getElementById('accessibility-text-scaling-style');
      if (style) {
        style.remove();
      }
    }
  }

  /**
   * 应用动画减弱
   */
  private applyReducedMotion(): void {
    if (this.config.enabled && this.config.enableReducedMotion) {
      // 添加动画减弱样式
      const style = document.createElement('style');
      style.id = 'accessibility-reduced-motion-style';
      style.textContent = `
        *, *::before, *::after {
          animation-duration: 0.001s !important;
          animation-delay: 0s !important;
          transition-duration: 0.001s !important;
          transition-delay: 0s !important;
        }
      `;
      document.head.appendChild(style);
    } else {
      // 移除动画减弱样式
      const style = document.getElementById('accessibility-reduced-motion-style');
      if (style) {
        style.remove();
      }
    }
  }

  /**
   * 应用颜色调整
   */
  private applyColorAdjustment(): void {
    if (this.config.enabled && this.config.enableColorAdjustment) {
      // 添加颜色调整样式
      const style = document.createElement('style');
      style.id = 'accessibility-color-adjustment-style';
      style.textContent = `
        html {
          filter: brightness(${this.colorAdjustments.brightness}) 
                 contrast(${this.colorAdjustments.contrast}) 
                 saturate(${this.colorAdjustments.saturation}) !important;
        }
      `;
      document.head.appendChild(style);
    } else {
      // 移除颜色调整样式
      const style = document.getElementById('accessibility-color-adjustment-style');
      if (style) {
        style.remove();
      }
    }
  }

  /**
   * 设置文本缩放比例
   * @param factor 缩放比例
   */
  public setTextScaleFactor(factor: number): void {
    this.textScaleFactor = Math.max(0.5, Math.min(2.0, factor));
    
    if (this.config.enabled && this.config.enableTextScaling) {
      this.applyTextScaling();
    }

    this.emit(AccessibilityEventType.TEXT_SCALING_CHANGED, {
      enabled: this.config.enableTextScaling,
      factor: this.textScaleFactor
    });
  }

  /**
   * 设置颜色调整
   * @param adjustments 颜色调整设置
   */
  public setColorAdjustments(adjustments: Partial<typeof this.colorAdjustments>): void {
    this.colorAdjustments = { ...this.colorAdjustments, ...adjustments };
    
    if (this.config.enabled && this.config.enableColorAdjustment) {
      this.applyColorAdjustment();
    }

    this.emit(AccessibilityEventType.COLOR_ADJUSTMENT_CHANGED, {
      enabled: this.config.enableColorAdjustment,
      adjustments: this.colorAdjustments
    });
  }

  /**
   * 朗读消息（用于屏幕阅读器）
   * @param message 消息内容
   * @param priority 优先级
   */
  public announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    if (!this.config.enabled || !this.config.enableScreenReader) return;

    // 创建或获取朗读区域
    let announcer = document.getElementById('accessibility-announcer');
    if (!announcer) {
      announcer = document.createElement('div');
      announcer.id = 'accessibility-announcer';
      announcer.setAttribute('aria-live', priority);
      announcer.setAttribute('aria-atomic', 'true');
      announcer.style.position = 'absolute';
      announcer.style.width = '1px';
      announcer.style.height = '1px';
      announcer.style.overflow = 'hidden';
      announcer.style.clip = 'rect(0, 0, 0, 0)';
      document.body.appendChild(announcer);
    } else {
      announcer.setAttribute('aria-live', priority);
    }

    // 清空后设置消息，确保屏幕阅读器能够识别内容变化
    announcer.textContent = '';
    setTimeout(() => {
      announcer!.textContent = message;
    }, 50);

    // 发出朗读事件
    this.emit(AccessibilityEventType.ANNOUNCE, {
      message,
      priority
    });

    if (this.config.debug) {
      console.log(`朗读消息 (${priority}): ${message}`);
    }
  }

  /**
   * 获取当前配置
   */
  public getConfig(): AccessibilityConfig {
    return { ...this.config };
  }

  /**
   * 获取文本缩放比例
   */
  public getTextScaleFactor(): number {
    return this.textScaleFactor;
  }

  /**
   * 获取颜色调整设置
   */
  public getColorAdjustments(): typeof this.colorAdjustments {
    return { ...this.colorAdjustments };
  }

  /**
   * 检查是否启用屏幕阅读器支持
   */
  public isScreenReaderEnabled(): boolean {
    return this.config.enabled && this.config.enableScreenReader === true;
  }

  /**
   * 检查是否启用高对比度模式
   */
  public isHighContrastEnabled(): boolean {
    return this.config.enabled && this.config.enableHighContrast === true;
  }

  /**
   * 检查是否启用文本缩放
   */
  public isTextScalingEnabled(): boolean {
    return this.config.enabled && this.config.enableTextScaling === true;
  }

  /**
   * 检查是否启用动画减弱
   */
  public isReducedMotionEnabled(): boolean {
    return this.config.enabled && this.config.enableReducedMotion === true;
  }
}

// 导出单例实例
export default AccessibilityService.getInstance();
