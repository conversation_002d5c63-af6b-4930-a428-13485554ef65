.performance-charts {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .performance-toolbar {
    padding: 12px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .performance-content {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
    
    .ant-tabs {
      height: 100%;
      
      .ant-tabs-content {
        height: 100%;
        
        .ant-tabs-tabpane {
          height: 100%;
          overflow-y: auto;
        }
      }
    }
  }
  
  .chart-controls {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f9f9f9;
    border-radius: 4px;
  }
  
  .chart-wrapper {
    height: 400px;
    margin-bottom: 16px;
  }
  
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 0;
    
    .loading-text {
      margin-top: 16px;
      font-size: 16px;
      color: #1890ff;
    }
  }
  
  // 模拟图表样式
  .chart-container {
    width: 100%;
    height: 100%;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .chart-placeholder {
      text-align: center;
      
      .chart-type {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 8px;
      }
      
      .chart-data-info {
        color: #888;
      }
    }
  }
}

// 暗色主题样式
.dark-theme {
  .performance-charts {
    .performance-toolbar {
      background-color: #1e1e1e;
      border-bottom: 1px solid #303030;
    }
    
    .chart-controls {
      background-color: #2a2a2a;
    }
    
    .chart-container {
      border-color: #303030;
      background-color: #1e1e1e;
    }
  }
}
