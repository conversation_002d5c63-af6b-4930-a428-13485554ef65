/**
 * 场景优化建议面板样式
 */

.scene-optimization-panel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .optimization-toolbar {
    padding: 12px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
    
    .ant-space {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
  }
  
  .optimization-content {
    padding: 16px;
    overflow: auto;
    flex: 1;
  }
  
  .optimization-score {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px;
    
    .ant-typography {
      margin-bottom: 16px;
    }
    
    .optimization-score-text {
      margin-top: 16px;
      width: 100%;
      max-width: 400px;
    }
  }
  
  .optimization-item-header {
    display: flex;
    align-items: center;
    
    .optimization-item-title {
      margin-left: 8px;
      margin-right: 8px;
      font-weight: 500;
    }
  }
  
  .optimization-item-content {
    padding: 8px 16px;
    
    .ant-typography {
      margin-bottom: 8px;
    }
  }
  
  .optimization-tips {
    list-style: none;
    padding-left: 0;
    
    li {
      margin-bottom: 8px;
      display: flex;
      align-items: flex-start;
      
      .optimization-tip-icon {
        margin-right: 8px;
        margin-top: 4px;
        color: #1890ff;
      }
    }
  }
  
  .optimization-warning {
    color: #faad14;
  }
  
  .optimization-error {
    color: #f5222d;
  }
  
  .optimization-good {
    color: #52c41a;
  }
}

// 暗色主题样式
.dark-theme {
  .scene-optimization-panel {
    .optimization-toolbar {
      background-color: #1e1e1e;
      border-bottom: 1px solid #303030;
    }
  }
}
