/**
 * 冲突预测面板组件
 * 用于显示基于意图的冲突预测
 */
import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import {
  Card,
  List,
  Button,
  Space,
  Tooltip,
  Divider,
  Typography,
  Tag,
  Alert,
  Switch,
  Slider,
  Empty,
  Badge,
  Progress,
  Collapse,
  Tabs
} from 'antd';
import {
  WarningOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CheckOutlined,
  CloseOutlined,
  SettingOutlined,
  BulbOutlined,
  RadarChartOutlined
} from '@ant-design/icons';
import { RootState } from '../../store';
import {
  PredictedConflict,
  EditingIntent,
  intentBasedConflictPreventionService
} from '../../services/IntentBasedConflictPreventionService';
import {
  setSelectedPredictedConflictId,
  setNotificationEnabled,
  setPredictionEnabled,
  setPredictionThreshold,
  resolvePredictedConflict,
  clearResolvedPredictedConflicts
} from '../../store/collaboration/conflictPredictionSlice';
import { formatDateTime, formatRelativeTime } from '../../utils/formatters';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;

/**
 * 冲突预测面板组件
 */
const ConflictPredictionPanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 从Redux获取状态
  const predictedConflicts = useSelector((state: RootState) => state.conflictPrediction.predictedConflicts);
  const selectedPredictedConflictId = useSelector((state: RootState) => state.conflictPrediction.selectedPredictedConflictId);
  const notificationEnabled = useSelector((state: RootState) => state.conflictPrediction.notificationEnabled);
  const predictionEnabled = useSelector((state: RootState) => state.conflictPrediction.predictionEnabled);
  const predictionThreshold = useSelector((state: RootState) => state.conflictPrediction.predictionThreshold);

  // 本地状态
  const [activeTab, setActiveTab] = useState<string>('pending');
  const [showSettings, setShowSettings] = useState<boolean>(false);

  // 过滤冲突
  const pendingConflicts = predictedConflicts.filter(c => !c.resolved);
  const resolvedConflicts = predictedConflicts.filter(c => c.resolved);

  // 选中的冲突
  const selectedConflict = predictedConflicts.find(c => c.id === selectedPredictedConflictId);

  // 同步Redux状态到服务
  useEffect(() => {
    intentBasedConflictPreventionService.setEnabled(predictionEnabled);
  }, [predictionEnabled]);

  useEffect(() => {
    intentBasedConflictPreventionService.setConflictPredictionThreshold(predictionThreshold);
  }, [predictionThreshold]);

  // 处理启用/禁用预测
  const handleTogglePrediction = (checked: boolean) => {
    dispatch(setPredictionEnabled(checked));
  };

  // 处理启用/禁用通知
  const handleToggleNotification = (checked: boolean) => {
    dispatch(setNotificationEnabled(checked));
  };

  // 处理阈值变更
  const handleThresholdChange = (value: number) => {
    dispatch(setPredictionThreshold(value));
  };

  // 处理选择冲突
  const handleSelectConflict = (id: string) => {
    dispatch(setSelectedPredictedConflictId(id));
  };

  // 处理解决冲突
  const handleResolveConflict = (id: string) => {
    dispatch(resolvePredictedConflict(id));
  };

  // 处理清除已解决的冲突
  const handleClearResolved = () => {
    dispatch(clearResolvedPredictedConflicts());
  };

  // 渲染冲突概率
  const renderProbability = (probability: number) => {
    let color = 'green';

    if (probability >= 0.8) {
      color = 'red';
    } else if (probability >= 0.6) {
      color = 'orange';
    } else if (probability >= 0.4) {
      color = 'blue';
    }

    return (
      <Tooltip title={`冲突概率: ${Math.round(probability * 100)}%`}>
        <Progress
          percent={Math.round(probability * 100)}
          size="small"
          strokeColor={color}
          showInfo={false}
        />
      </Tooltip>
    );
  };

  // 渲染冲突类型标签
  const renderConflictTypeTag = (type: string) => {
    let color = 'blue';
    let text = '属性冲突';

    switch (type) {
      case 'entity_conflict':
        color = 'purple';
        text = '实体冲突';
        break;

      case 'component_conflict':
        color = 'cyan';
        text = '组件冲突';
        break;

      case 'deletion_conflict':
        color = 'red';
        text = '删除冲突';
        break;

      case 'scene_conflict':
        color = 'orange';
        text = '场景冲突';
        break;
    }

    return <Tag color={color}>{text}</Tag>;
  };

  // 渲染冲突项
  const renderConflictItem = (conflict: PredictedConflict) => {
    const { id, type, userA, userB, probability, createdAt, resolved, resolvedAt, entityId } = conflict;
    const isSelected = id === selectedPredictedConflictId;

    return (
      <List.Item
        key={id}
        className={`conflict-item ${isSelected ? 'selected' : ''} ${resolved ? 'resolved' : ''}`}
        actions={[
          <Button
            key="view"
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleSelectConflict(id)}
          />,
          !resolved && (
            <Button
              key="resolve"
              type="text"
              icon={<CheckOutlined />}
              onClick={() => handleResolveConflict(id)}
            />
          )
        ]}
      >
        <List.Item.Meta
          avatar={
            <Badge status={resolved ? 'success' : 'warning'} />
          }
          title={
            <Space>
              {renderConflictTypeTag(type)}
              <Text strong>
                {userA.name} 和 {userB.name}
              </Text>
            </Space>
          }
          description={
            <Space direction="vertical" style={{ width: '100%' }}>
              {entityId && (
                <Text type="secondary">实体: {entityId}</Text>
              )}
              {renderProbability(probability)}
              <Text type="secondary">
                <ClockCircleOutlined /> {formatRelativeTime(createdAt)}
              </Text>
            </Space>
          }
        />
      </List.Item>
    );
  };

  // 渲染设置面板
  const renderSettingsPanel = () => {
    return (
      <div className="settings-panel">
        <Title level={5}>{t('collaboration.conflictPrediction.settings')}</Title>

        <div className="setting-item">
          <Text>{t('collaboration.conflictPrediction.enablePrediction')}</Text>
          <Switch
            checked={predictionEnabled}
            onChange={handleTogglePrediction}
          />
        </div>

        <div className="setting-item">
          <Text>{t('collaboration.conflictPrediction.enableNotification')}</Text>
          <Switch
            checked={notificationEnabled}
            onChange={handleToggleNotification}
            disabled={!predictionEnabled}
          />
        </div>

        <div className="setting-item">
          <Text>{t('collaboration.conflictPrediction.threshold')}</Text>
          <Slider
            min={0.1}
            max={1}
            step={0.05}
            value={predictionThreshold}
            onChange={handleThresholdChange}
            disabled={!predictionEnabled}
            tooltip={{ formatter: (value) => `${Math.round(value * 100)}%` }}
          />
        </div>
      </div>
    );
  };

  // 渲染意图详情
  const renderIntentDetails = (intent: EditingIntent) => {
    return (
      <div className="intent-details">
        <Space direction="vertical" style={{ width: '100%' }}>
          <div className="intent-info">
            <Text strong>{intent.userName}</Text>
            <Text type="secondary">
              {formatRelativeTime(intent.startTime)}
            </Text>
          </div>

          <div className="intent-type">
            <Tag color="blue">{intent.type}</Tag>
            <Text type="secondary">置信度: {Math.round(intent.confidence * 100)}%</Text>
          </div>

          {intent.entityId && (
            <div className="intent-entity">
              <Text>实体: {intent.entityId}</Text>
            </div>
          )}

          {intent.componentId && (
            <div className="intent-component">
              <Text>组件: {intent.componentId}</Text>
            </div>
          )}

          {intent.propertyPath && intent.propertyPath.length > 0 && (
            <div className="intent-property">
              <Text>属性: {intent.propertyPath.join('.')}</Text>
            </div>
          )}

          {intent.resourceId && (
            <div className="intent-resource">
              <Text>资源: {intent.resourceId}</Text>
            </div>
          )}
        </Space>
      </div>
    );
  };

  // 渲染选中的冲突详情
  const renderSelectedConflictDetails = () => {
    if (!selectedConflict) {
      return (
        <Empty description={t('collaboration.conflictPrediction.noSelectedConflict')} />
      );
    }

    const { type, userA, userB, probability, createdAt, resolved, resolvedAt, entityId, componentId, propertyPath } = selectedConflict;

    return (
      <div className="selected-conflict-details">
        <Alert
          type={resolved ? 'success' : 'warning'}
          message={
            <Space>
              {renderConflictTypeTag(type)}
              <Text strong>
                {t('collaboration.conflictPrediction.conflictBetween')} {userA.name} {t('collaboration.conflictPrediction.and')} {userB.name}
              </Text>
            </Space>
          }
          description={
            <Space direction="vertical" style={{ width: '100%' }}>
              <div className="conflict-probability">
                <Text>{t('collaboration.conflictPrediction.probability')}: {Math.round(probability * 100)}%</Text>
                {renderProbability(probability)}
              </div>

              <div className="conflict-time">
                <Text type="secondary">
                  <ClockCircleOutlined /> {formatDateTime(createdAt)}
                </Text>
                {resolved && resolvedAt && (
                  <Text type="secondary">
                    <CheckOutlined /> {t('collaboration.conflictPrediction.resolvedAt')} {formatDateTime(resolvedAt)}
                  </Text>
                )}
              </div>

              {entityId && (
                <div className="conflict-entity">
                  <Text>{t('collaboration.conflictPrediction.entity')}: {entityId}</Text>
                </div>
              )}

              {componentId && (
                <div className="conflict-component">
                  <Text>{t('collaboration.conflictPrediction.component')}: {componentId}</Text>
                </div>
              )}

              {propertyPath && propertyPath.length > 0 && (
                <div className="conflict-property">
                  <Text>{t('collaboration.conflictPrediction.property')}: {propertyPath.join('.')}</Text>
                </div>
              )}
            </Space>
          }
          showIcon
        />

        <Divider>{t('collaboration.conflictPrediction.userIntents')}</Divider>

        <div className="user-intents">
          <div className="user-a-intent">
            <Title level={5}>{userA.name} {t('collaboration.conflictPrediction.intent')}</Title>
            {renderIntentDetails(userA.intent)}
          </div>

          <div className="user-b-intent">
            <Title level={5}>{userB.name} {t('collaboration.conflictPrediction.intent')}</Title>
            {renderIntentDetails(userB.intent)}
          </div>
        </div>

        {!resolved && (
          <div className="conflict-actions">
            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={() => handleResolveConflict(selectedConflict.id)}
            >
              {t('collaboration.conflictPrediction.markAsResolved')}
            </Button>
          </div>
        )}
      </div>
    );
  };

  return (
    <Card
      title={
        <Space>
          <RadarChartOutlined />
          <span>{t('collaboration.conflictPrediction.title')}</span>
          {pendingConflicts.length > 0 && (
            <Badge count={pendingConflicts.length} />
          )}
        </Space>
      }
      extra={
        <Space>
          <Tooltip title={t('collaboration.conflictPrediction.settings')}>
            <Button
              type={showSettings ? 'primary' : 'default'}
              shape="circle"
              icon={<SettingOutlined />}
              onClick={() => setShowSettings(!showSettings)}
            />
          </Tooltip>
          {resolvedConflicts.length > 0 && (
            <Button
              size="small"
              onClick={handleClearResolved}
            >
              {t('collaboration.conflictPrediction.clearResolved')}
            </Button>
          )}
        </Space>
      }
      className="conflict-prediction-panel"
    >
      {showSettings && (
        <>
          {renderSettingsPanel()}
          <Divider />
        </>
      )}

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <WarningOutlined />
              {t('collaboration.conflictPrediction.pending')}
              {pendingConflicts.length > 0 && (
                <Badge count={pendingConflicts.length} style={{ marginLeft: 8 }} />
              )}
            </span>
          }
          key="pending"
        >
          {pendingConflicts.length > 0 ? (
            <List
              dataSource={pendingConflicts}
              renderItem={renderConflictItem}
              className="conflict-list"
            />
          ) : (
            <Empty description={t('collaboration.conflictPrediction.noPendingConflicts')} />
          )}
        </TabPane>

        <TabPane
          tab={
            <span>
              <CheckOutlined />
              {t('collaboration.conflictPrediction.resolved')}
              {resolvedConflicts.length > 0 && (
                <Badge count={resolvedConflicts.length} style={{ marginLeft: 8 }} />
              )}
            </span>
          }
          key="resolved"
        >
          {resolvedConflicts.length > 0 ? (
            <List
              dataSource={resolvedConflicts}
              renderItem={renderConflictItem}
              className="conflict-list"
            />
          ) : (
            <Empty description={t('collaboration.conflictPrediction.noResolvedConflicts')} />
          )}
        </TabPane>

        <TabPane
          tab={
            <span>
              <BulbOutlined />
              {t('collaboration.conflictPrediction.details')}
            </span>
          }
          key="details"
        >
          {renderSelectedConflictDetails()}
        </TabPane>
      </Tabs>

      <style jsx>{`
        .conflict-prediction-panel {
          margin-bottom: 16px;
        }

        .settings-panel {
          padding: 8px;
          background-color: #f7f7f7;
          border-radius: 4px;
        }

        .setting-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
        }

        .conflict-list {
          max-height: 400px;
          overflow-y: auto;
        }

        .conflict-item {
          border-left: 3px solid transparent;
          transition: all 0.3s;
        }

        .conflict-item:hover {
          background-color: #f5f5f5;
        }

        .conflict-item.selected {
          border-left-color: #1890ff;
          background-color: #e6f7ff;
        }

        .conflict-item.resolved {
          opacity: 0.7;
        }

        .selected-conflict-details {
          padding: 8px;
        }

        .user-intents {
          display: flex;
          flex-direction: row;
          gap: 16px;
          margin-top: 16px;
        }

        .user-a-intent, .user-b-intent {
          flex: 1;
          padding: 16px;
          border: 1px solid #eee;
          border-radius: 4px;
        }

        .user-a-intent {
          background-color: #e6f7ff;
        }

        .user-b-intent {
          background-color: #f6ffed;
        }

        .conflict-actions {
          margin-top: 16px;
          display: flex;
          justify-content: flex-end;
        }

        .intent-details {
          margin-top: 8px;
        }

        .intent-info, .intent-type, .intent-entity, .intent-component, .intent-property, .intent-resource {
          margin-bottom: 8px;
        }
      `}</style>
    </Card>
  );
}

export default ConflictPredictionPanel;