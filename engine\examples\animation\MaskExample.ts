/**
 * 遮罩示例
 * 展示不同遮罩功能的使用方法
 */
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { AnimationBlender, BlendMode, BlendLayer } from '../../src/animation/AnimationBlender';
import { Animator } from '../../src/animation/Animator';
import { AnimationMask, MaskType, MaskWeightType, BoneGroupType } from '../../src/animation/AnimationMask';
import { GUI } from 'dat.gui';

// 场景类
class MaskExample {
  // 场景
  private scene: THREE.Scene;
  // 相机
  private camera: THREE.PerspectiveCamera;
  // 渲染器
  private renderer: THREE.WebGLRenderer;
  // 控制器
  private controls: OrbitControls;
  // 时钟
  private clock: THREE.Clock;
  // 模型
  private model: THREE.Object3D | null = null;
  // 混合器
  private mixer: THREE.AnimationMixer | null = null;
  // 动画控制器
  private animator: Animator | null = null;
  // 混合器
  private blender: AnimationBlender | null = null;
  // 动画片段
  private animations: THREE.AnimationClip[] = [];
  // GUI
  private gui: GUI;
  // 混合层
  private layers: BlendLayer[] = [];
  // 遮罩
  private masks: Map<string, AnimationMask> = new Map();
  // 骨骼列表
  private bones: string[] = [];
  // 遮罩类型选项
  private maskTypeOptions = {
    INCLUDE: MaskType.INCLUDE,
    EXCLUDE: MaskType.EXCLUDE,
    HIERARCHY: MaskType.HIERARCHY,
    INVERSE_HIERARCHY: MaskType.INVERSE_HIERARCHY,
    GROUP: MaskType.GROUP,
    BLEND: MaskType.BLEND,
    TRANSITION: MaskType.TRANSITION
  };
  // 权重类型选项
  private weightTypeOptions = {
    BINARY: MaskWeightType.BINARY,
    SMOOTH: MaskWeightType.SMOOTH,
    DISTANCE: MaskWeightType.DISTANCE,
    GRADIENT: MaskWeightType.GRADIENT,
    DYNAMIC: MaskWeightType.DYNAMIC,
    CURVE: MaskWeightType.CURVE,
    BLEND: MaskWeightType.BLEND
  };
  // 骨骼组类型选项
  private boneGroupOptions = {
    HEAD: BoneGroupType.HEAD,
    TORSO: BoneGroupType.TORSO,
    LEFT_ARM: BoneGroupType.LEFT_ARM,
    RIGHT_ARM: BoneGroupType.RIGHT_ARM,
    LEFT_LEG: BoneGroupType.LEFT_LEG,
    RIGHT_LEG: BoneGroupType.RIGHT_LEG,
    LEFT_HAND: BoneGroupType.LEFT_HAND,
    RIGHT_HAND: BoneGroupType.RIGHT_HAND,
    LEFT_FOOT: BoneGroupType.LEFT_FOOT,
    RIGHT_FOOT: BoneGroupType.RIGHT_FOOT,
    UPPER_BODY: BoneGroupType.UPPER_BODY,
    LOWER_BODY: BoneGroupType.LOWER_BODY,
    LEFT_SIDE: BoneGroupType.LEFT_SIDE,
    RIGHT_SIDE: BoneGroupType.RIGHT_SIDE
  };

  /**
   * 构造函数
   */
  constructor() {
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0xa0a0a0);
    this.scene.fog = new THREE.Fog(0xa0a0a0, 10, 50);

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 100);
    this.camera.position.set(0, 2, 5);
    this.camera.lookAt(0, 1, 0);

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    document.body.appendChild(this.renderer.domElement);

    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 1, 0);
    this.controls.update();

    // 创建时钟
    this.clock = new THREE.Clock();

    // 创建灯光
    this.setupLights();

    // 创建地面
    this.setupGround();

    // 创建GUI
    this.gui = new GUI();
    this.gui.width = 300;

    // 加载模型
    this.loadModel();

    // 添加窗口大小调整监听器
    window.addEventListener('resize', this.onWindowResize.bind(this));

    // 开始动画循环
    this.animate();
  }

  /**
   * 设置灯光
   */
  private setupLights(): void {
    // 半球光
    const hemiLight = new THREE.HemisphereLight(0xffffff, 0x444444, 0.6);
    hemiLight.position.set(0, 20, 0);
    this.scene.add(hemiLight);

    // 方向光
    const dirLight = new THREE.DirectionalLight(0xffffff, 0.8);
    dirLight.position.set(3, 10, 10);
    dirLight.castShadow = true;
    dirLight.shadow.camera.top = 2;
    dirLight.shadow.camera.bottom = -2;
    dirLight.shadow.camera.left = -2;
    dirLight.shadow.camera.right = 2;
    dirLight.shadow.camera.near = 0.1;
    dirLight.shadow.camera.far = 40;
    this.scene.add(dirLight);
  }

  /**
   * 设置地面
   */
  private setupGround(): void {
    // 地面
    const ground = new THREE.Mesh(
      new THREE.PlaneGeometry(100, 100),
      new THREE.MeshPhongMaterial({ color: 0x999999, depthWrite: false })
    );
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    this.scene.add(ground);

    // 网格
    const grid = new THREE.GridHelper(100, 100, 0x000000, 0x000000);
    grid.material.opacity = 0.2;
    grid.material.transparent = true;
    this.scene.add(grid);
  }

  /**
   * 加载模型
   */
  private loadModel(): void {
    const loader = new GLTFLoader();
    loader.load(
      'models/gltf/Xbot.glb',
      (gltf) => {
        this.model = gltf.scene;
        this.model.traverse((object) => {
          if ((object as THREE.Mesh).isMesh) {
            object.castShadow = true;
          }
          if ((object as THREE.Bone).isBone) {
            this.bones.push(object.name);
          }
        });
        this.scene.add(this.model);

        // 获取动画
        this.animations = gltf.animations;

        // 创建混合器
        this.mixer = new THREE.AnimationMixer(this.model);

        // 创建动画控制器
        this.animator = new Animator(this.mixer, this.animations);

        // 创建混合器
        this.blender = new AnimationBlender(this.animator);

        // 创建默认遮罩
        this.createDefaultMasks();

        // 设置默认混合层
        this.setupDefaultLayers();

        // 设置GUI
        this.setupGUI();
      },
      undefined,
      (error) => {
        console.error('加载模型时出错:', error);
      }
    );
  }

  /**
   * 创建默认遮罩
   */
  private createDefaultMasks(): void {
    // 创建上半身遮罩
    const upperBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.UPPER_BODY);
    this.masks.set('upperBody', upperBodyMask);

    // 创建下半身遮罩
    const lowerBodyMask = AnimationMask.createBoneGroupMask(BoneGroupType.LOWER_BODY);
    this.masks.set('lowerBody', lowerBodyMask);

    // 创建左侧遮罩
    const leftSideMask = AnimationMask.createBoneGroupMask(BoneGroupType.LEFT_SIDE);
    this.masks.set('leftSide', leftSideMask);

    // 创建右侧遮罩
    const rightSideMask = AnimationMask.createBoneGroupMask(BoneGroupType.RIGHT_SIDE);
    this.masks.set('rightSide', rightSideMask);
  }

  /**
   * 设置默认混合层
   */
  private setupDefaultLayers(): void {
    if (!this.blender) return;

    // 清空混合层
    this.blender.clearLayers();
    this.layers = [];

    // 添加默认混合层
    const idleLayer: BlendLayer = {
      clipName: 'idle',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.OVERRIDE
    };
    this.blender.addLayer(idleLayer);
    this.layers.push(idleLayer);
  }

  /**
   * 设置GUI
   */
  private setupGUI(): void {
    if (!this.animator || !this.blender) return;

    // 清空GUI
    this.gui.destroy();
    this.gui = new GUI();
    this.gui.width = 300;

    // 添加动画列表
    const animationFolder = this.gui.addFolder('动画');
    const animationNames = this.animations.map(clip => clip.name);
    const animationController = {
      animation: animationNames[0],
      play: () => {
        const layer: BlendLayer = {
          clipName: animationController.animation,
          weight: 1.0,
          timeScale: 1.0,
          blendMode: BlendMode.OVERRIDE
        };
        this.blender!.clearLayers();
        this.blender!.addLayer(layer);
        this.layers = [layer];
        this.setupLayersGUI();
      }
    };
    animationFolder.add(animationController, 'animation', animationNames);
    animationFolder.add(animationController, 'play');
    animationFolder.open();

    // 添加混合层控制
    this.setupLayersGUI();

    // 添加遮罩控制
    this.setupMasksGUI();

    // 添加遮罩示例
    const examplesFolder = this.gui.addFolder('遮罩示例');
    const exampleController = {
      showUpperLower: () => this.showUpperLowerExample(),
      showLeftRight: () => this.showLeftRightExample(),
      showBlendMask: () => this.showBlendMaskExample(),
      showTransitionMask: () => this.showTransitionMaskExample()
    };
    examplesFolder.add(exampleController, 'showUpperLower').name('上下身示例');
    examplesFolder.add(exampleController, 'showLeftRight').name('左右侧示例');
    examplesFolder.add(exampleController, 'showBlendMask').name('遮罩混合示例');
    examplesFolder.add(exampleController, 'showTransitionMask').name('遮罩过渡示例');
    examplesFolder.open();
  }

  /**
   * 设置混合层GUI
   */
  private setupLayersGUI(): void {
    if (!this.animator || !this.blender) return;

    // 移除旧的混合层文件夹
    const oldFolder = this.gui.__folders['混合层'];
    if (oldFolder) {
      this.gui.removeFolder(oldFolder);
    }

    // 添加混合层文件夹
    const layersFolder = this.gui.addFolder('混合层');

    // 添加混合层控制
    for (let i = 0; i < this.layers.length; i++) {
      const layer = this.layers[i];
      const layerFolder = layersFolder.addFolder(`层 ${i + 1}: ${layer.clipName}`);
      layerFolder.add(layer, 'weight', 0, 1, 0.01).name('权重').onChange(() => this.blender!.update(0));
      layerFolder.add(layer, 'timeScale', 0.1, 2, 0.1).name('时间缩放').onChange(() => this.blender!.update(0));

      // 添加遮罩选择
      const maskNames = ['无遮罩', ...Array.from(this.masks.keys())];
      const maskController = {
        mask: layer.mask ? maskNames.find(name => this.masks.get(name)?.getBones().toString() === layer.mask?.toString()) || '无遮罩' : '无遮罩',
        updateMask: () => {
          if (maskController.mask === '无遮罩') {
            delete layer.mask;
          } else {
            const mask = this.masks.get(maskController.mask);
            if (mask) {
              layer.mask = mask.getBones();
            }
          }
          this.blender!.update(0);
        }
      };
      layerFolder.add(maskController, 'mask', maskNames).name('遮罩').onChange(maskController.updateMask);
      
      layerFolder.open();
    }

    // 添加新层按钮
    const layerController = {
      addLayer: () => {
        if (this.animations.length > 0) {
          const layer: BlendLayer = {
            clipName: this.animations[0].name,
            weight: 1.0,
            timeScale: 1.0,
            blendMode: BlendMode.ADDITIVE
          };
          this.blender!.addLayer(layer);
          this.layers.push(layer);
          this.setupLayersGUI();
        }
      },
      clearLayers: () => {
        this.blender!.clearLayers();
        this.layers = [];
        this.setupLayersGUI();
      }
    };
    layersFolder.add(layerController, 'addLayer').name('添加层');
    layersFolder.add(layerController, 'clearLayers').name('清空层');
    layersFolder.open();
  }

  /**
   * 设置遮罩GUI
   */
  private setupMasksGUI(): void {
    // 移除旧的遮罩文件夹
    const oldFolder = this.gui.__folders['遮罩'];
    if (oldFolder) {
      this.gui.removeFolder(oldFolder);
    }

    // 添加遮罩文件夹
    const masksFolder = this.gui.addFolder('遮罩');

    // 添加遮罩控制
    for (const [name, mask] of this.masks.entries()) {
      const maskFolder = masksFolder.addFolder(name);
      maskFolder.add({ type: mask.getType() }, 'type', this.maskTypeOptions).name('类型').disable();
      maskFolder.add({ weightType: mask.getWeightType() }, 'weightType', this.weightTypeOptions).name('权重类型').disable();
      maskFolder.open();
    }

    // 添加新遮罩按钮
    const maskController = {
      name: '新遮罩',
      type: MaskType.INCLUDE,
      weightType: MaskWeightType.BINARY,
      boneGroup: BoneGroupType.HEAD,
      createMask: () => {
        if (maskController.type === MaskType.GROUP) {
          const mask = AnimationMask.createBoneGroupMask(maskController.boneGroup);
          this.masks.set(maskController.name, mask);
        } else {
          const mask = new AnimationMask({
            name: maskController.name,
            type: maskController.type,
            weightType: maskController.weightType,
            bones: []
          });
          this.masks.set(maskController.name, mask);
        }
        this.setupMasksGUI();
      },
      clearMasks: () => {
        this.masks.clear();
        this.createDefaultMasks();
        this.setupMasksGUI();
      }
    };
    masksFolder.add(maskController, 'name').name('名称');
    masksFolder.add(maskController, 'type', this.maskTypeOptions).name('类型');
    masksFolder.add(maskController, 'weightType', this.weightTypeOptions).name('权重类型');
    masksFolder.add(maskController, 'boneGroup', this.boneGroupOptions).name('骨骼组');
    masksFolder.add(maskController, 'createMask').name('创建遮罩');
    masksFolder.add(maskController, 'clearMasks').name('重置遮罩');
    masksFolder.open();
  }

  /**
   * 显示上下身示例
   */
  private showUpperLowerExample(): void {
    if (!this.blender) return;

    // 清空混合层
    this.blender.clearLayers();
    this.layers = [];

    // 添加上半身动画层
    const upperLayer: BlendLayer = {
      clipName: 'wave',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.OVERRIDE,
      mask: this.masks.get('upperBody')?.getBones()
    };
    this.blender.addLayer(upperLayer);
    this.layers.push(upperLayer);

    // 添加下半身动画层
    const lowerLayer: BlendLayer = {
      clipName: 'walk',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.OVERRIDE,
      mask: this.masks.get('lowerBody')?.getBones()
    };
    this.blender.addLayer(lowerLayer);
    this.layers.push(lowerLayer);

    // 更新GUI
    this.setupLayersGUI();
  }

  /**
   * 显示左右侧示例
   */
  private showLeftRightExample(): void {
    if (!this.blender) return;

    // 清空混合层
    this.blender.clearLayers();
    this.layers = [];

    // 添加左侧动画层
    const leftLayer: BlendLayer = {
      clipName: 'walk',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.OVERRIDE,
      mask: this.masks.get('leftSide')?.getBones()
    };
    this.blender.addLayer(leftLayer);
    this.layers.push(leftLayer);

    // 添加右侧动画层
    const rightLayer: BlendLayer = {
      clipName: 'run',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.OVERRIDE,
      mask: this.masks.get('rightSide')?.getBones()
    };
    this.blender.addLayer(rightLayer);
    this.layers.push(rightLayer);

    // 更新GUI
    this.setupLayersGUI();
  }

  /**
   * 显示遮罩混合示例
   */
  private showBlendMaskExample(): void {
    if (!this.blender) return;

    // 清空混合层
    this.blender.clearLayers();
    this.layers = [];

    // 创建混合遮罩
    const upperBodyMask = this.masks.get('upperBody')!;
    const lowerBodyMask = this.masks.get('lowerBody')!;
    const blendMask = AnimationMask.createBlendMask(
      [upperBodyMask, lowerBodyMask],
      [0.7, 0.3],
      true
    );
    this.masks.set('blendMask', blendMask);

    // 添加第一个动画层
    const layer1: BlendLayer = {
      clipName: 'wave',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.OVERRIDE,
      mask: blendMask.getBones()
    };
    this.blender.addLayer(layer1);
    this.layers.push(layer1);

    // 更新GUI
    this.setupLayersGUI();
    this.setupMasksGUI();

    // 添加混合参数控制
    const blendFolder = this.gui.addFolder('混合参数');
    const blendController = {
      upperWeight: 0.7,
      lowerWeight: 0.3,
      updateWeights: () => {
        blendMask.setBlendParams(
          [upperBodyMask, lowerBodyMask],
          [blendController.upperWeight, blendController.lowerWeight],
          true
        );
        this.blender!.update(0);
      }
    };
    blendFolder.add(blendController, 'upperWeight', 0, 1, 0.01).name('上半身权重').onChange(blendController.updateWeights);
    blendFolder.add(blendController, 'lowerWeight', 0, 1, 0.01).name('下半身权重').onChange(blendController.updateWeights);
    blendFolder.open();
  }

  /**
   * 显示遮罩过渡示例
   */
  private showTransitionMaskExample(): void {
    if (!this.blender) return;

    // 清空混合层
    this.blender.clearLayers();
    this.layers = [];

    // 创建过渡遮罩
    const upperBodyMask = this.masks.get('upperBody')!;
    const lowerBodyMask = this.masks.get('lowerBody')!;
    const transitionMask = AnimationMask.createTransitionMask(
      upperBodyMask,
      lowerBodyMask,
      0.5
    );
    this.masks.set('transitionMask', transitionMask);

    // 添加第一个动画层
    const layer1: BlendLayer = {
      clipName: 'wave',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.OVERRIDE
    };
    this.blender.addLayer(layer1);
    this.layers.push(layer1);

    // 添加第二个动画层
    const layer2: BlendLayer = {
      clipName: 'walk',
      weight: 1.0,
      timeScale: 1.0,
      blendMode: BlendMode.OVERRIDE,
      mask: transitionMask.getBones()
    };
    this.blender.addLayer(layer2);
    this.layers.push(layer2);

    // 更新GUI
    this.setupLayersGUI();
    this.setupMasksGUI();

    // 添加过渡参数控制
    const transitionFolder = this.gui.addFolder('过渡参数');
    const transitionController = {
      progress: 0.5,
      updateProgress: () => {
        transitionMask.updateTransitionProgress(transitionController.progress);
        this.blender!.update(0);
      }
    };
    transitionFolder.add(transitionController, 'progress', 0, 1, 0.01).name('过渡进度').onChange(transitionController.updateProgress);
    transitionFolder.open();
  }

  /**
   * 窗口大小调整处理函数
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 动画循环
   */
  private animate(): void {
    requestAnimationFrame(this.animate.bind(this));

    const delta = this.clock.getDelta();

    // 更新控制器
    this.controls.update();

    // 更新混合器
    if (this.mixer) {
      this.mixer.update(delta);
    }

    // 更新混合器
    if (this.blender) {
      this.blender.update(delta);
    }

    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }
}

// 创建示例
new MaskExample();
