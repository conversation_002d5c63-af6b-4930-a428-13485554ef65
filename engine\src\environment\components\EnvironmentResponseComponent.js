"use strict";
/**
 * 环境响应组件
 *
 * 该组件定义实体如何响应环境变化，包括动画、特效、行为等。
 * 可以附加到角色或其他需要对环境做出响应的实体上。
 */
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnvironmentResponseComponent = exports.ResponsePriority = exports.ResponseType = void 0;
var Component_1 = require("../../core/Component");
/**
 * 响应类型枚举
 */
var ResponseType;
(function (ResponseType) {
    /** 动画响应 */
    ResponseType["ANIMATION"] = "animation";
    /** 特效响应 */
    ResponseType["EFFECT"] = "effect";
    /** 声音响应 */
    ResponseType["SOUND"] = "sound";
    /** 行为响应 */
    ResponseType["BEHAVIOR"] = "behavior";
    /** 自定义响应 */
    ResponseType["CUSTOM"] = "custom";
    /** 物理响应 */
    ResponseType["PHYSICS"] = "physics";
    /** 姿势响应 */
    ResponseType["POSE"] = "pose";
    /** 面部表情响应 */
    ResponseType["FACIAL"] = "facial";
    /** 对话响应 */
    ResponseType["DIALOGUE"] = "dialogue";
    /** 移动响应 */
    ResponseType["LOCOMOTION"] = "locomotion";
    /** 状态变化响应 */
    ResponseType["STATE_CHANGE"] = "state_change";
    /** 属性变化响应 */
    ResponseType["PROPERTY_CHANGE"] = "property_change";
    /** 材质变化响应 */
    ResponseType["MATERIAL_CHANGE"] = "material_change";
    /** 粒子响应 */
    ResponseType["PARTICLE"] = "particle";
    /** 光照响应 */
    ResponseType["LIGHTING"] = "lighting";
})(ResponseType || (exports.ResponseType = ResponseType = {}));
/**
 * 响应优先级枚举
 */
var ResponsePriority;
(function (ResponsePriority) {
    ResponsePriority[ResponsePriority["LOW"] = 0] = "LOW";
    ResponsePriority[ResponsePriority["MEDIUM"] = 1] = "MEDIUM";
    ResponsePriority[ResponsePriority["HIGH"] = 2] = "HIGH";
    ResponsePriority[ResponsePriority["CRITICAL"] = 3] = "CRITICAL";
})(ResponsePriority || (exports.ResponsePriority = ResponsePriority = {}));
/**
 * 环境响应组件
 */
var EnvironmentResponseComponent = /** @class */ (function (_super) {
    __extends(EnvironmentResponseComponent, _super);
    /**
     * 构造函数
     * @param config 配置
     */
    function EnvironmentResponseComponent(config) {
        if (config === void 0) { config = {}; }
        var _this = _super.call(this, 'EnvironmentResponseComponent') || this;
        // 响应规则
        _this.rules = [];
        // 当前活动的响应
        _this.activeResponses = new Map();
        // 响应变化回调函数
        _this.onResponseChangeCallbacks = [];
        // 设置默认配置
        _this.config = __assign({ autoRespond: true, debug: false, rules: [] }, config);
        // 初始化规则
        _this.rules = _this.config.rules || [];
        return _this;
    }
    /**
     * 添加响应规则
     * @param rule 响应规则
     */
    EnvironmentResponseComponent.prototype.addRule = function (rule) {
        // 检查是否已存在相同ID的规则
        var existingRuleIndex = this.rules.findIndex(function (r) { return r.id === rule.id; });
        if (existingRuleIndex !== -1) {
            this.rules[existingRuleIndex] = rule;
        }
        else {
            this.rules.push(rule);
        }
        if (this.config.debug) {
            console.log("\u6DFB\u52A0\u54CD\u5E94\u89C4\u5219: ".concat(rule.name, " (").concat(rule.id, ")"));
        }
    };
    /**
     * 移除响应规则
     * @param ruleId 规则ID
     */
    EnvironmentResponseComponent.prototype.removeRule = function (ruleId) {
        var index = this.rules.findIndex(function (rule) { return rule.id === ruleId; });
        if (index !== -1) {
            // 停止活动的响应
            this.stopResponse(ruleId);
            // 移除规则
            this.rules.splice(index, 1);
            if (this.config.debug) {
                console.log("\u79FB\u9664\u54CD\u5E94\u89C4\u5219: ".concat(ruleId));
            }
        }
    };
    /**
     * 启用响应规则
     * @param ruleId 规则ID
     */
    EnvironmentResponseComponent.prototype.enableRule = function (ruleId) {
        var rule = this.rules.find(function (r) { return r.id === ruleId; });
        if (rule) {
            rule.enabled = true;
        }
    };
    /**
     * 禁用响应规则
     * @param ruleId 规则ID
     */
    EnvironmentResponseComponent.prototype.disableRule = function (ruleId) {
        var rule = this.rules.find(function (r) { return r.id === ruleId; });
        if (rule) {
            rule.enabled = false;
            // 停止活动的响应
            this.stopResponse(ruleId);
        }
    };
    /**
     * 评估环境并触发响应
     * @param environmentData 环境数据
     */
    EnvironmentResponseComponent.prototype.evaluateAndRespond = function (environmentData) {
        if (!this.config.autoRespond) {
            return;
        }
        var now = Date.now();
        var triggeredRules = [];
        // 评估所有规则
        for (var _i = 0, _a = this.rules; _i < _a.length; _i++) {
            var rule = _a[_i];
            if (!rule.enabled) {
                continue;
            }
            // 检查冷却时间
            if (rule.cooldown && rule.lastTriggeredTime && (now - rule.lastTriggeredTime) < rule.cooldown) {
                continue;
            }
            // 评估所有条件
            var conditionsMet = rule.conditions.every(function (condition) { return condition.evaluate(environmentData); });
            if (conditionsMet) {
                triggeredRules.push(rule);
                rule.lastTriggeredTime = now;
            }
            else {
                // 如果条件不满足但响应是活动的，停止响应
                this.stopResponse(rule.id);
            }
        }
        // 按优先级排序
        triggeredRules.sort(function (a, b) { return b.priority - a.priority; });
        // 触发响应
        for (var _b = 0, triggeredRules_1 = triggeredRules; _b < triggeredRules_1.length; _b++) {
            var rule = triggeredRules_1[_b];
            this.triggerResponse(rule);
        }
        // 通知响应变化
        this.notifyResponseChange();
    };
    /**
     * 触发响应
     * @param rule 响应规则
     */
    EnvironmentResponseComponent.prototype.triggerResponse = function (rule) {
        // 如果响应已经活动，不重复触发
        if (this.activeResponses.has(rule.id)) {
            return;
        }
        var actions = rule.actions;
        this.activeResponses.set(rule.id, actions);
        // 执行所有动作
        for (var _i = 0, actions_1 = actions; _i < actions_1.length; _i++) {
            var action = actions_1[_i];
            action.execute(this.entity);
        }
        if (this.config.debug) {
            console.log("\u89E6\u53D1\u54CD\u5E94: ".concat(rule.name, " (").concat(rule.id, ")"));
        }
    };
    /**
     * 停止响应
     * @param ruleId 规则ID
     */
    EnvironmentResponseComponent.prototype.stopResponse = function (ruleId) {
        var actions = this.activeResponses.get(ruleId);
        if (!actions) {
            return;
        }
        // 停止所有动作
        for (var _i = 0, actions_2 = actions; _i < actions_2.length; _i++) {
            var action = actions_2[_i];
            if (action.stop) {
                action.stop(this.entity);
            }
        }
        this.activeResponses.delete(ruleId);
        if (this.config.debug) {
            console.log("\u505C\u6B62\u54CD\u5E94: ".concat(ruleId));
        }
    };
    /**
     * 注册响应变化回调
     * @param callback 回调函数
     */
    EnvironmentResponseComponent.prototype.onResponseChange = function (callback) {
        this.onResponseChangeCallbacks.push(callback);
    };
    /**
     * 移除响应变化回调
     * @param callback 回调函数
     */
    EnvironmentResponseComponent.prototype.removeResponseChangeCallback = function (callback) {
        var index = this.onResponseChangeCallbacks.indexOf(callback);
        if (index !== -1) {
            this.onResponseChangeCallbacks.splice(index, 1);
        }
    };
    /**
     * 通知响应变化
     */
    EnvironmentResponseComponent.prototype.notifyResponseChange = function () {
        for (var _i = 0, _a = this.onResponseChangeCallbacks; _i < _a.length; _i++) {
            var callback = _a[_i];
            callback(this.activeResponses);
        }
    };
    /**
     * 获取当前活动的响应
     * @returns 活动的响应
     */
    EnvironmentResponseComponent.prototype.getActiveResponses = function () {
        return new Map(this.activeResponses);
    };
    return EnvironmentResponseComponent;
}(Component_1.Component));
exports.EnvironmentResponseComponent = EnvironmentResponseComponent;
