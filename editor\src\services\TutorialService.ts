/**
 * 教程服务
 * 负责管理编辑器的交互式教程
 */
import { EventEmitter } from 'events';
import { i18n } from '../i18n';

export interface TutorialStep {
  id: string;
  title: string;
  description: string;
  targetElement?: string;
  highlightElement?: boolean;
  action?: string;
  completionCriteria?: string;
  nextButtonText?: string;
  previousButtonText?: string;
  skipButtonText?: string;
}

export interface Tutorial {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // 预计完成时间（分钟）
  steps: TutorialStep[];
  prerequisites?: string[];
  tags?: string[];
  interactive?: boolean; // 是否为交互式教程
  tasks?: any[]; // 任务列表，用于交互式教程
}

export class TutorialService {
  private static instance: TutorialService;
  private tutorials: Tutorial[] = [];
  private currentTutorial: Tutorial | null = null;
  private currentStepIndex: number = -1;
  private events = new EventEmitter();
  private completedTutorials: Set<string> = new Set();
  private tutorialProgress: Map<string, number> = new Map();

  private constructor() {
    this.loadTutorials();
    this.loadUserProgress();
  }

  /**
   * 获取教程服务实例
   */
  public static getInstance(): TutorialService {
    if (!TutorialService.instance) {
      TutorialService.instance = new TutorialService();
    }
    return TutorialService.instance;
  }

  /**
   * 加载教程数据
   */
  private loadTutorials(): void {
    // 导入交互式教程
    import('../tutorials/CharacterCreationTutorial').then(module => {
      const characterCreationTutorial = module.CharacterCreationTutorial;
      // 添加到教程列表
      if (characterCreationTutorial && !this.tutorials.some(t => t.id === characterCreationTutorial.id)) {
        this.tutorials.push(characterCreationTutorial);
      }
    }).catch(error => {
      console.error('Failed to load character creation tutorial:', error);
    });

    // 基础教程数据
    this.tutorials = [
      {
        id: 'editor-basics',
        title: i18n.t('tutorials.editorBasics.title'),
        description: i18n.t('tutorials.editorBasics.description'),
        category: 'getting-started',
        difficulty: 'beginner',
        duration: 10,
        steps: [
          {
            id: 'welcome',
            title: i18n.t('tutorials.editorBasics.steps.welcome.title'),
            description: i18n.t('tutorials.editorBasics.steps.welcome.description'),
            nextButtonText: i18n.t('tutorials.next'),
            skipButtonText: i18n.t('tutorials.skip')
          },
          {
            id: 'interface-overview',
            title: i18n.t('tutorials.editorBasics.steps.interfaceOverview.title'),
            description: i18n.t('tutorials.editorBasics.steps.interfaceOverview.description'),
            nextButtonText: i18n.t('tutorials.next'),
            previousButtonText: i18n.t('tutorials.previous')
          },
          // 更多步骤...
        ],
        tags: ['editor', 'basics', 'interface']
      },
      {
        id: 'visual-scripting',
        title: i18n.t('tutorials.visualScripting.title'),
        description: i18n.t('tutorials.visualScripting.description'),
        category: 'scripting',
        difficulty: 'intermediate',
        duration: 20,
        steps: [
          {
            id: 'introduction',
            title: i18n.t('tutorials.visualScripting.steps.introduction.title'),
            description: i18n.t('tutorials.visualScripting.steps.introduction.description'),
            nextButtonText: i18n.t('tutorials.next'),
            skipButtonText: i18n.t('tutorials.skip')
          },
          // 更多步骤...
        ],
        prerequisites: ['editor-basics'],
        tags: ['visual scripting', 'nodes', 'logic']
      },
      {
        id: 'animation-system',
        title: i18n.t('tutorials.animationSystem.title'),
        description: i18n.t('tutorials.animationSystem.description'),
        category: 'animation',
        difficulty: 'intermediate',
        duration: 25,
        steps: [
          {
            id: 'introduction',
            title: i18n.t('tutorials.animationSystem.steps.introduction.title'),
            description: i18n.t('tutorials.animationSystem.steps.introduction.description'),
            nextButtonText: i18n.t('tutorials.next'),
            skipButtonText: i18n.t('tutorials.skip')
          },
          // 更多步骤...
        ],
        prerequisites: ['editor-basics'],
        tags: ['animation', 'keyframe', 'state machine']
      }
      // 更多教程...
    ];
  }

  /**
   * 加载用户进度
   */
  private loadUserProgress(): void {
    try {
      // 从本地存储加载已完成的教程
      const completedTutorialsJson = localStorage.getItem('completedTutorials');
      if (completedTutorialsJson) {
        const completedTutorialsArray = JSON.parse(completedTutorialsJson);
        this.completedTutorials = new Set(completedTutorialsArray);
      }

      // 从本地存储加载教程进度
      const tutorialProgressJson = localStorage.getItem('tutorialProgress');
      if (tutorialProgressJson) {
        const tutorialProgressObj = JSON.parse(tutorialProgressJson);
        this.tutorialProgress = new Map(Object.entries(tutorialProgressObj));
      }
    } catch (error) {
      console.error('Failed to load tutorial progress:', error);
    }
  }

  /**
   * 保存用户进度
   */
  private saveUserProgress(): void {
    try {
      // 保存已完成的教程到本地存储
      localStorage.setItem('completedTutorials', JSON.stringify([...this.completedTutorials]));

      // 保存教程进度到本地存储
      const tutorialProgressObj = Object.fromEntries(this.tutorialProgress);
      localStorage.setItem('tutorialProgress', JSON.stringify(tutorialProgressObj));
    } catch (error) {
      console.error('Failed to save tutorial progress:', error);
    }
  }

  /**
   * 获取所有教程
   */
  public getTutorials(): Tutorial[] {
    return this.tutorials;
  }

  /**
   * 根据ID获取教程
   */
  public getTutorialById(id: string): Tutorial | undefined {
    return this.tutorials.find(tutorial => tutorial.id === id);
  }

  /**
   * 根据类别获取教程
   */
  public getTutorialsByCategory(category: string): Tutorial[] {
    return this.tutorials.filter(tutorial => tutorial.category === category);
  }

  /**
   * 获取推荐教程
   */
  public getRecommendedTutorials(): Tutorial[] {
    // 根据用户完成情况推荐教程
    const completedIds = [...this.completedTutorials];
    return this.tutorials.filter(tutorial => {
      // 如果已完成，不推荐
      if (completedIds.includes(tutorial.id)) {
        return false;
      }

      // 如果有前置条件，检查是否满足
      if (tutorial.prerequisites && tutorial.prerequisites.length > 0) {
        return tutorial.prerequisites.every(prereq => completedIds.includes(prereq));
      }

      return true;
    });
  }

  /**
   * 开始教程
   */
  public startTutorial(tutorialId: string): boolean {
    const tutorial = this.getTutorialById(tutorialId);
    if (!tutorial) {
      return false;
    }

    this.currentTutorial = tutorial;
    this.currentStepIndex = 0;

    // 获取保存的进度
    const savedProgress = this.tutorialProgress.get(tutorialId);
    if (savedProgress !== undefined && savedProgress > 0) {
      this.currentStepIndex = savedProgress;
    }

    this.events.emit('tutorialStarted', tutorial);
    this.events.emit('stepChanged', this.getCurrentStep());

    return true;
  }

  /**
   * 结束教程
   */
  public endTutorial(completed: boolean = false): void {
    if (!this.currentTutorial) {
      return;
    }

    if (completed) {
      this.completedTutorials.add(this.currentTutorial.id);
      this.tutorialProgress.delete(this.currentTutorial.id);
      this.saveUserProgress();
      this.events.emit('tutorialCompleted', this.currentTutorial);
    } else {
      // 保存进度
      if (this.currentTutorial && this.currentStepIndex >= 0) {
        this.tutorialProgress.set(this.currentTutorial.id, this.currentStepIndex);
        this.saveUserProgress();
      }
      this.events.emit('tutorialExited', this.currentTutorial);
    }

    this.currentTutorial = null;
    this.currentStepIndex = -1;
  }

  /**
   * 获取当前教程
   */
  public getCurrentTutorial(): Tutorial | null {
    return this.currentTutorial;
  }

  /**
   * 获取当前步骤
   */
  public getCurrentStep(): TutorialStep | null {
    if (!this.currentTutorial || this.currentStepIndex < 0 || this.currentStepIndex >= this.currentTutorial.steps.length) {
      return null;
    }

    return this.currentTutorial.steps[this.currentStepIndex];
  }

  /**
   * 下一步
   */
  public nextStep(): boolean {
    if (!this.currentTutorial || this.currentStepIndex < 0) {
      return false;
    }

    if (this.currentStepIndex < this.currentTutorial.steps.length - 1) {
      this.currentStepIndex++;
      this.tutorialProgress.set(this.currentTutorial.id, this.currentStepIndex);
      this.saveUserProgress();
      this.events.emit('stepChanged', this.getCurrentStep());
      return true;
    } else {
      // 完成教程
      this.endTutorial(true);
      return false;
    }
  }

  /**
   * 上一步
   */
  public previousStep(): boolean {
    if (!this.currentTutorial || this.currentStepIndex <= 0) {
      return false;
    }

    this.currentStepIndex--;
    this.tutorialProgress.set(this.currentTutorial.id, this.currentStepIndex);
    this.saveUserProgress();
    this.events.emit('stepChanged', this.getCurrentStep());
    return true;
  }

  /**
   * 检查教程是否已完成
   */
  public isTutorialCompleted(tutorialId: string): boolean {
    return this.completedTutorials.has(tutorialId);
  }

  /**
   * 获取教程进度
   */
  public getTutorialProgress(tutorialId: string): number {
    return this.tutorialProgress.get(tutorialId) || 0;
  }

  /**
   * 添加事件监听器
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.events.on(event, listener);
  }

  /**
   * 移除事件监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.events.off(event, listener);
  }
}

export default TutorialService.getInstance();
