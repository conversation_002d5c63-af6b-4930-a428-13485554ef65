/**
 * 动画库页面样式
 */
.animation-library-page {
  height: 100vh;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    z-index: 1;
    
    .header-title {
      h1 {
        margin: 0;
        font-size: 20px;
      }
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .page-content {
    padding: 24px;
    overflow-y: auto;
    
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 400px;
    }
  }
}

.animation-preview {
  .preview-canvas-container {
    height: 300px;
    background-color: #1e1e1e;
    border-radius: 4px;
    margin-bottom: 16px;
    
    .preview-canvas {
      width: 100%;
      height: 100%;
    }
  }
  
  .preview-controls {
    display: flex;
    justify-content: center;
  }
}
