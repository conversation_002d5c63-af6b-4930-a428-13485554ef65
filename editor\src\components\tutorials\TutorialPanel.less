.tutorial-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  padding: 16px;

  .tutorial-panel-header {
    margin-bottom: 16px;
  }

  .tutorial-panel-tabs {
    margin-bottom: 16px;

    .ant-tabs-nav {
      margin-bottom: 16px;
    }

    .ant-btn {
      margin-right: 8px;
    }
  }

  .tutorial-panel-content {
    flex: 1;
    overflow: auto;
    padding-right: 8px;

    .tutorial-section {
      margin-bottom: 24px;

      h4 {
        margin-bottom: 16px;
      }
    }
  }

  .tutorial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;

    .tutorial-grid-item {
      display: flex;
    }
  }

  .tutorial-card {
    display: flex;
    flex-direction: column;
    width: 100%;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.completed {
      border-color: #52c41a;

      .ant-card-head {
        background-color: rgba(82, 196, 26, 0.1);
      }
    }

    .tutorial-card-title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .achievement-icon {
        color: gold;
        font-size: 16px;
        margin-left: 8px;
      }
    }

    .ant-card-head-title {
      white-space: normal;
    }

    .completed-icon {
      color: #52c41a;
      font-size: 16px;
    }

    .tutorial-progress {
      margin: 12px 0;
    }

    .tutorial-tags {
      margin-top: 12px;
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .ant-tag {
        margin-right: 0;
        margin-bottom: 4px;
      }
    }

    .ant-card-actions {
      background-color: #fafafa;
    }
  }

  .tutorial-empty {
    padding: 24px;
    text-align: center;
    background-color: #f5f5f5;
    border-radius: 4px;
  }
}

.tutorial-modal {
  .tutorial-modal-title {
    display: flex;
    flex-direction: column;

    .tutorial-modal-progress {
      margin-top: 8px;
      display: flex;
      flex-direction: column;

      .ant-typography {
        margin-bottom: 4px;
      }
    }
  }

  .tutorial-step-content {
    min-height: 200px;
    margin-bottom: 24px;
  }

  .tutorial-modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #f0f0f0;
    padding-top: 16px;
  }

  .ant-modal-body {
    padding: 24px;
  }
}
