.video-player {
  position: relative;
  width: 100%;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;

  &.fullscreen {
    width: 100% !important;
    height: 100% !important;
  }

  .video-element {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
  }

  .video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 10;

    &.loading {
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }
    }

    &.error {
      .error-message {
        text-align: center;
        color: #fff;

        p {
          margin-bottom: 16px;
        }
      }
    }
  }

  .video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 8px 16px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
    z-index: 20;
    transition: opacity 0.3s;

    .progress-bar {
      margin-bottom: 8px;

      .ant-slider {
        margin: 0;

        .ant-slider-rail {
          background-color: rgba(255, 255, 255, 0.2);
        }

        .ant-slider-track {
          background-color: #1890ff;
        }

        .ant-slider-handle {
          border-color: #1890ff;
          background-color: #fff;
        }
      }
    }

    .control-buttons {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .ant-btn {
        color: #fff;

        &:hover {
          color: #1890ff;
        }
      }

      .time-display {
        color: #fff;
        font-size: 12px;
        margin-left: 8px;
      }

      .volume-control {
        position: relative;
        display: inline-block;

        .volume-slider {
          position: absolute;
          bottom: 40px;
          left: 50%;
          transform: translateX(-50%);
          height: 100px;
          padding: 8px;
          background-color: rgba(0, 0, 0, 0.7);
          border-radius: 4px;
          z-index: 30;

          .ant-slider {
            height: 100%;
            margin: 0;

            .ant-slider-rail {
              background-color: rgba(255, 255, 255, 0.2);
            }

            .ant-slider-track {
              background-color: #1890ff;
            }

            .ant-slider-handle {
              border-color: #1890ff;
              background-color: #fff;
            }
          }
        }
      }
    }
  }
}

// 暗色主题适配
.dark-theme {
  .video-player {
    .video-controls {
      .progress-bar {
        .ant-slider {
          .ant-slider-rail {
            background-color: rgba(255, 255, 255, 0.1);
          }

          .ant-slider-track {
            background-color: #177ddc;
          }

          .ant-slider-handle {
            border-color: #177ddc;
          }
        }
      }

      .control-buttons {
        .ant-btn {
          &:hover {
            color: #177ddc;
          }
        }
      }

      .volume-control {
        .volume-slider {
          .ant-slider {
            .ant-slider-rail {
              background-color: rgba(255, 255, 255, 0.1);
            }

            .ant-slider-track {
              background-color: #177ddc;
            }

            .ant-slider-handle {
              border-color: #177ddc;
            }
          }
        }
      }
    }
  }
}
