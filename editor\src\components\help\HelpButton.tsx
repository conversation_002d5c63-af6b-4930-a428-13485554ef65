/**
 * 帮助按钮组件
 * 用于在编辑器中提供快速访问帮助的按钮
 */
import React, { useState } from 'react';
import { Button, Tooltip, Popover, Space, Typography, Divider } from 'antd';
import { QuestionCircleOutlined, SearchOutlined, BookOutlined, VideoCameraOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import helpSystem, { HelpContent } from './HelpSystem';
import HelpPanel from './HelpPanel';
import './HelpButton.less';

const { Text, Link } = Typography;

// 帮助按钮属性
interface HelpButtonProps {
  contextId?: string;       // 上下文ID，用于显示相关帮助
  placement?: 'top' | 'bottom' | 'left' | 'right'; // 弹出位置
  size?: 'small' | 'middle' | 'large'; // 按钮大小
  type?: 'default' | 'primary' | 'ghost' | 'dashed' | 'link' | 'text'; // 按钮类型
  showTooltip?: boolean;    // 是否显示提示
  tooltipTitle?: string;    // 提示文本
  showPopoverTitle?: boolean; // 是否显示弹出框标题
  popoverTitle?: string;    // 弹出框标题
  popoverWidth?: number;    // 弹出框宽度
  showSearchInPopover?: boolean; // 是否在弹出框中显示搜索
  showDocLink?: boolean;    // 是否显示文档链接
  docUrl?: string;          // 文档URL
  showVideoLink?: boolean;  // 是否显示视频链接
  videoUrl?: string;        // 视频URL
}

/**
 * 帮助按钮组件
 */
const HelpButton: React.FC<HelpButtonProps> = ({
  contextId,
  placement = 'bottom',
  size = 'middle',
  type = 'default',
  showTooltip = true,
  tooltipTitle,
  showPopoverTitle = true,
  popoverTitle,
  popoverWidth = 300,
  showSearchInPopover = true,
  showDocLink = true,
  docUrl,
  showVideoLink = true,
  videoUrl,
}) => {
  const { t } = useTranslation();
  const [popoverVisible, setPopoverVisible] = useState<boolean>(false);
  const [helpContent, setHelpContent] = useState<HelpContent | undefined>(
    contextId ? helpSystem.getHelpContent(contextId) : undefined
  );
  const [searchQuery, setSearchQuery] = useState<string>('');

  // 处理按钮点击
  const handleButtonClick = () => {
    // 如果有上下文ID，显示相关帮助
    if (contextId) {
      helpSystem.showHelp(contextId);
      // 更新帮助内容
      setHelpContent(helpSystem.getHelpContent(contextId));
    }
  };

  // 处理弹出框可见性变化
  const handlePopoverVisibleChange = (visible: boolean) => {
    setPopoverVisible(visible);
    
    // 如果弹出框显示且有上下文ID，更新帮助内容
    if (visible && contextId) {
      setHelpContent(helpSystem.getHelpContent(contextId));
    }
  };

  // 处理搜索
  const handleSearch = () => {
    if (!searchQuery.trim()) return;
    
    // 打开帮助面板并执行搜索
    helpSystem.searchHelp(searchQuery);
    
    // 关闭弹出框
    setPopoverVisible(false);
  };

  // 处理文档链接点击
  const handleDocLinkClick = () => {
    // 如果有指定的文档URL，打开它
    if (docUrl) {
      window.open(docUrl, '_blank');
    } else if (helpContent && helpContent.relatedTopics && helpContent.relatedTopics.length > 0) {
      // 否则，显示第一个相关主题
      helpSystem.showHelp(helpContent.relatedTopics[0]);
    }
    
    // 关闭弹出框
    setPopoverVisible(false);
  };

  // 处理视频链接点击
  const handleVideoLinkClick = () => {
    // 如果有指定的视频URL，打开它
    if (videoUrl) {
      window.open(videoUrl, '_blank');
    } else if (helpContent && helpContent.videoUrl) {
      // 否则，打开帮助内容中的视频URL
      window.open(helpContent.videoUrl, '_blank');
    }
    
    // 关闭弹出框
    setPopoverVisible(false);
  };

  // 渲染弹出框内容
  const renderPopoverContent = () => {
    return (
      <div className="help-popover-content">
        {helpContent ? (
          <>
            <div className="help-content-summary">
              <Text strong>{helpContent.title}</Text>
              <Text>{helpContent.content.substring(0, 100)}...</Text>
            </div>
            
            <Divider style={{ margin: '8px 0' }} />
            
            <Space direction="vertical" style={{ width: '100%' }}>
              {showSearchInPopover && (
                <div className="help-search-section">
                  <Space.Compact style={{ width: '100%' }}>
                    <input
                      className="help-search-input"
                      placeholder={t('help.searchPlaceholder')}
                      value={searchQuery}
                      onChange={e => setSearchQuery(e.target.value)}
                      onKeyPress={e => e.key === 'Enter' && handleSearch()}
                    />
                    <Button
                      icon={<SearchOutlined />}
                      onClick={handleSearch}
                      disabled={!searchQuery.trim()}
                    />
                  </Space.Compact>
                </div>
              )}
              
              <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                {showDocLink && (
                  <Link onClick={handleDocLinkClick}>
                    <BookOutlined /> {t('help.viewDocumentation')}
                  </Link>
                )}
                
                {showVideoLink && (helpContent.videoUrl || videoUrl) && (
                  <Link onClick={handleVideoLinkClick}>
                    <VideoCameraOutlined /> {t('help.watchVideo')}
                  </Link>
                )}
              </Space>
            </Space>
          </>
        ) : (
          <Text>{t('help.noHelpAvailable')}</Text>
        )}
      </div>
    );
  };

  // 渲染按钮
  const button = (
    <Button
      type={type}
      size={size}
      icon={<QuestionCircleOutlined />}
      onClick={handleButtonClick}
      className="help-button"
    />
  );

  // 如果显示提示，包装按钮
  const buttonWithTooltip = showTooltip ? (
    <Tooltip title={tooltipTitle || t('help.getHelp')}>
      {button}
    </Tooltip>
  ) : button;

  // 如果有上下文ID，包装按钮到弹出框
  return contextId ? (
    <Popover
      content={renderPopoverContent()}
      title={showPopoverTitle ? (popoverTitle || t('help.helpInformation')) : undefined}
      trigger="click"
      placement={placement}
      visible={popoverVisible}
      onVisibleChange={handlePopoverVisibleChange}
      overlayClassName="help-button-popover"
      overlayStyle={{ width: popoverWidth }}
    >
      {buttonWithTooltip}
    </Popover>
  ) : buttonWithTooltip;
};

export default HelpButton;
