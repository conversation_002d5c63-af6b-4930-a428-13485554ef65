  // 渲染教程步骤模态框
  const renderTutorialModal = () => {
    if (!activeTutorial || !currentStep) {
      return null;
    }

    // 检查是否使用新的验证服务
    const validationTutorial = tutorialValidationService.getActiveTutorial();
    if (validationTutorial) {
      return (
        <Modal
          open={showTutorialModal}
          footer={null}
          closable={false}
          maskClosable={false}
          className="tutorial-modal"
          width={450}
        >
          <TutorialStepGuide
            tutorial={validationTutorial}
            onClose={() => {
              setShowTutorialModal(false);
              tutorialValidationService.clearActiveTutorial();
            }}
          />
        </Modal>
      );
    }

    // 兼容旧版本
    const currentIndex = activeTutorial.steps.findIndex(step => step.id === currentStep.id);
    const totalSteps = activeTutorial.steps.length;
    const progressPercent = totalSteps > 0 ? Math.round(((currentIndex + 1) / totalSteps) * 100) : 0;

    return (
      <Modal
        title={
          <div className="tutorial-modal-title">
            <div>{activeTutorial.title}</div>
            <div className="tutorial-modal-progress">
              <Text type="secondary">{t('tutorials.step')} {currentIndex + 1}/{totalSteps}</Text>
              <Progress percent={progressPercent} size="small" showInfo={false} />
            </div>
          </div>
        }
        open={showTutorialModal}
        footer={null}
        closable={false}
        maskClosable={false}
        className="tutorial-modal"
      >
        <div className="tutorial-step-content">
          <Title level={4}>{currentStep.title}</Title>
          <Paragraph>{currentStep.description}</Paragraph>
        </div>

        <div className="tutorial-modal-footer">
          <Button
            onClick={exitTutorial}
            icon={<CloseOutlined />}
          >
            {currentStep.skipButtonText || t('tutorials.exit')}
          </Button>

          <Space>
            {currentIndex > 0 && (
              <Button
                onClick={previousStep}
                icon={<LeftOutlined />}
              >
                {currentStep.previousButtonText || t('tutorials.previous')}
              </Button>
            )}

            <Button
              type="primary"
              onClick={nextStep}
              icon={<RightOutlined />}
            >
              {currentStep.nextButtonText || t('tutorials.next')}
            </Button>
          </Space>
        </div>
      </Modal>
    );
  };

  // 渲染教程面板内容
  const renderPanelContent = () => {
    if (activeTab === 'series' && selectedSeries) {
      return (
        <TutorialSeriesPanel
          series={selectedSeries}
          onBack={() => setSelectedSeries(null)}
          onTutorialSelect={handleTutorialItemSelect}
        />
      );
    }

    switch (activeTab) {
      case 'recommendations':
        return (
          <TutorialRecommendations
            onTutorialSelect={handleTutorialItemSelect}
            onSeriesSelect={handleSeriesSelect}
          />
        );
      case 'difficulty':
        return (
          <TutorialDifficultyPanel
            onTutorialSelect={handleTutorialItemSelect}
          />
        );
      case 'projects':
        return (
          <ProjectTutorialPanel />
        );
      case 'interactive':
        return (
          <div className="tutorial-section">
            <Title level={4}>{t('tutorials.interactiveTutorials')}</Title>
            <Paragraph>{t('tutorials.interactiveDescription')}</Paragraph>

            <div className="tutorial-grid">
              {tutorials
                .filter(tutorial => tutorial.interactive)
                .map(tutorial => (
                  <div key={tutorial.id} className="tutorial-grid-item">
                    {renderTutorialCard(tutorial)}
                  </div>
                ))
              }

              {tutorials.filter(tutorial => tutorial.interactive).length === 0 && (
                <div className="tutorial-empty">
                  <Text type="secondary">{t('tutorials.noInteractiveTutorials')}</Text>
                </div>
              )}
            </div>
          </div>
        );
      case 'all':
      default:
        return (
          <div className="tutorial-section">
            <Title level={4}>{t('tutorials.allTutorials')}</Title>
            <div className="tutorial-grid">
              {tutorials.map(tutorial => (
                <div key={tutorial.id} className="tutorial-grid-item">
                  {renderTutorialCard(tutorial)}
                </div>
              ))}
            </div>
          </div>
        );
    }
  };

  return (
    <div className="tutorial-panel">
      <div className="tutorial-panel-header">
        <Title level={3}>{t('tutorials.title')}</Title>
        <Paragraph>{t('tutorials.description')}</Paragraph>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={(key) => {
          setActiveTab(key);
          if (key !== 'series') {
            setSelectedSeries(null);
          }
        }}
        className="tutorial-panel-tabs"
      >
        <TabPane
          tab={
            <span>
              <StarOutlined /> {t('tutorials.recommended')}
            </span>
          }
          key="recommendations"
        />
        <TabPane
          tab={
            <span>
              <AppstoreOutlined /> {t('tutorials.byDifficulty')}
            </span>
          }
          key="difficulty"
        />
        <TabPane
          tab={
            <span>
              <CodeOutlined /> {t('tutorials.projectTutorials')}
            </span>
          }
          key="projects"
        />
        <TabPane
          tab={
            <span>
              <ReadOutlined /> {t('tutorials.interactive')}
            </span>
          }
          key="interactive"
        />
        <TabPane
          tab={
            <span>
              <BookOutlined /> {t('tutorials.allTutorials')}
            </span>
          }
          key="all"
        />
        {selectedSeries && (
          <TabPane
            tab={
              <span>
                <TeamOutlined /> {selectedSeries.title}
              </span>
            }
            key="series"
          />
        )}
      </Tabs>

      <div className="tutorial-panel-content">
        {renderPanelContent()}
      </div>

      {renderTutorialModal()}
      <TutorialHighlightContainer />
    </div>
  );
};

export default TutorialPanel;
