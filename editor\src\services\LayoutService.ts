/**
 * 布局服务
 * 提供布局管理相关的功能
 */
import { LayoutData, TabData, PanelData } from 'rc-dock';
import { defaultLayout, predefinedLayouts } from '../store/ui/layoutSlice';

// 面板内容映射
export const panelContentMap: Record<string, React.ComponentType<any>> = {};

/**
 * 布局服务类
 */
class LayoutService {
  private static instance: LayoutService;
  private dockLayoutRef: any = null;

  /**
   * 获取单例实例
   */
  public static getInstance(): LayoutService {
    if (!LayoutService.instance) {
      LayoutService.instance = new LayoutService();
    }
    return LayoutService.instance;
  }

  /**
   * 设置DockLayout引用
   * @param ref DockLayout组件引用
   */
  public setDockLayoutRef(ref: any): void {
    this.dockLayoutRef = ref;
  }

  /**
   * 获取DockLayout引用
   */
  public getDockLayoutRef(): any {
    return this.dockLayoutRef;
  }

  /**
   * 保存当前布局
   * @returns 当前布局数据
   */
  public saveCurrentLayout(): LayoutData | null {
    if (this.dockLayoutRef) {
      return this.dockLayoutRef.saveLayout();
    }
    return null;
  }

  /**
   * 加载布局
   * @param layout 布局数据
   */
  public loadLayout(layout: LayoutData): void {
    if (this.dockLayoutRef) {
      this.dockLayoutRef.loadLayout(layout);
    }
  }

  /**
   * 重置为默认布局
   */
  public resetToDefaultLayout(): void {
    if (this.dockLayoutRef) {
      this.dockLayoutRef.loadLayout(defaultLayout);
    }
  }

  /**
   * 加载预定义布局
   * @param layoutName 布局名称
   */
  public loadPredefinedLayout(layoutName: string): void {
    if (this.dockLayoutRef && predefinedLayouts[layoutName]) {
      this.dockLayoutRef.loadLayout(predefinedLayouts[layoutName]);
    }
  }

  /**
   * 查找面板或标签
   * @param id 面板或标签ID
   * @returns 面板数据或标签数据
   */
  public findPanelOrTab(id: string): PanelData | TabData | null {
    if (this.dockLayoutRef) {
      return this.dockLayoutRef.find(id);
    }
    return null;
  }

  /**
   * 更新标签
   * @param id 标签ID
   * @param newTab 新标签数据
   * @returns 是否更新成功
   */
  public updateTab(id: string, newTab: TabData): boolean {
    if (this.dockLayoutRef) {
      return this.dockLayoutRef.updateTab(id, newTab);
    }
    return false;
  }

  /**
   * 移动面板或标签
   * @param source 源面板或标签
   * @param target 目标面板或标签
   * @param direction 放置方向
   */
  public dockMove(source: any, target: any, direction: string): void {
    if (this.dockLayoutRef) {
      this.dockLayoutRef.dockMove(source, target, direction);
    }
  }

  /**
   * 注册面板内容组件
   * @param id 面板ID
   * @param component 组件
   */
  public static registerPanelContent(id: string, component: React.ComponentType<any>): void {
    panelContentMap[id] = component;
  }

  /**
   * 获取面板内容组件
   * @param id 面板ID
   * @returns 面板内容组件
   */
  public static getPanelContent(id: string): React.ComponentType<any> | null {
    return panelContentMap[id] || null;
  }
}

export default LayoutService;
