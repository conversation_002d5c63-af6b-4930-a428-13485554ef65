# 渲染系统入门指南

本指南将帮助您了解DL（Digital Learning）引擎的渲染系统，并学习如何使用它创建高质量的3D场景。

## 目录

1. [基本概念](#基本概念)
2. [设置渲染系统](#设置渲染系统)
3. [创建基本场景](#创建基本场景)
4. [添加光照和阴影](#添加光照和阴影)
5. [使用材质](#使用材质)
6. [添加后处理效果](#添加后处理效果)
7. [优化渲染性能](#优化渲染性能)
8. [高级渲染技术](#高级渲染技术)
9. [常见问题解答](#常见问题解答)

## 基本概念

DL（Digital Learning）引擎的渲染系统基于ECS（实体组件系统）架构，使用Three.js作为底层渲染引擎。以下是一些基本概念：

- **渲染系统（RenderSystem）**：负责管理和执行场景的渲染过程
- **渲染器（Renderer）**：执行实际的渲染操作
- **场景（Scene）**：包含要渲染的所有对象
- **相机（Camera）**：定义观察场景的视角
- **光源（Light）**：为场景提供照明
- **材质（Material）**：定义对象的外观
- **后处理（PostProcessing）**：在场景渲染完成后应用视觉效果

## 设置渲染系统

首先，我们需要创建一个渲染系统并将其添加到引擎的世界中：

```typescript
import { Engine } from '../../core/Engine';
import { World } from '../../core/World';
import { RenderSystem } from '../../rendering/RenderSystem';
import { Renderer } from '../../rendering/Renderer';

// 创建引擎
const engine = new Engine();

// 创建世界
const world = new World();
engine.setWorld(world);

// 创建渲染器
const renderer = new Renderer({
  width: window.innerWidth,
  height: window.innerHeight,
  antialias: true,
  shadows: true
});

// 创建渲染系统
const renderSystem = new RenderSystem(renderer, {
  enableShadows: true,
  enablePostProcessing: true,
  clearColor: { r: 0.1, g: 0.1, b: 0.1 },
  clearAlpha: 1.0
});

// 添加渲染系统到世界
world.addSystem(renderSystem);

// 处理窗口大小变化
window.addEventListener('resize', () => {
  renderer.setSize(window.innerWidth, window.innerHeight);
});

// 启动引擎
engine.start();
```

## 创建基本场景

接下来，我们创建一个基本场景，包含地面和一些简单的对象：

```typescript
import { Scene } from '../../scene/Scene';
import { Entity } from '../../core/Entity';
import { Transform } from '../../scene/Transform';
import { Camera, CameraType } from '../../rendering/Camera';
import * as THREE from 'three';

// 创建场景
const scene = new Scene();

// 创建相机
const cameraEntity = new Entity('相机');
const camera = new Camera({
  type: CameraType.PERSPECTIVE,
  fov: 75,
  aspect: window.innerWidth / window.innerHeight,
  near: 0.1,
  far: 1000,
  position: { x: 0, y: 5, z: 10 },
  lookAt: { x: 0, y: 0, z: 0 }
});
cameraEntity.addComponent(camera);
scene.addEntity(cameraEntity);

// 创建地面
const groundEntity = new Entity('地面');
groundEntity.addComponent(new Transform({
  position: { x: 0, y: 0, z: 0 },
  rotation: { x: 0, y: 0, z: 0 },
  scale: { x: 20, y: 1, z: 20 }
}));

// 创建地面几何体和材质
const groundGeometry = new THREE.PlaneGeometry(1, 1);
const groundMaterial = new THREE.MeshStandardMaterial({
  color: 0x808080,
  roughness: 0.7,
  metalness: 0.0
});

// 创建地面网格
const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
groundMesh.rotation.x = -Math.PI / 2;
groundMesh.receiveShadow = true;

// 添加网格到变换组件
const groundTransform = groundEntity.getComponent('Transform') as Transform;
groundTransform.getObject3D().add(groundMesh);

// 添加地面实体到场景
scene.addEntity(groundEntity);

// 创建立方体
const cubeEntity = new Entity('立方体');
cubeEntity.addComponent(new Transform({
  position: { x: 0, y: 1, z: 0 },
  rotation: { x: 0, y: Math.PI / 4, z: 0 },
  scale: { x: 1, y: 1, z: 1 }
}));

// 创建立方体几何体和材质
const cubeGeometry = new THREE.BoxGeometry(1, 1, 1);
const cubeMaterial = new THREE.MeshStandardMaterial({
  color: 0x0000ff,
  roughness: 0.5,
  metalness: 0.5
});

// 创建立方体网格
const cubeMesh = new THREE.Mesh(cubeGeometry, cubeMaterial);
cubeMesh.castShadow = true;
cubeMesh.receiveShadow = true;

// 添加网格到变换组件
const cubeTransform = cubeEntity.getComponent('Transform') as Transform;
cubeTransform.getObject3D().add(cubeMesh);

// 添加立方体实体到场景
scene.addEntity(cubeEntity);

// 设置活跃场景和相机
renderSystem.setActiveScene(scene);
renderSystem.setActiveCamera(camera);
```

## 添加光照和阴影

现在，我们添加光照和阴影，使场景更加逼真：

```typescript
import { Light, LightType } from '../../rendering/Light';

// 创建方向光
const directionalLightEntity = new Entity('方向光');
directionalLightEntity.addComponent(new Transform({
  position: { x: 5, y: 5, z: 5 },
  rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 },
  scale: { x: 1, y: 1, z: 1 }
}));

// 创建方向光组件
const directionalLight = new Light({
  type: LightType.DIRECTIONAL,
  color: 0xffffff,
  intensity: 1.0,
  castShadow: true,
  shadowMapSize: 2048,
  shadowBias: -0.0005
});
directionalLightEntity.addComponent(directionalLight);

// 添加方向光实体到场景
scene.addEntity(directionalLightEntity);

// 创建环境光
const ambientLightEntity = new Entity('环境光');

// 创建环境光组件
const ambientLight = new Light({
  type: LightType.AMBIENT,
  color: 0x404040,
  intensity: 0.5
});
ambientLightEntity.addComponent(ambientLight);

// 添加环境光实体到场景
scene.addEntity(ambientLightEntity);
```

## 使用材质

DL（Digital Learning）引擎支持多种材质类型，包括PBR（物理基础渲染）材质：

```typescript
import { MaterialSystem } from '../../rendering/materials/MaterialSystem';
import { PBRMaterial } from '../../rendering/materials/PBRMaterial';

// 创建材质系统
const materialSystem = new MaterialSystem();

// 创建PBR材质
const metalMaterial = new PBRMaterial({
  name: '金属材质',
  baseColor: { r: 0.8, g: 0.8, b: 0.8 },
  metalness: 1.0,
  roughness: 0.2
});

// 创建球体
const sphereEntity = new Entity('球体');
sphereEntity.addComponent(new Transform({
  position: { x: 3, y: 1, z: 0 },
  rotation: { x: 0, y: 0, z: 0 },
  scale: { x: 1, y: 1, z: 1 }
}));

// 创建球体几何体
const sphereGeometry = new THREE.SphereGeometry(0.5, 32, 32);

// 创建球体网格
const sphereMesh = new THREE.Mesh(sphereGeometry, metalMaterial.getMaterial());
sphereMesh.castShadow = true;
sphereMesh.receiveShadow = true;

// 添加网格到变换组件
const sphereTransform = sphereEntity.getComponent('Transform') as Transform;
sphereTransform.getObject3D().add(sphereMesh);

// 添加球体实体到场景
scene.addEntity(sphereEntity);
```

## 添加后处理效果

后处理效果可以增强场景的视觉效果：

```typescript
import { PostProcessingSystem } from '../../rendering/postprocessing/PostProcessingSystem';
import { BloomEffect } from '../../rendering/postprocessing/effects/BloomEffect';
import { SSAOEffect } from '../../rendering/postprocessing/effects/SSAOEffect';
import { ToneMappingEffect, ToneMappingType } from '../../rendering/postprocessing/effects/ToneMappingEffect';

// 创建后处理系统实体
const postProcessingEntity = new Entity('后处理系统');

// 创建后处理系统
const postProcessingSystem = new PostProcessingSystem({
  enabled: true,
  autoResize: true
});

// 创建泛光效果
const bloomEffect = new BloomEffect({
  name: 'Bloom',
  enabled: true,
  intensity: 1.0,
  threshold: 0.7,
  radius: 0.4
});

// 创建SSAO效果
const ssaoEffect = new SSAOEffect({
  name: 'SSAO',
  enabled: true,
  radius: 0.1825,
  bias: 0.025,
  intensity: 1.0,
  samples: 16
});

// 创建色调映射效果
const toneMappingEffect = new ToneMappingEffect({
  name: 'ToneMapping',
  enabled: true,
  type: ToneMappingType.ACES_FILMIC,
  exposure: 1.0
});

// 添加效果到后处理系统
postProcessingSystem.addEffect(bloomEffect);
postProcessingSystem.addEffect(ssaoEffect);
postProcessingSystem.addEffect(toneMappingEffect);

// 添加后处理系统到实体
postProcessingEntity.addComponent(postProcessingSystem);

// 添加后处理系统实体到场景
scene.addEntity(postProcessingEntity);
```

## 优化渲染性能

对于大型场景或低性能设备，我们可以使用以下优化技术：

```typescript
import { LODSystem } from '../../rendering/optimization/LODSystem';
import { FrustumCullingSystem } from '../../rendering/optimization/FrustumCullingSystem';
import { InstancedRenderingSystem } from '../../rendering/optimization/InstancedRenderingSystem';

// 创建LOD系统
const lodSystem = new LODSystem({
  enabled: true,
  autoUpdate: true,
  updateInterval: 0.5
});

// 创建视锥体剔除系统
const frustumCullingSystem = new FrustumCullingSystem({
  enabled: true,
  autoUpdate: true,
  updateInterval: 0.1,
  useBVH: true
});

// 创建实例化渲染系统
const instancedRenderingSystem = new InstancedRenderingSystem({
  enabled: true,
  maxInstancesPerBatch: 1000,
  useGPUInstancing: true
});

// 添加系统到世界
world.addSystem(lodSystem);
world.addSystem(frustumCullingSystem);
world.addSystem(instancedRenderingSystem);

// 创建实例化对象（例如，草地）
const grassGeometry = new THREE.PlaneGeometry(1, 1);
const grassMaterial = new THREE.MeshBasicMaterial({
  color: 0x00ff00,
  side: THREE.DoubleSide
});

const grassInstancedObject = instancedRenderingSystem.createInstancedObject(grassGeometry, grassMaterial);

// 添加草实例
for (let i = 0; i < 1000; i++) {
  const x = Math.random() * 20 - 10;
  const z = Math.random() * 20 - 10;
  const y = 0.5;
  const rotation = { x: 0, y: Math.random() * Math.PI * 2, z: 0 };
  const scale = { x: 1, y: 1, z: 1 };
  
  grassInstancedObject.addInstance({ x, y, z }, rotation, scale);
}

// 更新实例化对象
grassInstancedObject.updateInstances();
```

## 高级渲染技术

DL（Digital Learning）引擎支持多种高级渲染技术，如屏幕空间反射、全局光照和体积光：

```typescript
import { SSREffect } from '../../rendering/postprocessing/effects/SSREffect';
import { SSGIEffect } from '../../rendering/postprocessing/effects/SSGIEffect';
import { VolumetricLightSystem } from '../../rendering/volumetric/VolumetricLightSystem';

// 创建屏幕空间反射效果
const ssrEffect = new SSREffect({
  name: 'SSR',
  enabled: true,
  intensity: 0.5,
  maxSteps: 20,
  stride: 1,
  refineSteps: 5,
  thickness: 0.5,
  maxDistance: 50
});

// 创建屏幕空间全局光照效果
const ssgiEffect = new SSGIEffect({
  name: 'SSGI',
  enabled: true,
  intensity: 1.0,
  radius: 2.0,
  samples: 16,
  denoise: true,
  denoiseRadius: 12
});

// 添加效果到后处理系统
postProcessingSystem.addEffect(ssrEffect);
postProcessingSystem.addEffect(ssgiEffect);

// 创建体积光系统
const volumetricLightSystem = new VolumetricLightSystem({
  enabled: true,
  samples: 50,
  density: 0.1,
  weight: 0.5,
  decay: 0.95,
  exposure: 0.2
});

// 为光源添加体积光效果
volumetricLightSystem.addLight(directionalLight);

// 添加体积光系统到世界
world.addSystem(volumetricLightSystem);
```

## 常见问题解答

### 如何提高渲染性能？

1. 使用LOD（细节层次）系统减少远距离对象的复杂度
2. 启用视锥体剔除，仅渲染视野内的对象
3. 使用实例化渲染处理大量相似对象
4. 优化材质和纹理，减少内存使用
5. 降低后处理效果的质量或禁用部分效果

### 如何解决阴影问题？

1. 调整阴影贴图大小（shadowMapSize）以提高阴影质量
2. 调整阴影偏移（shadowBias）以减少阴影痤疮
3. 使用级联阴影映射（CSM）处理大型场景
4. 对于静态场景，考虑使用烘焙阴影

### 如何实现逼真的材质？

1. 使用PBR（物理基础渲染）材质
2. 提供准确的材质参数（金属度、粗糙度等）
3. 使用高质量的纹理贴图（法线贴图、金属度贴图、粗糙度贴图等）
4. 添加环境光照和反射

### 如何处理移动设备上的渲染？

1. 降低渲染分辨率
2. 减少后处理效果
3. 使用简化的材质
4. 减少光源数量和阴影质量
5. 增加LOD级别，更早地切换到低细节模型
