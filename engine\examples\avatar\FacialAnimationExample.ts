/**
 * 面部动画示例
 * 展示如何使用面部动画系统
 */
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { World } from '../../src/core/World';
import { Entity } from '../../src/core/Entity';
import { FacialAnimationSystem } from '../../src/avatar/systems/FacialAnimationSystem';
import { FacialAnimationModelAdapterSystem, FacialAnimationModelType } from '../../src/avatar/systems/FacialAnimationModelAdapterSystem';
import { LipSyncSystem } from '../../src/avatar/systems/LipSyncSystem';
import { FacialAnimationEditorSystem } from '../../src/avatar/systems/FacialAnimationEditorSystem';
import { AIAnimationSynthesisSystem } from '../../src/avatar/systems/AIAnimationSynthesisSystem';
import { FacialExpressionType, VisemeType } from '../../src/avatar/components/FacialAnimationComponent';

/**
 * 面部动画示例
 */
export class FacialAnimationExample {
  /** 场景 */
  private scene: THREE.Scene;

  /** 相机 */
  private camera: THREE.PerspectiveCamera;

  /** 渲染器 */
  private renderer: THREE.WebGLRenderer;

  /** 控制器 */
  private controls: OrbitControls;

  /** 世界 */
  private world: World;

  /** 角色实体 */
  private characterEntity: Entity;

  /** 面部动画系统 */
  private facialAnimationSystem: FacialAnimationSystem;

  /** 面部动画模型适配器系统 */
  private modelAdapterSystem: FacialAnimationModelAdapterSystem;

  /** 口型同步系统 */
  private lipSyncSystem: LipSyncSystem;

  /** 面部动画编辑器系统 */
  private facialAnimationEditorSystem: FacialAnimationEditorSystem;

  /** AI动画合成系统 */
  private aiAnimationSynthesisSystem: AIAnimationSynthesisSystem;

  /** 音频元素 */
  private audioElement: HTMLAudioElement | null = null;

  /** 表情按钮 */
  private expressionButtons: { [key: string]: HTMLButtonElement } = {};

  /** 口型按钮 */
  private visemeButtons: { [key: string]: HTMLButtonElement } = {};

  /** 播放按钮 */
  private playButton: HTMLButtonElement | null = null;

  /** 停止按钮 */
  private stopButton: HTMLButtonElement | null = null;

  /** 暂停按钮 */
  private pauseButton: HTMLButtonElement | null = null;

  /** 生成按钮 */
  private generateButton: HTMLButtonElement | null = null;

  /** 情感输入 */
  private emotionInput: HTMLInputElement | null = null;

  /** 是否使用GPU加速 */
  private useGPUAcceleration: boolean = false;

  /**
   * 构造函数
   */
  constructor() {
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x87CEEB);

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.camera.position.set(0, 1.6, 2);

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    document.body.appendChild(this.renderer.domElement);

    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 1.6, 0);
    this.controls.update();

    // 创建世界
    this.world = new World();

    // 创建角色实体
    this.characterEntity = this.world.createEntity();

    // 创建面部动画系统
    this.facialAnimationSystem = new FacialAnimationSystem({
      debug: true,
      autoDetectAudio: true
    });

    // 创建面部动画模型适配器系统
    this.modelAdapterSystem = new FacialAnimationModelAdapterSystem({
      debug: true,
      defaultModelType: FacialAnimationModelType.GLTF,
      autoDetectBlendShapes: true
    });

    // 创建口型同步系统
    this.lipSyncSystem = new LipSyncSystem({
      debug: true,
      fftSize: 1024,
      volumeThreshold: 0.01
    });

    // 创建面部动画编辑器系统
    this.facialAnimationEditorSystem = new FacialAnimationEditorSystem({
      debug: true
    });

    // 创建AI动画合成系统
    this.aiAnimationSynthesisSystem = new AIAnimationSynthesisSystem({
      debug: true,
      useLocalModel: true
    });

    // 设置模型适配器系统
    this.facialAnimationSystem.setModelAdapterSystem(this.modelAdapterSystem);

    // 添加系统到世界
    this.world.addSystem(this.facialAnimationSystem);
    this.world.addSystem(this.modelAdapterSystem);
    this.world.addSystem(this.lipSyncSystem);
    this.world.addSystem(this.facialAnimationEditorSystem);
    this.world.addSystem(this.aiAnimationSynthesisSystem);

    // 添加灯光
    this.addLights();

    // 添加地面
    this.addGround();

    // 加载模型
    this.loadModel();

    // 创建UI
    this.createUI();

    // 开始动画循环
    this.animate();

    // 监听窗口大小变化
    window.addEventListener('resize', this.onWindowResize.bind(this));
  }

  /**
   * 添加灯光
   */
  private addLights(): void {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(ambientLight);

    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 2, 3);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    this.scene.add(directionalLight);
  }

  /**
   * 添加地面
   */
  private addGround(): void {
    const groundGeometry = new THREE.PlaneGeometry(10, 10);
    const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x999999 });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    this.scene.add(ground);
  }

  /**
   * 加载模型
   */
  private loadModel(): void {
    const loader = new GLTFLoader();

    // 加载角色模型
    loader.load('/models/avatar.glb', (gltf) => {
      // 添加模型到场景
      this.scene.add(gltf.scene);

      // 设置阴影
      gltf.scene.traverse((object) => {
        if (object instanceof THREE.Mesh) {
          object.castShadow = true;
          object.receiveShadow = true;
        }
      });

      // 查找骨骼网格
      let skinnedMesh: THREE.SkinnedMesh | null = null;
      gltf.scene.traverse((object) => {
        if (object instanceof THREE.SkinnedMesh) {
          skinnedMesh = object;
        }
      });

      if (skinnedMesh) {
        // 创建面部动画组件
        const facialAnimation = this.facialAnimationSystem.createFacialAnimation(this.characterEntity);

        // 将面部动画组件与模型绑定
        this.facialAnimationSystem.linkToModel(this.characterEntity, skinnedMesh);

        // 创建口型同步组件
        const lipSync = this.lipSyncSystem.createLipSync(this.characterEntity);

        // 创建面部动画编辑器组件
        const editor = this.facialAnimationEditorSystem.createEditor(this.characterEntity);

        // 创建AI动画合成组件
        this.aiAnimationSynthesisSystem.createAIAnimationSynthesis(this.characterEntity);

        // 创建默认动画片段
        this.createDefaultAnimationClips();

        console.log('模型加载完成，已绑定面部动画组件和编辑器组件');
        console.log('混合形状字典:', skinnedMesh.morphTargetDictionary);
        console.log('混合形状影响:', skinnedMesh.morphTargetInfluences);

        // 启用按钮
        this.enableButtons();
      } else {
        console.warn('未找到骨骼网格');
      }
    }, undefined, (error) => {
      console.error('加载模型失败:', error);
    });
  }

  /**
   * 创建默认动画片段
   */
  private createDefaultAnimationClips(): void {
    // 获取编辑器
    const editor = this.facialAnimationEditorSystem.getEditor(this.characterEntity);
    if (!editor) return;

    // 创建表情动画片段
    const expressionClip = this.facialAnimationEditorSystem.createClip(this.characterEntity, '表情动画', 5.0);
    if (expressionClip) {
      // 添加表情关键帧
      expressionClip.addExpressionKeyframe(0.0, FacialExpressionType.NEUTRAL, 1.0);
      expressionClip.addExpressionKeyframe(1.0, FacialExpressionType.HAPPY, 1.0);
      expressionClip.addExpressionKeyframe(2.0, FacialExpressionType.SURPRISED, 1.0);
      expressionClip.addExpressionKeyframe(3.0, FacialExpressionType.ANGRY, 1.0);
      expressionClip.addExpressionKeyframe(4.0, FacialExpressionType.SAD, 1.0);
      expressionClip.addExpressionKeyframe(5.0, FacialExpressionType.NEUTRAL, 1.0);
    }

    // 创建口型动画片段
    const visemeClip = this.facialAnimationEditorSystem.createClip(this.characterEntity, '口型动画', 5.0);
    if (visemeClip) {
      // 添加口型关键帧
      visemeClip.addVisemeKeyframe(0.0, VisemeType.SILENT, 0.0);
      visemeClip.addVisemeKeyframe(1.0, VisemeType.AA, 1.0);
      visemeClip.addVisemeKeyframe(2.0, VisemeType.EE, 1.0);
      visemeClip.addVisemeKeyframe(3.0, VisemeType.OU, 1.0);
      visemeClip.addVisemeKeyframe(4.0, VisemeType.MM, 1.0);
      visemeClip.addVisemeKeyframe(5.0, VisemeType.SILENT, 0.0);
    }
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    const uiContainer = document.createElement('div');
    uiContainer.style.position = 'absolute';
    uiContainer.style.top = '10px';
    uiContainer.style.left = '10px';
    uiContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    uiContainer.style.padding = '10px';
    uiContainer.style.borderRadius = '5px';
    uiContainer.style.color = 'white';
    document.body.appendChild(uiContainer);

    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '面部动画示例';
    uiContainer.appendChild(title);

    // 创建表情按钮
    const expressionContainer = document.createElement('div');
    expressionContainer.style.marginBottom = '10px';
    uiContainer.appendChild(expressionContainer);

    const expressionTitle = document.createElement('h3');
    expressionTitle.textContent = '表情';
    expressionContainer.appendChild(expressionTitle);

    const expressions = [
      { name: '中性', value: FacialExpressionType.NEUTRAL },
      { name: '开心', value: FacialExpressionType.HAPPY },
      { name: '悲伤', value: FacialExpressionType.SAD },
      { name: '愤怒', value: FacialExpressionType.ANGRY },
      { name: '惊讶', value: FacialExpressionType.SURPRISED },
      { name: '恐惧', value: FacialExpressionType.FEARFUL },
      { name: '厌恶', value: FacialExpressionType.DISGUSTED }
    ];

    for (const expression of expressions) {
      const button = document.createElement('button');
      button.textContent = expression.name;
      button.style.margin = '5px';
      button.disabled = true;
      button.addEventListener('click', () => this.setExpression(expression.value));
      expressionContainer.appendChild(button);
      this.expressionButtons[expression.value] = button;
    }

    // 创建口型按钮
    const visemeContainer = document.createElement('div');
    visemeContainer.style.marginBottom = '10px';
    uiContainer.appendChild(visemeContainer);

    const visemeTitle = document.createElement('h3');
    visemeTitle.textContent = '口型';
    visemeContainer.appendChild(visemeTitle);

    const visemes = [
      { name: '静默', value: VisemeType.SILENT },
      { name: 'AA', value: VisemeType.AA },
      { name: 'EE', value: VisemeType.EE },
      { name: 'IH', value: VisemeType.IH },
      { name: 'OH', value: VisemeType.OH },
      { name: 'OU', value: VisemeType.OU },
      { name: 'MM', value: VisemeType.MM }
    ];

    for (const viseme of visemes) {
      const button = document.createElement('button');
      button.textContent = viseme.name;
      button.style.margin = '5px';
      button.disabled = true;
      button.addEventListener('click', () => this.setViseme(viseme.value));
      visemeContainer.appendChild(button);
      this.visemeButtons[viseme.value] = button;
    }

    // 创建播放控制
    const playbackContainer = document.createElement('div');
    playbackContainer.style.marginBottom = '10px';
    uiContainer.appendChild(playbackContainer);

    const playbackTitle = document.createElement('h3');
    playbackTitle.textContent = '播放控制';
    playbackContainer.appendChild(playbackTitle);

    this.playButton = document.createElement('button');
    this.playButton.textContent = '播放';
    this.playButton.style.margin = '5px';
    this.playButton.disabled = true;
    this.playButton.addEventListener('click', () => this.playAnimation());
    playbackContainer.appendChild(this.playButton);

    this.pauseButton = document.createElement('button');
    this.pauseButton.textContent = '暂停';
    this.pauseButton.style.margin = '5px';
    this.pauseButton.disabled = true;
    this.pauseButton.addEventListener('click', () => this.pauseAnimation());
    playbackContainer.appendChild(this.pauseButton);

    this.stopButton = document.createElement('button');
    this.stopButton.textContent = '停止';
    this.stopButton.style.margin = '5px';
    this.stopButton.disabled = true;
    this.stopButton.addEventListener('click', () => this.stopAnimation());
    playbackContainer.appendChild(this.stopButton);

    // 创建AI生成
    const aiContainer = document.createElement('div');
    aiContainer.style.marginBottom = '10px';
    uiContainer.appendChild(aiContainer);

    const aiTitle = document.createElement('h3');
    aiTitle.textContent = 'AI生成';
    aiContainer.appendChild(aiTitle);

    const emotionLabel = document.createElement('label');
    emotionLabel.textContent = '情感描述: ';
    aiContainer.appendChild(emotionLabel);

    this.emotionInput = document.createElement('input');
    this.emotionInput.type = 'text';
    this.emotionInput.value = '开心地笑';
    this.emotionInput.style.margin = '5px';
    this.emotionInput.disabled = true;
    aiContainer.appendChild(this.emotionInput);

    this.generateButton = document.createElement('button');
    this.generateButton.textContent = '生成动画';
    this.generateButton.style.margin = '5px';
    this.generateButton.disabled = true;
    this.generateButton.addEventListener('click', () => this.generateAnimation());
    aiContainer.appendChild(this.generateButton);

    // 创建测试按钮
    const testContainer = document.createElement('div');
    uiContainer.appendChild(testContainer);

    const testButton = document.createElement('button');
    testButton.textContent = '测试AI动画';
    testButton.style.margin = '5px';
    testButton.addEventListener('click', () => this.testAIAnimation());
    testContainer.appendChild(testButton);
  }

  /**
   * 启用按钮
   */
  private enableButtons(): void {
    // 启用表情按钮
    for (const key in this.expressionButtons) {
      this.expressionButtons[key].disabled = false;
    }

    // 启用口型按钮
    for (const key in this.visemeButtons) {
      this.visemeButtons[key].disabled = false;
    }

    // 启用播放控制按钮
    if (this.playButton) this.playButton.disabled = false;
    if (this.pauseButton) this.pauseButton.disabled = false;
    if (this.stopButton) this.stopButton.disabled = false;

    // 启用AI生成按钮
    if (this.emotionInput) this.emotionInput.disabled = false;
    if (this.generateButton) this.generateButton.disabled = false;
  }

  /**
   * 设置表情
   * @param expression 表情类型
   */
  private setExpression(expression: FacialExpressionType): void {
    // 获取面部动画组件
    const facialAnimation = this.facialAnimationSystem.getFacialAnimation(this.characterEntity);
    if (facialAnimation) {
      facialAnimation.setExpression(expression, 1.0, 0.3);
    }
  }

  /**
   * 设置口型
   * @param viseme 口型类型
   */
  private setViseme(viseme: VisemeType): void {
    // 获取面部动画组件
    const facialAnimation = this.facialAnimationSystem.getFacialAnimation(this.characterEntity);
    if (facialAnimation) {
      facialAnimation.setViseme(viseme, 1.0, 0.3);
    }
  }

  /**
   * 播放动画
   */
  private playAnimation(): void {
    // 获取编辑器
    const editor = this.facialAnimationEditorSystem.getEditor(this.characterEntity);
    if (editor) {
      editor.play();
    }
  }

  /**
   * 暂停动画
   */
  private pauseAnimation(): void {
    // 获取编辑器
    const editor = this.facialAnimationEditorSystem.getEditor(this.characterEntity);
    if (editor) {
      editor.pause();
    }
  }

  /**
   * 停止动画
   */
  private stopAnimation(): void {
    // 获取编辑器
    const editor = this.facialAnimationEditorSystem.getEditor(this.characterEntity);
    if (editor) {
      editor.stop();
    }
  }

  /**
   * 生成动画
   */
  private generateAnimation(): void {
    // 获取情感描述
    const emotionText = this.emotionInput ? this.emotionInput.value : '开心地笑';

    // 禁用生成按钮
    if (this.generateButton) {
      this.generateButton.disabled = true;
      this.generateButton.textContent = '生成中...';
    }

    // 生成面部动画
    const requestId = this.aiAnimationSynthesisSystem.generateFacialAnimation(
      this.characterEntity,
      emotionText,
      5.0,
      {
        loop: true,
        style: '自然',
        intensity: 0.8
      }
    );

    if (requestId) {
      console.log(`AI动画生成请求已提交: ${requestId}`);

      // 监听生成完成事件
      this.aiAnimationSynthesisSystem.addEventListener('generationComplete', (data) => {
        if (data.result.id === requestId) {
          console.log('AI动画生成完成:', data.result);

          // 启用生成按钮
          if (this.generateButton) {
            this.generateButton.disabled = false;
            this.generateButton.textContent = '生成动画';
          }

          // 如果生成成功，播放动画
          if (data.result.success && data.result.clip) {
            // 获取编辑器
            const editor = this.facialAnimationEditorSystem.getEditor(this.characterEntity);
            if (editor) {
              // 添加生成的片段
              editor.addClip(data.result.clip);

              // 设置当前片段并播放
              editor.setCurrentClip(data.result.clip.name);
              editor.play();
            }
          }
        }
      });
    }
  }

  /**
   * 测试AI动画
   */
  private testAIAnimation(): void {
    // 生成面部动画
    const requestId = this.aiAnimationSynthesisSystem.generateFacialAnimation(
      this.characterEntity,
      '开心地说话',
      5.0,
      {
        loop: true,
        style: '卡通',
        intensity: 0.8
      }
    );

    if (requestId) {
      console.log(`AI动画生成请求已提交: ${requestId}`);

      // 监听生成完成事件
      this.aiAnimationSynthesisSystem.addEventListener('generationComplete', (data) => {
        if (data.result.id === requestId) {
          console.log('AI动画生成完成:', data.result);
        }
      });
    }
  }

  /**
   * 窗口大小变化处理
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 动画循环
   */
  private animate(): void {
    requestAnimationFrame(this.animate.bind(this));

    // 更新世界
    this.world.update();

    // 更新控制器
    this.controls.update();

    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }
}

// 创建示例
new FacialAnimationExample();
