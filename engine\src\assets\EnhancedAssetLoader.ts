/**
 * 增强资产加载器
 * 支持更多资源类型和更高效的加载策略
 */
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader.js';
import { FontLoader } from 'three/examples/jsm/loaders/FontLoader.js';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader.js';
import { KTX2Loader } from 'three/examples/jsm/loaders/KTX2Loader.js';
import { TGALoader } from 'three/examples/jsm/loaders/TGALoader.js';
import { BasisTextureLoader } from 'three/examples/jsm/loaders/BasisTextureLoader.js';
import { AssetType } from './ResourceManager';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 加载器选项
 */
export interface LoaderOptions {
  /** 基础路径 */
  basePath?: string;
  /** 跨域设置 */
  crossOrigin?: string;
  /** DRACO解码器路径 */
  dracoDecoderPath?: string;
  /** KTX2转码器路径 */
  ktx2TranscoderPath?: string;
  /** Basis转码器路径 */
  basisTranscoderPath?: string;
  /** 是否启用DRACO压缩 */
  enableDraco?: boolean;
  /** 是否启用KTX2压缩 */
  enableKTX2?: boolean;
  /** 是否启用Basis压缩 */
  enableBasis?: boolean;
  /** 最大纹理尺寸 */
  maxTextureSize?: number;
  /** 是否启用纹理压缩 */
  enableTextureCompression?: boolean;
  /** 是否启用HDR */
  enableHDR?: boolean;
}

/**
 * 增强资产加载器
 */
export class EnhancedAssetLoader extends EventEmitter {
  /** Three.js加载管理器 */
  private manager: THREE.LoadingManager;

  /** 纹理加载器 */
  private textureLoader: THREE.TextureLoader;

  /** GLTF加载器 */
  private gltfLoader: GLTFLoader;

  /** FBX加载器 */
  private fbxLoader: FBXLoader;

  /** OBJ加载器 */
  private objLoader: OBJLoader;

  /** 立方体纹理加载器 */
  private cubeTextureLoader: THREE.CubeTextureLoader;

  /** 音频加载器 */
  private audioLoader: THREE.AudioLoader;

  /** 字体加载器 */
  private fontLoader: FontLoader;

  /** 文件加载器 */
  private fileLoader: THREE.FileLoader;

  /** TGA加载器 */
  private tgaLoader: TGALoader;

  /** DRACO加载器 */
  private dracoLoader: DRACOLoader | null = null;

  /** KTX2加载器 */
  private ktx2Loader: KTX2Loader | null = null;

  /** Basis加载器 */
  private basisLoader: BasisTextureLoader | null = null;

  /** 基础路径 */
  private basePath: string = '';

  /** 加载器选项 */
  private options: LoaderOptions;

  /**
   * 创建增强资产加载器实例
   * @param options 加载器选项
   */
  constructor(options: LoaderOptions = {}) {
    super();

    this.options = {
      basePath: '',
      crossOrigin: 'anonymous',
      dracoDecoderPath: 'assets/draco/',
      ktx2TranscoderPath: 'assets/ktx2/',
      basisTranscoderPath: 'assets/basis/',
      enableDraco: true,
      enableKTX2: true,
      enableBasis: false,
      maxTextureSize: 2048,
      enableTextureCompression: true,
      enableHDR: true,
      ...options
    };

    // 设置基础路径
    this.basePath = this.options.basePath || '';

    // 创建加载管理器
    this.manager = new THREE.LoadingManager();

    // 设置加载管理器事件
    this.manager.onStart = (url, itemsLoaded, itemsTotal) => {
      this.emit('loadStart', { url, itemsLoaded, itemsTotal });
    };

    this.manager.onLoad = () => {
      this.emit('loadComplete');
    };

    this.manager.onProgress = (url, itemsLoaded, itemsTotal) => {
      this.emit('loadProgress', { url, itemsLoaded, itemsTotal });
    };

    this.manager.onError = (url) => {
      this.emit('loadError', { url });
    };

    // 创建基本加载器
    this.textureLoader = new THREE.TextureLoader(this.manager);
    this.gltfLoader = new GLTFLoader(this.manager);
    this.fbxLoader = new FBXLoader(this.manager);
    this.objLoader = new OBJLoader(this.manager);
    this.cubeTextureLoader = new THREE.CubeTextureLoader(this.manager);
    this.audioLoader = new THREE.AudioLoader(this.manager);
    this.fontLoader = new FontLoader(this.manager);
    this.fileLoader = new THREE.FileLoader(this.manager);
    this.tgaLoader = new TGALoader(this.manager);

    // 设置跨域
    this.setCrossOrigin(this.options.crossOrigin || 'anonymous');

    // 设置基础路径
    this.setPath(this.basePath);

    // 初始化特殊加载器
    this.initSpecialLoaders();
  }

  /**
   * 初始化特殊加载器（DRACO, KTX2, Basis等）
   */
  private initSpecialLoaders(): void {
    // 初始化DRACO加载器
    if (this.options.enableDraco) {
      this.dracoLoader = new DRACOLoader(this.manager);
      this.dracoLoader.setDecoderPath(this.options.dracoDecoderPath || 'assets/draco/');
      this.dracoLoader.setWorkerLimit(2); // 限制工作线程数量
      // 检查方法是否存在
      if (typeof (this.gltfLoader as any).setDRACOLoader === 'function') {
        (this.gltfLoader as any).setDRACOLoader(this.dracoLoader);
      }
    }

    // 初始化KTX2加载器
    if (this.options.enableKTX2) {
      this.ktx2Loader = new KTX2Loader(this.manager);
      this.ktx2Loader.setTranscoderPath(this.options.ktx2TranscoderPath || 'assets/ktx2/');

      // 检测支持的压缩格式
      if (typeof window !== 'undefined') {
        const renderer = new THREE.WebGLRenderer();
        this.ktx2Loader.detectSupport(renderer);
        (renderer as any).dispose();
      }
    }

    // 初始化Basis加载器
    if (this.options.enableBasis) {
      this.basisLoader = new BasisTextureLoader(this.manager);
      this.basisLoader.setTranscoderPath(this.options.basisTranscoderPath || 'assets/basis/');

      // 检测支持的压缩格式
      if (typeof window !== 'undefined') {
        const renderer = new THREE.WebGLRenderer();
        this.basisLoader.detectSupport(renderer);
        (renderer as any).dispose();
      }
    }
  }

  /**
   * 加载资产
   * @param type 资产类型
   * @param url 资产URL
   * @returns Promise，解析为加载的资产数据
   */
  public async load(type: AssetType, url: string): Promise<any> {
    // 构建完整URL
    const fullUrl = this.resolveUrl(url);

    switch (type) {
      case AssetType.TEXTURE:
        return this.loadTexture(fullUrl);

      case AssetType.MODEL:
        return this.loadModel(fullUrl);

      case AssetType.AUDIO:
        return this.loadAudio(fullUrl);

      case AssetType.FONT:
        return this.loadFont(fullUrl);

      case AssetType.JSON:
        return this.loadJSON(fullUrl);

      case AssetType.TEXT:
        return this.loadText(fullUrl);

      case AssetType.BINARY:
        return this.loadBinary(fullUrl);

      case AssetType.CUBEMAP:
        return this.loadCubeTexture(fullUrl);

      default:
        throw new Error(`不支持的资产类型: ${type}`);
    }
  }

  /**
   * 解析URL
   * @param url 原始URL
   * @returns 解析后的完整URL
   */
  private resolveUrl(url: string): string {
    // 如果是绝对URL，则直接返回
    if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('data:')) {
      return url;
    }

    // 否则，拼接基础路径
    return this.basePath + url;
  }

  /**
   * 加载纹理
   * @param url 纹理URL
   * @returns Promise，解析为加载的纹理
   */
  private loadTexture(url: string): Promise<THREE.Texture> {
    // 根据文件扩展名选择加载器
    const extension = url.split('.').pop()?.toLowerCase();

    if (extension === 'tga') {
      return this.loadTGATexture(url);
    } else if (extension === 'ktx2' && this.ktx2Loader) {
      return this.loadKTX2Texture(url);
    } else if (extension === 'basis' && this.basisLoader) {
      return this.loadBasisTexture(url);
    } else {
      return this.loadStandardTexture(url);
    }
  }

  /**
   * 加载标准纹理
   * @param url 纹理URL
   * @returns Promise，解析为加载的纹理
   */
  private loadStandardTexture(url: string): Promise<THREE.Texture> {
    return new Promise((resolve, reject) => {
      this.textureLoader.load(
        url,
        texture => {
          // 设置纹理属性
          texture.colorSpace = THREE.SRGBColorSpace;
          texture.needsUpdate = true;

          // 应用最大纹理尺寸限制
          if (this.options.maxTextureSize &&
              (texture.image.width > this.options.maxTextureSize ||
               texture.image.height > this.options.maxTextureSize)) {
            this.resizeTexture(texture, this.options.maxTextureSize);
          }

          resolve(texture);
        },
        undefined,
        error => reject(new Error(`加载纹理失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载TGA纹理
   * @param url 纹理URL
   * @returns Promise，解析为加载的纹理
   */
  private loadTGATexture(url: string): Promise<THREE.Texture> {
    return new Promise((resolve, reject) => {
      this.tgaLoader.load(
        url,
        texture => {
          // 设置纹理属性
          texture.colorSpace = THREE.SRGBColorSpace;
          texture.needsUpdate = true;

          // 应用最大纹理尺寸限制
          if (this.options.maxTextureSize &&
              (texture.image.width > this.options.maxTextureSize ||
               texture.image.height > this.options.maxTextureSize)) {
            this.resizeTexture(texture, this.options.maxTextureSize);
          }

          resolve(texture);
        },
        undefined,
        error => reject(new Error(`加载TGA纹理失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载KTX2纹理
   * @param url 纹理URL
   * @returns Promise，解析为加载的纹理
   */
  private loadKTX2Texture(url: string): Promise<THREE.Texture> {
    if (!this.ktx2Loader) {
      throw new Error('KTX2加载器未初始化');
    }

    return new Promise((resolve, reject) => {
      this.ktx2Loader.load(
        url,
        texture => {
          // 设置纹理属性
          texture.colorSpace = THREE.SRGBColorSpace;
          texture.needsUpdate = true;
          resolve(texture);
        },
        undefined,
        error => reject(new Error(`加载KTX2纹理失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载Basis纹理
   * @param url 纹理URL
   * @returns Promise，解析为加载的纹理
   */
  private loadBasisTexture(url: string): Promise<THREE.Texture> {
    if (!this.basisLoader) {
      throw new Error('Basis加载器未初始化');
    }

    return new Promise((resolve, reject) => {
      this.basisLoader.load(
        url,
        texture => {
          // 设置纹理属性
          texture.colorSpace = THREE.SRGBColorSpace;
          texture.needsUpdate = true;
          resolve(texture);
        },
        undefined,
        error => reject(new Error(`加载Basis纹理失败: ${error.message}`))
      );
    });
  }

  /**
   * 调整纹理大小
   * @param texture 纹理
   * @param maxSize 最大尺寸
   */
  private resizeTexture(texture: THREE.Texture, maxSize: number): void {
    const image = texture.image;

    // 计算新尺寸
    let width = image.width;
    let height = image.height;

    if (width > height) {
      if (width > maxSize) {
        height = Math.round(height * maxSize / width);
        width = maxSize;
      }
    } else {
      if (height > maxSize) {
        width = Math.round(width * maxSize / height);
        height = maxSize;
      }
    }

    // 创建canvas并调整大小
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;

    // 绘制调整后的图像
    const context = canvas.getContext('2d');
    if (context) {
      context.drawImage(image, 0, 0, width, height);
      texture.image = canvas;
      texture.needsUpdate = true;
    }
  }

  /**
   * 加载模型
   * @param url 模型URL
   * @returns Promise，解析为加载的模型
   */
  private loadModel(url: string): Promise<any> {
    // 根据文件扩展名选择加载器
    const extension = url.split('.').pop()?.toLowerCase();

    switch (extension) {
      case 'gltf':
      case 'glb':
        return this.loadGLTF(url);

      case 'fbx':
        return this.loadFBX(url);

      case 'obj':
        return this.loadOBJ(url);

      default:
        throw new Error(`不支持的模型格式: ${extension}`);
    }
  }

  /**
   * 加载GLTF模型
   * @param url GLTF模型URL
   * @returns Promise，解析为加载的GLTF模型
   */
  private loadGLTF(url: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.gltfLoader.load(
        url,
        (gltf: any) => resolve(gltf),
        undefined,
        (error: any) => reject(new Error(`加载GLTF模型失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载FBX模型
   * @param url FBX模型URL
   * @returns Promise，解析为加载的FBX模型
   */
  private loadFBX(url: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.fbxLoader.load(
        url,
        (fbx: any) => resolve(fbx),
        undefined,
        (error: any) => reject(new Error(`加载FBX模型失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载OBJ模型
   * @param url OBJ模型URL
   * @returns Promise，解析为加载的OBJ模型
   */
  private loadOBJ(url: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.objLoader.load(
        url,
        (obj: any) => resolve(obj),
        undefined,
        (error: any) => reject(new Error(`加载OBJ模型失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载音频
   * @param url 音频URL
   * @returns Promise，解析为加载的音频数据
   */
  private loadAudio(url: string): Promise<AudioBuffer> {
    return new Promise((resolve, reject) => {
      this.audioLoader.load(
        url,
        (buffer: AudioBuffer) => resolve(buffer),
        undefined,
        (error: any) => reject(new Error(`加载音频失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载字体
   * @param url 字体URL
   * @returns Promise，解析为加载的字体
   */
  private loadFont(url: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.fontLoader.load(
        url,
        (font: any) => resolve(font),
        undefined,
        (error: any) => reject(new Error(`加载字体失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载JSON
   * @param url JSON URL
   * @returns Promise，解析为加载的JSON数据
   */
  private loadJSON(url: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.fileLoader.setResponseType('json');
      this.fileLoader.load(
        url,
        (data: any) => resolve(data),
        undefined,
        (error: any) => reject(new Error(`加载JSON失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载文本
   * @param url 文本URL
   * @returns Promise，解析为加载的文本
   */
  private loadText(url: string): Promise<string> {
    return new Promise((resolve, reject) => {
      this.fileLoader.setResponseType('text');
      this.fileLoader.load(
        url,
        (data: string) => resolve(data),
        undefined,
        (error: any) => reject(new Error(`加载文本失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载二进制数据
   * @param url 二进制数据URL
   * @returns Promise，解析为加载的二进制数据
   */
  private loadBinary(url: string): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      this.fileLoader.setResponseType('arraybuffer');
      this.fileLoader.load(
        url,
        (data: ArrayBuffer) => resolve(data),
        undefined,
        (error: any) => reject(new Error(`加载二进制数据失败: ${error.message}`))
      );
    });
  }

  /**
   * 加载立方体纹理
   * @param urls 立方体纹理URL数组或基础URL
   * @returns Promise，解析为加载的立方体纹理
   */
  private loadCubeTexture(urls: string | string[]): Promise<THREE.CubeTexture> {
    return new Promise((resolve, reject) => {
      // 如果提供的是单个URL，则假设它是一个基础URL，需要添加后缀
      const textureUrls = Array.isArray(urls) ? urls : [
        `${urls}_px.jpg`, `${urls}_nx.jpg`,
        `${urls}_py.jpg`, `${urls}_ny.jpg`,
        `${urls}_pz.jpg`, `${urls}_nz.jpg`
      ];

      this.cubeTextureLoader.load(
        textureUrls,
        (texture: THREE.CubeTexture) => {
          texture.colorSpace = THREE.SRGBColorSpace;
          resolve(texture);
        },
        undefined,
        (error: any) => reject(new Error(`加载立方体纹理失败: ${error.message}`))
      );
    });
  }

  /**
   * 设置加载基础路径
   * @param path 基础路径
   */
  public setPath(path: string): void {
    this.basePath = path;
    this.textureLoader.setPath(path);
    this.gltfLoader.setPath(path);
    this.fbxLoader.setPath(path);
    this.objLoader.setPath(path);
    this.cubeTextureLoader.setPath(path);
    this.audioLoader.setPath(path);
    this.fontLoader.setPath(path);
    this.fileLoader.setPath(path);
    this.tgaLoader.setPath(path);

    if (this.dracoLoader) {
      this.dracoLoader.setDecoderPath(path + (this.options.dracoDecoderPath || 'assets/draco/'));
    }

    if (this.ktx2Loader) {
      this.ktx2Loader.setTranscoderPath(path + (this.options.ktx2TranscoderPath || 'assets/ktx2/'));
    }

    if (this.basisLoader) {
      this.basisLoader.setTranscoderPath(path + (this.options.basisTranscoderPath || 'assets/basis/'));
    }
  }

  /**
   * 设置跨域
   * @param crossOrigin 跨域设置
   */
  public setCrossOrigin(crossOrigin: string): void {
    this.textureLoader.setCrossOrigin(crossOrigin);
    this.cubeTextureLoader.setCrossOrigin(crossOrigin);
    this.fileLoader.setCrossOrigin(crossOrigin);
  }

  /**
   * 销毁加载器
   */
  public dispose(): void {
    // 清除加载管理器的事件监听器
    this.manager.onStart = () => {};
    this.manager.onLoad = () => {};
    this.manager.onProgress = () => {};
    this.manager.onError = () => {};

    // 释放DRACO加载器
    if (this.dracoLoader) {
      (this.dracoLoader as any).dispose();
    }

    // 移除所有事件监听器
    this.removeAllListeners();
  }
}
