/**
 * 1D混合空间编辑器组件
 * 用于编辑1D动画混合空间
 */
import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Button,
  Form,
  Input,
  InputNumber,
  Switch,
  Slider,
  Table,
  Space,
  Modal,
  Select,
  Tooltip,
  message,
  Divider,
  Tabs,
  Collapse
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SettingOutlined,
  CloseOutlined,
  LineChartOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { RootState } from '../../store';
import {
  loadBlendSpace1D,
  updateBlendSpace1D,
  addBlendSpace1DNode,
  updateBlendSpace1DNode,
  removeBlendSpace1DNode,
  setBlendSpace1DPosition
} from '../../store/animations/blendSpaceSlice';
import { blendSpaceService } from '../../services/blendSpaceService';
import { BlendCurveType } from '../../../engine/src/animation/AnimationBlender';
import BlendCurveEditor from './BlendCurveEditor';
import './AnimationEditor.less';

const { Option } = Select;
const { confirm } = Modal;
const { TabPane } = Tabs;
const { Panel } = Collapse;

/**
 * 1D混合空间编辑器属性
 */
interface BlendSpace1DEditorProps {
  /** 实体ID */
  entityId: string;
  /** 混合空间ID */
  blendSpaceId: string;
  /** 关闭回调 */
  onClose?: () => void;
}

/**
 * 1D混合空间编辑器组件
 */
const BlendSpace1DEditor: React.FC<BlendSpace1DEditorProps> = ({ entityId, blendSpaceId, onClose }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 从Redux获取状态
  const { blendSpace, loading, error } = useSelector((state: RootState) => state.blendSpace);

  // 本地状态
  const [form] = Form.useForm();
  const [nodeForm] = Form.useForm();
  const [isEditing, setIsEditing] = useState(false);
  const [isAddingNode, setIsAddingNode] = useState(false);
  const [editingNode, setEditingNode] = useState<any>(null);
  const [availableClips, setAvailableClips] = useState<string[]>([]);
  const [currentPosition, setCurrentPosition] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showCurveEditor, setShowCurveEditor] = useState(false);
  const [curveType, setCurveType] = useState<BlendCurveType>(BlendCurveType.LINEAR);
  const [activeTab, setActiveTab] = useState<string>('basic');
  const [isDragging, setIsDragging] = useState(false);
  const [dragNodeId, setDragNodeId] = useState<string | null>(null);
  const [showNodeWeights, setShowNodeWeights] = useState(true);
  const [showHeatmap, setShowHeatmap] = useState(false);

  // 引用
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>(null);

  // 加载混合空间
  useEffect(() => {
    if (entityId && blendSpaceId) {
      dispatch(loadBlendSpace1D({ entityId, blendSpaceId }));
      loadAvailableClips();
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [dispatch, entityId, blendSpaceId]);

  // 加载可用的动画片段
  const loadAvailableClips = async () => {
    try {
      const clips = await blendSpaceService.getAvailableAnimationClips(entityId);
      setAvailableClips(clips);
    } catch (error) {
      console.error('加载动画片段失败:', error);
      message.error(t('editor.animation.loadClipsFailed'));
    }
  };

  // 当混合空间加载完成后，设置表单值
  useEffect(() => {
    if (blendSpace && blendSpace.id === blendSpaceId) {
      form.setFieldsValue({
        name: blendSpace.name,
        description: blendSpace.description,
        minValue: blendSpace.config.minValue,
        maxValue: blendSpace.config.maxValue,
        normalizeInput: blendSpace.config.normalizeInput,
        useSmoothing: blendSpace.config.useSmoothing,
        smoothingFactor: blendSpace.config.smoothingFactor,
        enableExtrapolation: blendSpace.config.enableExtrapolation,
        blendCurveType: blendSpace.config.blendCurveType || BlendCurveType.LINEAR
      });

      setCurrentPosition(blendSpace.position || 0);

      // 设置混合曲线类型
      if (blendSpace.config.blendCurveType) {
        setCurveType(blendSpace.config.blendCurveType);
      }
    }
  }, [blendSpace, blendSpaceId, form]);

  // 绘制混合空间
  useEffect(() => {
    if (canvasRef.current && blendSpace && blendSpace.id === blendSpaceId) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');

      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 绘制背景
      ctx.fillStyle = '#f0f0f0';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // 获取配置
      const minValue = blendSpace.config.minValue;
      const maxValue = blendSpace.config.maxValue;
      const range = maxValue - minValue;

      // 绘制热图（如果启用）
      if (showHeatmap && blendSpace.nodes && blendSpace.nodes.length > 0) {
        // 绘制热图
        const resolution = 100; // 热图分辨率
        const step = canvas.width / resolution;

        for (let i = 0; i < resolution; i++) {
          const x = i * step;
          const pos = minValue + (i / resolution) * range;

          // 计算该位置的权重
          let totalWeight = 0;
          let weights: { [key: string]: number } = {};

          // 初始化所有节点的权重为0
          for (const node of blendSpace.nodes) {
            weights[node.id] = 0;
          }

          // 计算权重
          if (blendSpace.nodes.length === 1) {
            // 如果只有一个节点，则权重为1
            weights[blendSpace.nodes[0].id] = 1;
            totalWeight = 1;
          } else {
            // 计算每个节点的权重
            for (let j = 0; j < blendSpace.nodes.length - 1; j++) {
              const node1 = blendSpace.nodes[j];
              const node2 = blendSpace.nodes[j + 1];

              if (pos >= node1.position && pos <= node2.position) {
                const t = (pos - node1.position) / (node2.position - node1.position);
                weights[node1.id] = 1 - t;
                weights[node2.id] = t;
                totalWeight = 1;
                break;
              }
            }

            // 处理边界情况
            if (totalWeight === 0) {
              if (pos < blendSpace.nodes[0].position) {
                weights[blendSpace.nodes[0].id] = 1;
                totalWeight = 1;
              } else if (pos > blendSpace.nodes[blendSpace.nodes.length - 1].position) {
                weights[blendSpace.nodes[blendSpace.nodes.length - 1].id] = 1;
                totalWeight = 1;
              }
            }
          }

          // 绘制热图条带
          if (totalWeight > 0) {
            // 混合颜色
            let r = 0, g = 0, b = 0;
            for (const node of blendSpace.nodes) {
              const weight = weights[node.id];
              if (weight > 0) {
                // 为每个节点分配一个颜色
                const hue = (blendSpace.nodes.indexOf(node) * 137) % 360; // 使用黄金角来分散颜色
                const color = hslToRgb(hue / 360, 0.7, 0.5);
                r += color[0] * weight;
                g += color[1] * weight;
                b += color[2] * weight;
              }
            }

            ctx.fillStyle = `rgba(${Math.round(r)}, ${Math.round(g)}, ${Math.round(b)}, 0.7)`;
            ctx.fillRect(x, 0, step + 1, canvas.height); // +1 避免间隙
          }
        }
      }

      // 绘制轴线
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(0, canvas.height / 2);
      ctx.lineTo(canvas.width, canvas.height / 2);
      ctx.stroke();

      // 绘制刻度
      ctx.textAlign = 'center';
      ctx.textBaseline = 'top';
      ctx.fillStyle = '#000000';

      // 绘制最小值
      ctx.fillText(minValue.toString(), 10, canvas.height / 2 + 5);

      // 绘制最大值
      ctx.fillText(maxValue.toString(), canvas.width - 10, canvas.height / 2 + 5);

      // 绘制中间值
      const midValue = (minValue + maxValue) / 2;
      ctx.fillText(midValue.toString(), canvas.width / 2, canvas.height / 2 + 5);

      // 绘制节点
      if (blendSpace.nodes) {
        for (const node of blendSpace.nodes) {
          // 计算节点位置
          const x = ((node.position - minValue) / range) * (canvas.width - 20) + 10;
          const y = canvas.height / 2;

          // 绘制节点
          ctx.beginPath();
          ctx.arc(x, y, 10, 0, Math.PI * 2);

          // 设置节点颜色
          if (node.weight > 0) {
            // 为每个节点分配一个颜色
            const hue = (blendSpace.nodes.indexOf(node) * 137) % 360; // 使用黄金角来分散颜色
            const [r, g, b] = hslToRgb(hue / 360, 0.7, 0.5);
            ctx.fillStyle = `rgba(${r}, ${g}, ${b}, ${node.weight})`;
          } else {
            ctx.fillStyle = '#cccccc';
          }

          // 如果是正在拖动的节点，则高亮显示
          if (dragNodeId === node.id) {
            ctx.strokeStyle = '#ff4d4f';
            ctx.lineWidth = 2;
          } else {
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = 1;
          }

          ctx.fill();
          ctx.stroke();

          // 绘制节点名称
          ctx.fillStyle = '#000000';
          ctx.fillText(node.clipName, x, y - 25);

          // 绘制节点权重（如果启用）
          if (showNodeWeights) {
            ctx.fillText(node.weight.toFixed(2), x, y + 15);
          }
        }
      }

      // 绘制当前位置
      const posX = ((currentPosition - minValue) / range) * (canvas.width - 20) + 10;

      ctx.strokeStyle = '#ff0000';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(posX, 0);
      ctx.lineTo(posX, canvas.height);
      ctx.stroke();

      // 绘制当前位置值
      ctx.fillStyle = '#ff0000';
      ctx.fillText(currentPosition.toFixed(2), posX, 5);
    }
  }, [blendSpace, blendSpaceId, currentPosition, dragNodeId, showNodeWeights, showHeatmap]);

  // HSL转RGB辅助函数
  const hslToRgb = (h: number, s: number, l: number): [number, number, number] => {
    let r, g, b;

    if (s === 0) {
      r = g = b = l; // 灰色
    } else {
      const hue2rgb = (p: number, q: number, t: number) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1/6) return p + (q - p) * 6 * t;
        if (t < 1/2) return q;
        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
        return p;
      };

      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1/3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1/3);
    }

    return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
  };

  // 处理播放/暂停
  useEffect(() => {
    if (isPlaying) {
      let startTime = Date.now();
      let direction = 1;

      const animate = () => {
        const now = Date.now();
        const deltaTime = (now - startTime) / 1000;
        startTime = now;

        // 更新位置
        let newPosition = currentPosition + direction * deltaTime;

        // 检查边界
        if (blendSpace && blendSpace.config) {
          if (newPosition >= blendSpace.config.maxValue) {
            newPosition = blendSpace.config.maxValue;
            direction = -1;
          } else if (newPosition <= blendSpace.config.minValue) {
            newPosition = blendSpace.config.minValue;
            direction = 1;
          }
        }

        setCurrentPosition(newPosition);

        // 更新混合空间位置
        if (entityId && blendSpaceId) {
          dispatch(setBlendSpace1DPosition({ entityId, blendSpaceId, position: newPosition }));
        }

        animationFrameRef.current = requestAnimationFrame(animate);
      };

      animationFrameRef.current = requestAnimationFrame(animate);
    } else if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isPlaying, currentPosition, blendSpace, entityId, blendSpaceId, dispatch]);

  // 处理表单提交
  const handleFormSubmit = (values: any) => {
    if (!blendSpace) return;

    dispatch(updateBlendSpace1D({
      entityId,
      blendSpaceId,
      data: {
        name: values.name,
        description: values.description,
        config: {
          minValue: values.minValue,
          maxValue: values.maxValue,
          normalizeInput: values.normalizeInput,
          useSmoothing: values.useSmoothing,
          smoothingFactor: values.smoothingFactor,
          enableExtrapolation: values.enableExtrapolation,
          blendCurveType: values.blendCurveType || BlendCurveType.LINEAR,
          // 保留之前的自定义曲线设置
          customBlendCurve: blendSpace.config.customBlendCurve,
          blendCurvePreset: blendSpace.config.blendCurvePreset,
          bezierPoints: blendSpace.config.bezierPoints
        }
      }
    }));

    // 更新本地状态
    setCurveType(values.blendCurveType || BlendCurveType.LINEAR);

    setIsEditing(false);
    message.success(t('editor.animation.updateBlendSpaceSuccess'));
  };

  // 处理添加节点
  const handleAddNode = () => {
    nodeForm.resetFields();
    setEditingNode(null);
    setIsAddingNode(true);
  };

  // 处理编辑节点
  const handleEditNode = (node: any) => {
    nodeForm.setFieldsValue({
      clipName: node.clipName,
      position: node.position
    });
    setEditingNode(node);
    setIsAddingNode(true);
  };

  // 处理删除节点
  const handleDeleteNode = (node: any) => {
    confirm({
      title: t('editor.animation.confirmDeleteNode'),
      content: t('editor.animation.confirmDeleteNodeContent'),
      onOk: () => {
        dispatch(removeBlendSpace1DNode({
          entityId,
          blendSpaceId,
          nodeId: node.id
        }));
      }
    });
  };

  // 处理节点表单提交
  const handleNodeFormSubmit = (values: any) => {
    if (editingNode) {
      // 更新节点
      dispatch(updateBlendSpace1DNode({
        entityId,
        blendSpaceId,
        nodeId: editingNode.id,
        data: {
          clipName: values.clipName,
          position: values.position
        }
      }));
    } else {
      // 添加节点
      dispatch(addBlendSpace1DNode({
        entityId,
        blendSpaceId,
        data: {
          clipName: values.clipName,
          position: values.position
        }
      }));
    }

    setIsAddingNode(false);
    message.success(editingNode ? t('editor.animation.updateNodeSuccess') : t('editor.animation.addNodeSuccess'));
  };

  // 处理位置滑块变化
  const handlePositionChange = (value: number) => {
    setCurrentPosition(value);

    // 更新混合空间位置
    if (entityId && blendSpaceId) {
      dispatch(setBlendSpace1DPosition({ entityId, blendSpaceId, position: value }));
    }
  };

  // 处理播放/暂停
  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  // 处理画布点击
  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || !blendSpace) return;

    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // 获取配置
    const minValue = blendSpace.config.minValue;
    const maxValue = blendSpace.config.maxValue;
    const range = maxValue - minValue;

    // 检查是否点击了节点
    if (blendSpace.nodes) {
      for (const node of blendSpace.nodes) {
        const nodeX = ((node.position - minValue) / range) * (canvas.width - 20) + 10;
        const nodeY = canvas.height / 2;

        // 计算点击位置与节点的距离
        const distance = Math.sqrt(Math.pow(x - nodeX, 2) + Math.pow(y - nodeY, 2));

        // 如果点击了节点
        if (distance <= 10) {
          setDragNodeId(node.id);
          return;
        }
      }
    }

    // 如果没有点击节点，则更新当前位置
    const position = minValue + ((x - 10) / (canvas.width - 20)) * range;
    handlePositionChange(Math.max(minValue, Math.min(maxValue, position)));
  };

  // 处理画布鼠标移动
  const handleCanvasMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!dragNodeId || !canvasRef.current || !blendSpace) return;

    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;

    // 获取配置
    const minValue = blendSpace.config.minValue;
    const maxValue = blendSpace.config.maxValue;
    const range = maxValue - minValue;

    // 计算新位置
    const position = minValue + ((x - 10) / (canvas.width - 20)) * range;
    const newPosition = Math.max(minValue, Math.min(maxValue, position));

    // 更新节点位置
    dispatch(updateBlendSpace1DNode({
      entityId,
      blendSpaceId,
      nodeId: dragNodeId,
      data: {
        position: newPosition
      }
    }));

    setIsDragging(true);
  };

  // 处理画布鼠标按下
  const handleCanvasMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    handleCanvasClick(e);
  };

  // 处理画布鼠标抬起
  const handleCanvasMouseUp = () => {
    setDragNodeId(null);
    setIsDragging(false);
  };

  // 处理画布鼠标离开
  const handleCanvasMouseLeave = () => {
    if (isDragging) {
      setDragNodeId(null);
      setIsDragging(false);
    }
  };

  // 切换显示节点权重
  const toggleNodeWeights = () => {
    setShowNodeWeights(!showNodeWeights);
  };

  // 切换显示热图
  const toggleHeatmap = () => {
    setShowHeatmap(!showHeatmap);
  };

  // 处理曲线类型变更
  const handleCurveTypeChange = (type: BlendCurveType) => {
    setCurveType(type);

    // 更新混合空间配置
    if (blendSpace) {
      dispatch(updateBlendSpace1D({
        entityId,
        blendSpaceId,
        data: {
          ...blendSpace,
          config: {
            ...blendSpace.config,
            blendCurveType: type
          }
        }
      }));
    }
  };

  // 处理自定义曲线变更
  const handleCustomCurveChange = (curve: (t: number) => number) => {
    // 更新混合空间配置
    if (blendSpace) {
      dispatch(updateBlendSpace1D({
        entityId,
        blendSpaceId,
        data: {
          ...blendSpace,
          config: {
            ...blendSpace.config,
            blendCurveType: BlendCurveType.CUSTOM,
            customBlendCurve: curve.toString()
          }
        }
      }));
    }
  };

  // 处理预设曲线变更
  const handlePresetCurveChange = (presetName: string) => {
    // 更新混合空间配置
    if (blendSpace) {
      dispatch(updateBlendSpace1D({
        entityId,
        blendSpaceId,
        data: {
          ...blendSpace,
          config: {
            ...blendSpace.config,
            blendCurveType: BlendCurveType.CUSTOM,
            blendCurvePreset: presetName
          }
        }
      }));
    }
  };

  // 处理贝塞尔曲线变更
  const handleBezierCurveChange = (x1: number, y1: number, x2: number, y2: number) => {
    // 更新混合空间配置
    if (blendSpace) {
      dispatch(updateBlendSpace1D({
        entityId,
        blendSpaceId,
        data: {
          ...blendSpace,
          config: {
            ...blendSpace.config,
            blendCurveType: BlendCurveType.CUSTOM,
            bezierPoints: { x1, y1, x2, y2 }
          }
        }
      }));
    }
  };

  // 渲染节点表格
  const renderNodeTable = () => {
    if (!blendSpace || !blendSpace.nodes) return null;

    const columns = [
      {
        title: t('editor.animation.clipName'),
        dataIndex: 'clipName',
        key: 'clipName'
      },
      {
        title: t('editor.animation.position'),
        dataIndex: 'position',
        key: 'position',
        render: (text: number) => text.toFixed(2)
      },
      {
        title: t('editor.animation.weight'),
        dataIndex: 'weight',
        key: 'weight',
        render: (text: number) => text.toFixed(2)
      },
      {
        title: t('editor.actions'),
        key: 'actions',
        render: (_: any, record: any) => (
          <Space>
            <Tooltip title={t('editor.edit')}>
              <Button
                icon={<EditOutlined />}
                size="small"
                onClick={() => handleEditNode(record)}
              />
            </Tooltip>
            <Tooltip title={t('editor.delete')}>
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
                onClick={() => handleDeleteNode(record)}
              />
            </Tooltip>
          </Space>
        )
      }
    ];

    return (
      <Table
        dataSource={blendSpace.nodes}
        columns={columns}
        rowKey="id"
        size="small"
        pagination={false}
      />
    );
  };

  return (
    <div className="blend-space-1d-editor">
      <div className="editor-header">
        <h3>{blendSpace ? blendSpace.name : t('editor.animation.blendSpace1D')}</h3>

        <Space>
          {isEditing ? (
            <Button
              icon={<SaveOutlined />}
              type="primary"
              onClick={() => form.submit()}
            >
              {t('editor.save')}
            </Button>
          ) : (
            <Button
              icon={<EditOutlined />}
              onClick={() => setIsEditing(true)}
            >
              {t('editor.edit')}
            </Button>
          )}

          <Button
            icon={<CloseOutlined />}
            onClick={onClose}
          >
            {t('editor.close')}
          </Button>
        </Space>
      </div>

      <div className="editor-content">
        {isEditing ? (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleFormSubmit}
          >
            <Form.Item
              name="name"
              label={t('editor.animation.name')}
              rules={[{ required: true, message: t('editor.animation.nameRequired') }]}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name="description"
              label={t('editor.animation.description')}
            >
              <Input.TextArea rows={2} />
            </Form.Item>

            <Form.Item
              name="minValue"
              label={t('editor.animation.minValue')}
              rules={[{ required: true, message: t('editor.animation.minValueRequired') }]}
            >
              <InputNumber style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="maxValue"
              label={t('editor.animation.maxValue')}
              rules={[{ required: true, message: t('editor.animation.maxValueRequired') }]}
            >
              <InputNumber style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="normalizeInput"
              label={t('editor.animation.normalizeInput')}
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name="useSmoothing"
              label={t('editor.animation.useSmoothing')}
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name="smoothingFactor"
              label={t('editor.animation.smoothingFactor')}
            >
              <Slider min={0} max={1} step={0.01} />
            </Form.Item>

            <Form.Item
              name="enableExtrapolation"
              label={t('editor.animation.enableExtrapolation')}
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Divider>{t('editor.animation.curve.settings')}</Divider>

            <Form.Item
              name="blendCurveType"
              label={t('editor.animation.curve.type')}
            >
              <Select>
                <Option value={BlendCurveType.LINEAR}>{t('editor.animation.curve.linear')}</Option>
                <Option value={BlendCurveType.EASE_IN}>{t('editor.animation.curve.easeIn')}</Option>
                <Option value={BlendCurveType.EASE_OUT}>{t('editor.animation.curve.easeOut')}</Option>
                <Option value={BlendCurveType.EASE_IN_OUT}>{t('editor.animation.curve.easeInOut')}</Option>
                <Option value={BlendCurveType.ELASTIC}>{t('editor.animation.curve.elastic')}</Option>
                <Option value={BlendCurveType.BOUNCE}>{t('editor.animation.curve.bounce')}</Option>
                <Option value={BlendCurveType.SINE}>{t('editor.animation.curve.sine')}</Option>
                <Option value={BlendCurveType.EXPONENTIAL}>{t('editor.animation.curve.exponential')}</Option>
                <Option value={BlendCurveType.CIRCULAR}>{t('editor.animation.curve.circular')}</Option>
                <Option value={BlendCurveType.QUADRATIC}>{t('editor.animation.curve.quadratic')}</Option>
                <Option value={BlendCurveType.CUBIC}>{t('editor.animation.curve.cubic')}</Option>
                <Option value={BlendCurveType.QUARTIC}>{t('editor.animation.curve.quartic')}</Option>
                <Option value={BlendCurveType.QUINTIC}>{t('editor.animation.curve.quintic')}</Option>
                <Option value={BlendCurveType.CUSTOM}>{t('editor.animation.curve.custom')}</Option>
              </Select>
            </Form.Item>

            <Form.Item>
              <Button
                type="default"
                icon={<LineChartOutlined />}
                onClick={() => setShowCurveEditor(true)}
              >
                {t('editor.animation.curve.advancedSettings')}
              </Button>
            </Form.Item>
          </Form>
        ) : (
          <div className="blend-space-info">
            {blendSpace && (
              <>
                <p><strong>{t('editor.animation.description')}:</strong> {blendSpace.description || t('editor.animation.noDescription')}</p>
                <p><strong>{t('editor.animation.minValue')}:</strong> {blendSpace.config.minValue}</p>
                <p><strong>{t('editor.animation.maxValue')}:</strong> {blendSpace.config.maxValue}</p>
                <p><strong>{t('editor.animation.normalizeInput')}:</strong> {blendSpace.config.normalizeInput ? t('editor.yes') : t('editor.no')}</p>
                <p><strong>{t('editor.animation.useSmoothing')}:</strong> {blendSpace.config.useSmoothing ? t('editor.yes') : t('editor.no')}</p>
                <p><strong>{t('editor.animation.smoothingFactor')}:</strong> {blendSpace.config.smoothingFactor}</p>
                <p><strong>{t('editor.animation.enableExtrapolation')}:</strong> {blendSpace.config.enableExtrapolation ? t('editor.yes') : t('editor.no')}</p>
                <Divider>{t('editor.animation.curve.settings')}</Divider>
                <p><strong>{t('editor.animation.curve.type')}:</strong> {t(`editor.animation.curve.${blendSpace.config.blendCurveType || 'linear'}`)}</p>
                {blendSpace.config.blendCurveType === BlendCurveType.CUSTOM && blendSpace.config.blendCurvePreset && (
                  <p><strong>{t('editor.animation.curve.preset')}:</strong> {blendSpace.config.blendCurvePreset}</p>
                )}
                <Button
                  type="default"
                  icon={<LineChartOutlined />}
                  onClick={() => setShowCurveEditor(true)}
                  style={{ marginTop: '8px' }}
                >
                  {t('editor.animation.curve.advancedSettings')}
                </Button>
              </>
            )}
          </div>
        )}

        <Divider>{t('editor.animation.visualization')}</Divider>

        <div className="blend-space-visualization">
          <div className="blend-space-toolbar">
            <Space>
              <Button
                icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={handlePlayPause}
                title={isPlaying ? t('editor.animation.pause') : t('editor.animation.play')}
              >
                {isPlaying ? t('editor.animation.pause') : t('editor.animation.play')}
              </Button>

              <Button
                icon={<LineChartOutlined />}
                onClick={toggleHeatmap}
                type={showHeatmap ? 'primary' : 'default'}
                title={t('editor.animation.toggleHeatmap')}
              >
                {t('editor.animation.heatmap')}
              </Button>

              <Tooltip title={t('editor.animation.toggleWeights')}>
                <Button
                  icon={showNodeWeights ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                  onClick={toggleNodeWeights}
                  type={showNodeWeights ? 'primary' : 'default'}
                >
                  {t('editor.animation.weights')}
                </Button>
              </Tooltip>

              <Tooltip title={t('editor.animation.curve.settings')}>
                <Button
                  icon={<SettingOutlined />}
                  onClick={() => setShowCurveEditor(true)}
                >
                  {t('editor.animation.curve.settings')}
                </Button>
              </Tooltip>
            </Space>

            <div className="position-display">
              {t('editor.animation.position')}: {currentPosition.toFixed(2)}
            </div>
          </div>

          <canvas
            ref={canvasRef}
            width={600}
            height={200}
            className="blend-space-canvas"
            onClick={handleCanvasClick}
            onMouseDown={handleCanvasMouseDown}
            onMouseMove={handleCanvasMouseMove}
            onMouseUp={handleCanvasMouseUp}
            onMouseLeave={handleCanvasMouseLeave}
          />

          <div className="blend-space-controls">
            <Space>
              <span>{t('editor.animation.position')}:</span>

              <Slider
                value={currentPosition}
                min={blendSpace ? blendSpace.config.minValue : 0}
                max={blendSpace ? blendSpace.config.maxValue : 1}
                step={0.01}
                style={{ width: 300 }}
                onChange={handlePositionChange}
              />

              <InputNumber
                value={currentPosition}
                min={blendSpace ? blendSpace.config.minValue : 0}
                max={blendSpace ? blendSpace.config.maxValue : 1}
                step={0.01}
                onChange={handlePositionChange}
              />
            </Space>
          </div>

          <div className="blend-space-instructions">
            {t('editor.animation.dragNodesInstructions')}
          </div>
        </div>

        <Divider>{t('editor.animation.nodes')}</Divider>

        <div className="blend-space-nodes">
          <div className="nodes-header">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddNode}
            >
              {t('editor.animation.addNode')}
            </Button>
          </div>

          <div className="nodes-content">
            {renderNodeTable()}
          </div>
        </div>
      </div>

      <Modal
        title={t('editor.animation.curve.editor')}
        visible={showCurveEditor}
        onCancel={() => setShowCurveEditor(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setShowCurveEditor(false)}>
            {t('editor.close')}
          </Button>
        ]}
      >
        <BlendCurveEditor
          initialCurveType={curveType}
          onCurveTypeChange={handleCurveTypeChange}
          onCustomCurveChange={handleCustomCurveChange}
          onPresetCurveChange={handlePresetCurveChange}
          onBezierCurveChange={handleBezierCurveChange}
        />
      </Modal>

      <Modal
        title={editingNode ? t('editor.animation.editNode') : t('editor.animation.addNode')}
        visible={isAddingNode}
        onCancel={() => setIsAddingNode(false)}
        footer={null}
      >
        <Form
          form={nodeForm}
          layout="vertical"
          onFinish={handleNodeFormSubmit}
        >
          <Form.Item
            name="clipName"
            label={t('editor.animation.clipName')}
            rules={[{ required: true, message: t('editor.animation.clipNameRequired') }]}
          >
            <Select>
              {availableClips.map(clip => (
                <Option key={clip} value={clip}>{clip}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="position"
            label={t('editor.animation.position')}
            rules={[{ required: true, message: t('editor.animation.positionRequired') }]}
          >
            <InputNumber
              min={blendSpace ? blendSpace.config.minValue : 0}
              max={blendSpace ? blendSpace.config.maxValue : 1}
              step={0.01}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingNode ? t('editor.update') : t('editor.add')}
              </Button>
              <Button onClick={() => setIsAddingNode(false)}>
                {t('editor.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default BlendSpace1DEditor;
