/**
 * 地形生成算法枚举
 */
export enum TerrainGenerationAlgorithm {
  /** 柏林噪声 */
  PERLIN = 'perlin',
  /** 分形 */
  FRACTAL = 'fractal',
  /** 金刚石方块 */
  DIAMOND_SQUARE = 'diamond_square',
  /** 多重分形 */
  MULTIFRACTAL = 'multifractal',
  /** 高度图 */
  HEIGHTMAP = 'heightmap',
  /** 热侵蚀 */
  THERMAL_EROSION = 'thermal_erosion',
  /** 水侵蚀 */
  HYDRAULIC_EROSION = 'hydraulic_erosion',
  /** 河流生成 */
  RIVER = 'river',
  /** 地下河生成 */
  UNDERGROUND_RIVER = 'underground_river',
  /** 地下湖泊生成 */
  UNDERGROUND_LAKE = 'underground_lake',
  /** 山脉生成 */
  MOUNTAIN = 'mountain',
  /** 峡谷生成 */
  CANYON = 'canyon',
  /** 洞穴生成 */
  CAVE = 'cave',
  /** 悬崖生成 */
  CLIFF = 'cliff',
  /** 火山生成 */
  VOLCANO = 'volcano',
  /** 平原生成 */
  PLAIN = 'plain',
  /** 丘陵生成 */
  HILLS = 'hills',
  /** 沙漠生成 */
  DESERT = 'desert',
  /** 岛屿生成 */
  ISLAND = 'island',
  /** 特征组合生成 */
  FEATURE_COMBINATION = 'feature_combination'
}
