/**
 * 子片段
 * 用于从动画片段中提取子片段
 */
import * as THREE from 'three';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 子片段事件类型
 */
export enum SubClipEventType {
  /** 子片段创建 */
  CREATED = 'created',
  /** 子片段更新 */
  UPDATED = 'updated'
}

/**
 * 子片段配置
 */
export interface SubClipConfig {
  /** 子片段名称 */
  name?: string;
  /** 源片段名称 */
  sourceName?: string;
  /** 开始时间（秒） */
  startTime?: number;
  /** 结束时间（秒） */
  endTime?: number;
  /** 是否循环 */
  loop?: boolean;
  /** 是否反向播放 */
  reverse?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 子片段
 * 用于从动画片段中提取子片段
 */
export class SubClip {
  /** 子片段名称 */
  private name: string;
  /** 源片段名称 */
  private sourceName: string;
  /** 开始时间（秒） */
  private startTime: number;
  /** 结束时间（秒） */
  private endTime: number;
  /** 是否循环 */
  private loop: boolean;
  /** 是否反向播放 */
  private reverse: boolean;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 是否启用调试 */
  private debug: boolean;

  /**
   * 创建子片段
   * @param config 配置
   */
  constructor(config: SubClipConfig = {}) {
    this.name = config.name || 'subClip';
    this.sourceName = config.sourceName || '';
    this.startTime = config.startTime !== undefined ? config.startTime : 0;
    this.endTime = config.endTime !== undefined ? config.endTime : 1;
    this.loop = config.loop !== undefined ? config.loop : true;
    this.reverse = config.reverse !== undefined ? config.reverse : false;
    this.debug = config.debug || false;
  }

  /**
   * 获取子片段名称
   * @returns 子片段名称
   */
  public getName(): string {
    return this.name;
  }

  /**
   * 设置子片段名称
   * @param name 子片段名称
   */
  public setName(name: string): void {
    this.name = name;
  }

  /**
   * 获取源片段名称
   * @returns 源片段名称
   */
  public getSourceName(): string {
    return this.sourceName;
  }

  /**
   * 设置源片段名称
   * @param sourceName 源片段名称
   */
  public setSourceName(sourceName: string): void {
    this.sourceName = sourceName;
  }

  /**
   * 获取开始时间
   * @returns 开始时间（秒）
   */
  public getStartTime(): number {
    return this.startTime;
  }

  /**
   * 设置开始时间
   * @param startTime 开始时间（秒）
   */
  public setStartTime(startTime: number): void {
    this.startTime = startTime;

    // 触发更新事件
    this.eventEmitter.emit(SubClipEventType.UPDATED, { startTime });

    if (this.debug) {
      console.log(`设置子片段开始时间: ${startTime}`);
    }
  }

  /**
   * 获取结束时间
   * @returns 结束时间（秒）
   */
  public getEndTime(): number {
    return this.endTime;
  }

  /**
   * 设置结束时间
   * @param endTime 结束时间（秒）
   */
  public setEndTime(endTime: number): void {
    this.endTime = endTime;

    // 触发更新事件
    this.eventEmitter.emit(SubClipEventType.UPDATED, { endTime });

    if (this.debug) {
      console.log(`设置子片段结束时间: ${endTime}`);
    }
  }

  /**
   * 获取持续时间
   * @returns 持续时间（秒）
   */
  public getDuration(): number {
    return this.endTime - this.startTime;
  }

  /**
   * 是否循环
   * @returns 是否循环
   */
  public isLoop(): boolean {
    return this.loop;
  }

  /**
   * 设置是否循环
   * @param loop 是否循环
   */
  public setLoop(loop: boolean): void {
    this.loop = loop;

    // 触发更新事件
    this.eventEmitter.emit(SubClipEventType.UPDATED, { loop });

    if (this.debug) {
      console.log(`设置子片段循环: ${loop}`);
    }
  }

  /**
   * 是否反向播放
   * @returns 是否反向播放
   */
  public isReverse(): boolean {
    return this.reverse;
  }

  /**
   * 设置是否反向播放
   * @param reverse 是否反向播放
   */
  public setReverse(reverse: boolean): void {
    this.reverse = reverse;

    // 触发更新事件
    this.eventEmitter.emit(SubClipEventType.UPDATED, { reverse });

    if (this.debug) {
      console.log(`设置子片段反向播放: ${reverse}`);
    }
  }

  /**
   * 从源片段创建子片段
   * @param sourceClip 源片段
   * @returns 子片段
   */
  public createFromClip(sourceClip: THREE.AnimationClip): THREE.AnimationClip {
    if (!sourceClip) {
      console.error('源片段为空');
      return null!;
    }

    // 检查时间范围
    if (this.startTime < 0) {
      console.warn(`开始时间 ${this.startTime} 小于0，将设置为0`);
      this.startTime = 0;
    }

    if (this.endTime > sourceClip.duration) {
      console.warn(`结束时间 ${this.endTime} 大于源片段持续时间 ${sourceClip.duration}，将设置为源片段持续时间`);
      this.endTime = sourceClip.duration;
    }

    if (this.startTime >= this.endTime) {
      console.error(`开始时间 ${this.startTime} 大于或等于结束时间 ${this.endTime}`);
      return null!;
    }

    // 创建子片段
    const subClip = THREE.AnimationUtils.subclip(
      sourceClip,
      this.name,
      this.startTime,
      this.endTime,
      this.loop ? THREE.LoopRepeat : THREE.LoopOnce
    );

    // 如果需要反向播放，反转轨道
    if (this.reverse) {
      this.reverseClip(subClip);
    }

    // 触发创建事件
    this.eventEmitter.emit(SubClipEventType.CREATED, { clip: subClip });

    if (this.debug) {
      console.log(`创建子片段: ${this.name}, 源片段: ${sourceClip.name}, 开始时间: ${this.startTime}, 结束时间: ${this.endTime}, 持续时间: ${this.getDuration()}`);
    }

    return subClip;
  }

  /**
   * 反转片段
   * @param clip 动画片段
   */
  private reverseClip(clip: THREE.AnimationClip): void {
    // 遍历所有轨道
    for (const track of clip.tracks) {
      // 获取时间和值
      const times = track.times.slice();
      const values = track.values.slice();

      // 反转时间
      track.times = times.reverse();

      // 反转值
      const valueSize = track.getValueSize();
      const valueCount = values.length / valueSize;

      const newValues = new Float32Array(values.length);

      for (let i = 0; i < valueCount; i++) {
        const srcOffset = i * valueSize;
        const dstOffset = (valueCount - i - 1) * valueSize;

        for (let j = 0; j < valueSize; j++) {
          newValues[dstOffset + j] = values[srcOffset + j];
        }
      }

      track.values = newValues;
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public addEventListener(event: SubClipEventType, listener: (data: any) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public removeEventListener(event: SubClipEventType, listener: (data: any) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 创建动作子片段
   * @param sourceClip 源片段
   * @param actionName 动作名称
   * @param startTime 开始时间（秒）
   * @param endTime 结束时间（秒）
   * @param loop 是否循环
   * @param debug 是否启用调试
   * @returns 子片段
   */
  public static createActionSubClip(
    sourceClip: THREE.AnimationClip,
    actionName: string,
    startTime: number,
    endTime: number,
    loop: boolean = true,
    debug: boolean = false
  ): THREE.AnimationClip {
    const subClip = new SubClip({
      name: `${sourceClip.name}_${actionName}`,
      sourceName: sourceClip.name,
      startTime,
      endTime,
      loop,
      debug
    });

    return subClip.createFromClip(sourceClip);
  }

  /**
   * 创建循环子片段
   * @param sourceClip 源片段
   * @param loopStartTime 循环开始时间（秒）
   * @param loopEndTime 循环结束时间（秒）
   * @param debug 是否启用调试
   * @returns 子片段
   */
  public static createLoopSubClip(
    sourceClip: THREE.AnimationClip,
    loopStartTime: number,
    loopEndTime: number,
    debug: boolean = false
  ): THREE.AnimationClip {
    const subClip = new SubClip({
      name: `${sourceClip.name}_loop`,
      sourceName: sourceClip.name,
      startTime: loopStartTime,
      endTime: loopEndTime,
      loop: true,
      debug
    });

    return subClip.createFromClip(sourceClip);
  }

  /**
   * 创建入场子片段
   * @param sourceClip 源片段
   * @param entryEndTime 入场结束时间（秒）
   * @param debug 是否启用调试
   * @returns 子片段
   */
  public static createEntrySubClip(
    sourceClip: THREE.AnimationClip,
    entryEndTime: number,
    debug: boolean = false
  ): THREE.AnimationClip {
    const subClip = new SubClip({
      name: `${sourceClip.name}_entry`,
      sourceName: sourceClip.name,
      startTime: 0,
      endTime: entryEndTime,
      loop: false,
      debug
    });

    return subClip.createFromClip(sourceClip);
  }

  /**
   * 创建退场子片段
   * @param sourceClip 源片段
   * @param exitStartTime 退场开始时间（秒）
   * @param debug 是否启用调试
   * @returns 子片段
   */
  public static createExitSubClip(
    sourceClip: THREE.AnimationClip,
    exitStartTime: number,
    debug: boolean = false
  ): THREE.AnimationClip {
    const subClip = new SubClip({
      name: `${sourceClip.name}_exit`,
      sourceName: sourceClip.name,
      startTime: exitStartTime,
      endTime: sourceClip.duration,
      loop: false,
      debug
    });

    return subClip.createFromClip(sourceClip);
  }
}
