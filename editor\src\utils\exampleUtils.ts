/**
 * 示例项目工具函数
 */
import { ExampleCategory } from '../types/example';

/**
 * 获取类别颜色
 * @param category 类别
 * @returns 颜色代码
 */
export const getCategoryColor = (category: ExampleCategory): string => {
  switch (category) {
    case 'basic':
      return 'blue';
    case 'material':
      return 'purple';
    case 'animation':
      return 'green';
    case 'physics':
      return 'orange';
    case 'visualscript':
      return 'cyan';
    case 'performance':
      return 'red';
    case 'collaboration':
      return 'magenta';
    case 'tutorial':
      return 'gold';
    default:
      return 'default';
  }
};

/**
 * 获取标签颜色
 * @param tag 标签
 * @returns 颜色代码
 */
export const getTagColor = (tag: string): string => {
  // 根据标签名称的哈希值生成颜色
  const hash = tag.split('').reduce((acc, char) => {
    return char.charCodeAt(0) + ((acc << 5) - acc);
  }, 0);
  
  const colors = [
    'magenta', 'red', 'volcano', 'orange', 'gold',
    'lime', 'green', 'cyan', 'blue', 'geekblue', 'purple'
  ];
  
  const index = Math.abs(hash) % colors.length;
  return colors[index];
};

/**
 * 格式化日期
 * @param dateString 日期字符串
 * @returns 格式化后的日期字符串
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 获取文件扩展名
 * @param filename 文件名
 * @returns 文件扩展名
 */
export const getFileExtension = (filename: string): string => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
};

/**
 * 获取文件图标
 * @param filename 文件名
 * @returns 图标名称
 */
export const getFileIcon = (filename: string): string => {
  const ext = getFileExtension(filename).toLowerCase();
  
  switch (ext) {
    case 'html':
    case 'htm':
      return 'html';
    case 'css':
      return 'css';
    case 'js':
    case 'ts':
    case 'jsx':
    case 'tsx':
      return 'js';
    case 'json':
      return 'json';
    case 'md':
      return 'markdown';
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
    case 'svg':
      return 'image';
    case 'mp3':
    case 'wav':
    case 'ogg':
      return 'audio';
    case 'mp4':
    case 'webm':
    case 'avi':
      return 'video';
    case 'glb':
    case 'gltf':
    case 'obj':
    case 'fbx':
      return 'model';
    default:
      return 'file';
  }
};
