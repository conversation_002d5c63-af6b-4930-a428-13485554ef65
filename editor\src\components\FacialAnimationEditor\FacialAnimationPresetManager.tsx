/**
 * 面部动画预设管理组件
 * 用于管理面部动画预设和模板
 */
import React, { useState, useEffect, useRef } from 'react';
import { 
  Card, 
  Button, 
  Tabs, 
  List, 
  Tag, 
  Space, 
  Input, 
  Select, 
  Modal, 
  Form, 
  message, 
  Tooltip, 
  Popconfirm,
  Upload,
  Divider,
  Empty
} from 'antd';
import { 
  PlusOutlined, 
  DeleteOutlined, 
  EditOutlined, 
  ExportOutlined, 
  ImportOutlined, 
  CopyOutlined,
  EyeOutlined,
  SaveOutlined,
  UploadOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { FacialExpressionType } from '../../../engine/src/animation/FacialAnimation';
import { VisemeType } from '../../../engine/src/animation/FacialAnimation';
import { FacialAnimationPresetType } from '../../../engine/src/avatar/presets/FacialAnimationPresetSystem';
import { FacialAnimationPreview } from './FacialAnimationPreview';
import './FacialAnimationPresetManager.less';

const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 面部动画预设
 */
interface FacialAnimationPreset {
  id: string;
  name: string;
  type: FacialAnimationPresetType;
  description?: string;
  tags?: string[];
  culture?: string;
  expression?: FacialExpressionType;
  weight?: number;
  expressionCombos?: { expression: FacialExpressionType, weight: number }[];
  animationSequence?: { expression: FacialExpressionType, weight: number, duration: number }[];
  author?: string;
  createdAt?: Date;
  updatedAt?: Date;
  thumbnail?: string;
}

/**
 * 面部动画预设管理器属性
 */
interface FacialAnimationPresetManagerProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
  /** 默认预设类型 */
  defaultPresetType?: FacialAnimationPresetType;
  /** 默认文化 */
  defaultCulture?: string;
  /** 应用预设回调 */
  onPresetApply?: (preset: FacialAnimationPreset) => void;
  /** 导入预设回调 */
  onPresetImport?: (presets: FacialAnimationPreset[]) => void;
  /** 导出预设回调 */
  onPresetExport?: (presets: FacialAnimationPreset[]) => void;
}

/**
 * 面部动画预设管理器
 */
export const FacialAnimationPresetManager: React.FC<FacialAnimationPresetManagerProps> = ({
  entityId,
  editable = true,
  defaultPresetType = FacialAnimationPresetType.STANDARD,
  defaultCulture = 'global',
  onPresetApply,
  onPresetImport,
  onPresetExport
}) => {
  const { t } = useTranslation();
  
  // 状态
  const [presets, setPresets] = useState<FacialAnimationPreset[]>([]);
  const [filteredPresets, setFilteredPresets] = useState<FacialAnimationPreset[]>([]);
  const [selectedPreset, setSelectedPreset] = useState<FacialAnimationPreset | null>(null);
  const [presetType, setPresetType] = useState<FacialAnimationPresetType>(defaultPresetType);
  const [culture, setCulture] = useState<string>(defaultCulture);
  const [searchText, setSearchText] = useState<string>('');
  const [isPreviewVisible, setIsPreviewVisible] = useState<boolean>(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState<boolean>(false);
  const [isImportModalVisible, setIsImportModalVisible] = useState<boolean>(false);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  
  // 表单引用
  const [form] = Form.useForm();
  
  // 预设类型列表
  const presetTypes = [
    { value: FacialAnimationPresetType.STANDARD, label: t('editor.animation.presetTypes.standard') },
    { value: FacialAnimationPresetType.CULTURAL, label: t('editor.animation.presetTypes.cultural') },
    { value: FacialAnimationPresetType.EMOTION_COMBO, label: t('editor.animation.presetTypes.emotionCombo') },
    { value: FacialAnimationPresetType.ANIMATION_SEQUENCE, label: t('editor.animation.presetTypes.animationSequence') },
    { value: FacialAnimationPresetType.CUSTOM, label: t('editor.animation.presetTypes.custom') }
  ];
  
  // 文化列表
  const cultures = [
    { value: 'global', label: t('editor.animation.cultures.global') },
    { value: 'chinese', label: t('editor.animation.cultures.chinese') },
    { value: 'japanese', label: t('editor.animation.cultures.japanese') },
    { value: 'american', label: t('editor.animation.cultures.american') }
  ];
  
  // 表情列表
  const expressions = Object.values(FacialExpressionType).map(type => ({
    value: type,
    label: t(`editor.animation.expressions.${type}`)
  }));
  
  // 加载预设
  useEffect(() => {
    loadPresets();
  }, []);
  
  // 过滤预设
  useEffect(() => {
    filterPresets();
  }, [presets, presetType, culture, searchText]);
  
  // 加载预设
  const loadPresets = async () => {
    try {
      // 这里应该从引擎中加载预设
      // 示例数据，实际实现需要与引擎集成
      const mockPresets: FacialAnimationPreset[] = [
        {
          id: 'happy',
          name: '开心',
          type: FacialAnimationPresetType.STANDARD,
          description: '标准开心表情',
          tags: ['基础', '积极'],
          expression: FacialExpressionType.HAPPY,
          weight: 1.0,
          culture: 'global',
          author: '系统',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'sad',
          name: '悲伤',
          type: FacialAnimationPresetType.STANDARD,
          description: '标准悲伤表情',
          tags: ['基础', '消极'],
          expression: FacialExpressionType.SAD,
          weight: 1.0,
          culture: 'global',
          author: '系统',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'chinese_smile',
          name: '含蓄微笑',
          type: FacialAnimationPresetType.CULTURAL,
          description: '中国传统含蓄微笑',
          tags: ['文化', '微笑'],
          culture: 'chinese',
          expressionCombos: [
            { expression: FacialExpressionType.HAPPY, weight: 0.5 },
            { expression: FacialExpressionType.NEUTRAL, weight: 0.5 }
          ],
          author: '系统',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'laugh_sequence',
          name: '笑声序列',
          type: FacialAnimationPresetType.ANIMATION_SEQUENCE,
          description: '从微笑到大笑的序列',
          tags: ['序列', '积极'],
          culture: 'global',
          animationSequence: [
            { expression: FacialExpressionType.HAPPY, weight: 0.5, duration: 0.3 },
            { expression: FacialExpressionType.HAPPY, weight: 0.8, duration: 0.2 },
            { expression: FacialExpressionType.HAPPY, weight: 1.0, duration: 0.5 },
            { expression: FacialExpressionType.HAPPY, weight: 0.8, duration: 0.3 },
            { expression: FacialExpressionType.HAPPY, weight: 0.5, duration: 0.2 },
            { expression: FacialExpressionType.NEUTRAL, weight: 0.3, duration: 0.2 }
          ],
          author: '系统',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
      
      setPresets(mockPresets);
    } catch (error) {
      console.error('加载预设失败:', error);
      message.error(t('editor.animation.loadPresetsFailed'));
    }
  };
  
  // 过滤预设
  const filterPresets = () => {
    let filtered = [...presets];
    
    // 按类型过滤
    if (presetType) {
      filtered = filtered.filter(preset => preset.type === presetType);
    }
    
    // 按文化过滤
    if (culture && presetType === FacialAnimationPresetType.CULTURAL) {
      filtered = filtered.filter(preset => preset.culture === culture);
    }
    
    // 按搜索文本过滤
    if (searchText) {
      const lowerSearchText = searchText.toLowerCase();
      filtered = filtered.filter(preset => 
        preset.name.toLowerCase().includes(lowerSearchText) || 
        preset.description?.toLowerCase().includes(lowerSearchText) ||
        preset.tags?.some(tag => tag.toLowerCase().includes(lowerSearchText))
      );
    }
    
    setFilteredPresets(filtered);
  };
  
  // 应用预设
  const applyPreset = (preset: FacialAnimationPreset) => {
    if (onPresetApply) {
      onPresetApply(preset);
    }
    
    message.success(t('editor.animation.presetApplied', { name: preset.name }));
  };
  
  // 预览预设
  const previewPreset = (preset: FacialAnimationPreset) => {
    setSelectedPreset(preset);
    setIsPreviewVisible(true);
    setIsPlaying(true);
  };
  
  // 编辑预设
  const editPreset = (preset: FacialAnimationPreset) => {
    setSelectedPreset(preset);
    form.setFieldsValue({
      name: preset.name,
      description: preset.description,
      type: preset.type,
      culture: preset.culture,
      tags: preset.tags?.join(', '),
      expression: preset.expression,
      weight: preset.weight
    });
    setIsEditModalVisible(true);
  };
  
  // 删除预设
  const deletePreset = async (preset: FacialAnimationPreset) => {
    try {
      // 这里应该从引擎中删除预设
      // 示例代码，实际实现需要与引擎集成
      setPresets(presets.filter(p => p.id !== preset.id));
      message.success(t('editor.animation.presetDeleted', { name: preset.name }));
    } catch (error) {
      console.error('删除预设失败:', error);
      message.error(t('editor.animation.deletePresetFailed'));
    }
  };
  
  // 创建新预设
  const createPreset = () => {
    setSelectedPreset(null);
    form.resetFields();
    form.setFieldsValue({
      type: presetType,
      culture: culture
    });
    setIsEditModalVisible(true);
  };
  
  // 保存预设
  const savePreset = async (values: any) => {
    try {
      // 解析标签
      const tags = values.tags ? values.tags.split(',').map((tag: string) => tag.trim()) : [];
      
      // 创建预设对象
      const preset: FacialAnimationPreset = {
        id: selectedPreset?.id || `preset_${Date.now()}`,
        name: values.name,
        type: values.type,
        description: values.description,
        tags,
        culture: values.culture,
        expression: values.expression,
        weight: values.weight,
        expressionCombos: values.expressionCombos,
        animationSequence: values.animationSequence,
        author: '用户',
        createdAt: selectedPreset?.createdAt || new Date(),
        updatedAt: new Date()
      };
      
      // 更新或添加预设
      if (selectedPreset) {
        // 更新预设
        setPresets(presets.map(p => p.id === preset.id ? preset : p));
        message.success(t('editor.animation.presetUpdated', { name: preset.name }));
      } else {
        // 添加预设
        setPresets([...presets, preset]);
        message.success(t('editor.animation.presetCreated', { name: preset.name }));
      }
      
      // 关闭编辑模态框
      setIsEditModalVisible(false);
    } catch (error) {
      console.error('保存预设失败:', error);
      message.error(t('editor.animation.savePresetFailed'));
    }
  };
  
  // 导入预设
  const importPresets = async (importedPresets: FacialAnimationPreset[]) => {
    try {
      // 合并预设
      const mergedPresets = [...presets];
      
      for (const preset of importedPresets) {
        const existingIndex = mergedPresets.findIndex(p => p.id === preset.id);
        
        if (existingIndex >= 0) {
          // 更新现有预设
          mergedPresets[existingIndex] = preset;
        } else {
          // 添加新预设
          mergedPresets.push(preset);
        }
      }
      
      // 更新预设列表
      setPresets(mergedPresets);
      
      // 回调
      if (onPresetImport) {
        onPresetImport(importedPresets);
      }
      
      message.success(t('editor.animation.presetsImported', { count: importedPresets.length }));
      
      // 关闭导入模态框
      setIsImportModalVisible(false);
    } catch (error) {
      console.error('导入预设失败:', error);
      message.error(t('editor.animation.importPresetsFailed'));
    }
  };
  
  // 导出预设
  const exportPresets = async () => {
    try {
      // 导出所有预设
      const presetsToExport = filteredPresets.length > 0 ? filteredPresets : presets;
      
      // 创建JSON数据
      const jsonData = JSON.stringify(presetsToExport, null, 2);
      
      // 创建下载链接
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `facial_animation_presets_${new Date().toISOString().slice(0, 10)}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      // 回调
      if (onPresetExport) {
        onPresetExport(presetsToExport);
      }
      
      message.success(t('editor.animation.presetsExported', { count: presetsToExport.length }));
    } catch (error) {
      console.error('导出预设失败:', error);
      message.error(t('editor.animation.exportPresetsFailed'));
    }
  };
  
  return (
    <div className="facial-animation-preset-manager">
      <div className="preset-manager-header">
        <Input.Search
          placeholder={t('editor.animation.searchPresets')}
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          style={{ width: 200 }}
        />
        
        <Space>
          <Select
            value={presetType}
            onChange={setPresetType}
            style={{ width: 150 }}
          >
            {presetTypes.map(type => (
              <Option key={type.value} value={type.value}>{type.label}</Option>
            ))}
          </Select>
          
          {presetType === FacialAnimationPresetType.CULTURAL && (
            <Select
              value={culture}
              onChange={setCulture}
              style={{ width: 120 }}
            >
              {cultures.map(culture => (
                <Option key={culture.value} value={culture.value}>{culture.label}</Option>
              ))}
            </Select>
          )}
        </Space>
        
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={createPreset}
            disabled={!editable}
          >
            {t('editor.animation.createPreset')}
          </Button>
          
          <Button
            icon={<ImportOutlined />}
            onClick={() => setIsImportModalVisible(true)}
            disabled={!editable}
          >
            {t('editor.animation.importPresets')}
          </Button>
          
          <Button
            icon={<ExportOutlined />}
            onClick={exportPresets}
          >
            {t('editor.animation.exportPresets')}
          </Button>
        </Space>
      </div>
      
      <div className="preset-list">
        {filteredPresets.length === 0 ? (
          <Empty description={t('editor.animation.noPresets')} />
        ) : (
          <List
            grid={{ gutter: 16, column: 3 }}
            dataSource={filteredPresets}
            renderItem={preset => (
              <List.Item>
                <Card
                  hoverable
                  cover={preset.thumbnail && <img alt={preset.name} src={preset.thumbnail} />}
                  actions={[
                    <Tooltip title={t('editor.animation.applyPreset')}>
                      <Button type="text" icon={<SaveOutlined />} onClick={() => applyPreset(preset)} />
                    </Tooltip>,
                    <Tooltip title={t('editor.animation.previewPreset')}>
                      <Button type="text" icon={<EyeOutlined />} onClick={() => previewPreset(preset)} />
                    </Tooltip>,
                    editable && (
                      <Tooltip title={t('editor.animation.editPreset')}>
                        <Button type="text" icon={<EditOutlined />} onClick={() => editPreset(preset)} />
                      </Tooltip>
                    ),
                    editable && (
                      <Popconfirm
                        title={t('editor.animation.confirmDeletePreset')}
                        onConfirm={() => deletePreset(preset)}
                        okText={t('common.yes')}
                        cancelText={t('common.no')}
                      >
                        <Tooltip title={t('editor.animation.deletePreset')}>
                          <Button type="text" icon={<DeleteOutlined />} />
                        </Tooltip>
                      </Popconfirm>
                    )
                  ]}
                >
                  <Card.Meta
                    title={preset.name}
                    description={
                      <>
                        <div>{preset.description}</div>
                        <div className="preset-tags">
                          {preset.tags?.map(tag => (
                            <Tag key={tag}>{tag}</Tag>
                          ))}
                        </div>
                      </>
                    }
                  />
                </Card>
              </List.Item>
            )}
          />
        )}
      </div>
      
      {/* 预览模态框 */}
      <Modal
        title={t('editor.animation.previewPreset', { name: selectedPreset?.name })}
        open={isPreviewVisible}
        onCancel={() => setIsPreviewVisible(false)}
        footer={null}
        width={800}
      >
        <div className="preset-preview">
          <FacialAnimationPreview
            entityId={entityId}
            currentTime={currentTime}
            isPlaying={isPlaying}
          />
          
          <div className="preset-details">
            <h3>{selectedPreset?.name}</h3>
            <p>{selectedPreset?.description}</p>
            
            <div className="preset-info">
              <div><strong>{t('editor.animation.presetType')}:</strong> {presetTypes.find(t => t.value === selectedPreset?.type)?.label}</div>
              {selectedPreset?.culture && (
                <div><strong>{t('editor.animation.culture')}:</strong> {cultures.find(c => c.value === selectedPreset?.culture)?.label}</div>
              )}
              {selectedPreset?.author && (
                <div><strong>{t('editor.animation.author')}:</strong> {selectedPreset?.author}</div>
              )}
            </div>
            
            <div className="preset-tags">
              {selectedPreset?.tags?.map(tag => (
                <Tag key={tag}>{tag}</Tag>
              ))}
            </div>
            
            <Button
              type="primary"
              onClick={() => {
                if (selectedPreset) {
                  applyPreset(selectedPreset);
                }
                setIsPreviewVisible(false);
              }}
            >
              {t('editor.animation.applyPreset')}
            </Button>
          </div>
        </div>
      </Modal>
      
      {/* 编辑模态框 */}
      <Modal
        title={selectedPreset ? t('editor.animation.editPreset', { name: selectedPreset.name }) : t('editor.animation.createPreset')}
        open={isEditModalVisible}
        onCancel={() => setIsEditModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={savePreset}
        >
          <Form.Item
            name="name"
            label={t('editor.animation.presetName')}
            rules={[{ required: true, message: t('editor.animation.presetNameRequired') }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="description"
            label={t('editor.animation.presetDescription')}
          >
            <Input.TextArea rows={2} />
          </Form.Item>
          
          <Form.Item
            name="type"
            label={t('editor.animation.presetType')}
            rules={[{ required: true, message: t('editor.animation.presetTypeRequired') }]}
          >
            <Select>
              {presetTypes.map(type => (
                <Option key={type.value} value={type.value}>{type.label}</Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="culture"
            label={t('editor.animation.culture')}
            rules={[{ required: true, message: t('editor.animation.cultureRequired') }]}
          >
            <Select>
              {cultures.map(culture => (
                <Option key={culture.value} value={culture.value}>{culture.label}</Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="tags"
            label={t('editor.animation.tags')}
            help={t('editor.animation.tagsHelp')}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="expression"
            label={t('editor.animation.expression')}
          >
            <Select>
              {expressions.map(expr => (
                <Option key={expr.value} value={expr.value}>{expr.label}</Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="weight"
            label={t('editor.animation.expressionWeight')}
          >
            <Input type="number" min={0} max={1} step={0.1} />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {t('common.save')}
              </Button>
              <Button onClick={() => setIsEditModalVisible(false)}>
                {t('common.cancel')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 导入模态框 */}
      <Modal
        title={t('editor.animation.importPresets')}
        open={isImportModalVisible}
        onCancel={() => setIsImportModalVisible(false)}
        footer={null}
      >
        <Upload.Dragger
          accept=".json"
          beforeUpload={(file) => {
            const reader = new FileReader();
            reader.onload = (e) => {
              try {
                const importedPresets = JSON.parse(e.target?.result as string);
                importPresets(importedPresets);
              } catch (error) {
                console.error('解析导入文件失败:', error);
                message.error(t('editor.animation.parseImportFileFailed'));
              }
            };
            reader.readAsText(file);
            return false;
          }}
          showUploadList={false}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">{t('editor.animation.clickOrDragToImport')}</p>
          <p className="ant-upload-hint">{t('editor.animation.importPresetHint')}</p>
        </Upload.Dragger>
      </Modal>
    </div>
  );
};
