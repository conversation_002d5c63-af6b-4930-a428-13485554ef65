/**
 * 基础物理示例
 * 展示如何使用物理系统创建简单的物理场景
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Vector3 } from '../../src/math/Vector3';
import { Quaternion } from '../../src/math/Quaternion';
import { 
  PhysicsSystem, 
  PhysicsBodyComponent, 
  PhysicsColliderComponent,
  BodyType,
  ColliderType,
  PhysicsMaterialFactory
} from '../../src/physics';

/**
 * 基础物理示例
 */
export class BasicPhysicsExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  
  /** 地面实体 */
  private ground: Entity;
  
  /** 盒子实体列表 */
  private boxes: Entity[] = [];
  
  /** 球体实体列表 */
  private spheres: Entity[] = [];
  
  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建基础物理示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建场景
    this.scene = new Scene('物理示例场景');
    
    // 创建物理系统
    this.physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.82, z: 0 },
      debug: true
    });
    
    // 添加物理系统到引擎
    this.engine.addSystem(this.physicsSystem);
    
    // 初始化物理材质工厂
    PhysicsMaterialFactory.initialize();
    
    // 创建地面
    this.ground = this.createGround();
    
    // 添加地面到场景
    this.scene.addEntity(this.ground);
  }

  /**
   * 初始化示例
   */
  public initialize(): void {
    if (this.initialized) return;
    
    // 创建一些物理对象
    this.createBoxes();
    this.createSpheres();
    
    // 设置场景为活跃场景
    this.engine.getWorld().setActiveScene(this.scene);
    
    this.initialized = true;
  }

  /**
   * 创建地面
   * @returns 地面实体
   */
  private createGround(): Entity {
    // 创建地面实体
    const ground = new Entity('ground');
    
    // 添加变换组件
    const transform = ground.getTransform();
    transform.setPosition(0, -0.5, 0);
    transform.setScale(10, 1, 10);
    
    // 创建地面网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ color: 0x808080 });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    ground.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    ground.addComponent(new PhysicsBodyComponent({
      type: BodyType.STATIC,
      mass: 0
    }));
    
    // 添加碰撞器组件
    ground.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: 5, y: 0.5, z: 5 }
      }
    }));
    
    return ground;
  }

  /**
   * 创建盒子
   */
  private createBoxes(): void {
    // 创建多个盒子
    for (let i = 0; i < 10; i++) {
      const box = this.createBox(
        Math.random() * 4 - 2,
        3 + i * 0.5,
        Math.random() * 4 - 2,
        Math.random() * 0.5 + 0.2
      );
      
      this.boxes.push(box);
      this.scene.addEntity(box);
    }
  }

  /**
   * 创建盒子
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   * @param size 盒子大小
   * @returns 盒子实体
   */
  private createBox(x: number, y: number, z: number, size: number): Entity {
    // 创建盒子实体
    const box = new Entity(`box_${this.boxes.length}`);
    
    // 添加变换组件
    const transform = box.getTransform();
    transform.setPosition(x, y, z);
    transform.setRotation(
      Math.random() * Math.PI,
      Math.random() * Math.PI,
      Math.random() * Math.PI
    );
    transform.setScale(size, size, size);
    
    // 创建盒子网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ 
      color: Math.random() * 0xffffff 
    });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    box.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    box.addComponent(new PhysicsBodyComponent({
      type: BodyType.DYNAMIC,
      mass: size * 2,
      material: PhysicsMaterialFactory.getMaterial('wood')
    }));
    
    // 添加碰撞器组件
    box.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: size / 2, y: size / 2, z: size / 2 }
      }
    }));
    
    return box;
  }

  /**
   * 创建球体
   */
  private createSpheres(): void {
    // 创建多个球体
    for (let i = 0; i < 10; i++) {
      const sphere = this.createSphere(
        Math.random() * 4 - 2,
        5 + i * 0.5,
        Math.random() * 4 - 2,
        Math.random() * 0.3 + 0.2
      );
      
      this.spheres.push(sphere);
      this.scene.addEntity(sphere);
    }
  }

  /**
   * 创建球体
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   * @param radius 球体半径
   * @returns 球体实体
   */
  private createSphere(x: number, y: number, z: number, radius: number): Entity {
    // 创建球体实体
    const sphere = new Entity(`sphere_${this.spheres.length}`);
    
    // 添加变换组件
    const transform = sphere.getTransform();
    transform.setPosition(x, y, z);
    transform.setScale(radius * 2, radius * 2, radius * 2);
    
    // 创建球体网格
    const geometry = new THREE.SphereGeometry(0.5, 16, 16);
    const material = new THREE.MeshStandardMaterial({ 
      color: Math.random() * 0xffffff 
    });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    sphere.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    sphere.addComponent(new PhysicsBodyComponent({
      type: BodyType.DYNAMIC,
      mass: radius * 4,
      material: PhysicsMaterialFactory.getMaterial('rubber')
    }));
    
    // 添加碰撞器组件
    sphere.addComponent(new PhysicsColliderComponent({
      type: ColliderType.SPHERE,
      params: {
        radius: radius
      }
    }));
    
    return sphere;
  }

  /**
   * 添加力到随机物体
   */
  public addRandomForce(): void {
    // 随机选择一个物体
    const entities = [...this.boxes, ...this.spheres];
    const entity = entities[Math.floor(Math.random() * entities.length)];
    
    // 获取物理体组件
    const physicsBody = entity.getComponent(PhysicsBodyComponent);
    if (!physicsBody) return;
    
    // 添加随机力
    const force = new Vector3(
      (Math.random() - 0.5) * 10,
      Math.random() * 15,
      (Math.random() - 0.5) * 10
    );
    
    physicsBody.applyImpulse(force);
  }

  /**
   * 重置场景
   */
  public reset(): void {
    // 移除所有盒子和球体
    for (const box of this.boxes) {
      this.scene.removeEntity(box);
    }
    
    for (const sphere of this.spheres) {
      this.scene.removeEntity(sphere);
    }
    
    this.boxes = [];
    this.spheres = [];
    
    // 重新创建物体
    this.createBoxes();
    this.createSpheres();
  }

  /**
   * 更新示例
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized) return;
    
    // 更新引擎
    this.engine.update(deltaTime);
  }

  /**
   * 获取引擎
   * @returns 引擎实例
   */
  public getEngine(): Engine {
    return this.engine;
  }

  /**
   * 获取场景
   * @returns 场景实例
   */
  public getScene(): Scene {
    return this.scene;
  }

  /**
   * 获取物理系统
   * @returns 物理系统
   */
  public getPhysicsSystem(): PhysicsSystem {
    return this.physicsSystem;
  }

  /**
   * 销毁示例
   */
  public dispose(): void {
    // 销毁引擎
    this.engine.dispose();
  }
}
