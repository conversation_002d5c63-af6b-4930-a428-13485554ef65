/**
 * 材质服务
 */
import axios from 'axios';
import { Material } from '../store/materials/materialsSlice';

const API_URL = '/api/materials';

export const materialService = {
  /**
   * 获取所有材质
   */
  async getMaterials() {
    const response = await axios.get(API_URL);
    return response.data;
  },

  /**
   * 获取单个材质
   */
  async getMaterial(materialId: string) {
    const response = await axios.get(`${API_URL}/${materialId}`);
    return response.data;
  },

  /**
   * 创建材质
   */
  async createMaterial(materialData: Omit<Material, 'id' | 'createdAt' | 'updatedAt'>) {
    const response = await axios.post(API_URL, materialData);
    return response.data;
  },

  /**
   * 更新材质
   */
  async updateMaterial(materialId: string, materialData: Partial<Material>) {
    const response = await axios.patch(`${API_URL}/${materialId}`, materialData);
    return response.data;
  },

  /**
   * 删除材质
   */
  async deleteMaterial(materialId: string) {
    await axios.delete(`${API_URL}/${materialId}`);
  },

  /**
   * 导出材质
   */
  async exportMaterial(materialId: string) {
    const response = await axios.get(`${API_URL}/${materialId}/export`);
    return response.data;
  },

  /**
   * 导入材质
   */
  async importMaterial(materialData: any) {
    const response = await axios.post(`${API_URL}/import`, materialData);
    return response.data;
  },

  /**
   * 复制材质
   */
  async duplicateMaterial(materialId: string, newName?: string) {
    const response = await axios.post(`${API_URL}/${materialId}/duplicate`, { name: newName });
    return response.data;
  },
};
