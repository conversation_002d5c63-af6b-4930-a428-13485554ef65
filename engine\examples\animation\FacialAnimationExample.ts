/**
 * 面部动画示例
 * 演示如何使用面部动画系统和口型同步
 */
import * as THREE from 'three';
import { Engine } from '../../core/Engine';
import { World } from '../../core/World';
import { Entity } from '../../core/Entity';
import { FacialAnimationSystem, FacialAnimationComponent, FacialExpressionType, VisemeType } from '../../animation/FacialAnimation';
import { LipSyncSystem, LipSyncComponent } from '../../animation/LipSync';
import { FacialAnimationEditorSystem } from '../../animation/FacialAnimationEditorSystem';
import { AIAnimationSynthesisSystem } from '../../animation/AIAnimationSynthesisSystem';
import { GLTFLoader } from '../../loaders/GLTFLoader';
import { OrbitControls } from '../../utils/OrbitControls';

/**
 * 面部动画示例
 */
export class FacialAnimationExample {
  /** 引擎 */
  private engine: Engine;
  /** 世界 */
  private world: World;
  /** 场景 */
  private scene: THREE.Scene;
  /** 相机 */
  private camera: THREE.PerspectiveCamera;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer;
  /** 控制器 */
  private controls: OrbitControls;
  /** 角色实体 */
  private characterEntity: Entity;
  /** 面部动画系统 */
  private facialAnimationSystem: FacialAnimationSystem;
  /** 口型同步系统 */
  private lipSyncSystem: LipSyncSystem;
  /** 面部动画编辑器系统 */
  private facialAnimationEditorSystem: FacialAnimationEditorSystem;
  /** AI动画合成系统 */
  private aiAnimationSynthesisSystem: AIAnimationSynthesisSystem;
  /** 音频元素 */
  private audioElement: HTMLAudioElement | null = null;
  /** 是否运行中 */
  private running: boolean = false;

  /**
   * 构造函数
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 获取世界
    this.world = this.engine.getWorld();
    
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x333333);
    
    // 创建相机
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.camera.position.set(0, 1.6, 2);
    
    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    document.body.appendChild(this.renderer.domElement);
    
    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 1.6, 0);
    this.controls.update();
    
    // 创建角色实体
    this.characterEntity = this.world.createEntity();
    
    // 创建面部动画系统
    this.facialAnimationSystem = new FacialAnimationSystem(this.world, {
      debug: true,
      autoDetectAudio: true
    });
    
    // 创建口型同步系统
    this.lipSyncSystem = new LipSyncSystem(this.world, {
      debug: true,
      fftSize: 1024,
      volumeThreshold: 0.01
    });
    
    // 创建面部动画编辑器系统
    this.facialAnimationEditorSystem = new FacialAnimationEditorSystem(this.world, {
      debug: true
    });
    
    // 创建AI动画合成系统
    this.aiAnimationSynthesisSystem = new AIAnimationSynthesisSystem(this.world, {
      debug: true,
      useLocalModel: true
    });
    
    // 添加系统到世界
    this.world.addSystem(this.facialAnimationSystem);
    this.world.addSystem(this.lipSyncSystem);
    this.world.addSystem(this.facialAnimationEditorSystem);
    this.world.addSystem(this.aiAnimationSynthesisSystem);
    
    // 设置窗口大小变化监听
    window.addEventListener('resize', this.onWindowResize.bind(this));
  }

  /**
   * 窗口大小变化处理
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(ambientLight);
    
    // 添加方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 2, 3);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);
    
    // 添加地面
    const ground = new THREE.Mesh(
      new THREE.PlaneGeometry(10, 10),
      new THREE.MeshStandardMaterial({ color: 0x999999 })
    );
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    this.scene.add(ground);
    
    // 添加坐标轴辅助
    const axesHelper = new THREE.AxesHelper(1);
    this.scene.add(axesHelper);
  }

  /**
   * 加载模型
   */
  private loadModel(): void {
    // 创建GLTF加载器
    const loader = new GLTFLoader();
    
    // 加载角色模型
    loader.load('models/character.glb', (gltf) => {
      // 添加模型到场景
      this.scene.add(gltf.scene);
      
      // 设置阴影
      gltf.scene.traverse((object) => {
        if (object instanceof THREE.Mesh) {
          object.castShadow = true;
          object.receiveShadow = true;
        }
      });
      
      // 创建面部动画组件
      const facialAnimation = this.facialAnimationSystem.createFacialAnimation(this.characterEntity);
      
      // 创建口型同步组件
      const lipSync = this.lipSyncSystem.createLipSync(this.characterEntity);
      
      // 创建面部动画编辑器
      const editor = this.facialAnimationEditorSystem.createEditor(this.characterEntity);
      
      // 创建默认表情动画
      this.facialAnimationEditorSystem.createDefaultExpressionAnimation(this.characterEntity, 'happy');
      
      // 创建默认口型动画
      this.facialAnimationEditorSystem.createDefaultVisemeAnimation(this.characterEntity, 'talk');
      
      // 创建组合动画
      this.facialAnimationEditorSystem.createCombinedAnimation(this.characterEntity, 'combined');
      
      console.log('模型加载完成');
    }, undefined, (error) => {
      console.error('加载模型失败:', error);
    });
  }

  /**
   * 加载音频
   */
  private loadAudio(): void {
    // 创建音频元素
    this.audioElement = new Audio('audio/speech.mp3');
    this.audioElement.loop = true;
    
    // 设置音频加载完成回调
    this.audioElement.addEventListener('canplaythrough', () => {
      console.log('音频加载完成');
      
      // 启动口型同步跟踪
      this.lipSyncSystem.startTracking(this.audioElement);
    });
    
    // 加载音频
    this.audioElement.load();
  }

  /**
   * 播放音频
   */
  private playAudio(): void {
    if (this.audioElement) {
      this.audioElement.play();
      console.log('音频播放中');
    }
  }

  /**
   * 暂停音频
   */
  private pauseAudio(): void {
    if (this.audioElement) {
      this.audioElement.pause();
      console.log('音频已暂停');
    }
  }

  /**
   * 测试表情
   */
  private testExpressions(): void {
    // 获取面部动画组件
    const facialAnimation = this.facialAnimationSystem.getFacialAnimation(this.characterEntity);
    if (!facialAnimation) return;
    
    // 定义表情序列
    const expressions = [
      FacialExpressionType.NEUTRAL,
      FacialExpressionType.HAPPY,
      FacialExpressionType.SAD,
      FacialExpressionType.ANGRY,
      FacialExpressionType.SURPRISED
    ];
    
    // 循环播放表情
    let index = 0;
    setInterval(() => {
      facialAnimation.setExpression(expressions[index], 1.0, 0.5);
      console.log(`表情: ${expressions[index]}`);
      
      index = (index + 1) % expressions.length;
    }, 2000);
  }

  /**
   * 测试口型
   */
  private testVisemes(): void {
    // 获取口型同步组件
    const lipSync = this.lipSyncSystem.getLipSync(this.characterEntity);
    if (!lipSync) return;
    
    // 定义口型序列
    const visemes = [
      VisemeType.SILENT,
      VisemeType.AA,
      VisemeType.EE,
      VisemeType.IH,
      VisemeType.OH,
      VisemeType.OU
    ];
    
    // 循环播放口型
    let index = 0;
    setInterval(() => {
      lipSync.setViseme(visemes[index], 1.0, 0.2);
      console.log(`口型: ${visemes[index]}`);
      
      index = (index + 1) % visemes.length;
    }, 1000);
  }

  /**
   * 测试AI动画生成
   */
  private testAIAnimation(): void {
    // 生成面部动画
    const requestId = this.aiAnimationSynthesisSystem.generateFacialAnimation(
      this.characterEntity,
      '开心地说话',
      5.0,
      {
        loop: true,
        style: '卡通',
        intensity: 0.8
      }
    );
    
    if (requestId) {
      console.log(`AI动画生成请求已提交: ${requestId}`);
      
      // 监听生成完成事件
      this.aiAnimationSynthesisSystem.addEventListener('generationComplete', (data) => {
        if (data.result.id === requestId) {
          console.log('AI动画生成完成:', data.result);
        }
      });
    }
  }

  /**
   * 启动示例
   */
  public start(): void {
    // 创建场景
    this.createScene();
    
    // 加载模型
    this.loadModel();
    
    // 加载音频
    this.loadAudio();
    
    // 启动引擎
    this.engine.start();
    
    // 开始动画循环
    this.running = true;
    this.animate();
    
    // 延迟测试表情和口型
    setTimeout(() => {
      this.testExpressions();
      this.testVisemes();
      this.playAudio();
      this.testAIAnimation();
    }, 3000);
    
    console.log('面部动画示例已启动');
  }

  /**
   * 停止示例
   */
  public stop(): void {
    this.running = false;
    this.pauseAudio();
    this.lipSyncSystem.stopTracking();
    this.engine.stop();
    console.log('面部动画示例已停止');
  }

  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;
    
    requestAnimationFrame(this.animate.bind(this));
    
    // 更新控制器
    this.controls.update();
    
    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }
}
