# 软体物理系统

软体物理系统是DL（Digital Learning）引擎中用于模拟软体物理行为的系统，包括布料、绳索、气球、果冻等软体类型。本文档介绍软体物理系统的使用方法和API。

## 目录

- [概述](#概述)
- [系统组件](#系统组件)
- [使用方法](#使用方法)
- [软体类型](#软体类型)
- [高级功能](#高级功能)
- [性能优化](#性能优化)
- [示例](#示例)

## 概述

软体物理系统基于粒子和约束实现，使用Cannon.js物理引擎作为底层物理模拟引擎。系统提供了多种软体类型，包括布料、绳索、体积软体、气球和果冻等，并支持软体与刚体的交互、切割和撕裂等高级功能。

## 系统组件

软体物理系统由以下主要组件组成：

- **SoftBodySystem**：软体物理系统的主类，管理所有软体组件
- **SoftBodyComponent**：软体组件，附加到实体上，定义软体的类型和属性
- **SoftRigidInteraction**：软体与刚体交互系统，处理软体与刚体之间的碰撞和交互
- **SoftBodyCutter**：软体切割系统，提供软体的切割和撕裂功能
- **SpatialPartitioning**：空间分区系统，用于加速碰撞检测
- **SoftBodyLOD**：软体LOD系统，根据距离调整软体的细节层次

## 使用方法

### 初始化软体物理系统

```typescript
// 创建物理系统
const physicsSystem = new PhysicsSystem({
  gravity: { x: 0, y: -9.82, z: 0 }
});

// 创建软体物理系统
const softBodySystem = new SoftBodySystem({
  physicsSystem: physicsSystem,
  debug: true,
  useSpatialPartitioning: true,
  useLOD: true,
  useSoftRigidInteraction: true,
  useSoftBodyCutter: true
});

// 添加系统到引擎
engine.addSystem(physicsSystem);
engine.addSystem(softBodySystem);
```

### 创建软体组件

```typescript
// 创建布料
const clothEntity = new Entity('cloth');
const clothTransform = clothEntity.getTransform();
clothTransform.setPosition(0, 5, 0);

const clothComponent = new SoftBodyComponent({
  type: SoftBodyType.CLOTH,
  mass: 1,
  stiffness: 100,
  damping: 0.1,
  fixedCorners: true,
  params: {
    gridSize: { x: 20, y: 20 },
    width: 10,
    height: 10
  }
});

clothEntity.addComponent(clothComponent);
softBodySystem.addSoftBody(clothComponent);
```

### 软体与刚体交互

```typescript
// 添加刚体到交互系统
const rigidBodyComponent = entity.getComponent(PhysicsBodyComponent);
softBodySystem.addRigidBody(rigidBodyComponent);

// 移除刚体从交互系统
softBodySystem.removeRigidBody(rigidBodyComponent);
```

### 软体切割

```typescript
// 使用平面切割软体
const plane = {
  normal: new THREE.Vector3(0, 1, 0),
  point: new THREE.Vector3(0, 0, 0)
};
softBodySystem.cutSoftBodyWithPlane(entityId, plane);

// 使用射线切割软体
const ray = {
  origin: new THREE.Vector3(0, 5, 0),
  direction: new THREE.Vector3(0, -1, 0),
  length: 10
};
softBodySystem.cutSoftBodyWithRay(entityId, ray);

// 设置撕裂阈值
softBodySystem.setTearingThreshold(1.5);

// 启用/禁用撕裂
softBodySystem.setTearingEnabled(true);
```

## 软体类型

软体物理系统支持以下软体类型：

### 布料 (CLOTH)

布料是一种二维软体，由网格状的粒子和约束组成，可以模拟旗帜、窗帘、衣物等物体。

```typescript
const clothComponent = new SoftBodyComponent({
  type: SoftBodyType.CLOTH,
  mass: 1,
  stiffness: 100,
  damping: 0.1,
  fixedCorners: true,
  params: {
    gridSize: { x: 20, y: 20 },
    width: 10,
    height: 10
  }
});
```

### 绳索 (ROPE)

绳索是一种一维软体，由线性排列的粒子和约束组成，可以模拟绳子、链条等物体。

```typescript
const ropeComponent = new SoftBodyComponent({
  type: SoftBodyType.ROPE,
  mass: 1,
  stiffness: 100,
  damping: 0.1,
  fixedEnds: true,
  params: {
    segments: 20,
    length: 10
  }
});
```

### 体积软体 (VOLUME)

体积软体是一种三维软体，由三维网格状的粒子和约束组成，可以模拟软体积物体。

```typescript
const volumeComponent = new SoftBodyComponent({
  type: SoftBodyType.VOLUME,
  mass: 1,
  stiffness: 300,
  damping: 0.1,
  params: {
    size: 2,
    segments: 5
  }
});
```

### 气球 (BALLOON)

气球是一种特殊的体积软体，具有内部压力，可以模拟气球、皮球等物体。

```typescript
const balloonComponent = new SoftBodyComponent({
  type: SoftBodyType.BALLOON,
  mass: 0.5,
  stiffness: 50,
  damping: 0.1,
  params: {
    radius: 1,
    segments: 16,
    pressure: 10
  }
});
```

### 果冻 (JELLY)

果冻是一种特殊的体积软体，具有高弹性和阻尼，可以模拟果冻、橡胶等物体。

```typescript
const jellyComponent = new SoftBodyComponent({
  type: SoftBodyType.JELLY,
  mass: 1,
  stiffness: 300,
  damping: 0.5,
  params: {
    size: 1,
    segments: 5
  }
});
```

## 高级功能

### 软体与刚体交互

软体物理系统支持软体与刚体之间的双向交互，包括碰撞检测和响应。

### 切割和撕裂

软体物理系统支持软体的切割和撕裂功能，可以使用平面或射线切割软体，也可以根据约束拉伸程度自动撕裂软体。

### 应用外力

可以对软体应用外力或冲量，例如风力、爆炸力等。

```typescript
// 应用外力
const force = new THREE.Vector3(0, 10, 0);
softBodyComponent.applyForce(force);

// 应用冲量
const impulse = new THREE.Vector3(0, 5, 0);
softBodyComponent.applyImpulse(impulse);
```

## 性能优化

### 空间分区

软体物理系统使用空间分区技术加速碰撞检测，减少不必要的碰撞检测计算。

### LOD系统

软体物理系统使用LOD（细节层次）系统根据与相机的距离动态调整软体的细节层次，远处的软体使用更低的分辨率和更少的迭代次数，提高性能。

## 示例

软体物理系统提供了多个示例，展示不同类型的软体和功能：

- **ClothExample**：展示布料物理效果
- **RopeExample**：展示绳索物理效果
- **BalloonExample**：展示气球物理效果
- **CuttingExample**：展示软体切割和撕裂功能

可以在`examples/physics`目录下找到这些示例。
