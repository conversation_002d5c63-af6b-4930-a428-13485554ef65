@import './variables.less';

// 全局样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  font-family: @font-family;
  font-size: @font-size-base;
  line-height: @line-height-base;
  color: @text-color;
  background-color: @bg-color;
}

#root {
  width: 100%;
  height: 100%;
}

// 编辑器布局样式
.editor-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-header {
  height: @editor-header-height;
  background-color: @header-bg;
  color: @text-color-inverse;
  display: flex;
  align-items: center;
  padding: 0 @spacing-md;
  z-index: 1000;
}

.editor-toolbar {
  height: @editor-toolbar-height;
  background-color: @component-bg;
  border-bottom: 1px solid @border-color-split;
  display: flex;
  align-items: center;
  padding: 0 @spacing-md;
  z-index: 900;
}

.editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.editor-sidebar {
  width: @editor-panel-width;
  background-color: @component-bg;
  border-right: 1px solid @border-color-split;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.editor-main {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.editor-panel {
  width: @editor-panel-width;
  background-color: @component-bg;
  border-left: 1px solid @border-color-split;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.editor-statusbar {
  height: @editor-statusbar-height;
  background-color: @component-bg;
  border-top: 1px solid @border-color-split;
  display: flex;
  align-items: center;
  padding: 0 @spacing-md;
  font-size: @font-size-sm;
  color: @text-color-secondary;
}

// 面板样式
.panel-header {
  padding: @spacing-sm @spacing-md;
  border-bottom: 1px solid @border-color-split;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-content {
  flex: 1;
  overflow: auto;
  padding: @spacing-md;
}

// 场景视图样式
.scene-view {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #1e1e1e;
}

.scene-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.scene-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.scene-tools {
  position: absolute;
  top: @spacing-md;
  left: @spacing-md;
  z-index: 100;
  pointer-events: auto;
}

// 工具栏样式
.toolbar-group {
  display: flex;
  align-items: center;
  margin-right: @spacing-md;
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background-color: @border-color-split;
  margin: 0 @spacing-md;
}

// 层级面板样式
.hierarchy-item {
  display: flex;
  align-items: center;
  padding: @spacing-xs @spacing-sm;
  cursor: pointer;
  border-radius: @border-radius-base;
  
  &:hover {
    background-color: @border-color-split;
  }
  
  &.selected {
    background-color: fade(@primary-color, 10%);
  }
}

.hierarchy-item-icon {
  margin-right: @spacing-xs;
}

.hierarchy-item-actions {
  margin-left: auto;
  display: flex;
  align-items: center;
}

// 检查器面板样式
.inspector-section {
  margin-bottom: @spacing-md;
}

.inspector-section-header {
  font-weight: bold;
  margin-bottom: @spacing-xs;
}

.inspector-field {
  display: flex;
  align-items: center;
  margin-bottom: @spacing-xs;
}

.inspector-field-label {
  width: 100px;
  flex-shrink: 0;
}

.inspector-field-value {
  flex: 1;
}

// 资产面板样式
.asset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: @spacing-sm;
  padding: @spacing-sm;
}

.asset-item {
  border-radius: @border-radius-base;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid @border-color-split;
  
  &:hover {
    border-color: @primary-color;
  }
  
  &.selected {
    border-color: @primary-color;
    box-shadow: 0 0 0 2px fade(@primary-color, 20%);
  }
}

.asset-thumbnail {
  width: 100%;
  height: 80px;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
}

.asset-info {
  padding: @spacing-xs;
  font-size: @font-size-sm;
}

.asset-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 暗色主题样式
.dark-theme {
  color: @dark-text-color;
  background-color: @dark-bg-color;
  
  .editor-header {
    background-color: @dark-header-bg;
  }
  
  .editor-toolbar,
  .editor-sidebar,
  .editor-panel,
  .editor-statusbar {
    background-color: @dark-component-bg;
    border-color: @dark-border-color-split;
  }
  
  .toolbar-divider {
    background-color: @dark-border-color-split;
  }
  
  .hierarchy-item {
    &:hover {
      background-color: @dark-border-color-split;
    }
    
    &.selected {
      background-color: fade(@primary-color, 20%);
    }
  }
  
  .asset-item {
    border-color: @dark-border-color-split;
  }
  
  .asset-thumbnail {
    background-color: #2a2a2a;
  }
}
