# 混合模式文档

## 概述

混合模式用于控制多个动画之间的混合方式。IR-Engine 提供了多种混合模式，可以创建复杂的动画效果。

## 基本混合模式

### OVERRIDE（覆盖）

完全替换之前的动画。

```typescript
const layer: BlendLayer = {
  clipName: 'walk',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.OVERRIDE
};
blender.addLayer(layer);
```

### ADDITIVE（叠加）

在之前的动画基础上叠加。适用于添加额外的动作，如在行走的同时挥手。

```typescript
const baseLayer: BlendLayer = {
  clipName: 'walk',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.OVERRIDE
};

const additiveLayer: BlendLayer = {
  clipName: 'wave',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.ADDITIVE
};

blender.addLayer(baseLayer);
blender.addLayer(additiveLayer);
```

### MULTIPLY（乘法）

将当前动画与之前的动画相乘。适用于修改现有动画的幅度。

```typescript
const layer: BlendLayer = {
  clipName: 'amplify',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.MULTIPLY
};
blender.addLayer(layer);
```

### DIFFERENCE（差值）

计算当前动画与之前动画的差值。适用于创建相对运动。

```typescript
const layer: BlendLayer = {
  clipName: 'delta',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.DIFFERENCE
};
blender.addLayer(layer);
```

### AVERAGE（平均）

计算当前动画与之前动画的平均值。适用于平滑过渡。

```typescript
const layer: BlendLayer = {
  clipName: 'smooth',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.AVERAGE
};
blender.addLayer(layer);
```

### MAX（最大值）

取当前动画与之前动画中的最大值。适用于强调最大运动。

```typescript
const layer: BlendLayer = {
  clipName: 'emphasize',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.MAX
};
blender.addLayer(layer);
```

### MIN（最小值）

取当前动画与之前动画中的最小值。适用于限制运动范围。

```typescript
const layer: BlendLayer = {
  clipName: 'limit',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.MIN
};
blender.addLayer(layer);
```

## 高级混合模式

### CROSS_FADE（交叉淡入淡出）

在两个动画之间平滑过渡。

```typescript
const fromLayer: BlendLayer = {
  clipName: 'walk',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.OVERRIDE
};

const toLayer: BlendLayer = {
  clipName: 'run',
  weight: 0.5, // 过渡进度
  timeScale: 1.0,
  blendMode: BlendMode.CROSS_FADE,
  crossFadeTime: 1.0 // 过渡时间（秒）
};

blender.addLayer(fromLayer);
blender.addLayer(toLayer);
```

### BLEND_TREE（混合树）

基于参数混合多个动画。适用于创建参数化的动画，如基于速度在行走和跑步之间混合。

```typescript
const blendTreeLayer: BlendLayer = {
  clipName: 'locomotion',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.BLEND_TREE,
  blendTreeParams: {
    paramName: 'speed',
    paramValue: 0.5, // 0-1 之间的值
    blendType: '1D', // '1D', '2D', 'Direct'
    nodes: [
      {
        clipName: 'idle',
        threshold: 0,
        weight: 0.5
      },
      {
        clipName: 'walk',
        threshold: 0.5,
        weight: 0.5
      },
      {
        clipName: 'run',
        threshold: 1,
        weight: 0
      }
    ]
  }
};
blender.addLayer(blendTreeLayer);
```

#### 1D 混合树

基于一个参数在多个动画之间混合。

```typescript
const blendTreeLayer: BlendLayer = {
  // ... 其他属性
  blendTreeParams: {
    paramName: 'speed',
    paramValue: 0.5,
    blendType: '1D',
    nodes: [
      { clipName: 'idle', threshold: 0, weight: 0 },
      { clipName: 'walk', threshold: 0.5, weight: 0 },
      { clipName: 'run', threshold: 1, weight: 0 }
    ]
  }
};
```

#### 2D 混合树

基于两个参数在多个动画之间混合。适用于创建方向性动画，如八向移动。

```typescript
const blendTreeLayer: BlendLayer = {
  // ... 其他属性
  blendTreeParams: {
    paramName: 'direction',
    paramValue: { x: 0, y: 0 }, // 二维参数
    blendType: '2D',
    nodes: [
      { clipName: 'walkForward', threshold: { x: 0, y: 1 }, weight: 0 },
      { clipName: 'walkBackward', threshold: { x: 0, y: -1 }, weight: 0 },
      { clipName: 'walkLeft', threshold: { x: -1, y: 0 }, weight: 0 },
      { clipName: 'walkRight', threshold: { x: 1, y: 0 }, weight: 0 }
    ]
  }
};
```

### WEIGHTED（加权）

根据自定义权重混合多个动画。适用于创建复杂的混合效果。

```typescript
const weightedLayer: BlendLayer = {
  clipName: 'composite',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.WEIGHTED,
  weightParams: {
    weights: {
      'idle': 0.2,
      'walk': 0.5,
      'run': 0.3
    },
    normalize: true // 是否归一化权重
  }
};
blender.addLayer(weightedLayer);
```

### LAYERED（分层）

按层次混合动画。适用于创建复杂的分层动画，如上半身和下半身分别执行不同的动画。

```typescript
const layeredLayer: BlendLayer = {
  clipName: 'composite',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.LAYERED,
  layerParams: {
    layers: [
      {
        clipName: 'walk',
        layerIndex: 0, // 层次索引，越小越底层
        weight: 1.0,
        mask: ['leftLeg', 'rightLeg'] // 遮罩
      },
      {
        clipName: 'wave',
        layerIndex: 1,
        weight: 1.0,
        mask: ['leftArm', 'rightArm'] // 遮罩
      }
    ]
  }
};
blender.addLayer(layeredLayer);
```

### SEQUENTIAL（序列）

按顺序播放多个动画。适用于创建动画序列，如行走、跳跃、行走。

```typescript
const sequentialLayer: BlendLayer = {
  clipName: 'sequence',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.SEQUENTIAL,
  sequenceParams: {
    sequence: [
      {
        clipName: 'idle',
        duration: 2.0, // 持续时间（秒）
        transitionTime: 0.5 // 过渡时间（秒）
      },
      {
        clipName: 'walk',
        duration: 3.0,
        transitionTime: 0.5
      },
      {
        clipName: 'run',
        duration: 2.0,
        transitionTime: 0.5
      },
      {
        clipName: 'jump',
        duration: 1.0,
        transitionTime: 0.5
      }
    ],
    currentIndex: 0, // 当前索引
    loop: true // 是否循环
  }
};
blender.addLayer(sequentialLayer);
```

## 混合层

混合层用于组织和控制动画混合。每个混合层包含一个动画片段和混合参数。

```typescript
interface BlendLayer {
  /** 动画片段名称 */
  clipName: string;
  /** 权重 */
  weight: number;
  /** 时间缩放 */
  timeScale: number;
  /** 混合模式 */
  blendMode: BlendMode;
  /** 遮罩 */
  mask?: string[];
  /** 交叉淡入淡出时间（秒） */
  crossFadeTime?: number;
  /** 混合树参数 */
  blendTreeParams?: BlendTreeParams;
  /** 权重参数 */
  weightParams?: WeightedBlendParams;
  /** 层次参数 */
  layerParams?: LayeredBlendParams;
  /** 序列参数 */
  sequenceParams?: SequentialBlendParams;
}
```

## 示例

### 行走和挥手

```typescript
// 添加行走动画层
const walkLayer: BlendLayer = {
  clipName: 'walk',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.OVERRIDE
};
blender.addLayer(walkLayer);

// 添加挥手动画层
const waveLayer: BlendLayer = {
  clipName: 'wave',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.ADDITIVE
};
blender.addLayer(waveLayer);
```

### 基于速度的混合树

```typescript
// 添加混合树层
const blendTreeLayer: BlendLayer = {
  clipName: 'locomotion',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.BLEND_TREE,
  blendTreeParams: {
    paramName: 'speed',
    paramValue: 0.5,
    blendType: '1D',
    nodes: [
      {
        clipName: 'idle',
        threshold: 0,
        weight: 0.5
      },
      {
        clipName: 'walk',
        threshold: 0.5,
        weight: 0.5
      },
      {
        clipName: 'run',
        threshold: 1,
        weight: 0
      }
    ]
  }
};
blender.addLayer(blendTreeLayer);

// 更新参数值
blendTreeLayer.blendTreeParams!.paramValue = 0.7;
blender.update(0);
```

### 上半身和下半身分离

```typescript
// 添加下半身动画层
const lowerLayer: BlendLayer = {
  clipName: 'walk',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.OVERRIDE,
  mask: ['leftLeg', 'rightLeg']
};
blender.addLayer(lowerLayer);

// 添加上半身动画层
const upperLayer: BlendLayer = {
  clipName: 'wave',
  weight: 1.0,
  timeScale: 1.0,
  blendMode: BlendMode.OVERRIDE,
  mask: ['spine', 'leftArm', 'rightArm', 'head']
};
blender.addLayer(upperLayer);
```

## 最佳实践

1. 从简单的混合模式开始，如 OVERRIDE 和 ADDITIVE。
2. 使用 CROSS_FADE 创建平滑的过渡。
3. 使用 BLEND_TREE 创建参数化的动画。
4. 使用 LAYERED 分离身体不同部位的动画。
5. 使用 SEQUENTIAL 创建动画序列。
6. 合理设置权重和时间缩放，以获得最佳效果。
7. 使用遮罩限制动画影响的骨骼。
8. 测试不同的混合模式组合，以获得最佳效果。
