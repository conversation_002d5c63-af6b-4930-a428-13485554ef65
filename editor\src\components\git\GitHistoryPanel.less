.git-history-panel {
  padding: 0 8px;
}

.git-history-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.git-history-list {
  max-height: 400px;
  overflow-y: auto;
}

.git-commit-item {
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.git-commit-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.git-commit-header {
  display: flex;
  flex-direction: column;
}

.git-commit-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.git-commit-message {
  word-break: break-word;
}

.git-commit-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
}

.git-commit-author, .git-commit-date {
  display: flex;
  align-items: center;
}

.git-commit-author .anticon, .git-commit-date .anticon {
  margin-right: 4px;
}

.git-commit-actions {
  margin-top: 8px;
  display: flex;
  justify-content: flex-end;
}

.git-commit-detail {
  margin-top: 16px;
}

.git-commit-detail-item {
  margin-bottom: 12px;
}

.git-commit-detail-label {
  font-weight: 500;
  margin-bottom: 4px;
}

.git-commit-detail-value {
  background-color: rgba(0, 0, 0, 0.02);
  padding: 8px;
  border-radius: 4px;
  word-break: break-all;
}

.git-loading {
  padding: 16px;
  text-align: center;
  color: rgba(0, 0, 0, 0.45);
}
