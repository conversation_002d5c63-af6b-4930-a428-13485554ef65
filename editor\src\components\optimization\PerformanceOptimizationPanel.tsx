/**
 * 性能优化面板组件
 * 集成所有性能优化工具
 */
import React, { useState } from 'react';
import { Tabs, Card, Button, Space, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  ThunderboltOutlined,
  Bar<PERSON><PERSON>Outlined,
  SettingOutlined,
  LineChartOutlined,
  EyeOutlined,
  CloudOutlined,
  PartitionOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import DynamicLODPanel from './DynamicLODPanel';
import OcclusionCullingPanel from './OcclusionCullingPanel';
import InstancedRenderingPanel from './InstancedRenderingPanel';
import ChunkedSceneLoadingPanel from './ChunkedSceneLoadingPanel';
import { PerformanceMonitor } from '../../../engine/src/utils/PerformanceMonitor';
import { EngineService } from '../../services/EngineService';
import { SceneService } from '../../services/SceneService';
import './PerformanceOptimizationPanel.less';

const { TabPane } = Tabs;

/**
 * 性能优化面板组件
 */
const PerformanceOptimizationPanel: React.FC = () => {
  const { t } = useTranslation();
  
  // 状态
  const [activeTab, setActiveTab] = useState('lod');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  
  // 分析场景性能
  const analyzeScenePerformance = async () => {
    setIsAnalyzing(true);
    
    try {
      // 获取引擎
      const engine = EngineService.getInstance().getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }
      
      // 获取性能监控器
      const performanceMonitor = PerformanceMonitor.getInstance();
      
      // 开始性能分析
      performanceMonitor.startAnalysis();
      
      // 等待一段时间
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 结束性能分析
      const analysisResult = performanceMonitor.endAnalysis();
      
      // 根据分析结果切换到相应的标签页
      if (analysisResult.bottlenecks.includes('rendering')) {
        if (analysisResult.details.rendering.includes('drawcalls')) {
          setActiveTab('instancing');
        } else if (analysisResult.details.rendering.includes('visibility')) {
          setActiveTab('occlusion');
        } else {
          setActiveTab('lod');
        }
      } else if (analysisResult.bottlenecks.includes('memory')) {
        setActiveTab('chunking');
      }
    } catch (error) {
      console.error('分析场景性能失败:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };
  
  // 应用自动优化
  const applyAutoOptimization = async () => {
    try {
      // 获取引擎
      const engine = EngineService.getInstance().getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }
      
      // 获取性能优化服务
      const optimizationService = (engine as any).getOptimizationService?.();
      if (!optimizationService) {
        throw new Error('性能优化服务未初始化');
      }
      
      // 应用自动优化
      await optimizationService.applyAutoOptimization();
    } catch (error) {
      console.error('应用自动优化失败:', error);
    }
  };
  
  return (
    <div className="performance-optimization-panel">
      <Card 
        title={
          <Space>
            <ThunderboltOutlined />
            <span>{t('optimization.title')}</span>
          </Space>
        }
        extra={
          <Space>
            <Tooltip title={t('optimization.analyzePerformance')}>
              <Button 
                icon={<BarChartOutlined />} 
                loading={isAnalyzing}
                onClick={analyzeScenePerformance}
              >
                {t('optimization.analyze')}
              </Button>
            </Tooltip>
            <Tooltip title={t('optimization.applyAutoOptimization')}>
              <Button 
                type="primary" 
                icon={<ThunderboltOutlined />}
                onClick={applyAutoOptimization}
              >
                {t('optimization.autoOptimize')}
              </Button>
            </Tooltip>
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <span>
                <SettingOutlined />
                {t('optimization.dynamicLOD.tabTitle')}
              </span>
            } 
            key="lod"
          >
            <DynamicLODPanel />
          </TabPane>
          <TabPane 
            tab={
              <span>
                <EyeOutlined />
                {t('optimization.occlusionCulling.tabTitle')}
              </span>
            } 
            key="occlusion"
          >
            <OcclusionCullingPanel />
          </TabPane>
          <TabPane 
            tab={
              <span>
                <AppstoreOutlined />
                {t('optimization.instancedRendering.tabTitle')}
              </span>
            } 
            key="instancing"
          >
            <InstancedRenderingPanel />
          </TabPane>
          <TabPane 
            tab={
              <span>
                <PartitionOutlined />
                {t('optimization.chunkedSceneLoading.tabTitle')}
              </span>
            } 
            key="chunking"
          >
            <ChunkedSceneLoadingPanel />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default PerformanceOptimizationPanel;
