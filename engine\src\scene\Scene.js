"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Scene = void 0;
/**
 * 场景类
 * 表示3D场景，包含实体、光照和环境
 */
var THREE = require("three");
var EventEmitter_1 = require("../utils/EventEmitter");
var SceneGraph_1 = require("./SceneGraph");
var SceneLayerManager_1 = require("./SceneLayerManager");
var Scene = /** @class */ (function (_super) {
    __extends(Scene, _super);
    /**
     * 创建场景实例
     * @param name 场景名称
     */
    function Scene(name) {
        if (name === void 0) { name = '场景'; }
        var _this = _super.call(this) || this;
        /** 场景ID */
        _this.id = '';
        /** 实体列表 */
        _this.entities = [];
        /** 天空盒 */
        _this.skybox = null;
        /** 是否启用雾效 */
        _this.fogEnabled = false;
        _this.name = name;
        // 创建Three.js场景
        _this.threeScene = new THREE.Scene();
        // 创建环境光
        _this.ambientLight = new THREE.AmbientLight(0x404040, 1);
        _this.threeScene.add(_this.ambientLight);
        // 创建场景图
        _this.sceneGraph = new SceneGraph_1.SceneGraph(_this);
        // 创建场景图层管理器
        _this.layerManager = new SceneLayerManager_1.SceneLayerManager(_this, {
            createDefaultLayers: true,
            defaultLayerCount: 3
        });
        return _this;
    }
    /**
     * 更新场景
     * @param deltaTime 帧间隔时间（秒）
     */
    Scene.prototype.update = function (deltaTime) {
        // 更新天空盒
        if (this.skybox) {
            this.skybox.update(deltaTime);
        }
    };
    /**
     * 固定时间步长更新
     * @param fixedDeltaTime 固定帧间隔时间（秒）
     */
    Scene.prototype.fixedUpdate = function (fixedDeltaTime) {
        // 目前没有需要固定更新的内容
    };
    /**
     * 添加实体
     * @param entity 实体实例
     * @returns 添加的实体
     */
    Scene.prototype.addEntity = function (entity) {
        // 如果已经在场景中，则不做任何操作
        if (this.entities.includes(entity)) {
            return entity;
        }
        // 添加到实体列表
        this.entities.push(entity);
        // 获取实体的变换组件
        var transform = entity.getTransform();
        if (transform) {
            // 添加到Three.js场景
            var object3D = transform.getObject3D();
            this.threeScene.add(object3D);
        }
        // 发出实体添加事件
        this.emit('entityAdded', entity);
        return entity;
    };
    /**
     * 移除实体
     * @param entity 实体实例
     * @returns 是否成功移除
     */
    Scene.prototype.removeEntity = function (entity) {
        var index = this.entities.indexOf(entity);
        if (index === -1) {
            return false;
        }
        // 从实体列表中移除
        this.entities.splice(index, 1);
        // 获取实体的变换组件
        var transform = entity.getTransform();
        if (transform) {
            // 从Three.js场景中移除
            var object3D = transform.getObject3D();
            this.threeScene.remove(object3D);
        }
        // 发出实体移除事件
        this.emit('entityRemoved', entity);
        return true;
    };
    /**
     * 获取所有实体
     * @returns 实体数组
     */
    Scene.prototype.getEntities = function () {
        return __spreadArray([], this.entities, true);
    };
    /**
     * 根据名称查找实体
     * @param name 实体名称
     * @returns 匹配的实体数组
     */
    Scene.prototype.findEntitiesByName = function (name) {
        return this.entities.filter(function (entity) { return entity.name === name; });
    };
    /**
     * 根据标签查找实体
     * @param tag 实体标签
     * @returns 匹配的实体数组
     */
    Scene.prototype.findEntitiesByTag = function (tag) {
        return this.entities.filter(function (entity) { return entity.hasTag(tag); });
    };
    /**
     * 设置天空盒
     * @param skybox 天空盒实例
     */
    Scene.prototype.setSkybox = function (skybox) {
        // 移除旧的天空盒
        if (this.skybox) {
            var skyboxMesh = this.skybox.getMesh();
            if (skyboxMesh) {
                this.threeScene.remove(skyboxMesh);
            }
        }
        this.skybox = skybox;
        // 添加新的天空盒
        if (skybox) {
            var skyboxMesh = skybox.getMesh();
            if (skyboxMesh) {
                this.threeScene.add(skyboxMesh);
            }
        }
    };
    /**
     * 获取天空盒
     * @returns 天空盒实例
     */
    Scene.prototype.getSkybox = function () {
        return this.skybox;
    };
    /**
     * 设置环境光
     * @param color 颜色
     * @param intensity 强度
     */
    Scene.prototype.setAmbientLight = function (color, intensity) {
        this.ambientLight.color.set(color);
        this.ambientLight.intensity = intensity;
    };
    /**
     * 获取环境光
     * @returns 环境光实例
     */
    Scene.prototype.getAmbientLight = function () {
        return this.ambientLight;
    };
    /**
     * 设置雾效
     * @param color 颜色
     * @param near 近距离
     * @param far 远距离
     */
    Scene.prototype.setFog = function (color, near, far) {
        this.threeScene.fog = new THREE.Fog(color, near, far);
        this.fogEnabled = true;
    };
    /**
     * 设置指数雾效
     * @param color 颜色
     * @param density 密度
     */
    Scene.prototype.setExponentialFog = function (color, density) {
        this.threeScene.fog = new THREE.FogExp2(color, density);
        this.fogEnabled = true;
    };
    /**
     * 清除雾效
     */
    Scene.prototype.clearFog = function () {
        this.threeScene.fog = null;
        this.fogEnabled = false;
    };
    /**
     * 是否启用雾效
     * @returns 是否启用雾效
     */
    Scene.prototype.isFogEnabled = function () {
        return this.fogEnabled;
    };
    /**
     * 获取Three.js场景
     * @returns Three.js场景实例
     */
    Scene.prototype.getThreeScene = function () {
        return this.threeScene;
    };
    /**
     * 清空场景
     */
    Scene.prototype.clear = function () {
        // 移除所有实体
        while (this.entities.length > 0) {
            this.removeEntity(this.entities[0]);
        }
        // 移除天空盒
        this.setSkybox(null);
        // 清除雾效
        this.clearFog();
        // 发出清空事件
        this.emit('cleared');
    };
    /**
     * 销毁场景
     */
    Scene.prototype.dispose = function () {
        // 清空场景
        this.clear();
        // 移除环境光
        this.threeScene.remove(this.ambientLight);
        // 销毁场景图
        this.sceneGraph.dispose();
        // 销毁场景图层管理器
        this.layerManager.dispose();
        // 移除所有事件监听器
        this.removeAllListeners();
    };
    /**
     * 获取场景图
     * @returns 场景图实例
     */
    Scene.prototype.getSceneGraph = function () {
        return this.sceneGraph;
    };
    /**
     * 获取场景图层管理器
     * @returns 场景图层管理器实例
     */
    Scene.prototype.getLayerManager = function () {
        return this.layerManager;
    };
    /**
     * 获取根实体
     * @returns 根实体
     */
    Scene.prototype.getRootEntity = function () {
        // 获取没有父实体的实体
        for (var _i = 0, _a = this.entities; _i < _a.length; _i++) {
            var entity = _a[_i];
            if (!entity.getParent()) {
                return entity;
            }
        }
        return null;
    };
    /**
     * 查找实体
     * @param predicate 查询函数
     * @returns 匹配的实体数组
     */
    Scene.prototype.findEntities = function (predicate) {
        return this.entities.filter(predicate);
    };
    /**
     * 查找第一个实体
     * @param predicate 查询函数
     * @returns 匹配的实体，如果不存在则返回null
     */
    Scene.prototype.findEntity = function (predicate) {
        return this.entities.find(predicate) || null;
    };
    /**
     * 根据ID查找实体
     * @param id 实体ID
     * @returns 实体实例，如果不存在则返回null
     */
    Scene.prototype.findEntityById = function (id) {
        return this.entities.find(function (entity) { return entity.id === id; }) || null;
    };
    /**
     * 是否可见
     * @returns 是否可见
     */
    Scene.prototype.isVisible = function () {
        return true;
    };
    /**
     * 是否锁定
     * @returns 是否锁定
     */
    Scene.prototype.isLocked = function () {
        return false;
    };
    /**
     * 获取场景ID
     * @returns 场景ID
     */
    Scene.prototype.getId = function () {
        return this.id;
    };
    /**
     * 设置场景ID
     * @param id 场景ID
     */
    Scene.prototype.setId = function (id) {
        this.id = id;
    };
    /**
     * 获取场景名称
     * @returns 场景名称
     */
    Scene.prototype.getName = function () {
        return this.name;
    };
    /**
     * 设置场景名称
     * @param name 场景名称
     */
    Scene.prototype.setName = function (name) {
        this.name = name;
    };
    /**
     * 获取活跃相机
     * @returns 活跃相机
     */
    Scene.prototype.getActiveCamera = function () {
        // 查找场景中的相机实体
        for (var _i = 0, _a = this.entities; _i < _a.length; _i++) {
            var entity = _a[_i];
            var camera = entity.getComponent('Camera');
            if (camera) {
                return camera;
            }
        }
        return null;
    };
    return Scene;
}(EventEmitter_1.EventEmitter));
exports.Scene = Scene;
