.instanced-rendering-panel {
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    
    .loading-spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top: 4px solid #1890ff;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }
    
    .loading-text {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.65);
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  }
  
  .setting-item {
    margin-bottom: 16px;
    
    .setting-label {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 8px;
      display: flex;
      align-items: center;
    }
    
    .setting-control {
      display: flex;
      align-items: center;
    }
  }
  
  .ant-collapse-content-box {
    padding: 16px !important;
  }
  
  .ant-statistic {
    .ant-statistic-title {
      font-size: 14px;
    }
    
    .ant-statistic-content {
      font-size: 20px;
    }
  }
  
  .ant-table-small {
    .ant-table-thead > tr > th {
      background-color: #f5f5f5;
      padding: 8px 16px;
    }
    
    .ant-table-tbody > tr > td {
      padding: 8px 16px;
    }
  }
}
