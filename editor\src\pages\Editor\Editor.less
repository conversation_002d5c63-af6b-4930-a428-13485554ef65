/**
 * 编辑器样式
 */
.editor-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  align-items: center;
  padding: 0 16px;
  height: 48px;
  line-height: 48px;
  background: #001529;
  color: #fff;

  .logo {
    width: 120px;
    font-size: 18px;
    font-weight: bold;
    margin-right: 24px;
  }

  .ant-menu {
    flex: 1;
    background: transparent;
    border-bottom: none;
  }

  .header-actions {
    margin-left: 16px;
  }
}

.editor-sider {
  background: #f0f2f5;
  border-right: 1px solid #e8e8e8;

  .sider-header {
    padding: 8px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;

    .panel-tabs {
      flex: 1;
      display: flex;
      justify-content: center;
      gap: 8px;
    }
  }

  .panel-content {
    height: calc(100% - 48px);
    overflow-y: auto;
    padding: 8px;
  }
}

.editor-content {
  position: relative;
  background: #1e1e1e;
  overflow: hidden;
}

.properties-sider {
  border-left: 1px solid #e8e8e8;
  background: #f0f2f5;
  padding: 16px;
  overflow-y: auto;
}
