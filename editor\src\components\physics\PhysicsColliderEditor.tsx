/**
 * 物理碰撞器编辑器组件
 * 用于编辑物理碰撞器属性
 */
import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Switch, InputNumber, Button, Collapse, Tooltip, Space, Divider } from 'antd';
import { InfoCircleOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { updateEntity } from '../../store/entities/entitiesSlice';
import { PhysicsCollider } from '../../../../engine/src/physics/PhysicsCollider';
import { Vector3Input } from '../common/Vector3Input';

const { Option } = Select;
const { Panel } = Collapse;

interface PhysicsColliderEditorProps {
  entityId: string;
}

/**
 * 物理碰撞器编辑器组件
 */
const PhysicsColliderEditor: React.FC<PhysicsColliderEditorProps> = ({ entityId }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // 获取实体数据
  const entity = useSelector((state: RootState) => 
    state.entities.entities.find(entity => entity.id === entityId)
  );
  
  // 获取物理碰撞器组件数据
  const physicsColliderComponent = entity?.components?.physicsCollider;
  
  // 表单状态
  const [form] = Form.useForm();
  
  // 碰撞器类型
  const [colliderType, setColliderType] = useState(physicsColliderComponent?.type || 'box');
  
  // 初始化表单
  useEffect(() => {
    if (physicsColliderComponent) {
      setColliderType(physicsColliderComponent.type || 'box');
      
      form.setFieldsValue({
        type: physicsColliderComponent.type || 'box',
        isTrigger: physicsColliderComponent.isTrigger || false,
        offset: physicsColliderComponent.offset || { x: 0, y: 0, z: 0 },
        rotation: physicsColliderComponent.rotation || { x: 0, y: 0, z: 0 },
        // 盒体参数
        boxHalfExtents: physicsColliderComponent.params?.halfExtents || { x: 0.5, y: 0.5, z: 0.5 },
        // 球体参数
        sphereRadius: physicsColliderComponent.params?.radius || 0.5,
        // 胶囊体参数
        capsuleRadius: physicsColliderComponent.params?.radius || 0.5,
        capsuleHeight: physicsColliderComponent.params?.height || 1,
        // 圆柱体参数
        cylinderRadius: physicsColliderComponent.params?.radius || 0.5,
        cylinderHeight: physicsColliderComponent.params?.height || 1,
        // 平面参数
        planeNormal: physicsColliderComponent.params?.normal || { x: 0, y: 1, z: 0 },
        // 碰撞过滤
        collisionGroup: physicsColliderComponent.collisionGroup || 1,
        collisionMask: physicsColliderComponent.collisionMask || -1
      });
    }
  }, [physicsColliderComponent, form]);
  
  // 处理表单变更
  const handleValuesChange = (changedValues: any, allValues: any) => {
    if (!entity) return;
    
    // 如果碰撞器类型改变，更新状态
    if (changedValues.type) {
      setColliderType(changedValues.type);
    }
    
    // 构建参数对象
    let params: any = {};
    
    switch (allValues.type) {
      case 'box':
        params = {
          halfExtents: allValues.boxHalfExtents
        };
        break;
      case 'sphere':
        params = {
          radius: allValues.sphereRadius
        };
        break;
      case 'capsule':
        params = {
          radius: allValues.capsuleRadius,
          height: allValues.capsuleHeight
        };
        break;
      case 'cylinder':
        params = {
          radius: allValues.cylinderRadius,
          height: allValues.cylinderHeight
        };
        break;
      case 'plane':
        params = {
          normal: allValues.planeNormal
        };
        break;
    }
    
    // 更新实体的物理碰撞器组件
    dispatch(updateEntity({
      id: entityId,
      changes: {
        components: {
          ...entity.components,
          physicsCollider: {
            type: allValues.type,
            isTrigger: allValues.isTrigger,
            offset: allValues.offset,
            rotation: allValues.rotation,
            params,
            collisionGroup: allValues.collisionGroup,
            collisionMask: allValues.collisionMask
          }
        }
      }
    }));
  };
  
  // 处理移除组件
  const handleRemoveComponent = () => {
    if (!entity) return;
    
    // 创建新的组件对象，不包含物理碰撞器组件
    const { physicsCollider, ...otherComponents } = entity.components || {};
    
    // 更新实体
    dispatch(updateEntity({
      id: entityId,
      changes: {
        components: otherComponents
      }
    }));
  };
  
  // 如果没有实体或物理碰撞器组件，显示空状态
  if (!entity || !physicsColliderComponent) {
    return (
      <div className="component-editor empty-state">
        <p>{t('editor.physics.noPhysicsCollider')}</p>
        <Button 
          type="primary" 
          onClick={() => {
            if (entity) {
              dispatch(updateEntity({
                id: entityId,
                changes: {
                  components: {
                    ...entity.components,
                    physicsCollider: {
                      type: 'box',
                      isTrigger: false,
                      offset: { x: 0, y: 0, z: 0 },
                      rotation: { x: 0, y: 0, z: 0 },
                      params: {
                        halfExtents: { x: 0.5, y: 0.5, z: 0.5 }
                      },
                      collisionGroup: 1,
                      collisionMask: -1
                    }
                  }
                }
              }));
            }
          }}
        >
          {t('editor.physics.addPhysicsCollider')}
        </Button>
      </div>
    );
  }
  
  return (
    <div className="component-editor physics-collider-editor">
      <div className="component-header">
        <h3>{t('editor.physics.physicsCollider')}</h3>
        <Tooltip title={t('editor.common.remove')}>
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={handleRemoveComponent}
          />
        </Tooltip>
      </div>
      
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValuesChange}
      >
        <Form.Item 
          name="type" 
          label={t('editor.physics.colliderType')}
          tooltip={t('editor.physics.colliderTypeTooltip')}
        >
          <Select>
            <Option value="box">{t('editor.physics.colliders.box')}</Option>
            <Option value="sphere">{t('editor.physics.colliders.sphere')}</Option>
            <Option value="capsule">{t('editor.physics.colliders.capsule')}</Option>
            <Option value="cylinder">{t('editor.physics.colliders.cylinder')}</Option>
            <Option value="plane">{t('editor.physics.colliders.plane')}</Option>
          </Select>
        </Form.Item>
        
        <Form.Item 
          name="isTrigger" 
          label={t('editor.physics.isTrigger')}
          tooltip={t('editor.physics.isTriggerTooltip')}
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
        
        <Collapse defaultActiveKey={['transform', 'shape', 'collision']}>
          <Panel header={t('editor.physics.transform')} key="transform">
            <Form.Item 
              name="offset" 
              label={t('editor.physics.offset')}
              tooltip={t('editor.physics.offsetTooltip')}
            >
              <Vector3Input />
            </Form.Item>
            
            <Form.Item 
              name="rotation" 
              label={t('editor.physics.rotation')}
              tooltip={t('editor.physics.rotationTooltip')}
            >
              <Vector3Input />
            </Form.Item>
          </Panel>
          
          <Panel header={t('editor.physics.shapeParameters')} key="shape">
            {colliderType === 'box' && (
              <Form.Item 
                name="boxHalfExtents" 
                label={t('editor.physics.boxHalfExtents')}
                tooltip={t('editor.physics.boxHalfExtentsTooltip')}
              >
                <Vector3Input min={0.01} />
              </Form.Item>
            )}
            
            {colliderType === 'sphere' && (
              <Form.Item 
                name="sphereRadius" 
                label={t('editor.physics.sphereRadius')}
                tooltip={t('editor.physics.sphereRadiusTooltip')}
                rules={[{ type: 'number', min: 0.01 }]}
              >
                <InputNumber min={0.01} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            )}
            
            {colliderType === 'capsule' && (
              <>
                <Form.Item 
                  name="capsuleRadius" 
                  label={t('editor.physics.capsuleRadius')}
                  tooltip={t('editor.physics.capsuleRadiusTooltip')}
                  rules={[{ type: 'number', min: 0.01 }]}
                >
                  <InputNumber min={0.01} step={0.1} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item 
                  name="capsuleHeight" 
                  label={t('editor.physics.capsuleHeight')}
                  tooltip={t('editor.physics.capsuleHeightTooltip')}
                  rules={[{ type: 'number', min: 0.01 }]}
                >
                  <InputNumber min={0.01} step={0.1} style={{ width: '100%' }} />
                </Form.Item>
              </>
            )}
            
            {colliderType === 'cylinder' && (
              <>
                <Form.Item 
                  name="cylinderRadius" 
                  label={t('editor.physics.cylinderRadius')}
                  tooltip={t('editor.physics.cylinderRadiusTooltip')}
                  rules={[{ type: 'number', min: 0.01 }]}
                >
                  <InputNumber min={0.01} step={0.1} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item 
                  name="cylinderHeight" 
                  label={t('editor.physics.cylinderHeight')}
                  tooltip={t('editor.physics.cylinderHeightTooltip')}
                  rules={[{ type: 'number', min: 0.01 }]}
                >
                  <InputNumber min={0.01} step={0.1} style={{ width: '100%' }} />
                </Form.Item>
              </>
            )}
            
            {colliderType === 'plane' && (
              <Form.Item 
                name="planeNormal" 
                label={t('editor.physics.planeNormal')}
                tooltip={t('editor.physics.planeNormalTooltip')}
              >
                <Vector3Input />
              </Form.Item>
            )}
          </Panel>
          
          <Panel header={t('editor.physics.collisionFiltering')} key="collision">
            <Form.Item 
              name="collisionGroup" 
              label={t('editor.physics.collisionGroup')}
              tooltip={t('editor.physics.collisionGroupTooltip')}
              rules={[{ type: 'number', min: 1 }]}
            >
              <InputNumber min={1} step={1} style={{ width: '100%' }} />
            </Form.Item>
            
            <Form.Item 
              name="collisionMask" 
              label={t('editor.physics.collisionMask')}
              tooltip={t('editor.physics.collisionMaskTooltip')}
            >
              <InputNumber step={1} style={{ width: '100%' }} />
            </Form.Item>
          </Panel>
        </Collapse>
      </Form>
    </div>
  );
};

export default PhysicsColliderEditor;
