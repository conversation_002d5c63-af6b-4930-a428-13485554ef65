"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Renderer = void 0;
/**
 * 渲染器类
 * 负责渲染场景
 */
var THREE = require("three");
var EventEmitter_1 = require("../utils/EventEmitter");
var Renderer = /** @class */ (function (_super) {
    __extends(Renderer, _super);
    /**
     * 创建渲染器实例
     * @param options 渲染器选项
     */
    function Renderer(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this) || this;
        /** 调整大小监听器 */
        _this.resizeListener = null;
        // 获取画布元素
        if (options.canvas) {
            if (typeof options.canvas === 'string') {
                var element = document.getElementById(options.canvas);
                if (!element || !(element instanceof HTMLCanvasElement)) {
                    throw new Error("\u627E\u4E0D\u5230ID\u4E3A ".concat(options.canvas, " \u7684\u753B\u5E03\u5143\u7D20"));
                }
                _this.canvas = element;
            }
            else {
                _this.canvas = options.canvas;
            }
        }
        else {
            _this.canvas = document.createElement('canvas');
            document.body.appendChild(_this.canvas);
        }
        // 创建Three.js渲染器
        _this.renderer = new THREE.WebGLRenderer({
            canvas: _this.canvas,
            antialias: options.antialias !== undefined ? options.antialias : true,
            alpha: options.alpha !== undefined ? options.alpha : false,
            depth: options.depth !== undefined ? options.depth : true,
            stencil: options.stencil !== undefined ? options.stencil : true,
            logarithmicDepthBuffer: options.logarithmicDepthBuffer !== undefined ? options.logarithmicDepthBuffer : false,
        });
        // 设置渲染器属性
        _this.renderer.setClearColor(0x000000);
        _this.renderer.setPixelRatio(options.pixelRatio || window.devicePixelRatio);
        // 设置阴影
        if (options.shadows !== undefined ? options.shadows : true) {
            _this.renderer.shadowMap.enabled = true;
            _this.renderer.shadowMap.type = options.shadowMapType || THREE.PCFSoftShadowMap;
        }
        // 设置色调映射
        if (options.toneMapping) {
            _this.renderer.toneMapping = options.toneMapping;
            _this.renderer.toneMappingExposure = options.toneMappingExposure || 1.0;
        }
        // 设置输出颜色空间
        if (options.outputColorSpace) {
            _this.renderer.outputColorSpace = options.outputColorSpace;
        }
        else {
            _this.renderer.outputColorSpace = THREE.SRGBColorSpace;
        }
        // 设置自动清除
        _this.renderer.autoClear = options.autoClear !== undefined ? options.autoClear : true;
        // 设置自动调整大小
        _this.autoResize = options.autoResize !== undefined ? options.autoResize : true;
        // 设置初始大小
        var width = options.width || _this.canvas.clientWidth || 800;
        var height = options.height || _this.canvas.clientHeight || 600;
        _this.setSize(width, height);
        // 如果启用自动调整大小，添加窗口调整大小事件监听器
        if (_this.autoResize) {
            _this.resizeListener = _this.handleResize.bind(_this);
            window.addEventListener('resize', _this.resizeListener);
        }
        return _this;
    }
    /**
     * 处理窗口调整大小事件
     */
    Renderer.prototype.handleResize = function () {
        if (!this.canvas.parentElement) {
            return;
        }
        var width = this.canvas.parentElement.clientWidth;
        var height = this.canvas.parentElement.clientHeight;
        this.setSize(width, height);
    };
    /**
     * 设置渲染器大小
     * @param width 宽度
     * @param height 高度
     * @param updateStyle 是否更新样式
     */
    Renderer.prototype.setSize = function (width, height, updateStyle) {
        if (updateStyle === void 0) { updateStyle = true; }
        this.renderer.setSize(width, height, updateStyle);
        // 发出大小变更事件
        this.emit('resize', width, height);
    };
    /**
     * 获取渲染器大小
     * @returns 渲染器大小
     */
    Renderer.prototype.getSize = function () {
        var size = new THREE.Vector2();
        this.renderer.getSize(size);
        return {
            width: size.x,
            height: size.y,
        };
    };
    /**
     * 设置像素比例
     * @param pixelRatio 像素比例
     */
    Renderer.prototype.setPixelRatio = function (pixelRatio) {
        this.renderer.setPixelRatio(pixelRatio);
    };
    /**
     * 获取像素比例
     * @returns 像素比例
     */
    Renderer.prototype.getPixelRatio = function () {
        return this.renderer.getPixelRatio();
    };
    /**
     * 设置清除颜色
     * @param color 颜色
     * @param alpha 透明度
     */
    Renderer.prototype.setClearColor = function (color, alpha) {
        if (alpha === void 0) { alpha = 1; }
        this.renderer.setClearColor(color, alpha);
    };
    /**
     * 获取清除颜色
     * @returns 清除颜色
     */
    Renderer.prototype.getClearColor = function () {
        return this.renderer.getClearColor(new THREE.Color());
    };
    /**
     * 设置自动清除
     * @param autoClear 是否自动清除
     */
    Renderer.prototype.setAutoClear = function (autoClear) {
        this.renderer.autoClear = autoClear;
    };
    /**
     * 是否自动清除
     * @returns 是否自动清除
     */
    Renderer.prototype.isAutoClear = function () {
        return this.renderer.autoClear;
    };
    /**
     * 清除渲染器
     */
    Renderer.prototype.clear = function () {
        this.renderer.clear();
    };
    /**
     * 渲染场景
     * @param scene 场景
     * @param camera 相机
     */
    Renderer.prototype.render = function (scene, camera) {
        this.renderer.render(scene.getThreeScene(), camera.getThreeCamera());
        // 发出渲染事件
        this.emit('render', scene, camera);
    };
    /**
     * 获取Three.js渲染器
     * @returns Three.js渲染器
     */
    Renderer.prototype.getThreeRenderer = function () {
        return this.renderer;
    };
    /**
     * 获取画布元素
     * @returns 画布元素
     */
    Renderer.prototype.getCanvas = function () {
        return this.canvas;
    };
    /**
     * 设置自动调整大小
     * @param autoResize 是否自动调整大小
     */
    Renderer.prototype.setAutoResize = function (autoResize) {
        if (this.autoResize === autoResize) {
            return;
        }
        this.autoResize = autoResize;
        if (autoResize) {
            // 添加窗口调整大小事件监听器
            if (!this.resizeListener) {
                this.resizeListener = this.handleResize.bind(this);
            }
            window.addEventListener('resize', this.resizeListener);
        }
        else {
            // 移除窗口调整大小事件监听器
            if (this.resizeListener) {
                window.removeEventListener('resize', this.resizeListener);
            }
        }
    };
    /**
     * 是否自动调整大小
     * @returns 是否自动调整大小
     */
    Renderer.prototype.isAutoResize = function () {
        return this.autoResize;
    };
    /**
     * 销毁渲染器
     */
    Renderer.prototype.dispose = function () {
        // 移除窗口调整大小事件监听器
        if (this.resizeListener) {
            window.removeEventListener('resize', this.resizeListener);
            this.resizeListener = null;
        }
        // 销毁Three.js渲染器
        this.renderer.dispose();
        // 移除所有事件监听器
        this.removeAllListeners();
    };
    return Renderer;
}(EventEmitter_1.EventEmitter));
exports.Renderer = Renderer;
