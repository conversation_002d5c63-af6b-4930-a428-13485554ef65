/**
 * 冲突可视化状态管理
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ConflictVisualization } from '../../services/ConflictVisualizationService';
import { ConflictHistoryEntry } from '../../services/ConflictVisualizationService';

// 状态接口
interface ConflictVisualizationState {
  visualizations: ConflictVisualization[];
  history: ConflictHistoryEntry[];
  showVisualizationPanel: boolean;
  showHistoryPanel: boolean;
  selectedVisualizationId: string | null;
  selectedHistoryEntryId: string | null;
}

// 初始状态
const initialState: ConflictVisualizationState = {
  visualizations: [],
  history: [],
  showVisualizationPanel: false,
  showHistoryPanel: false,
  selectedVisualizationId: null,
  selectedHistoryEntryId: null
};

// 创建Slice
export const conflictVisualizationSlice = createSlice({
  name: 'conflictVisualization',
  initialState,
  reducers: {
    // 添加冲突可视化
    addConflictVisualization: (state, action: PayloadAction<ConflictVisualization>) => {
      // 检查是否已存在相同ID的可视化
      const existingIndex = state.visualizations.findIndex(v => v.id === action.payload.id);
      
      if (existingIndex >= 0) {
        // 更新现有可视化
        state.visualizations[existingIndex] = action.payload;
      } else {
        // 添加新可视化
        state.visualizations.push(action.payload);
      }
    },
    
    // 移除冲突可视化
    removeConflictVisualization: (state, action: PayloadAction<string>) => {
      state.visualizations = state.visualizations.filter(v => v.id !== action.payload);
      
      // 如果选中的可视化被移除，清除选中状态
      if (state.selectedVisualizationId === action.payload) {
        state.selectedVisualizationId = null;
      }
    },
    
    // 清除所有冲突可视化
    clearConflictVisualizations: (state) => {
      state.visualizations = [];
      state.selectedVisualizationId = null;
    },
    
    // 添加冲突历史记录
    addConflictHistory: (state, action: PayloadAction<ConflictHistoryEntry>) => {
      // 检查是否已存在相同ID的历史记录
      const existingIndex = state.history.findIndex(h => h.id === action.payload.id);
      
      if (existingIndex >= 0) {
        // 更新现有历史记录
        state.history[existingIndex] = action.payload;
      } else {
        // 添加新历史记录
        state.history.push(action.payload);
      }
      
      // 按创建时间排序
      state.history.sort((a, b) => b.createdAt - a.createdAt);
    },
    
    // 更新冲突历史记录
    updateConflictHistory: (state, action: PayloadAction<{ id: string, resolvedAt: number }>) => {
      const { id, resolvedAt } = action.payload;
      const historyEntry = state.history.find(h => h.id === id);
      
      if (historyEntry) {
        historyEntry.resolvedAt = resolvedAt;
      }
    },
    
    // 清除冲突历史记录
    clearConflictHistory: (state) => {
      state.history = [];
      state.selectedHistoryEntryId = null;
    },
    
    // 设置是否显示可视化面板
    setShowVisualizationPanel: (state, action: PayloadAction<boolean>) => {
      state.showVisualizationPanel = action.payload;
    },
    
    // 设置是否显示历史面板
    setShowHistoryPanel: (state, action: PayloadAction<boolean>) => {
      state.showHistoryPanel = action.payload;
    },
    
    // 设置选中的可视化ID
    setSelectedVisualizationId: (state, action: PayloadAction<string | null>) => {
      state.selectedVisualizationId = action.payload;
    },
    
    // 设置选中的历史记录ID
    setSelectedHistoryEntryId: (state, action: PayloadAction<string | null>) => {
      state.selectedHistoryEntryId = action.payload;
    }
  }
});

// 导出Actions
export const {
  addConflictVisualization,
  removeConflictVisualization,
  clearConflictVisualizations,
  addConflictHistory,
  updateConflictHistory,
  clearConflictHistory,
  setShowVisualizationPanel,
  setShowHistoryPanel,
  setSelectedVisualizationId,
  setSelectedHistoryEntryId
} = conflictVisualizationSlice.actions;

// 导出Reducer
export default conflictVisualizationSlice.reducer;
