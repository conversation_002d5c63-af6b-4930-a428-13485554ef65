/**
 * 动画服务
 */
import axios from 'axios';
import { Animation } from '../store/animations/animationsSlice';

const API_URL = '/api/animations';

export const animationService = {
  /**
   * 获取所有动画
   */
  async getAnimations() {
    const response = await axios.get(API_URL);
    return response.data;
  },

  /**
   * 获取单个动画
   */
  async getAnimation(animationId: string) {
    const response = await axios.get(`${API_URL}/${animationId}`);
    return response.data;
  },

  /**
   * 创建动画
   */
  async createAnimation(animationData: Omit<Animation, 'id' | 'createdAt' | 'updatedAt'>) {
    const response = await axios.post(API_URL, animationData);
    return response.data;
  },

  /**
   * 更新动画
   */
  async updateAnimation(animationId: string, animationData: Partial<Animation>) {
    const response = await axios.patch(`${API_URL}/${animationId}`, animationData);
    return response.data;
  },

  /**
   * 删除动画
   */
  async deleteAnimation(animationId: string) {
    await axios.delete(`${API_URL}/${animationId}`);
  },

  /**
   * 导出动画
   */
  async exportAnimation(animationId: string) {
    const response = await axios.get(`${API_URL}/${animationId}/export`);
    return response.data;
  },

  /**
   * 导入动画
   */
  async importAnimation(animationData: any) {
    const response = await axios.post(`${API_URL}/import`, animationData);
    return response.data;
  },

  /**
   * 复制动画
   */
  async duplicateAnimation(animationId: string, newName?: string) {
    const response = await axios.post(`${API_URL}/${animationId}/duplicate`, { name: newName });
    return response.data;
  },

  /**
   * 应用动画到实体
   */
  async applyAnimation(animationId: string, entityId: string) {
    const response = await axios.post(`${API_URL}/${animationId}/apply`, { entityId });
    return response.data;
  },
};
