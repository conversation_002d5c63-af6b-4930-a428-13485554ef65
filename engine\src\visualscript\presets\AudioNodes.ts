/**
 * 音频相关的可视化脚本节点
 */

import { VisualScriptNode } from '../VisualScriptNode';
import { NodeRegistry } from '../NodeRegistry';

/**
 * 播放音频节点
 */
export class PlayAudioNode extends VisualScriptNode {
  constructor() {
    super('PlayAudio', '播放音频');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('audioClip', 'string', '音频片段');
    this.addInput('volume', 'number', '音量');
    this.addInput('loop', 'boolean', '循环');
    this.addOutput('completed', 'exec', '完成');
  }

  public execute(inputs: any): any {
    if (inputs.trigger && inputs.audioClip) {
      // 这里应该连接到实际的音频系统
      console.log(`播放音频: ${inputs.audioClip}, 音量: ${inputs.volume || 1}, 循环: ${inputs.loop || false}`);
      return { completed: true };
    }
    return {};
  }
}

/**
 * 停止音频节点
 */
export class StopAudioNode extends VisualScriptNode {
  constructor() {
    super('StopAudio', '停止音频');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('audioClip', 'string', '音频片段');
    this.addOutput('completed', 'exec', '完成');
  }

  public execute(inputs: any): any {
    if (inputs.trigger) {
      // 这里应该连接到实际的音频系统
      console.log(`停止音频: ${inputs.audioClip || '全部'}`);
      return { completed: true };
    }
    return {};
  }
}

/**
 * 设置音量节点
 */
export class SetVolumeNode extends VisualScriptNode {
  constructor() {
    super('SetVolume', '设置音量');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('volume', 'number', '音量');
    this.addInput('fadeTime', 'number', '淡入淡出时间');
    this.addOutput('completed', 'exec', '完成');
  }

  public execute(inputs: any): any {
    if (inputs.trigger && typeof inputs.volume === 'number') {
      const volume = Math.max(0, Math.min(1, inputs.volume));
      const fadeTime = inputs.fadeTime || 0;
      
      // 这里应该连接到实际的音频系统
      console.log(`设置音量: ${volume}, 淡入淡出时间: ${fadeTime}`);
      return { completed: true };
    }
    return {};
  }
}

/**
 * 音频分析节点
 */
export class AudioAnalyzerNode extends VisualScriptNode {
  constructor() {
    super('AudioAnalyzer', '音频分析');
    this.addInput('audioSource', 'string', '音频源');
    this.addOutput('volume', 'number', '音量');
    this.addOutput('frequency', 'array', '频率数据');
    this.addOutput('waveform', 'array', '波形数据');
  }

  public execute(inputs: any): any {
    // 这里应该连接到实际的音频分析系统
    // 暂时返回模拟数据
    return {
      volume: 0.5,
      frequency: new Array(256).fill(0),
      waveform: new Array(1024).fill(0)
    };
  }
}

/**
 * 3D音频节点
 */
export class Audio3DNode extends VisualScriptNode {
  constructor() {
    super('Audio3D', '3D音频');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('audioClip', 'string', '音频片段');
    this.addInput('position', 'vector3', '位置');
    this.addInput('maxDistance', 'number', '最大距离');
    this.addInput('rolloffFactor', 'number', '衰减因子');
    this.addOutput('completed', 'exec', '完成');
  }

  public execute(inputs: any): any {
    if (inputs.trigger && inputs.audioClip && inputs.position) {
      // 这里应该连接到实际的3D音频系统
      console.log(`播放3D音频: ${inputs.audioClip} 在位置 (${(inputs as any).getPosition().x}, ${(inputs as any).getPosition().y}, ${(inputs as any).getPosition().z})`);
      return { completed: true };
    }
    return {};
  }
}

/**
 * 注册音频节点
 */
export function registerAudioNodes(): void {
  NodeRegistry.register('PlayAudio', PlayAudioNode);
  NodeRegistry.register('StopAudio', StopAudioNode);
  NodeRegistry.register('SetVolume', SetVolumeNode);
  NodeRegistry.register('AudioAnalyzer', AudioAnalyzerNode);
  NodeRegistry.register('Audio3D', Audio3DNode);
}
