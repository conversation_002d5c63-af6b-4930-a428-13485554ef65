/**
 * LOD生成器
 * 用于自动生成不同细节级别的模型
 */
import * as THREE from 'three';
import { LODLevel } from './LODComponent';

/**
 * LOD生成器配置接口
 */
export interface LODGeneratorOptions {
  /** 高细节级别的简化比例 */
  highDetailRatio?: number;
  /** 中细节级别的简化比例 */
  mediumDetailRatio?: number;
  /** 低细节级别的简化比例 */
  lowDetailRatio?: number;
  /** 极低细节级别的简化比例 */
  veryLowDetailRatio?: number;
  /** 是否保留UV */
  preserveUVs?: boolean;
  /** 是否保留法线 */
  preserveNormals?: boolean;
  /** 是否保留颜色 */
  preserveColors?: boolean;
}

/**
 * LOD生成结果接口
 */
export interface LODGeneratorResult {
  /** 原始网格 */
  original: THREE.Mesh;
  /** 高细节网格 */
  high: THREE.Mesh;
  /** 中细节网格 */
  medium: THREE.Mesh;
  /** 低细节网格 */
  low: THREE.Mesh;
  /** 极低细节网格 */
  veryLow: THREE.Mesh;
}

/**
 * LOD生成器类
 */
export class LODGenerator {
  /** 高细节级别的简化比例 */
  private highDetailRatio: number;

  /** 中细节级别的简化比例 */
  private mediumDetailRatio: number;

  /** 低细节级别的简化比例 */
  private lowDetailRatio: number;

  /** 极低细节级别的简化比例 */
  private veryLowDetailRatio: number;

  /** 是否保留UV */
  private preserveUVs: boolean;

  /** 是否保留法线 */
  private preserveNormals: boolean;

  /** 是否保留颜色 */
  private preserveColors: boolean;

  /**
   * 创建LOD生成器
   * @param options LOD生成器配置
   */
  constructor(options: LODGeneratorOptions = {}) {
    this.highDetailRatio = options.highDetailRatio !== undefined ? options.highDetailRatio : 0.8;
    this.mediumDetailRatio = options.mediumDetailRatio !== undefined ? options.mediumDetailRatio : 0.5;
    this.lowDetailRatio = options.lowDetailRatio !== undefined ? options.lowDetailRatio : 0.25;
    this.veryLowDetailRatio = options.veryLowDetailRatio !== undefined ? options.veryLowDetailRatio : 0.1;
    this.preserveUVs = options.preserveUVs !== undefined ? options.preserveUVs : true;
    this.preserveNormals = options.preserveNormals !== undefined ? options.preserveNormals : true;
    this.preserveColors = options.preserveColors !== undefined ? options.preserveColors : true;
  }

  /**
   * 生成LOD
   * @param mesh 原始网格
   * @returns LOD生成结果
   */
  public generate(mesh: THREE.Mesh): LODGeneratorResult {
    // 检查网格是否有效
    if (!mesh || !mesh.geometry) {
      throw new Error('无效的网格');
    }

    // 克隆原始网格
    const original = mesh.clone();

    // 生成不同细节级别的网格
    const high = this.generateLevel(mesh, this.highDetailRatio);
    const medium = this.generateLevel(mesh, this.mediumDetailRatio);
    const low = this.generateLevel(mesh, this.lowDetailRatio);
    const veryLow = this.generateLevel(mesh, this.veryLowDetailRatio);

    // 返回结果
    return {
      original,
      high,
      medium,
      low,
      veryLow
    };
  }

  /**
   * 生成指定细节级别的网格
   * @param mesh 原始网格
   * @param ratio 简化比例
   * @returns 简化后的网格
   */
  public generateLevel(mesh: THREE.Mesh, ratio: number): THREE.Mesh {
    // 克隆网格
    const clonedMesh = mesh.clone();
    const geometry = clonedMesh.geometry;

    // 如果比例为1，则不进行简化
    if (ratio >= 1) {
      return clonedMesh;
    }

    // 简化几何体
    const simplifiedGeometry = this.simplifyGeometry(geometry, ratio);

    // 创建新网格
    const newMesh = new THREE.Mesh(simplifiedGeometry, clonedMesh.material);

    // 复制网格属性
    newMesh.name = clonedMesh.name;
    newMesh.castShadow = clonedMesh.castShadow;
    newMesh.receiveShadow = clonedMesh.receiveShadow;
    newMesh.visible = clonedMesh.visible;
    newMesh.userData = { ...clonedMesh.userData };

    return newMesh;
  }

  /**
   * 简化几何体
   * @param geometry 原始几何体
   * @param ratio 简化比例
   * @returns 简化后的几何体
   */
  private simplifyGeometry(geometry: THREE.BufferGeometry, ratio: number): THREE.BufferGeometry {
    // 确保几何体是BufferGeometry
    if (!(geometry instanceof THREE.BufferGeometry)) {
      throw new Error('几何体必须是BufferGeometry');
    }

    // 获取几何体的顶点数
    const positions = geometry.getAttribute('position');
    if (!positions) {
      throw new Error('几何体没有position属性');
    }

    // 计算目标顶点数
    const vertexCount = positions.count;
    const targetVertexCount = Math.max(4, Math.floor(vertexCount * ratio));

    // 如果目标顶点数大于等于当前顶点数，则不进行简化
    if (targetVertexCount >= vertexCount) {
      return geometry.clone();
    }

    // 使用Three.js的简化算法
    // 注意：Three.js没有内置的几何体简化算法，这里使用一个简单的实现
    // 在实际应用中，应该使用更高级的简化算法，如Simplify.js或MeshOptimizer
    return this.simplifyMesh(geometry, targetVertexCount);
  }

  /**
   * 简化网格
   * @param geometry 原始几何体
   * @param targetVertexCount 目标顶点数
   * @returns 简化后的几何体
   */
  private simplifyMesh(geometry: THREE.BufferGeometry, targetVertexCount: number): THREE.BufferGeometry {
    // 确保几何体是索引的
    if (!geometry.index) {
      geometry = geometry.clone();
      geometry.setIndex(this.generateIndices(geometry));
    }

    // 克隆几何体
    const simplifiedGeometry = geometry.clone();

    // 获取几何体的顶点和索引
    const positions = simplifiedGeometry.getAttribute('position');
    const indices = simplifiedGeometry.getIndex();

    if (!positions || !indices) {
      throw new Error('几何体没有position或index属性');
    }

    // 获取顶点数和三角形数
    const vertexCount = positions.count;
    const triangleCount = indices.count / 3;

    // 计算需要移除的三角形数
    const targetTriangleCount = Math.max(2, Math.floor(targetVertexCount / 3) * 3);
    const trianglesToRemove = triangleCount - targetTriangleCount;

    // 如果不需要移除三角形，则直接返回
    if (trianglesToRemove <= 0) {
      return simplifiedGeometry;
    }

    // 计算每个三角形的重要性
    const triangleImportance = this.calculateTriangleImportance(simplifiedGeometry);

    // 按重要性排序三角形
    const triangleIndices = Array.from({ length: triangleCount }, (_, i) => i);
    triangleIndices.sort((a, b) => triangleImportance[a] - triangleImportance[b]);

    // 移除最不重要的三角形
    const trianglesToKeep = triangleIndices.slice(trianglesToRemove);
    trianglesToKeep.sort((a, b) => a - b);

    // 创建新的索引数组
    const newIndices = new Uint32Array(targetTriangleCount * 3);
    let newIndexCount = 0;

    for (const triangleIndex of trianglesToKeep) {
      const indexOffset = triangleIndex * 3;
      newIndices[newIndexCount++] = indices.getX(indexOffset);
      newIndices[newIndexCount++] = indices.getX(indexOffset + 1);
      newIndices[newIndexCount++] = indices.getX(indexOffset + 2);
    }

    // 更新几何体的索引
    simplifiedGeometry.setIndex(new THREE.BufferAttribute(newIndices, 1));

    // 重新计算法线
    if (this.preserveNormals) {
      simplifiedGeometry.computeVertexNormals();
    }

    return simplifiedGeometry;
  }

  /**
   * 计算三角形的重要性
   * @param geometry 几何体
   * @returns 三角形重要性数组
   */
  private calculateTriangleImportance(geometry: THREE.BufferGeometry): number[] {
    // 获取几何体的顶点和索引
    const positions = geometry.getAttribute('position');
    const indices = geometry.getIndex();

    if (!positions || !indices) {
      throw new Error('几何体没有position或index属性');
    }

    // 获取三角形数
    const triangleCount = indices.count / 3;

    // 计算每个三角形的面积
    const triangleAreas = new Array(triangleCount);
    const v1 = new THREE.Vector3();
    const v2 = new THREE.Vector3();
    const v3 = new THREE.Vector3();
    const e1 = new THREE.Vector3();
    const e2 = new THREE.Vector3();
    const normal = new THREE.Vector3();

    for (let i = 0; i < triangleCount; i++) {
      const i3 = i * 3;
      const i1 = indices.getX(i3);
      const i2 = indices.getX(i3 + 1);
      const i3i = indices.getX(i3 + 2);

      v1.fromBufferAttribute(positions, i1);
      v2.fromBufferAttribute(positions, i2);
      v3.fromBufferAttribute(positions, i3i);

      e1.subVectors(v2, v1);
      e2.subVectors(v3, v1);
      normal.crossVectors(e1, e2);

      triangleAreas[i] = normal.length() * 0.5;
    }

    return triangleAreas;
  }

  /**
   * 为非索引几何体生成索引
   * @param geometry 几何体
   * @returns 索引数组
   */
  private generateIndices(geometry: THREE.BufferGeometry): THREE.BufferAttribute {
    const positions = geometry.getAttribute('position');
    if (!positions) {
      throw new Error('几何体没有position属性');
    }

    const vertexCount = positions.count;
    const indices = new Uint32Array(vertexCount);

    for (let i = 0; i < vertexCount; i++) {
      indices[i] = i;
    }

    return new THREE.BufferAttribute(indices, 1);
  }

  /**
   * 生成指定级别的LOD
   * @param mesh 原始网格
   * @param level 级别
   * @returns 简化后的网格
   */
  public generateLOD(mesh: THREE.Mesh, level: LODLevel): THREE.Mesh {
    switch (level) {
      case LODLevel.HIGH:
        return this.generateLevel(mesh, this.highDetailRatio);
      case LODLevel.MEDIUM:
        return this.generateLevel(mesh, this.mediumDetailRatio);
      case LODLevel.LOW:
        return this.generateLevel(mesh, this.lowDetailRatio);
      case LODLevel.VERY_LOW:
        return this.generateLevel(mesh, this.veryLowDetailRatio);
      default:
        return mesh.clone();
    }
  }

  /**
   * 设置高细节级别的简化比例
   * @param ratio 简化比例
   */
  public setHighDetailRatio(ratio: number): void {
    this.highDetailRatio = ratio;
  }

  /**
   * 获取高细节级别的简化比例
   * @returns 简化比例
   */
  public getHighDetailRatio(): number {
    return this.highDetailRatio;
  }

  /**
   * 设置中细节级别的简化比例
   * @param ratio 简化比例
   */
  public setMediumDetailRatio(ratio: number): void {
    this.mediumDetailRatio = ratio;
  }

  /**
   * 获取中细节级别的简化比例
   * @returns 简化比例
   */
  public getMediumDetailRatio(): number {
    return this.mediumDetailRatio;
  }

  /**
   * 设置低细节级别的简化比例
   * @param ratio 简化比例
   */
  public setLowDetailRatio(ratio: number): void {
    this.lowDetailRatio = ratio;
  }

  /**
   * 获取低细节级别的简化比例
   * @returns 简化比例
   */
  public getLowDetailRatio(): number {
    return this.lowDetailRatio;
  }

  /**
   * 设置极低细节级别的简化比例
   * @param ratio 简化比例
   */
  public setVeryLowDetailRatio(ratio: number): void {
    this.veryLowDetailRatio = ratio;
  }

  /**
   * 获取极低细节级别的简化比例
   * @returns 简化比例
   */
  public getVeryLowDetailRatio(): number {
    return this.veryLowDetailRatio;
  }
}
