/**
 * 粒子系统编辑器样式
 */
.particle-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .particle-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
    
    h2 {
      margin: 0;
    }
    
    .particle-editor-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .particle-editor-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    
    .particle-editor-preview {
      width: 40%;
      padding: 16px;
      display: flex;
      flex-direction: column;
      
      .preview-canvas {
        flex: 1;
        width: 100%;
        background-color: #1e1e1e;
        border-radius: 4px;
      }
      
      .preview-controls {
        display: flex;
        justify-content: center;
        gap: 8px;
        margin-top: 16px;
      }
      
      .preset-controls {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 16px;
        flex-wrap: wrap;
        
        span {
          margin-right: 8px;
        }
      }
    }
    
    .particle-editor-form {
      width: 60%;
      padding: 16px;
      overflow-y: auto;
      
      .ant-form-item {
        margin-bottom: 16px;
      }
    }
  }
}
