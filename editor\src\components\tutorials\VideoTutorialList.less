.video-tutorial-list {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
  
  .tutorial-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .ant-typography {
      margin: 0;
    }
    
    .search-bar {
      width: 300px;
    }
  }
  
  .tutorial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    margin-top: 16px;
    
    .tutorial-grid-item {
      display: flex;
    }
  }
  
  .tutorial-card {
    width: 100%;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    .tutorial-thumbnail {
      position: relative;
      height: 168px; // 16:9 ratio for 300px width
      overflow: hidden;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .progress-bar {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background-color: rgba(0, 0, 0, 0.3);
        
        .progress {
          height: 100%;
          background-color: #1890ff;
        }
      }
      
      .play-button {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        opacity: 0;
        transition: opacity 0.2s ease;
      }
      
      &:hover {
        .play-button {
          opacity: 1;
        }
      }
    }
    
    .tutorial-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .watched-icon {
        color: #52c41a;
      }
    }
    
    .tutorial-meta {
      .tutorial-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        margin-bottom: 4px;
      }
      
      .tutorial-duration {
        color: #999;
        font-size: 12px;
      }
    }
    
    .ant-card-actions {
      background-color: #fafafa;
    }
  }
  
  .search-results {
    margin-top: 16px;
    
    .ant-typography {
      margin-bottom: 16px;
    }
  }
  
  .ant-tabs {
    .ant-tabs-nav {
      margin-bottom: 16px;
    }
  }
  
  .ant-empty {
    margin: 32px 0;
  }
}
