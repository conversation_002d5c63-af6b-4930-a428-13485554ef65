"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.I18n = void 0;
/**
 * 国际化类
 * 提供多语言支持
 */
var EventEmitter_1 = require("../utils/EventEmitter");
var I18n = /** @class */ (function (_super) {
    __extends(I18n, _super);
    /**
     * 创建国际化实例
     * @param options 国际化选项
     */
    function I18n(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this) || this;
        /** 语言资源 */
        _this.resources = new Map();
        _this.language = options.language || 'zh-CN';
        _this.fallbackLanguage = options.fallbackLanguage || 'en-US';
        // 加载语言资源
        if (options.resources) {
            for (var _i = 0, _a = Object.entries(options.resources); _i < _a.length; _i++) {
                var _b = _a[_i], lang = _b[0], translations = _b[1];
                _this.addResources(lang, translations);
            }
        }
        // 加载默认语言资源
        _this.loadDefaultResources();
        return _this;
    }
    /**
     * 加载默认语言资源
     */
    I18n.prototype.loadDefaultResources = function () {
        // 中文资源
        this.addResources('zh-CN', {
            'common.yes': '是',
            'common.no': '否',
            'common.ok': '确定',
            'common.cancel': '取消',
            'common.save': '保存',
            'common.delete': '删除',
            'common.edit': '编辑',
            'common.create': '创建',
            'common.update': '更新',
            'common.search': '搜索',
            'common.loading': '加载中...',
            'common.error': '错误',
            'common.success': '成功',
            'common.warning': '警告',
            'common.info': '信息',
            'engine.initialized': '引擎已初始化',
            'engine.started': '引擎已启动',
            'engine.stopped': '引擎已停止',
            'engine.error': '引擎错误',
            'scene.created': '场景已创建',
            'scene.loaded': '场景已加载',
            'scene.unloaded': '场景已卸载',
            'scene.cleared': '场景已清空',
            'entity.created': '实体已创建',
            'entity.deleted': '实体已删除',
            'entity.duplicated': '实体已复制',
            'entity.renamed': '实体已重命名',
            'component.added': '组件已添加',
            'component.removed': '组件已移除',
            'component.enabled': '组件已启用',
            'component.disabled': '组件已禁用',
            'asset.loaded': '资源已加载',
            'asset.unloaded': '资源已卸载',
            'asset.error': '资源加载错误',
            'physics.collision': '物理碰撞',
            'physics.trigger': '物理触发器',
            'input.keyDown': '按键按下',
            'input.keyUp': '按键抬起',
            'input.mouseDown': '鼠标按下',
            'input.mouseUp': '鼠标抬起',
            'input.mouseMove': '鼠标移动',
            'input.mouseWheel': '鼠标滚轮',
            'input.touchStart': '触摸开始',
            'input.touchEnd': '触摸结束',
            'input.touchMove': '触摸移动',
            'error.notFound': '未找到',
            'error.invalidParameter': '无效参数',
            'error.missingParameter': '缺少参数',
            'error.notSupported': '不支持',
            'error.notImplemented': '未实现',
            'error.outOfMemory': '内存不足',
            'error.timeout': '超时',
        });
        // 英文资源
        this.addResources('en-US', {
            'common.yes': 'Yes',
            'common.no': 'No',
            'common.ok': 'OK',
            'common.cancel': 'Cancel',
            'common.save': 'Save',
            'common.delete': 'Delete',
            'common.edit': 'Edit',
            'common.create': 'Create',
            'common.update': 'Update',
            'common.search': 'Search',
            'common.loading': 'Loading...',
            'common.error': 'Error',
            'common.success': 'Success',
            'common.warning': 'Warning',
            'common.info': 'Info',
            'engine.initialized': 'Engine initialized',
            'engine.started': 'Engine started',
            'engine.stopped': 'Engine stopped',
            'engine.error': 'Engine error',
            'scene.created': 'Scene created',
            'scene.loaded': 'Scene loaded',
            'scene.unloaded': 'Scene unloaded',
            'scene.cleared': 'Scene cleared',
            'entity.created': 'Entity created',
            'entity.deleted': 'Entity deleted',
            'entity.duplicated': 'Entity duplicated',
            'entity.renamed': 'Entity renamed',
            'component.added': 'Component added',
            'component.removed': 'Component removed',
            'component.enabled': 'Component enabled',
            'component.disabled': 'Component disabled',
            'asset.loaded': 'Asset loaded',
            'asset.unloaded': 'Asset unloaded',
            'asset.error': 'Asset loading error',
            'physics.collision': 'Physics collision',
            'physics.trigger': 'Physics trigger',
            'input.keyDown': 'Key down',
            'input.keyUp': 'Key up',
            'input.mouseDown': 'Mouse down',
            'input.mouseUp': 'Mouse up',
            'input.mouseMove': 'Mouse move',
            'input.mouseWheel': 'Mouse wheel',
            'input.touchStart': 'Touch start',
            'input.touchEnd': 'Touch end',
            'input.touchMove': 'Touch move',
            'error.notFound': 'Not found',
            'error.invalidParameter': 'Invalid parameter',
            'error.missingParameter': 'Missing parameter',
            'error.notSupported': 'Not supported',
            'error.notImplemented': 'Not implemented',
            'error.outOfMemory': 'Out of memory',
            'error.timeout': 'Timeout',
        });
    };
    /**
     * 添加语言资源
     * @param language 语言
     * @param resources 资源
     */
    I18n.prototype.addResources = function (language, resources) {
        if (!this.resources.has(language)) {
            this.resources.set(language, new Map());
        }
        var langResources = this.resources.get(language);
        for (var _i = 0, _a = Object.entries(resources); _i < _a.length; _i++) {
            var _b = _a[_i], key = _b[0], value = _b[1];
            langResources.set(key, value);
        }
    };
    /**
     * 设置当前语言
     * @param language 语言
     */
    I18n.prototype.setLanguage = function (language) {
        if (this.language === language) {
            return;
        }
        var oldLanguage = this.language;
        this.language = language;
        // 发出语言变更事件
        this.emit('languageChanged', language, oldLanguage);
    };
    /**
     * 获取当前语言
     * @returns 当前语言
     */
    I18n.prototype.getLanguage = function () {
        return this.language;
    };
    /**
     * 设置回退语言
     * @param language 语言
     */
    I18n.prototype.setFallbackLanguage = function (language) {
        this.fallbackLanguage = language;
    };
    /**
     * 获取回退语言
     * @returns 回退语言
     */
    I18n.prototype.getFallbackLanguage = function () {
        return this.fallbackLanguage;
    };
    /**
     * 翻译文本
     * @param key 翻译键
     * @param params 参数
     * @returns 翻译后的文本
     */
    I18n.prototype.translate = function (key, params) {
        if (params === void 0) { params = {}; }
        // 尝试从当前语言获取翻译
        var translation = this.getTranslation(this.language, key);
        // 如果没有找到，尝试从回退语言获取翻译
        if (!translation && this.fallbackLanguage !== this.language) {
            translation = this.getTranslation(this.fallbackLanguage, key);
        }
        // 如果仍然没有找到，返回键名
        if (!translation) {
            return key;
        }
        // 替换参数
        return this.replaceParams(translation, params);
    };
    /**
     * 获取翻译
     * @param language 语言
     * @param key 翻译键
     * @returns 翻译文本
     */
    I18n.prototype.getTranslation = function (language, key) {
        var langResources = this.resources.get(language);
        if (!langResources) {
            return undefined;
        }
        return langResources.get(key);
    };
    /**
     * 替换参数
     * @param text 文本
     * @param params 参数
     * @returns 替换后的文本
     */
    I18n.prototype.replaceParams = function (text, params) {
        return text.replace(/\{(\w+)\}/g, function (match, key) {
            return params[key] !== undefined ? String(params[key]) : match;
        });
    };
    /**
     * 获取支持的语言列表
     * @returns 语言列表
     */
    I18n.prototype.getLanguages = function () {
        return Array.from(this.resources.keys());
    };
    /**
     * 是否支持指定语言
     * @param language 语言
     * @returns 是否支持
     */
    I18n.prototype.hasLanguage = function (language) {
        return this.resources.has(language);
    };
    /**
     * 获取语言资源
     * @param language 语言
     * @returns 语言资源
     */
    I18n.prototype.getResources = function (language) {
        var langResources = this.resources.get(language);
        if (!langResources) {
            return {};
        }
        var result = {};
        for (var _i = 0, _a = Array.from(langResources.entries()); _i < _a.length; _i++) {
            var _b = _a[_i], key = _b[0], value = _b[1];
            result[key] = value;
        }
        return result;
    };
    return I18n;
}(EventEmitter_1.EventEmitter));
exports.I18n = I18n;
