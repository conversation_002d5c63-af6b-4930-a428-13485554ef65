/**
 * 角色物理交互示例
 * 展示如何使用物理交互系统实现角色与物理对象的交互
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { Entity } from '../../src/core/Entity';
import { World } from '../../src/core/World';
import { TransformComponent } from '../../src/core/TransformComponent';
import { MeshComponent } from '../../src/rendering/MeshComponent';
import { PhysicsSystem } from '../../src/physics/PhysicsSystem';
import { PhysicsBody } from '../../src/physics/PhysicsBody';
import { PhysicsCollider } from '../../src/physics/PhysicsCollider';
import { CharacterControllerComponent } from '../../src/physics/components/CharacterControllerComponent';
import { PhysicsCharacterInteractionSystem } from '../../src/physics/interaction/PhysicsCharacterInteractionSystem';
import { PhysicsInteractionComponent, InteractionType } from '../../src/physics/interaction/PhysicsInteractionComponent';
import { InputSystem } from '../../src/input/InputSystem';
import { KeyboardInput } from '../../src/input/KeyboardInput';
import { Debug } from '../../src/utils/Debug';

/**
 * 角色物理交互示例
 */
export class CharacterPhysicsInteractionExample {
  /** 引擎实例 */
  private engine: Engine;

  /** 世界实例 */
  private world: World;

  /** 物理系统 */
  private physicsSystem: PhysicsSystem;

  /** 物理角色交互系统 */
  private interactionSystem: PhysicsCharacterInteractionSystem;

  /** 输入系统 */
  private inputSystem: InputSystem;

  /** 角色实体 */
  private character: Entity;

  /** 可交互物体 */
  private interactableObjects: Entity[] = [];

  /** 当前交互目标 */
  private currentInteractionTarget: Entity | null = null;

  /** 当前交互类型 */
  private currentInteractionType: InteractionType | null = null;

  /**
   * 创建示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();

    // 获取世界
    this.world = this.engine.getWorld();

    // 创建物理系统
    this.physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.81, z: 0 },
      debug: true
    });

    // 创建物理角色交互系统
    this.interactionSystem = new PhysicsCharacterInteractionSystem({
      debug: true,
      maxInteractionDistance: 2.0,
      maxInteractionForce: 1000.0,
      interactionForceDamping: 0.5,
      enableForceFeedback: true,
      enableInteractionAnimation: true
    });

    // 创建输入系统
    this.inputSystem = new InputSystem();

    // 添加系统到引擎
    this.engine.addSystem(this.physicsSystem);
    this.engine.addSystem(this.interactionSystem);
    this.engine.addSystem(this.inputSystem);
  }

  /**
   * 初始化示例
   */
  public initialize(): void {
    // 创建场景
    this.createScene();

    // 创建角色
    this.character = this.createCharacter();

    // 创建可交互物体
    this.createInteractableObjects();

    // 设置输入处理
    this.setupInput();

    // 设置交互事件监听
    this.setupInteractionEvents();

    // 初始化引擎
    this.engine.initialize();
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 创建地面
    const ground = new Entity('ground');

    // 添加变换组件
    ground.addComponent(new TransformComponent({
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 20, y: 0.1, z: 20 }
    }));

    // 添加网格组件
    ground.addComponent(new MeshComponent({
      geometry: new THREE.BoxGeometry(1, 1, 1),
      material: new THREE.MeshStandardMaterial({ color: 0x808080 })
    }));

    // 添加物理体组件
    ground.addComponent(new PhysicsBody({
      type: 'static',
      mass: 0
    }));

    // 添加碰撞器组件
    ground.addComponent(new PhysicsCollider({
      type: 'box',
      size: { x: 20, y: 0.1, z: 20 }
    }));

    // 添加到世界
    this.world.addEntity(ground);

    // 创建光源
    const light = new Entity('light');

    // 添加变换组件
    light.addComponent(new TransformComponent({
      position: { x: 0, y: 10, z: 0 }
    }));

    // 创建环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 1);
    this.engine.getScene().add(ambientLight);

    // 创建方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(5, 10, 5);
    directionalLight.castShadow = true;
    this.engine.getScene().add(directionalLight);
  }

  /**
   * 创建角色
   * @returns 角色实体
   */
  private createCharacter(): Entity {
    // 创建角色实体
    const character = new Entity('character');

    // 添加变换组件
    character.addComponent(new TransformComponent({
      position: { x: 0, y: 1, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 0.5, y: 1, z: 0.5 }
    }));

    // 添加网格组件
    character.addComponent(new MeshComponent({
      geometry: new THREE.BoxGeometry(1, 1, 1),
      material: new THREE.MeshStandardMaterial({ color: 0x0000ff })
    }));

    // 添加物理体组件
    character.addComponent(new PhysicsBody({
      type: 'dynamic',
      mass: 70,
      fixedRotation: true
    }));

    // 添加碰撞器组件
    character.addComponent(new PhysicsCollider({
      type: 'capsule',
      radius: 0.25,
      height: 1.0
    }));

    // 添加角色控制器组件
    character.addComponent(new CharacterControllerComponent({
      walkSpeed: 5.0,
      runSpeed: 10.0,
      jumpForce: 7.0,
      airControl: 0.3
    }));

    // 添加到世界
    this.world.addEntity(character);

    return character;
  }

  /**
   * 创建可交互物体
   */
  private createInteractableObjects(): void {
    // 创建可推拉的箱子
    const box = this.createInteractableBox(
      'pushable_box',
      { x: 2, y: 0.5, z: 2 },
      { x: 1, y: 1, z: 1 },
      0xff0000,
      10,
      [InteractionType.PUSH, InteractionType.PULL]
    );
    this.interactableObjects.push(box);

    // 创建可举起的小箱子
    const smallBox = this.createInteractableBox(
      'liftable_box',
      { x: -2, y: 0.5, z: 2 },
      { x: 0.5, y: 0.5, z: 0.5 },
      0x00ff00,
      5,
      [InteractionType.LIFT, InteractionType.THROW]
    );
    this.interactableObjects.push(smallBox);

    // 创建可攀爬的墙
    const wall = this.createInteractableBox(
      'climbable_wall',
      { x: 0, y: 2, z: -5 },
      { x: 4, y: 4, z: 0.5 },
      0xffff00,
      0,
      [InteractionType.CLIMB]
    );
    this.interactableObjects.push(wall);

    // 创建可悬挂的横杆
    const bar = this.createInteractableBox(
      'hangable_bar',
      { x: 0, y: 3, z: 0 },
      { x: 3, y: 0.1, z: 0.1 },
      0xff00ff,
      0,
      [InteractionType.HANG]
    );
    this.interactableObjects.push(bar);
  }

  /**
   * 创建可交互的箱子
   * @param name 实体名称
   * @param position 位置
   * @param size 尺寸
   * @param color 颜色
   * @param mass 质量
   * @param interactionTypes 交互类型
   * @returns 箱子实体
   */
  private createInteractableBox(
    name: string,
    position: { x: number, y: number, z: number },
    size: { x: number, y: number, z: number },
    color: number,
    mass: number,
    interactionTypes: InteractionType[]
  ): Entity {
    // 创建箱子实体
    const box = new Entity(name);

    // 添加变换组件
    box.addComponent(new TransformComponent({
      position,
      rotation: { x: 0, y: 0, z: 0 },
      scale: size
    }));

    // 添加网格组件
    box.addComponent(new MeshComponent({
      geometry: new THREE.BoxGeometry(1, 1, 1),
      material: new THREE.MeshStandardMaterial({ color })
    }));

    // 添加物理体组件
    box.addComponent(new PhysicsBody({
      type: mass > 0 ? 'dynamic' : 'static',
      mass
    }));

    // 添加碰撞器组件
    box.addComponent(new PhysicsCollider({
      type: 'box',
      size
    }));

    // 添加交互组件
    box.addComponent(new PhysicsInteractionComponent({
      enabled: true,
      maxInteractionDistance: 2.0,
      interactionForce: 500.0,
      interactionForceDamping: 0.5,
      allowedInteractionTypes: interactionTypes,
      canBePushed: interactionTypes.includes(InteractionType.PUSH),
      canBePulled: interactionTypes.includes(InteractionType.PULL),
      canBeLifted: interactionTypes.includes(InteractionType.LIFT),
      canBeThrown: interactionTypes.includes(InteractionType.THROW),
      canBeClimbed: interactionTypes.includes(InteractionType.CLIMB),
      canBeHanged: interactionTypes.includes(InteractionType.HANG),
      onInteractionStart: (entity, interactor, type) => {
        Debug.log('Interaction', `开始交互: ${interactor.id} -> ${entity.id} (${type})`);
      },
      onInteractionEnd: (entity, interactor, type) => {
        Debug.log('Interaction', `结束交互: ${interactor.id} -> ${entity.id} (${type})`);
      }
    }));

    // 添加到世界
    this.world.addEntity(box);

    return box;
  }

  /**
   * 设置输入处理
   */
  private setupInput(): void {
    // 获取键盘输入
    const keyboard = this.inputSystem.getKeyboard() as KeyboardInput;

    // 交互键处理
    keyboard.onKeyDown('e', () => {
      this.handleInteractionKeyPressed();
    });

    // 切换交互类型
    keyboard.onKeyDown('1', () => {
      this.currentInteractionType = InteractionType.PUSH;
      Debug.log('Interaction', `当前交互类型: ${this.currentInteractionType}`);
    });

    keyboard.onKeyDown('2', () => {
      this.currentInteractionType = InteractionType.PULL;
      Debug.log('Interaction', `当前交互类型: ${this.currentInteractionType}`);
    });

    keyboard.onKeyDown('3', () => {
      this.currentInteractionType = InteractionType.LIFT;
      Debug.log('Interaction', `当前交互类型: ${this.currentInteractionType}`);
    });

    keyboard.onKeyDown('4', () => {
      this.currentInteractionType = InteractionType.THROW;
      Debug.log('Interaction', `当前交互类型: ${this.currentInteractionType}`);
    });

    keyboard.onKeyDown('5', () => {
      this.currentInteractionType = InteractionType.CLIMB;
      Debug.log('Interaction', `当前交互类型: ${this.currentInteractionType}`);
    });

    keyboard.onKeyDown('6', () => {
      this.currentInteractionType = InteractionType.HANG;
      Debug.log('Interaction', `当前交互类型: ${this.currentInteractionType}`);
    });

    // 取消交互
    keyboard.onKeyDown('q', () => {
      this.cancelInteraction();
    });
  }

  /**
   * 处理交互键按下
   */
  private handleInteractionKeyPressed(): void {
    // 如果已经在交互中，取消交互
    if (this.interactionSystem.isInteracting(this.character)) {
      this.cancelInteraction();
      return;
    }

    // 如果没有选择交互类型，默认为推动
    if (!this.currentInteractionType) {
      this.currentInteractionType = InteractionType.PUSH;
    }

    // 查找最近的可交互物体
    const nearestObject = this.findNearestInteractableObject();
    if (nearestObject) {
      // 开始交互
      const success = this.interactionSystem.startInteraction(
        this.character,
        nearestObject,
        this.currentInteractionType
      );

      if (success) {
        this.currentInteractionTarget = nearestObject;
        Debug.log('Interaction', `开始交互: ${this.currentInteractionType} -> ${nearestObject.id}`);
      } else {
        Debug.warn('Interaction', `无法开始交互: ${this.currentInteractionType} -> ${nearestObject.id}`);
      }
    } else {
      Debug.warn('Interaction', '附近没有可交互物体');
    }
  }

  /**
   * 取消交互
   */
  private cancelInteraction(): void {
    if (this.currentInteractionTarget) {
      this.interactionSystem.terminateInteraction(
        this.character.id,
        this.currentInteractionTarget.id
      );

      Debug.log('Interaction', `取消交互: ${this.currentInteractionTarget.id}`);
      this.currentInteractionTarget = null;
    }
  }

  /**
   * 查找最近的可交互物体
   * @returns 最近的可交互物体，如果没有则返回null
   */
  private findNearestInteractableObject(): Entity | null {
    // 获取角色位置
    const characterTransform = this.character.getTransform();
    if (!characterTransform) return null;

    const characterPosition = characterTransform.getWorldPosition();

    // 最近距离和物体
    let nearestDistance = Number.MAX_VALUE;
    let nearestObject: Entity | null = null;

    // 遍历所有可交互物体
    for (const object of this.interactableObjects) {
      // 获取物体位置
      const objectTransform = object.getTransform();
      if (!objectTransform) continue;

      const objectPosition = objectTransform.getWorldPosition();

      // 计算距离
      const distance = new THREE.Vector3(
        characterPosition.x - objectPosition.x,
        characterPosition.y - objectPosition.y,
        characterPosition.z - objectPosition.z
      ).length();

      // 检查是否在交互距离内
      if (distance <= this.interactionSystem.config.maxInteractionDistance) {
        // 检查是否是最近的
        if (distance < nearestDistance) {
          nearestDistance = distance;
          nearestObject = object;
        }
      }
    }

    return nearestObject;
  }

  /**
   * 设置交互事件监听
   */
  private setupInteractionEvents(): void {
    // 监听交互开始事件
    this.interactionSystem.on('interactionStart', (data) => {
      Debug.log('InteractionEvent', `交互开始: ${data.character.id} -> ${data.target.id} (${data.type})`);
    });

    // 监听交互结束事件
    this.interactionSystem.on('interactionEnd', (data) => {
      Debug.log('InteractionEvent', `交互结束: ${data.character.id} -> ${data.target.id}`);
    });
  }

  /**
   * 运行示例
   */
  public run(): void {
    // 启动引擎
    this.engine.start();

    // 输出使用说明
    console.log('角色物理交互示例');
    console.log('-------------------');
    console.log('WASD: 移动角色');
    console.log('空格: 跳跃');
    console.log('E: 开始/结束交互');
    console.log('Q: 取消交互');
    console.log('1-6: 切换交互类型');
    console.log('1: 推动');
    console.log('2: 拉动');
    console.log('3: 举起');
    console.log('4: 投掷');
    console.log('5: 攀爬');
    console.log('6: 悬挂');
  }

  /**
   * 停止示例
   */
  public stop(): void {
    // 停止引擎
    this.engine.stop();
  }
}