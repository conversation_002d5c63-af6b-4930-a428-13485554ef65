"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResourceManager = exports.ResourceState = exports.AssetType = void 0;
/**
 * 资源管理器类
 * 负责资源的加载、缓存和释放
 */
var EventEmitter_1 = require("../utils/EventEmitter");
/**
 * 资产类型
 */
var AssetType;
(function (AssetType) {
    AssetType["TEXTURE"] = "texture";
    AssetType["MODEL"] = "model";
    AssetType["MATERIAL"] = "material";
    AssetType["AUDIO"] = "audio";
    AssetType["FONT"] = "font";
    AssetType["SHADER"] = "shader";
    AssetType["JSON"] = "json";
    AssetType["TEXT"] = "text";
    AssetType["BINARY"] = "binary";
    AssetType["CUBEMAP"] = "cubemap";
    AssetType["UNKNOWN"] = "unknown";
})(AssetType || (exports.AssetType = AssetType = {}));
/**
 * 资源状态
 */
var ResourceState;
(function (ResourceState) {
    /** 未加载 */
    ResourceState["UNLOADED"] = "unloaded";
    /** 加载中 */
    ResourceState["LOADING"] = "loading";
    /** 已加载 */
    ResourceState["LOADED"] = "loaded";
    /** 加载失败 */
    ResourceState["ERROR"] = "error";
})(ResourceState || (exports.ResourceState = ResourceState = {}));
/**
 * 资源管理器
 */
var ResourceManager = /** @class */ (function (_super) {
    __extends(ResourceManager, _super);
    /**
     * 创建资源管理器实例
     * @param options 资源管理器选项
     */
    function ResourceManager(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this) || this;
        /** 资源映射 */
        _this.resources = new Map();
        /** 加载中的资源 */
        _this.loadingResources = new Map();
        /** 当前缓存大小（字节） */
        _this.currentCacheSize = 0;
        /** 当前并发加载数 */
        _this.currentConcurrentLoads = 0;
        /** 加载队列 */
        _this.loadQueue = [];
        /** 清理定时器ID */
        _this.cleanupTimerId = null;
        /** 是否已初始化 */
        _this.initialized = false;
        // 设置选项
        _this.maxCacheSize = options.maxCacheSize || 1024 * 1024 * 100; // 默认100MB
        _this.cleanupThreshold = options.cleanupThreshold || 0.8; // 默认80%
        _this.cleanupInterval = options.cleanupInterval || 60000; // 默认1分钟
        _this.autoCleanup = options.autoCleanup !== undefined ? options.autoCleanup : true;
        _this.enablePreload = options.enablePreload !== undefined ? options.enablePreload : true;
        _this.maxConcurrentLoads = options.maxConcurrentLoads || 6;
        return _this;
    }
    /**
     * 初始化资源管理器
     */
    ResourceManager.prototype.initialize = function () {
        if (this.initialized) {
            return;
        }
        // 如果启用自动清理，则启动清理定时器
        if (this.autoCleanup) {
            this.startCleanupTimer();
        }
        this.initialized = true;
        this.emit('initialized');
    };
    /**
     * 加载资源
     * @param id 资源ID
     * @param type 资源类型
     * @param url 资源URL
     * @returns Promise，解析为资源数据
     */
    ResourceManager.prototype.load = function (id, type, url) {
        return __awaiter(this, void 0, void 0, function () {
            var resource, loadPromise;
            var _this = this;
            return __generator(this, function (_a) {
                // 检查资源是否已存在
                if (this.resources.has(id)) {
                    resource = this.resources.get(id);
                    // 如果已加载，则更新访问时间并返回数据
                    if (resource.state === ResourceState.LOADED) {
                        resource.lastAccessTime = Date.now();
                        resource.refCount++;
                        return [2 /*return*/, resource.data];
                    }
                    // 如果加载失败，则重新加载
                    if (resource.state === ResourceState.ERROR) {
                        resource.state = ResourceState.UNLOADED;
                    }
                    // 如果未加载，则继续加载流程
                }
                // 检查是否已在加载中
                if (this.loadingResources.has(id)) {
                    return [2 /*return*/, this.loadingResources.get(id)];
                }
                loadPromise = new Promise(function (resolve, reject) {
                    // 如果当前并发加载数达到最大值，则加入队列
                    if (_this.currentConcurrentLoads >= _this.maxConcurrentLoads) {
                        _this.loadQueue.push({ id: id, type: type, url: url, resolve: resolve, reject: reject });
                        return;
                    }
                    // 增加并发加载计数
                    _this.currentConcurrentLoads++;
                    // 更新资源状态
                    _this.updateResourceState(id, type, url, ResourceState.LOADING);
                    // 发出加载开始事件
                    _this.emit('loadStart', { id: id, type: type, url: url });
                    // 执行实际加载
                    _this.loadResource(id, type, url)
                        .then(function (data) {
                        // 更新资源状态
                        _this.updateResourceState(id, type, url, ResourceState.LOADED, data);
                        // 发出加载完成事件
                        _this.emit('loadComplete', { id: id, type: type, url: url, data: data });
                        // 减少并发加载计数
                        _this.currentConcurrentLoads--;
                        // 从加载中资源映射中移除
                        _this.loadingResources.delete(id);
                        // 处理队列中的下一个加载请求
                        _this.processNextQueuedLoad();
                        resolve(data);
                    })
                        .catch(function (error) {
                        // 更新资源状态
                        _this.updateResourceState(id, type, url, ResourceState.ERROR, undefined, error);
                        // 发出加载错误事件
                        _this.emit('loadError', { id: id, type: type, url: url, error: error });
                        // 减少并发加载计数
                        _this.currentConcurrentLoads--;
                        // 从加载中资源映射中移除
                        _this.loadingResources.delete(id);
                        // 处理队列中的下一个加载请求
                        _this.processNextQueuedLoad();
                        reject(error);
                    });
                });
                // 添加到加载中资源映射
                this.loadingResources.set(id, loadPromise);
                return [2 /*return*/, loadPromise];
            });
        });
    };
    /**
     * 处理队列中的下一个加载请求
     */
    ResourceManager.prototype.processNextQueuedLoad = function () {
        // 如果队列为空，则返回
        if (this.loadQueue.length === 0) {
            return;
        }
        // 从队列中取出下一个加载请求
        var _a = this.loadQueue.shift(), id = _a.id, type = _a.type, url = _a.url, resolve = _a.resolve, reject = _a.reject;
        // 加载资源
        this.load(id, type, url)
            .then(resolve)
            .catch(reject);
    };
    /**
     * 实际加载资源
     * @param id 资源ID
     * @param type 资源类型
     * @param url 资源URL
     * @returns Promise，解析为资源数据
     */
    ResourceManager.prototype.loadResource = function (_id, type, url) {
        return __awaiter(this, void 0, void 0, function () {
            var response, data, _a, error_1;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 11, , 12]);
                        return [4 /*yield*/, fetch(url)];
                    case 1:
                        response = _b.sent();
                        if (!response.ok) {
                            throw new Error("HTTP error! status: ".concat(response.status));
                        }
                        data = void 0;
                        _a = type;
                        switch (_a) {
                            case AssetType.JSON: return [3 /*break*/, 2];
                            case AssetType.TEXT: return [3 /*break*/, 4];
                            case AssetType.BINARY: return [3 /*break*/, 6];
                        }
                        return [3 /*break*/, 8];
                    case 2: return [4 /*yield*/, response.json()];
                    case 3:
                        data = _b.sent();
                        return [3 /*break*/, 10];
                    case 4: return [4 /*yield*/, response.text()];
                    case 5:
                        data = _b.sent();
                        return [3 /*break*/, 10];
                    case 6: return [4 /*yield*/, response.arrayBuffer()];
                    case 7:
                        data = _b.sent();
                        return [3 /*break*/, 10];
                    case 8: return [4 /*yield*/, response.blob()];
                    case 9:
                        // 对于其他类型，使用blob
                        data = _b.sent();
                        return [3 /*break*/, 10];
                    case 10: return [2 /*return*/, data];
                    case 11:
                        error_1 = _b.sent();
                        throw new Error("\u52A0\u8F7D\u8D44\u6E90\u5931\u8D25: ".concat(error_1.message));
                    case 12: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 更新资源状态
     * @param id 资源ID
     * @param type 资源类型
     * @param url 资源URL
     * @param state 资源状态
     * @param data 资源数据
     * @param error 加载错误
     */
    ResourceManager.prototype.updateResourceState = function (id, type, url, state, data, error) {
        // 获取现有资源或创建新资源
        var resource = this.resources.get(id);
        if (!resource) {
            resource = {
                id: id,
                type: type,
                url: url,
                data: undefined,
                state: ResourceState.UNLOADED,
                refCount: 0,
                lastAccessTime: Date.now(),
                size: 0,
            };
            this.resources.set(id, resource);
        }
        // 更新资源状态
        resource.state = state;
        // 如果提供了数据，则更新数据和大小
        if (data !== undefined) {
            // 如果之前有数据，则减少缓存大小
            if (resource.data) {
                this.currentCacheSize -= resource.size;
            }
            resource.data = data;
            resource.refCount = 1;
            resource.lastAccessTime = Date.now();
            // 计算资源大小
            resource.size = this.calculateResourceSize(data);
            // 增加缓存大小
            this.currentCacheSize += resource.size;
            // 如果超过最大缓存大小，则触发清理
            if (this.currentCacheSize > this.maxCacheSize * this.cleanupThreshold) {
                this.cleanup();
            }
        }
        // 如果提供了错误，则更新错误
        if (error !== undefined) {
            resource.error = error;
        }
    };
    /**
     * 计算资源大小
     * @param data 资源数据
     * @returns 资源大小（字节）
     */
    ResourceManager.prototype.calculateResourceSize = function (data) {
        if (!data) {
            return 0;
        }
        // 根据数据类型计算大小
        if (data instanceof ArrayBuffer) {
            return data.byteLength;
        }
        else if (data instanceof Blob) {
            return data.size;
        }
        else if (typeof data === 'string') {
            return data.length * 2; // 假设UTF-16编码
        }
        else if (data instanceof Object) {
            // 对于对象，使用JSON字符串长度作为估计
            try {
                return JSON.stringify(data).length * 2;
            }
            catch (error) {
                return 1024; // 默认1KB
            }
        }
        return 1024; // 默认1KB
    };
    /**
     * 释放资源
     * @param id 资源ID
     * @returns 是否成功释放
     */
    ResourceManager.prototype.release = function (id) {
        // 检查资源是否存在
        if (!this.resources.has(id)) {
            return false;
        }
        var resource = this.resources.get(id);
        // 减少引用计数
        resource.refCount--;
        // 如果引用计数为0，则考虑释放资源
        if (resource.refCount <= 0) {
            // 如果资源已加载，则减少缓存大小
            if (resource.state === ResourceState.LOADED && resource.data) {
                this.currentCacheSize -= resource.size;
            }
            // 清除资源数据
            resource.data = undefined;
            resource.state = ResourceState.UNLOADED;
            resource.refCount = 0;
            // 发出资源释放事件
            this.emit('resourceReleased', { id: id });
            return true;
        }
        return false;
    };
    /**
     * 获取资源
     * @param id 资源ID
     * @returns 资源信息，如果不存在则返回null
     */
    ResourceManager.prototype.getResource = function (id) {
        var resource = this.resources.get(id);
        if (!resource) {
            return null;
        }
        // 更新访问时间
        resource.lastAccessTime = Date.now();
        return resource;
    };
    /**
     * 获取资源数据
     * @param id 资源ID
     * @returns 资源数据，如果不存在或未加载则返回null
     */
    ResourceManager.prototype.getResourceData = function (id) {
        var resource = this.getResource(id);
        if (!resource || resource.state !== ResourceState.LOADED) {
            return null;
        }
        return resource.data;
    };
    /**
     * 预加载资源
     * @param id 资源ID
     * @param type 资源类型
     * @param url 资源URL
     * @returns Promise，解析为资源数据
     */
    ResourceManager.prototype.preload = function (id, type, url) {
        // 如果未启用预加载，则直接返回
        if (!this.enablePreload) {
            return Promise.resolve(null);
        }
        // 调用加载方法
        return this.load(id, type, url);
    };
    /**
     * 批量预加载资源
     * @param resources 资源数组
     * @param onProgress 进度回调
     * @returns Promise，解析为资源数据映射
     */
    ResourceManager.prototype.preloadBatch = function (resources, onProgress) {
        return __awaiter(this, void 0, void 0, function () {
            var total, loaded, results, _i, resources_1, _a, id, type, url, data, error_2;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        // 如果未启用预加载，则直接返回
                        if (!this.enablePreload) {
                            return [2 /*return*/, Promise.resolve(new Map())];
                        }
                        total = resources.length;
                        loaded = 0;
                        results = new Map();
                        _i = 0, resources_1 = resources;
                        _b.label = 1;
                    case 1:
                        if (!(_i < resources_1.length)) return [3 /*break*/, 7];
                        _a = resources_1[_i], id = _a.id, type = _a.type, url = _a.url;
                        _b.label = 2;
                    case 2:
                        _b.trys.push([2, 4, , 5]);
                        return [4 /*yield*/, this.load(id, type, url)];
                    case 3:
                        data = _b.sent();
                        results.set(id, data);
                        return [3 /*break*/, 5];
                    case 4:
                        error_2 = _b.sent();
                        console.error("\u9884\u52A0\u8F7D\u8D44\u6E90 ".concat(id, " \u5931\u8D25:"), error_2);
                        return [3 /*break*/, 5];
                    case 5:
                        loaded++;
                        if (onProgress) {
                            onProgress(loaded, total);
                        }
                        _b.label = 6;
                    case 6:
                        _i++;
                        return [3 /*break*/, 1];
                    case 7: return [2 /*return*/, results];
                }
            });
        });
    };
    /**
     * 清理缓存
     */
    ResourceManager.prototype.cleanup = function () {
        // 如果缓存大小未超过阈值，则不需要清理
        if (this.currentCacheSize <= this.maxCacheSize * this.cleanupThreshold) {
            return;
        }
        // 获取所有资源
        var resources = Array.from(this.resources.values());
        // 按照最后访问时间排序（最早访问的在前面）
        resources.sort(function (a, b) { return a.lastAccessTime - b.lastAccessTime; });
        // 计算需要释放的空间
        var targetSize = this.maxCacheSize * 0.7; // 释放到70%
        var sizeToFree = this.currentCacheSize - targetSize;
        var freedSize = 0;
        // 释放资源直到达到目标大小
        for (var _i = 0, resources_2 = resources; _i < resources_2.length; _i++) {
            var resource = resources_2[_i];
            // 如果资源未加载或引用计数大于0，则跳过
            if (resource.state !== ResourceState.LOADED || resource.refCount > 0) {
                continue;
            }
            // 释放资源
            this.release(resource.id);
            // 增加已释放大小
            freedSize += resource.size;
            // 如果已释放足够空间，则停止
            if (freedSize >= sizeToFree) {
                break;
            }
        }
        // 发出缓存清理事件
        this.emit('cacheCleanup', { freedSize: freedSize });
    };
    /**
     * 启动清理定时器
     */
    ResourceManager.prototype.startCleanupTimer = function () {
        var _this = this;
        // 如果已有定时器，则先停止
        if (this.cleanupTimerId !== null) {
            this.stopCleanupTimer();
        }
        // 启动新定时器
        this.cleanupTimerId = window.setInterval(function () {
            _this.cleanup();
        }, this.cleanupInterval);
    };
    /**
     * 停止清理定时器
     */
    ResourceManager.prototype.stopCleanupTimer = function () {
        if (this.cleanupTimerId !== null) {
            window.clearInterval(this.cleanupTimerId);
            this.cleanupTimerId = null;
        }
    };
    /**
     * 获取缓存统计信息
     * @returns 缓存统计信息
     */
    ResourceManager.prototype.getCacheStats = function () {
        // 计算已加载资源数量
        var loadedResources = Array.from(this.resources.values()).filter(function (resource) { return resource.state === ResourceState.LOADED; }).length;
        return {
            totalResources: this.resources.size,
            loadedResources: loadedResources,
            currentCacheSize: this.currentCacheSize,
            maxCacheSize: this.maxCacheSize,
            usagePercentage: (this.currentCacheSize / this.maxCacheSize) * 100,
        };
    };
    /**
     * 清空缓存
     */
    ResourceManager.prototype.clearCache = function () {
        // 释放所有资源
        for (var _i = 0, _a = Array.from(this.resources.keys()); _i < _a.length; _i++) {
            var id = _a[_i];
            this.release(id);
        }
        // 清空资源映射
        this.resources.clear();
        // 重置缓存大小
        this.currentCacheSize = 0;
        // 发出缓存清空事件
        this.emit('cacheCleared');
    };
    /**
     * 销毁资源管理器
     */
    ResourceManager.prototype.dispose = function () {
        // 停止清理定时器
        this.stopCleanupTimer();
        // 清空缓存
        this.clearCache();
        // 清空加载队列
        this.loadQueue = [];
        // 清空加载中资源映射
        this.loadingResources.clear();
        // 移除所有事件监听器
        this.removeAllListeners();
        this.initialized = false;
    };
    return ResourceManager;
}(EventEmitter_1.EventEmitter));
exports.ResourceManager = ResourceManager;
