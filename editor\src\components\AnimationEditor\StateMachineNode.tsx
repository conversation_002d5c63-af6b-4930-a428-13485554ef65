/**
 * 状态机节点组件
 * 用于显示和交互状态机中的状态节点
 */
import React, { useState, useRef, useEffect } from 'react';
import { Tooltip } from 'antd';
import { NodePosition } from '../../store/animations/stateMachineSlice';
import './StateMachineEditor.less';

/**
 * 状态机节点属性
 */
interface StateMachineNodeProps {
  /** 节点名称 */
  name: string;
  /** 节点类型 */
  type: string;
  /** 节点位置 */
  position: NodePosition;
  /** 是否选中 */
  selected?: boolean;
  /** 是否为当前状态 */
  isCurrentState?: boolean;
  /** 节点颜色 */
  color?: string;
  /** 点击回调 */
  onClick?: () => void;
  /** 拖动回调 */
  onDrag?: (position: NodePosition) => void;
}

/**
 * 状态机节点组件
 */
const StateMachineNode: React.FC<StateMachineNodeProps> = ({
  name,
  type,
  position,
  selected = false,
  isCurrentState = false,
  color,
  onClick,
  onDrag
}) => {
  // 状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [currentPosition, setCurrentPosition] = useState(position);
  
  // 引用
  const nodeRef = useRef<HTMLDivElement>(null);
  
  // 更新位置
  useEffect(() => {
    setCurrentPosition(position);
  }, [position]);
  
  // 处理鼠标按下
  const handleMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (e.button === 0) { // 左键
      if (nodeRef.current) {
        const rect = nodeRef.current.getBoundingClientRect();
        setDragOffset({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
        setIsDragging(true);
      }
    }
    
    if (onClick) {
      onClick();
    }
  };
  
  // 处理鼠标移动
  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && nodeRef.current) {
      const parentRect = nodeRef.current.parentElement?.getBoundingClientRect();
      
      if (parentRect) {
        const x = e.clientX - parentRect.left - dragOffset.x;
        const y = e.clientY - parentRect.top - dragOffset.y;
        
        setCurrentPosition({ x, y });
      }
    }
  };
  
  // 处理鼠标抬起
  const handleMouseUp = () => {
    if (isDragging && onDrag) {
      onDrag(currentPosition);
    }
    setIsDragging(false);
  };
  
  // 处理鼠标离开
  const handleMouseLeave = () => {
    if (isDragging && onDrag) {
      onDrag(currentPosition);
    }
    setIsDragging(false);
  };
  
  // 设置全局鼠标事件
  useEffect(() => {
    if (isDragging) {
      const handleGlobalMouseMove = (e: MouseEvent) => {
        if (nodeRef.current) {
          const parentRect = nodeRef.current.parentElement?.getBoundingClientRect();
          
          if (parentRect) {
            const x = e.clientX - parentRect.left - dragOffset.x;
            const y = e.clientY - parentRect.top - dragOffset.y;
            
            setCurrentPosition({ x, y });
          }
        }
      };
      
      const handleGlobalMouseUp = () => {
        if (isDragging && onDrag) {
          onDrag(currentPosition);
        }
        setIsDragging(false);
      };
      
      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove);
        document.removeEventListener('mouseup', handleGlobalMouseUp);
      };
    }
  }, [isDragging, dragOffset, onDrag, currentPosition]);
  
  // 获取节点类型图标
  const getTypeIcon = () => {
    switch (type) {
      case 'SingleAnimationState':
        return '▶';
      case 'BlendAnimationState':
        return '⚙';
      default:
        return '●';
    }
  };
  
  // 获取节点样式
  const getNodeStyle = () => {
    let backgroundColor = color || '#1890ff';
    
    if (type === 'SingleAnimationState') {
      backgroundColor = color || '#52c41a';
    } else if (type === 'BlendAnimationState') {
      backgroundColor = color || '#722ed1';
    }
    
    return {
      left: `${currentPosition.x}px`,
      top: `${currentPosition.y}px`,
      backgroundColor: selected ? '#f5222d' : isCurrentState ? '#fa8c16' : backgroundColor,
      borderColor: selected ? '#f5222d' : isCurrentState ? '#fa8c16' : backgroundColor,
      cursor: isDragging ? 'grabbing' : 'grab'
    };
  };
  
  return (
    <Tooltip title={`${name} (${type})`}>
      <div
        ref={nodeRef}
        className={`state-machine-node ${selected ? 'selected' : ''} ${isCurrentState ? 'current' : ''}`}
        style={getNodeStyle()}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
      >
        <div className="node-icon">{getTypeIcon()}</div>
        <div className="node-name">{name}</div>
      </div>
    </Tooltip>
  );
};

export default StateMachineNode;
