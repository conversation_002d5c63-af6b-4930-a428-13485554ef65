/**
 * 资源管理模块
 * 导出所有资源管理相关的类和接口
 */

// 导出原有资源管理系统
// 导出资产管理器
export { AssetManager } from './AssetManager';
export type { AssetInfo, AssetManagerOptions } from './AssetManager';
export { AssetType } from './ResourceManager';

// 导出资产加载器
export { AssetLoader } from './AssetLoader';

// 导出资源管理器
export { ResourceManager } from './ResourceManager';
export type {
  ResourceState,
  ResourceInfo,
  ResourceManagerOptions
} from './ResourceManager';

// 导出资源依赖管理器
export { ResourceDependencyManager } from './ResourceDependencyManager';
export type {
  DependencyType,
  DependencyInfo
} from './ResourceDependencyManager';

// 导出资源预加载器
export { ResourcePreloader } from './ResourcePreloader';
export type {
  PreloadResourceInfo,
  PreloadGroupInfo,
  PreloadProgressInfo,
  ResourcePreloaderOptions
} from './ResourcePreloader';

// 导出增强版资源管理系统
// 导出增强资源加载器
export { EnhancedAssetLoader } from './EnhancedAssetLoader';
export type { LoaderOptions } from './EnhancedAssetLoader';

// 导出增强资源管理器
export { EnhancedResourceManager } from './EnhancedResourceManager';
export type {
  EnhancedResourceManagerOptions,
  ResourceInfo as EnhancedResourceInfo
} from './EnhancedResourceManager';

// 导出增强资源依赖管理器
export { EnhancedResourceDependencyManager } from './EnhancedResourceDependencyManager';
export type {
  DependencyType as EnhancedDependencyType,
  EnhancedResourceDependencyManagerOptions,
  DependencyInfo as EnhancedDependencyInfo
} from './EnhancedResourceDependencyManager';

// 导出增强资源预加载器
export { EnhancedResourcePreloader } from './EnhancedResourcePreloader';
export type {
  EnhancedResourcePreloaderOptions,
  PreloadResourceInfo as EnhancedPreloadResourceInfo,
  PreloadGroupInfo as EnhancedPreloadGroupInfo,
  PreloadProgressInfo as EnhancedPreloadProgressInfo
} from './EnhancedResourcePreloader';

// 导出增强资源管理系统
export { EnhancedResourceSystem } from './EnhancedResourceSystem';
export type { EnhancedResourceSystemOptions } from './EnhancedResourceSystem';

// 导出示例
export { ResourceSystemExample } from './examples/ResourceSystemExample';

/**
 * 创建默认资源管理系统实例
 * @returns 资源管理系统实例
 */
export function createResourceSystem() {
  // 动态导入以避免循环依赖
  const { EnhancedResourceSystem } = require('./EnhancedResourceSystem');
  const resourceSystem = new EnhancedResourceSystem();
  resourceSystem.initialize();
  return resourceSystem;
}
