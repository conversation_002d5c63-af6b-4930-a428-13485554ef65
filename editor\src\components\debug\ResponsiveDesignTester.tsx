/**
 * 响应式设计测试工具
 * 用于测试不同设备和屏幕尺寸下的响应式设计
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Select, Button, Divider, Space, Typography, Row, Col, Slider, Switch, Tag, Tooltip, Alert, Tabs, Table, Input, Radio, Checkbox } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  MobileOutlined,
  TabletOutlined,
  LaptopOutlined,
  DesktopOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  FullscreenOutlined,
  ReloadOutlined,
  EyeOutlined,
  BugOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  SaveOutlined,
  CameraOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  FileImageOutlined
} from '@ant-design/icons';
import MobileDeviceService, { DeviceType, ScreenOrientation } from '../../services/MobileDeviceService';
import ResponsiveDesignService, { ResponsiveBreakpoint } from '../../services/ResponsiveDesignService';
import './ResponsiveDesignTester.less';

const { Title, Text, Paragraph } = Typography;
const { Option, OptGroup } = Select;
const { TabPane } = Tabs;

/**
 * 设备预设
 */
interface DevicePreset {
  name: string;
  width: number;
  height: number;
  deviceType: DeviceType;
  pixelRatio: number;
  userAgent: string;
}

/**
 * 测试结果
 */
interface TestResult {
  id: string;
  name: string;
  status: 'pass' | 'fail' | 'warning';
  description: string;
  details?: string;
}

/**
 * 测试场景
 */
interface TestScenario {
  id: string;
  name: string;
  description: string;
  devicePresets: string[];
  testCases: string[];
}

/**
 * 响应式设计测试工具属性
 */
interface ResponsiveDesignTesterProps {
  /** 是否显示标题 */
  showTitle?: boolean;
}

/**
 * 响应式设计测试工具
 */
const ResponsiveDesignTester: React.FC<ResponsiveDesignTesterProps> = ({
  showTitle = true
}) => {
  const { t } = useTranslation();
  const previewRef = useRef<HTMLDivElement>(null);
  
  // 设备状态
  const [deviceType, setDeviceType] = useState<DeviceType>(DeviceType.DESKTOP);
  const [orientation, setOrientation] = useState<ScreenOrientation>(ScreenOrientation.PORTRAIT);
  const [selectedPreset, setSelectedPreset] = useState<string>('iPhone 13');
  const [customWidth, setCustomWidth] = useState<number>(375);
  const [customHeight, setCustomHeight] = useState<number>(812);
  const [pixelRatio, setPixelRatio] = useState<number>(2);
  const [userAgent, setUserAgent] = useState<string>('Mobile');
  const [isTouchEnabled, setIsTouchEnabled] = useState<boolean>(true);
  
  // 测试状态
  const [isTestMode, setIsTestMode] = useState<boolean>(false);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [testResults, setTestResults] = useState<{[key: string]: boolean}>({});
  const [activeTab, setActiveTab] = useState<string>('device');
  const [testScenarios, setTestScenarios] = useState<TestScenario[]>([]);
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null);
  const [testHistory, setTestHistory] = useState<any[]>([]);
  const [autoTest, setAutoTest] = useState<boolean>(false);
  
  // 设备预设列表
  const devicePresets: DevicePreset[] = [
    // 移动设备
    { name: 'iPhone 13', width: 390, height: 844, deviceType: DeviceType.MOBILE, pixelRatio: 3, userAgent: 'iPhone' },
    { name: 'iPhone SE', width: 375, height: 667, deviceType: DeviceType.MOBILE, pixelRatio: 2, userAgent: 'iPhone' },
    { name: 'Google Pixel 6', width: 393, height: 851, deviceType: DeviceType.MOBILE, pixelRatio: 2.75, userAgent: 'Android' },
    { name: 'Samsung Galaxy S21', width: 360, height: 800, deviceType: DeviceType.MOBILE, pixelRatio: 3, userAgent: 'Android' },
    { name: 'Xiaomi Mi 11', width: 393, height: 873, deviceType: DeviceType.MOBILE, pixelRatio: 2.75, userAgent: 'Android' },
    
    // 平板设备
    { name: 'iPad Air', width: 820, height: 1180, deviceType: DeviceType.TABLET, pixelRatio: 2, userAgent: 'iPad' },
    { name: 'iPad Mini', width: 768, height: 1024, deviceType: DeviceType.TABLET, pixelRatio: 2, userAgent: 'iPad' },
    { name: 'Samsung Galaxy Tab S7', width: 800, height: 1280, deviceType: DeviceType.TABLET, pixelRatio: 2, userAgent: 'Android' },
    
    // 桌面设备
    { name: 'Laptop (1366x768)', width: 1366, height: 768, deviceType: DeviceType.DESKTOP, pixelRatio: 1, userAgent: 'Desktop' },
    { name: 'Desktop (1920x1080)', width: 1920, height: 1080, deviceType: DeviceType.DESKTOP, pixelRatio: 1, userAgent: 'Desktop' },
    { name: '4K Display', width: 3840, height: 2160, deviceType: DeviceType.DESKTOP, pixelRatio: 2, userAgent: 'Desktop' }
  ];
  
  // 初始化
  useEffect(() => {
    // 获取当前设备信息
    const deviceInfo = MobileDeviceService.getDeviceInfo();
    setDeviceType(deviceInfo.type);
    setOrientation(deviceInfo.orientation);
    setIsTouchEnabled(deviceInfo.isTouch);
    
    // 根据当前设备类型选择默认预设
    if (deviceInfo.type === DeviceType.DESKTOP) {
      setSelectedPreset('Laptop (1366x768)');
      setCustomWidth(1366);
      setCustomHeight(768);
      setPixelRatio(1);
      setUserAgent('Desktop');
    } else if (deviceInfo.type === DeviceType.TABLET) {
      setSelectedPreset('iPad Air');
      setCustomWidth(820);
      setCustomHeight(1180);
      setPixelRatio(2);
      setUserAgent('iPad');
    }
    
    // 加载测试场景
    loadTestScenarios();
  }, []);
  
  // 加载测试场景
  const loadTestScenarios = () => {
    // 这里可以从API或本地存储加载测试场景
    // 示例数据
    const scenarios: TestScenario[] = [
      {
        id: 'basic-responsive',
        name: '基础响应式测试',
        description: '测试基本的响应式布局在不同设备上的表现',
        devicePresets: ['iPhone 13', 'iPad Air', 'Laptop (1366x768)', 'Desktop (1920x1080)'],
        testCases: ['breakpoint', 'deviceType', 'orientation', 'touchMode']
      },
      {
        id: 'mobile-specific',
        name: '移动设备特定测试',
        description: '测试移动设备特定的功能和布局',
        devicePresets: ['iPhone 13', 'iPhone SE', 'Google Pixel 6', 'Samsung Galaxy S21'],
        testCases: ['touchMode', 'orientation', 'mobileLayout']
      },
      {
        id: 'tablet-specific',
        name: '平板设备特定测试',
        description: '测试平板设备特定的功能和布局',
        devicePresets: ['iPad Air', 'iPad Mini', 'Samsung Galaxy Tab S7'],
        testCases: ['touchMode', 'orientation', 'tabletLayout']
      }
    ];
    
    setTestScenarios(scenarios);
  };
  
  // 处理预设变更
  const handlePresetChange = (value: string) => {
    setSelectedPreset(value);
    
    const preset = devicePresets.find(p => p.name === value);
    if (preset) {
      setDeviceType(preset.deviceType);
      setCustomWidth(preset.width);
      setCustomHeight(preset.height);
      setPixelRatio(preset.pixelRatio);
      setUserAgent(preset.userAgent);
      setIsTouchEnabled(preset.deviceType !== DeviceType.DESKTOP);
    }
  };
  
  // 切换方向
  const toggleOrientation = () => {
    const newOrientation = orientation === ScreenOrientation.PORTRAIT
      ? ScreenOrientation.LANDSCAPE
      : ScreenOrientation.PORTRAIT;
    setOrientation(newOrientation);
    
    // 交换宽高
    const temp = customWidth;
    setCustomWidth(customHeight);
    setCustomHeight(temp);
  };
  
  // 应用测试设置
  const applyTestSettings = () => {
    // 模拟设备信息
    MobileDeviceService.simulateDevice({
      type: deviceType,
      orientation: orientation,
      width: customWidth,
      height: customHeight,
      pixelRatio: pixelRatio,
      isTouch: isTouchEnabled,
      userAgent: userAgent
    });
    
    // 触发响应式设计服务更新
    ResponsiveDesignService.forceUpdate();
  };
  
  // 重置测试设置
  const resetTestSettings = () => {
    MobileDeviceService.resetSimulation();
    ResponsiveDesignService.forceUpdate();
  };
  
  // 运行测试
  const runTests = () => {
    setIsTestMode(true);
    
    // 初始化测试结果
    const results: {[key: string]: boolean} = {};
    
    // 测试响应式断点
    const breakpoint = ResponsiveDesignService.getCurrentBreakpoint();
    const expectedBreakpoint = getExpectedBreakpoint(customWidth);
    results['breakpoint'] = breakpoint === expectedBreakpoint;
    
    // 测试设备类型检测
    const detectedDeviceType = MobileDeviceService.getDeviceInfo().type;
    results['deviceType'] = detectedDeviceType === deviceType;
    
    // 测试屏幕方向检测
    const detectedOrientation = MobileDeviceService.getDeviceInfo().orientation;
    results['orientation'] = detectedOrientation === orientation;
    
    // 测试触控模式
    const touchMode = ResponsiveDesignService.isTouchMode();
    results['touchMode'] = touchMode === isTouchEnabled;
    
    // 更新测试结果
    setTestResults(results);
    
    // 添加到测试历史
    addTestToHistory(results);
  };
  
  // 添加测试到历史记录
  const addTestToHistory = (results: {[key: string]: boolean}) => {
    const newHistory = [...testHistory];
    newHistory.push({
      timestamp: new Date().toISOString(),
      device: selectedPreset,
      width: customWidth,
      height: customHeight,
      orientation: orientation,
      results: { ...results }
    });
    setTestHistory(newHistory);
  };
  
  // 获取预期的响应式断点
  const getExpectedBreakpoint = (width: number): ResponsiveBreakpoint => {
    if (width < 480) return ResponsiveBreakpoint.XS;
    if (width < 576) return ResponsiveBreakpoint.XS;
    if (width < 768) return ResponsiveBreakpoint.SM;
    if (width < 992) return ResponsiveBreakpoint.MD;
    if (width < 1200) return ResponsiveBreakpoint.LG;
    if (width < 1600) return ResponsiveBreakpoint.XL;
    return ResponsiveBreakpoint.XXL;
  };
  
  // 运行自动测试
  const runAutoTest = () => {
    if (!selectedScenario) return;
    
    const scenario = testScenarios.find(s => s.id === selectedScenario);
    if (!scenario) return;
    
    // 开始自动测试
    setAutoTest(true);
    
    // 清空测试历史
    setTestHistory([]);
    
    // 对每个设备预设运行测试
    scenario.devicePresets.forEach((presetName, index) => {
      setTimeout(() => {
        handlePresetChange(presetName);
        setTimeout(() => {
          applyTestSettings();
          setTimeout(() => {
            runTests();
            
            // 最后一个测试完成后
            if (index === scenario.devicePresets.length - 1) {
              setAutoTest(false);
            }
          }, 500);
        }, 500);
      }, index * 2000); // 每个设备间隔2秒
    });
  };
  
  // 捕获屏幕截图
  const captureScreenshot = () => {
    if (!previewRef.current) return;
    
    // 这里可以使用html2canvas等库捕获截图
    // 示例代码，实际需要引入html2canvas库
    // html2canvas(previewRef.current).then(canvas => {
    //   const imgData = canvas.toDataURL('image/png');
    //   const link = document.createElement('a');
    //   link.download = `responsive-test-${selectedPreset}-${customWidth}x${customHeight}.png`;
    //   link.href = imgData;
    //   link.click();
    // });
    
    // 临时提示
    alert('截图功能需要引入html2canvas库');
  };
  
  // 渲染设备图标
  const renderDeviceIcon = (type: DeviceType) => {
    switch (type) {
      case DeviceType.MOBILE:
        return <MobileOutlined />;
      case DeviceType.TABLET:
        return <TabletOutlined />;
      case DeviceType.DESKTOP:
        return <LaptopOutlined />;
      default:
        return <DesktopOutlined />;
    }
  };
  
  // 渲染测试结果
  const renderTestResults = () => {
    if (!isTestMode) return null;
    
    return (
      <div className="test-results">
        <Divider>{t('deviceTesting.testResults')}</Divider>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Card size="small" title={t('deviceTesting.breakpointTest')}>
              <Space direction="vertical">
                <div>
                  {t('deviceTesting.expected')}: {getExpectedBreakpoint(customWidth)}
                </div>
                <div>
                  {t('deviceTesting.actual')}: {ResponsiveDesignService.getCurrentBreakpoint()}
                </div>
                <div>
                  {testResults['breakpoint'] ? (
                    <Tag color="success" icon={<CheckCircleOutlined />}>{t('deviceTesting.pass')}</Tag>
                  ) : (
                    <Tag color="error" icon={<CloseCircleOutlined />}>{t('deviceTesting.fail')}</Tag>
                  )}
                </div>
              </Space>
            </Card>
          </Col>
          <Col span={12}>
            <Card size="small" title={t('deviceTesting.deviceTypeTest')}>
              <Space direction="vertical">
                <div>
                  {t('deviceTesting.expected')}: {DeviceType[deviceType]}
                </div>
                <div>
                  {t('deviceTesting.actual')}: {DeviceType[MobileDeviceService.getDeviceInfo().type]}
                </div>
                <div>
                  {testResults['deviceType'] ? (
                    <Tag color="success" icon={<CheckCircleOutlined />}>{t('deviceTesting.pass')}</Tag>
                  ) : (
                    <Tag color="error" icon={<CloseCircleOutlined />}>{t('deviceTesting.fail')}</Tag>
                  )}
                </div>
              </Space>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };
  
  return (
    <div className="responsive-design-tester">
      {showTitle && <Title level={4}>{t('deviceTesting.title')}</Title>}
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={t('deviceTesting.deviceTab')} key="device">
          <Card>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div className="setting-label">{t('deviceTesting.devicePreset')}</div>
                <Select
                  style={{ width: '100%' }}
                  value={selectedPreset}
                  onChange={handlePresetChange}
                >
                  <OptGroup label={t('deviceTesting.mobileDevices')}>
                    {devicePresets.filter(p => p.deviceType === DeviceType.MOBILE).map(preset => (
                      <Option key={preset.name} value={preset.name}>
                        <Space>
                          <MobileOutlined />
                          {preset.name} ({preset.width}x{preset.height})
                        </Space>
                      </Option>
                    ))}
                  </OptGroup>
                  <OptGroup label={t('deviceTesting.tabletDevices')}>
                    {devicePresets.filter(p => p.deviceType === DeviceType.TABLET).map(preset => (
                      <Option key={preset.name} value={preset.name}>
                        <Space>
                          <TabletOutlined />
                          {preset.name} ({preset.width}x{preset.height})
                        </Space>
                      </Option>
                    ))}
                  </OptGroup>
                  <OptGroup label={t('deviceTesting.desktopDevices')}>
                    {devicePresets.filter(p => p.deviceType === DeviceType.DESKTOP).map(preset => (
                      <Option key={preset.name} value={preset.name}>
                        <Space>
                          <LaptopOutlined />
                          {preset.name} ({preset.width}x{preset.height})
                        </Space>
                      </Option>
                    ))}
                  </OptGroup>
                </Select>
              </Col>
              
              <Col span={12}>
                <div className="setting-label">{t('deviceTesting.deviceType')}</div>
                <Radio.Group value={deviceType} onChange={e => setDeviceType(e.target.value)}>
                  <Radio.Button value={DeviceType.MOBILE}>
                    <MobileOutlined /> {t('deviceTesting.mobile')}
                  </Radio.Button>
                  <Radio.Button value={DeviceType.TABLET}>
                    <TabletOutlined /> {t('deviceTesting.tablet')}
                  </Radio.Button>
                  <Radio.Button value={DeviceType.DESKTOP}>
                    <LaptopOutlined /> {t('deviceTesting.desktop')}
                  </Radio.Button>
                </Radio.Group>
              </Col>
              
              <Col span={12}>
                <div className="setting-label">{t('deviceTesting.screenWidth')}</div>
                <Slider
                  min={320}
                  max={3840}
                  value={customWidth}
                  onChange={setCustomWidth}
                  marks={{
                    320: '320px',
                    768: '768px',
                    1366: '1366px',
                    1920: '1920px'
                  }}
                />
              </Col>
              
              <Col span={12}>
                <div className="setting-label">{t('deviceTesting.screenHeight')}</div>
                <Slider
                  min={240}
                  max={2160}
                  value={customHeight}
                  onChange={setCustomHeight}
                  marks={{
                    240: '240px',
                    600: '600px',
                    768: '768px',
                    1080: '1080px'
                  }}
                />
              </Col>
              
              <Col span={12}>
                <div className="setting-label">{t('deviceTesting.pixelRatio')}</div>
                <Slider
                  min={1}
                  max={4}
                  step={0.25}
                  value={pixelRatio}
                  onChange={setPixelRatio}
                  marks={{
                    1: '1x',
                    2: '2x',
                    3: '3x',
                    4: '4x'
                  }}
                />
              </Col>
              
              <Col span={12}>
                <div className="setting-label">{t('deviceTesting.orientation')}</div>
                <Button onClick={toggleOrientation} icon={orientation === ScreenOrientation.PORTRAIT ? <RotateRightOutlined /> : <RotateLeftOutlined />}>
                  {orientation === ScreenOrientation.PORTRAIT ? t('deviceTesting.switchToLandscape') : t('deviceTesting.switchToPortrait')}
                </Button>
              </Col>
              
              <Col span={12}>
                <div className="setting-label">{t('deviceTesting.touchEnabled')}</div>
                <Switch
                  checked={isTouchEnabled}
                  onChange={setIsTouchEnabled}
                  checkedChildren={t('deviceTesting.enabled')}
                  unCheckedChildren={t('deviceTesting.disabled')}
                />
              </Col>
              
              <Col span={12}>
                <div className="setting-label">{t('deviceTesting.userAgent')}</div>
                <Input value={userAgent} onChange={e => setUserAgent(e.target.value)} />
              </Col>
            </Row>
            
            <Divider />
            
            <Space>
              <Button type="primary" onClick={applyTestSettings}>
                {t('deviceTesting.applySettings')}
              </Button>
              <Button onClick={resetTestSettings}>
                {t('deviceTesting.resetSettings')}
              </Button>
              <Button onClick={runTests} icon={<BugOutlined />}>
                {t('deviceTesting.runTests')}
              </Button>
              <Button onClick={captureScreenshot} icon={<CameraOutlined />}>
                {t('deviceTesting.captureScreenshot')}
              </Button>
            </Space>
            
            {renderTestResults()}
            
            <Divider />
            
            <div className="device-preview" ref={previewRef}>
              <div className="device-frame" style={{
                width: Math.min(customWidth / 4, 200),
                height: Math.min(customHeight / 4, 300),
                transform: orientation === ScreenOrientation.LANDSCAPE ? 'rotate(90deg)' : 'none'
              }}>
                <div className="device-screen">
                  {renderDeviceIcon(deviceType)}
                  <div className="device-info">
                    <div>{selectedPreset}</div>
                    <div>{customWidth}x{customHeight}</div>
                    <div>{pixelRatio}x</div>
                  </div>
                </div>
              </div>
            </div>
            
            <Paragraph className="help-text">
              <InfoCircleOutlined /> {t('deviceTesting.helpText')}
            </Paragraph>
          </Card>
        </TabPane>
        
        <TabPane tab={t('deviceTesting.testScenariosTab')} key="scenarios">
          <Card>
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <div className="setting-label">{t('deviceTesting.selectTestScenario')}</div>
                <Select
                  style={{ width: '100%' }}
                  value={selectedScenario}
                  onChange={setSelectedScenario}
                  placeholder={t('deviceTesting.selectScenarioPlaceholder')}
                >
                  {testScenarios.map(scenario => (
                    <Option key={scenario.id} value={scenario.id}>
                      {scenario.name}
                    </Option>
                  ))}
                </Select>
                
                {selectedScenario && (
                  <div className="scenario-description">
                    {testScenarios.find(s => s.id === selectedScenario)?.description}
                  </div>
                )}
              </Col>
              
              <Col span={24}>
                <Space>
                  <Button
                    type="primary"
                    onClick={runAutoTest}
                    disabled={!selectedScenario || autoTest}
                    icon={autoTest ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                  >
                    {autoTest ? t('deviceTesting.testingInProgress') : t('deviceTesting.runAutoTest')}
                  </Button>
                </Space>
              </Col>
            </Row>
            
            {testHistory.length > 0 && (
              <>
                <Divider>{t('deviceTesting.testHistory')}</Divider>
                <Table
                  dataSource={testHistory.map((item, index) => ({ ...item, key: index }))}
                  columns={[
                    {
                      title: t('deviceTesting.device'),
                      dataIndex: 'device',
                      key: 'device'
                    },
                    {
                      title: t('deviceTesting.dimensions'),
                      key: 'dimensions',
                      render: (_, record) => `${record.width}x${record.height}`
                    },
                    {
                      title: t('deviceTesting.orientation'),
                      dataIndex: 'orientation',
                      key: 'orientation',
                      render: (orientation) => ScreenOrientation[orientation]
                    },
                    {
                      title: t('deviceTesting.result'),
                      key: 'result',
                      render: (_, record) => {
                        const passCount = Object.values(record.results).filter(Boolean).length;
                        const totalCount = Object.values(record.results).length;
                        const passRate = Math.round((passCount / totalCount) * 100);
                        
                        let color = 'success';
                        let icon = <CheckCircleOutlined />;
                        
                        if (passRate < 70) {
                          color = 'error';
                          icon = <CloseCircleOutlined />;
                        } else if (passRate < 100) {
                          color = 'warning';
                          icon = <WarningOutlined />;
                        }
                        
                        return (
                          <Tag color={color} icon={icon}>
                            {passCount}/{totalCount} ({passRate}%)
                          </Tag>
                        );
                      }
                    }
                  ]}
                  pagination={false}
                  size="small"
                />
              </>
            )}
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default ResponsiveDesignTester;
