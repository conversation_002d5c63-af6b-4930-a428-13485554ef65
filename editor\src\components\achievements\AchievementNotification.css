/**
 * 成就通知样式
 */

.achievement-notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  width: 350px;
  max-width: 90vw;
  animation: slide-in 0.5s ease-out;
}

@keyframes slide-in {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.achievement-card {
  background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
  border: 1px solid #444;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  overflow: hidden;
}

.achievement-content {
  width: 100%;
}

.achievement-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.achievement-icon {
  font-size: 24px;
  margin-right: 10px;
}

.achievement-icon.trophy {
  color: gold;
}

.achievement-icon.star {
  color: #ff9500;
}

.achievement-icon.check {
  color: #52c41a;
}

.achievement-title {
  margin: 0 !important;
  color: #f0f0f0 !important;
}

.achievement-name {
  margin: 0 0 8px 0 !important;
  color: #fff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.achievement-description {
  color: #d9d9d9;
  margin-bottom: 12px;
  display: block;
}

.achievement-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.difficulty-badge {
  display: flex;
  align-items: center;
}

.difficulty-text {
  color: #d9d9d9;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 1px;
}

.achievement-pagination {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  width: 100px;
}

.achievement-count {
  color: #d9d9d9;
  font-size: 12px;
  margin-bottom: 4px;
}

/* 响应式调整 */
@media (max-width: 576px) {
  .achievement-notification-container {
    width: calc(100% - 40px);
    right: 10px;
    top: 10px;
  }
  
  .achievement-name {
    font-size: 18px !important;
  }
  
  .achievement-description {
    font-size: 12px;
  }
}
