.inspector-panel {
  height: 100%;
  overflow-y: auto;
  padding: 8px;
  
  &.inspector-panel-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    
    .empty-message {
      color: #999;
      font-size: 14px;
    }
  }
  
  .entity-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .ant-input {
      flex: 1;
      margin-right: 8px;
    }
    
    .entity-controls {
      display: flex;
      align-items: center;
      
      .ant-switch {
        margin-right: 8px;
      }
    }
  }
  
  .form-row {
    display: flex;
    gap: 8px;
  }
  
  .component-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  
  .component-content {
    padding: 8px 0;
    
    .property-group {
      margin-bottom: 8px;
      
      .property-label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        font-size: 12px;
        color: #666;
      }
      
      .property-fields {
        display: flex;
        gap: 4px;
        
        .ant-input-number {
          width: 100%;
        }
      }
    }
  }
  
  .ant-collapse {
    background-color: transparent;
    border: none;
    
    .ant-collapse-item {
      border-color: #f0f0f0;
      
      .ant-collapse-header {
        padding: 8px 12px;
        
        .ant-collapse-arrow {
          right: 8px;
        }
      }
      
      .ant-collapse-content {
        border-color: #f0f0f0;
        
        .ant-collapse-content-box {
          padding: 8px 12px;
        }
      }
    }
  }
  
  .ant-tabs {
    .ant-tabs-nav {
      margin-bottom: 8px;
    }
  }
}
