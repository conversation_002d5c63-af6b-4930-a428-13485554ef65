/**
 * 中文BERT情感分析模型
 * 专门针对中文文本的情感分析
 */
import { EmotionAnalysisResult } from './EmotionBasedAnimationGenerator';
import { BERTEmotionModel, EmotionAnalysisRequest } from './BERTEmotionModel';

/**
 * 中文分词器类型
 */
export enum ChineseTokenizerType {
  /** 简单分词 */
  SIMPLE = 'simple',
  /** 基于词典的分词 */
  DICTIONARY = 'dictionary',
  /** 基于规则的分词 */
  RULE = 'rule',
  /** 基于统计的分词 */
  STATISTICAL = 'statistical',
  /** 基于深度学习的分词 */
  DEEP_LEARNING = 'deep_learning'
}

/**
 * 中文方言类型
 */
export enum ChineseDialectType {
  /** 普通话 */
  MANDARIN = 'mandarin',
  /** 粤语 */
  CANTONESE = 'cantonese',
  /** 闽南语 */
  MIN_NAN = 'min_nan',
  /** 上海话 */
  SHANGHAINESE = 'shanghainese',
  /** 四川话 */
  SICHUANESE = 'sichuanese'
}

/**
 * 中文BERT情感分析模型配置
 */
export interface ChineseBERTModelConfig {
  /** 是否使用远程API */
  useRemoteAPI?: boolean;
  /** 远程API URL */
  remoteAPIUrl?: string;
  /** API密钥 */
  apiKey?: string;
  /** 模型路径 */
  modelPath?: string;
  /** 是否使用GPU */
  useGPU?: boolean;
  /** 是否使用缓存 */
  useCache?: boolean;
  /** 缓存大小 */
  cacheSize?: number;
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否使用量化模型 */
  useQuantized?: boolean;
  /** 量化位数 */
  quantizationBits?: 8 | 16 | 32;
  /** 批处理大小 */
  batchSize?: number;
  /** 模型变体 */
  modelVariant?: 'base' | 'large' | 'distilled';
  /** 情感类别 */
  emotionCategories?: string[];
  /** 是否使用多标签分类 */
  useMultiLabel?: boolean;
  /** 置信度阈值 */
  confidenceThreshold?: number;
  /** 是否使用中文分词 */
  useChineseTokenizer?: boolean;
  /** 分词器路径 */
  tokenizerPath?: string;
  /** 分词器类型 */
  tokenizerType?: ChineseTokenizerType;
  /** 是否使用情感词典增强 */
  useDictionaryEnhancement?: boolean;
  /** 情感词典路径 */
  emotionDictionaryPath?: string;
  /** 方言类型 */
  dialectType?: ChineseDialectType;
  /** 是否使用上下文分析 */
  useContextAnalysis?: boolean;
  /** 上下文窗口大小 */
  contextWindowSize?: number;
}

/**
 * 中文BERT情感分析模型
 * 专门针对中文文本的情感分析
 */
export class ChineseBERTEmotionModel extends BERTEmotionModel {
  /** 默认中文情感类别 */
  static readonly DEFAULT_CHINESE_EMOTION_CATEGORIES = [
    '高兴', '悲伤', '愤怒', '惊讶', '恐惧', '厌恶', '鄙视', '中性',
    '期待', '信任', '喜爱', '自豪', '满足', '感激', '羞愧', '内疚',
    '嫉妒', '失望', '无聊', '困惑', '怀疑', '焦虑', '尴尬', '疲倦'
  ];



  /** 英文情感映射到中文 */
  private englishToChineseEmotionMap: Map<string, string> = new Map([
    ['happy', '高兴'],
    ['sad', '悲伤'],
    ['angry', '愤怒'],
    ['surprised', '惊讶'],
    ['fearful', '恐惧'],
    ['disgusted', '厌恶'],
    ['contempt', '鄙视'],
    ['neutral', '中性'],
    ['anticipation', '期待'],
    ['trust', '信任'],
    ['love', '喜爱'],
    ['pride', '自豪'],
    ['satisfaction', '满足'],
    ['gratitude', '感激'],
    ['shame', '羞愧'],
    ['guilt', '内疚'],
    ['jealousy', '嫉妒'],
    ['disappointment', '失望'],
    ['boredom', '无聊'],
    ['confusion', '困惑'],
    ['doubt', '怀疑'],
    ['anxiety', '焦虑'],
    ['embarrassment', '尴尬'],
    ['fatigue', '疲倦']
  ]);

  /** 中文情感词典 */
  private chineseEmotionDictionary: Map<string, { emotion: string, score: number }> = new Map();

  /** 中文分词器 */
  private tokenizer: any = null;

  /** 方言处理器 */
  private dialectProcessor: any = null;

  /** 上下文历史 */
  private contextHistory: string[] = [];

  /** 上下文窗口大小 */
  private contextWindowSize: number = 5;

  /** 中文BERT配置 */
  private chineseConfig: ChineseBERTModelConfig;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: ChineseBERTModelConfig = {}) {
    // 设置默认中文情感类别
    const mergedConfig = {
      ...config,
      emotionCategories: config.emotionCategories || ChineseBERTEmotionModel.DEFAULT_CHINESE_EMOTION_CATEGORIES,
      modelPath: config.modelPath || 'models/chinese-bert-emotion',
      useChineseTokenizer: config.useChineseTokenizer !== undefined ? config.useChineseTokenizer : true,
      tokenizerPath: config.tokenizerPath || 'models/chinese-bert-emotion/tokenizer',
      tokenizerType: config.tokenizerType || ChineseTokenizerType.DICTIONARY,
      useDictionaryEnhancement: config.useDictionaryEnhancement !== undefined ? config.useDictionaryEnhancement : true,
      emotionDictionaryPath: config.emotionDictionaryPath || 'models/chinese-bert-emotion/dictionary',
      dialectType: config.dialectType || ChineseDialectType.MANDARIN,
      useContextAnalysis: config.useContextAnalysis !== undefined ? config.useContextAnalysis : false,
      contextWindowSize: config.contextWindowSize || 5
    };

    super(mergedConfig);

    // 保存中文配置
    this.chineseConfig = mergedConfig;

    // 设置上下文窗口大小
    this.contextWindowSize = mergedConfig.contextWindowSize || 5;

    // 初始化中文情感词典
    this.initChineseEmotionDictionary();

    // 初始化分词器
    if (mergedConfig.useChineseTokenizer) {
      this.initTokenizer(mergedConfig.tokenizerType!, mergedConfig.tokenizerPath!);
    }

    // 初始化方言处理器
    if (mergedConfig.dialectType !== ChineseDialectType.MANDARIN) {
      this.initDialectProcessor(mergedConfig.dialectType!);
    }
  }

  /**
   * 初始化分词器
   * @param type 分词器类型
   * @param path 分词器路径
   */
  private async initTokenizer(type: ChineseTokenizerType, _path: string): Promise<void> {
    try {
      switch (type) {
        case ChineseTokenizerType.SIMPLE:
          // 简单分词不需要额外初始化
          if (this.chineseConfig.debug) {
            console.log('使用简单中文分词器');
          }
          break;

        case ChineseTokenizerType.DICTIONARY:
          // 加载词典分词器
          if (this.chineseConfig.debug) {
            console.log('加载基于词典的中文分词器');
          }
          // TODO: 实现词典分词器加载
          break;

        case ChineseTokenizerType.RULE:
          // 加载规则分词器
          if (this.chineseConfig.debug) {
            console.log('加载基于规则的中文分词器');
          }
          // TODO: 实现规则分词器加载
          break;

        case ChineseTokenizerType.STATISTICAL:
          // 加载统计分词器
          if (this.chineseConfig.debug) {
            console.log('加载基于统计的中文分词器');
          }
          // TODO: 实现统计分词器加载
          break;

        case ChineseTokenizerType.DEEP_LEARNING:
          // 加载深度学习分词器
          if (this.chineseConfig.debug) {
            console.log('加载基于深度学习的中文分词器');
          }
          // TODO: 实现深度学习分词器加载
          break;

        default:
          if (this.chineseConfig.debug) {
            console.warn(`未知的分词器类型: ${type}，使用简单分词器`);
          }
      }
    } catch (error) {
      console.error('初始化中文分词器失败:', error);
      if (this.chineseConfig.debug) {
        console.warn('将使用简单分词器作为后备');
      }
    }
  }

  /**
   * 初始化方言处理器
   * @param dialectType 方言类型
   */
  private initDialectProcessor(dialectType: ChineseDialectType): void {
    try {
      if (this.chineseConfig.debug) {
        console.log(`初始化${dialectType}方言处理器`);
      }

      // TODO: 实现方言处理器初始化

    } catch (error) {
      console.error('初始化方言处理器失败:', error);
    }
  }

  /**
   * 初始化中文情感词典
   */
  private initChineseEmotionDictionary(): void {
    // 高兴
    this.chineseEmotionDictionary.set('开心', { emotion: '高兴', score: 0.8 });
    this.chineseEmotionDictionary.set('高兴', { emotion: '高兴', score: 0.9 });
    this.chineseEmotionDictionary.set('快乐', { emotion: '高兴', score: 0.8 });
    this.chineseEmotionDictionary.set('喜悦', { emotion: '高兴', score: 0.7 });
    this.chineseEmotionDictionary.set('欢喜', { emotion: '高兴', score: 0.7 });
    this.chineseEmotionDictionary.set('欢乐', { emotion: '高兴', score: 0.7 });
    this.chineseEmotionDictionary.set('愉快', { emotion: '高兴', score: 0.6 });
    this.chineseEmotionDictionary.set('兴奋', { emotion: '高兴', score: 0.6 });
    this.chineseEmotionDictionary.set('欣喜', { emotion: '高兴', score: 0.7 });
    this.chineseEmotionDictionary.set('欢欣', { emotion: '高兴', score: 0.7 });
    this.chineseEmotionDictionary.set('雀跃', { emotion: '高兴', score: 0.8 });
    this.chineseEmotionDictionary.set('欢快', { emotion: '高兴', score: 0.7 });
    this.chineseEmotionDictionary.set('欢畅', { emotion: '高兴', score: 0.7 });
    this.chineseEmotionDictionary.set('喜笑颜开', { emotion: '高兴', score: 0.9 });
    this.chineseEmotionDictionary.set('眉开眼笑', { emotion: '高兴', score: 0.8 });
    this.chineseEmotionDictionary.set('笑逐颜开', { emotion: '高兴', score: 0.8 });

    // 悲伤
    this.chineseEmotionDictionary.set('悲伤', { emotion: '悲伤', score: 0.9 });
    this.chineseEmotionDictionary.set('难过', { emotion: '悲伤', score: 0.8 });
    this.chineseEmotionDictionary.set('伤心', { emotion: '悲伤', score: 0.8 });
    this.chineseEmotionDictionary.set('忧伤', { emotion: '悲伤', score: 0.7 });
    this.chineseEmotionDictionary.set('忧郁', { emotion: '悲伤', score: 0.7 });
    this.chineseEmotionDictionary.set('哀伤', { emotion: '悲伤', score: 0.7 });
    this.chineseEmotionDictionary.set('痛苦', { emotion: '悲伤', score: 0.6 });
    this.chineseEmotionDictionary.set('悲痛', { emotion: '悲伤', score: 0.8 });
    this.chineseEmotionDictionary.set('悲哀', { emotion: '悲伤', score: 0.8 });
    this.chineseEmotionDictionary.set('悲凉', { emotion: '悲伤', score: 0.7 });
    this.chineseEmotionDictionary.set('凄凉', { emotion: '悲伤', score: 0.7 });
    this.chineseEmotionDictionary.set('凄惨', { emotion: '悲伤', score: 0.8 });
    this.chineseEmotionDictionary.set('黯然', { emotion: '悲伤', score: 0.6 });
    this.chineseEmotionDictionary.set('心酸', { emotion: '悲伤', score: 0.7 });
    this.chineseEmotionDictionary.set('心碎', { emotion: '悲伤', score: 0.8 });
    this.chineseEmotionDictionary.set('泪流满面', { emotion: '悲伤', score: 0.9 });

    // 愤怒
    this.chineseEmotionDictionary.set('愤怒', { emotion: '愤怒', score: 0.9 });
    this.chineseEmotionDictionary.set('生气', { emotion: '愤怒', score: 0.8 });
    this.chineseEmotionDictionary.set('恼火', { emotion: '愤怒', score: 0.7 });
    this.chineseEmotionDictionary.set('暴怒', { emotion: '愤怒', score: 0.9 });
    this.chineseEmotionDictionary.set('气愤', { emotion: '愤怒', score: 0.8 });
    this.chineseEmotionDictionary.set('恼怒', { emotion: '愤怒', score: 0.7 });
    this.chineseEmotionDictionary.set('发火', { emotion: '愤怒', score: 0.8 });
    this.chineseEmotionDictionary.set('火冒三丈', { emotion: '愤怒', score: 0.9 });
    this.chineseEmotionDictionary.set('大发雷霆', { emotion: '愤怒', score: 0.9 });
    this.chineseEmotionDictionary.set('怒不可遏', { emotion: '愤怒', score: 0.9 });
    this.chineseEmotionDictionary.set('勃然大怒', { emotion: '愤怒', score: 0.9 });
    this.chineseEmotionDictionary.set('怒气冲冲', { emotion: '愤怒', score: 0.8 });
    this.chineseEmotionDictionary.set('气急败坏', { emotion: '愤怒', score: 0.8 });
    this.chineseEmotionDictionary.set('怒火中烧', { emotion: '愤怒', score: 0.8 });

    // 惊讶
    this.chineseEmotionDictionary.set('惊讶', { emotion: '惊讶', score: 0.9 });
    this.chineseEmotionDictionary.set('吃惊', { emotion: '惊讶', score: 0.8 });
    this.chineseEmotionDictionary.set('震惊', { emotion: '惊讶', score: 0.8 });
    this.chineseEmotionDictionary.set('惊奇', { emotion: '惊讶', score: 0.7 });
    this.chineseEmotionDictionary.set('惊异', { emotion: '惊讶', score: 0.7 });
    this.chineseEmotionDictionary.set('惊愕', { emotion: '惊讶', score: 0.8 });
    this.chineseEmotionDictionary.set('愕然', { emotion: '惊讶', score: 0.7 });
    this.chineseEmotionDictionary.set('目瞪口呆', { emotion: '惊讶', score: 0.9 });
    this.chineseEmotionDictionary.set('瞠目结舌', { emotion: '惊讶', score: 0.9 });
    this.chineseEmotionDictionary.set('大吃一惊', { emotion: '惊讶', score: 0.8 });
    this.chineseEmotionDictionary.set('出乎意料', { emotion: '惊讶', score: 0.7 });
    this.chineseEmotionDictionary.set('始料不及', { emotion: '惊讶', score: 0.7 });

    // 恐惧
    this.chineseEmotionDictionary.set('恐惧', { emotion: '恐惧', score: 0.9 });
    this.chineseEmotionDictionary.set('害怕', { emotion: '恐惧', score: 0.8 });
    this.chineseEmotionDictionary.set('惊恐', { emotion: '恐惧', score: 0.8 });
    this.chineseEmotionDictionary.set('惊吓', { emotion: '恐惧', score: 0.7 });
    this.chineseEmotionDictionary.set('畏惧', { emotion: '恐惧', score: 0.7 });
    this.chineseEmotionDictionary.set('恐慌', { emotion: '恐惧', score: 0.8 });
    this.chineseEmotionDictionary.set('惶恐', { emotion: '恐惧', score: 0.7 });
    this.chineseEmotionDictionary.set('惊骇', { emotion: '恐惧', score: 0.8 });
    this.chineseEmotionDictionary.set('胆战心惊', { emotion: '恐惧', score: 0.9 });
    this.chineseEmotionDictionary.set('心惊肉跳', { emotion: '恐惧', score: 0.8 });
    this.chineseEmotionDictionary.set('毛骨悚然', { emotion: '恐惧', score: 0.9 });
    this.chineseEmotionDictionary.set('不寒而栗', { emotion: '恐惧', score: 0.8 });

    // 厌恶
    this.chineseEmotionDictionary.set('厌恶', { emotion: '厌恶', score: 0.9 });
    this.chineseEmotionDictionary.set('讨厌', { emotion: '厌恶', score: 0.8 });
    this.chineseEmotionDictionary.set('恶心', { emotion: '厌恶', score: 0.8 });
    this.chineseEmotionDictionary.set('反感', { emotion: '厌恶', score: 0.7 });
    this.chineseEmotionDictionary.set('嫌弃', { emotion: '厌恶', score: 0.7 });
    this.chineseEmotionDictionary.set('憎恶', { emotion: '厌恶', score: 0.8 });
    this.chineseEmotionDictionary.set('厌烦', { emotion: '厌恶', score: 0.7 });
    this.chineseEmotionDictionary.set('作呕', { emotion: '厌恶', score: 0.8 });
    this.chineseEmotionDictionary.set('深恶痛绝', { emotion: '厌恶', score: 0.9 });
    this.chineseEmotionDictionary.set('嗤之以鼻', { emotion: '厌恶', score: 0.8 });
    this.chineseEmotionDictionary.set('避之不及', { emotion: '厌恶', score: 0.7 });

    // 鄙视
    this.chineseEmotionDictionary.set('鄙视', { emotion: '鄙视', score: 0.9 });
    this.chineseEmotionDictionary.set('轻蔑', { emotion: '鄙视', score: 0.8 });
    this.chineseEmotionDictionary.set('蔑视', { emotion: '鄙视', score: 0.8 });
    this.chineseEmotionDictionary.set('不屑', { emotion: '鄙视', score: 0.7 });
    this.chineseEmotionDictionary.set('藐视', { emotion: '鄙视', score: 0.8 });
    this.chineseEmotionDictionary.set('看不起', { emotion: '鄙视', score: 0.8 });
    this.chineseEmotionDictionary.set('瞧不起', { emotion: '鄙视', score: 0.8 });
    this.chineseEmotionDictionary.set('目中无人', { emotion: '鄙视', score: 0.7 });
    this.chineseEmotionDictionary.set('不屑一顾', { emotion: '鄙视', score: 0.8 });
    this.chineseEmotionDictionary.set('嗤之以鼻', { emotion: '鄙视', score: 0.7 });

    // 期待
    this.chineseEmotionDictionary.set('期待', { emotion: '期待', score: 0.8 });
    this.chineseEmotionDictionary.set('期盼', { emotion: '期待', score: 0.8 });
    this.chineseEmotionDictionary.set('盼望', { emotion: '期待', score: 0.7 });
    this.chineseEmotionDictionary.set('企盼', { emotion: '期待', score: 0.7 });
    this.chineseEmotionDictionary.set('憧憬', { emotion: '期待', score: 0.7 });
    this.chineseEmotionDictionary.set('向往', { emotion: '期待', score: 0.6 });
    this.chineseEmotionDictionary.set('翘首以盼', { emotion: '期待', score: 0.9 });
    this.chineseEmotionDictionary.set('望眼欲穿', { emotion: '期待', score: 0.9 });
    this.chineseEmotionDictionary.set('引颈而望', { emotion: '期待', score: 0.8 });

    // 信任
    this.chineseEmotionDictionary.set('信任', { emotion: '信任', score: 0.8 });
    this.chineseEmotionDictionary.set('相信', { emotion: '信任', score: 0.7 });
    this.chineseEmotionDictionary.set('信赖', { emotion: '信任', score: 0.8 });
    this.chineseEmotionDictionary.set('信服', { emotion: '信任', score: 0.7 });
    this.chineseEmotionDictionary.set('信心', { emotion: '信任', score: 0.6 });
    this.chineseEmotionDictionary.set('依赖', { emotion: '信任', score: 0.6 });
    this.chineseEmotionDictionary.set('托付', { emotion: '信任', score: 0.7 });

    // 喜爱
    this.chineseEmotionDictionary.set('喜爱', { emotion: '喜爱', score: 0.8 });
    this.chineseEmotionDictionary.set('喜欢', { emotion: '喜爱', score: 0.7 });
    this.chineseEmotionDictionary.set('爱慕', { emotion: '喜爱', score: 0.8 });
    this.chineseEmotionDictionary.set('钟爱', { emotion: '喜爱', score: 0.8 });
    this.chineseEmotionDictionary.set('宠爱', { emotion: '喜爱', score: 0.7 });
    this.chineseEmotionDictionary.set('疼爱', { emotion: '喜爱', score: 0.7 });
    this.chineseEmotionDictionary.set('爱不释手', { emotion: '喜爱', score: 0.9 });
    this.chineseEmotionDictionary.set('爱不释手', { emotion: '喜爱', score: 0.9 });
    this.chineseEmotionDictionary.set('爱不释手', { emotion: '喜爱', score: 0.9 });

    // 自豪
    this.chineseEmotionDictionary.set('自豪', { emotion: '自豪', score: 0.8 });
    this.chineseEmotionDictionary.set('骄傲', { emotion: '自豪', score: 0.8 });
    this.chineseEmotionDictionary.set('得意', { emotion: '自豪', score: 0.7 });
    this.chineseEmotionDictionary.set('自满', { emotion: '自豪', score: 0.6 });
    this.chineseEmotionDictionary.set('自信', { emotion: '自豪', score: 0.6 });
    this.chineseEmotionDictionary.set('志得意满', { emotion: '自豪', score: 0.8 });
    this.chineseEmotionDictionary.set('扬眉吐气', { emotion: '自豪', score: 0.8 });
    this.chineseEmotionDictionary.set('意气风发', { emotion: '自豪', score: 0.7 });

    // 满足
    this.chineseEmotionDictionary.set('满足', { emotion: '满足', score: 0.8 });
    this.chineseEmotionDictionary.set('满意', { emotion: '满足', score: 0.7 });
    this.chineseEmotionDictionary.set('知足', { emotion: '满足', score: 0.7 });
    this.chineseEmotionDictionary.set('称心', { emotion: '满足', score: 0.7 });
    this.chineseEmotionDictionary.set('如意', { emotion: '满足', score: 0.6 });
    this.chineseEmotionDictionary.set('心满意足', { emotion: '满足', score: 0.9 });
    this.chineseEmotionDictionary.set('称心如意', { emotion: '满足', score: 0.8 });
    this.chineseEmotionDictionary.set('心安理得', { emotion: '满足', score: 0.7 });

    // 感激
    this.chineseEmotionDictionary.set('感激', { emotion: '感激', score: 0.8 });
    this.chineseEmotionDictionary.set('感谢', { emotion: '感激', score: 0.7 });
    this.chineseEmotionDictionary.set('谢意', { emotion: '感激', score: 0.7 });
    this.chineseEmotionDictionary.set('感恩', { emotion: '感激', score: 0.8 });
    this.chineseEmotionDictionary.set('铭记', { emotion: '感激', score: 0.6 });
    this.chineseEmotionDictionary.set('感激不尽', { emotion: '感激', score: 0.9 });
    this.chineseEmotionDictionary.set('感恩戴德', { emotion: '感激', score: 0.9 });
    this.chineseEmotionDictionary.set('铭记在心', { emotion: '感激', score: 0.8 });

    // 羞愧
    this.chineseEmotionDictionary.set('羞愧', { emotion: '羞愧', score: 0.8 });
    this.chineseEmotionDictionary.set('羞耻', { emotion: '羞愧', score: 0.8 });
    this.chineseEmotionDictionary.set('害羞', { emotion: '羞愧', score: 0.6 });
    this.chineseEmotionDictionary.set('惭愧', { emotion: '羞愧', score: 0.7 });
    this.chineseEmotionDictionary.set('羞涩', { emotion: '羞愧', score: 0.6 });
    this.chineseEmotionDictionary.set('无地自容', { emotion: '羞愧', score: 0.9 });
    this.chineseEmotionDictionary.set('羞愧难当', { emotion: '羞愧', score: 0.9 });
    this.chineseEmotionDictionary.set('无颜见人', { emotion: '羞愧', score: 0.8 });

    // 内疚
    this.chineseEmotionDictionary.set('内疚', { emotion: '内疚', score: 0.8 });
    this.chineseEmotionDictionary.set('愧疚', { emotion: '内疚', score: 0.8 });
    this.chineseEmotionDictionary.set('自责', { emotion: '内疚', score: 0.7 });
    this.chineseEmotionDictionary.set('悔恨', { emotion: '内疚', score: 0.7 });
    this.chineseEmotionDictionary.set('懊悔', { emotion: '内疚', score: 0.7 });
    this.chineseEmotionDictionary.set('良心不安', { emotion: '内疚', score: 0.8 });
    this.chineseEmotionDictionary.set('问心有愧', { emotion: '内疚', score: 0.8 });
    this.chineseEmotionDictionary.set('于心不忍', { emotion: '内疚', score: 0.7 });

    // 中性
    this.chineseEmotionDictionary.set('平静', { emotion: '中性', score: 0.7 });
    this.chineseEmotionDictionary.set('平淡', { emotion: '中性', score: 0.7 });
    this.chineseEmotionDictionary.set('冷静', { emotion: '中性', score: 0.6 });
    this.chineseEmotionDictionary.set('淡然', { emotion: '中性', score: 0.6 });
    this.chineseEmotionDictionary.set('镇定', { emotion: '中性', score: 0.6 });
    this.chineseEmotionDictionary.set('波澜不惊', { emotion: '中性', score: 0.7 });
    this.chineseEmotionDictionary.set('处变不惊', { emotion: '中性', score: 0.7 });
    this.chineseEmotionDictionary.set('不动声色', { emotion: '中性', score: 0.7 });
  }

  /**
   * 分析情感
   * @param text 文本
   * @param options 选项
   * @returns 情感分析结果
   */
  public async analyzeEmotion(
    text: string,
    options: Partial<Omit<EmotionAnalysisRequest, 'text'>> = {}
  ): Promise<EmotionAnalysisResult> {
    // 预处理文本
    let processedText = text;

    // 如果有方言处理，先进行方言处理
    if (this.dialectProcessor && this.chineseConfig.dialectType !== ChineseDialectType.MANDARIN) {
      try {
        processedText = this.dialectProcessor.process(text);
        if (this.chineseConfig.debug) {
          console.log('方言处理后的文本:', processedText);
        }
      } catch (error) {
        if (this.chineseConfig.debug) {
          console.warn('方言处理失败:', error);
        }
      }
    }

    // 使用父类方法分析情感
    let result: EmotionAnalysisResult;

    try {
      // 尝试使用模型分析
      result = await super.analyzeEmotion(processedText, {
        ...options,
        // 启用上下文分析
        useContext: this.chineseConfig.useContextAnalysis || options.useContext,
        // 设置上下文窗口大小
        contextWindowSize: this.contextWindowSize
      });

      // 如果启用了词典增强，结合词典分析结果
      if (this.chineseConfig.useDictionaryEnhancement) {
        const dictionaryResult = this.analyzeEmotionWithDictionary(processedText);
        result = this.combineResults(result, dictionaryResult, 0.7, 0.3); // 70%模型，30%词典
      }
    } catch (error) {
      if (this.chineseConfig.debug) {
        console.warn('模型分析失败，回退到词典分析:', error);
      }
      // 回退到词典分析
      result = this.analyzeEmotionWithDictionary(processedText);
    }

    // 转换情感标签为中文
    if (result.primaryEmotion && this.englishToChineseEmotionMap.has(result.primaryEmotion)) {
      result.primaryEmotion = this.englishToChineseEmotionMap.get(result.primaryEmotion)!;
    }

    // 转换情感分数映射的键为中文
    const chineseScores: { [key: string]: number } = {};
    for (const [emotion, score] of Object.entries(result.scores)) {
      const chineseEmotion = this.englishToChineseEmotionMap.get(emotion) || emotion;
      chineseScores[chineseEmotion] = score;
    }
    result.scores = chineseScores;

    // 添加方言信息
    if (this.chineseConfig.dialectType !== ChineseDialectType.MANDARIN) {
      result.dialect = this.chineseConfig.dialectType;
    }

    // 添加分析时间戳
    result.timestamp = Date.now();

    return result;
  }

  /**
   * 合并两个分析结果
   * @param result1 结果1
   * @param result2 结果2
   * @param weight1 权重1
   * @param weight2 权重2
   * @returns 合并后的结果
   */
  private combineResults(
    result1: EmotionAnalysisResult,
    result2: EmotionAnalysisResult,
    weight1: number = 0.5,
    weight2: number = 0.5
  ): EmotionAnalysisResult {
    // 合并情感分数
    const combinedScores: { [key: string]: number } = {};

    // 获取所有情感类别
    const allEmotions = new Set([
      ...Object.keys(result1.scores),
      ...Object.keys(result2.scores)
    ]);

    // 计算加权平均分数
    for (const emotion of allEmotions) {
      const score1 = result1.scores[emotion] || 0;
      const score2 = result2.scores[emotion] || 0;
      combinedScores[emotion] = (score1 * weight1) + (score2 * weight2);
    }

    // 找出主要情感
    let primaryEmotion = '';
    let maxScore = -1;

    for (const [emotion, score] of Object.entries(combinedScores)) {
      if (score > maxScore) {
        maxScore = score;
        primaryEmotion = emotion;
      }
    }

    // 计算强度
    const intensity = Math.min(1.0, (result1.intensity * weight1) + (result2.intensity * weight2));

    return {
      primaryEmotion,
      primaryIntensity: intensity,
      intensity,
      scores: combinedScores,
      // 合并详细信息
      details: {
        ...(result1.details || {}),
        ...(result2.details || {}),
        combined: true,
        modelWeight: weight1,
        dictionaryWeight: weight2
      }
    };
  }



  /**
   * 使用词典分析情感
   * @param text 文本
   * @returns 情感分析结果
   */
  private analyzeEmotionWithDictionary(text: string): EmotionAnalysisResult {
    // 使用改进的情感分析
    const emotionScores: { [key: string]: number } = {};

    // 初始化所有情感类别的分数
    for (const emotion of ChineseBERTEmotionModel.DEFAULT_CHINESE_EMOTION_CATEGORIES) {
      emotionScores[emotion] = 0;
    }
    emotionScores['中性'] = 0.1; // 默认有一点中性情感

    // 使用改进的分词
    const words = this.tokenizeChineseText(text);

    // 计算每个情感的分数
    for (const word of words) {
      const entry = this.chineseEmotionDictionary.get(word);
      if (entry) {
        emotionScores[entry.emotion] += entry.score;
      }
    }

    // 上下文分析
    if (this.contextHistory.length > 0 && this.chineseConfig.useContextAnalysis) {
      this.applyContextAnalysis(emotionScores);
    }

    // 找出主要情感
    let primaryEmotion = '中性';
    let maxScore = 0;

    for (const [emotion, score] of Object.entries(emotionScores)) {
      if (score > maxScore) {
        maxScore = score;
        primaryEmotion = emotion;
      }
    }

    // 计算总分数
    const totalScore = Object.values(emotionScores).reduce((sum, score) => sum + score, 0);

    // 归一化分数
    const normalizedScores: { [key: string]: number } = {};
    for (const [emotion, score] of Object.entries(emotionScores)) {
      normalizedScores[emotion] = totalScore > 0 ? score / totalScore : (emotion === '中性' ? 1 : 0);
    }

    // 更新上下文历史
    this.updateContextHistory(text, primaryEmotion);

    const intensity = Math.min(1.0, maxScore);

    return {
      primaryEmotion,
      primaryIntensity: intensity,
      intensity,
      scores: normalizedScores,
      // 添加更多详细信息
      details: {
        wordCount: words.length,
        emotionalWords: words.filter(word => this.chineseEmotionDictionary.has(word)).length,
        contextInfluence: this.contextHistory.length > 0 && this.chineseConfig.useContextAnalysis
      }
    };
  }

  /**
   * 应用上下文分析
   * @param emotionScores 情感分数
   */
  private applyContextAnalysis(emotionScores: { [key: string]: number }): void {
    // 简单的上下文影响实现
    // 历史情感会对当前情感产生一定影响

    // 获取最近的情感历史
    const recentHistory = this.contextHistory.slice(-this.contextWindowSize);

    // 情感惯性因子（越近的历史影响越大）
    const inertiaFactor = 0.2;

    // 计算历史情感的影响
    for (let i = 0; i < recentHistory.length; i++) {
      const historyEmotion = recentHistory[i];
      // 影响权重随着时间衰减
      const weight = inertiaFactor * (1 - (i / recentHistory.length));

      // 增加历史情感的分数
      if (emotionScores[historyEmotion] !== undefined) {
        emotionScores[historyEmotion] += weight;
      }
    }
  }

  /**
   * 更新上下文历史
   * @param text 文本
   * @param emotion 情感
   */
  private updateContextHistory(_text: string, emotion: string): void {
    // 只有启用上下文分析时才更新历史
    if (!this.chineseConfig.useContextAnalysis) return;

    // 添加到历史
    this.contextHistory.push(emotion);

    // 限制历史大小
    if (this.contextHistory.length > this.contextWindowSize) {
      this.contextHistory.shift();
    }
  }

  /**
   * 中文分词
   * @param text 文本
   * @returns 分词结果
   */
  private tokenizeChineseText(text: string): string[] {
    // 如果有专业分词器，使用专业分词器
    if (this.tokenizer) {
      try {
        return this.tokenizer.tokenize(text);
      } catch (error) {
        if (this.chineseConfig.debug) {
          console.warn('专业分词器失败，回退到简单分词:', error);
        }
      }
    }

    // 回退到简单分词
    return this.simpleChineseTokenize(text);
  }

  /**
   * 简单的中文分词
   * @param text 文本
   * @returns 分词结果
   */
  private simpleChineseTokenize(text: string): string[] {
    // 这是一个改进的简单分词实现

    // 1. 预处理文本
    let processedText = text;

    // 如果有方言处理器，先进行方言处理
    if (this.dialectProcessor) {
      try {
        processedText = this.dialectProcessor.process(text);
      } catch (error) {
        if (this.chineseConfig.debug) {
          console.warn('方言处理失败:', error);
        }
      }
    }

    // 2. 移除标点符号，保留空格
    const cleanText = processedText.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, ' ');

    // 3. 按空格分割
    const spaceSplit = cleanText.split(/\s+/).filter(word => word.length > 0);

    // 4. 对于中文，使用更智能的分词策略
    const result: string[] = [];

    // 常用词组词典（简化版）
    const commonPhrases = new Set([
      '开心', '高兴', '快乐', '喜悦', '欢喜', '欢乐', '愉快', '兴奋',
      '悲伤', '难过', '伤心', '忧伤', '忧郁', '哀伤', '痛苦',
      '愤怒', '生气', '恼火', '暴怒', '气愤', '恼怒',
      '惊讶', '吃惊', '震惊', '惊奇', '惊异',
      '恐惧', '害怕', '惊恐', '惊吓', '畏惧',
      '厌恶', '讨厌', '恶心', '反感', '嫌弃',
      '鄙视', '轻蔑', '蔑视', '不屑',
      '喜笑颜开', '眉开眼笑', '笑逐颜开', '泪流满面', '火冒三丈',
      '大发雷霆', '怒不可遏', '勃然大怒', '怒气冲冲', '目瞪口呆',
      '瞠目结舌', '胆战心惊', '心惊肉跳', '毛骨悚然', '不寒而栗',
      '深恶痛绝', '嗤之以鼻', '避之不及', '不屑一顾'
    ]);

    for (const part of spaceSplit) {
      // 如果是纯中文
      if (/^[\u4e00-\u9fa5]+$/.test(part)) {
        // 1. 先检查常用词组
        let i = 0;
        while (i < part.length) {
          let matched = false;

          // 尝试最长匹配（最多4个字符）
          for (let len = Math.min(4, part.length - i); len > 0; len--) {
            const phrase = part.substring(i, i + len);
            if (commonPhrases.has(phrase) || this.chineseEmotionDictionary.has(phrase)) {
              result.push(phrase);
              i += len;
              matched = true;
              break;
            }
          }

          // 如果没有匹配到词组，则添加单字
          if (!matched) {
            result.push(part.charAt(i));
            i++;
          }
        }

        // 2. 添加可能的词组（2-3个字符）
        for (let i = 0; i < part.length - 1; i++) {
          const phrase2 = part.substring(i, i + 2);
          if (!result.includes(phrase2)) {
            result.push(phrase2);
          }
        }

        for (let i = 0; i < part.length - 2; i++) {
          const phrase3 = part.substring(i, i + 3);
          if (!result.includes(phrase3)) {
            result.push(phrase3);
          }
        }

        // 3. 添加四字成语
        for (let i = 0; i < part.length - 3; i++) {
          const phrase4 = part.substring(i, i + 4);
          if (!result.includes(phrase4) && this.isFourCharacterIdiom(phrase4)) {
            result.push(phrase4);
          }
        }
      } else {
        // 非纯中文，直接添加
        result.push(part);
      }
    }

    return result;
  }

  /**
   * 判断是否为四字成语
   * @param phrase 短语
   * @returns 是否为四字成语
   */
  private isFourCharacterIdiom(phrase: string): boolean {
    // 简单判断是否为四字成语
    if (phrase.length !== 4) return false;

    // 常见四字成语列表（非常简化）
    const commonIdioms = new Set([
      '喜笑颜开', '眉开眼笑', '笑逐颜开', '愁眉苦脸', '泪流满面',
      '火冒三丈', '大发雷霆', '怒不可遏', '勃然大怒', '怒气冲冲',
      '目瞪口呆', '瞠目结舌', '胆战心惊', '心惊肉跳', '毛骨悚然',
      '不寒而栗', '深恶痛绝', '嗤之以鼻', '避之不及', '不屑一顾',
      '翘首以盼', '望眼欲穿', '心满意足', '称心如意', '感激不尽',
      '感恩戴德', '无地自容', '羞愧难当', '无颜见人', '良心不安',
      '问心有愧', '波澜不惊', '处变不惊', '不动声色'
    ]);

    return commonIdioms.has(phrase);
  }
}
