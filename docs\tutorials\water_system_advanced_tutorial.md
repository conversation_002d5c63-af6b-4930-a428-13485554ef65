# 水体系统高级教程

本教程将深入介绍水体系统的高级功能和优化技巧，帮助您创建更加逼真和高性能的水体效果。

## 目录

1. [多线程水体物理计算](#多线程水体物理计算)
2. [水体空间分区优化](#水体空间分区优化)
3. [自定义水体着色器](#自定义水体着色器)
4. [高级水体交互效果](#高级水体交互效果)
5. [水体粒子系统进阶](#水体粒子系统进阶)
6. [水体光照效果进阶](#水体光照效果进阶)
7. [水体性能优化](#水体性能优化)
8. [水体与地形集成](#水体与地形集成)
9. [水体特效案例](#水体特效案例)
10. [常见问题解决](#常见问题解决)

## 多线程水体物理计算

水体物理计算是一项计算密集型任务，特别是对于大型水体或高分辨率水体。使用多线程可以显著提高水体物理计算的性能。

### 启用多线程计算

```typescript
// 配置水体物理系统
const waterPhysicsSystemConfig = {
  enabled: true,
  autoUpdate: true,
  enableMultithreading: true,
  workerCount: 4 // 根据CPU核心数调整
};

// 创建水体物理系统
const waterPhysicsSystem = new WaterPhysicsSystem(world, waterPhysicsSystemConfig);
```

### 多线程计算原理

水体物理系统使用Web Worker实现多线程计算。主要计算任务包括：

1. **水波计算**：计算水面波动
2. **水流计算**：计算水流动态
3. **法线计算**：计算水面法线
4. **浮力和阻力计算**：计算物体受到的浮力和阻力

这些计算任务被分配到多个工作线程中并行执行，然后将结果返回给主线程。

### 自定义工作线程数量

```typescript
// 根据设备性能动态调整工作线程数量
const workerCount = navigator.hardwareConcurrency || 4;
waterPhysicsSystem.setConfig({ workerCount });
```

### 多线程计算的限制

- 不是所有浏览器都支持Web Worker
- 数据传输开销可能抵消多线程带来的性能提升
- 调试困难

## 水体空间分区优化

对于大型水体或多个水体，使用空间分区可以显著提高性能。

### 启用空间分区

```typescript
// 配置水体物理系统
const waterPhysicsSystemConfig = {
  enabled: true,
  autoUpdate: true,
  enableSpatialPartitioning: true,
  spatialGridSize: 10 // 网格大小
};

// 创建水体物理系统
const waterPhysicsSystem = new WaterPhysicsSystem(world, waterPhysicsSystemConfig);
```

### 空间分区原理

空间分区将场景划分为多个网格，只更新相机附近的水体，远离相机的水体使用简化计算或跳过更新。

### 自适应更新频率

```typescript
// 配置水体物理系统
const waterPhysicsSystemConfig = {
  enabled: true,
  autoUpdate: true,
  enableAdaptiveUpdate: true,
  minUpdateFrequency: 1,
  maxUpdateFrequency: 10
};

// 创建水体物理系统
const waterPhysicsSystem = new WaterPhysicsSystem(world, waterPhysicsSystemConfig);
```

## 自定义水体着色器

水体系统使用自定义着色器实现各种水体效果。您可以修改这些着色器以实现自定义效果。

### 水体顶点着色器

```glsl
// 水体顶点着色器
uniform mat4 textureMatrix;
uniform float time;
uniform float waveStrength;
uniform vec2 waveDirection;

varying vec4 mirrorCoord;
varying vec3 worldPosition;
varying vec3 viewVector;

void main() {
  // 计算波动
  vec3 pos = position.xyz;
  float waveHeight = sin(pos.x * waveDirection.x + pos.z * waveDirection.y + time) * waveStrength;
  pos.y += waveHeight;
  
  // 计算世界坐标
  worldPosition = (modelMatrix * vec4(pos, 1.0)).xyz;
  
  // 计算视线方向
  viewVector = cameraPosition - worldPosition;
  
  // 计算反射坐标
  mirrorCoord = textureMatrix * vec4(pos, 1.0);
  
  // 计算最终位置
  gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
}
```

### 水体片段着色器

```glsl
// 水体片段着色器
uniform sampler2D reflectionSampler;
uniform sampler2D refractionSampler;
uniform sampler2D normalSampler;
uniform sampler2D depthSampler;
uniform vec3 waterColor;
uniform float time;
uniform float reflectivity;
uniform float refractivity;

varying vec4 mirrorCoord;
varying vec3 worldPosition;
varying vec3 viewVector;

void main() {
  // 计算法线
  vec4 normalColor = texture2D(normalSampler, vec2(worldPosition.x * 0.01, worldPosition.z * 0.01) + time * 0.01);
  vec3 normal = normalize(normalColor.rgb * 2.0 - 1.0);
  
  // 计算反射和折射
  vec4 reflectionColor = texture2D(reflectionSampler, mirrorCoord.xy / mirrorCoord.w);
  vec4 refractionColor = texture2D(refractionSampler, mirrorCoord.xy / mirrorCoord.w);
  
  // 计算菲涅尔系数
  float theta = max(dot(normalize(viewVector), normal), 0.0);
  float reflectionFactor = reflectivity + (1.0 - reflectivity) * pow(1.0 - theta, 5.0);
  
  // 混合反射和折射
  vec4 finalColor = mix(refractionColor, reflectionColor, reflectionFactor);
  
  // 添加水体颜色
  finalColor.rgb = mix(finalColor.rgb, waterColor, 0.5);
  
  // 输出最终颜色
  gl_FragColor = finalColor;
}
```

### 自定义水体材质

```typescript
// 创建自定义水体材质
const waterMaterial = new THREE.ShaderMaterial({
  uniforms: {
    reflectionSampler: { value: null },
    refractionSampler: { value: null },
    normalSampler: { value: null },
    depthSampler: { value: null },
    waterColor: { value: new THREE.Color(0x0077be) },
    time: { value: 0 },
    reflectivity: { value: 0.5 },
    refractivity: { value: 0.7 },
    textureMatrix: { value: new THREE.Matrix4() },
    waveStrength: { value: 0.1 },
    waveDirection: { value: new THREE.Vector2(1, 1) }
  },
  vertexShader: waterVertexShader,
  fragmentShader: waterFragmentShader,
  transparent: true
});

// 设置水体材质
waterBody.setMaterial(waterMaterial);
```

## 高级水体交互效果

水体交互系统提供了多种交互效果，如水花、水波纹、水滴等。

### 水花效果

```typescript
// 配置水体交互系统
const waterInteractionSystemConfig = {
  enabled: true,
  autoUpdate: true,
  enableSplashEffect: true,
  splashEffectStrength: 1.0
};

// 创建水体交互系统
const waterInteractionSystem = new WaterInteractionSystem(world, waterInteractionSystemConfig);

// 手动创建水花效果
waterInteractionSystem.createSplashEffect(
  waterBody.getEntityId(),
  new THREE.Vector3(0, 0, 0), // 位置
  1.0, // 强度
  new THREE.Vector3(1, 1, 1) // 尺寸
);
```

### 水波纹效果

```typescript
// 配置水体交互系统
const waterInteractionSystemConfig = {
  enabled: true,
  autoUpdate: true,
  enableRippleEffect: true,
  rippleEffectStrength: 1.0
};

// 创建水体交互系统
const waterInteractionSystem = new WaterInteractionSystem(world, waterInteractionSystemConfig);

// 手动创建水波纹效果
waterInteractionSystem.createRippleEffect(
  waterBody.getEntityId(),
  new THREE.Vector3(0, 0, 0), // 位置
  1.0, // 强度
  new THREE.Vector3(1, 0, 0) // 方向
);
```

### 水体分裂效果

```typescript
// 配置水体交互系统
const waterInteractionSystemConfig = {
  enabled: true,
  autoUpdate: true,
  enableSplittingEffect: true,
  splittingEffectStrength: 1.0
};

// 创建水体交互系统
const waterInteractionSystem = new WaterInteractionSystem(world, waterInteractionSystemConfig);

// 手动创建水体分裂效果
waterInteractionSystem.createSplittingEffect(
  waterBody.getEntityId(),
  new THREE.Vector3(0, 0, 0), // 位置
  1.0, // 强度
  new THREE.Vector3(1, 0, 0) // 方向
);
```

## 水体粒子系统进阶

水下粒子系统提供了多种粒子效果，如气泡、悬浮物、光束等。

### 自定义粒子效果

```typescript
// 配置水下粒子系统
const underwaterParticleSystemConfig = {
  enabled: true,
  autoUpdate: true,
  maxParticles: 1000
};

// 创建水下粒子系统
const underwaterParticleSystem = new UnderwaterParticleSystem(world, underwaterParticleSystemConfig);

// 添加自定义粒子组
underwaterParticleSystem.addParticleGroup(
  waterBody.getEntityId(),
  'customParticles',
  {
    type: UnderwaterParticleType.BUBBLE,
    count: 200,
    size: [0.05, 0.15],
    color: 0xffffff,
    opacity: 0.5,
    lifetime: [3, 8],
    speed: [0.1, 0.3],
    acceleration: new THREE.Vector3(0, 0.1, 0),
    rotation: true,
    rotationSpeed: [0.1, 0.2],
    blending: THREE.AdditiveBlending,
    emissionArea: {
      shape: 'box',
      size: new THREE.Vector3(10, 5, 10),
      position: new THREE.Vector3(0, 0, 0)
    }
  }
);
```

### 粒子系统事件监听

```typescript
// 监听粒子系统事件
underwaterParticleSystem.on('particleCreated', (data) => {
  console.log('粒子已创建:', data);
});

underwaterParticleSystem.on('particleRemoved', (data) => {
  console.log('粒子已移除:', data);
});
```

## 水体光照效果进阶

水体光照系统提供了多种光照效果，如反射、折射、焦散、体积光等。

### 自定义焦散效果

```typescript
// 配置水体光照系统
const waterLightingSystemConfig = {
  enabled: true,
  autoUpdate: true,
  enableCaustics: true,
  causticsIntensity: 1.0,
  causticsMapResolution: 512
};

// 创建水体光照系统
const waterLightingSystem = new WaterLightingSystem(world, waterLightingSystemConfig);

// 监听焦散更新事件
waterLightingSystem.on(WaterLightingSystemEventType.CAUSTICS_UPDATED, (data) => {
  // 使用焦散贴图
  const causticsTexture = data.causticsTexture;
  // 应用到其他材质
  someMaterial.uniforms.causticsMap.value = causticsTexture;
});
```

### 自定义体积光效果

```typescript
// 配置水体光照系统
const waterLightingSystemConfig = {
  enabled: true,
  autoUpdate: true,
  enableVolumetricLight: true,
  volumetricLightIntensity: 1.0,
  volumetricLightMapResolution: 256
};

// 创建水体光照系统
const waterLightingSystem = new WaterLightingSystem(world, waterLightingSystemConfig);

// 监听体积光更新事件
waterLightingSystem.on(WaterLightingSystemEventType.VOLUMETRIC_LIGHT_UPDATED, (data) => {
  // 使用体积光贴图
  const volumetricLightTexture = data.volumetricLightTexture;
  // 应用到其他材质
  someMaterial.uniforms.volumetricLightMap.value = volumetricLightTexture;
});
```

## 水体性能优化

水体系统提供了多种性能优化选项，如多线程计算、空间分区、自适应更新频率等。

### 性能监控

```typescript
// 配置水体物理系统
const waterPhysicsSystemConfig = {
  enabled: true,
  autoUpdate: true,
  enablePerformanceMonitoring: true
};

// 创建水体物理系统
const waterPhysicsSystem = new WaterPhysicsSystem(world, waterPhysicsSystemConfig);

// 获取性能数据
const performanceData = waterPhysicsSystem.getPerformanceData();
console.log('水体物理系统性能数据:', performanceData);
```

### 动态调整水体质量

```typescript
// 根据帧率动态调整水体质量
const adjustWaterQuality = (fps) => {
  if (fps < 30) {
    // 低质量
    waterPhysicsSystem.setConfig({
      updateFrequency: 2,
      enableMultithreading: false
    });
    waterLightingSystem.setConfig({
      reflectionMapResolution: 256,
      refractionMapResolution: 256,
      enableCaustics: false,
      enableVolumetricLight: false
    });
  } else if (fps < 60) {
    // 中质量
    waterPhysicsSystem.setConfig({
      updateFrequency: 1,
      enableMultithreading: true
    });
    waterLightingSystem.setConfig({
      reflectionMapResolution: 512,
      refractionMapResolution: 512,
      enableCaustics: true,
      enableVolumetricLight: false
    });
  } else {
    // 高质量
    waterPhysicsSystem.setConfig({
      updateFrequency: 1,
      enableMultithreading: true
    });
    waterLightingSystem.setConfig({
      reflectionMapResolution: 1024,
      refractionMapResolution: 1024,
      enableCaustics: true,
      enableVolumetricLight: true
    });
  }
};
```

## 水体与地形集成

水体系统可以与地形系统集成，创建更加逼真的水体效果。

### 水体与地形交互

```typescript
// 获取地形高度
const getTerrainHeight = (x, z) => {
  // 实现地形高度查询
  return terrainSystem.getHeight(x, z);
};

// 创建水体
const waterBody = new WaterBodyComponent();
waterBody.setSize({ width: 100, height: 10, depth: 100 });
waterBody.setPosition(new THREE.Vector3(0, 0, 0));

// 设置水体与地形交互
waterBody.setTerrainInteraction({
  enabled: true,
  getTerrainHeight: getTerrainHeight,
  followTerrain: true,
  terrainOffset: 1.0
});
```

### 河流生成

```typescript
// 创建河流
const createRiver = (path, width, depth) => {
  // 创建河流实体
  const riverEntity = new Entity();
  
  // 创建河流水体
  const riverWaterBody = WaterPresets.createPreset(world, {
    type: WaterPresetType.RIVER,
    size: { width, height: depth, depth: 0 }, // 深度将根据路径长度计算
    position: new THREE.Vector3(0, 0, 0)
  });
  
  // 设置河流路径
  riverWaterBody.setPath(path);
  
  // 设置河流与地形交互
  riverWaterBody.setTerrainInteraction({
    enabled: true,
    getTerrainHeight: getTerrainHeight,
    followTerrain: true,
    terrainOffset: 0.5
  });
  
  // 添加到世界
  world.addEntity(riverEntity);
  
  return riverWaterBody;
};
```

## 水体特效案例

### 瀑布效果

```typescript
// 创建瀑布
const createWaterfall = (position, width, height, depth) => {
  // 创建瀑布水体
  const waterfallWaterBody = WaterPresets.createPreset(world, {
    type: WaterPresetType.WATERFALL,
    size: { width, height, depth },
    position,
    rotation: new THREE.Euler(-Math.PI / 4, 0, 0)
  });
  
  // 创建水花效果
  const splashPosition = new THREE.Vector3(
    position.x,
    position.y - height * Math.sin(Math.PI / 4),
    position.z + height * Math.cos(Math.PI / 4)
  );
  
  waterInteractionSystem.createSplashEffect(
    waterfallWaterBody.getEntityId(),
    splashPosition,
    1.0,
    new THREE.Vector3(width, 1, depth)
  );
  
  // 创建水雾效果
  underwaterParticleSystem.addParticleGroup(
    waterfallWaterBody.getEntityId(),
    'waterfallMist',
    {
      type: UnderwaterParticleType.MIST,
      count: 200,
      size: [0.5, 1.5],
      color: 0xffffff,
      opacity: 0.3,
      lifetime: [2, 5],
      speed: [0.1, 0.2],
      acceleration: new THREE.Vector3(0, 0.05, 0),
      blending: THREE.AdditiveBlending,
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(width, depth, width),
        position: splashPosition
      }
    }
  );
  
  return waterfallWaterBody;
};
```

### 海浪效果

```typescript
// 创建海浪
const createOceanWaves = (position, width, depth) => {
  // 创建海洋水体
  const oceanWaterBody = WaterPresets.createPreset(world, {
    type: WaterPresetType.OCEAN,
    size: { width, height: 50, depth },
    position
  });
  
  // 设置波动参数
  oceanWaterBody.setWaveParams({
    amplitude: 1.0,
    frequency: 0.1,
    speed: 0.5,
    direction: new THREE.Vector2(1, 1)
  });
  
  // 添加波浪粒子效果
  underwaterParticleSystem.addParticleGroup(
    oceanWaterBody.getEntityId(),
    'oceanFoam',
    {
      type: UnderwaterParticleType.FOAM,
      count: 500,
      size: [0.5, 1.5],
      color: 0xffffff,
      opacity: 0.7,
      lifetime: [3, 8],
      speed: [0.1, 0.3],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(width, 1, depth),
        position: new THREE.Vector3(position.x, position.y + 0.5, position.z)
      }
    }
  );
  
  return oceanWaterBody;
};
```

## 常见问题解决

### 水体闪烁问题

水体闪烁通常是由于深度缓冲区精度不足或Z-fighting引起的。解决方法：

```typescript
// 调整相机近平面和远平面
camera.near = 0.1;
camera.far = 1000;
camera.updateProjectionMatrix();

// 使用对数深度缓冲
renderer.logarithmicDepthBuffer = true;

// 调整水体位置，避免与其他物体重叠
waterBody.setPosition(new THREE.Vector3(0, 0.01, 0)); // 略微抬高水面
```

### 水体性能问题

水体性能问题通常是由于计算量过大或渲染开销过高引起的。解决方法：

```typescript
// 降低水体分辨率
waterBody.setResolution(64); // 降低高度图分辨率

// 禁用高级效果
waterLightingSystem.setConfig({
  enableCaustics: false,
  enableVolumetricLight: false
});

// 启用空间分区
waterPhysicsSystem.setConfig({
  enableSpatialPartitioning: true,
  spatialGridSize: 10
});

// 增加更新频率
waterPhysicsSystem.setConfig({
  updateFrequency: 2 // 每两帧更新一次
});
```

### 水体与物体交互问题

水体与物体交互问题通常是由于物理计算不准确或碰撞检测失败引起的。解决方法：

```typescript
// 调整物理参数
waterBody.setDensity(1.0);
waterBody.setViscosity(0.5);

// 启用调试可视化
waterPhysicsSystem.setConfig({
  enableDebugVisualization: true
});

// 手动设置物体浮力
physicsBody.setBouyancyFactor(1.0);
```
