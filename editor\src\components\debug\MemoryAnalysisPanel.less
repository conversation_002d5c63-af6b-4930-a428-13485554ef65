/**
 * 内存分析面板样式
 */

.memory-analysis-panel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .memory-toolbar {
    padding: 12px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;

    .ant-space {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      gap: 8px;
    }
  }

  .memory-content {
    padding: 16px;
    overflow: auto;
    flex: 1;

    .ant-tabs-content {
      height: 100%;
    }
  }

  .memory-usage {
    .memory-usage-item {
      margin-bottom: 16px;

      .ant-typography {
        margin-bottom: 4px;
      }
    }
  }

  .memory-warning {
    color: #faad14;
  }

  .memory-error {
    color: #f5222d;
  }

  .memory-good {
    color: #52c41a;
  }

  // 资源过滤器样式
  .resource-filter {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f9f9f9;
    border-radius: 4px;

    .ant-space {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      gap: 12px;
    }
  }

  // 泄漏控制样式
  .leak-controls {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f9f9f9;
    border-radius: 4px;

    .ant-space {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      gap: 12px;
    }
  }

  // 快照控制样式
  .snapshot-controls {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f9f9f9;
    border-radius: 4px;

    .ant-space {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      gap: 12px;
    }
  }

  // 已释放资源样式
  .memory-disposed {
    color: #999;
    text-decoration: line-through;
  }

  // 已释放资源行样式
  .memory-disposed-row {
    background-color: #f9f9f9;
    opacity: 0.7;
  }

  // 选中行样式
  .selected-row {
    background-color: #e6f7ff;
  }

  // 快照选择样式
  .snapshot-selection {
    margin-bottom: 16px;

    .snapshot-select, .snapshot-compare {
      padding: 12px;
      background-color: #f9f9f9;
      border-radius: 4px;
    }
  }

  // 快照详情样式
  .snapshot-details {
    margin-top: 16px;
  }
}

// 暗色主题样式
.dark-theme {
  .memory-analysis-panel {
    .memory-toolbar {
      background-color: #1e1e1e;
      border-bottom: 1px solid #303030;
    }

    .resource-filter, .leak-controls, .snapshot-controls, .snapshot-select, .snapshot-compare {
      background-color: #2a2a2a;
    }

    .memory-disposed-row {
      background-color: #1e1e1e;
    }

    .selected-row {
      background-color: #111d2c;
    }
  }
}
