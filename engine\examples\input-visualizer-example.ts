/**
 * 输入可视化器示例
 * 演示如何使用输入可视化器可视化输入状态和事件
 */
import {
  Engine,
  World,
  Scene,
  Camera,
  Renderer,
  InputSystem,
  InputManager,
  InputDevice,
  InputAction,
  InputBinding,
  InputMapping,
  InputComponent,
  ButtonInputAction,
  ValueInputAction,
  VectorInputAction,
  ButtonInputMapping,
  AxisInputMapping,
  VectorInputMapping,
  KeyboardDevice,
  MouseDevice,
  GamepadDevice,
  TouchDevice,
  InputMappingType,
  InputActionType
} from '../src';
import { InputVisualizer } from '../src/input/InputVisualizer';

// 创建引擎
const engine = new Engine();

// 创建世界
const world = new World();
engine.setWorld(world);

// 创建输入系统
const inputSystem = new InputSystem();
world.addSystem(inputSystem);

// 创建输入管理器
const inputManager = InputManager.getInstance();
inputManager.initialize();

// 创建渲染系统
const renderer = new Renderer({
  width: window.innerWidth,
  height: window.innerHeight,
  antialias: true
});
document.body.appendChild(renderer.getDomElement());

// 创建场景
const scene = new Scene();
world.addEntity(scene);

// 创建相机
const camera = new Camera({
  type: 'perspective',
  fov: 75,
  aspect: window.innerWidth / window.innerHeight,
  near: 0.1,
  far: 1000
});
camera.position.set(0, 5, 10);
camera.lookAt(0, 0, 0);
world.addEntity(camera);

// 创建玩家实体
const player = world.createEntity();
player.name = '玩家';

// 创建输入动作
const moveAction = new VectorInputAction('move');
const jumpAction = new ButtonInputAction('jump');
const fireAction = new ButtonInputAction('fire');
const aimAction = new ValueInputAction('aim');

// 创建输入映射
const keyboardMoveMapping = new VectorInputMapping(
  'keyboardMove',
  'keyboard',
  'KeyD', // 右
  'KeyW', // 上
  1,
  0.1
);

const keyboardJumpMapping = new ButtonInputMapping(
  'keyboardJump',
  'keyboard',
  'Space'
);

const mouseFireMapping = new ButtonInputMapping(
  'mouseFire',
  'mouse',
  'button:0' // 左键
);

const mouseAimMapping = new AxisInputMapping(
  'mouseAim',
  'mouse',
  'wheel:delta',
  0.01
);

// 创建输入绑定
const moveBinding = new InputBinding('move', 'keyboardMove');
const jumpBinding = new InputBinding('jump', 'keyboardJump');
const fireBinding = new InputBinding('fire', 'mouseFire');
const aimBinding = new InputBinding('aim', 'mouseAim');

// 创建输入组件
const inputComponent = new InputComponent(player, {
  actions: [moveAction, jumpAction, fireAction, aimAction],
  bindings: [moveBinding, jumpBinding, fireBinding, aimBinding],
  mappings: [keyboardMoveMapping, keyboardJumpMapping, mouseFireMapping, mouseAimMapping]
});

// 添加输入组件到玩家实体
player.addComponent(inputComponent);

// 创建输入可视化器
const inputVisualizer = new InputVisualizer({
  showDevices: true,
  showActions: true,
  showEventLog: true,
  maxEventLogEntries: 50,
  autoUpdate: true,
  updateInterval: 100
});

// 添加输入组件到可视化器
inputVisualizer.addInputComponent(inputComponent);

// 创建UI元素
const createUI = () => {
  const container = document.createElement('div');
  container.style.position = 'absolute';
  container.style.top = '10px';
  container.style.left = '10px';
  container.style.color = 'white';
  container.style.fontFamily = 'Arial, sans-serif';
  container.style.fontSize = '14px';
  container.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
  container.style.padding = '10px';
  container.style.borderRadius = '5px';
  container.style.width = '300px';

  const title = document.createElement('h2');
  title.textContent = '输入可视化器示例';
  title.style.margin = '0 0 10px 0';
  container.appendChild(title);

  const description = document.createElement('p');
  description.textContent = '这个示例演示了如何使用输入可视化器可视化输入状态和事件。请尝试使用键盘、鼠标和游戏手柄进行输入，观察右侧的可视化器。';
  container.appendChild(description);

  const instructions = document.createElement('div');
  instructions.innerHTML = `
    <h3>操作说明</h3>
    <ul>
      <li>移动: W/A/S/D 键</li>
      <li>跳跃: 空格键</li>
      <li>开火: 鼠标左键</li>
      <li>瞄准: 鼠标滚轮</li>
    </ul>
  `;
  container.appendChild(instructions);

  document.body.appendChild(container);
};

// 创建UI
createUI();

// 游戏循环
const gameLoop = () => {
  // 更新输入管理器
  inputManager.update(engine.deltaTime);

  // 更新输入组件
  inputComponent.update(engine.deltaTime);

  // 更新世界
  world.update(engine.deltaTime);

  // 渲染场景
  renderer.render(scene, camera);

  // 请求下一帧
  requestAnimationFrame(gameLoop);
};

// 开始游戏循环
gameLoop();

// 窗口大小调整
window.addEventListener('resize', () => {
  renderer.setSize(window.innerWidth, window.innerHeight);
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
});
