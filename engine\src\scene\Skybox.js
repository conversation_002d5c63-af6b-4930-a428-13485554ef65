"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Skybox = exports.SkyboxType = void 0;
/**
 * 天空盒类
 * 创建和管理场景的天空盒
 */
var THREE = require("three");
var SkyboxType;
(function (SkyboxType) {
    SkyboxType["CUBEMAP"] = "cubemap";
    SkyboxType["EQUIRECTANGULAR"] = "equirectangular";
    SkyboxType["PROCEDURAL"] = "procedural";
})(SkyboxType || (exports.SkyboxType = SkyboxType = {}));
var Skybox = /** @class */ (function () {
    /**
     * 创建天空盒实例
     * @param options 天空盒选项
     */
    function Skybox(options) {
        /** 天空盒网格 */
        this.mesh = null;
        /** 天空盒材质 */
        this.material = null;
        /** 天空盒几何体 */
        this.geometry = null;
        this.type = options.type;
        this.rotate = options.rotate || false;
        this.rotationSpeed = options.rotationSpeed || 0.001;
        // 根据类型创建天空盒
        switch (this.type) {
            case SkyboxType.CUBEMAP:
                this.createCubemapSkybox(options.cubemapPaths || []);
                break;
            case SkyboxType.EQUIRECTANGULAR:
                this.createEquirectangularSkybox(options.equirectangularPath || '');
                break;
            case SkyboxType.PROCEDURAL:
                this.createProceduralSkybox(options.proceduralParams || {});
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u5929\u7A7A\u76D2\u7C7B\u578B: ".concat(this.type));
                break;
        }
    }
    /**
     * 创建立方体贴图天空盒
     * @param paths 6个面的贴图路径（顺序：右、左、上、下、前、后）
     */
    Skybox.prototype.createCubemapSkybox = function (paths) {
        if (paths.length !== 6) {
            console.error('立方体贴图需要6个面的贴图路径');
            return;
        }
        // 加载立方体贴图
        var loader = new THREE.CubeTextureLoader();
        var texture = loader.load(paths);
        // 创建几何体和材质
        this.geometry = new THREE.BoxGeometry(1, 1, 1);
        this.material = new THREE.MeshBasicMaterial({
            envMap: texture,
            side: THREE.BackSide,
        });
        // 创建网格
        this.mesh = new THREE.Mesh(this.geometry, this.material);
        this.mesh.setScale(1000, 1000, 1000);
    };
    /**
     * 创建等距矩形贴图天空盒
     * @param path 等距矩形贴图路径
     */
    Skybox.prototype.createEquirectangularSkybox = function (path) {
        if (!path) {
            console.error('等距矩形贴图需要提供贴图路径');
            return;
        }
        // 加载等距矩形贴图
        var loader = new THREE.TextureLoader();
        var texture = loader.load(path);
        texture.mapping = THREE.EquirectangularReflectionMapping;
        // 创建几何体和材质
        this.geometry = new THREE.SphereGeometry(1, 64, 32);
        this.material = new THREE.MeshBasicMaterial({
            map: texture,
            side: THREE.BackSide,
        });
        // 创建网格
        this.mesh = new THREE.Mesh(this.geometry, this.material);
        this.mesh.setScale(1000, 1000, 1000);
    };
    /**
     * 创建程序化天空盒
     * @param params 程序化天空盒参数
     */
    Skybox.prototype.createProceduralSkybox = function (params) {
        // 默认参数
        var topColor = params.topColor !== undefined ? params.topColor : 0x0077ff;
        var bottomColor = params.bottomColor !== undefined ? params.bottomColor : 0xffffff;
        var exponent = params.exponent !== undefined ? params.exponent : 0.6;
        // 创建着色器材质
        var vertexShader = "\n      varying vec3 vWorldPosition;\n      \n      void main() {\n        vec4 worldPosition = modelMatrix * vec4(position, 1.0);\n        vWorldPosition = worldPosition.xyz;\n        \n        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n      }\n    ";
        var fragmentShader = "\n      uniform vec3 topColor;\n      uniform vec3 bottomColor;\n      uniform float exponent;\n      \n      varying vec3 vWorldPosition;\n      \n      void main() {\n        float h = normalize(vWorldPosition).y;\n        float t = max(0.0, min(1.0, pow(max(0.0, h), exponent)));\n        \n        gl_FragColor = vec4(mix(bottomColor, topColor, t), 1.0);\n      }\n    ";
        // 创建几何体和材质
        this.geometry = new THREE.SphereGeometry(1, 64, 32);
        this.material = new THREE.ShaderMaterial({
            uniforms: {
                topColor: { value: new THREE.Color(topColor) },
                bottomColor: { value: new THREE.Color(bottomColor) },
                exponent: { value: exponent },
            },
            vertexShader: vertexShader,
            fragmentShader: fragmentShader,
            side: THREE.BackSide,
        });
        // 创建网格
        this.mesh = new THREE.Mesh(this.geometry, this.material);
        this.mesh.setScale(1000, 1000, 1000);
    };
    /**
     * 更新天空盒
     * @param deltaTime 帧间隔时间（秒）
     */
    Skybox.prototype.update = function (deltaTime) {
        if (this.rotate && this.mesh) {
            this.mesh.rotation.y += this.rotationSpeed * deltaTime;
        }
    };
    /**
     * 设置旋转
     * @param rotate 是否旋转
     */
    Skybox.prototype.setRotate = function (rotate) {
        this.rotate = rotate;
    };
    /**
     * 设置旋转速度
     * @param speed 旋转速度
     */
    Skybox.prototype.setRotationSpeed = function (speed) {
        this.rotationSpeed = speed;
    };
    /**
     * 获取天空盒网格
     * @returns 天空盒网格
     */
    Skybox.prototype.getMesh = function () {
        return this.mesh;
    };
    /**
     * 获取天空盒类型
     * @returns 天空盒类型
     */
    Skybox.prototype.getType = function () {
        return this.type;
    };
    /**
     * 销毁天空盒
     */
    Skybox.prototype.dispose = function () {
        // 销毁几何体
        if (this.geometry) {
            this.geometry.dispose();
            this.geometry = null;
        }
        // 销毁材质
        if (this.material) {
            if (Array.isArray(this.material)) {
                for (var _i = 0, _a = this.material; _i < _a.length; _i++) {
                    var mat = _a[_i];
                    mat.dispose();
                }
            }
            else {
                this.material.dispose();
            }
            this.material = null;
        }
        this.mesh = null;
    };
    return Skybox;
}());
exports.Skybox = Skybox;
