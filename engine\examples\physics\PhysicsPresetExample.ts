/**
 * 物理预设示例
 * 展示如何使用物理预设系统保存和加载物理配置
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Vector3 } from '../../src/math/Vector3';
import { Quaternion } from '../../src/math/Quaternion';
import { 
  PhysicsSystem, 
  PhysicsBodyComponent, 
  PhysicsColliderComponent,
  BodyType,
  ColliderType,
  PhysicsMaterialFactory
} from '../../src/physics';
import { PhysicsPresetManager } from '../../src/physics/presets/PhysicsPresetManager';
import { CommonPhysicsPresets } from '../../src/physics/presets/CommonPhysicsPresets';

/**
 * 物理预设示例
 */
export class PhysicsPresetExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  
  /** 物理预设管理器 */
  private presetManager: PhysicsPresetManager;
  
  /** 地面实体 */
  private ground: Entity;
  
  /** 物体实体列表 */
  private entities: Entity[] = [];
  
  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建物理预设示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建场景
    this.scene = new Scene('物理预设示例场景');
    
    // 创建物理系统
    this.physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.82, z: 0 },
      debug: true
    });
    
    // 添加物理系统到引擎
    this.engine.addSystem(this.physicsSystem);
    
    // 获取物理预设管理器
    this.presetManager = PhysicsPresetManager.getInstance();
    
    // 初始化物理材质工厂
    PhysicsMaterialFactory.initialize();
    
    // 创建地面
    this.ground = this.createGround();
    
    // 添加地面到场景
    this.scene.addEntity(this.ground);
  }

  /**
   * 初始化示例
   */
  public initialize(): void {
    if (this.initialized) return;
    
    // 添加常用预设
    this.addCommonPresets();
    
    // 创建预设示例
    this.createPresetExamples();
    
    // 设置场景为活跃场景
    this.engine.getWorld().setActiveScene(this.scene);
    
    this.initialized = true;
  }
  
  /**
   * 添加常用预设
   */
  private addCommonPresets(): void {
    // 获取所有常用预设
    const presets = CommonPhysicsPresets.getAllPresets();
    
    // 添加到预设管理器
    for (const preset of presets) {
      this.presetManager.addPreset(preset);
    }
  }

  /**
   * 创建地面
   * @returns 地面实体
   */
  private createGround(): Entity {
    // 创建地面实体
    const ground = new Entity('ground');
    
    // 应用静态地面预设
    this.presetManager.applyBodyPreset(ground, 'static_ground');
    
    // 添加变换组件
    const transform = ground.getTransform();
    transform.setPosition(0, -0.5, 0);
    transform.setScale(10, 1, 10);
    
    // 创建地面网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ color: 0x808080 });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    ground.addComponent('MeshComponent', { mesh });
    
    return ground;
  }

  /**
   * 创建预设示例
   */
  private createPresetExamples(): void {
    // 创建不同预设的物体
    this.createPresetEntity('dynamic_box', -4, 5, -4);
    this.createPresetEntity('dynamic_sphere', -2, 5, -4);
    this.createPresetEntity('bouncy_ball', 0, 5, -4);
    this.createPresetEntity('trigger_zone', 2, 1, -4);
    this.createPresetEntity('kinematic_platform', 4, 1, -4);
    
    // 创建车辆预设示例
    this.createVehicleExample();
    
    // 创建角色预设示例
    this.createCharacterExample();
  }
  
  /**
   * 创建预设实体
   * @param presetName 预设名称
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   * @returns 实体
   */
  private createPresetEntity(presetName: string, x: number, y: number, z: number): Entity {
    // 获取预设
    const preset = this.presetManager.getPreset(presetName);
    if (!preset) {
      console.warn(`预设 "${presetName}" 不存在`);
      return null;
    }
    
    // 创建实体
    const entity = new Entity(`preset_${presetName}`);
    
    // 应用预设
    this.presetManager.applyBodyPreset(entity, presetName);
    this.presetManager.applyColliderPreset(entity, presetName);
    
    // 添加变换组件
    const transform = entity.getTransform();
    transform.setPosition(x, y, z);
    
    // 创建网格
    let mesh: THREE.Mesh;
    
    if (preset.colliderPreset.type === ColliderType.BOX) {
      // 盒子网格
      const size = preset.colliderPreset.params.halfExtents.x * 2;
      const geometry = new THREE.BoxGeometry(1, 1, 1);
      const material = new THREE.MeshStandardMaterial({ 
        color: new THREE.Color(Math.random(), Math.random(), Math.random()),
        transparent: preset.colliderPreset.isTrigger,
        opacity: preset.colliderPreset.isTrigger ? 0.5 : 1
      });
      mesh = new THREE.Mesh(geometry, material);
      transform.setScale(
        preset.colliderPreset.params.halfExtents.x * 2,
        preset.colliderPreset.params.halfExtents.y * 2,
        preset.colliderPreset.params.halfExtents.z * 2
      );
    } else if (preset.colliderPreset.type === ColliderType.SPHERE) {
      // 球体网格
      const radius = preset.colliderPreset.params.radius;
      const geometry = new THREE.SphereGeometry(1, 32, 32);
      const material = new THREE.MeshStandardMaterial({ 
        color: new THREE.Color(Math.random(), Math.random(), Math.random()),
        transparent: preset.colliderPreset.isTrigger,
        opacity: preset.colliderPreset.isTrigger ? 0.5 : 1
      });
      mesh = new THREE.Mesh(geometry, material);
      transform.setScale(radius * 2, radius * 2, radius * 2);
    } else {
      // 默认网格
      const geometry = new THREE.BoxGeometry(1, 1, 1);
      const material = new THREE.MeshStandardMaterial({ 
        color: new THREE.Color(Math.random(), Math.random(), Math.random()),
        transparent: preset.colliderPreset.isTrigger,
        opacity: preset.colliderPreset.isTrigger ? 0.5 : 1
      });
      mesh = new THREE.Mesh(geometry, material);
    }
    
    // 添加网格组件
    entity.addComponent('MeshComponent', { mesh });
    
    // 添加到场景
    this.scene.addEntity(entity);
    this.entities.push(entity);
    
    return entity;
  }
  
  /**
   * 创建车辆预设示例
   */
  private createVehicleExample(): void {
    // 创建底盘
    const chassis = new Entity('vehicle_chassis');
    this.presetManager.applyBodyPreset(chassis, 'car_chassis');
    
    // 添加变换组件
    const transform = chassis.getTransform();
    transform.setPosition(0, 2, 4);
    
    // 创建底盘网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ color: 0x3366cc });
    const mesh = new THREE.Mesh(geometry, material);
    chassis.addComponent('MeshComponent', { mesh });
    transform.setScale(2.4, 1, 5);
    
    // 添加到场景
    this.scene.addEntity(chassis);
    this.entities.push(chassis);
    
    // 创建车轮
    const wheelPositions = [
      { x: -1.2, y: 0, z: -2 },  // 左前
      { x: 1.2, y: 0, z: -2 },   // 右前
      { x: -1.2, y: 0, z: 2 },   // 左后
      { x: 1.2, y: 0, z: 2 }     // 右后
    ];
    
    for (let i = 0; i < wheelPositions.length; i++) {
      const wheel = new Entity(`vehicle_wheel_${i}`);
      this.presetManager.applyBodyPreset(wheel, 'car_wheel');
      
      // 添加变换组件
      const wheelTransform = wheel.getTransform();
      wheelTransform.setPosition(
        chassis.getTransform().position.x + wheelPositions[i].x,
        chassis.getTransform().position.y - 0.5,
        chassis.getTransform().position.z + wheelPositions[i].z
      );
      
      // 创建车轮网格
      const wheelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.2, 16);
      const wheelMaterial = new THREE.MeshStandardMaterial({ color: 0x333333 });
      const wheelMesh = new THREE.Mesh(wheelGeometry, wheelMaterial);
      wheelMesh.rotation.z = Math.PI / 2;
      wheel.addComponent('MeshComponent', { mesh: wheelMesh });
      
      // 添加到场景
      this.scene.addEntity(wheel);
      this.entities.push(wheel);
    }
  }
  
  /**
   * 创建角色预设示例
   */
  private createCharacterExample(): void {
    // 创建角色
    const character = new Entity('character');
    this.presetManager.applyBodyPreset(character, 'character_capsule');
    
    // 添加变换组件
    const transform = character.getTransform();
    transform.setPosition(4, 2, 0);
    
    // 创建角色网格
    const geometry = new THREE.CapsuleGeometry(0.4, 1.2, 8, 16);
    const material = new THREE.MeshStandardMaterial({ color: 0x66cc66 });
    const mesh = new THREE.Mesh(geometry, material);
    character.addComponent('MeshComponent', { mesh });
    
    // 添加到场景
    this.scene.addEntity(character);
    this.entities.push(character);
  }

  /**
   * 保存自定义预设
   */
  public saveCustomPreset(): void {
    // 选择一个实体
    if (this.entities.length === 0) return;
    
    const entity = this.entities[0];
    
    // 从实体创建预设
    const preset = this.presetManager.createBodyPresetFromEntity(
      entity,
      'custom_preset',
      '自定义预设',
      '自定义'
    );
    
    console.log('已保存自定义预设:', preset);
  }

  /**
   * 导出所有预设
   * @returns JSON字符串
   */
  public exportAllPresets(): string {
    return this.presetManager.exportAllPresets();
  }

  /**
   * 导入预设
   * @param json JSON字符串
   * @returns 导入的预设数量
   */
  public importPresets(json: string): number {
    return this.presetManager.importPresets(json);
  }

  /**
   * 重置场景
   */
  public reset(): void {
    // 移除所有实体
    for (const entity of this.entities) {
      this.scene.removeEntity(entity);
    }
    
    this.entities = [];
    
    // 重新创建示例
    this.createPresetExamples();
  }
}
