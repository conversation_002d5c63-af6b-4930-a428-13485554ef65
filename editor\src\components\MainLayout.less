.main-layout {
  height: 100vh;
  
  .header {
    display: flex;
    align-items: center;
    padding: 0 16px;
    
    .logo {
      width: 120px;
      color: white;
      font-size: 18px;
      font-weight: bold;
    }
    
    .ant-menu {
      flex: 1;
    }
    
    .header-right {
      display: flex;
      align-items: center;
      
      .ant-btn {
        margin-left: 8px;
      }
    }
  }
  
  .content {
    position: relative;
    height: 100%;
    overflow: hidden;
  }
  
  // 自定义rc-dock样式
  .dock-panel {
    background-color: #fff;
    border: 1px solid #f0f0f0;
    
    .dock-tab {
      background-color: #f5f5f5;
      
      &.dock-tab-active {
        background-color: #fff;
      }
    }
    
    .dock-tabbar {
      background-color: #f5f5f5;
    }
    
    .dock-content {
      padding: 8px;
    }
  }
  
  // 帮助和教程模态框样式
  .ant-modal-content {
    .ant-modal-body {
      .help-panel, .tutorial-panel {
        height: 100%;
      }
    }
  }
}
