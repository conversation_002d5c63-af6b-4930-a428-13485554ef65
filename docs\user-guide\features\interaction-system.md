# 交互系统

DL（Digital Learning）引擎编辑器的交互系统提供了丰富的工具和组件，用于创建用户与场景对象之间的交互。本文档将详细介绍如何设置和使用交互系统，包括交互类型、事件处理、高亮效果和抓取系统等功能。

## 交互系统概述

### 主要功能

DL（Digital Learning）引擎的交互系统支持以下主要功能：

- **交互检测**：检测用户与场景对象的交互
- **交互事件**：处理交互事件，如点击、悬停、接近等
- **交互提示**：显示交互提示，指导用户如何交互
- **交互高亮**：高亮显示可交互对象
- **抓取系统**：允许用户抓取和操作场景对象
- **交互约束**：限制交互的方式和范围
- **交互反馈**：提供视觉、音频和触觉反馈

### 交互组件

交互系统主要包括以下组件：

- **交互系统（InteractionSystem）**：管理所有交互组件和交互逻辑
- **可交互组件（InteractableComponent）**：标记对象为可交互对象
- **交互事件组件（InteractionEventComponent）**：处理交互事件
- **交互提示组件（InteractionPromptComponent）**：显示交互提示
- **交互高亮组件（InteractionHighlightComponent）**：控制高亮效果
- **抓取组件（GrabbableComponent）**：允许对象被抓取

## 设置交互系统

### 启用交互系统

1. 点击顶部菜单栏的"编辑 > 项目设置"
2. 在项目设置对话框中，选择"交互"选项卡
3. 勾选"启用交互系统"
4. 设置全局交互参数
5. 点击"应用"按钮

![交互系统设置](../../assets/images/interaction-system-settings.png)

### 全局交互参数

交互系统的全局参数包括：

- **最大交互距离**：用户可以与对象交互的最大距离
- **交互检测频率**：交互检测的更新频率
- **默认高亮颜色**：可交互对象的默认高亮颜色
- **默认提示样式**：交互提示的默认样式
- **启用触觉反馈**：是否启用触觉反馈（如支持的设备）
- **启用音频反馈**：是否启用音频反馈

## 创建可交互对象

### 添加可交互组件

1. 在场景中选择要设为可交互的对象
2. 在属性面板中，点击"添加组件"按钮
3. 选择"交互 > 可交互组件"
4. 设置交互属性

![可交互组件](../../assets/images/interactable-component.png)

### 可交互组件属性

可交互组件包含以下主要属性：

- **交互类型**：点击交互、接近交互、悬停交互等
- **交互标签**：描述交互对象的文本标签
- **交互提示**：显示给用户的交互提示文本
- **交互距离**：与此对象交互的最大距离
- **交互优先级**：当多个对象可交互时的优先级
- **交互条件**：触发交互的条件
- **交互回调**：交互触发时执行的函数

### 交互类型

DL（Digital Learning）引擎支持以下交互类型：

- **点击交互（Click）**：用户点击对象时触发
- **接近交互（Proximity）**：用户接近对象时触发
- **悬停交互（Hover）**：用户将鼠标悬停在对象上时触发
- **注视交互（Gaze）**：用户注视对象一段时间后触发
- **手势交互（Gesture）**：用户做特定手势时触发
- **语音交互（Voice）**：用户说特定语音命令时触发

### 设置点击交互示例

1. 选择场景中的对象
2. 添加"可交互组件"
3. 将"交互类型"设置为"点击交互"
4. 设置"交互标签"为描述性文本（如"开关"）
5. 设置"交互提示"为指导性文本（如"点击开关"）
6. 设置"交互距离"为适当值（如2米）
7. 点击"应用"按钮

## 交互事件

### 添加交互事件组件

1. 选择带有可交互组件的对象
2. 在属性面板中，点击"添加组件"按钮
3. 选择"交互 > 交互事件组件"
4. 设置事件处理函数

![交互事件组件](../../assets/images/interaction-event-component.png)

### 交互事件类型

交互系统支持以下事件类型：

- **交互开始（Interaction Start）**：交互开始时触发
- **交互结束（Interaction End）**：交互结束时触发
- **进入范围（Enter Range）**：用户进入交互范围时触发
- **离开范围（Exit Range）**：用户离开交互范围时触发
- **悬停开始（Hover Start）**：鼠标开始悬停时触发
- **悬停结束（Hover End）**：鼠标结束悬停时触发
- **选择（Select）**：对象被选择时触发
- **取消选择（Deselect）**：对象被取消选择时触发

### 设置事件处理

可以通过以下方式设置事件处理：

1. **可视化脚本**：使用视觉脚本编辑器创建事件处理逻辑
2. **组件方法**：在组件中定义事件处理方法
3. **JavaScript代码**：编写JavaScript代码处理事件

#### 使用视觉脚本示例

1. 选择带有交互事件组件的对象
2. 点击"编辑事件处理"按钮
3. 在视觉脚本编辑器中：
   - 添加"交互事件"节点
   - 选择要处理的事件类型
   - 连接到动作节点（如播放动画、改变颜色等）
4. 保存视觉脚本

#### 使用JavaScript代码示例

```javascript
// 获取交互事件组件
const interactionEvent = entity.getComponent('InteractionEvent');

// 添加事件监听器
interactionEvent.addEventListener('interactionStart', (event) => {
  console.log('交互开始', event);
  // 执行交互逻辑
  entity.getComponent('Light').intensity = 1.0;
});

interactionEvent.addEventListener('interactionEnd', (event) => {
  console.log('交互结束', event);
  // 执行交互结束逻辑
  entity.getComponent('Light').intensity = 0.5;
});
```

## 交互提示

### 添加交互提示组件

1. 选择带有可交互组件的对象
2. 在属性面板中，点击"添加组件"按钮
3. 选择"交互 > 交互提示组件"
4. 设置提示属性

![交互提示组件](../../assets/images/interaction-prompt-component.png)

### 交互提示属性

交互提示组件包含以下主要属性：

- **提示文本**：显示给用户的提示文本
- **提示图标**：与提示文本一起显示的图标
- **提示位置类型**：提示显示的位置（世界空间、屏幕空间、跟随对象）
- **位置偏移**：提示相对于对象的位置偏移
- **显示条件**：控制提示显示的条件
- **显示动画**：提示显示和隐藏的动画效果
- **样式模板**：预定义的提示样式模板

### 提示位置类型

DL（Digital Learning）引擎支持以下提示位置类型：

- **世界空间（World）**：提示显示在3D世界中的固定位置
- **屏幕空间（Screen）**：提示显示在屏幕上的固定位置
- **跟随对象（Follow）**：提示跟随对象移动

### 自定义提示样式

1. 在交互提示组件中，点击"自定义样式"按钮
2. 在样式编辑器中，可以设置：
   - **字体**：提示文本的字体
   - **字体大小**：提示文本的大小
   - **颜色**：提示文本和背景的颜色
   - **边框**：提示框的边框样式
   - **背景**：提示框的背景样式
   - **图标**：提示图标的样式和位置
3. 点击"保存为模板"可以保存为可重用的样式模板

## 交互高亮

### 添加交互高亮组件

1. 选择带有可交互组件的对象
2. 在属性面板中，点击"添加组件"按钮
3. 选择"交互 > 交互高亮组件"
4. 设置高亮属性

![交互高亮组件](../../assets/images/interaction-highlight-component.png)

### 高亮类型

DL（Digital Learning）引擎支持以下高亮类型：

- **轮廓高亮（Outline）**：在对象周围显示轮廓线
- **发光高亮（Glow）**：使对象发光
- **颜色高亮（Color）**：改变对象的颜色
- **脉冲高亮（Pulse）**：创建脉冲效果
- **自定义高亮（Custom）**：使用自定义着色器创建高亮效果

### 高亮属性

交互高亮组件包含以下主要属性：

- **高亮类型**：高亮的视觉效果类型
- **高亮颜色**：高亮效果的颜色
- **高亮强度**：高亮效果的强度
- **脉冲频率**：脉冲效果的频率
- **高亮条件**：控制高亮显示的条件
- **高亮过渡**：高亮效果的过渡动画
- **高亮材质**：用于高亮效果的材质

### 设置轮廓高亮示例

1. 选择带有可交互组件的对象
2. 添加"交互高亮组件"
3. 将"高亮类型"设置为"轮廓高亮"
4. 设置"高亮颜色"为所需颜色
5. 设置"高亮强度"为适当值
6. 设置"高亮条件"（如"悬停时"）
7. 点击"应用"按钮

## 抓取系统

### 添加抓取组件

1. 选择要设为可抓取的对象
2. 在属性面板中，点击"添加组件"按钮
3. 选择"交互 > 抓取组件"
4. 设置抓取属性

![抓取组件](../../assets/images/grabbable-component.png)

### 抓取类型

DL（Digital Learning）引擎支持以下抓取类型：

- **直接抓取（Direct）**：对象直接附加到控制器或光标
- **距离抓取（Distance）**：对象保持一定距离
- **弹簧抓取（Spring）**：对象通过弹簧连接，有物理效果
- **物理抓取（Physics）**：使用物理约束进行抓取

### 抓取属性

抓取组件包含以下主要属性：

- **抓取类型**：抓取的方式
- **抓取点**：对象被抓取的点
- **抓取距离**：距离抓取的默认距离
- **抓取约束**：抓取时的移动和旋转约束
- **抓取条件**：允许抓取的条件
- **抓取反馈**：抓取时的视觉和触觉反馈
- **抓取事件**：抓取开始和结束时触发的事件

### 设置物理抓取示例

1. 选择带有刚体组件的对象
2. 添加"抓取组件"
3. 将"抓取类型"设置为"物理抓取"
4. 设置"抓取点"为对象的适当位置
5. 设置"抓取约束"（如允许旋转但限制移动）
6. 点击"应用"按钮

### 抓取交互

用户可以通过以下方式与可抓取对象交互：

1. **鼠标抓取**：点击并按住鼠标按钮
2. **触摸抓取**：触摸并按住屏幕
3. **VR控制器抓取**：使用VR控制器的抓取按钮
4. **手势抓取**：使用手势识别的抓取手势

### 抓取事件处理

```javascript
// 获取抓取组件
const grabbable = entity.getComponent('Grabbable');

// 添加抓取事件监听器
grabbable.addEventListener('grabStart', (event) => {
  console.log('抓取开始', event);
  // 执行抓取开始逻辑
  entity.getComponent('AudioSource').play('pickup_sound');
});

grabbable.addEventListener('grabEnd', (event) => {
  console.log('抓取结束', event);
  // 执行抓取结束逻辑
  entity.getComponent('AudioSource').play('drop_sound');
});
```

## 交互约束

### 添加交互约束组件

1. 选择带有可交互或可抓取组件的对象
2. 在属性面板中，点击"添加组件"按钮
3. 选择"交互 > 交互约束组件"
4. 设置约束属性

![交互约束组件](../../assets/images/interaction-constraint-component.png)

### 约束类型

DL（Digital Learning）引擎支持以下交互约束类型：

- **轴约束（Axis）**：限制交互在特定轴上
- **平面约束（Plane）**：限制交互在特定平面上
- **旋转约束（Rotation）**：限制旋转角度
- **距离约束（Distance）**：限制交互距离
- **边界约束（Boundary）**：限制在特定边界内
- **网格约束（Grid）**：限制在网格点上
- **路径约束（Path）**：限制在预定义路径上

### 约束属性

交互约束组件包含以下主要属性：

- **约束类型**：约束的类型
- **约束参数**：特定约束类型的参数
- **约束空间**：约束应用的坐标空间（局部/全局）
- **约束条件**：约束生效的条件
- **约束过渡**：约束的过渡动画
- **约束反馈**：约束生效时的反馈

### 设置轴约束示例

1. 选择带有可抓取组件的对象
2. 添加"交互约束组件"
3. 将"约束类型"设置为"轴约束"
4. 设置"约束轴"为所需轴（如Y轴）
5. 设置"约束空间"（如"全局空间"）
6. 点击"应用"按钮

## 交互反馈

### 添加交互反馈组件

1. 选择带有可交互组件的对象
2. 在属性面板中，点击"添加组件"按钮
3. 选择"交互 > 交互反馈组件"
4. 设置反馈属性

![交互反馈组件](../../assets/images/interaction-feedback-component.png)

### 反馈类型

DL（Digital Learning）引擎支持以下交互反馈类型：

- **视觉反馈**：高亮、颜色变化、动画等
- **音频反馈**：音效、语音提示等
- **触觉反馈**：振动、力反馈等
- **UI反馈**：UI元素变化、提示等

### 反馈属性

交互反馈组件包含以下主要属性：

- **视觉反馈设置**：控制视觉反馈效果
- **音频反馈设置**：控制音频反馈效果
- **触觉反馈设置**：控制触觉反馈效果
- **UI反馈设置**：控制UI反馈效果
- **反馈条件**：触发反馈的条件
- **反馈延迟**：反馈触发的延迟时间
- **反馈持续时间**：反馈效果的持续时间

### 设置音频反馈示例

1. 选择带有可交互组件的对象
2. 添加"交互反馈组件"
3. 在"音频反馈设置"中：
   - 设置"交互开始音效"
   - 设置"交互结束音效"
   - 调整音量和音调参数
4. 点击"应用"按钮

## 最佳实践

### 交互设计原则

- **一致性**：保持交互方式和反馈的一致性
- **可发现性**：使可交互对象易于发现
- **反馈**：提供清晰的交互反馈
- **容错**：设计容错的交互机制
- **自然映射**：使交互方式与预期结果自然对应
- **简单性**：保持交互简单直观

### 性能优化

- 使用适当的交互检测频率
- 对远处对象使用简化的交互检测
- 使用交互LOD（细节层次）系统
- 优化高亮效果的渲染性能
- 使用事件委托减少事件监听器数量

### 可访问性考虑

- 提供多种交互方式（点击、键盘、语音等）
- 使用清晰的视觉和音频提示
- 考虑色盲用户的高亮颜色选择
- 提供足够的交互时间和容错机制
- 支持辅助技术

## 故障排除

### 交互不响应

如果交互不响应：

1. 检查对象是否正确添加了可交互组件
2. 确认交互距离设置合理
3. 检查交互条件是否满足
4. 确保没有其他对象阻挡交互射线
5. 检查事件处理函数是否正确设置

### 高亮效果问题

如果高亮效果显示不正确：

1. 检查高亮组件设置
2. 确认对象的材质兼容所选高亮类型
3. 调整高亮强度和颜色
4. 检查高亮条件是否满足
5. 尝试不同的高亮类型

## 下一步

现在您已经了解了交互系统的基本功能，可以继续学习其他相关功能：

- [物理系统](./physics-system.md)
- [动画系统](./animation-system.md)
- [视觉脚本](./visual-scripting.md)
- [交互高级技巧](../advanced/advanced-interaction.md)
