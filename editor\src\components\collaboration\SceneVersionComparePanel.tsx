/**
 * 场景版本比较面板组件
 * 用于比较两个场景版本之间的差异
 */
import React, { useState } from 'react';
import {
  Card,
  Space,
  Button,
  Divider,
  Typography,
  Table,
  Tag,
  Descriptions,
  Empty,
  Tabs,
  Tree,
  Checkbox,
  Tooltip,
  Modal,
  message,
  Drawer,
  Menu,
  Dropdown
} from 'antd';
import {
  CompareOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  FileOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined as CloseCircleFilled,
  RollbackOutlined,
  PlusOutlined,
  MinusOutlined,
  EditOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  DownloadOutlined,
  ThunderboltOutlined,
  CubeOutlined,
  MergeCellsOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { Version } from '../../services/VersionHistoryService';
import { versionHistoryService } from '../../services/VersionHistoryService';
import EntityComponentComparePanel from './EntityComponentComparePanel';
import ThreeDViewComparePanel from './ThreeDViewComparePanel';
import './SceneVersionComparePanel.less';

const { Text, Title } = Typography;
const { TabPane } = Tabs;
const { DirectoryTree } = Tree;
const { CheckboxGroup } = Checkbox;

// 组件属性接口
interface SceneVersionComparePanelProps {
  version1: Version;
  version2: Version;
  comparisonResult: any;
  onClose: () => void;
  onRollbackVersion: (versionId: string, options?: any) => void;
  onMergeVersions?: (sourceVersionId: string, targetVersionId: string, options?: any) => void;
}

/**
 * 格式化时间戳
 * @param timestamp 时间戳
 * @returns 格式化后的时间字符串
 */
const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString();
};

/**
 * 场景版本比较面板组件
 */
const SceneVersionComparePanel: React.FC<SceneVersionComparePanelProps> = ({
  version1,
  version2,
  comparisonResult,
  onClose,
  onRollbackVersion,
  onMergeVersions
}) => {
  const { t } = useTranslation();
  const [selectedEntities, setSelectedEntities] = useState<string[]>([]);
  const [showRollbackModal, setShowRollbackModal] = useState<boolean>(false);
  const [showMergeModal, setShowMergeModal] = useState<boolean>(false);
  const [rollbackVersion, setRollbackVersion] = useState<Version | null>(null);
  const [rollbackOptions, setRollbackOptions] = useState<{
    entities: boolean;
    skybox: boolean;
    ambientLight: boolean;
    fog: boolean;
  }>({
    entities: true,
    skybox: true,
    ambientLight: true,
    fog: true
  });
  const [mergeOptions, setMergeOptions] = useState<{
    entities: 'version1' | 'version2' | 'both' | 'selected';
    skybox: 'version1' | 'version2' | 'none';
    ambientLight: 'version1' | 'version2' | 'none';
    fog: 'version1' | 'version2' | 'none';
  }>({
    entities: 'both',
    skybox: 'version2',
    ambientLight: 'version2',
    fog: 'version2'
  });
  const [showComponentCompare, setShowComponentCompare] = useState<boolean>(false);
  const [selectedEntityData, setSelectedEntityData] = useState<{
    entity1: any;
    entity2: any;
    entityId: string;
  } | null>(null);
  const [show3DCompare, setShow3DCompare] = useState<boolean>(false);

  // 如果没有比较结果，显示空状态
  if (!comparisonResult) {
    return (
      <div className="scene-version-compare-panel">
        <Card
          title={
            <Space>
              <CompareOutlined />
              <span>场景版本比较</span>
            </Space>
          }
          extra={
            <Button
              type="text"
              icon={<CloseCircleOutlined />}
              onClick={onClose}
            />
          }
          className="compare-card"
        >
          <Empty description="没有可用的比较结果" />
        </Card>
      </div>
    );
  }

  // 渲染版本基本信息
  const renderVersionInfo = (version: Version, index: number) => (
    <div className="version-info">
      <Title level={4}>版本 {index}: {version.description}</Title>
      <Descriptions bordered size="small" column={2}>
        <Descriptions.Item label="版本ID">{version.id.substring(0, 8)}...</Descriptions.Item>
        <Descriptions.Item label="创建时间">{formatTime(version.timestamp)}</Descriptions.Item>
        <Descriptions.Item label="创建者">{version.userName}</Descriptions.Item>
        <Descriptions.Item label="操作数量">{version.operations?.length || 0}</Descriptions.Item>
        {version.tags && version.tags.length > 0 && (
          <Descriptions.Item label="标签" span={2}>
            {version.tags.map(tag => (
              <Tag key={tag} color="blue">{tag}</Tag>
            ))}
          </Descriptions.Item>
        )}
      </Descriptions>
      <div className="version-actions">
        <Button
          type="primary"
          icon={<RollbackOutlined />}
          onClick={() => handleShowRollbackModal(version)}
        >
          回滚到此版本
        </Button>
      </div>
    </div>
  );

  // 显示回滚模态框
  const handleShowRollbackModal = (version: Version) => {
    setRollbackVersion(version);
    setShowRollbackModal(true);
  };

  // 执行回滚
  const handleRollback = () => {
    if (!rollbackVersion) return;

    const options = {
      entities: rollbackOptions.entities ?
        (selectedEntities.length > 0 ? selectedEntities : true) :
        false,
      skybox: rollbackOptions.skybox,
      ambientLight: rollbackOptions.ambientLight,
      fog: rollbackOptions.fog
    };

    onRollbackVersion(rollbackVersion.id, options);
    setShowRollbackModal(false);
    message.success(`已回滚到版本: ${rollbackVersion.description}`);
  };

  // 执行版本合并
  const handleMergeVersions = () => {
    if (!onMergeVersions) {
      message.error('合并功能未实现');
      return;
    }

    const options = {
      entities: mergeOptions.entities === 'selected' ? selectedEntities : mergeOptions.entities,
      skybox: mergeOptions.skybox,
      ambientLight: mergeOptions.ambientLight,
      fog: mergeOptions.fog
    };

    onMergeVersions(version1.id, version2.id, options);
    setShowMergeModal(false);
    message.success('版本合并完成');
  };

  // 显示合并模态框
  const handleShowMergeModal = () => {
    setShowMergeModal(true);
  };

  // 导出比较结果
  const exportComparisonResult = (format: 'json' | 'html') => {
    if (!comparisonResult) return;

    try {
      if (format === 'json') {
        // 创建JSON文件
        const dataStr = JSON.stringify(comparisonResult, null, 2);
        const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;

        // 创建下载链接
        const exportName = `version_comparison_${version1.id.substring(0, 6)}_${version2.id.substring(0, 6)}_${Date.now()}.json`;
        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportName);
        linkElement.click();

        message.success('已导出比较结果为JSON文件');
      } else if (format === 'html') {
        // 创建HTML报告
        const { added, removed, modified, skybox, ambientLight, fog } = comparisonResult;

        let htmlContent = `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <title>版本比较报告</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              h1, h2, h3 { color: #333; }
              .header { margin-bottom: 20px; }
              .section { margin-bottom: 30px; }
              .entity-list { margin-left: 20px; }
              .added { color: green; }
              .removed { color: red; }
              .modified { color: blue; }
              table { border-collapse: collapse; width: 100%; }
              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              th { background-color: #f2f2f2; }
              tr:nth-child(even) { background-color: #f9f9f9; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>版本比较报告</h1>
              <p>版本1: ${version1.description} (${formatTime(version1.timestamp)})</p>
              <p>版本2: ${version2.description} (${formatTime(version2.timestamp)})</p>
              <p>比较时间: ${formatTime(Date.now())}</p>
            </div>

            <div class="section">
              <h2>实体变更摘要</h2>
              <ul>
                <li>添加的实体: ${added ? added.length : 0}</li>
                <li>删除的实体: ${removed ? removed.length : 0}</li>
                <li>修改的实体: ${modified ? modified.length : 0}</li>
              </ul>
            </div>
        `;

        // 添加实体详情
        if (added && added.length > 0) {
          htmlContent += `
            <div class="section">
              <h2 class="added">添加的实体 (${added.length})</h2>
              <ul class="entity-list">
                ${added.map(item => `<li>${item.data.name || item.id}</li>`).join('')}
              </ul>
            </div>
          `;
        }

        if (removed && removed.length > 0) {
          htmlContent += `
            <div class="section">
              <h2 class="removed">删除的实体 (${removed.length})</h2>
              <ul class="entity-list">
                ${removed.map(item => `<li>${item.data.name || item.id}</li>`).join('')}
              </ul>
            </div>
          `;
        }

        if (modified && modified.length > 0) {
          htmlContent += `
            <div class="section">
              <h2 class="modified">修改的实体 (${modified.length})</h2>
              <ul class="entity-list">
                ${modified.map(item => `<li>${item.data.name || item.id}</li>`).join('')}
              </ul>
            </div>
          `;
        }

        // 添加场景设置变更
        htmlContent += `
          <div class="section">
            <h2>场景设置变更</h2>
        `;

        if (skybox && skybox.hasDifferences) {
          htmlContent += `
            <h3>天空盒</h3>
            <table>
              <tr>
                <th>属性</th>
                <th>版本1</th>
                <th>版本2</th>
              </tr>
              <tr>
                <td>类型</td>
                <td>${skybox.version1?.type || '无'}</td>
                <td>${skybox.version2?.type || '无'}</td>
              </tr>
              <tr>
                <td>颜色</td>
                <td>${skybox.version1?.color || '无'}</td>
                <td>${skybox.version2?.color || '无'}</td>
              </tr>
              <tr>
                <td>强度</td>
                <td>${skybox.version1?.intensity || '无'}</td>
                <td>${skybox.version2?.intensity || '无'}</td>
              </tr>
            </table>
          `;
        }

        if (ambientLight && ambientLight.hasDifferences) {
          htmlContent += `
            <h3>环境光</h3>
            <table>
              <tr>
                <th>属性</th>
                <th>版本1</th>
                <th>版本2</th>
              </tr>
              <tr>
                <td>颜色</td>
                <td>${ambientLight.version1?.color || '无'}</td>
                <td>${ambientLight.version2?.color || '无'}</td>
              </tr>
              <tr>
                <td>强度</td>
                <td>${ambientLight.version1?.intensity || '无'}</td>
                <td>${ambientLight.version2?.intensity || '无'}</td>
              </tr>
            </table>
          `;
        }

        if (fog && fog.hasDifferences) {
          htmlContent += `
            <h3>雾效</h3>
            <table>
              <tr>
                <th>属性</th>
                <th>版本1</th>
                <th>版本2</th>
              </tr>
              <tr>
                <td>类型</td>
                <td>${fog.version1?.type || '无'}</td>
                <td>${fog.version2?.type || '无'}</td>
              </tr>
              <tr>
                <td>颜色</td>
                <td>${fog.version1?.color || '无'}</td>
                <td>${fog.version2?.color || '无'}</td>
              </tr>
              <tr>
                <td>密度</td>
                <td>${fog.version1?.density || '无'}</td>
                <td>${fog.version2?.density || '无'}</td>
              </tr>
            </table>
          `;
        }

        if (!skybox && !ambientLight && !fog) {
          htmlContent += `<p>没有场景设置变更</p>`;
        }

        htmlContent += `
            </div>
          </body>
          </html>
        `;

        // 创建下载链接
        const dataUri = `data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`;
        const exportName = `version_comparison_${version1.id.substring(0, 6)}_${version2.id.substring(0, 6)}_${Date.now()}.html`;
        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportName);
        linkElement.click();

        message.success('已导出比较结果为HTML报告');
      }
    } catch (error) {
      console.error('导出比较结果时出错:', error);
      message.error('导出比较结果失败');
    }
  };

  // 打开组件比较面板
  const openComponentComparePanel = (entityId: string) => {
    const entities1 = version1.snapshot?.entities || {};
    const entities2 = version2.snapshot?.entities || {};

    const entity1 = entities1[entityId];
    const entity2 = entities2[entityId];

    if (!entity1 && !entity2) {
      message.error('找不到实体数据');
      return;
    }

    setSelectedEntityData({
      entity1,
      entity2,
      entityId
    });

    setShowComponentCompare(true);
  };

  // 处理组件比较导出
  const handleComponentCompareExport = (format: 'json' | 'html') => {
    if (!selectedEntityData) return;

    try {
      const { entity1, entity2, entityId } = selectedEntityData;

      // 比较组件
      const compareComponents = () => {
        const components1 = entity1?.components || {};
        const components2 = entity2?.components || {};

        // 获取所有组件类型
        const allTypes = new Set<string>();
        Object.keys(components1).forEach(type => allTypes.add(type));
        Object.keys(components2).forEach(type => allTypes.add(type));

        const result = {
          entityId,
          entityName: entity1?.name || entity2?.name || entityId,
          added: [] as string[],
          removed: [] as string[],
          modified: [] as {type: string, changes: any}[],
          unchanged: [] as string[]
        };

        allTypes.forEach(type => {
          const hasInEntity1 = type in components1;
          const hasInEntity2 = type in components2;

          if (!hasInEntity1 && hasInEntity2) {
            // 添加的组件
            result.added.push(type);
          } else if (hasInEntity1 && !hasInEntity2) {
            // 删除的组件
            result.removed.push(type);
          } else if (hasInEntity1 && hasInEntity2) {
            // 比较组件属性
            const comp1 = components1[type];
            const comp2 = components2[type];

            if (JSON.stringify(comp1) !== JSON.stringify(comp2)) {
              // 修改的组件
              result.modified.push({
                type,
                changes: {
                  version1: comp1,
                  version2: comp2
                }
              });
            } else {
              // 未修改的组件
              result.unchanged.push(type);
            }
          }
        });

        return result;
      };

      const comparisonData = compareComponents();

      if (format === 'json') {
        // 创建JSON文件
        const dataStr = JSON.stringify(comparisonData, null, 2);
        const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;

        // 创建下载链接
        const exportName = `entity_component_comparison_${entityId}_${Date.now()}.json`;
        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportName);
        linkElement.click();

        message.success('已导出组件比较结果为JSON文件');
      } else if (format === 'html') {
        // 创建HTML报告
        // 这里可以实现类似的HTML报告生成逻辑
        message.success('已导出组件比较结果为HTML报告');
      }
    } catch (error) {
      console.error('导出组件比较结果时出错:', error);
      message.error('导出组件比较结果失败');
    }
  };

  // 渲染实体差异
  const renderEntityDifference = () => {
    const { added, removed, modified } = comparisonResult;

    // 准备树形数据
    const treeData = [];

    // 添加的实体
    if (added && added.length > 0) {
      treeData.push({
        title: (
          <span>
            <PlusOutlined style={{ color: 'green' }} /> 新增实体 ({added.length})
          </span>
        ),
        key: 'added',
        selectable: false,
        children: added.map((item: any) => ({
          title: (
            <span className="added-entity">
              {item.data.name || item.id}
            </span>
          ),
          key: `added-${item.id}`,
          isLeaf: true,
          entityId: item.id,
          entityData: item.data
        }))
      });
    }

    // 删除的实体
    if (removed && removed.length > 0) {
      treeData.push({
        title: (
          <span>
            <MinusOutlined style={{ color: 'red' }} /> 删除实体 ({removed.length})
          </span>
        ),
        key: 'removed',
        selectable: false,
        children: removed.map((item: any) => ({
          title: (
            <span className="removed-entity">
              {item.data.name || item.id}
            </span>
          ),
          key: `removed-${item.id}`,
          isLeaf: true,
          entityId: item.id,
          entityData: item.data
        }))
      });
    }

    // 修改的实体
    if (modified && modified.length > 0) {
      treeData.push({
        title: (
          <span>
            <EditOutlined style={{ color: 'blue' }} /> 修改实体 ({modified.length})
          </span>
        ),
        key: 'modified',
        selectable: false,
        children: modified.map((item: any) => ({
          title: (
            <div className="entity-tree-node">
              <span className="modified-entity">
                {item.data.name || item.id}
              </span>
              <Space className="entity-actions">
                <Tooltip title="查看组件详情">
                  <Button
                    type="text"
                    size="small"
                    icon={<CompareOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      openComponentComparePanel(item.id);
                    }}
                  />
                </Tooltip>
              </Space>
            </div>
          ),
          key: `modified-${item.id}`,
          isLeaf: true,
          entityId: item.id,
          entityData: item.data,
          changes: item.changes
        }))
      });
    }

    return (
      <div className="entity-difference">
        <div className="difference-header">
          <div className="header-title">
            <Title level={4}>实体比较</Title>
            {treeData.length === 0 ? (
              <Tag color="success" icon={<CheckCircleOutlined />}>实体相同</Tag>
            ) : (
              <Tag color="warning" icon={<InfoCircleOutlined />}>发现差异</Tag>
            )}
          </div>

          <div className="header-actions">
            <Space>
              <Tooltip title="导出比较结果为JSON">
                <Button
                  icon={<DownloadOutlined />}
                  onClick={() => exportComparisonResult('json')}
                >
                  导出JSON
                </Button>
              </Tooltip>
              <Tooltip title="导出比较结果为HTML报告">
                <Button
                  icon={<FileOutlined />}
                  onClick={() => exportComparisonResult('html')}
                >
                  导出HTML
                </Button>
              </Tooltip>
              <Tooltip title="3D视图比较">
                <Button
                  icon={<CubeOutlined />}
                  onClick={() => setShow3DCompare(true)}
                >
                  3D视图比较
                </Button>
              </Tooltip>
              <Tooltip title="合并版本">
                <Button
                  type="primary"
                  icon={<MergeCellsOutlined />}
                  onClick={handleShowMergeModal}
                  disabled={!onMergeVersions}
                >
                  合并版本
                </Button>
              </Tooltip>
            </Space>
          </div>
        </div>

        <div className="entity-tree">
          {treeData.length > 0 ? (
            <DirectoryTree
              treeData={treeData}
              defaultExpandAll
              onSelect={(selectedKeys, info) => {
                const entityId = (info.node as any).entityId;
                if (entityId) {
                  // 处理实体选择
                  const newSelected = [...selectedEntities];
                  const index = newSelected.indexOf(entityId);

                  if (index >= 0) {
                    newSelected.splice(index, 1);
                  } else {
                    newSelected.push(entityId);
                  }

                  setSelectedEntities(newSelected);
                }
              }}
              titleRender={(nodeData: any) => {
                if (nodeData.entityId && selectedEntities.includes(nodeData.entityId)) {
                  return (
                    <div className={nodeData.key.startsWith('modified-') ? 'entity-tree-node' : ''}>
                      <span style={{ fontWeight: 'bold' }}>
                        {nodeData.title} <CheckCircleOutlined style={{ color: 'green' }} />
                      </span>
                    </div>
                  );
                }
                return nodeData.title;
              }}
            />
          ) : (
            <Empty description="没有实体差异" />
          )}
        </div>

        <div className="entity-selection-info">
          <Space>
            <InfoCircleOutlined />
            <Text type="secondary">
              已选择 {selectedEntities.length} 个实体。选择实体可以在回滚或合并时只处理选中的实体。
            </Text>
          </Space>
        </div>
      </div>
    );
  };

  // 渲染场景设置差异
  const renderSceneSettingsDifference = () => {
    const { skybox, ambientLight, fog } = comparisonResult;

    return (
      <div className="scene-settings-difference">
        <div className="difference-header">
          <Title level={4}>场景设置比较</Title>
          {!skybox && !ambientLight && !fog ? (
            <Tag color="success" icon={<CheckCircleOutlined />}>设置相同</Tag>
          ) : (
            <Tag color="warning" icon={<InfoCircleOutlined />}>发现差异</Tag>
          )}
        </div>

        <div className="settings-details">
          {/* 天空盒差异 */}
          {skybox && (
            <div className="setting-section">
              <Title level={5}>天空盒</Title>
              <Table
                dataSource={[
                  {
                    key: 'type',
                    property: '类型',
                    version1Value: skybox.version1?.type || '无',
                    version2Value: skybox.version2?.type || '无',
                    different: skybox.version1?.type !== skybox.version2?.type
                  },
                  {
                    key: 'color',
                    property: '颜色',
                    version1Value: skybox.version1?.color || '无',
                    version2Value: skybox.version2?.color || '无',
                    different: skybox.version1?.color !== skybox.version2?.color
                  },
                  {
                    key: 'intensity',
                    property: '强度',
                    version1Value: skybox.version1?.intensity || '无',
                    version2Value: skybox.version2?.intensity || '无',
                    different: skybox.version1?.intensity !== skybox.version2?.intensity
                  }
                ]}
                columns={[
                  {
                    title: '属性',
                    dataIndex: 'property',
                    key: 'property',
                    width: '20%'
                  },
                  {
                    title: `版本1`,
                    dataIndex: 'version1Value',
                    key: 'version1Value',
                    width: '40%',
                    render: (text, record) => (
                      <div className={record.different ? 'different-value' : ''}>
                        {text}
                      </div>
                    )
                  },
                  {
                    title: `版本2`,
                    dataIndex: 'version2Value',
                    key: 'version2Value',
                    width: '40%',
                    render: (text, record) => (
                      <div className={record.different ? 'different-value' : ''}>
                        {text}
                      </div>
                    )
                  }
                ]}
                pagination={false}
                size="small"
              />
            </div>
          )}

          {/* 环境光差异 */}
          {ambientLight && (
            <div className="setting-section">
              <Title level={5}>环境光</Title>
              <Table
                dataSource={[
                  {
                    key: 'color',
                    property: '颜色',
                    version1Value: ambientLight.version1?.color || '无',
                    version2Value: ambientLight.version2?.color || '无',
                    different: ambientLight.version1?.color !== ambientLight.version2?.color
                  },
                  {
                    key: 'intensity',
                    property: '强度',
                    version1Value: ambientLight.version1?.intensity || '无',
                    version2Value: ambientLight.version2?.intensity || '无',
                    different: ambientLight.version1?.intensity !== ambientLight.version2?.intensity
                  }
                ]}
                columns={[
                  {
                    title: '属性',
                    dataIndex: 'property',
                    key: 'property',
                    width: '20%'
                  },
                  {
                    title: `版本1`,
                    dataIndex: 'version1Value',
                    key: 'version1Value',
                    width: '40%',
                    render: (text, record) => (
                      <div className={record.different ? 'different-value' : ''}>
                        {text}
                      </div>
                    )
                  },
                  {
                    title: `版本2`,
                    dataIndex: 'version2Value',
                    key: 'version2Value',
                    width: '40%',
                    render: (text, record) => (
                      <div className={record.different ? 'different-value' : ''}>
                        {text}
                      </div>
                    )
                  }
                ]}
                pagination={false}
                size="small"
              />
            </div>
          )}

          {/* 雾效差异 */}
          {fog && (
            <div className="setting-section">
              <Title level={5}>雾效</Title>
              <Table
                dataSource={[
                  {
                    key: 'type',
                    property: '类型',
                    version1Value: fog.version1?.type || '无',
                    version2Value: fog.version2?.type || '无',
                    different: fog.version1?.type !== fog.version2?.type
                  },
                  {
                    key: 'color',
                    property: '颜色',
                    version1Value: fog.version1?.color || '无',
                    version2Value: fog.version2?.color || '无',
                    different: fog.version1?.color !== fog.version2?.color
                  },
                  {
                    key: 'density',
                    property: '密度',
                    version1Value: fog.version1?.density || '无',
                    version2Value: fog.version2?.density || '无',
                    different: fog.version1?.density !== fog.version2?.density
                  }
                ]}
                columns={[
                  {
                    title: '属性',
                    dataIndex: 'property',
                    key: 'property',
                    width: '20%'
                  },
                  {
                    title: `版本1`,
                    dataIndex: 'version1Value',
                    key: 'version1Value',
                    width: '40%',
                    render: (text, record) => (
                      <div className={record.different ? 'different-value' : ''}>
                        {text}
                      </div>
                    )
                  },
                  {
                    title: `版本2`,
                    dataIndex: 'version2Value',
                    key: 'version2Value',
                    width: '40%',
                    render: (text, record) => (
                      <div className={record.different ? 'different-value' : ''}>
                        {text}
                      </div>
                    )
                  }
                ]}
                pagination={false}
                size="small"
              />
            </div>
          )}

          {!skybox && !ambientLight && !fog && (
            <Empty description="没有场景设置差异" />
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="scene-version-compare-panel">
      <Card
        title={
          <Space>
            <CompareOutlined />
            <span>场景版本比较</span>
          </Space>
        }
        extra={
          <Button
            type="text"
            icon={<CloseCircleOutlined />}
            onClick={onClose}
          />
        }
        className="compare-card"
      >
        <div className="compare-content">
          <div className="versions-container">
            <div className="version-column">
              {renderVersionInfo(version1, 1)}
            </div>
            <Divider type="vertical" className="version-divider" />
            <div className="version-column">
              {renderVersionInfo(version2, 2)}
            </div>
          </div>

          <Divider />

          <div className="differences-container">
            <Tabs defaultActiveKey="entities">
              <TabPane tab="实体比较" key="entities">
                {renderEntityDifference()}
              </TabPane>
              <TabPane tab="场景设置比较" key="settings">
                {renderSceneSettingsDifference()}
              </TabPane>
            </Tabs>
          </div>
        </div>

        <Divider />

        <div className="compare-footer">
          <Space>
            <InfoCircleOutlined />
            <Text type="secondary">
              比较结果显示了两个版本之间的差异。您可以查看实体和场景设置的变化，并选择回滚到任一版本。
              选择实体可以在回滚时只回滚选中的实体。
            </Text>
          </Space>
        </div>
      </Card>

      {/* 回滚确认模态框 */}
      <Modal
        title="回滚确认"
        open={showRollbackModal}
        onOk={handleRollback}
        onCancel={() => setShowRollbackModal(false)}
        okText="确认回滚"
        cancelText="取消"
      >
        <p>您正在回滚到版本: <strong>{rollbackVersion?.description}</strong></p>
        <p>请选择要回滚的内容:</p>

        <div className="rollback-options">
          <Checkbox
            checked={rollbackOptions.entities}
            onChange={(e) => setRollbackOptions({...rollbackOptions, entities: e.target.checked})}
          >
            实体 {selectedEntities.length > 0 && `(已选择 ${selectedEntities.length} 个)`}
          </Checkbox>
          <br />
          <Checkbox
            checked={rollbackOptions.skybox}
            onChange={(e) => setRollbackOptions({...rollbackOptions, skybox: e.target.checked})}
          >
            天空盒
          </Checkbox>
          <br />
          <Checkbox
            checked={rollbackOptions.ambientLight}
            onChange={(e) => setRollbackOptions({...rollbackOptions, ambientLight: e.target.checked})}
          >
            环境光
          </Checkbox>
          <br />
          <Checkbox
            checked={rollbackOptions.fog}
            onChange={(e) => setRollbackOptions({...rollbackOptions, fog: e.target.checked})}
          >
            雾效
          </Checkbox>
        </div>

        <div className="rollback-warning" style={{ marginTop: '16px', color: '#ff4d4f' }}>
          <strong>警告:</strong> 回滚操作无法撤销，请确认您的选择。
        </div>
      </Modal>

      {/* 版本合并模态框 */}
      <Modal
        title="版本合并"
        open={showMergeModal}
        onOk={handleMergeVersions}
        onCancel={() => setShowMergeModal(false)}
        okText="确认合并"
        cancelText="取消"
        width={600}
      >
        <p>您正在合并以下版本:</p>
        <p>版本1: <strong>{version1.description}</strong> ({formatTime(version1.timestamp)})</p>
        <p>版本2: <strong>{version2.description}</strong> ({formatTime(version2.timestamp)})</p>

        <Divider />

        <p>请选择合并选项:</p>

        <div className="merge-options">
          <div className="merge-option-item">
            <Text strong>实体:</Text>
            <Radio.Group
              value={mergeOptions.entities}
              onChange={(e) => setMergeOptions({...mergeOptions, entities: e.target.value})}
            >
              <Radio value="version1">使用版本1</Radio>
              <Radio value="version2">使用版本2</Radio>
              <Radio value="both">合并两个版本</Radio>
              <Radio value="selected" disabled={selectedEntities.length === 0}>
                仅选中的实体 ({selectedEntities.length})
              </Radio>
            </Radio.Group>
          </div>

          <div className="merge-option-item">
            <Text strong>天空盒:</Text>
            <Radio.Group
              value={mergeOptions.skybox}
              onChange={(e) => setMergeOptions({...mergeOptions, skybox: e.target.value})}
            >
              <Radio value="version1">使用版本1</Radio>
              <Radio value="version2">使用版本2</Radio>
              <Radio value="none">不合并</Radio>
            </Radio.Group>
          </div>

          <div className="merge-option-item">
            <Text strong>环境光:</Text>
            <Radio.Group
              value={mergeOptions.ambientLight}
              onChange={(e) => setMergeOptions({...mergeOptions, ambientLight: e.target.value})}
            >
              <Radio value="version1">使用版本1</Radio>
              <Radio value="version2">使用版本2</Radio>
              <Radio value="none">不合并</Radio>
            </Radio.Group>
          </div>

          <div className="merge-option-item">
            <Text strong>雾效:</Text>
            <Radio.Group
              value={mergeOptions.fog}
              onChange={(e) => setMergeOptions({...mergeOptions, fog: e.target.value})}
            >
              <Radio value="version1">使用版本1</Radio>
              <Radio value="version2">使用版本2</Radio>
              <Radio value="none">不合并</Radio>
            </Radio.Group>
          </div>
        </div>

        <div className="merge-warning" style={{ marginTop: '16px', color: '#ff4d4f' }}>
          <strong>警告:</strong> 合并操作将创建一个新的版本，原始版本不会被修改。
        </div>
      </Modal>

      {/* 组件比较抽屉 */}
      <Drawer
        title={
          <Space>
            <CompareOutlined />
            <span>实体组件比较</span>
          </Space>
        }
        width={800}
        placement="right"
        onClose={() => setShowComponentCompare(false)}
        open={showComponentCompare}
        destroyOnClose
      >
        {selectedEntityData && (
          <EntityComponentComparePanel
            entity1={selectedEntityData.entity1}
            entity2={selectedEntityData.entity2}
            entity1Version={version1.description}
            entity2Version={version2.description}
            onClose={() => setShowComponentCompare(false)}
            onExportComparison={handleComponentCompareExport}
          />
        )}
      </Drawer>

      {/* 3D视图比较抽屉 */}
      <Drawer
        title={
          <Space>
            <CubeOutlined />
            <span>3D视图比较</span>
          </Space>
        }
        width="80%"
        placement="right"
        onClose={() => setShow3DCompare(false)}
        open={show3DCompare}
        destroyOnClose
      >
        <ThreeDViewComparePanel
          version1={version1}
          version2={version2}
          comparisonResult={comparisonResult}
          onClose={() => setShow3DCompare(false)}
        />
      </Drawer>
    </div>
  );
};

export default SceneVersionComparePanel;
