<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>场景优化示例 - DL（Digital Learning）引擎</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      background-color: #000;
      font-family: Arial, sans-serif;
    }
    
    #canvas-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    
    #loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-size: 24px;
      text-align: center;
    }
    
    #loading .spinner {
      width: 40px;
      height: 40px;
      margin: 20px auto;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div id="canvas-container"></div>
  
  <div id="loading">
    <div>加载中...</div>
    <div class="spinner"></div>
  </div>
  
  <script type="module">
    import { SceneOptimizationExample } from './SceneOptimizationExample';
    
    // 等待DOM加载完成
    document.addEventListener('DOMContentLoaded', async () => {
      // 获取容器
      const container = document.getElementById('canvas-container');
      
      // 创建示例
      const example = new SceneOptimizationExample();
      
      try {
        // 初始化示例
        await example.initialize();
        
        // 隐藏加载提示
        document.getElementById('loading').style.display = 'none';
      } catch (error) {
        console.error('初始化失败:', error);
        document.getElementById('loading').innerHTML = `
          <div>加载失败</div>
          <div>${error.message}</div>
        `;
      }
      
      // 添加窗口大小调整事件
      window.addEventListener('resize', () => {
        // 调整渲染器大小
        const renderer = example.getEngine().getRenderer();
        if (renderer) {
          renderer.setSize(window.innerWidth, window.innerHeight);
        }
      });
      
      // 添加卸载事件
      window.addEventListener('beforeunload', () => {
        // 销毁示例
        example.dispose();
      });
    });
  </script>
</body>
</html>
