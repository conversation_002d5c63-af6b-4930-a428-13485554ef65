/**
 * 资源依赖可视化面板样式
 */
.resource-dependency-visualizer-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .visualizer-header {
    padding: 12px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
  }

  .ant-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .ant-tabs-content {
      flex: 1;
      overflow: hidden;

      .ant-tabs-tabpane {
        height: 100%;
        overflow: auto;
      }
    }
  }

  .dependency-graph {
    width: 100%;
    height: 100%;
    min-height: 500px;
    background-color: #f9f9f9;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    overflow: hidden;
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 300px;
  }

  .graph-controls {
    background-color: rgba(255, 255, 255, 0.8);
    padding: 8px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .analysis-results {
    padding: 16px;

    .analysis-card {
      height: 100%;
      margin-bottom: 16px;
    }

    .analysis-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      padding-bottom: 8px;
      border-bottom: 1px dashed #f0f0f0;

      &:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
      }

      .analysis-label {
        color: #666;
      }

      .analysis-value {
        font-weight: 500;
      }
    }
  }

  .optimization-suggestions {
    padding: 16px;

    .suggestion-card {
      margin-bottom: 16px;

      .suggestion-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .suggestion-title {
          margin-left: 8px;
          font-weight: 500;
        }
      }

      .suggestion-content {
        .suggestion-section {
          margin-bottom: 8px;

          .suggestion-label {
            font-weight: 500;
            margin-bottom: 4px;
          }

          .suggestion-value {
            color: #666;
          }
        }
      }
    }
  }

  .resource-details {
    .detail-section {
      margin-bottom: 16px;

      .detail-label {
        font-weight: 500;
        margin-bottom: 4px;
      }

      .detail-value {
        color: #666;
        word-break: break-all;
      }
    }
  }

  .visualizer-settings {
    .setting-section {
      margin-bottom: 24px;

      .setting-label {
        font-weight: 500;
        margin-bottom: 8px;
      }

      .setting-value {
        .ant-radio-group {
          display: flex;
          flex-direction: column;

          .ant-radio-button-wrapper {
            margin-bottom: 8px;
          }
        }
      }
    }
  }
}

// 节点样式
.resource-node {
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  background-color: white;
  width: 150px;
  font-size: 12px;

  .node-header {
    font-weight: bold;
    margin-bottom: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .node-type {
    color: #666;
    margin-bottom: 5px;
  }

  .node-size {
    color: #666;
  }

  &.texture {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
  }

  &.model {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
  }

  &.material {
    background-color: #fffbe6;
    border: 1px solid #ffd666;
  }

  &.audio {
    background-color: #f0f5ff;
    border: 1px solid #adc6ff;
  }

  &.shader {
    background-color: #fff0f6;
    border: 1px solid #ffadd2;
  }

  &.selected {
    border: 2px solid #1890ff;
  }
}

// 边样式
.dependency-edge {
  &.strong {
    stroke: #1890ff;
    stroke-width: 2px;
  }

  &.weak {
    stroke: #52c41a;
    stroke-width: 1px;
    stroke-dasharray: 5, 5;
  }

  &.lazy {
    stroke: #faad14;
    stroke-width: 1px;
    stroke-dasharray: 3, 3;
  }

  &.preload {
    stroke: #722ed1;
    stroke-width: 1px;
  }
}
