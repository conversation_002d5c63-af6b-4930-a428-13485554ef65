/**
 * 物理材质编辑器组件
 * 用于编辑物理材质属性
 */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Form, 
  Input, 
  InputNumber, 
  Button, 
  Card, 
  Tabs, 
  Space, 
  Select, 
  Slider, 
  Switch, 
  Divider, 
  Tooltip, 
  Modal, 
  message 
} from 'antd';
import { 
  PlusOutlined, 
  DeleteOutlined, 
  SaveOutlined, 
  CopyOutlined, 
  ImportOutlined, 
  ExportOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { PhysicsMaterialPreset } from '../../../engine/src/physics/presets/PhysicsPreset';

const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 物理材质编辑器属性
 */
interface PhysicsMaterialEditorProps {
  /** 当前选中的材质名称 */
  selectedMaterial?: string;
  /** 材质变更回调 */
  onMaterialChange?: (materialName: string) => void;
  /** 是否显示预览 */
  showPreview?: boolean;
}

/**
 * 物理材质编辑器组件
 */
const PhysicsMaterialEditor: React.FC<PhysicsMaterialEditorProps> = ({
  selectedMaterial = 'default',
  onMaterialChange,
  showPreview = true
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // 从Redux获取材质列表
  const materials = useSelector((state: RootState) => state.physics.materials);
  
  // 表单实例
  const [form] = Form.useForm();
  
  // 当前选中的材质
  const [currentMaterial, setCurrentMaterial] = useState<string>(selectedMaterial);
  
  // 新材质名称
  const [newMaterialName, setNewMaterialName] = useState<string>('');
  
  // 是否显示新建材质对话框
  const [showNewMaterialModal, setShowNewMaterialModal] = useState<boolean>(false);
  
  // 是否显示导入对话框
  const [showImportModal, setShowImportModal] = useState<boolean>(false);
  
  // 导入JSON
  const [importJson, setImportJson] = useState<string>('');
  
  // 预览参数
  const [previewParams, setPreviewParams] = useState({
    friction: 0.3,
    restitution: 0.3,
    stiffness: 1e6,
    relaxation: 3
  });
  
  // 当前材质数据
  const currentMaterialData = materials.find(m => m.name === currentMaterial) || {
    name: 'default',
    friction: 0.3,
    restitution: 0.3,
    contactEquationStiffness: 1e6,
    contactEquationRelaxation: 3,
    frictionEquationStiffness: 1e6,
    frictionEquationRelaxation: 3
  };
  
  // 当选中的材质变化时，更新表单
  useEffect(() => {
    if (currentMaterial) {
      form.setFieldsValue({
        name: currentMaterialData.name,
        friction: currentMaterialData.friction,
        restitution: currentMaterialData.restitution,
        contactEquationStiffness: currentMaterialData.contactEquationStiffness,
        contactEquationRelaxation: currentMaterialData.contactEquationRelaxation,
        frictionEquationStiffness: currentMaterialData.frictionEquationStiffness,
        frictionEquationRelaxation: currentMaterialData.frictionEquationRelaxation
      });
      
      // 更新预览参数
      setPreviewParams({
        friction: currentMaterialData.friction,
        restitution: currentMaterialData.restitution,
        stiffness: currentMaterialData.contactEquationStiffness,
        relaxation: currentMaterialData.contactEquationRelaxation
      });
    }
  }, [currentMaterial, currentMaterialData, form]);
  
  // 表单值变化时更新预览
  const handleValuesChange = (changedValues: any, allValues: any) => {
    setPreviewParams({
      friction: allValues.friction,
      restitution: allValues.restitution,
      stiffness: allValues.contactEquationStiffness,
      relaxation: allValues.contactEquationRelaxation
    });
  };
  
  // 保存材质
  const handleSaveMaterial = () => {
    form.validateFields().then(values => {
      // 创建材质数据
      const materialData: PhysicsMaterialPreset = {
        name: values.name,
        friction: values.friction,
        restitution: values.restitution,
        contactEquationStiffness: values.contactEquationStiffness,
        contactEquationRelaxation: values.contactEquationRelaxation,
        frictionEquationStiffness: values.frictionEquationStiffness,
        frictionEquationRelaxation: values.frictionEquationRelaxation
      };
      
      // 派发保存材质动作
      dispatch({
        type: 'physics/saveMaterial',
        payload: materialData
      });
      
      message.success(t('editor.physics.materialSaved', { name: values.name }));
    });
  };
  
  // 创建新材质
  const handleCreateMaterial = () => {
    if (!newMaterialName.trim()) {
      message.error(t('editor.physics.materialNameRequired'));
      return;
    }
    
    // 检查名称是否已存在
    if (materials.some(m => m.name === newMaterialName)) {
      message.error(t('editor.physics.materialNameExists'));
      return;
    }
    
    // 创建新材质
    const newMaterial: PhysicsMaterialPreset = {
      name: newMaterialName,
      friction: 0.3,
      restitution: 0.3,
      contactEquationStiffness: 1e6,
      contactEquationRelaxation: 3,
      frictionEquationStiffness: 1e6,
      frictionEquationRelaxation: 3
    };
    
    // 派发添加材质动作
    dispatch({
      type: 'physics/addMaterial',
      payload: newMaterial
    });
    
    // 选择新材质
    setCurrentMaterial(newMaterialName);
    if (onMaterialChange) {
      onMaterialChange(newMaterialName);
    }
    
    // 关闭对话框
    setShowNewMaterialModal(false);
    setNewMaterialName('');
    
    message.success(t('editor.physics.materialCreated', { name: newMaterialName }));
  };
  
  // 删除材质
  const handleDeleteMaterial = () => {
    if (currentMaterial === 'default') {
      message.error(t('editor.physics.cannotDeleteDefaultMaterial'));
      return;
    }
    
    Modal.confirm({
      title: t('editor.physics.confirmDeleteMaterial'),
      content: t('editor.physics.confirmDeleteMaterialContent', { name: currentMaterial }),
      onOk: () => {
        // 派发删除材质动作
        dispatch({
          type: 'physics/deleteMaterial',
          payload: currentMaterial
        });
        
        // 选择默认材质
        setCurrentMaterial('default');
        if (onMaterialChange) {
          onMaterialChange('default');
        }
        
        message.success(t('editor.physics.materialDeleted', { name: currentMaterial }));
      }
    });
  };
  
  // 复制材质
  const handleDuplicateMaterial = () => {
    // 创建新名称
    const newName = `${currentMaterial}_copy`;
    
    // 复制当前材质数据
    const materialData: PhysicsMaterialPreset = {
      ...currentMaterialData,
      name: newName
    };
    
    // 派发添加材质动作
    dispatch({
      type: 'physics/addMaterial',
      payload: materialData
    });
    
    // 选择新材质
    setCurrentMaterial(newName);
    if (onMaterialChange) {
      onMaterialChange(newName);
    }
    
    message.success(t('editor.physics.materialDuplicated', { name: newName }));
  };
  
  // 导出材质
  const handleExportMaterial = () => {
    try {
      // 创建JSON
      const json = JSON.stringify(currentMaterialData, null, 2);
      
      // 创建下载链接
      const blob = new Blob([json], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${currentMaterial}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      message.success(t('editor.physics.materialExported', { name: currentMaterial }));
    } catch (error) {
      message.error(t('editor.physics.exportError'));
      console.error('导出材质错误:', error);
    }
  };
  
  // 导入材质
  const handleImportMaterial = () => {
    try {
      // 解析JSON
      const materialData = JSON.parse(importJson);
      
      // 验证数据
      if (!materialData.name || typeof materialData.friction !== 'number' || typeof materialData.restitution !== 'number') {
        message.error(t('editor.physics.invalidMaterialData'));
        return;
      }
      
      // 检查名称是否已存在
      if (materials.some(m => m.name === materialData.name)) {
        // 添加后缀
        materialData.name = `${materialData.name}_imported`;
      }
      
      // 派发添加材质动作
      dispatch({
        type: 'physics/addMaterial',
        payload: materialData
      });
      
      // 选择导入的材质
      setCurrentMaterial(materialData.name);
      if (onMaterialChange) {
        onMaterialChange(materialData.name);
      }
      
      // 关闭对话框
      setShowImportModal(false);
      setImportJson('');
      
      message.success(t('editor.physics.materialImported', { name: materialData.name }));
    } catch (error) {
      message.error(t('editor.physics.importError'));
      console.error('导入材质错误:', error);
    }
  };
  
  // 材质选择变更
  const handleMaterialSelectChange = (value: string) => {
    setCurrentMaterial(value);
    if (onMaterialChange) {
      onMaterialChange(value);
    }
  };
  
  // 渲染材质预览
  const renderMaterialPreview = () => {
    if (!showPreview) return null;
    
    return (
      <Card title={t('editor.physics.materialPreview')} className="material-preview-card">
        <div className="material-preview">
          <div className="preview-container">
            <div 
              className="preview-object" 
              style={{ 
                transform: `translateY(${-50 * previewParams.restitution}px)`,
                transition: `transform ${0.5 / (previewParams.stiffness / 1e6)}s`
              }}
            />
            <div className="preview-surface" style={{ opacity: 1 - previewParams.friction }} />
          </div>
          <div className="preview-params">
            <div>{t('editor.physics.friction')}: {previewParams.friction.toFixed(2)}</div>
            <div>{t('editor.physics.restitution')}: {previewParams.restitution.toFixed(2)}</div>
            <div>{t('editor.physics.stiffness')}: {previewParams.stiffness.toExponential(1)}</div>
            <div>{t('editor.physics.relaxation')}: {previewParams.relaxation.toFixed(1)}</div>
          </div>
        </div>
      </Card>
    );
  };
  
  return (
    <div className="physics-material-editor">
      <div className="material-editor-header">
        <Select
          value={currentMaterial}
          onChange={handleMaterialSelectChange}
          style={{ width: 200 }}
        >
          {materials.map(material => (
            <Option key={material.name} value={material.name}>{material.name}</Option>
          ))}
        </Select>
        <Space>
          <Button 
            icon={<PlusOutlined />} 
            onClick={() => setShowNewMaterialModal(true)}
            title={t('editor.physics.newMaterial')}
          />
          <Button 
            icon={<DeleteOutlined />} 
            onClick={handleDeleteMaterial}
            disabled={currentMaterial === 'default'}
            title={t('editor.physics.deleteMaterial')}
          />
          <Button 
            icon={<CopyOutlined />} 
            onClick={handleDuplicateMaterial}
            title={t('editor.physics.duplicateMaterial')}
          />
          <Button 
            icon={<ImportOutlined />} 
            onClick={() => setShowImportModal(true)}
            title={t('editor.physics.importMaterial')}
          />
          <Button 
            icon={<ExportOutlined />} 
            onClick={handleExportMaterial}
            title={t('editor.physics.exportMaterial')}
          />
        </Space>
      </div>
      
      <div className="material-editor-content">
        <div className="material-form">
          <Form
            form={form}
            layout="vertical"
            onValuesChange={handleValuesChange}
            initialValues={{
              name: currentMaterialData.name,
              friction: currentMaterialData.friction,
              restitution: currentMaterialData.restitution,
              contactEquationStiffness: currentMaterialData.contactEquationStiffness,
              contactEquationRelaxation: currentMaterialData.contactEquationRelaxation,
              frictionEquationStiffness: currentMaterialData.frictionEquationStiffness,
              frictionEquationRelaxation: currentMaterialData.frictionEquationRelaxation
            }}
          >
            <Form.Item
              name="name"
              label={t('editor.physics.materialName')}
              rules={[{ required: true, message: t('editor.physics.materialNameRequired') }]}
            >
              <Input disabled={currentMaterial === 'default'} />
            </Form.Item>
            
            <Tabs defaultActiveKey="basic">
              <TabPane tab={t('editor.physics.basicProperties')} key="basic">
                <Form.Item
                  name="friction"
                  label={
                    <span>
                      {t('editor.physics.friction')}
                      <Tooltip title={t('editor.physics.frictionTooltip')}>
                        <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                      </Tooltip>
                    </span>
                  }
                >
                  <Slider
                    min={0}
                    max={1}
                    step={0.01}
                    marks={{ 0: '0', 0.5: '0.5', 1: '1' }}
                  />
                </Form.Item>
                
                <Form.Item
                  name="restitution"
                  label={
                    <span>
                      {t('editor.physics.restitution')}
                      <Tooltip title={t('editor.physics.restitutionTooltip')}>
                        <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                      </Tooltip>
                    </span>
                  }
                >
                  <Slider
                    min={0}
                    max={1}
                    step={0.01}
                    marks={{ 0: '0', 0.5: '0.5', 1: '1' }}
                  />
                </Form.Item>
              </TabPane>
              
              <TabPane tab={t('editor.physics.advancedProperties')} key="advanced">
                <Form.Item
                  name="contactEquationStiffness"
                  label={
                    <span>
                      {t('editor.physics.contactStiffness')}
                      <Tooltip title={t('editor.physics.contactStiffnessTooltip')}>
                        <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                      </Tooltip>
                    </span>
                  }
                >
                  <InputNumber
                    min={1e4}
                    max={1e8}
                    step={1e5}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
                
                <Form.Item
                  name="contactEquationRelaxation"
                  label={
                    <span>
                      {t('editor.physics.contactRelaxation')}
                      <Tooltip title={t('editor.physics.contactRelaxationTooltip')}>
                        <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                      </Tooltip>
                    </span>
                  }
                >
                  <InputNumber
                    min={1}
                    max={20}
                    step={0.1}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
                
                <Form.Item
                  name="frictionEquationStiffness"
                  label={
                    <span>
                      {t('editor.physics.frictionStiffness')}
                      <Tooltip title={t('editor.physics.frictionStiffnessTooltip')}>
                        <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                      </Tooltip>
                    </span>
                  }
                >
                  <InputNumber
                    min={1e4}
                    max={1e8}
                    step={1e5}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
                
                <Form.Item
                  name="frictionEquationRelaxation"
                  label={
                    <span>
                      {t('editor.physics.frictionRelaxation')}
                      <Tooltip title={t('editor.physics.frictionRelaxationTooltip')}>
                        <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                      </Tooltip>
                    </span>
                  }
                >
                  <InputNumber
                    min={1}
                    max={20}
                    step={0.1}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </TabPane>
            </Tabs>
            
            <Form.Item>
              <Button 
                type="primary" 
                icon={<SaveOutlined />} 
                onClick={handleSaveMaterial}
              >
                {t('editor.physics.saveMaterial')}
              </Button>
            </Form.Item>
          </Form>
        </div>
        
        {renderMaterialPreview()}
      </div>
      
      {/* 新建材质对话框 */}
      <Modal
        title={t('editor.physics.newMaterial')}
        open={showNewMaterialModal}
        onOk={handleCreateMaterial}
        onCancel={() => setShowNewMaterialModal(false)}
      >
        <Form layout="vertical">
          <Form.Item
            label={t('editor.physics.materialName')}
            required
            rules={[{ required: true, message: t('editor.physics.materialNameRequired') }]}
          >
            <Input 
              value={newMaterialName} 
              onChange={e => setNewMaterialName(e.target.value)} 
              placeholder={t('editor.physics.enterMaterialName')}
            />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 导入材质对话框 */}
      <Modal
        title={t('editor.physics.importMaterial')}
        open={showImportModal}
        onOk={handleImportMaterial}
        onCancel={() => setShowImportModal(false)}
      >
        <Form layout="vertical">
          <Form.Item
            label={t('editor.physics.materialJson')}
            required
          >
            <Input.TextArea 
              value={importJson} 
              onChange={e => setImportJson(e.target.value)} 
              placeholder={t('editor.physics.enterMaterialJson')}
              rows={10}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PhysicsMaterialEditor;
