# 着色器参考文档

本文档提供了DL（Digital Learning）引擎编辑器中所有内置着色器的详细参考信息，包括着色器的功能、属性、变体和使用示例。

## 目录

- [标准着色器](#标准着色器)
- [PBR着色器](#pbr着色器)
- [无光照着色器](#无光照着色器)
- [透明着色器](#透明着色器)
- [天空盒着色器](#天空盒着色器)
- [后处理着色器](#后处理着色器)
- [粒子着色器](#粒子着色器)
- [UI着色器](#ui着色器)
- [自定义着色器](#自定义着色器)

## 标准着色器

标准着色器是DL（Digital Learning）引擎中最常用的着色器，支持基本的光照模型和纹理映射。它实现了Blinn-Phong光照模型，适用于大多数非PBR渲染场景。

### 属性

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| color | Color | (1, 1, 1, 1) | 主颜色（漫反射颜色） |
| mainTexture | Texture2D | null | 主纹理（漫反射贴图） |
| normalMap | Texture2D | null | 法线贴图，用于增加表面细节 |
| normalScale | float | 1.0 | 法线贴图强度 |
| specularMap | Texture2D | null | 高光贴图，控制表面反射强度 |
| emissionMap | Texture2D | null | 自发光贴图，定义自发光区域 |
| emissionColor | Color | (0, 0, 0, 1) | 自发光颜色，与自发光贴图相乘 |
| specularColor | Color | (0.5, 0.5, 0.5, 1) | 高光颜色，定义反射光的颜色 |
| shininess | float | 32.0 | 光泽度，控制高光区域大小（值越大，高光越集中） |
| occlusionMap | Texture2D | null | 遮挡贴图，模拟环境光遮挡 |
| occlusionStrength | float | 1.0 | 遮挡强度 |
| tiling | Vector2 | (1, 1) | 纹理平铺，控制纹理重复次数 |
| offset | Vector2 | (0, 0) | 纹理偏移，控制纹理位置 |
| alphaClip | float | 0.0 | Alpha裁剪阈值，低于此值的像素将被丢弃 |
| renderMode | enum | Opaque | 渲染模式（不透明、透明、裁剪） |
| cullMode | enum | Back | 面剔除模式（正面、背面、双面） |
| receiveShadows | boolean | true | 是否接收阴影 |
| environmentReflection | enum | Off | 环境反射模式（关闭、简单、高质量） |
| reflectionIntensity | float | 0.5 | 环境反射强度 |
| rimColor | Color | (0, 0, 0, 0) | 边缘光颜色 |
| rimPower | float | 3.0 | 边缘光强度（值越大，边缘光越窄） |
| heightMap | Texture2D | null | 高度贴图，用于视差映射 |
| heightScale | float | 0.05 | 高度贴图缩放，控制视差效果强度 |
| detailMap | Texture2D | null | 细节贴图，增加近距离细节 |
| detailScale | float | 1.0 | 细节贴图缩放 |

### 变体

| 变体名 | 描述 |
|-------|------|
| STANDARD_NORMAL_MAP | 启用法线贴图，增加表面细节 |
| STANDARD_SPECULAR_MAP | 启用高光贴图，控制表面反射 |
| STANDARD_EMISSION_MAP | 启用自发光贴图，添加自发光效果 |
| STANDARD_OCCLUSION_MAP | 启用遮挡贴图，增强环境光遮挡 |
| STANDARD_DETAIL_MAP | 启用细节贴图，增加近距离细节 |
| STANDARD_HEIGHT_MAP | 启用高度贴图，实现视差效果 |
| STANDARD_ALPHA_CLIP | 启用Alpha裁剪，适用于植被等物体 |
| STANDARD_TRANSPARENT | 启用透明渲染，适用于玻璃等物体 |
| STANDARD_RIM_LIGHT | 启用边缘光效果，增加轮廓感 |
| STANDARD_ENV_REFLECTION | 启用环境反射，增加真实感 |
| STANDARD_RECEIVE_SHADOWS | 启用阴影接收，增强场景深度感 |

### 使用示例

```typescript
// 创建标准材质
const material = new Material('StandardMaterial', 'Standard');

// 设置基本属性
material.setColor('color', new Color(1, 0, 0, 1)); // 红色
material.setTexture('mainTexture', AssetManager.getTexture('textures/diffuse.png'));
material.setFloat('shininess', 64.0);

// 启用法线贴图
material.setTexture('normalMap', AssetManager.getTexture('textures/normal.png'));
material.setFloat('normalScale', 1.2); // 增强法线效果
material.enableKeyword('STANDARD_NORMAL_MAP');

// 启用高光贴图
material.setTexture('specularMap', AssetManager.getTexture('textures/specular.png'));
material.setColor('specularColor', new Color(0.8, 0.8, 0.8, 1));
material.enableKeyword('STANDARD_SPECULAR_MAP');

// 启用自发光
material.setTexture('emissionMap', AssetManager.getTexture('textures/emission.png'));
material.setColor('emissionColor', new Color(0, 0.5, 1, 1)); // 蓝色发光
material.enableKeyword('STANDARD_EMISSION_MAP');

// 启用边缘光
material.setColor('rimColor', new Color(0.3, 0, 0, 1)); // 暗红色边缘光
material.setFloat('rimPower', 5.0);
material.enableKeyword('STANDARD_RIM_LIGHT');

// 设置渲染模式
material.setEnum('renderMode', 'Opaque');
material.setEnum('cullMode', 'Back');

// 设置纹理平铺和偏移
material.setVector2('tiling', new Vector2(2, 2)); // 纹理重复2x2次
material.setVector2('offset', new Vector2(0.5, 0)); // 水平偏移0.5
```

![标准着色器](../../assets/images/standard-shader.png)

### 性能考虑

标准着色器提供了多种功能，但并非所有功能都需要同时启用。以下是一些性能优化建议：

- **选择性启用变体**：只启用需要的变体，每增加一个变体都会增加着色器复杂度。
- **纹理大小**：根据对象在场景中的重要性和距离选择适当的纹理分辨率。
- **法线贴图**：对于远处或不重要的对象，可以考虑不使用法线贴图。
- **自发光**：自发光物体可能需要额外的渲染通道，影响性能。
- **透明度**：透明渲染比不透明渲染更消耗性能，尤其是多层透明物体。

### 常见问题

**问题**：材质看起来太暗或太亮？
**解答**：检查光照设置和材质的颜色值。标准着色器依赖场景光照，确保场景中有适当的光源。

**问题**：法线贴图效果不明显？
**解答**：尝试增加`normalScale`值，或检查法线贴图是否正确（RGB通道对应XYZ方向）。

**问题**：透明物体渲染顺序错误？
**解答**：透明物体需要从后到前渲染，调整`renderOrder`属性或考虑使用Alpha测试而非Alpha混合。

## PBR着色器

基于物理的渲染(PBR)着色器，提供更真实的光照和材质表现。PBR着色器基于物理原理模拟光线与表面的交互，使材质在不同光照条件下都能保持一致的外观。

### 属性

| 属性名 | 类型 | 默认值 | 描述 |
|-------|------|-------|------|
| albedoColor | Color | (1, 1, 1, 1) | 反照率颜色，表面的基础颜色 |
| albedoMap | Texture2D | null | 反照率贴图，定义表面颜色分布 |
| normalMap | Texture2D | null | 法线贴图，增加表面细节而不增加几何复杂度 |
| normalScale | float | 1.0 | 法线贴图强度，控制法线效果的强弱 |
| metallicMap | Texture2D | null | 金属度贴图，定义表面哪些部分是金属（白色） |
| roughnessMap | Texture2D | null | 粗糙度贴图，定义表面的微观粗糙程度 |
| metallicRoughnessMap | Texture2D | null | 组合贴图，R通道为金属度，G通道为粗糙度 |
| aoMap | Texture2D | null | 环境光遮蔽贴图，模拟缝隙和凹陷处的阴影 |
| emissionMap | Texture2D | null | 自发光贴图，定义表面发光区域 |
| emissionColor | Color | (0, 0, 0, 1) | 自发光颜色，与自发光贴图相乘 |
| metallic | float | 0.0 | 金属度，0为非金属，1为金属 |
| roughness | float | 0.5 | 粗糙度，0为光滑，1为粗糙 |
| ao | float | 1.0 | 环境光遮蔽强度，控制AO效果强弱 |
| reflectance | float | 0.5 | 反射率，控制非金属表面的镜面反射强度 |
| clearCoat | float | 0.0 | 清漆层强度，模拟表面额外的透明涂层 |
| clearCoatRoughness | float | 0.1 | 清漆层粗糙度，控制清漆层的光滑程度 |
| anisotropy | float | 0.0 | 各向异性强度，用于创建方向性反射效果 |
| anisotropyDirection | Vector2 | (1, 0) | 各向异性方向，控制反射的方向性 |
| subsurface | float | 0.0 | 次表面散射强度，用于模拟半透明材质 |
| subsurfaceColor | Color | (1, 1, 1, 1) | 次表面散射颜色，控制散射光的颜色 |
| subsurfaceRadius | Vector3 | (1, 1, 1) | 次表面散射半径，控制不同颜色通道的散射距离 |
| tiling | Vector2 | (1, 1) | 纹理平铺，控制纹理重复次数 |
| offset | Vector2 | (0, 0) | 纹理偏移，控制纹理位置 |
| alphaClip | float | 0.0 | Alpha裁剪阈值，低于此值的像素将被丢弃 |
| renderMode | enum | Opaque | 渲染模式（不透明、透明、裁剪） |
| cullMode | enum | Back | 面剔除模式（正面、背面、双面） |
| environmentIntensity | float | 1.0 | 环境光照强度，控制环境反射和环境光的强度 |
| iridescence | float | 0.0 | 彩虹色效果强度，模拟薄膜干涉现象 |
| iridescenceThickness | float | 400.0 | 彩虹色薄膜厚度，控制彩虹色效果的颜色分布 |
| specularAA | boolean | false | 镜面反射抗锯齿，减少高光闪烁 |

### 变体

| 变体名 | 描述 |
|-------|------|
| PBR_NORMAL_MAP | 启用法线贴图，增加表面细节 |
| PBR_METALLIC_MAP | 启用金属度贴图，精确控制金属区域 |
| PBR_ROUGHNESS_MAP | 启用粗糙度贴图，精确控制表面粗糙度 |
| PBR_METALLIC_ROUGHNESS_MAP | 启用组合金属度/粗糙度贴图，节省纹理内存 |
| PBR_AO_MAP | 启用环境光遮蔽贴图，增强表面细节 |
| PBR_EMISSION_MAP | 启用自发光贴图，添加发光效果 |
| PBR_ALPHA_CLIP | 启用Alpha裁剪，适用于复杂形状如植被 |
| PBR_TRANSPARENT | 启用透明渲染，适用于玻璃等材质 |
| PBR_CLEAR_COAT | 启用清漆层，适用于汽车漆面等材质 |
| PBR_ANISOTROPIC | 启用各向异性，适用于拉丝金属等材质 |
| PBR_SUBSURFACE | 启用次表面散射，适用于皮肤、蜡等材质 |
| PBR_IRIDESCENCE | 启用彩虹色效果，适用于肥皂泡、油膜等材质 |
| PBR_SPECULAR_AA | 启用镜面反射抗锯齿，减少高光闪烁 |

### 使用示例

```typescript
// 创建PBR材质
const material = new Material('PBRMaterial', 'PBR');

// 设置基本属性
material.setColor('albedoColor', new Color(0.8, 0.8, 0.8, 1));
material.setTexture('albedoMap', AssetManager.getTexture('textures/albedo.png'));
material.setFloat('metallic', 0.8); // 高金属度，适合金属表面
material.setFloat('roughness', 0.2); // 较低粗糙度，表面较光滑

// 启用法线贴图
material.setTexture('normalMap', AssetManager.getTexture('textures/normal.png'));
material.setFloat('normalScale', 1.0);
material.enableKeyword('PBR_NORMAL_MAP');

// 启用金属度和粗糙度贴图
// 方式1：分别使用金属度和粗糙度贴图
material.setTexture('metallicMap', AssetManager.getTexture('textures/metallic.png'));
material.enableKeyword('PBR_METALLIC_MAP');
material.setTexture('roughnessMap', AssetManager.getTexture('textures/roughness.png'));
material.enableKeyword('PBR_ROUGHNESS_MAP');

// 方式2：使用组合贴图（更高效）
// material.setTexture('metallicRoughnessMap', AssetManager.getTexture('textures/metallic_roughness.png'));
// material.enableKeyword('PBR_METALLIC_ROUGHNESS_MAP');

// 启用环境光遮蔽
material.setTexture('aoMap', AssetManager.getTexture('textures/ao.png'));
material.setFloat('ao', 1.0);
material.enableKeyword('PBR_AO_MAP');

// 启用自发光
material.setTexture('emissionMap', AssetManager.getTexture('textures/emission.png'));
material.setColor('emissionColor', new Color(1, 0.2, 0, 1)); // 橙红色发光
material.enableKeyword('PBR_EMISSION_MAP');

// 启用清漆层（适用于汽车漆面等）
material.setFloat('clearCoat', 0.5);
material.setFloat('clearCoatRoughness', 0.05);
material.enableKeyword('PBR_CLEAR_COAT');

// 设置渲染模式
material.setEnum('renderMode', 'Opaque');
```

![PBR着色器](../../assets/images/pbr-shader.png)

### 材质预设

DL（Digital Learning）引擎提供了一系列PBR材质预设，可以快速创建常见材质：

```typescript
// 创建金属材质预设
const goldMaterial = MaterialPresets.createMetal('Gold', new Color(1.0, 0.765, 0.336, 1.0), 0.1);

// 创建塑料材质预设
const redPlasticMaterial = MaterialPresets.createPlastic('RedPlastic', new Color(0.8, 0.0, 0.0, 1.0), 0.2);

// 创建木材材质预设
const woodMaterial = MaterialPresets.createWood('Wood', AssetManager.getTexture('textures/wood_albedo.png'));

// 创建皮肤材质预设（带次表面散射）
const skinMaterial = MaterialPresets.createSkin('Skin', AssetManager.getTexture('textures/skin_albedo.png'));

// 创建布料材质预设
const fabricMaterial = MaterialPresets.createFabric('Fabric', AssetManager.getTexture('textures/fabric_albedo.png'));
```

### 性能考虑

PBR着色器比标准着色器更复杂，需要更多的计算资源。以下是一些性能优化建议：

- **使用组合贴图**：使用metallicRoughnessMap代替单独的metallicMap和roughnessMap可以减少纹理采样次数。
- **选择性启用高级特性**：清漆层、各向异性、次表面散射和彩虹色效果都会增加着色器复杂度，只在必要时启用。
- **纹理分辨率**：根据对象在场景中的重要性选择适当的纹理分辨率。
- **LOD系统**：对于远处的对象，使用简化的材质变体或降低纹理分辨率。
- **预计算环境光照**：使用光照探针和反射探针预计算环境光照，减少实时计算。

### 常见问题

**问题**：PBR材质看起来不真实？
**解答**：PBR材质需要基于物理的光照环境才能正确渲染。确保场景使用了HDR环境贴图或适当的光照探针。

**问题**：金属材质看起来像塑料？
**解答**：金属材质的albedo应该接近黑色或暗灰色，反射主要来自金属度参数。检查金属度值是否接近1.0。

**问题**：材质在不同平台上看起来不一致？
**解答**：PBR着色器在不同平台上可能有细微差异。确保使用相同的光照模型和环境设置进行测试。
