/**
 * UI状态切片
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 定义面板类型
export enum PanelType {
  HIERARCHY = 'hierarchy',
  INSPECTOR = 'inspector',
  ASSETS = 'assets',
  SCENE = 'scene',
  CONSOLE = 'console',
  ANIMATION = 'animation',
  PHYSICS = 'physics',
  PARTICLE = 'particle',
  LAYERS = 'layers',
  INSTANCES = 'instances',
  COLLABORATION = 'collaboration',
  USER_TESTING = 'userTesting',
  DEBUG = 'debug',
  PERFORMANCE_OPTIMIZATION = 'performanceOptimization',
  RESOURCE_HOT_UPDATE = 'resourceHotUpdate',
  ENVIRONMENT = 'environment',
  GIT = 'git',
}

// 定义面板位置
export enum PanelPosition {
  LEFT = 'left',
  RIGHT = 'right',
  BOTTOM = 'bottom',
  CENTER = 'center',
}

// 定义面板接口
export interface Panel {
  id: string;
  type: PanelType;
  position: PanelPosition;
  isVisible: boolean;
  size: number; // 百分比或像素
}

// 定义对话框类型
export enum DialogType {
  NEW_PROJECT = 'newProject',
  OPEN_PROJECT = 'openProject',
  SAVE_PROJECT_AS = 'saveProjectAs',
  PROJECT_SETTINGS = 'projectSettings',
  EDITOR_SETTINGS = 'editorSettings',
  IMPORT_ASSET = 'importAsset',
  EXPORT_SCENE = 'exportScene',
  PUBLISH_PROJECT = 'publishProject',
  CONFIRM = 'confirm',
  ALERT = 'alert',
}

// 定义对话框接口
export interface Dialog {
  type: DialogType;
  isOpen: boolean;
  title: string;
  message?: string;
  data?: any;
  onConfirm?: () => void;
  onCancel?: () => void;
}

// 定义主题类型
export enum ThemeType {
  LIGHT = 'light',
  DARK = 'dark',
  SYSTEM = 'system',
}

// 定义视口模式
export enum ViewportMode {
  SELECT = 'select',
  TRANSLATE = 'translate',
  ROTATE = 'rotate',
  SCALE = 'scale',
}

// 定义渲染模式
export enum RenderMode {
  SOLID = 'solid',
  WIREFRAME = 'wireframe',
  TEXTURED = 'textured',
}

// 定义UI状态
interface UIState {
  panels: Panel[];
  dialogs: Dialog[];
  theme: 'light' | 'dark'; // 简化主题类型，与rc-dock兼容
  language: string;
  sidebarCollapsed: boolean;
  fullscreen: boolean;
  menuVisible: boolean;
  viewportMode: ViewportMode;
  renderMode: RenderMode;
  contextMenu: {
    isOpen: boolean;
    x: number;
    y: number;
    items: any[];
  };
  notifications: {
    id: string;
    type: 'success' | 'info' | 'warning' | 'error';
    message: string;
    duration?: number;
  }[];
  // 添加布局相关状态
  layout: any; // 当前布局
  savedLayouts: Record<string, any>; // 保存的布局
  activeLayout: string; // 当前激活的布局名称
}

// 初始面板配置
const initialPanels: Panel[] = [
  {
    id: 'hierarchy',
    type: PanelType.HIERARCHY,
    position: PanelPosition.LEFT,
    isVisible: true,
    size: 25,
  },
  {
    id: 'inspector',
    type: PanelType.INSPECTOR,
    position: PanelPosition.RIGHT,
    isVisible: true,
    size: 25,
  },
  {
    id: 'assets',
    type: PanelType.ASSETS,
    position: PanelPosition.BOTTOM,
    isVisible: true,
    size: 30,
  },
  {
    id: 'scene',
    type: PanelType.SCENE,
    position: PanelPosition.CENTER,
    isVisible: true,
    size: 100,
  },
  {
    id: 'console',
    type: PanelType.CONSOLE,
    position: PanelPosition.BOTTOM,
    isVisible: false,
    size: 30,
  },
  {
    id: 'animation',
    type: PanelType.ANIMATION,
    position: PanelPosition.BOTTOM,
    isVisible: false,
    size: 30,
  },
  {
    id: 'physics',
    type: PanelType.PHYSICS,
    position: PanelPosition.RIGHT,
    isVisible: false,
    size: 25,
  },
  {
    id: 'particle',
    type: PanelType.PARTICLE,
    position: PanelPosition.RIGHT,
    isVisible: false,
    size: 25,
  },
  {
    id: 'layers',
    type: PanelType.LAYERS,
    position: PanelPosition.LEFT,
    isVisible: false,
    size: 25,
  },
  {
    id: 'instances',
    type: PanelType.INSTANCES,
    position: PanelPosition.RIGHT,
    isVisible: false,
    size: 25,
  },
  {
    id: 'collaboration',
    type: PanelType.COLLABORATION,
    position: PanelPosition.RIGHT,
    isVisible: false,
    size: 25,
  },
  {
    id: 'performanceOptimization',
    type: PanelType.PERFORMANCE_OPTIMIZATION,
    position: PanelPosition.RIGHT,
    isVisible: false,
    size: 25,
  },
  {
    id: 'resourceHotUpdate',
    type: PanelType.RESOURCE_HOT_UPDATE,
    position: PanelPosition.RIGHT,
    isVisible: false,
    size: 30,
  },
  {
    id: 'environment',
    type: PanelType.ENVIRONMENT,
    position: PanelPosition.RIGHT,
    isVisible: false,
    size: 30,
  },
  {
    id: 'git',
    type: PanelType.GIT,
    position: PanelPosition.RIGHT,
    isVisible: false,
    size: 30,
  },
];

// 初始状态
const initialState: UIState = {
  panels: initialPanels,
  dialogs: [],
  theme: 'light', // 默认使用亮色主题
  language: 'zh-CN',
  sidebarCollapsed: false,
  fullscreen: false,
  menuVisible: true,
  viewportMode: ViewportMode.SELECT,
  renderMode: RenderMode.TEXTURED,
  contextMenu: {
    isOpen: false,
    x: 0,
    y: 0,
    items: [],
  },
  notifications: [],
  // 初始化布局相关状态
  layout: null,
  savedLayouts: {},
  activeLayout: 'default',
};

// 创建UI切片
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setPanelVisibility: (state, action: PayloadAction<{ id: string; isVisible: boolean }>) => {
      const panel = state.panels.find((p) => p.id === action.payload.id);
      if (panel) {
        panel.isVisible = action.payload.isVisible;
      }
    },
    setPanelSize: (state, action: PayloadAction<{ id: string; size: number }>) => {
      const panel = state.panels.find((p) => p.id === action.payload.id);
      if (panel) {
        panel.size = action.payload.size;
      }
    },
    setPanelPosition: (state, action: PayloadAction<{ id: string; position: PanelPosition }>) => {
      const panel = state.panels.find((p) => p.id === action.payload.id);
      if (panel) {
        panel.position = action.payload.position;
      }
    },
    resetPanelLayout: (state) => {
      state.panels = initialPanels;
    },
    openDialog: (state, action: PayloadAction<Omit<Dialog, 'isOpen'>>) => {
      const existingDialog = state.dialogs.find((d) => d.type === action.payload.type);
      if (existingDialog) {
        existingDialog.isOpen = true;
        existingDialog.title = action.payload.title;
        existingDialog.message = action.payload.message;
        existingDialog.data = action.payload.data;
        existingDialog.onConfirm = action.payload.onConfirm;
        existingDialog.onCancel = action.payload.onCancel;
      } else {
        state.dialogs.push({
          ...action.payload,
          isOpen: true,
        });
      }
    },
    closeDialog: (state, action: PayloadAction<DialogType>) => {
      const dialog = state.dialogs.find((d) => d.type === action.payload);
      if (dialog) {
        dialog.isOpen = false;
      }
    },
    closeAllDialogs: (state) => {
      state.dialogs.forEach((dialog) => {
        dialog.isOpen = false;
      });
    },
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
    },

    // 布局相关操作
    setLayout: (state, action: PayloadAction<any>) => {
      state.layout = action.payload;
    },

    saveLayout: (state, action: PayloadAction<{ name: string; layout: any }>) => {
      const { name, layout } = action.payload;
      state.savedLayouts[name] = layout;
      state.activeLayout = name;
    },

    loadLayout: (state, action: PayloadAction<string>) => {
      const layoutName = action.payload;
      if (state.savedLayouts[layoutName]) {
        state.layout = state.savedLayouts[layoutName];
        state.activeLayout = layoutName;
      }
    },

    deleteLayout: (state, action: PayloadAction<string>) => {
      const layoutName = action.payload;
      if (layoutName !== 'default' && state.savedLayouts[layoutName]) {
        delete state.savedLayouts[layoutName];
        if (state.activeLayout === layoutName) {
          state.activeLayout = 'default';
        }
      }
    },
    setLanguage: (state, action: PayloadAction<string>) => {
      state.language = action.payload;
    },
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    toggleFullscreen: (state) => {
      state.fullscreen = !state.fullscreen;
    },
    toggleMenu: (state) => {
      state.menuVisible = !state.menuVisible;
    },
    openContextMenu: (state, action: PayloadAction<{ x: number; y: number; items: any[] }>) => {
      state.contextMenu = {
        isOpen: true,
        x: action.payload.x,
        y: action.payload.y,
        items: action.payload.items,
      };
    },
    closeContextMenu: (state) => {
      state.contextMenu.isOpen = false;
    },
    addNotification: (
      state,
      action: PayloadAction<{
        type: 'success' | 'info' | 'warning' | 'error';
        message: string;
        duration?: number;
      }>
    ) => {
      const id = Date.now().toString();
      state.notifications.push({
        id,
        type: action.payload.type,
        message: action.payload.message,
        duration: action.payload.duration,
      });
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter((n) => n.id !== action.payload);
    },
    clearAllNotifications: (state) => {
      state.notifications = [];
    },
    setViewportMode: (state, action: PayloadAction<ViewportMode>) => {
      state.viewportMode = action.payload;
    },
    setRenderMode: (state, action: PayloadAction<RenderMode>) => {
      state.renderMode = action.payload;
    },
  },
});

export const {
  setPanelVisibility,
  setPanelSize,
  setPanelPosition,
  resetPanelLayout,
  openDialog,
  closeDialog,
  closeAllDialogs,
  setTheme,
  setLanguage,
  toggleSidebar,
  toggleFullscreen,
  toggleMenu,
  openContextMenu,
  closeContextMenu,
  addNotification,
  removeNotification,
  clearAllNotifications,
  setViewportMode,
  setRenderMode,
  // 布局相关操作
  setLayout,
  saveLayout,
  loadLayout,
  deleteLayout,
} = uiSlice.actions;

export default uiSlice.reducer;
