/**
 * 辅助功能设置面板
 * 用于配置编辑器的辅助功能
 */
import React, { useState, useEffect } from 'react';
import { Card, Form, Switch, Slider, Divider, Space, Typography, Button, Tooltip, Alert } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  EyeOutlined,
  KeyboardOutlined,
  ContrastOutlined,
  FontSizeOutlined,
  PlayCircleOutlined,
  SoundOutlined,
  AimOutlined,
  BgColorsOutlined,
  InfoCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import AccessibilityService, { AccessibilityConfig, AccessibilityEventType } from '../../services/AccessibilityService';
import './AccessibilitySettingsPanel.less';

const { Title, Text, Paragraph } = Typography;

/**
 * 辅助功能设置面板属性
 */
interface AccessibilitySettingsPanelProps {
  /** 是否显示标题 */
  showTitle?: boolean;
}

/**
 * 辅助功能设置面板
 */
const AccessibilitySettingsPanel: React.FC<AccessibilitySettingsPanelProps> = ({
  showTitle = true
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  
  // 状态
  const [config, setConfig] = useState<AccessibilityConfig>(AccessibilityService.getConfig());
  const [textScaleFactor, setTextScaleFactor] = useState<number>(AccessibilityService.getTextScaleFactor());
  const [colorAdjustments, setColorAdjustments] = useState(AccessibilityService.getColorAdjustments());
  const [systemSettings, setSystemSettings] = useState({
    prefersReducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
    prefersHighContrast: window.matchMedia('(prefers-contrast: more)').matches
  });
  
  // 初始化
  useEffect(() => {
    // 监听配置变更事件
    const handleConfigChanged = (data: any) => {
      setConfig(data.newConfig);
    };
    
    // 监听文本缩放变更事件
    const handleTextScalingChanged = (data: any) => {
      setTextScaleFactor(data.factor);
    };
    
    // 监听颜色调整变更事件
    const handleColorAdjustmentChanged = (data: any) => {
      setColorAdjustments(data.adjustments);
    };
    
    // 添加事件监听
    AccessibilityService.on(AccessibilityEventType.CONFIG_CHANGED, handleConfigChanged);
    AccessibilityService.on(AccessibilityEventType.TEXT_SCALING_CHANGED, handleTextScalingChanged);
    AccessibilityService.on(AccessibilityEventType.COLOR_ADJUSTMENT_CHANGED, handleColorAdjustmentChanged);
    
    // 清理函数
    return () => {
      AccessibilityService.off(AccessibilityEventType.CONFIG_CHANGED, handleConfigChanged);
      AccessibilityService.off(AccessibilityEventType.TEXT_SCALING_CHANGED, handleTextScalingChanged);
      AccessibilityService.off(AccessibilityEventType.COLOR_ADJUSTMENT_CHANGED, handleColorAdjustmentChanged);
    };
  }, []);
  
  // 处理配置变更
  const handleConfigChange = (changedValues: any, allValues: any) => {
    // 更新辅助功能服务配置
    AccessibilityService.configure(changedValues);
    
    // 如果启用了屏幕阅读器，朗读变更信息
    if (config.enableScreenReader && config.enableAutoAnnounce) {
      const changedKey = Object.keys(changedValues)[0];
      if (changedKey) {
        const settingName = t(`accessibility.settings.${changedKey.replace('enable', '').toLowerCase()}`);
        const settingState = changedValues[changedKey] ? t('enabled') : t('disabled');
        AccessibilityService.announce(`${settingName} ${settingState}`);
      }
    }
  };
  
  // 处理文本缩放变更
  const handleTextScaleChange = (value: number) => {
    setTextScaleFactor(value);
    AccessibilityService.setTextScaleFactor(value);
  };
  
  // 处理颜色调整变更
  const handleColorAdjustmentChange = (key: keyof typeof colorAdjustments, value: number) => {
    const newAdjustments = { ...colorAdjustments, [key]: value };
    setColorAdjustments(newAdjustments);
    AccessibilityService.setColorAdjustments({ [key]: value });
  };
  
  // 重置为系统设置
  const resetToSystemSettings = () => {
    AccessibilityService.configure({
      enableReducedMotion: systemSettings.prefersReducedMotion,
      enableHighContrast: systemSettings.prefersHighContrast
    });
    
    // 朗读重置信息
    if (config.enableScreenReader && config.enableAutoAnnounce) {
      AccessibilityService.announce(t('accessibility.resetToSystemSettings'));
    }
  };
  
  // 测试屏幕阅读器
  const testScreenReader = () => {
    AccessibilityService.announce(t('accessibility.testMessage'), 'assertive');
  };
  
  return (
    <div className="accessibility-settings-panel">
      <Card
        title={showTitle ? (
          <Space>
            <EyeOutlined />
            {t('accessibility.title')}
          </Space>
        ) : undefined}
        extra={
          <Tooltip title={t('accessibility.resetToSystemSettings')}>
            <Button
              icon={<ReloadOutlined />}
              onClick={resetToSystemSettings}
              size="small"
            />
          </Tooltip>
        }
      >
        {systemSettings.prefersReducedMotion || systemSettings.prefersHighContrast ? (
          <Alert
            message={t('accessibility.systemSettingsDetected')}
            description={
              <div>
                {systemSettings.prefersReducedMotion && (
                  <div>{t('accessibility.systemReducedMotion')}</div>
                )}
                {systemSettings.prefersHighContrast && (
                  <div>{t('accessibility.systemHighContrast')}</div>
                )}
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        ) : null}
        
        <Form
          form={form}
          layout="vertical"
          initialValues={config}
          onValuesChange={handleConfigChange}
        >
          <Form.Item
            name="enabled"
            valuePropName="checked"
            label={t('accessibility.settings.masterSwitch')}
          >
            <Switch />
          </Form.Item>
          
          <Divider />
          
          <Form.Item
            name="enableScreenReader"
            valuePropName="checked"
            label={
              <Space>
                <SoundOutlined />
                {t('accessibility.settings.screenReader')}
              </Space>
            }
            extra={t('accessibility.settings.screenReaderDescription')}
          >
            <Switch disabled={!config.enabled} />
          </Form.Item>
          
          {config.enabled && config.enableScreenReader && (
            <div style={{ marginBottom: 16 }}>
              <Button onClick={testScreenReader}>
                {t('accessibility.testScreenReader')}
              </Button>
            </div>
          )}
          
          <Form.Item
            name="enableKeyboardNavigation"
            valuePropName="checked"
            label={
              <Space>
                <KeyboardOutlined />
                {t('accessibility.settings.keyboardNavigation')}
              </Space>
            }
            extra={t('accessibility.settings.keyboardNavigationDescription')}
          >
            <Switch disabled={!config.enabled} />
          </Form.Item>
          
          <Form.Item
            name="enableHighContrast"
            valuePropName="checked"
            label={
              <Space>
                <ContrastOutlined />
                {t('accessibility.settings.highContrast')}
              </Space>
            }
            extra={t('accessibility.settings.highContrastDescription')}
          >
            <Switch disabled={!config.enabled} />
          </Form.Item>
          
          <Form.Item
            name="enableTextScaling"
            valuePropName="checked"
            label={
              <Space>
                <FontSizeOutlined />
                {t('accessibility.settings.textScaling')}
              </Space>
            }
            extra={t('accessibility.settings.textScalingDescription')}
          >
            <Switch disabled={!config.enabled} />
          </Form.Item>
          
          {config.enabled && config.enableTextScaling && (
            <Form.Item
              label={t('accessibility.settings.textScaleFactor')}
            >
              <Slider
                min={0.5}
                max={2}
                step={0.1}
                value={textScaleFactor}
                onChange={handleTextScaleChange}
                marks={{
                  0.5: '50%',
                  1: '100%',
                  1.5: '150%',
                  2: '200%'
                }}
              />
            </Form.Item>
          )}
          
          <Form.Item
            name="enableReducedMotion"
            valuePropName="checked"
            label={
              <Space>
                <PlayCircleOutlined />
                {t('accessibility.settings.reducedMotion')}
              </Space>
            }
            extra={t('accessibility.settings.reducedMotionDescription')}
          >
            <Switch disabled={!config.enabled} />
          </Form.Item>
          
          <Form.Item
            name="enableFocusIndicator"
            valuePropName="checked"
            label={
              <Space>
                <AimOutlined />
                {t('accessibility.settings.focusIndicator')}
              </Space>
            }
            extra={t('accessibility.settings.focusIndicatorDescription')}
          >
            <Switch disabled={!config.enabled} />
          </Form.Item>
          
          <Form.Item
            name="enableColorAdjustment"
            valuePropName="checked"
            label={
              <Space>
                <BgColorsOutlined />
                {t('accessibility.settings.colorAdjustment')}
              </Space>
            }
            extra={t('accessibility.settings.colorAdjustmentDescription')}
          >
            <Switch disabled={!config.enabled} />
          </Form.Item>
          
          {config.enabled && config.enableColorAdjustment && (
            <>
              <Form.Item
                label={t('accessibility.settings.brightness')}
              >
                <Slider
                  min={0.5}
                  max={1.5}
                  step={0.1}
                  value={colorAdjustments.brightness}
                  onChange={(value) => handleColorAdjustmentChange('brightness', value)}
                  marks={{
                    0.5: t('accessibility.settings.dark'),
                    1: t('accessibility.settings.normal'),
                    1.5: t('accessibility.settings.bright')
                  }}
                />
              </Form.Item>
              
              <Form.Item
                label={t('accessibility.settings.contrast')}
              >
                <Slider
                  min={0.5}
                  max={1.5}
                  step={0.1}
                  value={colorAdjustments.contrast}
                  onChange={(value) => handleColorAdjustmentChange('contrast', value)}
                  marks={{
                    0.5: t('accessibility.settings.low'),
                    1: t('accessibility.settings.normal'),
                    1.5: t('accessibility.settings.high')
                  }}
                />
              </Form.Item>
              
              <Form.Item
                label={t('accessibility.settings.saturation')}
              >
                <Slider
                  min={0}
                  max={2}
                  step={0.1}
                  value={colorAdjustments.saturation}
                  onChange={(value) => handleColorAdjustmentChange('saturation', value)}
                  marks={{
                    0: t('accessibility.settings.grayscale'),
                    1: t('accessibility.settings.normal'),
                    2: t('accessibility.settings.vivid')
                  }}
                />
              </Form.Item>
            </>
          )}
          
          <Form.Item
            name="enableAutoAnnounce"
            valuePropName="checked"
            label={t('accessibility.settings.autoAnnounce')}
            extra={t('accessibility.settings.autoAnnounceDescription')}
          >
            <Switch disabled={!config.enabled || !config.enableScreenReader} />
          </Form.Item>
        </Form>
        
        <Divider />
        
        <Paragraph>
          <InfoCircleOutlined /> {t('accessibility.helpText')}
        </Paragraph>
      </Card>
    </div>
  );
};

export default AccessibilitySettingsPanel;
