"use strict";
/**
 * 级联阴影映射着色器
 * 用于实现级联阴影映射的着色器代码
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Shader = void 0;
/**
 * 着色器类
 */
var Shader = /** @class */ (function () {
    function Shader() {
    }
    /**
     * 生成lights_pars_begin着色器代码
     * @returns 着色器代码
     */
    Shader.lights_pars_begin = function () {
        return /* glsl */ "\nuniform sampler2D directionalShadowMap[ NUM_DIR_LIGHT_SHADOWS ];\nvarying vec4 vDirectionalShadowCoord[ NUM_DIR_LIGHT_SHADOWS ];\nstruct DirectionalLightShadow {\n  float shadowBias;\n  float shadowNormalBias;\n  float shadowRadius;\n  vec2 shadowMapSize;\n};\nuniform DirectionalLightShadow directionalLightShadows[ NUM_DIR_LIGHT_SHADOWS ];\n\n// CSM specific variables and constants\n#ifdef USE_CSM\n  uniform vec2 CSM_cascades[CSM_CASCADES];\n  uniform float cameraNear;\n  uniform float shadowFar;\n#endif\n\n// helper functions from three.js\n#if NUM_DIR_LIGHT_SHADOWS > 0\n  float getShadow(sampler2D shadowMap, vec2 shadowMapSize, float shadowBias, float shadowRadius, vec4 shadowCoord) {\n    float shadow = 1.0;\n    shadowCoord.xyz /= shadowCoord.w;\n    shadowCoord.z += shadowBias;\n    bool inFrustum = shadowCoord.x >= 0.0 && shadowCoord.x <= 1.0 && shadowCoord.y >= 0.0 && shadowCoord.y <= 1.0;\n    bool frustumTest = inFrustum && shadowCoord.z <= 1.0;\n    if (frustumTest) {\n      #ifdef USE_PCSS\n        shadow = PCSS(shadowMap, shadowCoord.xy, shadowCoord.z, shadowMapSize, shadowRadius);\n      #else\n        #if defined(SHADOWMAP_TYPE_PCF)\n          vec2 texelSize = vec2(1.0) / shadowMapSize;\n          float dx0 = -texelSize.x * shadowRadius;\n          float dy0 = -texelSize.y * shadowRadius;\n          float dx1 = +texelSize.x * shadowRadius;\n          float dy1 = +texelSize.y * shadowRadius;\n          float dx2 = dx0 / 2.0;\n          float dy2 = dy0 / 2.0;\n          float dx3 = dx1 / 2.0;\n          float dy3 = dy1 / 2.0;\n          shadow = (\n            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx0, dy0), shadowCoord.z) +\n            texture2DCompare(shadowMap, shadowCoord.xy + vec2(0.0, dy0), shadowCoord.z) +\n            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx1, dy0), shadowCoord.z) +\n            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx2, dy2), shadowCoord.z) +\n            texture2DCompare(shadowMap, shadowCoord.xy + vec2(0.0, dy2), shadowCoord.z) +\n            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx3, dy2), shadowCoord.z) +\n            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx0, 0.0), shadowCoord.z) +\n            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx2, 0.0), shadowCoord.z) +\n            texture2DCompare(shadowMap, shadowCoord.xy, shadowCoord.z) +\n            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx3, 0.0), shadowCoord.z) +\n            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx1, 0.0), shadowCoord.z) +\n            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx2, dy3), shadowCoord.z) +\n            texture2DCompare(shadowMap, shadowCoord.xy + vec2(0.0, dy3), shadowCoord.z) +\n            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx3, dy3), shadowCoord.z) +\n            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx0, dy1), shadowCoord.z) +\n            texture2DCompare(shadowMap, shadowCoord.xy + vec2(0.0, dy1), shadowCoord.z) +\n            texture2DCompare(shadowMap, shadowCoord.xy + vec2(dx1, dy1), shadowCoord.z)\n          ) * (1.0 / 17.0);\n        #elif defined(SHADOWMAP_TYPE_PCF_SOFT)\n          vec2 texelSize = vec2(1.0) / shadowMapSize;\n          float dx = texelSize.x;\n          float dy = texelSize.y;\n          vec2 uv = shadowCoord.xy;\n          vec2 f = fract(uv * shadowMapSize + 0.5);\n          uv -= f * texelSize;\n          shadow = (\n            texture2DCompare(shadowMap, uv, shadowCoord.z) +\n            texture2DCompare(shadowMap, uv + vec2(dx, 0.0), shadowCoord.z) +\n            texture2DCompare(shadowMap, uv + vec2(0.0, dy), shadowCoord.z) +\n            texture2DCompare(shadowMap, uv + texelSize, shadowCoord.z) +\n            mix(\n              mix(texture2DCompare(shadowMap, uv, shadowCoord.z),\n                texture2DCompare(shadowMap, uv + vec2(dx, 0.0), shadowCoord.z), f.x),\n              mix(texture2DCompare(shadowMap, uv + vec2(0.0, dy), shadowCoord.z),\n                texture2DCompare(shadowMap, uv + texelSize, shadowCoord.z), f.x), f.y)\n          ) * (1.0 / 5.0);\n        #else // no PCF\n          shadow = texture2DCompare(shadowMap, shadowCoord.xy, shadowCoord.z);\n        #endif\n      #endif\n    }\n    return shadow;\n  }\n#endif\n";
    };
    /**
     * 生成lights_fragment_begin着色器代码
     * @param cascades 级联数量
     * @returns 着色器代码
     */
    Shader.lights_fragment_begin = function (cascades) {
        return /* glsl */ "\n/**\n * This is a template that can be used to light a material, it uses pluggable\n * RenderEquations (RE)for specific lighting scenarios.\n *\n * Instructions for use:\n * - Ensure that both RE_Direct, RE_IndirectDiffuse and RE_IndirectSpecular are defined\n * - Create a material parameter that is to be passed as the third parameter to your lighting functions.\n *\n * TODO:\n * - Add area light support.\n * - Add sphere light support.\n * - Add diffuse light probe (irradiance cubemap) support.\n */\n\nGeometricContext geometry;\n\ngeometry.position = - vViewPosition;\ngeometry.normal = normal;\ngeometry.viewDir = ( isOrthographic ) ? vec3( 0, 0, 1 ) : normalize( vViewPosition );\n\n#ifdef USE_CLEARCOAT\n\n  geometry.clearcoatNormal = clearcoatNormal;\n\n#endif\n\n#ifdef USE_IRIDESCENCE\n\n  float dotNVi = saturate( dot( normal, geometry.viewDir ) );\n\n  if ( material.iridescenceThickness == 0.0 ) {\n\n    material.iridescence = 0.0;\n\n  } else {\n\n    material.iridescence = saturate( material.iridescence );\n\n  }\n\n  if ( material.iridescence > 0.0 ) {\n\n    material.iridescenceFresnel = evalIridescence( 1.0, material.iridescenceIOR, dotNVi, material.iridescenceThickness, material.specularColor );\n\n    // Iridescence F0 approximation\n    material.iridescenceF0 = Schlick_to_F0( material.iridescenceFresnel, 1.0, dotNVi );\n\n  }\n\n#endif\n\nIncidentLight directLight;\n\n#if ( NUM_POINT_LIGHTS > 0 ) && defined( RE_Direct )\n\n  PointLight pointLight;\n  #if defined( USE_SHADOWMAP ) && NUM_POINT_LIGHT_SHADOWS > 0\n  PointLightShadow pointLightShadow;\n  #endif\n\n  #pragma unroll_loop_start\n  for ( int i = 0; i < NUM_POINT_LIGHTS; i ++ ) {\n\n    pointLight = pointLights[ i ];\n\n    getPointLightInfo( pointLight, geometry, directLight );\n\n    #if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_POINT_LIGHT_SHADOWS )\n    pointLightShadow = pointLightShadows[ i ];\n    directLight.color *= ( directLight.visible && receiveShadow ) ? getPointShadow( pointShadowMap[ i ], pointLightShadow.shadowMapSize, pointLightShadow.shadowBias, pointLightShadow.shadowRadius, vPointShadowCoord[ i ], pointLightShadow.shadowCameraNear, pointLightShadow.shadowCameraFar ) : 1.0;\n    #endif\n\n    RE_Direct( directLight, geometry, material, reflectedLight );\n\n  }\n  #pragma unroll_loop_end\n\n#endif\n\n#if ( NUM_SPOT_LIGHTS > 0 ) && defined( RE_Direct )\n\n  SpotLight spotLight;\n  vec4 spotColor;\n  vec3 spotLightCoord;\n  bool inSpotLightMap;\n\n  #if defined( USE_SHADOWMAP ) && NUM_SPOT_LIGHT_SHADOWS > 0\n  SpotLightShadow spotLightShadow;\n  #endif\n\n  #pragma unroll_loop_start\n  for ( int i = 0; i < NUM_SPOT_LIGHTS; i ++ ) {\n\n    spotLight = spotLights[ i ];\n\n    getSpotLightInfo( spotLight, geometry, directLight );\n\n    // spot lights are ordered [shadows with maps, shadows without maps, maps without shadows, none]\n    #if ( UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS_WITH_MAPS )\n    #define SPOT_LIGHT_MAP_INDEX UNROLLED_LOOP_INDEX\n    #elif ( UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS )\n    #define SPOT_LIGHT_MAP_INDEX NUM_SPOT_LIGHT_MAPS\n    #else\n    #define SPOT_LIGHT_MAP_INDEX ( UNROLLED_LOOP_INDEX - NUM_SPOT_LIGHT_SHADOWS + NUM_SPOT_LIGHT_SHADOWS_WITH_MAPS )\n    #endif\n\n    #if ( SPOT_LIGHT_MAP_INDEX < NUM_SPOT_LIGHT_MAPS )\n      spotLightCoord = vSpotLightCoord[ i ].xyz / vSpotLightCoord[ i ].w;\n      inSpotLightMap = all( lessThan( abs( spotLightCoord * 2. - 1. ), vec3( 1.0 ) ) );\n      spotColor = texture2D( spotLightMap[ SPOT_LIGHT_MAP_INDEX ], spotLightCoord.xy );\n      directLight.color = inSpotLightMap ? directLight.color * spotColor.rgb : directLight.color;\n    #endif\n\n    #undef SPOT_LIGHT_MAP_INDEX\n\n    #if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS )\n    spotLightShadow = spotLightShadows[ i ];\n    directLight.color *= ( directLight.visible && receiveShadow ) ? getShadow( spotShadowMap[ i ], spotLightShadow.shadowMapSize, spotLightShadow.shadowBias, spotLightShadow.shadowRadius, vSpotLightCoord[ i ] ) : 1.0;\n    #endif\n\n    RE_Direct( directLight, geometry, material, reflectedLight );\n\n  }\n  #pragma unroll_loop_end\n\n#endif\n\n#if ( NUM_DIR_LIGHTS > 0 ) && defined( RE_Direct )\n\n  DirectionalLight directionalLight;\n  #if defined( USE_SHADOWMAP ) && NUM_DIR_LIGHT_SHADOWS > 0\n  DirectionalLightShadow directionalLightShadow;\n  #endif\n\n  #ifdef USE_CSM\n  float linearDepth = (vViewPosition.z + cameraNear) / (shadowFar - cameraNear);\n  float cascadeCenter;\n  float cascadeSize;\n  int cascadeIndex;\n  #ifdef CSM_FADE\n  float cascadeRatio;\n  float ratio;\n  #endif\n  #endif\n\n  #pragma unroll_loop_start\n  for ( int i = 0; i < NUM_DIR_LIGHTS; i ++ ) {\n\n    directionalLight = directionalLights[ i ];\n    getDirectionalLightInfo( directionalLight, geometry, directLight );\n\n    #if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_DIR_LIGHT_SHADOWS )\n    \n    #ifdef USE_CSM\n    // \u8BA1\u7B97\u7EA7\u8054\u7D22\u5F15\n    cascadeIndex = -1;\n    for (int j = 0; j < ".concat(cascades, "; j++) {\n      if (linearDepth >= CSM_cascades[j].x && linearDepth < CSM_cascades[j].y) {\n        cascadeIndex = j;\n        cascadeCenter = (CSM_cascades[j].x + CSM_cascades[j].y) * 0.5;\n        cascadeSize = CSM_cascades[j].y - CSM_cascades[j].x;\n        break;\n      }\n    }\n\n    if (cascadeIndex >= 0) {\n      vec3 prevColor = directLight.color;\n      directionalLightShadow = directionalLightShadows[ i ];\n      directLight.color *= (directLight.visible && receiveShadow) ? getShadow(\n        directionalShadowMap[ i ],\n        directionalLightShadow.shadowMapSize,\n        directionalLightShadow.shadowBias,\n        directionalLightShadow.shadowRadius,\n        vDirectionalShadowCoord[ i ]\n      ) : 1.0;\n\n      #ifdef CSM_FADE\n      // \u8BA1\u7B97\u7EA7\u8054\u8FB9\u7F18\u7684\u6DE1\u5165\u6DE1\u51FA\n      bool shouldFadeLastCascade = cascadeIndex == ").concat(cascades, " - 1 && linearDepth > cascadeCenter;\n      if (shouldFadeLastCascade) {\n        ratio = (linearDepth - cascadeCenter) / (cascadeSize * 0.5);\n        ratio = smoothstep(0.0, 1.0, ratio);\n        directLight.color = mix(directLight.color, prevColor, ratio);\n      }\n      #endif\n    }\n    #else\n    directionalLightShadow = directionalLightShadows[ i ];\n    directLight.color *= ( directLight.visible && receiveShadow ) ? getShadow( directionalShadowMap[ i ], directionalLightShadow.shadowMapSize, directionalLightShadow.shadowBias, directionalLightShadow.shadowRadius, vDirectionalShadowCoord[ i ] ) : 1.0;\n    #endif\n\n    #endif\n\n    RE_Direct( directLight, geometry, material, reflectedLight );\n\n  }\n  #pragma unroll_loop_end\n\n#endif\n\n#if ( NUM_RECT_AREA_LIGHTS > 0 ) && defined( RE_Direct_RectArea )\n\n  RectAreaLight rectAreaLight;\n\n  #pragma unroll_loop_start\n  for ( int i = 0; i < NUM_RECT_AREA_LIGHTS; i ++ ) {\n\n    rectAreaLight = rectAreaLights[ i ];\n    RE_Direct_RectArea( rectAreaLight, geometry, material, reflectedLight );\n\n  }\n  #pragma unroll_loop_end\n\n#endif\n\n#if defined( RE_IndirectDiffuse )\n\n  vec3 iblIrradiance = vec3( 0.0 );\n\n  vec3 irradiance = getAmbientLightIrradiance( ambientLightColor );\n\n  irradiance += getLightProbeIrradiance( lightProbe, geometry.normal );\n\n  #if ( NUM_HEMI_LIGHTS > 0 )\n\n    #pragma unroll_loop_start\n    for ( int i = 0; i < NUM_HEMI_LIGHTS; i ++ ) {\n\n      irradiance += getHemisphereLightIrradiance( hemisphereLights[ i ], geometry.normal );\n\n    }\n    #pragma unroll_loop_end\n\n  #endif\n\n#endif\n\n#if defined( RE_IndirectSpecular )\n\n  vec3 radiance = vec3( 0.0 );\n  vec3 clearcoatRadiance = vec3( 0.0 );\n\n#endif\n");
    };
    return Shader;
}());
exports.Shader = Shader;
