"use strict";
/**
 * UUID工具类
 * 生成唯一标识符
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.isValidUUID = exports.generateShortId = exports.generateUUID = void 0;
/**
 * 生成UUID v4
 * @returns UUID字符串
 */
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (Math.random() * 16) | 0;
        var v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
}
exports.generateUUID = generateUUID;
/**
 * 生成简短的唯一ID
 * @param length ID长度，默认为8
 * @returns 唯一ID字符串
 */
function generateShortId(length) {
    if (length === void 0) { length = 8; }
    var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var result = '';
    for (var i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}
exports.generateShortId = generateShortId;
/**
 * 检查字符串是否为有效的UUID
 * @param uuid 要检查的字符串
 * @returns 是否为有效的UUID
 */
function isValidUUID(uuid) {
    var regex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return regex.test(uuid);
}
exports.isValidUUID = isValidUUID;
