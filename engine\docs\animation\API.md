# 动画系统API文档

## AnimationClip

表示一个动画片段，包含一系列的动画轨道。

### 构造函数

```typescript
constructor(name: string, duration: number = 0, speed: number = 1.0)
```

- `name`: 动画片段名称
- `duration`: 动画片段持续时间（秒）
- `speed`: 动画播放速度

### 属性

- `name: string` - 动画片段名称
- `duration: number` - 动画片段持续时间（秒）
- `speed: number` - 动画播放速度
- `loopMode: LoopMode` - 循环模式（NONE, REPEAT, PING_PONG）
- `blendTime: number` - 混合时间（秒）

### 方法

#### addTrack

添加动画轨道。

```typescript
addTrack(targetPath: string, type: string, keyframes: any[]): void
```

- `targetPath`: 目标路径，如"character.position"
- `type`: 轨道类型，如"vector3", "quaternion", "color", "number", "boolean"
- `keyframes`: 关键帧数组，每个关键帧包含time和value属性

#### removeTrack

移除动画轨道。

```typescript
removeTrack(targetPath: string): boolean
```

- `targetPath`: 目标路径

#### getStateAtTime

获取指定时间点的动画状态。

```typescript
getStateAtTime(time: number, result: Map<string, any> = new Map()): Map<string, any>
```

- `time`: 时间点（秒）
- `result`: 结果对象，用于存储计算结果

#### evaluateTrack

评估轨道在指定时间点的值。

```typescript
evaluateTrack(track: any, time: number): any
```

- `track`: 轨道对象
- `time`: 时间点（秒）

#### toThreeAnimationClip

转换为Three.js的AnimationClip。

```typescript
toThreeAnimationClip(): THREE.AnimationClip
```

#### clone

克隆动画片段。

```typescript
clone(): AnimationClip
```

#### setCacheEnabled

设置是否启用缓存。

```typescript
setCacheEnabled(enabled: boolean): void
```

- `enabled`: 是否启用

#### clearCache

清除缓存。

```typescript
clearCache(): void
```

## Animator

动画控制器，用于控制动画片段的播放、混合和过渡。

### 构造函数

```typescript
constructor(options: AnimatorOptions = {})
```

- `options`: 动画控制器选项
  - `entity`: 目标实体
  - `clips`: 动画片段数组
  - `autoPlay`: 是否自动播放
  - `defaultBlendTime`: 默认混合时间（秒）
  - `timeScale`: 时间缩放

### 方法

#### setEntity

设置目标实体。

```typescript
setEntity(entity: Entity): void
```

- `entity`: 实体

#### addClip

添加动画片段。

```typescript
addClip(clip: AnimationClip): void
```

- `clip`: 动画片段

#### removeClip

移除动画片段。

```typescript
removeClip(name: string): boolean
```

- `name`: 动画片段名称

#### getClip

获取动画片段。

```typescript
getClip(name: string): AnimationClip | null
```

- `name`: 动画片段名称

#### getClips

获取所有动画片段。

```typescript
getClips(): AnimationClip[]
```

#### play

播放动画。

```typescript
play(name: string, blendTime?: number): boolean
```

- `name`: 动画片段名称
- `blendTime`: 混合时间（秒），如果为0则立即切换

#### stop

停止播放。

```typescript
stop(): void
```

#### pause

暂停播放。

```typescript
pause(): void
```

#### resume

恢复播放。

```typescript
resume(): void
```

#### setTime

设置播放时间。

```typescript
setTime(time: number): void
```

- `time`: 时间（秒）

#### getTime

获取播放时间。

```typescript
getTime(): number
```

#### setTimeScale

设置时间缩放。

```typescript
setTimeScale(timeScale: number): void
```

- `timeScale`: 时间缩放

#### getTimeScale

获取时间缩放。

```typescript
getTimeScale(): number
```

#### setLoop

设置循环模式。

```typescript
setLoop(loop: boolean): void
```

- `loop`: 是否循环

#### getLoop

获取循环模式。

```typescript
getLoop(): boolean
```

#### getState

获取当前动画状态。

```typescript
getState(): AnimationState
```

#### getCurrentClip

获取当前播放的动画片段。

```typescript
getCurrentClip(): AnimationClip | null
```

#### getNextClip

获取下一个要播放的动画片段。

```typescript
getNextClip(): AnimationClip | null
```

#### getBlendFactor

获取混合因子。

```typescript
getBlendFactor(): number
```

#### addListener

添加事件监听器。

```typescript
addListener(type: AnimationEventType, listener: (...args: any[]) => void): void
```

- `type`: 事件类型
- `listener`: 监听器函数

#### removeListener

移除事件监听器。

```typescript
removeListener(type: AnimationEventType, listener: (...args: any[]) => void): void
```

- `type`: 事件类型
- `listener`: 监听器函数

#### update

更新动画。

```typescript
update(deltaTime: number): void
```

- `deltaTime`: 帧间隔时间（秒）

#### setCacheEnabled

设置是否启用缓存。

```typescript
setCacheEnabled(enabled: boolean): void
```

- `enabled`: 是否启用

#### clearCache

清除缓存。

```typescript
clearCache(): void
```

## AnimationStateMachine

动画状态机，用于管理复杂的动画状态和转换。

### 构造函数

```typescript
constructor(animator: Animator)
```

- `animator`: 动画控制器

### 方法

#### addState

添加状态。

```typescript
addState(state: AnimationState): void
```

- `state`: 动画状态

#### addTransition

添加转换规则。

```typescript
addTransition(rule: TransitionRule): void
```

- `rule`: 转换规则

#### setParameter

设置参数。

```typescript
setParameter(name: string, value: any): void
```

- `name`: 参数名称
- `value`: 参数值

#### getParameter

获取参数。

```typescript
getParameter(name: string): any
```

- `name`: 参数名称

#### setCurrentState

设置当前状态。

```typescript
setCurrentState(stateName: string): void
```

- `stateName`: 状态名称

#### update

更新状态机。

```typescript
update(deltaTime: number): void
```

- `deltaTime`: 时间增量（秒）

#### addEventListener

添加事件监听器。

```typescript
addEventListener(type: AnimationStateMachineEventType, callback: (data: any) => void): void
```

- `type`: 事件类型
- `callback`: 回调函数

#### removeEventListener

移除事件监听器。

```typescript
removeEventListener(type: AnimationStateMachineEventType, callback: (data: any) => void): void
```

- `type`: 事件类型
- `callback`: 回调函数

## AvatarAnimationSystem

Avatar动画系统，用于管理角色动画。

### 构造函数

```typescript
constructor(world: World, config: AvatarAnimationSystemConfig = {})
```

- `world`: 世界
- `config`: 配置
  - `debug`: 是否启用调试模式
  - `autoRetarget`: 是否自动重定向动画
  - `useStateMachine`: 是否使用状态机
  - `useBlendSpace`: 是否使用混合空间

### 方法

#### registerAvatar

注册Avatar实体。

```typescript
registerAvatar(entity: Entity): void
```

- `entity`: 实体

#### unregisterAvatar

注销Avatar实体。

```typescript
unregisterAvatar(entity: Entity): void
```

- `entity`: 实体

#### update

更新系统。

```typescript
update(deltaTime: number): void
```

- `deltaTime`: 时间增量（秒）

#### setUpdateFrequency

设置实体更新频率。

```typescript
setUpdateFrequency(entity: Entity, frequency: number): void
```

- `entity`: 实体
- `frequency`: 更新频率（秒）

#### markNeedsUpdate

标记实体需要更新。

```typescript
markNeedsUpdate(entity: Entity): void
```

- `entity`: 实体

#### addEventListener

添加事件监听器。

```typescript
addEventListener(type: string, callback: (data: any) => void): void
```

- `type`: 事件类型
- `callback`: 回调函数

#### removeEventListener

移除事件监听器。

```typescript
removeEventListener(type: string, callback: (data: any) => void): void
```

- `type`: 事件类型
- `callback`: 回调函数

## 枚举类型

### LoopMode

动画循环模式。

```typescript
enum LoopMode {
  /** 不循环 */
  NONE = 'none',
  /** 重复循环 */
  REPEAT = 'repeat',
  /** 来回循环 */
  PING_PONG = 'pingpong'
}
```

### AnimationState

动画状态。

```typescript
enum AnimationState {
  /** 停止 */
  STOPPED = 'stopped',
  /** 播放中 */
  PLAYING = 'playing',
  /** 暂停 */
  PAUSED = 'paused',
  /** 混合中 */
  BLENDING = 'blending'
}
```

### AnimationEventType

动画事件类型。

```typescript
enum AnimationEventType {
  /** 开始 */
  START = 'start',
  /** 停止 */
  STOP = 'stop',
  /** 暂停 */
  PAUSE = 'pause',
  /** 恢复 */
  RESUME = 'resume',
  /** 循环 */
  LOOP = 'loop',
  /** 完成 */
  COMPLETE = 'complete',
  /** 混合开始 */
  BLEND_START = 'blendStart',
  /** 混合完成 */
  BLEND_COMPLETE = 'blendComplete'
}
```

### AnimationStateMachineEventType

动画状态机事件类型。

```typescript
enum AnimationStateMachineEventType {
  /** 状态进入 */
  STATE_ENTER = 'stateEnter',
  /** 状态退出 */
  STATE_EXIT = 'stateExit',
  /** 状态转换开始 */
  TRANSITION_START = 'transitionStart',
  /** 状态转换结束 */
  TRANSITION_END = 'transitionEnd'
}
```
