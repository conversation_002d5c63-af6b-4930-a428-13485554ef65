/**
 * 视频播放器组件
 * 用于播放视频教程
 */
import React, { useEffect, useRef, useState } from 'react';
import { Button, Slider, Space, Tooltip } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  SoundOutlined,
  FullscreenOutlined,
  ExpandOutlined,
  ReloadOutlined,
  DownloadOutlined,
  MutedSoundOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './VideoPlayer.less';

// 视频播放器属性
interface VideoPlayerProps {
  url: string;                  // 视频URL
  width?: number | string;      // 播放器宽度
  height?: number | string;     // 播放器高度
  autoPlay?: boolean;           // 是否自动播放
  loop?: boolean;               // 是否循环播放
  controls?: boolean;           // 是否显示原生控制条
  poster?: string;              // 封面图片URL
  onEnded?: () => void;         // 播放结束回调
  onError?: (error: any) => void; // 错误回调
}

/**
 * 视频播放器组件
 */
const VideoPlayer: React.FC<VideoPlayerProps> = ({
  url,
  width = '100%',
  height = 'auto',
  autoPlay = false,
  loop = false,
  controls = false,
  poster,
  onEnded,
  onError,
}) => {
  const { t } = useTranslation();
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [playing, setPlaying] = useState<boolean>(autoPlay);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);
  const [volume, setVolume] = useState<number>(1);
  const [muted, setMuted] = useState<boolean>(false);
  const [showControls, setShowControls] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [fullscreen, setFullscreen] = useState<boolean>(false);
  const [showVolumeSlider, setShowVolumeSlider] = useState<boolean>(false);
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 初始化视频
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    // 加载元数据
    const handleLoadedMetadata = () => {
      setDuration(video.duration);
      setLoading(false);
    };

    // 播放进度更新
    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime);
    };

    // 播放结束
    const handleEnded = () => {
      setPlaying(false);
      if (onEnded) onEnded();
    };

    // 播放错误
    const handleError = (e: any) => {
      setError('视频加载失败');
      setLoading(false);
      if (onError) onError(e);
    };

    // 添加事件监听
    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('ended', handleEnded);
    video.addEventListener('error', handleError);

    // 清理事件监听
    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('ended', handleEnded);
      video.removeEventListener('error', handleError);
    };
  }, [onEnded, onError]);

  // 控制播放状态
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    if (playing) {
      video.play().catch(error => {
        console.error('播放失败:', error);
        setPlaying(false);
      });
    } else {
      video.pause();
    }
  }, [playing]);

  // 控制音量
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    video.volume = volume;
    video.muted = muted;
  }, [volume, muted]);

  // 控制全屏
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleFullscreenChange = () => {
      setFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // 自动隐藏控制条
  useEffect(() => {
    if (!showControls) return;

    // 清除之前的定时器
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }

    // 设置新的定时器
    controlsTimeoutRef.current = setTimeout(() => {
      setShowControls(false);
    }, 3000);

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [showControls, currentTime]);

  // 切换播放状态
  const togglePlay = () => {
    setPlaying(!playing);
  };

  // 切换静音
  const toggleMute = () => {
    setMuted(!muted);
  };

  // 切换全屏
  const toggleFullscreen = () => {
    const container = containerRef.current;
    if (!container) return;

    if (!document.fullscreenElement) {
      container.requestFullscreen().catch(err => {
        console.error('全屏失败:', err);
      });
    } else {
      document.exitFullscreen();
    }
  };

  // 重新加载视频
  const reloadVideo = () => {
    const video = videoRef.current;
    if (!video) return;

    video.load();
    setLoading(true);
    setError(null);
  };

  // 格式化时间
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  // 处理进度条变化
  const handleProgressChange = (value: number) => {
    const video = videoRef.current;
    if (!video) return;

    video.currentTime = value;
    setCurrentTime(value);
  };

  // 处理音量变化
  const handleVolumeChange = (value: number) => {
    setVolume(value);
  };

  // 显示控制条
  const showControlsHandler = () => {
    setShowControls(true);
  };

  return (
    <div
      ref={containerRef}
      className={`video-player ${fullscreen ? 'fullscreen' : ''}`}
      style={{ width, height }}
      onMouseMove={showControlsHandler}
      onMouseEnter={showControlsHandler}
    >
      <video
        ref={videoRef}
        className="video-element"
        src={url}
        poster={poster}
        loop={loop}
        controls={controls}
        onClick={togglePlay}
        onDoubleClick={toggleFullscreen}
      />

      {loading && !error && (
        <div className="video-overlay loading">
          <div className="loading-spinner" />
        </div>
      )}

      {error && (
        <div className="video-overlay error">
          <div className="error-message">
            <p>{error}</p>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={reloadVideo}
            >
              {t('videoPlayer.reload')}
            </Button>
          </div>
        </div>
      )}

      {!controls && showControls && !error && (
        <div className="video-controls">
          <div className="progress-bar">
            <Slider
              min={0}
              max={duration}
              value={currentTime}
              onChange={handleProgressChange}
              tooltip={{ formatter: value => formatTime(value || 0) }}
            />
          </div>

          <div className="control-buttons">
            <Space>
              <Button
                type="text"
                icon={playing ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={togglePlay}
              />

              <div
                className="volume-control"
                onMouseEnter={() => setShowVolumeSlider(true)}
                onMouseLeave={() => setShowVolumeSlider(false)}
              >
                <Button
                  type="text"
                  icon={muted || volume === 0 ? <MutedSoundOutlined /> : <SoundOutlined />}
                  onClick={toggleMute}
                />
                {showVolumeSlider && (
                  <div className="volume-slider">
                    <Slider
                      vertical
                      min={0}
                      max={1}
                      step={0.1}
                      value={muted ? 0 : volume}
                      onChange={handleVolumeChange}
                    />
                  </div>
                )}
              </div>

              <span className="time-display">
                {formatTime(currentTime)} / {formatTime(duration)}
              </span>
            </Space>

            <Space>
              <Tooltip title={t('videoPlayer.reload')}>
                <Button
                  type="text"
                  icon={<ReloadOutlined />}
                  onClick={reloadVideo}
                />
              </Tooltip>

              <Tooltip title={t('videoPlayer.download')}>
                <Button
                  type="text"
                  icon={<DownloadOutlined />}
                  onClick={() => window.open(url, '_blank')}
                />
              </Tooltip>

              <Tooltip title={fullscreen ? t('videoPlayer.exitFullscreen') : t('videoPlayer.fullscreen')}>
                <Button
                  type="text"
                  icon={fullscreen ? <ExpandOutlined /> : <FullscreenOutlined />}
                  onClick={toggleFullscreen}
                />
              </Tooltip>
            </Space>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoPlayer;
