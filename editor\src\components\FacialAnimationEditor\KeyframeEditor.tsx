/**
 * 关键帧编辑器组件
 * 提供面部动画关键帧编辑功能
 */
import React, { useState, useEffect } from 'react';
import { Form, Input, InputNumber, Select, Button, Table, Space, Tooltip } from 'antd';
import { DeleteOutlined, CopyOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { Keyframe, Track, TrackType } from './TimelineEditor';
import './KeyframeEditor.less';

const { Option } = Select;

/**
 * 关键帧编辑器属性
 */
interface KeyframeEditorProps {
  /** 实体ID */
  entityId?: string;
  /** 当前时间 */
  currentTime: number;
  /** 动画持续时间 */
  duration: number;
}

/**
 * 表情类型选项
 */
const expressionOptions = [
  { value: 'neutral', label: '中性' },
  { value: 'happy', label: '开心' },
  { value: 'sad', label: '悲伤' },
  { value: 'angry', label: '愤怒' },
  { value: 'surprised', label: '惊讶' },
  { value: 'fear', label: '恐惧' },
  { value: 'disgust', label: '厌恶' },
  { value: 'contempt', label: '蔑视' }
];

/**
 * 口型类型选项
 */
const visemeOptions = [
  { value: 'silent', label: '静默' },
  { value: 'aa', label: 'AA (啊)' },
  { value: 'ee', label: 'EE (衣)' },
  { value: 'ih', label: 'IH (一)' },
  { value: 'oh', label: 'OH (哦)' },
  { value: 'ou', label: 'OU (欧)' },
  { value: 'pp', label: 'PP (噗)' },
  { value: 'ff', label: 'FF (夫)' },
  { value: 'th', label: 'TH (思)' },
  { value: 'dd', label: 'DD (的)' },
  { value: 'kk', label: 'KK (克)' },
  { value: 'ch', label: 'CH (吃)' },
  { value: 'ss', label: 'SS (丝)' },
  { value: 'nn', label: 'NN (呢)' },
  { value: 'rr', label: 'RR (日)' }
];

/**
 * 缓动类型选项
 */
const easingOptions = [
  { value: 'linear', label: '线性' },
  { value: 'easeInQuad', label: '二次方缓入' },
  { value: 'easeOutQuad', label: '二次方缓出' },
  { value: 'easeInOutQuad', label: '二次方缓入缓出' },
  { value: 'easeInCubic', label: '三次方缓入' },
  { value: 'easeOutCubic', label: '三次方缓出' },
  { value: 'easeInOutCubic', label: '三次方缓入缓出' },
  { value: 'easeInElastic', label: '弹性缓入' },
  { value: 'easeOutElastic', label: '弹性缓出' },
  { value: 'easeInOutElastic', label: '弹性缓入缓出' }
];

/**
 * 关键帧编辑器组件
 */
const KeyframeEditor: React.FC<KeyframeEditorProps> = ({
  entityId,
  currentTime,
  duration
}) => {
  const { t } = useTranslation();
  
  // 状态
  const [tracks, setTracks] = useState<Track[]>([]);
  const [selectedTrackId, setSelectedTrackId] = useState<string | null>(null);
  const [selectedKeyframeId, setSelectedKeyframeId] = useState<string | null>(null);
  const [form] = Form.useForm();
  
  // 加载轨道数据
  useEffect(() => {
    if (entityId) {
      // 这里应该从引擎中加载实体的轨道数据
      // 示例数据，实际实现需要与引擎集成
      const mockTracks: Track[] = [
        {
          id: '1',
          name: t('editor.animation.expressionTrack'),
          type: TrackType.EXPRESSION,
          keyframes: [
            { id: '1-1', time: 0, value: 'neutral', type: 'expression', easing: 'linear' },
            { id: '1-2', time: 1, value: 'happy', type: 'expression', easing: 'easeInOutQuad' },
            { id: '1-3', time: 2, value: 'surprised', type: 'expression', easing: 'easeInOutQuad' },
            { id: '1-4', time: 3, value: 'happy', type: 'expression', easing: 'easeInOutQuad' },
            { id: '1-5', time: 4, value: 'neutral', type: 'expression', easing: 'easeOutQuad' }
          ],
          color: '#1890ff'
        },
        {
          id: '2',
          name: t('editor.animation.visemeTrack'),
          type: TrackType.VISEME,
          keyframes: [
            { id: '2-1', time: 0, value: 'silent', type: 'viseme', easing: 'linear' },
            { id: '2-2', time: 0.5, value: 'aa', type: 'viseme', easing: 'easeInQuad' },
            { id: '2-3', time: 1.5, value: 'oh', type: 'viseme', easing: 'easeInOutQuad' },
            { id: '2-4', time: 2.5, value: 'ee', type: 'viseme', easing: 'easeInOutQuad' },
            { id: '2-5', time: 3.5, value: 'silent', type: 'viseme', easing: 'easeOutQuad' }
          ],
          color: '#52c41a'
        }
      ];
      
      setTracks(mockTracks);
      
      if (mockTracks.length > 0) {
        setSelectedTrackId(mockTracks[0].id);
      }
    }
  }, [entityId, t]);
  
  // 当选择的轨道或关键帧变化时，更新表单
  useEffect(() => {
    if (selectedTrackId && selectedKeyframeId) {
      const track = tracks.find(t => t.id === selectedTrackId);
      const keyframe = track?.keyframes.find(kf => kf.id === selectedKeyframeId);
      
      if (keyframe) {
        form.setFieldsValue({
          time: keyframe.time,
          easing: keyframe.easing || 'linear',
          ...(track?.type === TrackType.EXPRESSION 
            ? { expression: keyframe.value } 
            : track?.type === TrackType.VISEME 
              ? { viseme: keyframe.value } 
              : { 
                  expression: keyframe.value.expression,
                  viseme: keyframe.value.viseme
                })
        });
      }
    }
  }, [selectedTrackId, selectedKeyframeId, tracks, form]);
  
  // 添加关键帧
  const handleAddKeyframe = () => {
    if (!selectedTrackId) return;
    
    const track = tracks.find(t => t.id === selectedTrackId);
    if (!track) return;
    
    const newKeyframe: Keyframe = {
      id: `${selectedTrackId}-${Date.now()}`,
      time: currentTime,
      value: track.type === TrackType.EXPRESSION 
        ? 'neutral' 
        : track.type === TrackType.VISEME 
          ? 'silent' 
          : { expression: 'neutral', viseme: 'silent' },
      type: track.type === TrackType.EXPRESSION 
        ? 'expression' 
        : track.type === TrackType.VISEME 
          ? 'viseme' 
          : 'combined',
      easing: 'linear'
    };
    
    const newTracks = tracks.map(t => {
      if (t.id === selectedTrackId) {
        return {
          ...t,
          keyframes: [...t.keyframes, newKeyframe].sort((a, b) => a.time - b.time)
        };
      }
      return t;
    });
    
    setTracks(newTracks);
    setSelectedKeyframeId(newKeyframe.id);
  };
  
  // 删除关键帧
  const handleDeleteKeyframe = (keyframeId: string) => {
    if (!selectedTrackId) return;
    
    const newTracks = tracks.map(track => {
      if (track.id === selectedTrackId) {
        return {
          ...track,
          keyframes: track.keyframes.filter(kf => kf.id !== keyframeId)
        };
      }
      return track;
    });
    
    setTracks(newTracks);
    
    if (selectedKeyframeId === keyframeId) {
      setSelectedKeyframeId(null);
      form.resetFields();
    }
  };
  
  // 复制关键帧
  const handleCopyKeyframe = (keyframeId: string) => {
    if (!selectedTrackId) return;
    
    const track = tracks.find(t => t.id === selectedTrackId);
    const keyframe = track?.keyframes.find(kf => kf.id === keyframeId);
    
    if (!track || !keyframe) return;
    
    const newKeyframe: Keyframe = {
      ...keyframe,
      id: `${selectedTrackId}-${Date.now()}`,
      time: Math.min(duration, keyframe.time + 0.5)
    };
    
    const newTracks = tracks.map(t => {
      if (t.id === selectedTrackId) {
        return {
          ...t,
          keyframes: [...t.keyframes, newKeyframe].sort((a, b) => a.time - b.time)
        };
      }
      return t;
    });
    
    setTracks(newTracks);
    setSelectedKeyframeId(newKeyframe.id);
  };
  
  // 表单值变化处理
  const handleFormValuesChange = (changedValues: any, allValues: any) => {
    if (!selectedTrackId || !selectedKeyframeId) return;
    
    const track = tracks.find(t => t.id === selectedTrackId);
    if (!track) return;
    
    const newTracks = tracks.map(t => {
      if (t.id === selectedTrackId) {
        return {
          ...t,
          keyframes: t.keyframes.map(kf => {
            if (kf.id === selectedKeyframeId) {
              return {
                ...kf,
                time: allValues.time,
                easing: allValues.easing,
                value: t.type === TrackType.EXPRESSION 
                  ? allValues.expression 
                  : t.type === TrackType.VISEME 
                    ? allValues.viseme 
                    : { 
                        expression: allValues.expression,
                        viseme: allValues.viseme
                      }
              };
            }
            return kf;
          }).sort((a, b) => a.time - b.time)
        };
      }
      return t;
    });
    
    setTracks(newTracks);
  };
  
  // 表格列定义
  const columns = [
    {
      title: t('editor.animation.time'),
      dataIndex: 'time',
      key: 'time',
      render: (time: number) => time.toFixed(2),
      sorter: (a: Keyframe, b: Keyframe) => a.time - b.time
    },
    {
      title: t('editor.animation.value'),
      dataIndex: 'value',
      key: 'value',
      render: (value: any, record: Keyframe) => {
        if (typeof value === 'object') {
          return `${value.expression}, ${value.viseme}`;
        }
        return value;
      }
    },
    {
      title: t('editor.animation.easing'),
      dataIndex: 'easing',
      key: 'easing'
    },
    {
      title: t('editor.animation.actions'),
      key: 'actions',
      render: (_: any, record: Keyframe) => (
        <Space>
          <Tooltip title={t('editor.animation.copyKeyframe')}>
            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => handleCopyKeyframe(record.id)}
            />
          </Tooltip>
          <Tooltip title={t('editor.animation.deleteKeyframe')}>
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteKeyframe(record.id)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];
  
  // 获取当前选中的轨道
  const selectedTrack = tracks.find(t => t.id === selectedTrackId);
  
  return (
    <div className="keyframe-editor">
      <div className="keyframe-editor-header">
        <div className="track-selector">
          <span>{t('editor.animation.track')}:</span>
          <Select
            value={selectedTrackId || undefined}
            onChange={setSelectedTrackId}
            style={{ width: 200 }}
            placeholder={t('editor.animation.selectTrack')}
          >
            {tracks.map(track => (
              <Option key={track.id} value={track.id}>
                {track.name}
              </Option>
            ))}
          </Select>
        </div>
        
        <Button
          type="primary"
          onClick={handleAddKeyframe}
          disabled={!selectedTrackId}
        >
          {t('editor.animation.addKeyframe')}
        </Button>
      </div>
      
      <div className="keyframe-list">
        <Table
          dataSource={selectedTrack?.keyframes || []}
          columns={columns}
          rowKey="id"
          pagination={false}
          size="small"
          rowClassName={(record) => record.id === selectedKeyframeId ? 'selected-row' : ''}
          onRow={(record) => ({
            onClick: () => setSelectedKeyframeId(record.id)
          })}
        />
      </div>
      
      {selectedKeyframeId && (
        <div className="keyframe-form">
          <h3>{t('editor.animation.keyframeProperties')}</h3>
          <Form
            form={form}
            layout="vertical"
            onValuesChange={handleFormValuesChange}
          >
            <Form.Item
              name="time"
              label={t('editor.animation.time')}
              rules={[{ required: true }]}
            >
              <InputNumber
                min={0}
                max={duration}
                step={0.01}
                style={{ width: '100%' }}
              />
            </Form.Item>
            
            {selectedTrack?.type === TrackType.EXPRESSION && (
              <Form.Item
                name="expression"
                label={t('editor.animation.expression')}
                rules={[{ required: true }]}
              >
                <Select>
                  {expressionOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            )}
            
            {selectedTrack?.type === TrackType.VISEME && (
              <Form.Item
                name="viseme"
                label={t('editor.animation.viseme')}
                rules={[{ required: true }]}
              >
                <Select>
                  {visemeOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            )}
            
            {selectedTrack?.type === TrackType.COMBINED && (
              <>
                <Form.Item
                  name="expression"
                  label={t('editor.animation.expression')}
                  rules={[{ required: true }]}
                >
                  <Select>
                    {expressionOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                
                <Form.Item
                  name="viseme"
                  label={t('editor.animation.viseme')}
                  rules={[{ required: true }]}
                >
                  <Select>
                    {visemeOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </>
            )}
            
            <Form.Item
              name="easing"
              label={t('editor.animation.easing')}
              rules={[{ required: true }]}
            >
              <Select>
                {easingOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Form>
        </div>
      )}
    </div>
  );
};

export default KeyframeEditor;
