/**
 * 响应式设计服务
 * 提供响应式设计相关的功能和配置
 */
import { EventEmitter } from 'events';
import MobileDeviceService, { DeviceType, ScreenOrientation } from './MobileDeviceService';
import MobileLayoutService from './MobileLayoutService';
import LayoutService from './LayoutService';

// 响应式设计事件类型
export enum ResponsiveEventType {
  BREAKPOINT_CHANGED = 'breakpointChanged',
  ORIENTATION_CHANGED = 'orientationChanged',
  DEVICE_TYPE_CHANGED = 'deviceTypeChanged',
  TOUCH_MODE_CHANGED = 'touchModeChanged',
  LAYOUT_OPTIMIZED = 'layoutOptimized',
  CONTROL_SIZE_CHANGED = 'controlSizeChanged'
}

// 响应式断点枚举
export enum ResponsiveBreakpoint {
  XS = 'xs',
  SM = 'sm',
  MD = 'md',
  LG = 'lg',
  XL = 'xl',
  XXL = 'xxl'
}

// 控件尺寸枚举
export enum ControlSize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
  EXTRA_LARGE = 'extraLarge'
}

// 响应式设计配置接口
export interface ResponsiveDesignConfig {
  // 是否启用调试模式
  debug?: boolean;
  // 是否启用自动响应式布局
  enableAutoLayout?: boolean;
  // 是否启用触控优化
  enableTouchOptimization?: boolean;
  // 是否启用控件尺寸自动调整
  enableControlSizeAdjustment?: boolean;
  // 是否启用方向响应
  enableOrientationResponse?: boolean;
  // 断点配置（像素）
  breakpoints?: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
  };
  // 控件尺寸配置
  controlSizes?: {
    small: {
      buttonHeight: number;
      buttonPadding: number;
      fontSize: number;
      iconSize: number;
      borderRadius: number;
    };
    medium: {
      buttonHeight: number;
      buttonPadding: number;
      fontSize: number;
      iconSize: number;
      borderRadius: number;
    };
    large: {
      buttonHeight: number;
      buttonPadding: number;
      fontSize: number;
      iconSize: number;
      borderRadius: number;
    };
    extraLarge: {
      buttonHeight: number;
      buttonPadding: number;
      fontSize: number;
      iconSize: number;
      borderRadius: number;
    };
  };
}

/**
 * 响应式设计服务类
 * 提供响应式设计相关的功能和配置
 */
export class ResponsiveDesignService extends EventEmitter {
  private static instance: ResponsiveDesignService;
  private mobileDeviceService: MobileDeviceService;
  private mobileLayoutService: MobileLayoutService;
  private layoutService: LayoutService;
  private config: ResponsiveDesignConfig;

  // 当前断点
  private currentBreakpoint: ResponsiveBreakpoint = ResponsiveBreakpoint.LG;
  
  // 当前控件尺寸
  private currentControlSize: ControlSize = ControlSize.MEDIUM;
  
  // 是否处于触控模式
  private touchMode: boolean = false;

  /**
   * 获取单例实例
   * @returns 响应式设计服务实例
   */
  public static getInstance(): ResponsiveDesignService {
    if (!ResponsiveDesignService.instance) {
      ResponsiveDesignService.instance = new ResponsiveDesignService();
    }
    return ResponsiveDesignService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    super();
    this.mobileDeviceService = MobileDeviceService.getInstance();
    this.mobileLayoutService = MobileLayoutService.getInstance();
    this.layoutService = LayoutService.getInstance();

    // 默认配置
    this.config = {
      debug: false,
      enableAutoLayout: true,
      enableTouchOptimization: true,
      enableControlSizeAdjustment: true,
      enableOrientationResponse: true,
      breakpoints: {
        xs: 480,
        sm: 576,
        md: 768,
        lg: 992,
        xl: 1200,
        xxl: 1600
      },
      controlSizes: {
        small: {
          buttonHeight: 32,
          buttonPadding: 8,
          fontSize: 12,
          iconSize: 14,
          borderRadius: 4
        },
        medium: {
          buttonHeight: 40,
          buttonPadding: 12,
          fontSize: 14,
          iconSize: 16,
          borderRadius: 6
        },
        large: {
          buttonHeight: 48,
          buttonPadding: 16,
          fontSize: 16,
          iconSize: 20,
          borderRadius: 8
        },
        extraLarge: {
          buttonHeight: 56,
          buttonPadding: 20,
          fontSize: 18,
          iconSize: 24,
          borderRadius: 10
        }
      }
    };

    // 初始化
    this.initialize();
  }

  /**
   * 初始化
   */
  private initialize(): void {
    // 监听设备变化
    this.mobileDeviceService.on('deviceChanged', this.handleDeviceChanged.bind(this));
    this.mobileDeviceService.on('orientationChanged', this.handleOrientationChanged.bind(this));
    this.mobileDeviceService.on('sizeChanged', this.handleSizeChanged.bind(this));

    // 初始化断点和控件尺寸
    this.updateBreakpoint();
    this.updateControlSize();
    this.updateTouchMode();

    // 添加窗口事件监听
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', this.handleResize.bind(this));
    }
  }

  /**
   * 配置服务
   * @param config 配置
   */
  public configure(config: Partial<ResponsiveDesignConfig>): void {
    this.config = { ...this.config, ...config };

    if (this.config.debug) {
      console.log('响应式设计服务配置已更新', this.config);
    }

    // 更新断点和控件尺寸
    this.updateBreakpoint();
    this.updateControlSize();
  }

  /**
   * 处理设备变化事件
   * @param deviceInfo 设备信息
   */
  private handleDeviceChanged(deviceInfo: any): void {
    // 更新触控模式
    this.updateTouchMode();

    // 发出设备类型变化事件
    this.emit(ResponsiveEventType.DEVICE_TYPE_CHANGED, {
      deviceType: deviceInfo.type,
      isMobile: deviceInfo.isMobile,
      isTablet: deviceInfo.isTablet,
      isTouch: deviceInfo.isTouch
    });

    // 更新控件尺寸
    if (this.config.enableControlSizeAdjustment) {
      this.updateControlSize();
    }

    // 优化布局
    if (this.config.enableAutoLayout) {
      this.optimizeLayout();
    }
  }

  /**
   * 处理屏幕方向变化事件
   * @param deviceInfo 设备信息
   */
  private handleOrientationChanged(deviceInfo: any): void {
    // 发出方向变化事件
    this.emit(ResponsiveEventType.ORIENTATION_CHANGED, {
      orientation: deviceInfo.orientation
    });

    // 更新断点
    this.updateBreakpoint();

    // 优化布局
    if (this.config.enableOrientationResponse && this.config.enableAutoLayout) {
      this.optimizeLayout();
    }
  }

  /**
   * 处理尺寸变化事件
   */
  private handleSizeChanged(): void {
    // 更新断点
    this.updateBreakpoint();

    // 更新控件尺寸
    if (this.config.enableControlSizeAdjustment) {
      this.updateControlSize();
    }
  }

  /**
   * 处理窗口大小改变事件
   */
  private handleResize(): void {
    // 更新断点
    this.updateBreakpoint();

    // 更新控件尺寸
    if (this.config.enableControlSizeAdjustment) {
      this.updateControlSize();
    }

    // 优化布局
    if (this.config.enableAutoLayout) {
      this.optimizeLayout();
    }
  }

  /**
   * 更新断点
   */
  private updateBreakpoint(): void {
    const width = window.innerWidth;
    let newBreakpoint: ResponsiveBreakpoint;

    // 根据窗口宽度确定断点
    if (width < this.config.breakpoints!.xs) {
      newBreakpoint = ResponsiveBreakpoint.XS;
    } else if (width < this.config.breakpoints!.sm) {
      newBreakpoint = ResponsiveBreakpoint.SM;
    } else if (width < this.config.breakpoints!.md) {
      newBreakpoint = ResponsiveBreakpoint.MD;
    } else if (width < this.config.breakpoints!.lg) {
      newBreakpoint = ResponsiveBreakpoint.LG;
    } else if (width < this.config.breakpoints!.xl) {
      newBreakpoint = ResponsiveBreakpoint.XL;
    } else {
      newBreakpoint = ResponsiveBreakpoint.XXL;
    }

    // 如果断点发生变化，则发出事件
    if (newBreakpoint !== this.currentBreakpoint) {
      const oldBreakpoint = this.currentBreakpoint;
      this.currentBreakpoint = newBreakpoint;

      this.emit(ResponsiveEventType.BREAKPOINT_CHANGED, {
        oldBreakpoint,
        newBreakpoint,
        width
      });

      if (this.config.debug) {
        console.log(`断点已变化: ${oldBreakpoint} -> ${newBreakpoint}`);
      }
    }
  }

  /**
   * 更新控件尺寸
   */
  private updateControlSize(): void {
    let newControlSize: ControlSize;
    const deviceInfo = this.mobileDeviceService.getDeviceInfo();

    // 根据设备类型和断点确定控件尺寸
    if (deviceInfo.type === DeviceType.MOBILE) {
      newControlSize = ControlSize.LARGE;
    } else if (deviceInfo.type === DeviceType.TABLET) {
      newControlSize = ControlSize.MEDIUM;
    } else {
      // 桌面设备根据断点确定
      if (this.currentBreakpoint === ResponsiveBreakpoint.XS || this.currentBreakpoint === ResponsiveBreakpoint.SM) {
        newControlSize = ControlSize.MEDIUM;
      } else {
        newControlSize = ControlSize.SMALL;
      }
    }

    // 如果是触摸设备，则增大控件尺寸
    if (deviceInfo.isTouch && this.config.enableTouchOptimization) {
      if (newControlSize === ControlSize.SMALL) {
        newControlSize = ControlSize.MEDIUM;
      } else if (newControlSize === ControlSize.MEDIUM) {
        newControlSize = ControlSize.LARGE;
      } else if (newControlSize === ControlSize.LARGE) {
        newControlSize = ControlSize.EXTRA_LARGE;
      }
    }

    // 如果控件尺寸发生变化，则发出事件
    if (newControlSize !== this.currentControlSize) {
      const oldControlSize = this.currentControlSize;
      this.currentControlSize = newControlSize;

      this.emit(ResponsiveEventType.CONTROL_SIZE_CHANGED, {
        oldControlSize,
        newControlSize,
        controlSizeConfig: this.config.controlSizes![newControlSize]
      });

      if (this.config.debug) {
        console.log(`控件尺寸已变化: ${oldControlSize} -> ${newControlSize}`);
      }
    }
  }

  /**
   * 更新触控模式
   */
  private updateTouchMode(): void {
    const deviceInfo = this.mobileDeviceService.getDeviceInfo();
    const newTouchMode = deviceInfo.isTouch;

    // 如果触控模式发生变化，则发出事件
    if (newTouchMode !== this.touchMode) {
      const oldTouchMode = this.touchMode;
      this.touchMode = newTouchMode;

      this.emit(ResponsiveEventType.TOUCH_MODE_CHANGED, {
        oldTouchMode,
        newTouchMode
      });

      if (this.config.debug) {
        console.log(`触控模式已变化: ${oldTouchMode} -> ${newTouchMode}`);
      }
    }
  }

  /**
   * 优化布局
   */
  private optimizeLayout(): void {
    // 获取设备信息
    const deviceInfo = this.mobileDeviceService.getDeviceInfo();

    // 如果是移动设备，则使用移动布局服务优化布局
    if (deviceInfo.isMobile || deviceInfo.isTablet) {
      this.mobileLayoutService.optimizeCurrentLayout();
    } else {
      // 桌面设备根据断点优化布局
      this.optimizeDesktopLayout();
    }

    // 发出布局优化事件
    this.emit(ResponsiveEventType.LAYOUT_OPTIMIZED, {
      deviceType: deviceInfo.type,
      breakpoint: this.currentBreakpoint,
      orientation: deviceInfo.orientation
    });
  }

  /**
   * 优化桌面布局
   */
  private optimizeDesktopLayout(): void {
    // 根据断点选择合适的布局
    let layoutName = '';

    switch (this.currentBreakpoint) {
      case ResponsiveBreakpoint.XS:
      case ResponsiveBreakpoint.SM:
        layoutName = 'compact';
        break;
      case ResponsiveBreakpoint.MD:
        layoutName = 'balanced';
        break;
      case ResponsiveBreakpoint.LG:
      case ResponsiveBreakpoint.XL:
      case ResponsiveBreakpoint.XXL:
        layoutName = 'expanded';
        break;
    }

    // 应用布局
    if (layoutName && this.layoutService) {
      this.layoutService.loadPredefinedLayout(layoutName);
    }
  }

  /**
   * 获取当前断点
   * @returns 当前断点
   */
  public getCurrentBreakpoint(): ResponsiveBreakpoint {
    return this.currentBreakpoint;
  }

  /**
   * 获取当前控件尺寸
   * @returns 当前控件尺寸
   */
  public getCurrentControlSize(): ControlSize {
    return this.currentControlSize;
  }

  /**
   * 获取控件尺寸配置
   * @param size 控件尺寸
   * @returns 控件尺寸配置
   */
  public getControlSizeConfig(size?: ControlSize): any {
    const sizeToUse = size || this.currentControlSize;
    return this.config.controlSizes![sizeToUse];
  }

  /**
   * 是否处于触控模式
   * @returns 是否处于触控模式
   */
  public isTouchMode(): boolean {
    return this.touchMode;
  }

  /**
   * 是否是移动设备
   * @returns 是否是移动设备
   */
  public isMobileDevice(): boolean {
    return this.mobileDeviceService.isMobileDevice();
  }

  /**
   * 是否是平板设备
   * @returns 是否是平板设备
   */
  public isTabletDevice(): boolean {
    return this.mobileDeviceService.isTabletDevice();
  }
}

export default ResponsiveDesignService.getInstance();
