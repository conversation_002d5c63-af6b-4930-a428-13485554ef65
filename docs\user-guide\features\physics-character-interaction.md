# 物理驱动的角色交互

本文档介绍如何使用物理驱动的角色交互系统，实现角色与物理对象之间的交互，包括推动、拉动、举起、投掷、攀爬和悬挂等功能。

## 概述

物理驱动的角色交互系统允许角色与场景中的物理对象进行各种交互。这些交互基于物理引擎，提供真实的物理反馈和约束。系统主要由以下部分组成：

- **物理角色交互系统**：管理角色与物理对象之间的交互
- **物理交互组件**：附加到可交互对象上，定义交互参数和行为
- **物理交互约束**：实现角色与物理对象之间的物理约束

## 交互类型

系统支持以下交互类型：

- **推动（Push）**：角色可以推动物体
- **拉动（Pull）**：角色可以拉动物体
- **举起（Lift）**：角色可以举起物体
- **投掷（Throw）**：角色可以投掷物体
- **攀爬（Climb）**：角色可以攀爬物体
- **悬挂（Hang）**：角色可以悬挂在物体上

## 系统组件

### 物理角色交互系统（PhysicsCharacterInteractionSystem）

物理角色交互系统是管理角色与物理对象交互的核心系统。它负责创建和管理交互约束，处理交互事件，并提供调试功能。

```typescript
// 创建物理角色交互系统
const interactionSystem = new PhysicsCharacterInteractionSystem({
  debug: true,                    // 是否启用调试
  maxInteractionDistance: 2.0,    // 最大交互距离
  maxInteractionForce: 1000.0,    // 最大交互力
  interactionForceDamping: 0.5,   // 交互力衰减系数
  enableForceFeedback: true,      // 是否启用力反馈
  enableInteractionAnimation: true // 是否启用交互动画
});

// 添加到引擎
engine.addSystem(interactionSystem);
```

### 物理交互组件（PhysicsInteractionComponent）

物理交互组件附加到可交互的物理对象上，定义对象的交互参数和行为。

```typescript
// 创建物理交互组件
const interactionComponent = new PhysicsInteractionComponent({
  enabled: true,                  // 是否启用
  maxInteractionDistance: 2.0,    // 最大交互距离
  interactionForce: 500.0,        // 交互力大小
  interactionForceDamping: 0.5,   // 交互力衰减系数
  allowedInteractionTypes: [      // 允许的交互类型
    InteractionType.PUSH,
    InteractionType.PULL
  ],
  canBePushed: true,              // 是否可以被推动
  canBePulled: true,              // 是否可以被拉动
  canBeLifted: false,             // 是否可以被举起
  canBeThrown: false,             // 是否可以被投掷
  canBeClimbed: false,            // 是否可以被攀爬
  canBeHanged: false,             // 是否可以被悬挂
  onInteractionStart: (entity, interactor, type) => {
    console.log(`开始交互: ${interactor.id} -> ${entity.id} (${type})`);
  },
  onInteractionEnd: (entity, interactor, type) => {
    console.log(`结束交互: ${interactor.id} -> ${entity.id} (${type})`);
  }
});

// 添加到实体
entity.addComponent(interactionComponent);
```

## 使用方法

### 开始交互

要开始交互，需要调用物理角色交互系统的`startInteraction`方法：

```typescript
// 开始交互
const success = interactionSystem.startInteraction(
  characterEntity,    // 角色实体
  targetEntity,       // 目标实体
  InteractionType.PUSH // 交互类型
);

if (success) {
  console.log('交互开始成功');
} else {
  console.log('交互开始失败');
}
```

### 结束交互

要结束交互，需要调用物理角色交互系统的`terminateInteraction`方法：

```typescript
// 结束交互
interactionSystem.terminateInteraction(
  characterEntity.id, // 角色实体ID
  targetEntity.id     // 目标实体ID
);
```

### 监听交互事件

可以监听交互系统的事件来响应交互状态的变化：

```typescript
// 监听交互开始事件
interactionSystem.on('interactionStart', (data) => {
  console.log(`交互开始: ${data.character.id} -> ${data.target.id} (${data.type})`);
});

// 监听交互结束事件
interactionSystem.on('interactionEnd', (data) => {
  console.log(`交互结束: ${data.character.id} -> ${data.target.id}`);
});
```

## 示例

以下是一个完整的示例，展示如何创建可交互的物体：

```typescript
// 创建可交互的箱子
const box = new Entity('interactable_box');

// 添加变换组件
box.addComponent(new TransformComponent({
  position: { x: 0, y: 0.5, z: 0 },
  rotation: { x: 0, y: 0, z: 0 },
  scale: { x: 1, y: 1, z: 1 }
}));

// 添加网格组件
box.addComponent(new MeshComponent({
  geometry: new THREE.BoxGeometry(1, 1, 1),
  material: new THREE.MeshStandardMaterial({ color: 0xff0000 })
}));

// 添加物理体组件
box.addComponent(new PhysicsBody({
  type: 'dynamic',
  mass: 10
}));

// 添加碰撞器组件
box.addComponent(new PhysicsCollider({
  type: 'box',
  size: { x: 1, y: 1, z: 1 }
}));

// 添加交互组件
box.addComponent(new PhysicsInteractionComponent({
  enabled: true,
  maxInteractionDistance: 2.0,
  interactionForce: 500.0,
  interactionForceDamping: 0.5,
  allowedInteractionTypes: [
    InteractionType.PUSH,
    InteractionType.PULL,
    InteractionType.LIFT,
    InteractionType.THROW
  ],
  canBePushed: true,
  canBePulled: true,
  canBeLifted: true,
  canBeThrown: true
}));

// 添加到世界
world.addEntity(box);
```

## 编辑器使用

在编辑器中，可以使用物理交互编辑器来编辑物理交互组件的参数：

1. 选择一个实体
2. 在物理编辑器页面中，找到"物理交互"面板
3. 设置交互参数，如交互类型、交互力等
4. 可以使用预设来快速应用常用的交互配置

## 最佳实践

- **合理设置交互距离**：根据游戏类型和角色大小设置合适的交互距离
- **调整交互力**：根据物体质量调整交互力大小，避免力过大或过小
- **使用交互预设**：为不同类型的物体创建交互预设，提高工作效率
- **添加视觉反馈**：在交互开始和结束时添加视觉效果，提升用户体验
- **结合动画系统**：将交互与角色动画系统结合，实现更真实的交互效果

## 调试

系统提供了调试功能，可以可视化交互约束和力：

```typescript
// 启用调试
const interactionSystem = new PhysicsCharacterInteractionSystem({
  debug: true
});
```

启用调试后，系统会在场景中显示交互线条和点，帮助开发者理解交互状态。

## 性能优化

- 限制同时交互的物体数量
- 对远离摄像机的交互降低物理精度
- 使用物理预设减少计算量
- 对小物体使用简化的物理模型

## 常见问题

### 交互不起作用

- 检查交互距离是否合适
- 确保物体有物理体和碰撞器组件
- 验证交互类型是否在允许列表中
- 检查物体的交互标志（如canBePushed等）

### 交互力过大或过小

- 调整interactionForce参数
- 修改interactionForceDamping参数
- 考虑物体的质量设置

### 物体交互时抖动

- 增加物理步长
- 减小交互力
- 增加阻尼系数
- 使用约束稳定器
