/**
 * 输入动画集成
 * 用于将输入系统与动画系统集成
 */
import * as THREE from 'three';
import type { Entity } from '../core/Entity';
import { InputSystem } from '../input/InputSystem';
import { InputComponent } from '../input/components/InputComponent';
import { InputAction } from '../input/InputAction';
import { AnimationBlender } from './AnimationBlender';
import { Animator } from './Animator';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 输入动画集成事件类型
 */
export enum InputAnimationEventType {
  /** 输入状态改变 */
  INPUT_STATE_CHANGED = 'inputStateChanged',
  /** 动作触发 */
  ACTION_TRIGGERED = 'actionTriggered',
  /** 动作结束 */
  ACTION_ENDED = 'actionEnded'
}

/**
 * 输入动画集成配置
 */
export interface InputAnimationIntegrationConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否自动更新动画参数 */
  autoUpdateParameters?: boolean;
  /** 移动输入动作名称 */
  moveActionName?: string;
  /** 跳跃输入动作名称 */
  jumpActionName?: string;
  /** 奔跑输入动作名称 */
  runActionName?: string;
  /** 攻击输入动作名称 */
  attackActionName?: string;
  /** 防御输入动作名称 */
  defendActionName?: string;
  /** 交互输入动作名称 */
  interactActionName?: string;
  /** 自定义输入动作映射 */
  customActionMappings?: Map<string, string>;
  /** 是否使用手势输入 */
  useGestureInput?: boolean;
  /** 是否使用语音输入 */
  useVoiceInput?: boolean;
}

/**
 * 输入动画集成
 * 用于将输入系统与动画系统集成
 */
export class InputAnimationIntegration {
  /** 实体 */
  private entity: Entity;
  /** 输入系统 */
  private inputSystem: InputSystem;
  /** 动画混合器 */
  private blender: AnimationBlender;
  /** 动画控制器 */
  private animator: Animator;
  /** 配置 */
  private config: InputAnimationIntegrationConfig;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 输入动作映射 */
  private actionMappings: Map<string, string> = new Map();
  /** 当前输入状态 */
  private inputState: Map<string, any> = new Map();
  /** 是否已初始化 */
  private initialized: boolean = false;
  /** 是否已销毁 */
  private destroyed: boolean = false;

  /**
   * 创建输入动画集成
   * @param entity 实体
   * @param inputSystem 输入系统
   * @param blender 动画混合器
   * @param config 配置
   */
  constructor(
    entity: Entity,
    inputSystem: InputSystem,
    blender: AnimationBlender,
    config: InputAnimationIntegrationConfig = {}
  ) {
    this.entity = entity;
    this.inputSystem = inputSystem;
    this.blender = blender;
    this.animator = blender.getAnimator();

    // 设置配置
    this.config = {
      debug: config.debug || false,
      autoUpdateParameters: config.autoUpdateParameters !== undefined ? config.autoUpdateParameters : true,
      moveActionName: config.moveActionName || 'move',
      jumpActionName: config.jumpActionName || 'jump',
      runActionName: config.runActionName || 'run',
      attackActionName: config.attackActionName || 'attack',
      defendActionName: config.defendActionName || 'defend',
      interactActionName: config.interactActionName || 'interact',
      customActionMappings: config.customActionMappings || new Map(),
      useGestureInput: config.useGestureInput || false,
      useVoiceInput: config.useVoiceInput || false
    };

    // 初始化动作映射
    this.initializeActionMappings();
  }

  /**
   * 初始化
   */
  public initialize(): void {
    if (this.initialized || this.destroyed) return;

    // 检查实体是否有输入组件
    const inputComponent = this.entity.getComponent<InputComponent>(InputComponent.TYPE);
    if (!inputComponent) {
      console.warn('实体没有输入组件，输入动画集成将不会工作');
      return;
    }

    // 添加输入事件监听器
    this.addInputEventListeners();

    this.initialized = true;

    if (this.config.debug) {
      console.log('输入动画集成已初始化');
    }
  }

  /**
   * 销毁
   */
  public destroy(): void {
    if (this.destroyed) return;

    // 移除输入事件监听器
    this.removeInputEventListeners();

    // 清理事件监听器
    this.eventEmitter.removeAllListeners();

    this.destroyed = true;

    if (this.config.debug) {
      console.log('输入动画集成已销毁');
    }
  }

  /**
   * 更新
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized || this.destroyed) return;

    // 获取输入组件
    const inputComponent = this.entity.getComponent<InputComponent>(InputComponent.TYPE);
    if (!inputComponent) return;

    // 如果自动更新参数，更新动画参数
    if (this.config.autoUpdateParameters) {
      this.updateAnimationParameters();
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public addEventListener(event: InputAnimationEventType, listener: (data: any) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public removeEventListener(event: InputAnimationEventType, listener: (data: any) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 初始化动作映射
   */
  private initializeActionMappings(): void {
    // 添加默认动作映射
    this.actionMappings.set(this.config.moveActionName!, 'moveDirection');
    this.actionMappings.set(this.config.jumpActionName!, 'jump');
    this.actionMappings.set(this.config.runActionName!, 'isRunning');
    this.actionMappings.set(this.config.attackActionName!, 'attack');
    this.actionMappings.set(this.config.defendActionName!, 'defend');
    this.actionMappings.set(this.config.interactActionName!, 'interact');

    // 添加自定义动作映射
    if (this.config.customActionMappings) {
      for (const [actionName, paramName] of this.config.customActionMappings.entries()) {
        this.actionMappings.set(actionName, paramName);
      }
    }
  }

  /**
   * 添加输入事件监听器
   */
  private addInputEventListeners(): void {
    // 获取输入组件
    const inputComponent = this.entity.getComponent<InputComponent>(InputComponent.TYPE);
    if (!inputComponent) return;

    // 遍历动作映射
    for (const [actionName, paramName] of this.actionMappings.entries()) {
      // 获取动作
      const action = inputComponent.getAction(actionName);
      if (!action) continue;

      // 添加事件监听器
      action.addEventListener('changed', this.handleInputChanged.bind(this, actionName, paramName));
      action.addEventListener('started', this.handleInputStarted.bind(this, actionName, paramName));
      action.addEventListener('ended', this.handleInputEnded.bind(this, actionName, paramName));
    }

    // 如果使用手势输入，添加手势事件监听器
    if (this.config.useGestureInput) {
      this.inputSystem.addEventListener('gesture', this.handleGestureInput.bind(this));
    }

    // 如果使用语音输入，添加语音事件监听器
    if (this.config.useVoiceInput) {
      this.inputSystem.addEventListener('voice', this.handleVoiceInput.bind(this));
    }
  }

  /**
   * 移除输入事件监听器
   */
  private removeInputEventListeners(): void {
    // 获取输入组件
    const inputComponent = this.entity.getComponent<InputComponent>(InputComponent.TYPE);
    if (!inputComponent) return;

    // 遍历动作映射
    for (const [actionName, paramName] of this.actionMappings.entries()) {
      // 获取动作
      const action = inputComponent.getAction(actionName);
      if (!action) continue;

      // 移除事件监听器
      action.removeEventListener('changed', this.handleInputChanged.bind(this, actionName, paramName));
      action.removeEventListener('started', this.handleInputStarted.bind(this, actionName, paramName));
      action.removeEventListener('ended', this.handleInputEnded.bind(this, actionName, paramName));
    }

    // 如果使用手势输入，移除手势事件监听器
    if (this.config.useGestureInput) {
      this.inputSystem.removeEventListener('gesture', this.handleGestureInput.bind(this));
    }

    // 如果使用语音输入，移除语音事件监听器
    if (this.config.useVoiceInput) {
      this.inputSystem.removeEventListener('voice', this.handleVoiceInput.bind(this));
    }
  }

  /**
   * 更新动画参数
   */
  private updateAnimationParameters(): void {
    // 遍历输入状态
    for (const [paramName, value] of this.inputState.entries()) {
      // 更新动画参数
      this.animator.setParameter(paramName, value);
    }

    // 特殊处理移动方向
    const moveDirection = this.inputState.get('moveDirection') as THREE.Vector2;
    if (moveDirection) {
      // 计算移动速度
      const moveSpeed = moveDirection.length();
      this.animator.setParameter('moveSpeed', moveSpeed);

      // 如果移动速度足够大，计算移动方向
      if (moveSpeed > 0.1) {
        const moveAngle = Math.atan2(moveDirection.x, moveDirection.y);
        this.animator.setParameter('moveAngle', moveAngle);
      }

      // 更新是否移动
      this.animator.setParameter('isMoving', moveSpeed > 0.1);
    }

    // 特殊处理奔跑
    const isRunning = this.inputState.get('isRunning') as boolean;
    if (isRunning !== undefined) {
      this.animator.setParameter('isRunning', isRunning);
    }
  }

  /**
   * 处理输入改变事件
   * @param actionName 动作名称
   * @param paramName 参数名称
   * @param action 输入动作
   */
  private handleInputChanged(actionName: string, paramName: string, action: InputAction): void {
    // 更新输入状态
    this.inputState.set(paramName, action.getValue());

    // 触发输入状态改变事件
    this.eventEmitter.emit(InputAnimationEventType.INPUT_STATE_CHANGED, {
      actionName,
      paramName,
      value: action.getValue()
    });

    if (this.config.debug) {
      console.log(`输入改变: ${actionName} -> ${paramName} = ${action.getValue()}`);
    }
  }

  /**
   * 处理输入开始事件
   * @param actionName 动作名称
   * @param paramName 参数名称
   * @param action 输入动作
   */
  private handleInputStarted(actionName: string, paramName: string, action: InputAction): void {
    // 更新输入状态
    this.inputState.set(paramName, action.getValue());

    // 触发动作触发事件
    this.eventEmitter.emit(InputAnimationEventType.ACTION_TRIGGERED, {
      actionName,
      paramName,
      value: action.getValue()
    });

    if (this.config.debug) {
      console.log(`输入开始: ${actionName} -> ${paramName} = ${action.getValue()}`);
    }
  }

  /**
   * 处理输入结束事件
   * @param actionName 动作名称
   * @param paramName 参数名称
   * @param action 输入动作
   */
  private handleInputEnded(actionName: string, paramName: string, action: InputAction): void {
    // 更新输入状态
    this.inputState.set(paramName, action.getValue());

    // 触发动作结束事件
    this.eventEmitter.emit(InputAnimationEventType.ACTION_ENDED, {
      actionName,
      paramName,
      value: action.getValue()
    });

    if (this.config.debug) {
      console.log(`输入结束: ${actionName} -> ${paramName} = ${action.getValue()}`);
    }
  }

  /**
   * 处理手势输入
   * @param event 手势事件
   */
  private handleGestureInput(event: any): void {
    // 这里可以根据手势类型触发不同的动画
    // 例如，挥手手势可以触发挥手动画
    if (event.gesture === 'wave') {
      this.animator.setParameter('wave', true);

      // 延迟重置参数
      setTimeout(() => {
        this.animator.setParameter('wave', false);
      }, 1000);
    }
  }

  /**
   * 处理语音输入
   * @param event 语音事件
   */
  private handleVoiceInput(event: any): void {
    // 这里可以根据语音命令触发不同的动画
    // 例如，"跳跃"命令可以触发跳跃动画
    if (event.command === 'jump') {
      this.animator.setParameter('jump', true);

      // 延迟重置参数
      setTimeout(() => {
        this.animator.setParameter('jump', false);
      }, 1000);
    }
  }
}
