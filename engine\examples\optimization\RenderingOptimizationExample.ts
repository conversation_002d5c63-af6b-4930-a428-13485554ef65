/**
 * 渲染优化示例
 * 展示如何使用LOD、视锥体剔除和实例化渲染
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { World } from '../../src/core/World';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Renderer } from '../../src/rendering/Renderer';
import { Camera } from '../../src/rendering/Camera';
import { Light, LightType } from '../../src/rendering/Light';
import { Transform } from '../../src/scene/Transform';
import { RenderSystem } from '../../src/rendering/RenderSystem';
import {
  LODSystem,
  LODComponent,
  LODLevel,
  LODGenerator,
  FrustumCullingSystem,
  CullableComponent,
  InstancedRenderingSystem,
  InstancedComponent
} from '../../src/rendering/optimization';

/**
 * 渲染优化示例类
 */
export class RenderingOptimizationExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 世界实例 */
  private world: World;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 相机实体 */
  private cameraEntity: Entity;
  
  /** 方向光实体 */
  private directionalLightEntity: Entity;
  
  /** 环境光实体 */
  private ambientLightEntity: Entity;
  
  /** 渲染系统 */
  private renderSystem: RenderSystem;
  
  /** LOD系统 */
  private lodSystem: LODSystem;
  
  /** 视锥体剔除系统 */
  private frustumCullingSystem: FrustumCullingSystem;
  
  /** 实例化渲染系统 */
  private instancedRenderingSystem: InstancedRenderingSystem;
  
  /** LOD实体列表 */
  private lodEntities: Entity[] = [];
  
  /** 可剔除实体列表 */
  private cullableEntities: Entity[] = [];
  
  /** 实例化实体列表 */
  private instancedEntities: Entity[] = [];
  
  /** 动画ID */
  private animationId: number = 0;
  
  /** 是否正在运行 */
  private running: boolean = false;
  
  /** 当前示例索引 */
  private currentExampleIndex: number = 0;
  
  /** 示例切换计时器 */
  private exampleSwitchTimer: number = 0;
  
  /** 示例切换间隔（秒） */
  private exampleSwitchInterval: number = 10;
  
  /** 示例名称列表 */
  private exampleNames: string[] = [
    'LOD（细节层次）',
    '视锥体剔除',
    '实例化渲染',
    '所有优化'
  ];
  
  /** LOD生成器 */
  private lodGenerator: LODGenerator;
  
  /** 实例ID列表 */
  private instanceIds: number[] = [];
  
  /**
   * 创建示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建世界
    this.world = new World();
    this.engine.setWorld(this.world);
    
    // 创建渲染器
    const renderer = new Renderer({
      width: window.innerWidth,
      height: window.innerHeight,
      antialias: true,
      shadows: true,
      shadowMapType: THREE.PCFSoftShadowMap
    });
    
    // 创建渲染系统
    this.renderSystem = new RenderSystem(renderer, {
      enableShadows: true,
      enablePostProcessing: false
    });
    this.world.addSystem(this.renderSystem);
    
    // 创建LOD系统
    this.lodSystem = new LODSystem({
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      useFrustumCheck: true,
      useDistanceCheck: true
    });
    this.world.addSystem(this.lodSystem);
    
    // 创建视锥体剔除系统
    this.frustumCullingSystem = new FrustumCullingSystem({
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      useOctree: true,
      useBoundingBoxCulling: true,
      useBoundingSphereCulling: true
    });
    this.world.addSystem(this.frustumCullingSystem);
    
    // 创建实例化渲染系统
    this.instancedRenderingSystem = new InstancedRenderingSystem({
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      useFrustumCulling: true,
      useGPUInstancing: true
    });
    this.world.addSystem(this.instancedRenderingSystem);
    
    // 创建LOD生成器
    this.lodGenerator = new LODGenerator({
      highDetailRatio: 0.8,
      mediumDetailRatio: 0.5,
      lowDetailRatio: 0.2,
      veryLowDetailRatio: 0.1,
      preserveUVs: true,
      preserveNormals: true
    });
    
    // 创建场景
    this.scene = new Scene('渲染优化示例场景');
    this.world.addEntity(this.scene);
    
    // 设置活跃场景
    this.renderSystem.setActiveScene(this.scene);
    
    // 创建相机
    this.cameraEntity = new Entity('相机');
    this.cameraEntity.addComponent(new Camera({
      type: 'perspective',
      fov: 60,
      near: 0.1,
      far: 1000,
      aspect: window.innerWidth / window.innerHeight
    }));
    this.cameraEntity.addComponent(new Transform({
      position: { x: 0, y: 5, z: 15 },
      rotation: { x: -0.3, y: 0, z: 0 }
    }));
    this.world.addEntity(this.cameraEntity);
    this.scene.addEntity(this.cameraEntity);
    
    // 设置活跃相机
    this.renderSystem.setActiveCamera(this.cameraEntity.getComponent('Camera') as Camera);
    this.lodSystem.setActiveCamera(this.cameraEntity.getComponent('Camera') as Camera);
    this.frustumCullingSystem.setActiveCamera(this.cameraEntity.getComponent('Camera') as Camera);
    this.instancedRenderingSystem.setActiveCamera(this.cameraEntity.getComponent('Camera') as Camera);
    
    // 设置活跃场景
    this.lodSystem.setActiveScene(this.scene);
    this.frustumCullingSystem.setActiveScene(this.scene);
    this.instancedRenderingSystem.setActiveScene(this.scene);
    
    // 创建方向光
    this.directionalLightEntity = new Entity('方向光');
    this.directionalLightEntity.addComponent(new Light({
      type: LightType.DIRECTIONAL,
      color: 0xffffff,
      intensity: 1,
      castShadow: true,
      shadowMapSize: 2048,
      shadowCameraNear: 0.1,
      shadowCameraFar: 500,
      shadowCameraLeft: -50,
      shadowCameraRight: 50,
      shadowCameraTop: 50,
      shadowCameraBottom: -50,
      shadowBias: -0.0005
    }));
    this.directionalLightEntity.addComponent(new Transform({
      position: { x: 20, y: 30, z: 20 },
      rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 }
    }));
    this.world.addEntity(this.directionalLightEntity);
    this.scene.addEntity(this.directionalLightEntity);
    
    // 创建环境光
    this.ambientLightEntity = new Entity('环境光');
    this.ambientLightEntity.addComponent(new Light({
      type: LightType.AMBIENT,
      color: 0x404040,
      intensity: 0.5
    }));
    this.world.addEntity(this.ambientLightEntity);
    this.scene.addEntity(this.ambientLightEntity);
    
    // 创建地面
    const groundEntity = new Entity('地面');
    groundEntity.addComponent(new Transform({
      position: { x: 0, y: -0.5, z: 0 },
      scale: { x: 100, y: 1, z: 100 }
    }));
    
    // 创建地面网格
    const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
    const groundMaterial = new THREE.MeshStandardMaterial({
      color: 0x808080,
      roughness: 0.7,
      metalness: 0.1
    });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.receiveShadow = true;
    
    // 添加到实体
    groundEntity.getComponent('Transform')?.getObject3D().add(groundMesh);
    
    // 添加到场景
    this.world.addEntity(groundEntity);
    this.scene.addEntity(groundEntity);
    
    // 创建示例
    this.createLODExample();
    this.createFrustumCullingExample();
    this.createInstancedRenderingExample();
    
    // 初始状态：只显示第一个示例
    this.showExample(0);
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', this.handleResize.bind(this));
    
    // 添加DOM元素
    this.addDomElements();
  }
  
  /**
   * 创建LOD示例
   */
  private createLODExample(): void {
    // 创建多个LOD实体
    for (let i = 0; i < 50; i++) {
      // 计算位置
      const angle = (i / 50) * Math.PI * 2;
      const radius = 20 + Math.random() * 10;
      const x = Math.cos(angle) * radius;
      const z = Math.sin(angle) * radius;
      const y = 1;
      
      // 创建实体
      const entity = new Entity(`LOD实体${i}`);
      entity.addComponent(new Transform({
        position: { x, y, z },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      }));
      
      // 创建网格
      const highDetailGeometry = new THREE.SphereGeometry(1, 32, 32);
      const mediumDetailGeometry = new THREE.SphereGeometry(1, 16, 16);
      const lowDetailGeometry = new THREE.SphereGeometry(1, 8, 8);
      const veryLowDetailGeometry = new THREE.SphereGeometry(1, 4, 4);
      
      const material = new THREE.MeshStandardMaterial({
        color: new THREE.Color(Math.random(), Math.random(), Math.random()),
        roughness: 0.7,
        metalness: 0.3
      });
      
      const highDetailMesh = new THREE.Mesh(highDetailGeometry, material);
      const mediumDetailMesh = new THREE.Mesh(mediumDetailGeometry, material);
      const lowDetailMesh = new THREE.Mesh(lowDetailGeometry, material);
      const veryLowDetailMesh = new THREE.Mesh(veryLowDetailGeometry, material);
      
      highDetailMesh.castShadow = true;
      highDetailMesh.receiveShadow = true;
      mediumDetailMesh.castShadow = true;
      mediumDetailMesh.receiveShadow = true;
      lowDetailMesh.castShadow = true;
      lowDetailMesh.receiveShadow = true;
      veryLowDetailMesh.castShadow = true;
      veryLowDetailMesh.receiveShadow = true;
      
      // 创建LOD组件
      entity.addComponent(new LODComponent({
        levels: [
          { level: LODLevel.HIGH, distance: 10, mesh: highDetailMesh, visible: true },
          { level: LODLevel.MEDIUM, distance: 20, mesh: mediumDetailMesh, visible: false },
          { level: LODLevel.LOW, distance: 30, mesh: lowDetailMesh, visible: false },
          { level: LODLevel.VERY_LOW, distance: 40, mesh: veryLowDetailMesh, visible: false }
        ],
        autoComputeBoundingRadius: true,
        autoSortLevels: true
      }));
      
      // 添加到场景
      this.world.addEntity(entity);
      this.scene.addEntity(entity);
      
      // 添加到LOD实体列表
      this.lodEntities.push(entity);
    }
  }
  
  /**
   * 创建视锥体剔除示例
   */
  private createFrustumCullingExample(): void {
    // 创建多个可剔除实体
    for (let i = 0; i < 100; i++) {
      // 计算位置
      const angle = (i / 100) * Math.PI * 2;
      const radius = 20 + Math.random() * 30;
      const x = Math.cos(angle) * radius;
      const z = Math.sin(angle) * radius;
      const y = 1 + Math.random() * 2;
      
      // 创建实体
      const entity = new Entity(`可剔除实体${i}`);
      entity.addComponent(new Transform({
        position: { x, y, z },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      }));
      
      // 创建网格
      const geometry = new THREE.BoxGeometry(1, 1, 1);
      const material = new THREE.MeshStandardMaterial({
        color: new THREE.Color(Math.random(), Math.random(), Math.random()),
        roughness: 0.7,
        metalness: 0.3
      });
      
      const mesh = new THREE.Mesh(geometry, material);
      mesh.castShadow = true;
      mesh.receiveShadow = true;
      
      // 添加到实体
      entity.getComponent('Transform')?.getObject3D().add(mesh);
      
      // 创建可剔除组件
      entity.addComponent(new CullableComponent({
        autoComputeBoundingRadius: true,
        useBoundingBox: true,
        autoComputeBoundingBox: true,
        visible: true,
        cullable: true
      }));
      
      // 添加到场景
      this.world.addEntity(entity);
      this.scene.addEntity(entity);
      
      // 添加到可剔除实体列表
      this.cullableEntities.push(entity);
    }
  }
  
  /**
   * 创建实例化渲染示例
   */
  private createInstancedRenderingExample(): void {
    // 创建实例化实体
    const entity = new Entity('实例化实体');
    entity.addComponent(new Transform({
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      scale: { x: 1, y: 1, z: 1 }
    }));
    
    // 创建原始网格
    const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
    const material = new THREE.MeshStandardMaterial({
      color: 0xffffff,
      roughness: 0.7,
      metalness: 0.3
    });
    
    const mesh = new THREE.Mesh(geometry, material);
    mesh.castShadow = true;
    mesh.receiveShadow = true;
    
    // 创建实例化组件
    entity.addComponent(new InstancedComponent({
      originalMesh: mesh,
      maxInstanceCount: 1000,
      useColor: true,
      dynamic: true,
      visible: true
    }));
    
    // 添加到场景
    this.world.addEntity(entity);
    this.scene.addEntity(entity);
    
    // 添加到实例化实体列表
    this.instancedEntities.push(entity);
    
    // 添加实例
    const instancedComponent = entity.getComponent('InstancedComponent') as InstancedComponent;
    
    for (let i = 0; i < 1000; i++) {
      // 计算位置
      const angle = (i / 1000) * Math.PI * 2;
      const radius = 20 + Math.random() * 10;
      const x = Math.cos(angle) * radius;
      const z = Math.sin(angle) * radius;
      const y = Math.random() * 5;
      
      // 创建实例数据
      const instanceData = {
        position: new THREE.Vector3(x, y, z),
        quaternion: new THREE.Quaternion().setFromEuler(new THREE.Euler(
          Math.random() * Math.PI,
          Math.random() * Math.PI,
          Math.random() * Math.PI
        )),
        scale: new THREE.Vector3(
          0.5 + Math.random() * 0.5,
          0.5 + Math.random() * 0.5,
          0.5 + Math.random() * 0.5
        ),
        color: new THREE.Color(Math.random(), Math.random(), Math.random())
      };
      
      // 添加实例
      const instanceId = instancedComponent.addInstance(instanceData);
      
      // 保存实例ID
      if (instanceId >= 0) {
        this.instanceIds.push(instanceId);
      }
    }
  }
  
  /**
   * 显示示例
   * @param index 示例索引
   */
  private showExample(index: number): void {
    // 隐藏所有示例
    for (const entity of this.lodEntities) {
      entity.setActive(false);
    }
    
    for (const entity of this.cullableEntities) {
      entity.setActive(false);
    }
    
    for (const entity of this.instancedEntities) {
      entity.setActive(false);
    }
    
    // 显示指定示例
    switch (index) {
      case 0: // LOD
        for (const entity of this.lodEntities) {
          entity.setActive(true);
        }
        break;
      case 1: // 视锥体剔除
        for (const entity of this.cullableEntities) {
          entity.setActive(true);
        }
        break;
      case 2: // 实例化渲染
        for (const entity of this.instancedEntities) {
          entity.setActive(true);
        }
        break;
      case 3: // 所有优化
        for (const entity of this.lodEntities) {
          entity.setActive(true);
        }
        for (const entity of this.cullableEntities) {
          entity.setActive(true);
        }
        for (const entity of this.instancedEntities) {
          entity.setActive(true);
        }
        break;
    }
    
    // 更新当前示例索引
    this.currentExampleIndex = index;
    
    // 更新示例名称
    document.getElementById('current-example')!.textContent = this.exampleNames[index];
  }
  
  /**
   * 添加DOM元素
   */
  private addDomElements(): void {
    // 创建信息面板
    const infoPanel = document.createElement('div');
    infoPanel.style.position = 'absolute';
    infoPanel.style.top = '10px';
    infoPanel.style.left = '10px';
    infoPanel.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    infoPanel.style.color = 'white';
    infoPanel.style.padding = '10px';
    infoPanel.style.borderRadius = '5px';
    infoPanel.style.fontFamily = 'Arial, sans-serif';
    infoPanel.innerHTML = `
      <h2>渲染优化示例</h2>
      <p>这个示例展示了如何使用LOD、视锥体剔除和实例化渲染。</p>
      <p>每隔10秒会自动切换示例。</p>
      <p>当前示例: <span id="current-example">${this.exampleNames[this.currentExampleIndex]}</span></p>
      <p>性能统计:</p>
      <ul>
        <li>帧率: <span id="fps">0</span> FPS</li>
        <li>可见物体数: <span id="visible-objects">0</span></li>
        <li>三角形数: <span id="triangles">0</span></li>
      </ul>
    `;
    document.body.appendChild(infoPanel);
    
    // 创建控制面板
    const controlPanel = document.createElement('div');
    controlPanel.style.position = 'absolute';
    controlPanel.style.top = '10px';
    controlPanel.style.right = '10px';
    controlPanel.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    controlPanel.style.color = 'white';
    controlPanel.style.padding = '10px';
    controlPanel.style.borderRadius = '5px';
    controlPanel.style.fontFamily = 'Arial, sans-serif';
    controlPanel.innerHTML = `
      <h2>控制面板</h2>
      <div>
        <button id="lod-button">LOD示例</button>
        <button id="frustum-button">视锥体剔除示例</button>
        <button id="instanced-button">实例化渲染示例</button>
        <button id="all-button">所有优化</button>
      </div>
    `;
    document.body.appendChild(controlPanel);
    
    // 添加按钮事件监听
    document.getElementById('lod-button')!.addEventListener('click', () => this.showExample(0));
    document.getElementById('frustum-button')!.addEventListener('click', () => this.showExample(1));
    document.getElementById('instanced-button')!.addEventListener('click', () => this.showExample(2));
    document.getElementById('all-button')!.addEventListener('click', () => this.showExample(3));
  }
  
  /**
   * 处理窗口大小变化
   */
  private handleResize(): void {
    // 更新相机宽高比
    const camera = this.cameraEntity.getComponent('Camera') as Camera;
    camera.setAspect(window.innerWidth / window.innerHeight);
  }
  
  /**
   * 更新性能统计
   */
  private updateStats(): void {
    // 计算帧率
    const now = performance.now();
    const deltaTime = now - (this._lastTime || now);
    const fps = Math.round(1000 / deltaTime);
    
    // 更新帧率
    document.getElementById('fps')!.textContent = fps.toString();
    
    // 计算可见物体数
    let visibleObjects = 0;
    
    // 计算三角形数
    let triangles = 0;
    
    // 遍历场景中的所有网格
    this.scene.getThreeScene().traverse((object) => {
      if (object instanceof THREE.Mesh && object.visible) {
        visibleObjects++;
        
        // 计算三角形数
        if (object.geometry) {
          if (object.geometry instanceof THREE.BufferGeometry) {
            const index = object.geometry.index;
            if (index) {
              triangles += index.count / 3;
            } else {
              const position = object.geometry.getAttribute('position');
              if (position) {
                triangles += position.count / 3;
              }
            }
          }
        }
      }
    });
    
    // 更新可见物体数
    document.getElementById('visible-objects')!.textContent = visibleObjects.toString();
    
    // 更新三角形数
    document.getElementById('triangles')!.textContent = Math.round(triangles).toString();
  }
  
  /**
   * 更新场景
   * @param deltaTime 帧间隔时间（秒）
   */
  private update(deltaTime: number): void {
    // 旋转相机
    const cameraTransform = this.cameraEntity.getComponent('Transform');
    if (cameraTransform) {
      const rotation = cameraTransform.getRotation();
      rotation.y += deltaTime * 0.1;
      cameraTransform.setRotation(rotation.x, rotation.y, rotation.z);
    }
    
    // 更新示例切换计时器
    this.exampleSwitchTimer += deltaTime;
    if (this.exampleSwitchTimer >= this.exampleSwitchInterval) {
      this.exampleSwitchTimer = 0;
      this.showExample((this.currentExampleIndex + 1) % this.exampleNames.length);
    }
    
    // 更新世界
    this.world.update(deltaTime);
    
    // 更新性能统计
    this.updateStats();
  }
  
  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;
    
    const now = performance.now();
    const deltaTime = Math.min((now - (this._lastTime || now)) / 1000, 0.1);
    this._lastTime = now;
    
    this.update(deltaTime);
    
    this.animationId = requestAnimationFrame(this.animate.bind(this));
  }
  
  /** 上一帧时间 */
  private _lastTime: number = 0;
  
  /**
   * 启动示例
   */
  public start(): void {
    if (this.running) return;
    
    this.running = true;
    this._lastTime = performance.now();
    this.animate();
  }
  
  /**
   * 停止示例
   */
  public stop(): void {
    if (!this.running) return;
    
    this.running = false;
    cancelAnimationFrame(this.animationId);
  }
  
  /**
   * 销毁示例
   */
  public dispose(): void {
    this.stop();
    
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize.bind(this));
    
    // 销毁引擎
    this.engine.dispose();
  }
}
