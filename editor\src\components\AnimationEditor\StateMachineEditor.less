/**
 * 状态机编辑器样式
 */
.state-machine-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f0f2f5;
  
  .state-machine-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background-color: #fff;
    border-bottom: 1px solid #e8e8e8;
    
    .editor-title {
      h2 {
        margin: 0;
      }
    }
    
    .editor-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .state-machine-editor-toolbar {
    padding: 8px 16px;
    background-color: #fff;
    border-bottom: 1px solid #e8e8e8;
  }
  
  .state-machine-editor-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    
    .state-machine-canvas {
      flex: 1;
      position: relative;
      overflow: hidden;
      background-color: #f0f2f5;
      
      .grid-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: linear-gradient(#e0e0e0 1px, transparent 1px),
                          linear-gradient(90deg, #e0e0e0 1px, transparent 1px);
        background-size: 20px 20px;
        pointer-events: none;
      }
      
      .canvas-content {
        position: absolute;
        top: 0;
        left: 0;
        transform-origin: 0 0;
      }
      
      .temp-transition-line {
        position: absolute;
        pointer-events: none;
        stroke: #1890ff;
        stroke-width: 2;
        stroke-dasharray: 5,5;
      }
    }
    
    .state-machine-panel {
      width: 300px;
      background-color: #fff;
      border-left: 1px solid #e8e8e8;
      overflow-y: auto;
      
      .no-selection-message {
        padding: 16px;
        text-align: center;
        color: #999;
      }
      
      .parameters-container {
        padding: 16px;
        
        .parameter-form {
          margin-top: 16px;
          padding: 16px;
          border: 1px solid #e8e8e8;
          border-radius: 4px;
          background-color: #f9f9f9;
        }
      }
    }
  }
  
  .state-machine-debugger {
    height: 300px;
    background-color: #fff;
    border-top: 1px solid #e8e8e8;
    overflow-y: auto;
    
    .debugger-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 16px;
      border-bottom: 1px solid #e8e8e8;
      
      h3 {
        margin: 0;
      }
    }
  }
}

.state-machine-node {
  position: absolute;
  width: 100px;
  height: 50px;
  border-radius: 8px;
  background-color: #1890ff;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: grab;
  user-select: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 2px solid transparent;
  transition: all 0.3s;
  
  &.selected {
    border-color: #f5222d;
    box-shadow: 0 0 0 2px rgba(245, 34, 45, 0.2);
  }
  
  &.current {
    border-color: #fa8c16;
    box-shadow: 0 0 0 2px rgba(250, 140, 22, 0.2);
  }
  
  .node-icon {
    font-size: 16px;
    line-height: 1;
  }
  
  .node-name {
    font-size: 12px;
    line-height: 1.5;
    text-align: center;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 4px;
  }
}

.state-machine-transition {
  pointer-events: all;
  cursor: pointer;
  
  path {
    transition: stroke 0.3s;
  }
  
  &.selected {
    path {
      stroke: #f5222d;
    }
    
    .transition-label {
      fill: #f5222d;
    }
  }
  
  .transition-label {
    pointer-events: none;
    transition: fill 0.3s;
  }
}

// 表单样式
.ant-form-item-label {
  font-weight: 500;
}

// 调试器样式
.state-machine-debugger {
  .ant-timeline-item-label {
    width: 80px;
  }
  
  .ant-timeline-item-content {
    margin-left: 90px;
  }
}
