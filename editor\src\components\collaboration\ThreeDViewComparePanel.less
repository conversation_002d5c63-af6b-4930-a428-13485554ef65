.three-d-view-compare-panel {
  margin: 16px;
  
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    margin: 0;
    
    .compare-card {
      height: 100%;
      border-radius: 0;
      
      .ant-card-body {
        height: calc(100% - 57px);
        display: flex;
        flex-direction: column;
      }
      
      .view-container {
        flex: 1;
      }
    }
  }
  
  .compare-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    
    .view-settings {
      margin-bottom: 16px;
      display: flex;
      justify-content: center;
    }
    
    .view-container {
      display: flex;
      height: 600px;
      
      .view-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        border: 1px solid #f0f0f0;
        
        &:first-child {
          margin-right: 8px;
        }
        
        .view-header {
          padding: 8px;
          background-color: #f5f5f5;
          text-align: center;
        }
        
        .view-content {
          flex: 1;
          position: relative;
          
          canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
}
