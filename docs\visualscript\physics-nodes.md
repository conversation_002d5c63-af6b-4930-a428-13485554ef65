# 视觉脚本物理节点使用指南

本文档介绍了DL（Digital Learning）引擎视觉脚本系统中的物理节点，包括基础物理节点和软体物理节点，以及它们的使用方法和示例。

## 目录

- [基础物理节点](#基础物理节点)
  - [射线检测节点](#射线检测节点)
  - [应用力节点](#应用力节点)
  - [碰撞检测节点](#碰撞检测节点)
  - [物理约束节点](#物理约束节点)
  - [物理材质节点](#物理材质节点)
- [软体物理节点](#软体物理节点)
  - [创建布料节点](#创建布料节点)
  - [创建绳索节点](#创建绳索节点)
  - [创建气球节点](#创建气球节点)
  - [创建果冻节点](#创建果冻节点)
  - [软体切割节点](#软体切割节点)
- [示例](#示例)
  - [基础物理示例](#基础物理示例)
  - [软体物理示例](#软体物理示例)

## 基础物理节点

基础物理节点提供了与刚体物理系统交互的功能，包括射线检测、应用力、碰撞检测、物理约束和物理材质等。

### 射线检测节点

射线检测节点用于执行物理射线检测，可以检测射线与物理对象的交点。

**节点类型**: `physics/raycast`

**输入插槽**:
- `origin` (Vector3): 射线起点
- `direction` (Vector3): 射线方向
- `maxDistance` (number): 最大检测距离，默认为100
- `collisionGroup` (number): 碰撞组，默认为-1
- `collisionMask` (number): 碰撞掩码，默认为-1

**输出插槽**:
- `hit` (boolean): 是否命中
- `hitEntity` (Entity): 命中的实体
- `hitPoint` (Vector3): 命中点
- `hitNormal` (Vector3): 命中法线
- `hitDistance` (number): 命中距离

**使用示例**:
```json
{
  "id": "perform-raycast",
  "type": "physics/raycast",
  "parameters": {
    "origin": {
      "value": {
        "x": 0,
        "y": 10,
        "z": 0
      }
    },
    "direction": {
      "value": {
        "x": 0,
        "y": -1,
        "z": 0
      }
    },
    "maxDistance": {
      "value": 20
    }
  }
}
```

### 应用力节点

应用力节点用于向物理体应用力，可以模拟推动、拉动等物理效果。

**节点类型**: `physics/applyForce`

**输入插槽**:
- `flow` (Flow): 输入流程
- `entity` (Entity): 目标实体
- `force` (Vector3): 力向量
- `localPoint` (Vector3): 局部应用点，默认为(0,0,0)

**输出插槽**:
- `flow` (Flow): 输出流程
- `success` (boolean): 是否成功

**使用示例**:
```json
{
  "id": "apply-force",
  "type": "physics/applyForce",
  "parameters": {
    "force": {
      "value": {
        "x": 0,
        "y": 0,
        "z": 10
      }
    }
  },
  "inputs": {
    "entity": {
      "nodeId": "get-box",
      "socket": "entity"
    }
  }
}
```

### 碰撞检测节点

碰撞检测节点用于检测两个实体之间的碰撞，可以获取碰撞点、法线等信息。

**节点类型**: `physics/collisionDetection`

**输入插槽**:
- `entityA` (Entity): 实体A
- `entityB` (Entity): 实体B

**输出插槽**:
- `colliding` (boolean): 是否碰撞
- `contactPoint` (Vector3): 接触点
- `contactNormal` (Vector3): 接触法线
- `penetrationDepth` (number): 穿透深度

**使用示例**:
```json
{
  "id": "check-collision",
  "type": "physics/collisionDetection",
  "inputs": {
    "entityA": {
      "nodeId": "get-box",
      "socket": "entity"
    },
    "entityB": {
      "nodeId": "get-sphere",
      "socket": "entity"
    }
  }
}
```

### 物理约束节点

物理约束节点用于创建物理约束，可以模拟铰链、弹簧等物理连接。

**节点类型**: `physics/createConstraint`

**输入插槽**:
- `flow` (Flow): 输入流程
- `entityA` (Entity): 实体A
- `entityB` (Entity): 实体B
- `constraintType` (string): 约束类型，默认为'hinge'
- `pivotA` (Vector3): 实体A上的枢轴点，默认为(0,0,0)
- `pivotB` (Vector3): 实体B上的枢轴点，默认为(0,0,0)
- `axisA` (Vector3): 实体A上的轴，默认为(0,1,0)
- `axisB` (Vector3): 实体B上的轴，默认为(0,1,0)

**输出插槽**:
- `flow` (Flow): 输出流程
- `constraintId` (string): 约束ID
- `success` (boolean): 是否成功

**使用示例**:
```json
{
  "id": "create-hinge",
  "type": "physics/createConstraint",
  "parameters": {
    "constraintType": {
      "value": "hinge"
    },
    "pivotA": {
      "value": {
        "x": 0,
        "y": 0,
        "z": 0.5
      }
    },
    "pivotB": {
      "value": {
        "x": 0,
        "y": 0,
        "z": -0.5
      }
    }
  },
  "inputs": {
    "entityA": {
      "nodeId": "get-box",
      "socket": "entity"
    },
    "entityB": {
      "nodeId": "get-sphere",
      "socket": "entity"
    }
  }
}
```

### 物理材质节点

物理材质节点用于创建物理材质，可以设置摩擦系数、恢复系数等物理属性。

**节点类型**: `physics/createMaterial`

**输入插槽**:
- `friction` (number): 摩擦系数，默认为0.3
- `restitution` (number): 恢复系数，默认为0.3

**输出插槽**:
- `material` (PhysicsMaterial): 物理材质

**使用示例**:
```json
{
  "id": "create-material",
  "type": "physics/createMaterial",
  "parameters": {
    "friction": {
      "value": 0.5
    },
    "restitution": {
      "value": 0.7
    }
  }
}
```

## 软体物理节点

软体物理节点提供了与软体物理系统交互的功能，包括创建布料、绳索、气球、果冻等软体，以及软体切割等操作。

### 创建布料节点

创建布料节点用于创建布料软体，可以模拟旗帜、窗帘等物体。

**节点类型**: `physics/softbody/createCloth`

**输入插槽**:
- `flow` (Flow): 输入流程
- `width` (number): 宽度，默认为1
- `height` (number): 高度，默认为1
- `segments` (number): 分段数，默认为10
- `position` (Vector3): 位置，默认为(0,0,0)
- `fixedCorners` (boolean): 是否固定角落，默认为true
- `mass` (number): 质量，默认为1
- `stiffness` (number): 刚度，默认为0.9

**输出插槽**:
- `flow` (Flow): 输出流程
- `entity` (Entity): 创建的实体
- `success` (boolean): 是否成功

**使用示例**:
```json
{
  "id": "create-cloth",
  "type": "physics/softbody/createCloth",
  "parameters": {
    "width": {
      "value": 2
    },
    "height": {
      "value": 2
    },
    "segments": {
      "value": 10
    },
    "position": {
      "value": {
        "x": 0,
        "y": 3,
        "z": 0
      }
    },
    "fixedCorners": {
      "value": true
    }
  }
}
```

### 创建绳索节点

创建绳索节点用于创建绳索软体，可以模拟绳子、链条等物体。

**节点类型**: `physics/softbody/createRope`

**输入插槽**:
- `flow` (Flow): 输入流程
- `start` (Vector3): 起点，默认为(0,1,0)
- `end` (Vector3): 终点，默认为(0,-1,0)
- `segments` (number): 分段数，默认为10
- `fixedEnds` (boolean): 是否固定两端，默认为true
- `mass` (number): 质量，默认为1
- `stiffness` (number): 刚度，默认为0.9

**输出插槽**:
- `flow` (Flow): 输出流程
- `entity` (Entity): 创建的实体
- `success` (boolean): 是否成功

**使用示例**:
```json
{
  "id": "create-rope",
  "type": "physics/softbody/createRope",
  "parameters": {
    "start": {
      "value": {
        "x": -2,
        "y": 5,
        "z": 0
      }
    },
    "end": {
      "value": {
        "x": -2,
        "y": 0,
        "z": 0
      }
    },
    "segments": {
      "value": 10
    },
    "fixedEnds": {
      "value": true
    }
  }
}
```

### 创建气球节点

创建气球节点用于创建气球软体，可以模拟气球、皮球等物体。

**节点类型**: `physics/softbody/createBalloon`

**输入插槽**:
- `flow` (Flow): 输入流程
- `radius` (number): 半径，默认为0.5
- `position` (Vector3): 位置，默认为(0,0,0)
- `segments` (number): 分段数，默认为16
- `pressure` (number): 内部压力，默认为100
- `mass` (number): 质量，默认为1
- `stiffness` (number): 刚度，默认为0.9

**输出插槽**:
- `flow` (Flow): 输出流程
- `entity` (Entity): 创建的实体
- `success` (boolean): 是否成功

**使用示例**:
```json
{
  "id": "create-balloon",
  "type": "physics/softbody/createBalloon",
  "parameters": {
    "radius": {
      "value": 1.5
    },
    "position": {
      "value": {
        "x": 5,
        "y": 5,
        "z": 0
      }
    },
    "pressure": {
      "value": 100
    }
  }
}
```

### 创建果冻节点

创建果冻节点用于创建果冻软体，可以模拟果冻、水袋等物体。

**节点类型**: `physics/softbody/createJelly`

**输入插槽**:
- `flow` (Flow): 输入流程
- `size` (Vector3): 尺寸，默认为(1,1,1)
- `position` (Vector3): 位置，默认为(0,0,0)
- `resolution` (number): 分辨率，默认为8
- `mass` (number): 质量，默认为1
- `stiffness` (number): 刚度，默认为0.8
- `damping` (number): 阻尼，默认为0.3

**输出插槽**:
- `flow` (Flow): 输出流程
- `entity` (Entity): 创建的实体
- `success` (boolean): 是否成功

**使用示例**:
```json
{
  "id": "create-jelly",
  "type": "physics/softbody/createJelly",
  "parameters": {
    "size": {
      "value": {
        "x": 2,
        "y": 2,
        "z": 2
      }
    },
    "position": {
      "value": {
        "x": 0,
        "y": 10,
        "z": 5
      }
    },
    "resolution": {
      "value": 8
    }
  }
}
```

### 软体切割节点

软体切割节点用于切割软体，可以模拟切割、撕裂等效果。

**节点类型**: `physics/softbody/cut`

**输入插槽**:
- `flow` (Flow): 输入流程
- `entity` (Entity): 软体实体
- `cutPoint` (Vector3): 切割点
- `cutNormal` (Vector3): 切割面法线

**输出插槽**:
- `flow` (Flow): 输出流程
- `success` (boolean): 是否成功
- `newEntity` (Entity): 切割后新创建的实体

**使用示例**:
```json
{
  "id": "cut-cloth",
  "type": "physics/softbody/cut",
  "parameters": {
    "cutPoint": {
      "value": {
        "x": 0,
        "y": 5,
        "z": 0
      }
    },
    "cutNormal": {
      "value": {
        "x": 1,
        "y": 0,
        "z": 0
      }
    }
  },
  "inputs": {
    "entity": {
      "nodeId": "create-cloth",
      "socket": "entity"
    }
  }
}
```

## 示例

### 基础物理示例

基础物理示例展示了如何使用基础物理节点创建物理场景，包括创建地面、盒子，以及使用射线检测等功能。

```
示例文件: examples/visualscript/physics-example.json
```

### 软体物理示例

软体物理示例展示了如何使用软体物理节点创建软体场景，包括创建布料、绳索、气球、果冻等软体，以及使用软体切割等功能。

```
示例文件: examples/visualscript/softbody-example.json
```
