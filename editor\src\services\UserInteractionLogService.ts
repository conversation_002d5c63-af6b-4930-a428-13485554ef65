/**
 * 用户交互日志服务
 * 用于记录和分析用户交互行为
 */
import { EventEmitter } from 'events';
import { store } from '../store';
import { UserActivity, UserActivityType } from './EnhancedUserSessionManager';
import { collaborationService } from './CollaborationService';
import { addUserInteractionLog, clearUserInteractionLogs } from '../store/logs/logsSlice';

/**
 * 交互日志类型
 */
export enum InteractionLogType {
  /** 用户活动 */
  USER_ACTIVITY = 'user_activity',
  /** 系统事件 */
  SYSTEM_EVENT = 'system_event',
  /** 权限操作 */
  PERMISSION_OPERATION = 'permission_operation',
  /** 编辑操作 */
  EDIT_OPERATION = 'edit_operation',
  /** 导航操作 */
  NAVIGATION = 'navigation',
  /** 工具使用 */
  TOOL_USAGE = 'tool_usage',
  /** 错误 */
  ERROR = 'error',
  /** 性能 */
  PERFORMANCE = 'performance',
  /** 协作 */
  COLLABORATION = 'collaboration',
}

/**
 * 交互日志级别
 */
export enum InteractionLogLevel {
  /** 调试 */
  DEBUG = 'debug',
  /** 信息 */
  INFO = 'info',
  /** 警告 */
  WARNING = 'warning',
  /** 错误 */
  ERROR = 'error',
  /** 严重 */
  CRITICAL = 'critical',
}

/**
 * 交互日志接口
 */
export interface InteractionLog {
  /** 日志ID */
  id: string;
  /** 日志类型 */
  type: InteractionLogType;
  /** 日志级别 */
  level: InteractionLogLevel;
  /** 时间戳 */
  timestamp: number;
  /** 用户ID */
  userId: string;
  /** 用户名 */
  userName: string;
  /** 会话ID */
  sessionId?: string;
  /** 操作 */
  action?: string;
  /** 目标 */
  target?: string;
  /** 目标类型 */
  targetType?: string;
  /** 详情 */
  details?: any;
  /** 相关活动 */
  relatedActivity?: UserActivity;
  /** 持续时间（毫秒） */
  duration?: number;
  /** 标签 */
  tags?: string[];
  /** 元数据 */
  metadata?: Record<string, any>;
}

/**
 * 用户交互日志服务配置
 */
export interface UserInteractionLogServiceConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 最大日志数量 */
  maxLogs?: number;
  /** 是否启用远程日志 */
  enableRemoteLogging?: boolean;
  /** 远程日志URL */
  remoteLoggingUrl?: string;
  /** 远程日志批处理大小 */
  remoteLoggingBatchSize?: number;
  /** 远程日志发送间隔（毫秒） */
  remoteLoggingInterval?: number;
  /** 是否记录调试日志 */
  logDebug?: boolean;
  /** 是否记录性能日志 */
  logPerformance?: boolean;
  /** 是否记录用户活动 */
  logUserActivity?: boolean;
  /** 是否记录系统事件 */
  logSystemEvents?: boolean;
  /** 是否记录错误 */
  logErrors?: boolean;
  /** 是否启用日志分析 */
  enableAnalytics?: boolean;
  /** 分析间隔（毫秒） */
  analyticsInterval?: number;
}

/**
 * 用户交互日志服务类
 */
class UserInteractionLogService extends EventEmitter {
  /** 配置 */
  private config: Required<UserInteractionLogServiceConfig>;
  
  /** 日志列表 */
  private logs: InteractionLog[] = [];
  
  /** 待发送的远程日志 */
  private pendingRemoteLogs: InteractionLog[] = [];
  
  /** 远程日志发送定时器ID */
  private remoteLoggingTimerId: number | null = null;
  
  /** 分析定时器ID */
  private analyticsTimerId: number | null = null;
  
  /** 分析结果 */
  private analyticsResults: Record<string, any> = {};
  
  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: UserInteractionLogServiceConfig = {}) {
    super();
    
    // 默认配置
    this.config = {
      enabled: config.enabled ?? true,
      maxLogs: config.maxLogs ?? 10000,
      enableRemoteLogging: config.enableRemoteLogging ?? false,
      remoteLoggingUrl: config.remoteLoggingUrl ?? '',
      remoteLoggingBatchSize: config.remoteLoggingBatchSize ?? 50,
      remoteLoggingInterval: config.remoteLoggingInterval ?? 60000, // 1分钟
      logDebug: config.logDebug ?? false,
      logPerformance: config.logPerformance ?? true,
      logUserActivity: config.logUserActivity ?? true,
      logSystemEvents: config.logSystemEvents ?? true,
      logErrors: config.logErrors ?? true,
      enableAnalytics: config.enableAnalytics ?? true,
      analyticsInterval: config.analyticsInterval ?? 300000, // 5分钟
    };
    
    // 初始化定时器
    this.initializeTimers();
  }
  
  /**
   * 初始化定时器
   * @private
   */
  private initializeTimers(): void {
    // 远程日志发送定时器
    if (this.config.enableRemoteLogging) {
      this.remoteLoggingTimerId = window.setInterval(() => {
        this.sendRemoteLogs();
      }, this.config.remoteLoggingInterval);
    }
    
    // 分析定时器
    if (this.config.enableAnalytics) {
      this.analyticsTimerId = window.setInterval(() => {
        this.runAnalytics();
      }, this.config.analyticsInterval);
    }
  }
  
  /**
   * 添加日志
   * @param log 日志对象
   */
  public addLog(log: Omit<InteractionLog, 'id' | 'timestamp' | 'userId' | 'userName'>): void {
    if (!this.config.enabled) {
      return;
    }
    
    // 跳过调试日志（如果配置为不记录）
    if (log.level === InteractionLogLevel.DEBUG && !this.config.logDebug) {
      return;
    }
    
    // 跳过性能日志（如果配置为不记录）
    if (log.type === InteractionLogType.PERFORMANCE && !this.config.logPerformance) {
      return;
    }
    
    // 跳过用户活动日志（如果配置为不记录）
    if (log.type === InteractionLogType.USER_ACTIVITY && !this.config.logUserActivity) {
      return;
    }
    
    // 跳过系统事件日志（如果配置为不记录）
    if (log.type === InteractionLogType.SYSTEM_EVENT && !this.config.logSystemEvents) {
      return;
    }
    
    // 创建完整日志对象
    const fullLog: InteractionLog = {
      ...log,
      id: this.generateId(),
      timestamp: Date.now(),
      userId: collaborationService.getUserId(),
      userName: collaborationService.getUserName(),
    };
    
    // 添加到日志列表
    this.logs.push(fullLog);
    
    // 如果日志数量超过最大值，删除最旧的日志
    if (this.logs.length > this.config.maxLogs) {
      this.logs.splice(0, this.logs.length - this.config.maxLogs);
    }
    
    // 添加到待发送的远程日志
    if (this.config.enableRemoteLogging) {
      this.pendingRemoteLogs.push(fullLog);
    }
    
    // 更新Redux状态
    store.dispatch(addUserInteractionLog(fullLog));
    
    // 触发事件
    this.emit('logAdded', fullLog);
    
    // 如果是错误日志，触发错误事件
    if (log.level === InteractionLogLevel.ERROR || log.level === InteractionLogLevel.CRITICAL) {
      this.emit('error', fullLog);
    }
  }
  
  /**
   * 记录用户活动
   * @param activity 用户活动
   */
  public logUserActivity(activity: UserActivity): void {
    this.addLog({
      type: InteractionLogType.USER_ACTIVITY,
      level: InteractionLogLevel.INFO,
      action: activity.type,
      details: activity.details,
      target: activity.resourceId,
      targetType: activity.resourceType,
      relatedActivity: activity,
      sessionId: collaborationService.getSessionId(),
    });
  }
  
  /**
   * 记录编辑操作
   * @param action 操作
   * @param target 目标
   * @param targetType 目标类型
   * @param details 详情
   */
  public logEditOperation(action: string, target: string, targetType: string, details?: any): void {
    this.addLog({
      type: InteractionLogType.EDIT_OPERATION,
      level: InteractionLogLevel.INFO,
      action,
      target,
      targetType,
      details,
      sessionId: collaborationService.getSessionId(),
    });
  }
  
  /**
   * 记录导航操作
   * @param from 来源
   * @param to 目标
   * @param details 详情
   */
  public logNavigation(from: string, to: string, details?: any): void {
    this.addLog({
      type: InteractionLogType.NAVIGATION,
      level: InteractionLogLevel.INFO,
      action: 'navigate',
      details: {
        from,
        to,
        ...details,
      },
      sessionId: collaborationService.getSessionId(),
    });
  }
  
  /**
   * 记录工具使用
   * @param toolName 工具名称
   * @param action 操作
   * @param details 详情
   * @param duration 持续时间
   */
  public logToolUsage(toolName: string, action: string, details?: any, duration?: number): void {
    this.addLog({
      type: InteractionLogType.TOOL_USAGE,
      level: InteractionLogLevel.INFO,
      action,
      target: toolName,
      targetType: 'tool',
      details,
      duration,
      sessionId: collaborationService.getSessionId(),
    });
  }
  
  /**
   * 记录错误
   * @param error 错误
   * @param context 上下文
   * @param level 级别
   */
  public logError(error: Error | string, context?: any, level: InteractionLogLevel = InteractionLogLevel.ERROR): void {
    this.addLog({
      type: InteractionLogType.ERROR,
      level,
      action: 'error',
      details: {
        error: typeof error === 'string' ? error : {
          message: error.message,
          stack: error.stack,
          name: error.name,
        },
        context,
      },
      sessionId: collaborationService.getSessionId(),
    });
  }
  
  /**
   * 记录性能
   * @param metric 指标
   * @param value 值
   * @param details 详情
   */
  public logPerformance(metric: string, value: number, details?: any): void {
    this.addLog({
      type: InteractionLogType.PERFORMANCE,
      level: InteractionLogLevel.INFO,
      action: 'performance',
      target: metric,
      targetType: 'metric',
      details: {
        value,
        ...details,
      },
      sessionId: collaborationService.getSessionId(),
    });
  }
  
  /**
   * 记录协作事件
   * @param action 操作
   * @param details 详情
   */
  public logCollaboration(action: string, details?: any): void {
    this.addLog({
      type: InteractionLogType.COLLABORATION,
      level: InteractionLogLevel.INFO,
      action,
      details,
      sessionId: collaborationService.getSessionId(),
    });
  }
  
  /**
   * 获取所有日志
   * @returns 日志列表
   */
  public getLogs(): InteractionLog[] {
    return [...this.logs];
  }
  
  /**
   * 获取指定类型的日志
   * @param type 日志类型
   * @returns 日志列表
   */
  public getLogsByType(type: InteractionLogType): InteractionLog[] {
    return this.logs.filter(log => log.type === type);
  }
  
  /**
   * 获取指定用户的日志
   * @param userId 用户ID
   * @returns 日志列表
   */
  public getLogsByUser(userId: string): InteractionLog[] {
    return this.logs.filter(log => log.userId === userId);
  }
  
  /**
   * 获取指定时间范围的日志
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @returns 日志列表
   */
  public getLogsByTimeRange(startTime: number, endTime: number): InteractionLog[] {
    return this.logs.filter(log => log.timestamp >= startTime && log.timestamp <= endTime);
  }
  
  /**
   * 清空日志
   */
  public clearLogs(): void {
    this.logs = [];
    this.pendingRemoteLogs = [];
    
    // 更新Redux状态
    store.dispatch(clearUserInteractionLogs());
    
    // 触发事件
    this.emit('logsCleared');
  }
  
  /**
   * 发送远程日志
   * @private
   */
  private async sendRemoteLogs(): Promise<void> {
    if (!this.config.enableRemoteLogging || this.pendingRemoteLogs.length === 0) {
      return;
    }
    
    try {
      // 获取要发送的日志批次
      const batch = this.pendingRemoteLogs.slice(0, this.config.remoteLoggingBatchSize);
      
      // 发送日志
      const response = await fetch(this.config.remoteLoggingUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(batch),
      });
      
      if (response.ok) {
        // 从待发送列表中移除已发送的日志
        this.pendingRemoteLogs = this.pendingRemoteLogs.slice(this.config.remoteLoggingBatchSize);
        
        // 触发事件
        this.emit('logsSent', batch);
      } else {
        throw new Error(`Failed to send logs: ${response.statusText}`);
      }
    } catch (error) {
      // 记录错误
      console.error('Failed to send logs:', error);
      
      // 触发事件
      this.emit('logSendError', error);
    }
  }
  
  /**
   * 运行分析
   * @private
   */
  private runAnalytics(): void {
    if (!this.config.enableAnalytics) {
      return;
    }
    
    // 实现日志分析逻辑
    const results = {
      // 用户活动统计
      userActivity: this.analyzeUserActivity(),
      
      // 工具使用统计
      toolUsage: this.analyzeToolUsage(),
      
      // 错误统计
      errors: this.analyzeErrors(),
      
      // 性能统计
      performance: this.analyzePerformance(),
      
      // 导航统计
      navigation: this.analyzeNavigation(),
      
      // 编辑操作统计
      editOperations: this.analyzeEditOperations(),
      
      // 时间戳
      timestamp: Date.now(),
    };
    
    // 更新分析结果
    this.analyticsResults = results;
    
    // 触发事件
    this.emit('analyticsUpdated', results);
  }
  
  /**
   * 分析用户活动
   * @private
   */
  private analyzeUserActivity(): any {
    // 实现用户活动分析逻辑
    return {};
  }
  
  /**
   * 分析工具使用
   * @private
   */
  private analyzeToolUsage(): any {
    // 实现工具使用分析逻辑
    return {};
  }
  
  /**
   * 分析错误
   * @private
   */
  private analyzeErrors(): any {
    // 实现错误分析逻辑
    return {};
  }
  
  /**
   * 分析性能
   * @private
   */
  private analyzePerformance(): any {
    // 实现性能分析逻辑
    return {};
  }
  
  /**
   * 分析导航
   * @private
   */
  private analyzeNavigation(): any {
    // 实现导航分析逻辑
    return {};
  }
  
  /**
   * 分析编辑操作
   * @private
   */
  private analyzeEditOperations(): any {
    // 实现编辑操作分析逻辑
    return {};
  }
  
  /**
   * 获取分析结果
   * @returns 分析结果
   */
  public getAnalyticsResults(): Record<string, any> {
    return { ...this.analyticsResults };
  }
  
  /**
   * 生成ID
   * @private
   */
  private generateId(): string {
    return `log_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
  }
  
  /**
   * 销毁
   */
  public destroy(): void {
    // 清除定时器
    if (this.remoteLoggingTimerId !== null) {
      window.clearInterval(this.remoteLoggingTimerId);
      this.remoteLoggingTimerId = null;
    }
    
    if (this.analyticsTimerId !== null) {
      window.clearInterval(this.analyticsTimerId);
      this.analyticsTimerId = null;
    }
    
    // 发送剩余的远程日志
    if (this.config.enableRemoteLogging && this.pendingRemoteLogs.length > 0) {
      this.sendRemoteLogs();
    }
    
    // 清空日志
    this.logs = [];
    this.pendingRemoteLogs = [];
    
    // 移除所有监听器
    this.removeAllListeners();
  }
}

// 创建单例实例
export const userInteractionLogService = new UserInteractionLogService();
