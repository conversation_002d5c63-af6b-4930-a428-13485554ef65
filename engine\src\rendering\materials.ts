/**
 * 材质模块
 * 导出所有材质相关的类和接口
 */

import * as THREE from 'three';

// 导出材质系统
export * from './materials/MaterialSystem';
export * from './materials/MaterialFactory';
export * from './materials/MaterialOptimizer';
export * from './materials/MaterialConverter';
export * from './materials/MaterialLibrary';

// 导出材质类型
export enum MaterialType {
  BASIC = 'basic',
  STANDARD = 'standard',
  PHYSICAL = 'physical',
  TOON = 'toon',
  MATCAP = 'matcap',
  NORMAL = 'normal',
  DEPTH = 'depth',
  DISTANCE = 'distance',
  LAMBERT = 'lambert',
  PHONG = 'phong',
  SHADER = 'shader',
  SPRITE = 'sprite',
  POINTS = 'points',
  LINE = 'line',
  DASH = 'dash'
}

// 导出材质接口
export interface MaterialOptions {
  /** 材质名称 */
  name?: string;
  /** 材质颜色 */
  color?: THREE.Color | string | number;
  /** 材质贴图 */
  map?: THREE.Texture;
  /** 是否透明 */
  transparent?: boolean;
  /** 透明度 */
  opacity?: number;
  /** 是否双面渲染 */
  side?: THREE.Side;
  /** 混合模式 */
  blending?: THREE.Blending;
  /** 是否启用深度测试 */
  depthTest?: boolean;
  /** 是否写入深度缓冲 */
  depthWrite?: boolean;
  /** 是否启用Alpha测试 */
  alphaTest?: number;
  /** 是否启用雾效 */
  fog?: boolean;
  /** 是否启用线框模式 */
  wireframe?: boolean;
  /** 是否启用剪裁 */
  clipIntersection?: boolean;
  /** 剪裁平面 */
  clippingPlanes?: THREE.Plane[];
  /** 自定义着色器 */
  customShader?: {
    vertexShader?: string;
    fragmentShader?: string;
    uniforms?: { [uniform: string]: THREE.IUniform };
  };
}

// 导出PBR材质接口
export interface PBRMaterialOptions extends MaterialOptions {
  /** 金属度 */
  metalness?: number;
  /** 粗糙度 */
  roughness?: number;
  /** 金属度贴图 */
  metalnessMap?: THREE.Texture;
  /** 粗糙度贴图 */
  roughnessMap?: THREE.Texture;
  /** 法线贴图 */
  normalMap?: THREE.Texture;
  /** 法线贴图缩放 */
  normalScale?: THREE.Vector2;
  /** 环境贴图 */
  envMap?: THREE.Texture;
  /** 环境贴图强度 */
  envMapIntensity?: number;
  /** 光照贴图 */
  lightMap?: THREE.Texture;
  /** 光照贴图强度 */
  lightMapIntensity?: number;
  /** 环境光遮蔽贴图 */
  aoMap?: THREE.Texture;
  /** 环境光遮蔽强度 */
  aoMapIntensity?: number;
  /** 发光贴图 */
  emissiveMap?: THREE.Texture;
  /** 发光颜色 */
  emissive?: THREE.Color | string | number;
  /** 发光强度 */
  emissiveIntensity?: number;
  /** 位移贴图 */
  displacementMap?: THREE.Texture;
  /** 位移缩放 */
  displacementScale?: number;
  /** 位移偏移 */
  displacementBias?: number;
}
