/**
 * 动画编辑器样式
 */

// 混合编辑器
.blend-editor {
  display: flex;
  flex-direction: column;
  height: 100%;

  .blend-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e8e8e8;

    h2 {
      margin: 0;
    }
  }

  .empty-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    text-align: center;

    p {
      margin-bottom: 16px;
      color: #999;
    }
  }

  .blend-space-list {
    .list-header {
      margin-bottom: 16px;
    }

    .list-content {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      .blend-space-card {
        width: 240px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        &.selected {
          border-color: #1890ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
        }
      }
    }
  }
}

// 1D混合空间编辑器
.blend-space-1d-editor {
  display: flex;
  flex-direction: column;

  .editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
    }
  }

  .editor-content {
    padding: 12px;
  }

  .blend-space-info {
    margin-bottom: 16px;

    p {
      margin-bottom: 8px;
    }
  }

  .blend-space-visualization {
    margin-bottom: 16px;

    .blend-space-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .position-display {
        font-family: monospace;
        font-size: 14px;
        padding: 4px 8px;
        background-color: #f5f5f5;
        border-radius: 4px;
      }
    }

    .blend-space-canvas {
      width: 100%;
      border: 1px solid #d9d9d9;
      border-radius: 2px;
      margin-bottom: 12px;
      cursor: pointer;
    }

    .blend-space-controls {
      display: flex;
      justify-content: center;
      margin-top: 12px;
    }

    .blend-space-instructions {
      margin-top: 8px;
      text-align: center;
      font-size: 12px;
      color: #666;
      font-style: italic;
    }
  }

  .blend-space-nodes {
    .nodes-header {
      margin-bottom: 16px;
    }

    .nodes-content {
      .selected-row {
        background-color: #e6f7ff;
      }
    }
  }
}

// 2D混合空间编辑器
.blend-space-2d-editor {
  display: flex;
  flex-direction: column;

  .editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e8e8e8;

    h3 {
      margin: 0;
    }
  }

  .editor-content {
    padding: 12px;
  }

  .blend-space-info {
    margin-bottom: 16px;

    p {
      margin-bottom: 8px;
    }
  }

  .blend-space-visualization {
    margin-bottom: 16px;

    .blend-space-canvas {
      width: 100%;
      border: 1px solid #d9d9d9;
      border-radius: 2px;
      margin-bottom: 12px;
      cursor: crosshair;
    }

    .blend-space-controls {
      display: flex;
      justify-content: center;
      margin-top: 12px;
    }
  }

  .blend-space-nodes {
    .nodes-header {
      margin-bottom: 16px;
    }

    .nodes-content {
      .selected-row {
        background-color: #e6f7ff;
      }
    }
  }
}
// 混合曲线编辑器
.blend-curve-editor {
  display: flex;
  flex-direction: column;
  padding: 16px;

  .curve-preview {
    margin: 16px 0;
    display: flex;
    flex-direction: column;
    align-items: center;

    canvas {
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background-color: #fafafa;
      margin-bottom: 16px;
    }

    .test-controls {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;
    }
  }

  .preset-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 16px;

    .preset-item {
      width: 120px;
      height: 80px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 8px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
      }

      &.selected {
        border-color: #1890ff;
        background-color: #e6f7ff;
      }

      .preset-name {
        font-size: 12px;
        margin-bottom: 4px;
        text-align: center;
      }

      .preset-preview {
        height: 40px;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-repeat: no-repeat;
          background-position: center;
          background-size: contain;
        }
      }
    }
  }

  .bezier-editor {
    margin-top: 16px;

    .bezier-canvas-container {
      position: relative;
      width: 100%;
      margin-bottom: 16px;

      canvas {
        width: 100%;
        height: 300px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background-color: #fafafa;
        cursor: pointer;
      }

      .bezier-instructions {
        position: absolute;
        bottom: 8px;
        left: 8px;
        font-size: 12px;
        color: #666;
        background-color: rgba(255, 255, 255, 0.8);
        padding: 4px 8px;
        border-radius: 4px;
      }
    }

    .bezier-controls {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }

    .bezier-presets {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 16px;

      button {
        min-width: 100px;
      }
    }
  }
}

.animation-editor {
  display: flex;
  flex-direction: column;
  height: 100%;

  .animation-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;

    h2 {
      margin: 0;
    }

    .animation-editor-actions {
      display: flex;
      gap: 8px;
    }
  }

  .animation-editor-content {
    display: flex;
    flex: 1;
    overflow: hidden;

    .animation-editor-preview {
      width: 40%;
      padding: 16px;
      display: flex;
      flex-direction: column;

      .preview-canvas {
        flex: 1;
        width: 100%;
        background-color: #1e1e1e;
        border-radius: 4px;
      }

      .preview-controls {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 16px;

        .time-display {
          margin-left: 8px;
          font-family: monospace;
        }
      }
    }

    .animation-editor-form {
      width: 60%;
      padding: 16px;
      overflow-y: auto;

      .animation-editor-timeline {
        margin-top: 16px;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        padding: 16px;

        .timeline-toolbar {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
        }

        .timeline-container {
          position: relative;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          overflow: hidden;

          .timeline-header {
            position: relative;
            height: 30px;
            border-bottom: 1px solid #e8e8e8;
            background-color: #f5f5f5;

            .timeline-ruler {
              position: relative;
              height: 100%;

              .timeline-tick {
                position: absolute;
                top: 0;
                height: 100%;
                width: 1px;
                background-color: #d9d9d9;

                span {
                  position: absolute;
                  top: 2px;
                  left: 2px;
                  font-size: 10px;
                  color: #666;
                }
              }
            }

            .timeline-cursor {
              position: absolute;
              top: 0;
              width: 2px;
              height: 100%;
              background-color: #ff4d4f;
              z-index: 1;
            }
          }

          .timeline-tracks {
            .timeline-track {
              position: relative;
              height: 40px;
              border-bottom: 1px solid #e8e8e8;
              cursor: pointer;

              &:hover {
                background-color: #f0f0f0;
              }

              &.selected {
                background-color: #e6f7ff;
              }

              .track-header {
                position: absolute;
                top: 0;
                left: 0;
                width: 120px;
                height: 100%;
                padding: 0 8px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                border-right: 1px solid #e8e8e8;
                background-color: #fafafa;
                z-index: 1;

                span {
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  font-size: 12px;
                }

                .track-actions {
                  display: flex;
                  gap: 4px;
                  opacity: 0;
                  transition: opacity 0.2s;
                }

                &:hover .track-actions {
                  opacity: 1;
                }
              }

              .track-content {
                position: relative;
                height: 100%;
                margin-left: 120px;

                .keyframe {
                  position: absolute;
                  top: 50%;
                  transform: translate(-50%, -50%);
                  width: 12px;
                  height: 12px;
                  background-color: #1890ff;
                  border-radius: 50%;
                  cursor: pointer;
                  z-index: 2;

                  &:hover {
                    transform: translate(-50%, -50%) scale(1.2);
                  }

                  &.selected {
                    background-color: #ff4d4f;
                    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
                  }
                }

                .track-lines {
                  position: absolute;
                  top: 0;
                  left: 0;
                  height: 100%;
                  pointer-events: none;
                }
              }
            }
          }
        }
      }

      .track-properties,
      .keyframe-properties {
        margin-top: 16px;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        padding: 16px;

        h3 {
          margin-top: 0;
          margin-bottom: 16px;
          font-size: 16px;
        }
      }
    }
  }
}
