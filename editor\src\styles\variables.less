// 颜色变量
@primary-color: #1890ff;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;
@info-color: #1890ff;

// 背景颜色
@bg-color: #f0f2f5;
@component-bg: #fff;
@header-bg: #001529;
@sidebar-bg: #001529;
@footer-bg: #f0f2f5;

// 文本颜色
@text-color: rgba(0, 0, 0, 0.85);
@text-color-secondary: rgba(0, 0, 0, 0.45);
@text-color-inverse: #fff;
@disabled-color: rgba(0, 0, 0, 0.25);

// 边框颜色
@border-color-base: #d9d9d9;
@border-color-split: #f0f0f0;

// 字体
@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
@code-family: 'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, Courier, monospace;

// 字体大小
@font-size-base: 14px;
@font-size-sm: 12px;
@font-size-lg: 16px;
@font-size-xl: 20px;
@font-size-xxl: 24px;

// 行高
@line-height-base: 1.5715;

// 边框圆角
@border-radius-base: 2px;
@border-radius-sm: 2px;
@border-radius-lg: 4px;

// 间距
@spacing-xs: 4px;
@spacing-sm: 8px;
@spacing-md: 16px;
@spacing-lg: 24px;
@spacing-xl: 32px;
@spacing-xxl: 48px;

// 阴影
@shadow-1: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);
@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
@shadow-3: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);

// 动画
@animation-duration-base: 0.2s;
@animation-duration-slow: 0.3s;
@animation-duration-fast: 0.1s;

// 编辑器特定变量
@editor-header-height: 40px;
@editor-toolbar-height: 40px;
@editor-statusbar-height: 24px;
@editor-panel-width: 300px;

// 暗色主题变量
@dark-bg-color: #121212;
@dark-component-bg: #1e1e1e;
@dark-header-bg: #1e1e1e;
@dark-sidebar-bg: #1e1e1e;
@dark-text-color: rgba(255, 255, 255, 0.85);
@dark-text-color-secondary: rgba(255, 255, 255, 0.45);
@dark-border-color-base: #434343;
@dark-border-color-split: #303030;
