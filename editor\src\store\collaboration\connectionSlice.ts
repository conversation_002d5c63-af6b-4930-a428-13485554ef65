/**
 * 连接状态Redux Slice
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ConnectionStatus } from '../../services/WebSocketConnectionManager';

// 连接状态接口
export interface ConnectionState {
  status: ConnectionStatus;
  lastConnected: number | null;
  lastDisconnected: number | null;
  reconnectAttempts: number;
  latency: number;
  isReconnecting: boolean;
  error: string | null;
}

// 初始状态
const initialState: ConnectionState = {
  status: ConnectionStatus.DISCONNECTED,
  lastConnected: null,
  lastDisconnected: null,
  reconnectAttempts: 0,
  latency: 0,
  isReconnecting: false,
  error: null
};

// 创建Slice
const connectionSlice = createSlice({
  name: 'connection',
  initialState,
  reducers: {
    // 设置连接状态
    setConnectionStatus: (state, action: PayloadAction<ConnectionStatus>) => {
      const prevStatus = state.status;
      state.status = action.payload;
      
      // 更新连接/断开时间
      if (prevStatus !== ConnectionStatus.CONNECTED && action.payload === ConnectionStatus.CONNECTED) {
        state.lastConnected = Date.now();
        state.reconnectAttempts = 0;
        state.error = null;
      } else if (prevStatus === ConnectionStatus.CONNECTED && action.payload !== ConnectionStatus.CONNECTED) {
        state.lastDisconnected = Date.now();
      }
      
      // 更新重连状态
      state.isReconnecting = action.payload === ConnectionStatus.RECONNECTING;
    },
    
    // 设置延迟
    setLatency: (state, action: PayloadAction<number>) => {
      state.latency = action.payload;
    },
    
    // 增加重连尝试次数
    incrementReconnectAttempts: (state) => {
      state.reconnectAttempts += 1;
    },
    
    // 设置重连尝试次数
    setReconnectAttempts: (state, action: PayloadAction<number>) => {
      state.reconnectAttempts = action.payload;
    },
    
    // 设置错误
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    // 重置状态
    resetConnectionState: (state) => {
      state.status = ConnectionStatus.DISCONNECTED;
      state.reconnectAttempts = 0;
      state.isReconnecting = false;
      state.error = null;
    }
  }
});

// 导出Actions
export const {
  setConnectionStatus,
  setLatency,
  incrementReconnectAttempts,
  setReconnectAttempts,
  setError,
  resetConnectionState
} = connectionSlice.actions;

// 导出Reducer
export default connectionSlice.reducer;
