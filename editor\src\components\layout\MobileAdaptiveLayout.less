/**
 * 移动设备适配布局样式
 */
@import '../../styles/responsive.less';

.mobile-adaptive-layout {
  width: 100%;
  height: 100vh;
  overflow: hidden;

  // 移动内容区域
  .mobile-content {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  // 触控控制容器
  .touch-control-container {
    position: absolute;
    bottom: 60px;
    left: 0;
    right: 0;
    z-index: 900;
    transition: all 0.3s ease;
  }

  // 控制面板切换按钮
  .control-panel-toggle {
    position: absolute;
    bottom: 70px;
    right: 16px;
    width: 48px;
    height: 48px;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
    }
  }

  // 全屏切换按钮
  .fullscreen-toggle {
    position: absolute;
    bottom: 70px;
    left: 16px;
    width: 48px;
    height: 48px;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
    }
  }

  // 状态指示器
  .status-indicators {
    position: absolute;
    top: 16px;
    right: 16px;
    display: flex;
    gap: 8px;
    z-index: 1000;

    .device-indicator,
    .performance-indicator,
    .network-indicator,
    .gyroscope-indicator-icon {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.5);
      color: #fff;
      border-radius: 50%;
      font-size: 18px;
      transition: all 0.3s ease;
    }
  }

  // 横屏模式
  &.landscape {
    .touch-control-container {
      bottom: 60px;
    }

    .control-panel-toggle {
      bottom: 70px;
      right: 16px;
    }

    .fullscreen-toggle {
      bottom: 70px;
      left: 16px;
    }
  }

  // 竖屏模式
  &.portrait {
    .touch-control-container {
      bottom: 120px;
    }

    .control-panel-toggle {
      bottom: 70px;
      right: 16px;
    }

    .fullscreen-toggle {
      bottom: 70px;
      left: 16px;
    }
  }

  // 触控模式
  &.touch-mode {
    .control-panel-toggle,
    .fullscreen-toggle {
      width: 56px;
      height: 56px;

      .anticon {
        font-size: 24px;
      }
    }

    .status-indicators {
      .device-indicator,
      .performance-indicator,
      .network-indicator,
      .gyroscope-indicator-icon {
        width: 40px;
        height: 40px;
        font-size: 22px;
      }
    }
  }

  // 断点适配
  &.breakpoint-xs,
  &.breakpoint-sm {
    .status-indicators {
      flex-direction: column;
    }
  }

  // 设备类型适配
  &.device-mobile {
    .touch-control-container {
      bottom: 80px;
    }
  }

  &.device-tablet {
    .touch-control-container {
      bottom: 70px;
    }
  }
}

// 移动加载状态
.mobile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background-color: #f0f2f5;

  .loading-text {
    margin-top: 16px;
    font-size: 16px;
    color: #1890ff;
  }
}

// 警告模态框
.warning-title {
  display: flex;
  align-items: center;

  .warning-icon {
    color: #faad14;
    font-size: 18px;
    margin-right: 8px;
  }
}

.warning-message {
  margin-bottom: 16px;

  p {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 响应式调整
@media (max-width: @screen-sm) {
  .mobile-adaptive-layout {
    .control-panel-toggle,
    .fullscreen-toggle {
      width: 40px;
      height: 40px;

      .anticon {
        font-size: 18px;
      }
    }

    .status-indicators {
      top: 8px;
      right: 8px;
      gap: 4px;

      .device-indicator,
      .performance-indicator,
      .network-indicator,
      .gyroscope-indicator-icon {
        width: 28px;
        height: 28px;
        font-size: 16px;
      }
    }

    &.touch-mode {
      .control-panel-toggle,
      .fullscreen-toggle {
        width: 48px;
        height: 48px;

        .anticon {
          font-size: 20px;
        }
      }

      .status-indicators {
        .device-indicator,
        .performance-indicator,
        .network-indicator,
        .gyroscope-indicator-icon {
          width: 32px;
          height: 32px;
          font-size: 18px;
        }
      }
    }
  }
}

// 暗色主题
.dark-theme {
  .mobile-adaptive-layout {
    background-color: #141414;
  }

  .mobile-loading {
    background-color: #141414;

    .loading-text {
      color: #1890ff;
    }
  }
}
