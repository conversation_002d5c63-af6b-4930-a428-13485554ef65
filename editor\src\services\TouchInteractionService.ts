/**
 * 触控交互服务
 * 提供触控操作优化和手势识别功能
 */
import { EventEmitter } from 'events';
import MobileDeviceService from './MobileDeviceService';

// 手势类型枚举
export enum GestureType {
  TAP = 'tap',
  DOUBLE_TAP = 'doubleTap',
  LONG_PRESS = 'longPress',
  SWIPE = 'swipe',
  PINCH = 'pinch',
  ROTATE = 'rotate',
  PAN = 'pan'
}

// 手势方向枚举
export enum GestureDirection {
  UP = 'up',
  DOWN = 'down',
  LEFT = 'left',
  RIGHT = 'right',
  NONE = 'none'
}

// 手势状态枚举
export enum GestureState {
  STARTED = 'started',
  UPDATED = 'updated',
  ENDED = 'ended',
  CANCELLED = 'cancelled'
}

// 手势数据接口
export interface GestureData {
  // 手势类型
  type: GestureType;
  // 手势状态
  state: GestureState;
  // 手势方向（适用于滑动手势）
  direction?: GestureDirection;
  // 手势位置
  position: { x: number; y: number };
  // 手势开始位置
  startPosition: { x: number; y: number };
  // 手势移动距离
  distance?: { x: number; y: number };
  // 手势缩放比例（适用于捏合手势）
  scale?: number;
  // 手势旋转角度（适用于旋转手势）
  rotation?: number;
  // 手势速度
  velocity?: { x: number; y: number };
  // 手势持续时间（毫秒）
  duration?: number;
  // 触摸点数量
  pointerCount?: number;
  // 原始事件
  originalEvent?: TouchEvent | MouseEvent;
}

// 触控交互配置接口
export interface TouchInteractionConfig {
  // 是否启用调试模式
  debug?: boolean;
  // 是否启用触控优化
  enabled?: boolean;
  // 是否阻止默认行为
  preventDefault?: boolean;
  // 是否阻止事件传播
  stopPropagation?: boolean;
  // 双击时间阈值（毫秒）
  doubleTapThreshold?: number;
  // 长按时间阈值（毫秒）
  longPressThreshold?: number;
  // 滑动距离阈值（像素）
  swipeThreshold?: number;
  // 滑动速度阈值（像素/毫秒）
  swipeVelocityThreshold?: number;
  // 是否启用多点触控
  enableMultiTouch?: boolean;
  // 是否启用惯性
  enableInertia?: boolean;
  // 惯性衰减系数
  inertiaDecay?: number;
  // 是否启用触控反馈
  enableFeedback?: boolean;
}

// 触控交互事件类型
export enum TouchInteractionEventType {
  GESTURE = 'gesture',
  TAP = 'tap',
  DOUBLE_TAP = 'doubleTap',
  LONG_PRESS = 'longPress',
  SWIPE = 'swipe',
  PINCH = 'pinch',
  ROTATE = 'rotate',
  PAN = 'pan',
  INERTIA_START = 'inertiaStart',
  INERTIA_UPDATE = 'inertiaUpdate',
  INERTIA_END = 'inertiaEnd'
}

/**
 * 触控交互服务类
 * 提供触控操作优化和手势识别功能
 */
export class TouchInteractionService extends EventEmitter {
  private static instance: TouchInteractionService;
  private mobileDeviceService: MobileDeviceService;
  private config: TouchInteractionConfig;

  // 触摸状态
  private touchActive: boolean = false;
  private touchStartTime: number = 0;
  private touchStartPosition: { x: number; y: number } = { x: 0, y: 0 };
  private touchCurrentPosition: { x: number; y: number } = { x: 0, y: 0 };
  private touchPreviousPosition: { x: number; y: number } = { x: 0, y: 0 };
  private touchVelocity: { x: number; y: number } = { x: 0, y: 0 };

  // 多点触控状态
  private touches: Touch[] = [];
  private previousTouches: Touch[] = [];
  private initialDistance: number = 0;
  private initialAngle: number = 0;
  private currentDistance: number = 0;
  private currentAngle: number = 0;

  // 手势状态
  private isGesturing: boolean = false;
  private currentGesture: GestureType | null = null;
  private longPressTimer: number | null = null;
  private lastTapTime: number = 0;

  // 惯性状态
  private inertiaActive: boolean = false;
  private inertiaVelocity: { x: number; y: number } = { x: 0, y: 0 };
  private inertiaTimerId: number | null = null;

  // 目标元素
  private targetElement: HTMLElement | null = null;

  /**
   * 获取单例实例
   * @returns 触控交互服务实例
   */
  public static getInstance(): TouchInteractionService {
    if (!TouchInteractionService.instance) {
      TouchInteractionService.instance = new TouchInteractionService();
    }
    return TouchInteractionService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    super();
    this.mobileDeviceService = MobileDeviceService.getInstance();

    // 默认配置
    this.config = {
      debug: false,
      enabled: true,
      preventDefault: true,
      stopPropagation: false,
      doubleTapThreshold: 300,
      longPressThreshold: 500,
      swipeThreshold: 50,
      swipeVelocityThreshold: 0.3,
      enableMultiTouch: true,
      enableInertia: true,
      inertiaDecay: 0.95,
      enableFeedback: true
    };
  }

  /**
   * 配置服务
   * @param config 配置
   */
  public configure(config: Partial<TouchInteractionConfig>): void {
    this.config = { ...this.config, ...config };

    if (this.config.debug) {
      console.log('触控交互服务配置已更新', this.config);
    }
  }

  /**
   * 初始化触控事件监听
   * @param element 目标元素
   */
  public initialize(element: HTMLElement): void {
    if (!this.config.enabled) {
      return;
    }

    this.targetElement = element;

    // 添加触摸事件监听
    element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: !this.config.preventDefault });
    element.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: !this.config.preventDefault });
    element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: !this.config.preventDefault });
    element.addEventListener('touchcancel', this.handleTouchCancel.bind(this), { passive: !this.config.preventDefault });

    // 添加鼠标事件监听（用于桌面设备测试）
    element.addEventListener('mousedown', this.handleMouseDown.bind(this));
    window.addEventListener('mousemove', this.handleMouseMove.bind(this));
    window.addEventListener('mouseup', this.handleMouseUp.bind(this));

    if (this.config.debug) {
      console.log('触控交互服务已初始化', element);
    }
  }

  /**
   * 销毁触控事件监听
   */
  public destroy(): void {
    if (!this.targetElement) {
      return;
    }

    // 移除触摸事件监听
    this.targetElement.removeEventListener('touchstart', this.handleTouchStart.bind(this));
    this.targetElement.removeEventListener('touchmove', this.handleTouchMove.bind(this));
    this.targetElement.removeEventListener('touchend', this.handleTouchEnd.bind(this));
    this.targetElement.removeEventListener('touchcancel', this.handleTouchCancel.bind(this));

    // 移除鼠标事件监听
    this.targetElement.removeEventListener('mousedown', this.handleMouseDown.bind(this));
    window.removeEventListener('mousemove', this.handleMouseMove.bind(this));
    window.removeEventListener('mouseup', this.handleMouseUp.bind(this));

    // 清除长按定时器
    if (this.longPressTimer !== null) {
      window.clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    // 清除惯性定时器
    if (this.inertiaTimerId !== null) {
      window.clearInterval(this.inertiaTimerId);
      this.inertiaTimerId = null;
    }

    this.targetElement = null;

    if (this.config.debug) {
      console.log('触控交互服务已销毁');
    }
  }

  /**
   * 处理触摸开始事件
   * @param event 触摸事件
   */
  private handleTouchStart(event: TouchEvent): void {
    if (!this.config.enabled) {
      return;
    }

    // 阻止默认行为和事件传播
    if (this.config.preventDefault) event.preventDefault();
    if (this.config.stopPropagation) event.stopPropagation();

    // 保存触摸点
    this.touches = Array.from(event.touches);
    this.previousTouches = [...this.touches];

    // 记录触摸开始时间和位置
    this.touchActive = true;
    this.touchStartTime = Date.now();

    // 单点触摸
    if (event.touches.length === 1) {
      const touch = event.touches[0];
      this.touchStartPosition = { x: touch.clientX, y: touch.clientY };
      this.touchCurrentPosition = { x: touch.clientX, y: touch.clientY };
      this.touchPreviousPosition = { x: touch.clientX, y: touch.clientY };

      // 启动长按定时器
      this.startLongPressTimer(event);
    }
    // 多点触摸
    else if (event.touches.length === 2 && this.config.enableMultiTouch) {
      this.handleMultiTouchStart(event);
    }

    // 停止惯性
    this.stopInertia();

    // 设置手势状态
    this.isGesturing = true;
    this.currentGesture = null;

    // 发出手势开始事件
    this.emitGestureEvent(GestureType.TAP, GestureState.STARTED, event);
  }

  /**
   * 处理触摸移动事件
   * @param event 触摸事件
   */
  private handleTouchMove(event: TouchEvent): void {
    if (!this.config.enabled || !this.touchActive) {
      return;
    }

    // 阻止默认行为和事件传播
    if (this.config.preventDefault) event.preventDefault();
    if (this.config.stopPropagation) event.stopPropagation();

    // 保存触摸点
    this.previousTouches = [...this.touches];
    this.touches = Array.from(event.touches);

    // 单点触摸
    if (event.touches.length === 1) {
      const touch = event.touches[0];
      this.touchPreviousPosition = { ...this.touchCurrentPosition };
      this.touchCurrentPosition = { x: touch.clientX, y: touch.clientY };

      // 计算速度
      const time = Date.now() - this.touchStartTime;
      if (time > 0) {
        this.touchVelocity = {
          x: (this.touchCurrentPosition.x - this.touchPreviousPosition.x) / time,
          y: (this.touchCurrentPosition.y - this.touchPreviousPosition.y) / time
        };
      }

      // 检测滑动手势
      this.detectPanGesture(event);
    }
    // 多点触摸
    else if (event.touches.length === 2 && this.config.enableMultiTouch) {
      this.handleMultiTouchMove(event);
    }

    // 清除长按定时器
    if (this.longPressTimer !== null) {
      window.clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  /**
   * 处理触摸结束事件
   * @param event 触摸事件
   */
  private handleTouchEnd(event: TouchEvent): void {
    if (!this.config.enabled || !this.touchActive) {
      return;
    }

    // 阻止默认行为和事件传播
    if (this.config.preventDefault) event.preventDefault();
    if (this.config.stopPropagation) event.stopPropagation();

    // 清除长按定时器
    if (this.longPressTimer !== null) {
      window.clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    // 计算触摸持续时间
    const touchDuration = Date.now() - this.touchStartTime;

    // 计算触摸距离
    const dx = this.touchCurrentPosition.x - this.touchStartPosition.x;
    const dy = this.touchCurrentPosition.y - this.touchStartPosition.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    // 检测手势类型
    if (distance < this.config.swipeThreshold!) {
      // 检测双击
      const now = Date.now();
      if (now - this.lastTapTime < this.config.doubleTapThreshold!) {
        this.emitGestureEvent(GestureType.DOUBLE_TAP, GestureState.ENDED, event);
        this.lastTapTime = 0; // 重置双击时间
      } else {
        this.emitGestureEvent(GestureType.TAP, GestureState.ENDED, event);
        this.lastTapTime = now;
      }
    } else {
      // 检测滑动
      this.detectSwipeGesture(event, touchDuration);
    }

    // 启动惯性
    if (this.config.enableInertia && this.currentGesture === GestureType.PAN) {
      this.startInertia();
    }

    // 重置触摸状态
    this.touchActive = false;
    this.isGesturing = false;
    this.currentGesture = null;
  }

  /**
   * 处理触摸取消事件
   * @param event 触摸事件
   */
  private handleTouchCancel(event: TouchEvent): void {
    if (!this.config.enabled) {
      return;
    }

    // 阻止默认行为和事件传播
    if (this.config.preventDefault) event.preventDefault();
    if (this.config.stopPropagation) event.stopPropagation();

    // 清除长按定时器
    if (this.longPressTimer !== null) {
      window.clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    // 停止惯性
    this.stopInertia();

    // 发出手势取消事件
    if (this.currentGesture) {
      this.emitGestureEvent(this.currentGesture, GestureState.CANCELLED, event);
    }

    // 重置触摸状态
    this.touchActive = false;
    this.isGesturing = false;
    this.currentGesture = null;
  }

  /**
   * 处理鼠标按下事件
   * @param event 鼠标事件
   */
  private handleMouseDown(event: MouseEvent): void {
    if (!this.config.enabled || this.mobileDeviceService.isTouchDevice()) {
      return;
    }

    // 记录触摸开始时间和位置
    this.touchActive = true;
    this.touchStartTime = Date.now();
    this.touchStartPosition = { x: event.clientX, y: event.clientY };
    this.touchCurrentPosition = { x: event.clientX, y: event.clientY };
    this.touchPreviousPosition = { x: event.clientX, y: event.clientY };

    // 启动长按定时器
    this.startLongPressTimer(event);

    // 停止惯性
    this.stopInertia();

    // 设置手势状态
    this.isGesturing = true;
    this.currentGesture = null;

    // 发出手势开始事件
    this.emitGestureEvent(GestureType.TAP, GestureState.STARTED, event);
  }

  /**
   * 处理鼠标移动事件
   * @param event 鼠标事件
   */
  private handleMouseMove(event: MouseEvent): void {
    if (!this.config.enabled || !this.touchActive || this.mobileDeviceService.isTouchDevice()) {
      return;
    }

    this.touchPreviousPosition = { ...this.touchCurrentPosition };
    this.touchCurrentPosition = { x: event.clientX, y: event.clientY };

    // 计算速度
    const time = Date.now() - this.touchStartTime;
    if (time > 0) {
      this.touchVelocity = {
        x: (this.touchCurrentPosition.x - this.touchPreviousPosition.x) / time,
        y: (this.touchCurrentPosition.y - this.touchPreviousPosition.y) / time
      };
    }

    // 检测滑动手势
    this.detectPanGesture(event);

    // 清除长按定时器
    if (this.longPressTimer !== null) {
      window.clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  /**
   * 处理鼠标释放事件
   * @param event 鼠标事件
   */
  private handleMouseUp(event: MouseEvent): void {
    if (!this.config.enabled || !this.touchActive || this.mobileDeviceService.isTouchDevice()) {
      return;
    }

    // 清除长按定时器
    if (this.longPressTimer !== null) {
      window.clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    // 计算触摸持续时间
    const touchDuration = Date.now() - this.touchStartTime;

    // 计算触摸距离
    const dx = this.touchCurrentPosition.x - this.touchStartPosition.x;
    const dy = this.touchCurrentPosition.y - this.touchStartPosition.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    // 检测手势类型
    if (distance < this.config.swipeThreshold!) {
      // 检测双击
      const now = Date.now();
      if (now - this.lastTapTime < this.config.doubleTapThreshold!) {
        this.emitGestureEvent(GestureType.DOUBLE_TAP, GestureState.ENDED, event);
        this.lastTapTime = 0; // 重置双击时间
      } else {
        this.emitGestureEvent(GestureType.TAP, GestureState.ENDED, event);
        this.lastTapTime = now;
      }
    } else {
      // 检测滑动
      this.detectSwipeGesture(event, touchDuration);
    }

    // 启动惯性
    if (this.config.enableInertia && this.currentGesture === GestureType.PAN) {
      this.startInertia();
    }

    // 重置触摸状态
    this.touchActive = false;
    this.isGesturing = false;
    this.currentGesture = null;
  }
}

  /**
   * 启动长按定时器
   * @param event 事件
   */
  private startLongPressTimer(event: TouchEvent | MouseEvent): void {
    if (this.longPressTimer !== null) {
      window.clearTimeout(this.longPressTimer);
    }

    this.longPressTimer = window.setTimeout(() => {
      // 发出长按事件
      this.emitGestureEvent(GestureType.LONG_PRESS, GestureState.ENDED, event);
      this.currentGesture = GestureType.LONG_PRESS;
      this.longPressTimer = null;
    }, this.config.longPressThreshold);
  }

  /**
   * 处理多点触摸开始
   * @param event 触摸事件
   */
  private handleMultiTouchStart(event: TouchEvent): void {
    if (event.touches.length !== 2) {
      return;
    }

    const touch1 = event.touches[0];
    const touch2 = event.touches[1];

    // 计算初始距离和角度
    const dx = touch2.clientX - touch1.clientX;
    const dy = touch2.clientY - touch1.clientY;
    this.initialDistance = Math.sqrt(dx * dx + dy * dy);
    this.currentDistance = this.initialDistance;
    this.initialAngle = Math.atan2(dy, dx);
    this.currentAngle = this.initialAngle;
  }

  /**
   * 处理多点触摸移动
   * @param event 触摸事件
   */
  private handleMultiTouchMove(event: TouchEvent): void {
    if (event.touches.length !== 2) {
      return;
    }

    const touch1 = event.touches[0];
    const touch2 = event.touches[1];

    // 计算当前距离和角度
    const dx = touch2.clientX - touch1.clientX;
    const dy = touch2.clientY - touch1.clientY;
    this.currentDistance = Math.sqrt(dx * dx + dy * dy);
    this.currentAngle = Math.atan2(dy, dx);

    // 计算缩放比例
    const scale = this.currentDistance / this.initialDistance;

    // 计算旋转角度
    let rotation = (this.currentAngle - this.initialAngle) * (180 / Math.PI);
    if (rotation < 0) {
      rotation += 360;
    }

    // 检测捏合手势
    if (Math.abs(scale - 1) > 0.1) {
      this.currentGesture = GestureType.PINCH;
      this.emitGestureEvent(GestureType.PINCH, GestureState.UPDATED, event, {
        scale,
        center: {
          x: (touch1.clientX + touch2.clientX) / 2,
          y: (touch1.clientY + touch2.clientY) / 2
        }
      });
    }

    // 检测旋转手势
    if (Math.abs(rotation) > 10) {
      this.currentGesture = GestureType.ROTATE;
      this.emitGestureEvent(GestureType.ROTATE, GestureState.UPDATED, event, {
        rotation,
        center: {
          x: (touch1.clientX + touch2.clientX) / 2,
          y: (touch1.clientY + touch2.clientY) / 2
        }
      });
    }
  }

  /**
   * 检测滑动手势
   * @param event 事件
   * @param duration 持续时间
   */
  private detectSwipeGesture(event: TouchEvent | MouseEvent, duration: number): void {
    // 计算距离和方向
    const dx = this.touchCurrentPosition.x - this.touchStartPosition.x;
    const dy = this.touchCurrentPosition.y - this.touchStartPosition.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    // 计算速度
    const velocity = distance / duration;

    // 如果距离和速度超过阈值，则认为是滑动手势
    if (distance >= this.config.swipeThreshold! && velocity >= this.config.swipeVelocityThreshold!) {
      // 确定滑动方向
      let direction = GestureDirection.NONE;

      if (Math.abs(dx) > Math.abs(dy)) {
        // 水平滑动
        direction = dx > 0 ? GestureDirection.RIGHT : GestureDirection.LEFT;
      } else {
        // 垂直滑动
        direction = dy > 0 ? GestureDirection.DOWN : GestureDirection.UP;
      }

      // 发出滑动事件
      this.emitGestureEvent(GestureType.SWIPE, GestureState.ENDED, event, { direction, velocity });
      this.currentGesture = GestureType.SWIPE;
    }
  }

  /**
   * 检测平移手势
   * @param event 事件
   */
  private detectPanGesture(event: TouchEvent | MouseEvent): void {
    // 计算距离
    const dx = this.touchCurrentPosition.x - this.touchStartPosition.x;
    const dy = this.touchCurrentPosition.y - this.touchStartPosition.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    // 如果距离超过阈值，则认为是平移手势
    if (distance >= this.config.swipeThreshold!) {
      this.currentGesture = GestureType.PAN;
      this.emitGestureEvent(GestureType.PAN, GestureState.UPDATED, event);
    }
  }

  /**
   * 启动惯性
   */
  private startInertia(): void {
    if (!this.config.enableInertia || this.inertiaActive) {
      return;
    }

    this.inertiaActive = true;
    this.inertiaVelocity = { ...this.touchVelocity };

    // 发出惯性开始事件
    this.emit(TouchInteractionEventType.INERTIA_START, {
      velocity: this.inertiaVelocity
    });

    // 启动惯性定时器
    this.inertiaTimerId = window.setInterval(() => {
      // 更新位置
      this.touchPreviousPosition = { ...this.touchCurrentPosition };
      this.touchCurrentPosition = {
        x: this.touchCurrentPosition.x + this.inertiaVelocity.x * 16, // 假设16ms帧率
        y: this.touchCurrentPosition.y + this.inertiaVelocity.y * 16
      };

      // 更新速度（应用衰减）
      this.inertiaVelocity = {
        x: this.inertiaVelocity.x * this.config.inertiaDecay!,
        y: this.inertiaVelocity.y * this.config.inertiaDecay!
      };

      // 发出惯性更新事件
      this.emit(TouchInteractionEventType.INERTIA_UPDATE, {
        position: this.touchCurrentPosition,
        velocity: this.inertiaVelocity
      });

      // 如果速度足够小，则停止惯性
      if (Math.abs(this.inertiaVelocity.x) < 0.01 && Math.abs(this.inertiaVelocity.y) < 0.01) {
        this.stopInertia();
      }
    }, 16); // 约60fps
  }

  /**
   * 停止惯性
   */
  private stopInertia(): void {
    if (!this.inertiaActive) {
      return;
    }

    if (this.inertiaTimerId !== null) {
      window.clearInterval(this.inertiaTimerId);
      this.inertiaTimerId = null;
    }

    this.inertiaActive = false;

    // 发出惯性结束事件
    this.emit(TouchInteractionEventType.INERTIA_END, {
      position: this.touchCurrentPosition
    });
  }

  /**
   * 发出手势事件
   * @param type 手势类型
   * @param state 手势状态
   * @param event 原始事件
   * @param extraData 额外数据
   */
  private emitGestureEvent(type: GestureType, state: GestureState, event: TouchEvent | MouseEvent, extraData: any = {}): void {
    // 构建手势数据
    const gestureData: GestureData = {
      type,
      state,
      position: { ...this.touchCurrentPosition },
      startPosition: { ...this.touchStartPosition },
      distance: {
        x: this.touchCurrentPosition.x - this.touchStartPosition.x,
        y: this.touchCurrentPosition.y - this.touchStartPosition.y
      },
      velocity: { ...this.touchVelocity },
      duration: Date.now() - this.touchStartTime,
      pointerCount: event instanceof TouchEvent ? event.touches.length : 1,
      originalEvent: event,
      ...extraData
    };

    // 发出通用手势事件
    this.emit(TouchInteractionEventType.GESTURE, gestureData);

    // 发出特定手势事件
    this.emit(type, gestureData);

    // 提供触觉反馈（如果支持）
    if (this.config.enableFeedback && 'vibrate' in navigator) {
      switch (type) {
        case GestureType.TAP:
          if (state === GestureState.ENDED) {
            navigator.vibrate(10);
          }
          break;
        case GestureType.DOUBLE_TAP:
          if (state === GestureState.ENDED) {
            navigator.vibrate([10, 30, 10]);
          }
          break;
        case GestureType.LONG_PRESS:
          if (state === GestureState.ENDED) {
            navigator.vibrate(50);
          }
          break;
        case GestureType.SWIPE:
          if (state === GestureState.ENDED) {
            navigator.vibrate(20);
          }
          break;
      }
    }

    if (this.config.debug) {
      console.log(`手势: ${type}, 状态: ${state}`, gestureData);
    }
  }
}

export default TouchInteractionService.getInstance();
