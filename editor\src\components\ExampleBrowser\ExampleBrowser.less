/**
 * 示例项目浏览器样式
 */
@import '../../styles/variables.less';

.example-browser {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: @background-color-light;

  .example-browser-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    background-color: @component-background;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 10;
    height: auto;
    line-height: normal;

    .header-title {
      display: flex;
      align-items: center;

      h2 {
        margin: 0;
        margin-right: 16px;
        color: @heading-color;
        font-weight: 600;
      }
    }

    .header-search {
      flex: 1;
      display: flex;
      justify-content: center;
      max-width: 400px;
    }

    .header-filter {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }

  .example-browser-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      gap: 16px;
    }

    .example-grid {
      margin-top: 16px;
    }
  }
}
