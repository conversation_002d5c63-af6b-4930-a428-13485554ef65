/**
 * 增强触控交互服务
 * 提供更多设备特定功能的触控操作优化和手势识别
 */
import { EventEmitter } from 'events';
import TouchInteractionService, { 
  GestureType, 
  GestureDirection, 
  GestureState, 
  GestureData, 
  TouchInteractionConfig,
  TouchInteractionEventType
} from './TouchInteractionService';
import MobileDeviceService, { DeviceType } from './MobileDeviceService';
import AccessibilityService from './AccessibilityService';

// 增强手势类型枚举
export enum EnhancedGestureType {
  // 基础手势
  TAP = 'tap',
  DOUBLE_TAP = 'doubleTap',
  LONG_PRESS = 'longPress',
  SWIPE = 'swipe',
  PINCH = 'pinch',
  ROTATE = 'rotate',
  PAN = 'pan',
  
  // 增强手势
  TRIPLE_TAP = 'tripleTap',
  TWO_FINGER_TAP = 'twoFingerTap',
  THREE_FINGER_TAP = 'threeFingerTap',
  TWO_FINGER_LONG_PRESS = 'twoFingerLongPress',
  EDGE_SWIPE = 'edgeSwipe',
  CIRCLE = 'circle',
  ZIG_ZAG = 'zigZag',
  SHAKE = 'shake'
}

// 边缘类型枚举
export enum EdgeType {
  TOP = 'top',
  BOTTOM = 'bottom',
  LEFT = 'left',
  RIGHT = 'right',
  NONE = 'none'
}

// 增强手势数据接口
export interface EnhancedGestureData extends GestureData {
  // 边缘类型（适用于边缘滑动手势）
  edge?: EdgeType;
  // 手指数量
  fingers?: number;
  // 圆形手势半径
  radius?: number;
  // 圆形手势完成度（0-1）
  circleCompletion?: number;
  // 震动强度
  shakeIntensity?: number;
  // 是否为辅助功能手势
  isAccessibilityGesture?: boolean;
}

// 增强触控交互配置接口
export interface EnhancedTouchInteractionConfig extends TouchInteractionConfig {
  // 是否启用三击手势
  enableTripleTap?: boolean;
  // 是否启用多指手势
  enableMultiFingerGestures?: boolean;
  // 是否启用边缘手势
  enableEdgeGestures?: boolean;
  // 是否启用形状手势
  enableShapeGestures?: boolean;
  // 是否启用设备运动手势
  enableMotionGestures?: boolean;
  // 是否启用辅助功能手势
  enableAccessibilityGestures?: boolean;
  // 三击时间阈值（毫秒）
  tripleTapThreshold?: number;
  // 边缘区域大小（像素）
  edgeSize?: number;
  // 圆形手势识别阈值
  circleThreshold?: number;
  // 震动识别阈值
  shakeThreshold?: number;
  // 震动识别间隔（毫秒）
  shakeInterval?: number;
}

// 增强触控交互事件类型
export enum EnhancedTouchInteractionEventType {
  // 基础事件
  GESTURE = 'gesture',
  TAP = 'tap',
  DOUBLE_TAP = 'doubleTap',
  LONG_PRESS = 'longPress',
  SWIPE = 'swipe',
  PINCH = 'pinch',
  ROTATE = 'rotate',
  PAN = 'pan',
  INERTIA_START = 'inertiaStart',
  INERTIA_UPDATE = 'inertiaUpdate',
  INERTIA_END = 'inertiaEnd',
  
  // 增强事件
  TRIPLE_TAP = 'tripleTap',
  TWO_FINGER_TAP = 'twoFingerTap',
  THREE_FINGER_TAP = 'threeFingerTap',
  TWO_FINGER_LONG_PRESS = 'twoFingerLongPress',
  EDGE_SWIPE = 'edgeSwipe',
  CIRCLE = 'circle',
  ZIG_ZAG = 'zigZag',
  SHAKE = 'shake',
  
  // 辅助功能事件
  ACCESSIBILITY_GESTURE = 'accessibilityGesture'
}

/**
 * 增强触控交互服务类
 * 提供更多设备特定功能的触控操作优化和手势识别
 */
export class EnhancedTouchInteractionService extends EventEmitter {
  private static instance: EnhancedTouchInteractionService;
  private touchInteractionService: TouchInteractionService;
  private mobileDeviceService: MobileDeviceService;
  private accessibilityService: AccessibilityService;
  private config: EnhancedTouchInteractionConfig;
  
  // 手势状态
  private lastTapTime: number = 0;
  private tapCount: number = 0;
  private touchPoints: Array<{x: number, y: number, time: number}> = [];
  private isEdgeArea: boolean = false;
  private edgeType: EdgeType = EdgeType.NONE;
  private circlePoints: Array<{x: number, y: number}> = [];
  private shakeCount: number = 0;
  private lastShakeTime: number = 0;
  private deviceMotionListener: ((event: DeviceMotionEvent) => void) | null = null;
  
  // 目标元素
  private targetElement: HTMLElement | null = null;
  
  /**
   * 获取单例实例
   * @returns 增强触控交互服务实例
   */
  public static getInstance(): EnhancedTouchInteractionService {
    if (!EnhancedTouchInteractionService.instance) {
      EnhancedTouchInteractionService.instance = new EnhancedTouchInteractionService();
    }
    return EnhancedTouchInteractionService.instance;
  }
  
  /**
   * 私有构造函数
   */
  private constructor() {
    super();
    this.touchInteractionService = TouchInteractionService.getInstance();
    this.mobileDeviceService = MobileDeviceService.getInstance();
    this.accessibilityService = AccessibilityService.getInstance();
    
    // 默认配置
    this.config = {
      debug: false,
      enabled: true,
      enableTripleTap: true,
      enableMultiFingerGestures: true,
      enableEdgeGestures: true,
      enableShapeGestures: true,
      enableMotionGestures: true,
      enableAccessibilityGestures: true,
      tripleTapThreshold: 500,
      edgeSize: 50,
      circleThreshold: 0.7,
      shakeThreshold: 15,
      shakeInterval: 1000
    };
  }
  
  /**
   * 配置服务
   * @param config 配置
   */
  public configure(config: Partial<EnhancedTouchInteractionConfig>): void {
    this.config = { ...this.config, ...config };
    
    // 配置基础触控交互服务
    this.touchInteractionService.configure(config);
    
    if (this.config.debug) {
      console.log('增强触控交互服务配置已更新', this.config);
    }
  }
  
  /**
   * 初始化触控事件监听
   * @param element 目标元素
   */
  public initialize(element: HTMLElement): void {
    if (!this.config.enabled) {
      return;
    }
    
    this.targetElement = element;
    
    // 初始化基础触控交互服务
    this.touchInteractionService.initialize(element);
    
    // 监听基础手势事件
    this.setupBasicGestureListeners();
    
    // 设置设备运动监听
    if (this.config.enableMotionGestures) {
      this.setupMotionListeners();
    }
    
    if (this.config.debug) {
      console.log('增强触控交互服务已初始化');
    }
  }
  
  /**
   * 销毁服务
   */
  public destroy(): void {
    // 移除基础手势事件监听
    this.removeBasicGestureListeners();
    
    // 移除设备运动监听
    if (this.deviceMotionListener) {
      window.removeEventListener('devicemotion', this.deviceMotionListener);
      this.deviceMotionListener = null;
    }
    
    // 销毁基础触控交互服务
    this.touchInteractionService.destroy();
    
    if (this.config.debug) {
      console.log('增强触控交互服务已销毁');
    }
  }
  
  /**
   * 设置基础手势事件监听
   */
  private setupBasicGestureListeners(): void {
    // 监听点击事件
    this.touchInteractionService.on(GestureType.TAP, this.handleTapGesture.bind(this));
    
    // 监听双击事件
    this.touchInteractionService.on(GestureType.DOUBLE_TAP, this.handleDoubleTapGesture.bind(this));
    
    // 监听长按事件
    this.touchInteractionService.on(GestureType.LONG_PRESS, this.handleLongPressGesture.bind(this));
    
    // 监听滑动事件
    this.touchInteractionService.on(GestureType.SWIPE, this.handleSwipeGesture.bind(this));
    
    // 监听捏合事件
    this.touchInteractionService.on(GestureType.PINCH, this.handlePinchGesture.bind(this));
    
    // 监听旋转事件
    this.touchInteractionService.on(GestureType.ROTATE, this.handleRotateGesture.bind(this));
    
    // 监听平移事件
    this.touchInteractionService.on(GestureType.PAN, this.handlePanGesture.bind(this));
  }
  
  /**
   * 移除基础手势事件监听
   */
  private removeBasicGestureListeners(): void {
    this.touchInteractionService.off(GestureType.TAP, this.handleTapGesture.bind(this));
    this.touchInteractionService.off(GestureType.DOUBLE_TAP, this.handleDoubleTapGesture.bind(this));
    this.touchInteractionService.off(GestureType.LONG_PRESS, this.handleLongPressGesture.bind(this));
    this.touchInteractionService.off(GestureType.SWIPE, this.handleSwipeGesture.bind(this));
    this.touchInteractionService.off(GestureType.PINCH, this.handlePinchGesture.bind(this));
    this.touchInteractionService.off(GestureType.ROTATE, this.handleRotateGesture.bind(this));
    this.touchInteractionService.off(GestureType.PAN, this.handlePanGesture.bind(this));
  }
  
  /**
   * 设置设备运动监听
   */
  private setupMotionListeners(): void {
    if (typeof window !== 'undefined' && 'DeviceMotionEvent' in window) {
      this.deviceMotionListener = this.handleDeviceMotion.bind(this);
      window.addEventListener('devicemotion', this.deviceMotionListener);
    }
  }
  
  /**
   * 处理点击手势
   * @param data 手势数据
   */
  private handleTapGesture(data: GestureData): void {
    // 转发基础点击事件
    this.emit(EnhancedTouchInteractionEventType.TAP, data);
    
    // 检测多指点击
    if (this.config.enableMultiFingerGestures && data.pointerCount) {
      if (data.pointerCount === 2) {
        this.emit(EnhancedTouchInteractionEventType.TWO_FINGER_TAP, {
          ...data,
          fingers: 2
        } as EnhancedGestureData);
      } else if (data.pointerCount === 3) {
        this.emit(EnhancedTouchInteractionEventType.THREE_FINGER_TAP, {
          ...data,
          fingers: 3
        } as EnhancedGestureData);
      }
    }
    
    // 检测三击
    if (this.config.enableTripleTap && data.state === GestureState.ENDED) {
      const now = Date.now();
      if (now - this.lastTapTime < this.config.tripleTapThreshold!) {
        this.tapCount++;
        if (this.tapCount === 3) {
          this.emit(EnhancedTouchInteractionEventType.TRIPLE_TAP, {
            ...data,
            type: EnhancedGestureType.TRIPLE_TAP
          } as EnhancedGestureData);
          this.tapCount = 0;
        }
      } else {
        this.tapCount = 1;
      }
      this.lastTapTime = now;
    }
    
    // 检测边缘点击
    if (this.config.enableEdgeGestures && data.state === GestureState.ENDED) {
      const edge = this.detectEdge(data.position.x, data.position.y);
      if (edge !== EdgeType.NONE) {
        this.emit(EnhancedTouchInteractionEventType.EDGE_SWIPE, {
          ...data,
          type: EnhancedGestureType.EDGE_SWIPE,
          edge
        } as EnhancedGestureData);
      }
    }
    
    // 记录触摸点
    if (data.state === GestureState.ENDED) {
      this.touchPoints.push({
        x: data.position.x,
        y: data.position.y,
        time: Date.now()
      });
      
      // 限制记录的点数
      if (this.touchPoints.length > 20) {
        this.touchPoints.shift();
      }
    }
  }
  
  /**
   * 处理双击手势
   * @param data 手势数据
   */
  private handleDoubleTapGesture(data: GestureData): void {
    // 转发双击事件
    this.emit(EnhancedTouchInteractionEventType.DOUBLE_TAP, data);
  }
  
  /**
   * 处理长按手势
   * @param data 手势数据
   */
  private handleLongPressGesture(data: GestureData): void {
    // 转发长按事件
    this.emit(EnhancedTouchInteractionEventType.LONG_PRESS, data);
    
    // 检测多指长按
    if (this.config.enableMultiFingerGestures && data.pointerCount === 2) {
      this.emit(EnhancedTouchInteractionEventType.TWO_FINGER_LONG_PRESS, {
        ...data,
        type: EnhancedGestureType.TWO_FINGER_LONG_PRESS,
        fingers: 2
      } as EnhancedGestureData);
    }
    
    // 检测辅助功能手势
    if (this.config.enableAccessibilityGestures && data.pointerCount === 3) {
      this.emit(EnhancedTouchInteractionEventType.ACCESSIBILITY_GESTURE, {
        ...data,
        isAccessibilityGesture: true
      } as EnhancedGestureData);
      
      // 切换高对比度模式
      const config = this.accessibilityService.getConfig();
      this.accessibilityService.configure({
        enableHighContrast: !config.enableHighContrast
      });
    }
  }
  
  /**
   * 处理滑动手势
   * @param data 手势数据
   */
  private handleSwipeGesture(data: GestureData): void {
    // 转发滑动事件
    this.emit(EnhancedTouchInteractionEventType.SWIPE, data);
    
    // 检测边缘滑动
    if (this.config.enableEdgeGestures && data.state === GestureState.ENDED) {
      const edge = this.detectEdge(data.startPosition.x, data.startPosition.y);
      if (edge !== EdgeType.NONE) {
        this.emit(EnhancedTouchInteractionEventType.EDGE_SWIPE, {
          ...data,
          type: EnhancedGestureType.EDGE_SWIPE,
          edge
        } as EnhancedGestureData);
      }
    }
  }
  
  /**
   * 处理捏合手势
   * @param data 手势数据
   */
  private handlePinchGesture(data: GestureData): void {
    // 转发捏合事件
    this.emit(EnhancedTouchInteractionEventType.PINCH, data);
  }
  
  /**
   * 处理旋转手势
   * @param data 手势数据
   */
  private handleRotateGesture(data: GestureData): void {
    // 转发旋转事件
    this.emit(EnhancedTouchInteractionEventType.ROTATE, data);
  }
  
  /**
   * 处理平移手势
   * @param data 手势数据
   */
  private handlePanGesture(data: GestureData): void {
    // 转发平移事件
    this.emit(EnhancedTouchInteractionEventType.PAN, data);
    
    // 检测形状手势
    if (this.config.enableShapeGestures && data.state === GestureState.UPDATED) {
      // 记录点
      this.circlePoints.push({
        x: data.position.x,
        y: data.position.y
      });
      
      // 限制记录的点数
      if (this.circlePoints.length > 50) {
        this.circlePoints.shift();
      }
      
      // 检测圆形手势
      if (this.circlePoints.length > 20) {
        const circleResult = this.detectCircle(this.circlePoints);
        if (circleResult.isCircle) {
          this.emit(EnhancedTouchInteractionEventType.CIRCLE, {
            ...data,
            type: EnhancedGestureType.CIRCLE,
            radius: circleResult.radius,
            circleCompletion: circleResult.completion
          } as EnhancedGestureData);
          
          // 清空点
          this.circlePoints = [];
        }
      }
    }
  }
  
  /**
   * 处理设备运动
   * @param event 设备运动事件
   */
  private handleDeviceMotion(event: DeviceMotionEvent): void {
    if (!this.config.enableMotionGestures || !event.accelerationIncludingGravity) {
      return;
    }
    
    const acceleration = event.accelerationIncludingGravity;
    const x = acceleration.x || 0;
    const y = acceleration.y || 0;
    const z = acceleration.z || 0;
    
    // 计算加速度大小
    const magnitude = Math.sqrt(x * x + y * y + z * z);
    
    // 检测震动手势
    const now = Date.now();
    if (magnitude > this.config.shakeThreshold!) {
      if (now - this.lastShakeTime > 200) {
        this.shakeCount++;
        this.lastShakeTime = now;
        
        if (this.shakeCount >= 3 && now - this.lastShakeTime < this.config.shakeInterval!) {
          this.emit(EnhancedTouchInteractionEventType.SHAKE, {
            type: EnhancedGestureType.SHAKE,
            state: GestureState.ENDED,
            position: { x: 0, y: 0 },
            startPosition: { x: 0, y: 0 },
            shakeIntensity: magnitude
          } as EnhancedGestureData);
          
          this.shakeCount = 0;
        }
      }
    }
    
    // 重置震动计数
    if (now - this.lastShakeTime > this.config.shakeInterval!) {
      this.shakeCount = 0;
    }
  }
  
  /**
   * 检测边缘
   * @param x X坐标
   * @param y Y坐标
   * @returns 边缘类型
   */
  private detectEdge(x: number, y: number): EdgeType {
    if (!this.targetElement) {
      return EdgeType.NONE;
    }
    
    const rect = this.targetElement.getBoundingClientRect();
    const edgeSize = this.config.edgeSize!;
    
    if (x - rect.left < edgeSize) {
      return EdgeType.LEFT;
    } else if (rect.right - x < edgeSize) {
      return EdgeType.RIGHT;
    } else if (y - rect.top < edgeSize) {
      return EdgeType.TOP;
    } else if (rect.bottom - y < edgeSize) {
      return EdgeType.BOTTOM;
    }
    
    return EdgeType.NONE;
  }
  
  /**
   * 检测圆形
   * @param points 点集合
   * @returns 圆形检测结果
   */
  private detectCircle(points: Array<{x: number, y: number}>): {isCircle: boolean, radius: number, completion: number} {
    if (points.length < 10) {
      return { isCircle: false, radius: 0, completion: 0 };
    }
    
    // 计算中心点
    let sumX = 0;
    let sumY = 0;
    for (const point of points) {
      sumX += point.x;
      sumY += point.y;
    }
    const centerX = sumX / points.length;
    const centerY = sumY / points.length;
    
    // 计算平均半径
    let sumRadius = 0;
    for (const point of points) {
      const dx = point.x - centerX;
      const dy = point.y - centerY;
      sumRadius += Math.sqrt(dx * dx + dy * dy);
    }
    const avgRadius = sumRadius / points.length;
    
    // 计算标准差
    let sumVariance = 0;
    for (const point of points) {
      const dx = point.x - centerX;
      const dy = point.y - centerY;
      const radius = Math.sqrt(dx * dx + dy * dy);
      sumVariance += Math.pow(radius - avgRadius, 2);
    }
    const stdDev = Math.sqrt(sumVariance / points.length);
    
    // 计算圆形完成度
    const firstPoint = points[0];
    const lastPoint = points[points.length - 1];
    const dx = firstPoint.x - lastPoint.x;
    const dy = firstPoint.y - lastPoint.y;
    const distance = Math.sqrt(dx * dx + dy * dy);
    const completion = 1 - Math.min(1, distance / (2 * avgRadius));
    
    // 判断是否为圆形
    const isCircle = stdDev / avgRadius < 0.2 && completion > this.config.circleThreshold!;
    
    return {
      isCircle,
      radius: avgRadius,
      completion
    };
  }
}

export default EnhancedTouchInteractionService.getInstance();
