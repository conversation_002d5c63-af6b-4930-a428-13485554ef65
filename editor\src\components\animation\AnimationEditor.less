/**
 * 动画编辑器样式
 */

// 动画编辑器面板
.animation-editor-panel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .animation-editor-header {
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e8e8e8;

    .header-title {
      font-size: 16px;
      font-weight: bold;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .animation-editor-content {
    flex: 1;
    display: flex;
    overflow: hidden;

    .animation-editor-sider {
      background: #f5f5f5;
      border-right: 1px solid #e8e8e8;
    }

    .animation-editor-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
  }

  .animation-editor-tab-header {
    padding: 8px;
    border-bottom: 1px solid #e8e8e8;
  }

  .animation-editor-tab-content {
    padding: 8px;
    overflow-y: auto;
    max-height: 300px;
  }

  .animation-editor-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    margin-bottom: 8px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background-color: #f0f0f0;
    }

    &.selected {
      background-color: #e6f7ff;
      border-color: #1890ff;
    }

    .animation-editor-item-title {
      font-weight: bold;
    }

    .animation-editor-item-controls {
      display: flex;
      gap: 4px;
    }
  }

  .animation-editor-properties {
    padding: 16px;
    overflow-y: auto;
  }
}

// 遮罩编辑器
.mask-editor {
  .add-bone-form {
    margin-bottom: 16px;
  }

  .mask-presets {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .ant-card {
      margin-bottom: 8px;
    }
  }

  .bone-tools {
    margin-bottom: 16px;
  }

  .bone-list {
    margin-bottom: 16px;

    .ant-tree {
      background: #f5f5f5;
      border-radius: 4px;
      padding: 8px;
    }
  }

  .bone-stats {
    background: #f5f5f5;
    border-radius: 4px;
    padding: 16px;
    margin-top: 16px;
  }

  .preview-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 16px;

    canvas {
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .preview-controls {
      display: flex;
      justify-content: center;
      gap: 8px;
    }
  }

  .dynamic-settings {
    background: #f5f5f5;
    border-radius: 4px;
    padding: 16px;
    margin-top: 16px;
  }

  .custom-presets {
    margin-top: 16px;

    .ant-card {
      margin-bottom: 8px;
    }
  }

  .preview-settings {
    background: #f5f5f5;
    border-radius: 4px;
    padding: 16px;
    margin-top: 16px;
  }

  // 暗色主题适配
  .dark-theme & {
    .bone-list .ant-tree,
    .bone-stats,
    .dynamic-settings,
    .preview-settings {
      background: #1e1e1e;
    }

    .preview-container canvas {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
    }
  }
}

// 混合模式编辑器
.blend-mode-editor {
  .blend-mode-form {
    margin-bottom: 16px;
  }

  .blend-mode-preview {
    margin-top: 16px;
    padding: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #f5f5f5;
  }
}

// 子片段编辑器
.sub-clip-editor {
  .time-range-slider {
    margin: 16px 0;
  }

  .sub-clip-preview {
    margin-top: 16px;
    padding: 16px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #f5f5f5;
  }
}

// 动画时间线
.animation-timeline {
  height: 200px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-top: 16px;
  position: relative;

  .timeline-ruler {
    height: 24px;
    border-bottom: 1px solid #e8e8e8;
    position: relative;
  }

  .timeline-content {
    height: calc(100% - 24px);
    position: relative;
    overflow-y: auto;
  }

  .timeline-track {
    height: 32px;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
  }

  .timeline-keyframe {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: #1890ff;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    top: 50%;
    cursor: pointer;

    &:hover {
      background-color: #40a9ff;
    }

    &.selected {
      background-color: #ff4d4f;
    }
  }

  .timeline-playhead {
    position: absolute;
    top: 0;
    width: 2px;
    height: 100%;
    background-color: #ff4d4f;
    z-index: 10;
  }
}

// 动画预览
.animation-preview {
  height: 300px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-top: 16px;
  position: relative;

  .preview-canvas {
    width: 100%;
    height: 100%;
  }

  .preview-controls {
    position: absolute;
    bottom: 16px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 8px;
    border-radius: 4px;
  }
}

// 混合曲线编辑器
.blend-curve-editor {
  .curve-preview {
    margin: 16px 0;
    display: flex;
    flex-direction: column;
    align-items: center;

    canvas {
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background-color: #fafafa;
      margin-bottom: 16px;
    }

    .test-controls {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;
    }
  }

  .bezier-editor {
    .bezier-canvas-container {
      position: relative;
      margin-bottom: 16px;

      canvas {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background-color: #fafafa;
      }

      .bezier-instructions {
        position: absolute;
        bottom: 8px;
        left: 8px;
        background-color: rgba(255, 255, 255, 0.8);
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
      }
    }
  }
}

// 重定向编辑器
.retargeting-editor {
  .bone-mapping-tools {
    margin-bottom: 16px;
  }

  .add-mapping-form {
    margin-bottom: 16px;
  }

  .retargeting-options {
    .option-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .preview-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 16px;

    canvas {
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .preview-controls {
      display: flex;
      justify-content: center;
      gap: 8px;
    }
  }
}

// 重定向编辑器面板
.retargeting-editor-panel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .retargeting-editor-header {
    padding: 8px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e8e8e8;
    background-color: #f5f5f5;

    .retargeting-editor-title {
      font-size: 16px;
      font-weight: bold;
    }

    .retargeting-editor-controls {
      display: flex;
      gap: 8px;
    }
  }

  .retargeting-editor-content {
    flex: 1;
    display: flex;
    overflow: hidden;
    padding: 16px;

    .retargeting-editor-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .retargeting-editor-controls {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .model-controls {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;

          .control-group {
            flex: 1;
            min-width: 200px;

            .control-label {
              font-weight: bold;
              margin-bottom: 8px;
            }
          }
        }

        .preview-view {
          flex: 1;
          min-height: 300px;
          border: 1px solid #e8e8e8;
          border-radius: 4px;
          margin: 16px 0;
          overflow: hidden;
        }

        .mapping-editor {
          flex: 1;
          min-height: 300px;
          border: 1px solid #e8e8e8;
          border-radius: 4px;
          padding: 16px;
          overflow: auto;
        }
      }
    }
  }

  // 暗色主题适配
  .dark-theme & {
    .retargeting-editor-header {
      background-color: #1e1e1e;
      border-bottom-color: #303030;
    }

    .preview-view,
    .mapping-editor {
      border-color: #303030;
    }

    .retargeting-options .option-item {
      border-bottom-color: #303030;
    }
  }
}
