/**
 * 高级口型同步分析器
 * 提供更高级的音频分析算法，用于提高口型识别的准确性
 */

import { VisemeType } from '../FacialAnimation';
import { LipSyncAIPredictor } from '../ai/LipSyncAIPredictor';

/**
 * 高级分析器配置
 */
export interface AdvancedAnalyzerConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** FFT大小 */
  fftSize?: number;
  /** 采样频率 */
  sampleRate?: number;
  /** 音量阈值 */
  volumeThreshold?: number;
  /** 平滑因子 */
  smoothingFactor?: number;
  /** 是否使用MFCC分析 */
  useMFCC?: boolean;
  /** 是否使用LPC分析 */
  useLPC?: boolean;
  /** 是否使用上下文预测 */
  useContextPrediction?: boolean;
  /** 上下文窗口大小 */
  contextWindowSize?: number;
  /** 频率带数量 */
  numFrequencyBands?: number;
  /** 梅尔滤波器数量 */
  numMelFilters?: number;
  /** 倒谱系数数量 */
  numCepstralCoeffs?: number;
  /** 是否使用AI预测 */
  useAIPrediction?: boolean;
  /** 是否使用频谱图分析 */
  useSpectrogram?: boolean;
  /** 是否使用音素识别 */
  usePhonemeRecognition?: boolean;
  /** 是否使用语音模式识别 */
  useSpeechPatternRecognition?: boolean;
  /** 是否使用在线学习 */
  useOnlineLearning?: boolean;
}

/**
 * 高级口型同步分析器
 */
export class LipSyncAdvancedAnalyzer {
  /** 配置 */
  private config: AdvancedAnalyzerConfig;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: AdvancedAnalyzerConfig = {
    debug: false,
    fftSize: 1024,
    sampleRate: 44100,
    volumeThreshold: 0.01,
    smoothingFactor: 0.5,
    useMFCC: true,
    useLPC: false,
    useContextPrediction: true,
    contextWindowSize: 5,
    numFrequencyBands: 15,
    numMelFilters: 26,
    numCepstralCoeffs: 13,
    useAIPrediction: false,
    useSpectrogram: false,
    usePhonemeRecognition: false,
    useSpeechPatternRecognition: false,
    useOnlineLearning: false
  };

  /** 频率带边界 */
  private frequencyBandBoundaries: number[] = [];

  /** 频率带能量 */
  private frequencyBands: Float32Array | null = null;

  /** 梅尔滤波器组 */
  private melFilterBank: Float32Array[] = [];

  /** 上下文历史 */
  private visemeHistory: VisemeType[] = [];

  /** 音素到口型映射 */
  private phonemeToVisemeMap: Map<string, VisemeType> = new Map();

  /** 口型转换矩阵 - 用于上下文预测 */
  private visemeTransitionMatrix: Map<string, Map<VisemeType, number>> = new Map();

  /** AI预测器 */
  private aiPredictor: LipSyncAIPredictor | null = null;

  /** 频谱图历史 */
  private spectrogramHistory: Float32Array[] = [];

  /** 音素识别结果历史 */
  private phonemeHistory: string[] = [];

  /** 语音模式识别器 */
  private speechPatternRecognizer: any = null;

  /** 在线学习数据 */
  private onlineLearningData: { features: Float32Array, viseme: VisemeType }[] = [];

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config?: Partial<AdvancedAnalyzerConfig>) {
    this.config = { ...LipSyncAdvancedAnalyzer.DEFAULT_CONFIG, ...config };
    this.initialize();
  }

  /**
   * 初始化分析器
   */
  private async initialize(): Promise<void> {
    // 初始化频率带边界
    this.initFrequencyBands();

    // 初始化梅尔滤波器组
    if (this.config.useMFCC) {
      this.initMelFilterBank();
    }

    // 初始化音素到口型映射
    this.initPhonemeToVisemeMap();

    // 初始化口型转换矩阵
    this.initVisemeTransitionMatrix();

    // 初始化AI预测器
    if (this.config.useAIPrediction) {
      await this.initAIPredictor();
    }

    // 初始化频谱图分析
    if (this.config.useSpectrogram) {
      this.initSpectrogramAnalysis();
    }

    // 初始化音素识别
    if (this.config.usePhonemeRecognition) {
      this.initPhonemeRecognition();
    }

    // 初始化语音模式识别
    if (this.config.useSpeechPatternRecognition) {
      this.initSpeechPatternRecognition();
    }
  }

  /**
   * 初始化AI预测器
   */
  private async initAIPredictor(): Promise<void> {
    try {
      this.aiPredictor = new LipSyncAIPredictor({
        debug: this.config.debug,
        useLocalModel: true,
        useMFCC: this.config.useMFCC,
        useSpectrogram: this.config.useSpectrogram,
        useContext: this.config.useContextPrediction,
        useOnlineLearning: this.config.useOnlineLearning,
        contextWindowSize: this.config.contextWindowSize
      });

      // 初始化AI预测器
      const success = await this.aiPredictor.initialize();

      if (!success && this.config.debug) {
        console.warn('AI预测器初始化失败，将使用传统分析方法');
      }
    } catch (error) {
      this.aiPredictor = null;
      if (this.config.debug) {
        console.error('初始化AI预测器时出错:', error);
      }
    }
  }

  /**
   * 初始化频谱图分析
   */
  private initSpectrogramAnalysis(): void {
    // 初始化频谱图历史数组
    this.spectrogramHistory = [];
  }

  /**
   * 初始化音素识别
   */
  private initPhonemeRecognition(): void {
    // 初始化音素历史数组
    this.phonemeHistory = [];
    // 实际应用中，这里可以加载音素识别模型
  }

  /**
   * 初始化语音模式识别
   */
  private initSpeechPatternRecognition(): void {
    // 初始化语音模式识别器
    // 实际应用中，这里可以加载语音模式识别模型
    this.speechPatternRecognizer = {
      recognize: (features: Float32Array) => {
        // 简单的模拟实现
        return 'speech';
      }
    };
  }

  /**
   * 初始化频率带边界
   */
  private initFrequencyBands(): void {
    // 使用更精细的频率带划分，更好地匹配人类语音特征
    // 人类语音主要集中在80Hz-8000Hz范围内
    const minFreq = 80;
    const maxFreq = 8000;
    const numBands = this.config.numFrequencyBands!;

    // 使用梅尔刻度进行频率带划分，更符合人耳感知
    this.frequencyBandBoundaries = new Array(numBands + 1);

    for (let i = 0; i <= numBands; i++) {
      // 将梅尔刻度均匀划分，然后转换回Hz
      const melMin = this.hzToMel(minFreq);
      const melMax = this.hzToMel(maxFreq);
      const melStep = (melMax - melMin) / numBands;
      const melFreq = melMin + i * melStep;

      this.frequencyBandBoundaries[i] = this.melToHz(melFreq);
    }

    // 创建频率带能量数组
    this.frequencyBands = new Float32Array(numBands);
  }

  /**
   * 初始化梅尔滤波器组
   */
  private initMelFilterBank(): void {
    const numFilters = this.config.numMelFilters!;
    const fftSize = this.config.fftSize!;
    const sampleRate = this.config.sampleRate!;

    // 计算梅尔刻度范围
    const lowFreq = 80;
    const highFreq = sampleRate / 2;
    const lowMel = this.hzToMel(lowFreq);
    const highMel = this.hzToMel(highFreq);

    // 创建梅尔滤波器组
    this.melFilterBank = new Array(numFilters);

    // 在梅尔刻度上均匀分布滤波器中心点
    const melPoints = new Array(numFilters + 2);
    for (let i = 0; i < melPoints.length; i++) {
      melPoints[i] = lowMel + (highMel - lowMel) / (numFilters + 1) * i;
    }

    // 转换回Hz
    const fftFreqs = new Array(melPoints.length);
    for (let i = 0; i < melPoints.length; i++) {
      fftFreqs[i] = this.melToHz(melPoints[i]);
    }

    // 计算滤波器组系数
    const binFreqs = new Array(fftSize / 2 + 1);
    for (let i = 0; i < binFreqs.length; i++) {
      binFreqs[i] = i * sampleRate / fftSize;
    }

    // 创建每个滤波器
    for (let i = 0; i < numFilters; i++) {
      this.melFilterBank[i] = new Float32Array(fftSize / 2 + 1);

      for (let j = 0; j < binFreqs.length; j++) {
        const freq = binFreqs[j];

        // 三角滤波器
        if (freq < fftFreqs[i] || freq > fftFreqs[i + 2]) {
          this.melFilterBank[i][j] = 0;
        } else if (freq <= fftFreqs[i + 1]) {
          this.melFilterBank[i][j] = (freq - fftFreqs[i]) / (fftFreqs[i + 1] - fftFreqs[i]);
        } else {
          this.melFilterBank[i][j] = (fftFreqs[i + 2] - freq) / (fftFreqs[i + 2] - fftFreqs[i + 1]);
        }
      }
    }
  }

  /**
   * 初始化音素到口型映射
   */
  private initPhonemeToVisemeMap(): void {
    // 英语音素到口型的映射
    // 这是一个更详细的映射，基于语音学研究
    this.phonemeToVisemeMap.set('p', VisemeType.PP);
    this.phonemeToVisemeMap.set('b', VisemeType.PP);
    this.phonemeToVisemeMap.set('m', VisemeType.PP);

    this.phonemeToVisemeMap.set('f', VisemeType.FF);
    this.phonemeToVisemeMap.set('v', VisemeType.FF);

    this.phonemeToVisemeMap.set('th', VisemeType.TH);
    this.phonemeToVisemeMap.set('dh', VisemeType.TH);

    this.phonemeToVisemeMap.set('t', VisemeType.DD);
    this.phonemeToVisemeMap.set('d', VisemeType.DD);
    this.phonemeToVisemeMap.set('n', VisemeType.DD);

    this.phonemeToVisemeMap.set('k', VisemeType.KK);
    this.phonemeToVisemeMap.set('g', VisemeType.KK);
    this.phonemeToVisemeMap.set('ng', VisemeType.KK);

    this.phonemeToVisemeMap.set('ch', VisemeType.CH);
    this.phonemeToVisemeMap.set('j', VisemeType.CH);
    this.phonemeToVisemeMap.set('sh', VisemeType.CH);
    this.phonemeToVisemeMap.set('zh', VisemeType.CH);

    this.phonemeToVisemeMap.set('s', VisemeType.SS);
    this.phonemeToVisemeMap.set('z', VisemeType.SS);

    this.phonemeToVisemeMap.set('l', VisemeType.NN);
    this.phonemeToVisemeMap.set('n', VisemeType.NN);

    this.phonemeToVisemeMap.set('r', VisemeType.RR);

    this.phonemeToVisemeMap.set('a', VisemeType.AA);
    this.phonemeToVisemeMap.set('ae', VisemeType.AA);

    this.phonemeToVisemeMap.set('e', VisemeType.EE);
    this.phonemeToVisemeMap.set('eh', VisemeType.EE);

    this.phonemeToVisemeMap.set('i', VisemeType.IH);
    this.phonemeToVisemeMap.set('ih', VisemeType.IH);
    this.phonemeToVisemeMap.set('y', VisemeType.IH);

    this.phonemeToVisemeMap.set('o', VisemeType.OH);
    this.phonemeToVisemeMap.set('aw', VisemeType.OH);

    this.phonemeToVisemeMap.set('u', VisemeType.OU);
    this.phonemeToVisemeMap.set('uw', VisemeType.OU);
    this.phonemeToVisemeMap.set('w', VisemeType.OU);
  }

  /**
   * 初始化口型转换矩阵
   */
  private initVisemeTransitionMatrix(): void {
    // 为每个口型创建转换概率
    for (const viseme in VisemeType) {
      const sourceViseme = VisemeType[viseme as keyof typeof VisemeType];
      const transitionMap = new Map<VisemeType, number>();

      // 设置默认转换概率
      for (const targetViseme in VisemeType) {
        transitionMap.set(
          VisemeType[targetViseme as keyof typeof VisemeType],
          0.05 // 默认低概率
        );
      }

      // 设置特定转换概率（基于语音学研究和观察）
      // 这些值可以通过机器学习进一步优化
      if (sourceViseme === VisemeType.PP) {
        transitionMap.set(VisemeType.AA, 0.3);
        transitionMap.set(VisemeType.EE, 0.2);
        transitionMap.set(VisemeType.PP, 0.1); // 自我转换概率
      } else if (sourceViseme === VisemeType.AA) {
        transitionMap.set(VisemeType.SS, 0.25);
        transitionMap.set(VisemeType.DD, 0.2);
        transitionMap.set(VisemeType.AA, 0.15); // 自我转换概率
      }
      // 可以添加更多特定转换概率...

      this.visemeTransitionMatrix.set(sourceViseme, transitionMap);
    }
  }

  /**
   * 分析音频数据
   * @param spectrum 频谱数据
   * @returns 口型类型
   */
  public analyzeAudio(spectrum: Float32Array): VisemeType {
    // 计算RMS
    const rms = this.calculateRMS(spectrum);

    // 如果音量太小，则设置为静默
    if (rms < this.config.volumeThreshold!) {
      const silentViseme = VisemeType.SILENT;
      this.updateVisemeHistory(silentViseme);
      return silentViseme;
    }

    // 更新频谱图历史
    if (this.config.useSpectrogram) {
      this.updateSpectrogramHistory(spectrum);
    }

    // 使用AI预测
    if (this.config.useAIPrediction && this.aiPredictor) {
      try {
        const prediction = this.aiPredictor.predict(spectrum);

        // 如果置信度足够高，使用AI预测结果
        if (prediction.confidence > 0.7) {
          const viseme = prediction.viseme;
          this.updateVisemeHistory(viseme);

          // 如果启用在线学习，收集训练数据
          if (this.config.useOnlineLearning) {
            this.collectTrainingData(spectrum, viseme);
          }

          return viseme;
        }
      } catch (error) {
        if (this.config.debug) {
          console.warn('AI预测失败，回退到传统方法:', error);
        }
      }
    }

    // 使用音素识别
    if (this.config.usePhonemeRecognition) {
      const phoneme = this.recognizePhoneme(spectrum);
      if (phoneme && this.phonemeToVisemeMap.has(phoneme)) {
        const viseme = this.phonemeToVisemeMap.get(phoneme)!;
        this.updateVisemeHistory(viseme);
        return viseme;
      }
    }

    // 使用语音模式识别
    if (this.config.useSpeechPatternRecognition && this.speechPatternRecognizer) {
      const pattern = this.speechPatternRecognizer.recognize(spectrum);
      if (pattern === 'silence') {
        return VisemeType.SILENT;
      }
    }

    // 根据配置选择传统分析方法
    let viseme: VisemeType;

    if (this.config.useMFCC) {
      // 使用MFCC分析
      viseme = this.analyzeMFCC(spectrum);
    } else if (this.config.useLPC) {
      // 使用LPC分析
      viseme = this.analyzeLPC(spectrum);
    } else {
      // 使用频率带分析
      this.calculateFrequencyBands(spectrum);
      viseme = this.analyzeFrequencyBands();
    }

    // 使用上下文预测改进结果
    if (this.config.useContextPrediction && this.visemeHistory.length > 0) {
      viseme = this.predictVisemeFromContext(viseme);
    }

    // 更新历史
    this.updateVisemeHistory(viseme);

    // 如果启用在线学习，收集训练数据
    if (this.config.useOnlineLearning) {
      this.collectTrainingData(spectrum, viseme);
    }

    return viseme;
  }

  /**
   * 更新频谱图历史
   * @param spectrum 频谱数据
   */
  private updateSpectrogramHistory(spectrum: Float32Array): void {
    // 添加到历史
    this.spectrogramHistory.push(new Float32Array(spectrum));

    // 限制历史大小
    if (this.spectrogramHistory.length > this.config.contextWindowSize! * 2) {
      this.spectrogramHistory.shift();
    }
  }

  /**
   * 识别音素
   * @param spectrum 频谱数据
   * @returns 识别的音素
   */
  private recognizePhoneme(spectrum: Float32Array): string | null {
    // 这里是简化的音素识别实现
    // 实际应用中，应该使用更复杂的音素识别算法或模型

    // 使用MFCC特征进行简单分类
    const mfcc = this.extractMFCC(spectrum);

    // 基于MFCC特征的简单规则
    const c1 = mfcc[1]; // 第二个系数，通常与开口度相关
    const c2 = mfcc[2]; // 第三个系数，通常与前后位置相关

    // 简单的规则分类
    if (c1 < -10) {
      return 'p'; // 闭合音素
    } else if (c1 < -5) {
      return 'f'; // 摩擦音素
    } else if (c1 < 0) {
      if (c2 > 0) {
        return 'th'; // 舌齿音素
      } else {
        return 'd'; // 舌尖音素
      }
    } else if (c1 < 5) {
      if (c2 > 0) {
        return 'k'; // 舌根音素
      } else {
        return 'ch'; // 齿擦音素
      }
    } else if (c1 < 10) {
      if (c2 > 0) {
        return 'e'; // 前元音
      } else {
        return 'i'; // 高元音
      }
    } else {
      return 'a'; // 开口元音
    }
  }

  /**
   * 提取MFCC特征
   * @param spectrum 频谱数据
   * @returns MFCC特征
   */
  private extractMFCC(spectrum: Float32Array): Float32Array {
    // 计算梅尔滤波器组输出
    const melEnergies = new Float32Array(this.config.numMelFilters!);

    for (let i = 0; i < this.config.numMelFilters!; i++) {
      let sum = 0;
      for (let j = 0; j < spectrum.length; j++) {
        // 将dB转换为功率谱
        const power = Math.pow(10, spectrum[j] / 10);
        sum += power * this.melFilterBank[i][j];
      }
      // 取对数
      melEnergies[i] = sum > 0 ? Math.log(sum) : 0;
    }

    // 计算DCT（离散余弦变换）得到MFCC
    const mfcc = new Float32Array(this.config.numCepstralCoeffs!);

    for (let i = 0; i < this.config.numCepstralCoeffs!; i++) {
      let sum = 0;
      for (let j = 0; j < this.config.numMelFilters!; j++) {
        sum += melEnergies[j] * Math.cos(Math.PI * i * (j + 0.5) / this.config.numMelFilters!);
      }
      mfcc[i] = sum;
    }

    return mfcc;
  }

  /**
   * 收集训练数据
   * @param spectrum 频谱数据
   * @param viseme 口型
   */
  private collectTrainingData(spectrum: Float32Array, viseme: VisemeType): void {
    // 添加到训练数据
    this.onlineLearningData.push({
      features: new Float32Array(spectrum),
      viseme
    });

    // 限制训练数据大小
    if (this.onlineLearningData.length > 1000) {
      this.onlineLearningData.shift();
    }

    // 如果收集了足够的数据，可以触发在线学习
    if (this.onlineLearningData.length % 100 === 0 && this.aiPredictor) {
      // 实际应用中，这里应该调用AI预测器的在线学习方法
      if (this.config.debug) {
        console.log(`收集了${this.onlineLearningData.length}条训练数据，可以进行在线学习`);
      }
    }
  }

  /**
   * 计算RMS（均方根）
   * @param spectrum 频谱数据
   * @returns RMS值
   */
  private calculateRMS(spectrum: Float32Array): number {
    let sum = 0;
    for (let i = 0; i < spectrum.length; i++) {
      // 将dB转换为线性幅度
      const amplitude = Math.pow(10, spectrum[i] / 20);
      sum += amplitude * amplitude;
    }
    return Math.sqrt(sum / spectrum.length);
  }

  /**
   * 计算频率带能量
   * @param spectrum 频谱数据
   */
  private calculateFrequencyBands(spectrum: Float32Array): void {
    const binCount = spectrum.length;
    const binWidth = this.config.sampleRate! / (binCount * 2);

    // 重置频率带
    for (let i = 0; i < this.frequencyBands!.length; i++) {
      this.frequencyBands![i] = 0;
    }

    // 计算每个频率带的能量
    for (let i = 0; i < binCount; i++) {
      const frequency = i * binWidth;

      // 找到对应的频率带
      for (let j = 0; j < this.frequencyBandBoundaries.length - 1; j++) {
        if (frequency >= this.frequencyBandBoundaries[j] &&
            frequency < this.frequencyBandBoundaries[j + 1]) {
          // 转换dB到线性刻度并累加
          this.frequencyBands![j] += Math.pow(10, spectrum[i] / 20);
          break;
        }
      }
    }
  }

  /**
   * 分析频率带并确定口型
   * @returns 口型类型
   */
  private analyzeFrequencyBands(): VisemeType {
    if (!this.frequencyBands || this.frequencyBands.length === 0) {
      return VisemeType.SILENT;
    }

    // 找出能量最大的频率带
    let maxEnergy = 0;
    let maxBandIndex = -1;

    for (let i = 0; i < this.frequencyBands.length; i++) {
      if (this.frequencyBands[i] > maxEnergy) {
        maxEnergy = this.frequencyBands[i];
        maxBandIndex = i;
      }
    }

    // 根据主导频率带确定口型
    if (maxBandIndex === -1) {
      return VisemeType.SILENT;
    }

    // 更精细的频率带到口型映射
    // 这个映射基于语音学研究，可以根据实际效果进行调整
    const bandToViseme: VisemeType[] = [
      VisemeType.PP,  // 80-100Hz: 闭合口型，如 p, b, m
      VisemeType.PP,  // 100-200Hz: 闭合口型，如 p, b, m
      VisemeType.FF,  // 200-300Hz: 半开口型，如 f, v
      VisemeType.FF,  // 300-400Hz: 半开口型，如 f, v
      VisemeType.TH,  // 400-500Hz: 舌齿音，如 th
      VisemeType.DD,  // 500-700Hz: 舌尖音，如 d, t
      VisemeType.DD,  // 700-900Hz: 舌尖音，如 d, t
      VisemeType.KK,  // 900-1200Hz: 舌根音，如 k, g
      VisemeType.CH,  // 1200-1500Hz: 齿擦音，如 ch, j
      VisemeType.SS,  // 1500-2000Hz: 擦音，如 s, z
      VisemeType.NN,  // 2000-2500Hz: 鼻音和边音，如 n, l
      VisemeType.RR,  // 2500-3000Hz: 卷舌音，如 r
      VisemeType.AA,  // 3000-4000Hz: 开口元音，如 a
      VisemeType.EE,  // 4000-5000Hz: 前元音，如 e
      VisemeType.IH   // 5000-8000Hz: 高元音，如 i
    ];

    // 确保索引在范围内
    if (maxBandIndex < bandToViseme.length) {
      return bandToViseme[maxBandIndex];
    } else {
      return VisemeType.SILENT; // 默认
    }
  }

  /**
   * 使用MFCC分析音频
   * @param spectrum 频谱数据
   * @returns 口型类型
   */
  private analyzeMFCC(spectrum: Float32Array): VisemeType {
    // 计算梅尔滤波器组输出
    const melEnergies = new Float32Array(this.config.numMelFilters!);

    for (let i = 0; i < this.config.numMelFilters!; i++) {
      let sum = 0;
      for (let j = 0; j < spectrum.length; j++) {
        // 将dB转换为功率谱
        const power = Math.pow(10, spectrum[j] / 10);
        sum += power * this.melFilterBank[i][j];
      }
      // 取对数
      melEnergies[i] = sum > 0 ? Math.log(sum) : 0;
    }

    // 计算DCT（离散余弦变换）得到MFCC
    const mfcc = new Float32Array(this.config.numCepstralCoeffs!);

    for (let i = 0; i < this.config.numCepstralCoeffs!; i++) {
      let sum = 0;
      for (let j = 0; j < this.config.numMelFilters!; j++) {
        sum += melEnergies[j] * Math.cos(Math.PI * i * (j + 0.5) / this.config.numMelFilters!);
      }
      mfcc[i] = sum;
    }

    // 基于MFCC特征确定口型
    // 这里使用简化的方法，实际应用中可以使用机器学习模型
    return this.mapMFCCToViseme(mfcc);
  }

  /**
   * 将MFCC映射到口型
   * @param mfcc MFCC特征
   * @returns 口型类型
   */
  private mapMFCCToViseme(mfcc: Float32Array): VisemeType {
    // 简化的映射方法
    // 实际应用中，这里可以使用预训练的分类器

    // 使用前几个系数的特征来区分主要口型
    const c1 = mfcc[1]; // 第二个系数，通常与开口度相关
    const c2 = mfcc[2]; // 第三个系数，通常与前后位置相关

    // 基于MFCC特征的简单规则
    if (c1 < -10) {
      return VisemeType.PP; // 闭合口型
    } else if (c1 < -5) {
      return VisemeType.FF; // 半开口型
    } else if (c1 < 0) {
      if (c2 > 0) {
        return VisemeType.TH; // 舌齿音
      } else {
        return VisemeType.DD; // 舌尖音
      }
    } else if (c1 < 5) {
      if (c2 > 0) {
        return VisemeType.KK; // 舌根音
      } else {
        return VisemeType.CH; // 齿擦音
      }
    } else if (c1 < 10) {
      if (c2 > 0) {
        return VisemeType.EE; // 前元音
      } else {
        return VisemeType.IH; // 高元音
      }
    } else {
      return VisemeType.AA; // 开口元音
    }
  }

  /**
   * 使用LPC分析音频
   * @param spectrum 频谱数据
   * @returns 口型类型
   */
  private analyzeLPC(spectrum: Float32Array): VisemeType {
    // LPC分析需要时域信号
    // 这里简化处理，实际应用中需要从频谱转换回时域或直接使用时域信号

    // 简单返回基于频率带的结果
    this.calculateFrequencyBands(spectrum);
    return this.analyzeFrequencyBands();
  }

  /**
   * 基于上下文预测口型
   * @param currentViseme 当前口型
   * @returns 预测的口型
   */
  private predictVisemeFromContext(currentViseme: VisemeType): VisemeType {
    if (this.visemeHistory.length === 0) {
      return currentViseme;
    }

    // 获取上一个口型
    const previousViseme = this.visemeHistory[this.visemeHistory.length - 1];

    // 获取转换概率
    const transitionMap = this.visemeTransitionMatrix.get(previousViseme);
    if (!transitionMap) {
      return currentViseme;
    }

    // 获取当前口型的转换概率
    const transitionProb = transitionMap.get(currentViseme) || 0;

    // 如果转换概率太低，可能是噪声，保持上一个口型
    if (transitionProb < 0.1) {
      // 检查是否有更可能的转换
      let maxProb = 0;
      let bestViseme = currentViseme;

      for (const [viseme, prob] of transitionMap.entries()) {
        if (prob > maxProb) {
          maxProb = prob;
          bestViseme = viseme;
        }
      }

      // 如果有更可能的转换，使用它
      if (maxProb > 0.2) {
        return bestViseme;
      }
    }

    return currentViseme;
  }

  /**
   * 更新口型历史
   * @param viseme 口型
   */
  private updateVisemeHistory(viseme: VisemeType): void {
    // 添加到历史
    this.visemeHistory.push(viseme);

    // 限制历史大小
    if (this.visemeHistory.length > this.config.contextWindowSize!) {
      this.visemeHistory.shift();
    }
  }

  /**
   * 将Hz转换为梅尔刻度
   * @param hz 频率(Hz)
   * @returns 梅尔刻度值
   */
  private hzToMel(hz: number): number {
    return 2595 * Math.log10(1 + hz / 700);
  }

  /**
   * 将梅尔刻度转换为Hz
   * @param mel 梅尔刻度值
   * @returns 频率(Hz)
   */
  private melToHz(mel: number): number {
    return 700 * (Math.pow(10, mel / 2595) - 1);
  }
}
