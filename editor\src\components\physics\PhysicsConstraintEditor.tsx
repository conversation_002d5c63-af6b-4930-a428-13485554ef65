/**
 * 物理约束编辑器组件
 * 用于编辑物理约束属性
 */
import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Switch, InputNumber, Button, Collapse, Tooltip, Space, Divider } from 'antd';
import { InfoCircleOutlined, DeleteOutlined, LinkOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { updateEntity } from '../../store/entities/entitiesSlice';
import { ConstraintType } from '../../../../engine/src/physics/constraints/PhysicsConstraint';
import { Vector3Input } from '../common/Vector3Input';
import { EntitySelector } from '../common/EntitySelector';

const { Option } = Select;
const { Panel } = Collapse;

interface PhysicsConstraintEditorProps {
  entityId: string;
}

/**
 * 物理约束编辑器组件
 */
const PhysicsConstraintEditor: React.FC<PhysicsConstraintEditorProps> = ({ entityId }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // 获取实体数据
  const entity = useSelector((state: RootState) => 
    state.entities.entities.find(entity => entity.id === entityId)
  );
  
  // 获取物理约束组件数据
  const physicsConstraintComponent = entity?.components?.physicsConstraint;
  
  // 表单状态
  const [form] = Form.useForm();
  
  // 约束类型
  const [constraintType, setConstraintType] = useState(physicsConstraintComponent?.type || ConstraintType.POINT_TO_POINT);
  
  // 初始化表单
  useEffect(() => {
    if (physicsConstraintComponent) {
      setConstraintType(physicsConstraintComponent.type || ConstraintType.POINT_TO_POINT);
      
      form.setFieldsValue({
        type: physicsConstraintComponent.type || ConstraintType.POINT_TO_POINT,
        targetEntity: physicsConstraintComponent.targetEntity || null,
        collideConnected: physicsConstraintComponent.collideConnected || true,
        // 点对点约束参数
        pivotA: physicsConstraintComponent.pivotA || { x: 0, y: 0, z: 0 },
        pivotB: physicsConstraintComponent.pivotB || { x: 0, y: 0, z: 0 },
        // 铰链约束参数
        axisA: physicsConstraintComponent.axisA || { x: 1, y: 0, z: 0 },
        axisB: physicsConstraintComponent.axisB || { x: 1, y: 0, z: 0 },
        // 距离约束参数
        distance: physicsConstraintComponent.distance || 1,
        // 弹簧约束参数
        stiffness: physicsConstraintComponent.stiffness || 100,
        damping: physicsConstraintComponent.damping || 1,
        // 圆锥扭转约束参数
        twistAngle: physicsConstraintComponent.twistAngle || Math.PI / 4,
        // 滑动约束参数
        lowerLimit: physicsConstraintComponent.lowerLimit || -1,
        upperLimit: physicsConstraintComponent.upperLimit || 1,
        // 车轮约束参数
        suspensionStiffness: physicsConstraintComponent.suspensionStiffness || 100,
        suspensionDamping: physicsConstraintComponent.suspensionDamping || 10,
        suspensionLength: physicsConstraintComponent.suspensionLength || 0.2,
        // 通用参数
        maxForce: physicsConstraintComponent.maxForce || 1e6
      });
    }
  }, [physicsConstraintComponent, form]);
  
  // 处理表单变更
  const handleValuesChange = (changedValues: any, allValues: any) => {
    if (!entity) return;
    
    // 如果约束类型改变，更新状态
    if (changedValues.type) {
      setConstraintType(changedValues.type);
    }
    
    // 更新实体的物理约束组件
    dispatch(updateEntity({
      id: entityId,
      changes: {
        components: {
          ...entity.components,
          physicsConstraint: {
            ...physicsConstraintComponent,
            ...changedValues
          }
        }
      }
    }));
  };
  
  // 处理移除组件
  const handleRemoveComponent = () => {
    if (!entity) return;
    
    // 创建新的组件对象，不包含物理约束组件
    const { physicsConstraint, ...otherComponents } = entity.components || {};
    
    // 更新实体
    dispatch(updateEntity({
      id: entityId,
      changes: {
        components: otherComponents
      }
    }));
  };
  
  // 如果没有实体或物理约束组件，显示空状态
  if (!entity || !physicsConstraintComponent) {
    return (
      <div className="component-editor empty-state">
        <p>{t('editor.physics.noPhysicsConstraint')}</p>
        <Button 
          type="primary" 
          onClick={() => {
            if (entity) {
              dispatch(updateEntity({
                id: entityId,
                changes: {
                  components: {
                    ...entity.components,
                    physicsConstraint: {
                      type: ConstraintType.POINT_TO_POINT,
                      targetEntity: null,
                      collideConnected: true,
                      pivotA: { x: 0, y: 0, z: 0 },
                      pivotB: { x: 0, y: 0, z: 0 },
                      maxForce: 1e6
                    }
                  }
                }
              }));
            }
          }}
        >
          {t('editor.physics.addPhysicsConstraint')}
        </Button>
      </div>
    );
  }
  
  return (
    <div className="component-editor physics-constraint-editor">
      <div className="component-header">
        <h3>{t('editor.physics.physicsConstraint')}</h3>
        <Tooltip title={t('editor.common.remove')}>
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={handleRemoveComponent}
          />
        </Tooltip>
      </div>
      
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValuesChange}
      >
        <Form.Item 
          name="type" 
          label={t('editor.physics.constraintType')}
          tooltip={t('editor.physics.constraintTypeTooltip')}
        >
          <Select>
            <Option value={ConstraintType.POINT_TO_POINT}>{t('editor.physics.constraints.pointToPoint')}</Option>
            <Option value={ConstraintType.HINGE}>{t('editor.physics.constraints.hinge')}</Option>
            <Option value={ConstraintType.DISTANCE}>{t('editor.physics.constraints.distance')}</Option>
            <Option value={ConstraintType.LOCK}>{t('editor.physics.constraints.lock')}</Option>
            <Option value={ConstraintType.SPRING}>{t('editor.physics.constraints.spring')}</Option>
            <Option value={ConstraintType.CONE_TWIST}>{t('editor.physics.constraints.coneTwist')}</Option>
            <Option value={ConstraintType.SLIDER}>{t('editor.physics.constraints.slider')}</Option>
            <Option value={ConstraintType.FIXED}>{t('editor.physics.constraints.fixed')}</Option>
            <Option value={ConstraintType.WHEEL}>{t('editor.physics.constraints.wheel')}</Option>
          </Select>
        </Form.Item>
        
        <Form.Item 
          name="targetEntity" 
          label={t('editor.physics.targetEntity')}
          tooltip={t('editor.physics.targetEntityTooltip')}
        >
          <EntitySelector />
        </Form.Item>
        
        <Form.Item 
          name="collideConnected" 
          label={t('editor.physics.collideConnected')}
          tooltip={t('editor.physics.collideConnectedTooltip')}
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
        
        <Collapse defaultActiveKey={['parameters']}>
          <Panel header={t('editor.physics.constraintParameters')} key="parameters">
            {(constraintType === ConstraintType.POINT_TO_POINT || 
              constraintType === ConstraintType.HINGE || 
              constraintType === ConstraintType.SPRING) && (
              <>
                <Form.Item 
                  name="pivotA" 
                  label={t('editor.physics.pivotA')}
                  tooltip={t('editor.physics.pivotATooltip')}
                >
                  <Vector3Input />
                </Form.Item>
                
                <Form.Item 
                  name="pivotB" 
                  label={t('editor.physics.pivotB')}
                  tooltip={t('editor.physics.pivotBTooltip')}
                >
                  <Vector3Input />
                </Form.Item>
              </>
            )}
            
            {(constraintType === ConstraintType.HINGE || 
              constraintType === ConstraintType.CONE_TWIST || 
              constraintType === ConstraintType.SLIDER || 
              constraintType === ConstraintType.WHEEL) && (
              <>
                <Form.Item 
                  name="axisA" 
                  label={t('editor.physics.axisA')}
                  tooltip={t('editor.physics.axisATooltip')}
                >
                  <Vector3Input />
                </Form.Item>
                
                <Form.Item 
                  name="axisB" 
                  label={t('editor.physics.axisB')}
                  tooltip={t('editor.physics.axisBTooltip')}
                >
                  <Vector3Input />
                </Form.Item>
              </>
            )}
            
            {constraintType === ConstraintType.DISTANCE && (
              <Form.Item 
                name="distance" 
                label={t('editor.physics.distance')}
                tooltip={t('editor.physics.distanceTooltip')}
                rules={[{ type: 'number', min: 0 }]}
              >
                <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            )}
            
            {constraintType === ConstraintType.SPRING && (
              <>
                <Form.Item 
                  name="stiffness" 
                  label={t('editor.physics.stiffness')}
                  tooltip={t('editor.physics.stiffnessTooltip')}
                  rules={[{ type: 'number', min: 0 }]}
                >
                  <InputNumber min={0} step={10} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item 
                  name="damping" 
                  label={t('editor.physics.damping')}
                  tooltip={t('editor.physics.dampingTooltip')}
                  rules={[{ type: 'number', min: 0 }]}
                >
                  <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
                </Form.Item>
              </>
            )}
            
            {constraintType === ConstraintType.CONE_TWIST && (
              <Form.Item 
                name="twistAngle" 
                label={t('editor.physics.twistAngle')}
                tooltip={t('editor.physics.twistAngleTooltip')}
                rules={[{ type: 'number', min: 0 }]}
              >
                <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            )}
            
            {constraintType === ConstraintType.SLIDER && (
              <>
                <Form.Item 
                  name="lowerLimit" 
                  label={t('editor.physics.lowerLimit')}
                  tooltip={t('editor.physics.lowerLimitTooltip')}
                >
                  <InputNumber step={0.1} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item 
                  name="upperLimit" 
                  label={t('editor.physics.upperLimit')}
                  tooltip={t('editor.physics.upperLimitTooltip')}
                >
                  <InputNumber step={0.1} style={{ width: '100%' }} />
                </Form.Item>
              </>
            )}
            
            {constraintType === ConstraintType.WHEEL && (
              <>
                <Form.Item 
                  name="suspensionStiffness" 
                  label={t('editor.physics.suspensionStiffness')}
                  tooltip={t('editor.physics.suspensionStiffnessTooltip')}
                  rules={[{ type: 'number', min: 0 }]}
                >
                  <InputNumber min={0} step={10} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item 
                  name="suspensionDamping" 
                  label={t('editor.physics.suspensionDamping')}
                  tooltip={t('editor.physics.suspensionDampingTooltip')}
                  rules={[{ type: 'number', min: 0 }]}
                >
                  <InputNumber min={0} step={1} style={{ width: '100%' }} />
                </Form.Item>
                
                <Form.Item 
                  name="suspensionLength" 
                  label={t('editor.physics.suspensionLength')}
                  tooltip={t('editor.physics.suspensionLengthTooltip')}
                  rules={[{ type: 'number', min: 0 }]}
                >
                  <InputNumber min={0} step={0.1} style={{ width: '100%' }} />
                </Form.Item>
              </>
            )}
            
            <Form.Item 
              name="maxForce" 
              label={t('editor.physics.maxForce')}
              tooltip={t('editor.physics.maxForceTooltip')}
              rules={[{ type: 'number', min: 0 }]}
            >
              <InputNumber min={0} step={1000} style={{ width: '100%' }} />
            </Form.Item>
          </Panel>
        </Collapse>
      </Form>
    </div>
  );
};

export default PhysicsConstraintEditor;
