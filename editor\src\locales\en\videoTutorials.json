{"videoTutorials": {"title": "Video Tutorials", "allTutorials": "All Tutorials", "recommended": "Recommended", "watched": "Watched", "search": "Search", "searchPlaceholder": "Search video tutorials...", "searchResults": "Search Results: Found {count} tutorials related to \"{query}\"", "noSearchResults": "No matching results found", "noRecommendations": "No recommended tutorials, you've watched them all!", "watch": "Watch", "continue": "Continue", "rewatch": "Rewatch", "minutes": "minutes", "loading": "Loading video tutorial...", "browserNotSupported": "Your browser does not support video playback. Please upgrade your browser or use a different one.", "playbackSpeed": "Playback Speed", "toggleChapters": "Show/Hide Chapters", "fullscreen": "Fullscreen", "chapters": "Chapters", "categories": {"getting-started": "Getting Started", "materials": "Material Editing", "animation": "Animation System", "scripting": "Scripting", "physics": "Physics System", "ui": "UI System", "networking": "Networking", "advanced": "Advanced Topics"}, "difficulty": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced"}, "editorBasics": {"title": "Editor Basics", "description": "Learn the basic interface and operations of the IR Engine Editor.", "chapters": {"introduction": "Introduction", "interfaceOverview": "Interface Overview", "sceneNavigation": "Scene Navigation", "objectManipulation": "Object Manipulation", "conclusion": "Conclusion"}}, "materialEditing": {"title": "Material Editing", "description": "Learn how to create and edit materials, including PBR materials and texture mapping.", "chapters": {"introduction": "Introduction", "materialTypes": "Material Types", "textureMapping": "Texture Mapping", "pbrMaterials": "PBR Materials", "shaderVariants": "<PERSON><PERSON>", "conclusion": "Conclusion"}}, "animationSystem": {"title": "Animation System", "description": "Learn how to create and edit animations using the IR Engine animation system.", "chapters": {"introduction": "Introduction", "animationTypes": "Animation Types", "keyframeAnimation": "Keyframe Animation", "animationBlending": "Animation Blending", "stateMachines": "State Machines", "conclusion": "Conclusion"}}, "visualScripting": {"title": "Visual Scripting", "description": "Learn how to create interactive content using the visual scripting system.", "chapters": {"introduction": "Introduction", "basicConcepts": "Basic Concepts", "nodeTypes": "Node Types", "creatingLogic": "Creating Logic", "debugging": "Debugging", "conclusion": "Conclusion"}}, "physicsSystem": {"title": "Physics System", "description": "Learn how to create realistic physical interactions using the IR Engine physics system.", "chapters": {"introduction": "Introduction", "rigidBodies": "Rigid Bodies", "colliders": "Colliders", "joints": "Joints", "softBodies": "Soft Bodies", "conclusion": "Conclusion"}}, "uiSystem": {"title": "UI System", "description": "Learn how to create user interfaces using the IR Engine UI system.", "chapters": {"introduction": "Introduction", "uiComponents": "UI Components", "layouts": "Layouts", "styling": "Styl<PERSON>", "interaction": "Interaction", "conclusion": "Conclusion"}}, "networkingSystem": {"title": "Networking System", "description": "Learn how to create multi-user experiences using the IR Engine networking system.", "chapters": {"introduction": "Introduction", "networkArchitecture": "Network Architecture", "entitySynchronization": "Entity Synchronization", "rpc": "Remote Procedure Calls", "roomManagement": "Room Management", "conclusion": "Conclusion"}}}}