/**
 * 高级表情生成器
 * 用于生成复杂的面部表情动画
 */
import { EmotionAnalysisResult, EmotionTimePoint } from './AdvancedEmotionAnalyzer';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 表情生成请求
 */
export interface ExpressionGenerationRequest {
  /** 请求ID */
  id: string;
  /** 情感分析结果 */
  emotionResult: EmotionAnalysisResult;
  /** 持续时间（秒） */
  duration: number;
  /** 是否循环 */
  loop?: boolean;
  /** 风格 */
  style?: ExpressionStyle;
  /** 强度 */
  intensity?: number;
  /** 是否启用微表情 */
  enableMicroExpressions?: boolean;
  /** 微表情配置 */
  microExpressionConfig?: MicroExpressionConfig;
  /** 混合配置 */
  blendConfig?: ExpressionBlendConfig;
  /** 自定义参数 */
  customParams?: Record<string, any>;
}

/**
 * 表情生成结果
 */
export interface ExpressionGenerationResult {
  /** 请求ID */
  id: string;
  /** 是否成功 */
  success: boolean;
  /** 错误信息 */
  error?: string;
  /** 表情数据 */
  expressionData?: ExpressionData;
  /** 生成时间（毫秒） */
  generationTime?: number;
}

/**
 * 表情数据
 */
export interface ExpressionData {
  /** 表情关键帧 */
  keyframes: ExpressionKeyframe[];
  /** 持续时间（秒） */
  duration: number;
  /** 是否循环 */
  loop: boolean;
  /** 表情名称 */
  name: string;
  /** 表情类型 */
  type: string;
  /** 表情强度 */
  intensity: number;
  /** 微表情数据 */
  microExpressions?: MicroExpressionData[];
}

/**
 * 表情关键帧
 */
export interface ExpressionKeyframe {
  /** 时间（秒） */
  time: number;
  /** 表情混合形状 */
  blendShapes: Record<string, number>;
  /** 情感 */
  emotion?: string;
  /** 强度 */
  intensity?: number;
}

/**
 * 微表情数据
 */
export interface MicroExpressionData {
  /** 类型 */
  type: string;
  /** 开始时间（秒） */
  startTime: number;
  /** 持续时间（秒） */
  duration: number;
  /** 强度 */
  intensity: number;
  /** 表情混合形状 */
  blendShapes: Record<string, number>;
}

/**
 * 表情风格
 */
export type ExpressionStyle = 'natural' | 'cartoon' | 'exaggerated' | 'subtle' | 'dramatic';

/**
 * 微表情配置
 */
export interface MicroExpressionConfig {
  /** 是否启用 */
  enabled: boolean;
  /** 频率 */
  frequency: number;
  /** 强度 */
  intensity: number;
  /** 持续时间 */
  duration: number;
  /** 类型 */
  types: string[];
  /** 随机性 */
  randomness: number;
}

/**
 * 表情混合配置
 */
export interface ExpressionBlendConfig {
  /** 混合模式 */
  mode: 'add' | 'multiply' | 'override' | 'weighted';
  /** 混合权重 */
  weight?: number;
  /** 过渡时间 */
  transitionTime: number;
  /** 情感变化 */
  emotionChanges?: EmotionTimePoint[];
}

/**
 * 高级表情生成器配置
 */
export interface AdvancedExpressionGeneratorConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 默认风格 */
  defaultStyle?: ExpressionStyle;
  /** 默认强度 */
  defaultIntensity?: number;
  /** 是否启用微表情 */
  enableMicroExpressions?: boolean;
  /** 微表情配置 */
  microExpressionConfig?: Partial<MicroExpressionConfig>;
  /** 混合配置 */
  blendConfig?: Partial<ExpressionBlendConfig>;
  /** 是否使用物理模拟 */
  usePhysics?: boolean;
  /** 物理参数 */
  physicsParams?: Record<string, any>;
}

/**
 * 表情混合形状映射
 */
export interface BlendShapeMapping {
  /** 情感到混合形状的映射 */
  emotionToBlendShapes: Record<string, Record<string, number>>;
  /** 微表情到混合形状的映射 */
  microExpressionToBlendShapes: Record<string, Record<string, number>>;
}

/**
 * 默认混合形状映射
 */
const DEFAULT_BLEND_SHAPE_MAPPING: BlendShapeMapping = {
  emotionToBlendShapes: {
    'happy': {
      'browInnerUp': 0.3,
      'browOuterUp': 0.5,
      'eyeSquintLeft': 0.4,
      'eyeSquintRight': 0.4,
      'cheekSquintLeft': 0.7,
      'cheekSquintRight': 0.7,
      'mouthSmile': 0.8,
      'mouthSmileLeft': 0.8,
      'mouthSmileRight': 0.8
    },
    'sad': {
      'browInnerDown': 0.7,
      'browOuterDown': 0.3,
      'eyeSquintLeft': 0.2,
      'eyeSquintRight': 0.2,
      'mouthFrownLeft': 0.6,
      'mouthFrownRight': 0.6,
      'mouthSad': 0.7
    },
    'angry': {
      'browInnerDown': 0.8,
      'browOuterDown': 0.6,
      'eyeSquintLeft': 0.7,
      'eyeSquintRight': 0.7,
      'noseSneer': 0.5,
      'mouthFrownLeft': 0.4,
      'mouthFrownRight': 0.4,
      'jawForward': 0.3
    },
    'surprised': {
      'browInnerUp': 0.8,
      'browOuterUp': 0.8,
      'eyeWideLeft': 0.9,
      'eyeWideRight': 0.9,
      'jawOpen': 0.6,
      'mouthOpen': 0.7
    },
    'fear': {
      'browInnerUp': 0.7,
      'browOuterUp': 0.5,
      'eyeWideLeft': 0.7,
      'eyeWideRight': 0.7,
      'mouthOpen': 0.3,
      'jawOpen': 0.2
    },
    'disgust': {
      'browInnerDown': 0.5,
      'noseSneer': 0.8,
      'mouthUpperUp': 0.6,
      'mouthLowerDown': 0.3,
      'mouthLeft': 0.4
    },
    'neutral': {
      'browInnerUp': 0.1,
      'browOuterUp': 0.1,
      'mouthClose': 0.1
    }
  },
  microExpressionToBlendShapes: {
    'blink': {
      'eyeBlinkLeft': 1.0,
      'eyeBlinkRight': 1.0
    },
    'eyebrow_raise': {
      'browInnerUp': 0.7,
      'browOuterUp': 0.7
    },
    'mouth_twitch': {
      'mouthLeft': 0.5,
      'mouthRight': 0.3
    },
    'nose_wrinkle': {
      'noseSneer': 0.7
    },
    'eye_squint': {
      'eyeSquintLeft': 0.6,
      'eyeSquintRight': 0.6
    },
    'head_shake': {},
    'head_nod': {},
    'lip_press': {
      'mouthPress': 0.8,
      'mouthPressLeft': 0.7,
      'mouthPressRight': 0.7
    },
    'brow_furrow': {
      'browInnerDown': 0.8
    },
    'eye_widen': {
      'eyeWideLeft': 0.8,
      'eyeWideRight': 0.8
    }
  }
};

/**
 * 默认微表情配置
 */
const DEFAULT_MICRO_EXPRESSION_CONFIG: MicroExpressionConfig = {
  enabled: true,
  frequency: 0.3,
  intensity: 0.5,
  duration: 0.2,
  types: ['blink', 'eyebrow_raise', 'mouth_twitch'],
  randomness: 0.5
};

/**
 * 默认混合配置
 */
const DEFAULT_BLEND_CONFIG: ExpressionBlendConfig = {
  mode: 'weighted',
  weight: 0.7,
  transitionTime: 0.3
};

/**
 * 高级表情生成器
 */
export class AdvancedExpressionGenerator {
  /** 配置 */
  private config: AdvancedExpressionGeneratorConfig;
  
  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: AdvancedExpressionGeneratorConfig = {
    debug: false,
    defaultStyle: 'natural',
    defaultIntensity: 0.8,
    enableMicroExpressions: true,
    microExpressionConfig: DEFAULT_MICRO_EXPRESSION_CONFIG,
    blendConfig: DEFAULT_BLEND_CONFIG,
    usePhysics: false
  };
  
  /** 混合形状映射 */
  private blendShapeMapping: BlendShapeMapping;
  
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  
  /** 随机数生成器种子 */
  private randomSeed: number = Math.random();
  
  /**
   * 构造函数
   * @param config 配置
   * @param blendShapeMapping 混合形状映射
   */
  constructor(
    config: AdvancedExpressionGeneratorConfig = {},
    blendShapeMapping: BlendShapeMapping = DEFAULT_BLEND_SHAPE_MAPPING
  ) {
    this.config = {
      ...AdvancedExpressionGenerator.DEFAULT_CONFIG,
      ...config
    };
    
    this.blendShapeMapping = blendShapeMapping;
  }
  
  /**
   * 生成表情
   * @param request 请求
   * @returns 生成结果
   */
  public async generateExpression(request: ExpressionGenerationRequest): Promise<ExpressionGenerationResult> {
    const startTime = Date.now();
    
    try {
      if (this.config.debug) {
        console.log('生成表情:', request);
      }
      
      // 创建表情数据
      const expressionData = this.createExpressionData(request);
      
      // 创建结果
      const result: ExpressionGenerationResult = {
        id: request.id,
        success: true,
        expressionData,
        generationTime: Date.now() - startTime
      };
      
      return result;
    } catch (error) {
      console.error('生成表情失败:', error);
      
      return {
        id: request.id,
        success: false,
        error: error instanceof Error ? error.message : String(error),
        generationTime: Date.now() - startTime
      };
    }
  }
  
  /**
   * 创建表情数据
   * @param request 请求
   * @returns 表情数据
   */
  private createExpressionData(request: ExpressionGenerationRequest): ExpressionData {
    // 获取情感分析结果
    const emotionResult = request.emotionResult;
    
    // 获取主要情感和强度
    const primaryEmotion = emotionResult.primaryEmotion;
    const primaryIntensity = emotionResult.primaryIntensity * (request.intensity || this.config.defaultIntensity || 0.8);
    
    // 创建表情名称
    const expressionName = `${primaryEmotion}_${Date.now()}`;
    
    // 创建表情类型
    const expressionType = request.style || this.config.defaultStyle || 'natural';
    
    // 创建表情关键帧
    const keyframes = this.createExpressionKeyframes(request);
    
    // 创建微表情数据
    const microExpressions = this.createMicroExpressions(request);
    
    // 创建表情数据
    const expressionData: ExpressionData = {
      keyframes,
      duration: request.duration,
      loop: request.loop || false,
      name: expressionName,
      type: expressionType,
      intensity: primaryIntensity
    };
    
    // 如果启用微表情，添加微表情数据
    if (microExpressions.length > 0) {
      expressionData.microExpressions = microExpressions;
    }
    
    return expressionData;
  }
  
  /**
   * 创建表情关键帧
   * @param request 请求
   * @returns 表情关键帧数组
   */
  private createExpressionKeyframes(request: ExpressionGenerationRequest): ExpressionKeyframe[] {
    // 获取情感分析结果
    const emotionResult = request.emotionResult;
    
    // 获取情感变化
    const emotionChanges = request.blendConfig?.emotionChanges || 
                          emotionResult.emotionChanges?.timeline || 
                          this.createDefaultEmotionChanges(emotionResult);
    
    // 获取持续时间
    const duration = request.duration;
    
    // 获取强度
    const intensity = request.intensity || this.config.defaultIntensity || 0.8;
    
    // 创建关键帧
    const keyframes: ExpressionKeyframe[] = [];
    
    // 遍历情感变化
    for (const change of emotionChanges) {
      // 计算时间
      const time = change.time * duration;
      
      // 获取情感和强度
      const emotion = change.emotion;
      const emotionIntensity = change.intensity * intensity;
      
      // 获取混合形状
      const blendShapes = this.getBlendShapesForEmotion(emotion, emotionIntensity, request.style);
      
      // 创建关键帧
      const keyframe: ExpressionKeyframe = {
        time,
        blendShapes,
        emotion,
        intensity: emotionIntensity
      };
      
      keyframes.push(keyframe);
    }
    
    return keyframes;
  }
  
  /**
   * 创建默认情感变化
   * @param emotionResult 情感分析结果
   * @returns 情感时间点数组
   */
  private createDefaultEmotionChanges(emotionResult: EmotionAnalysisResult): EmotionTimePoint[] {
    // 获取主要情感和强度
    const primaryEmotion = emotionResult.primaryEmotion;
    const primaryIntensity = emotionResult.primaryIntensity;
    
    // 创建默认情感变化
    const changes: EmotionTimePoint[] = [
      { time: 0.0, emotion: 'neutral', intensity: 0.8 },
      { time: 0.3, emotion: primaryEmotion, intensity: primaryIntensity }
    ];
    
    // 如果有次要情感，添加变化
    if (emotionResult.secondaryEmotion) {
      changes.push({
        time: 0.7,
        emotion: emotionResult.secondaryEmotion,
        intensity: emotionResult.secondaryIntensity || 0.3
      });
      
      changes.push({
        time: 1.0,
        emotion: primaryEmotion,
        intensity: primaryIntensity * 0.9
      });
    } else {
      changes.push({
        time: 1.0,
        emotion: primaryEmotion,
        intensity: primaryIntensity
      });
    }
    
    return changes;
  }
  
  /**
   * 创建微表情
   * @param request 请求
   * @returns 微表情数据数组
   */
  private createMicroExpressions(request: ExpressionGenerationRequest): MicroExpressionData[] {
    // 检查是否启用微表情
    const enableMicroExpressions = request.enableMicroExpressions !== undefined
      ? request.enableMicroExpressions
      : this.config.enableMicroExpressions;
    
    if (!enableMicroExpressions) {
      return [];
    }
    
    // 获取微表情配置
    const config = request.microExpressionConfig || 
                  this.config.microExpressionConfig || 
                  DEFAULT_MICRO_EXPRESSION_CONFIG;
    
    // 如果未启用，返回空数组
    if (!config.enabled) {
      return [];
    }
    
    // 获取持续时间
    const duration = request.duration;
    
    // 创建微表情数据
    const microExpressions: MicroExpressionData[] = [];
    
    // 计算微表情数量
    const count = Math.max(1, Math.floor(duration * config.frequency));
    
    // 设置随机种子
    this.setRandomSeed(request.id);
    
    // 生成微表情
    for (let i = 0; i < count; i++) {
      // 随机选择类型
      const typeIndex = Math.floor(this.random() * config.types.length);
      const type = config.types[typeIndex];
      
      // 计算开始时间
      const startTime = this.random() * (duration - config.duration);
      
      // 计算强度
      const baseIntensity = config.intensity;
      const randomFactor = 1.0 - config.randomness / 2 + this.random() * config.randomness;
      const intensity = baseIntensity * randomFactor;
      
      // 获取混合形状
      const blendShapes = this.getBlendShapesForMicroExpression(type, intensity);
      
      // 创建微表情数据
      const microExpression: MicroExpressionData = {
        type,
        startTime,
        duration: config.duration,
        intensity,
        blendShapes
      };
      
      microExpressions.push(microExpression);
    }
    
    // 按开始时间排序
    microExpressions.sort((a, b) => a.startTime - b.startTime);
    
    return microExpressions;
  }
  
  /**
   * 获取情感的混合形状
   * @param emotion 情感
   * @param intensity 强度
   * @param style 风格
   * @returns 混合形状
   */
  private getBlendShapesForEmotion(
    emotion: string,
    intensity: number,
    style?: ExpressionStyle
  ): Record<string, number> {
    // 获取基本混合形状
    const baseBlendShapes = this.blendShapeMapping.emotionToBlendShapes[emotion] || {};
    
    // 创建结果
    const result: Record<string, number> = {};
    
    // 应用强度
    for (const [key, value] of Object.entries(baseBlendShapes)) {
      result[key] = value * intensity;
    }
    
    // 应用风格
    if (style) {
      this.applyStyleToBlendShapes(result, style);
    }
    
    return result;
  }
  
  /**
   * 获取微表情的混合形状
   * @param type 类型
   * @param intensity 强度
   * @returns 混合形状
   */
  private getBlendShapesForMicroExpression(
    type: string,
    intensity: number
  ): Record<string, number> {
    // 获取基本混合形状
    const baseBlendShapes = this.blendShapeMapping.microExpressionToBlendShapes[type] || {};
    
    // 创建结果
    const result: Record<string, number> = {};
    
    // 应用强度
    for (const [key, value] of Object.entries(baseBlendShapes)) {
      result[key] = value * intensity;
    }
    
    return result;
  }
  
  /**
   * 应用风格到混合形状
   * @param blendShapes 混合形状
   * @param style 风格
   */
  private applyStyleToBlendShapes(
    blendShapes: Record<string, number>,
    style: ExpressionStyle
  ): void {
    switch (style) {
      case 'cartoon':
        // 卡通风格：夸张的表情
        for (const key in blendShapes) {
          blendShapes[key] = Math.min(1.0, blendShapes[key] * 1.5);
        }
        break;
      
      case 'exaggerated':
        // 夸张风格：极度夸张的表情
        for (const key in blendShapes) {
          blendShapes[key] = Math.min(1.0, blendShapes[key] * 2.0);
        }
        break;
      
      case 'subtle':
        // 微妙风格：轻微的表情
        for (const key in blendShapes) {
          blendShapes[key] *= 0.5;
        }
        break;
      
      case 'dramatic':
        // 戏剧性风格：强烈的对比
        for (const key in blendShapes) {
          if (blendShapes[key] > 0.5) {
            blendShapes[key] = Math.min(1.0, blendShapes[key] * 1.8);
          } else {
            blendShapes[key] *= 0.3;
          }
        }
        break;
      
      case 'natural':
      default:
        // 自然风格：不做调整
        break;
    }
  }
  
  /**
   * 设置随机种子
   * @param seed 种子
   */
  private setRandomSeed(seed: string): void {
    // 简单的字符串哈希
    let hash = 0;
    for (let i = 0; i < seed.length; i++) {
      hash = ((hash << 5) - hash) + seed.charCodeAt(i);
      hash |= 0; // 转换为32位整数
    }
    
    this.randomSeed = hash / 2147483647;
  }
  
  /**
   * 生成随机数
   * @returns 0-1之间的随机数
   */
  private random(): number {
    // 简单的线性同余生成器
    this.randomSeed = (this.randomSeed * 9301 + 49297) % 233280;
    return this.randomSeed / 233280;
  }
  
  /**
   * 设置混合形状映射
   * @param mapping 映射
   */
  public setBlendShapeMapping(mapping: BlendShapeMapping): void {
    this.blendShapeMapping = mapping;
  }
  
  /**
   * 获取混合形状映射
   * @returns 映射
   */
  public getBlendShapeMapping(): BlendShapeMapping {
    return { ...this.blendShapeMapping };
  }
  
  /**
   * 监听事件
   * @param event 事件名称
   * @param listener 监听器
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }
  
  /**
   * 取消监听事件
   * @param event 事件名称
   * @param listener 监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
  
  /**
   * 销毁
   */
  public dispose(): void {
    this.eventEmitter.removeAllListeners();
  }
}
