/**
 * EditorLayout组件测试
 */
import React from 'react';
import { render, screen, fireEvent } from '../../../__tests__/utils/test-utils';
import { EditorLayout } from '../EditorLayout';
import { vi } from 'vitest';

// 模拟组件
vi.mock('../../toolbar/Toolbar', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-toolbar">Toolbar</div>
}));

vi.mock('../DockLayout', () => ({
  __esModule: true,
  default: ({ defaultLayout, onLayoutChange }: any) => (
    <div data-testid="mock-dock-layout">
      <div data-testid="mock-dock-layout-content">
        {JSON.stringify(defaultLayout)}
      </div>
      <button 
        data-testid="mock-layout-change-button"
        onClick={() => onLayoutChange && onLayoutChange({ test: 'new-layout' })}
      >
        Change Layout
      </button>
    </div>
  )
}));

// 模拟面板组件
vi.mock('../../panels/ScenePanel', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-scene-panel">Scene Panel</div>
}));

vi.mock('../../panels/HierarchyPanel', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-hierarchy-panel">Hierarchy Panel</div>
}));

vi.mock('../../panels/InspectorPanel', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-inspector-panel">Inspector Panel</div>
}));

vi.mock('../../panels/AssetsPanel', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-assets-panel">Assets Panel</div>
}));

vi.mock('../../panels/ConsolePanel', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-console-panel">Console Panel</div>
}));

vi.mock('../../collaboration/CollaborationPanel', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-collaboration-panel">Collaboration Panel</div>
}));

vi.mock('../../testing/UserTestingPanel', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-user-testing-panel">User Testing Panel</div>
}));

vi.mock('../../debug/DebugPanel', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-debug-panel">Debug Panel</div>
}));

// 模拟服务
vi.mock('../../../services/LayoutService', () => ({
  __esModule: true,
  default: {
    registerPanelContent: vi.fn(),
    getPanelContent: vi.fn((contentId) => {
      const components: any = {
        'scene': () => <div>Scene Content</div>,
        'hierarchy': () => <div>Hierarchy Content</div>,
        'inspector': () => <div>Inspector Content</div>,
        'assets': () => <div>Assets Content</div>,
        'console': () => <div>Console Content</div>,
        'collaboration': () => <div>Collaboration Content</div>,
        'user-testing': () => <div>User Testing Content</div>,
        'debug': () => <div>Debug Content</div>,
      };
      return components[contentId];
    }),
  }
}));

describe('EditorLayout组件', () => {
  const mockProjectId = 'test-project';
  const mockSceneId = 'test-scene';

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('应该正确渲染EditorLayout组件', () => {
    render(
      <EditorLayout 
        projectId={mockProjectId} 
        sceneId={mockSceneId} 
      />
    );

    // 验证工具栏和布局组件已渲染
    expect(screen.getByTestId('mock-toolbar')).toBeInTheDocument();
    expect(screen.getByTestId('mock-dock-layout')).toBeInTheDocument();
  });

  it('应该在布局变化时更新状态', () => {
    render(
      <EditorLayout 
        projectId={mockProjectId} 
        sceneId={mockSceneId} 
      />
    );

    const changeButton = screen.getByTestId('mock-layout-change-button');
    fireEvent.click(changeButton);

    // 注意：由于Redux状态更新是异步的，这里我们只能验证按钮点击事件
    // 实际测试中可能需要使用更复杂的方法来验证Redux状态更新
    expect(changeButton).toBeInTheDocument();
  });
});
