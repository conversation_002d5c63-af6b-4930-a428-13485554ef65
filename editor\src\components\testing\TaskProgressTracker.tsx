/**
 * 任务进度跟踪组件
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Progress,
  Typography,
  Space,
  List,
  Tag,
  Button,
  Tooltip,
  Timeline,
  Collapse,
  Statistic,
  Row,
  Col
} from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  RightCircleOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  FieldTimeOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { useSelector } from 'react-redux';
import { selectTasks, selectCurrentTaskId } from '../../store/testing/userTestingSlice';
import { TestTask, userTestingService } from '../../services/UserTestingService';

const { Text, Title, Paragraph } = Typography;
const { Panel } = Collapse;

/**
 * 任务进度跟踪组件属性
 */
interface TaskProgressTrackerProps {
  /** 是否显示详细信息 */
  showDetails?: boolean;
  /** 是否显示时间统计 */
  showTimeStats?: boolean;
  /** 是否显示子任务 */
  showSubtasks?: boolean;
  /** 是否允许编辑 */
  allowEdit?: boolean;
  /** 任务完成回调 */
  onTaskComplete?: (taskId: string) => void;
  /** 任务进度更新回调 */
  onProgressUpdate?: (taskId: string, progress: number) => void;
}

/**
 * 任务进度跟踪组件
 */
const TaskProgressTracker: React.FC<TaskProgressTrackerProps> = ({
  showDetails = true,
  showTimeStats = true,
  showSubtasks = true,
  allowEdit = true,
  onTaskComplete,
  onProgressUpdate
}) => {
  // 从Redux获取状态
  const tasks = useSelector(selectTasks);
  const currentTaskId = useSelector(selectCurrentTaskId);
  
  // 本地状态
  const [taskTimers, setTaskTimers] = useState<Record<string, number>>({});
  const [activeTimers, setActiveTimers] = useState<Record<string, NodeJS.Timeout>>({});
  
  // 计算总进度
  const calculateTotalProgress = () => {
    if (tasks.length === 0) return 0;
    
    // 考虑任务权重
    const totalWeight = tasks.reduce((sum, task) => sum + (task.weight || 1), 0);
    const weightedProgress = tasks.reduce((sum, task) => {
      const taskProgress = task.progress || (task.completed ? 100 : 0);
      return sum + taskProgress * (task.weight || 1);
    }, 0);
    
    return Math.round(weightedProgress / totalWeight);
  };
  
  // 计算预计剩余时间
  const calculateEstimatedTimeRemaining = () => {
    if (tasks.length === 0) return 0;
    
    // 已完成的任务
    const completedTasks = tasks.filter(task => task.completed);
    if (completedTasks.length === 0) return 0;
    
    // 计算平均完成时间
    const avgCompletionTime = completedTasks.reduce((sum, task) => {
      return sum + (task.timeSpent || 0);
    }, 0) / completedTasks.length;
    
    // 未完成的任务
    const remainingTasks = tasks.filter(task => !task.completed);
    
    // 预计剩余时间
    return avgCompletionTime * remainingTasks.length;
  };
  
  // 启动任务计时器
  const startTaskTimer = (taskId: string) => {
    if (activeTimers[taskId]) return;
    
    const timer = setInterval(() => {
      setTaskTimers(prev => ({
        ...prev,
        [taskId]: (prev[taskId] || 0) + 1
      }));
    }, 1000);
    
    setActiveTimers(prev => ({
      ...prev,
      [taskId]: timer
    }));
  };
  
  // 停止任务计时器
  const stopTaskTimer = (taskId: string) => {
    if (!activeTimers[taskId]) return;
    
    clearInterval(activeTimers[taskId]);
    
    setActiveTimers(prev => {
      const newTimers = { ...prev };
      delete newTimers[taskId];
      return newTimers;
    });
  };
  
  // 更新任务进度
  const updateTaskProgress = (taskId: string, progress: number) => {
    if (!allowEdit) return;
    
    try {
      userTestingService.updateTaskProgress(taskId, progress);
      
      if (onProgressUpdate) {
        onProgressUpdate(taskId, progress);
      }
    } catch (error) {
      console.error('更新任务进度失败:', error);
    }
  };
  
  // 完成任务
  const completeTask = (taskId: string) => {
    if (!allowEdit) return;
    
    try {
      userTestingService.completeTask(taskId);
      
      // 停止计时器
      stopTaskTimer(taskId);
      
      if (onTaskComplete) {
        onTaskComplete(taskId);
      }
    } catch (error) {
      console.error('完成任务失败:', error);
    }
  };
  
  // 格式化时间
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    return `${hours > 0 ? `${hours}小时 ` : ''}${minutes}分钟 ${secs}秒`;
  };
  
  // 渲染任务列表
  const renderTaskList = () => {
    return (
      <List
        itemLayout="horizontal"
        dataSource={tasks}
        renderItem={task => (
          <List.Item
            actions={allowEdit ? [
              task.completed ? (
                <Tag color="success" icon={<CheckCircleOutlined />}>已完成</Tag>
              ) : (
                <Button
                  type="primary"
                  size="small"
                  icon={<CheckCircleOutlined />}
                  onClick={() => completeTask(task.id)}
                >
                  完成
                </Button>
              )
            ] : []}
          >
            <List.Item.Meta
              avatar={
                <Tooltip title={task.completed ? '已完成' : task.id === currentTaskId ? '进行中' : '未开始'}>
                  {task.completed ? (
                    <CheckCircleOutlined style={{ color: '#52c41a', fontSize: 20 }} />
                  ) : task.id === currentTaskId ? (
                    <ClockCircleOutlined style={{ color: '#1890ff', fontSize: 20 }} />
                  ) : (
                    <RightCircleOutlined style={{ color: '#d9d9d9', fontSize: 20 }} />
                  )}
                </Tooltip>
              }
              title={
                <Space>
                  <Text strong>{task.title}</Text>
                  {task.id === currentTaskId && (
                    <Tag color="blue">当前</Tag>
                  )}
                </Space>
              }
              description={
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Paragraph>{task.description}</Paragraph>
                  <Progress 
                    percent={task.progress || (task.completed ? 100 : 0)} 
                    size="small" 
                    status={task.completed ? 'success' : 'active'} 
                  />
                  {showTimeStats && (
                    <Space>
                      <FieldTimeOutlined />
                      <Text type="secondary">
                        {task.timeSpent ? formatTime(Math.floor(task.timeSpent / 1000)) : '未开始'}
                      </Text>
                      {task.id === currentTaskId && !task.completed && (
                        <Button
                          type="text"
                          size="small"
                          icon={activeTimers[task.id] ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                          onClick={() => {
                            if (activeTimers[task.id]) {
                              stopTaskTimer(task.id);
                            } else {
                              startTaskTimer(task.id);
                            }
                          }}
                        />
                      )}
                    </Space>
                  )}
                </Space>
              }
            />
          </List.Item>
        )}
      />
    );
  };
  
  // 渲染时间统计
  const renderTimeStats = () => {
    if (!showTimeStats) return null;
    
    const totalTime = tasks.reduce((sum, task) => sum + (task.timeSpent || 0), 0);
    const estimatedTimeRemaining = calculateEstimatedTimeRemaining();
    
    return (
      <Card size="small" title="时间统计" bordered={false}>
        <Row gutter={16}>
          <Col span={12}>
            <Statistic
              title="总耗时"
              value={formatTime(Math.floor(totalTime / 1000))}
              prefix={<FieldTimeOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="预计剩余时间"
              value={formatTime(Math.floor(estimatedTimeRemaining / 1000))}
              prefix={<ClockCircleOutlined />}
            />
          </Col>
        </Row>
      </Card>
    );
  };
  
  // 组件挂载时启动当前任务的计时器
  useEffect(() => {
    if (currentTaskId) {
      startTaskTimer(currentTaskId);
    }
    
    // 组件卸载时清除所有计时器
    return () => {
      Object.keys(activeTimers).forEach(taskId => {
        clearInterval(activeTimers[taskId]);
      });
    };
  }, [currentTaskId]);
  
  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Card size="small" title="任务进度" bordered={false}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Progress 
            percent={calculateTotalProgress()} 
            status="active" 
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
          <Text>
            已完成 {tasks.filter(task => task.completed).length}/{tasks.length} 个任务
          </Text>
        </Space>
      </Card>
      
      {showTimeStats && renderTimeStats()}
      
      {showDetails && (
        <Card size="small" title="任务详情" bordered={false}>
          {renderTaskList()}
        </Card>
      )}
    </Space>
  );
};

export default TaskProgressTracker;
