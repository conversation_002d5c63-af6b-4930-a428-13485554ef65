# 温泉系统教程

本教程将介绍如何使用引擎的温泉系统创建各种类型的温泉效果，包括标准温泉、高温温泉、低温温泉、大型温泉、小型温泉、硫磺温泉、矿物质温泉和地下温泉等。

## 目录

1. [温泉系统概述](#温泉系统概述)
2. [创建基本温泉](#创建基本温泉)
3. [使用温泉预设](#使用温泉预设)
4. [自定义温泉属性](#自定义温泉属性)
5. [温泉物理交互](#温泉物理交互)
6. [温泉粒子效果](#温泉粒子效果)
7. [温泉声音效果](#温泉声音效果)
8. [温泉热效应](#温泉热效应)
9. [矿物质效果](#矿物质效果)
10. [性能优化](#性能优化)
11. [常见问题解答](#常见问题解答)

## 温泉系统概述

温泉系统是DL（Digital Learning）引擎水体系统的扩展，专门用于创建各种类型的温泉效果。温泉系统由以下几个主要组件组成：

- **HotSpringComponent**：温泉组件，用于定义温泉的基本属性，如尺寸、位置、颜色、温度等。
- **HotSpringPresets**：温泉预设，提供各种类型的温泉预设配置，如标准温泉、高温温泉、低温温泉等。
- **WaterPhysicsSystem**：水体物理系统，用于模拟温泉的物理行为，如波动、流动等。
- **WaterInteractionSystem**：水体交互系统，用于处理温泉与其他物体的交互效果，如浮力、阻力等。
- **UnderwaterParticleSystem**：水下粒子系统，用于创建温泉的粒子效果，如气泡、水蒸气等。
- **AudioSystem**：音频系统，用于播放温泉的声音效果。

## 创建基本温泉

### 步骤1：创建温泉组件

```typescript
// 创建温泉实体
const hotSpringEntity = new Entity();
hotSpringEntity.setName('hotspring');

// 创建温泉组件
const hotSpringComponent = new HotSpringComponent(hotSpringEntity, {
  width: 10,
  height: 1,
  depth: 10,
  position: new THREE.Vector3(0, 0, 0),
  color: new THREE.Color(0x88ccff),
  opacity: 0.8,
  temperature: 60,
  waveAmplitude: 0.1,
  waveFrequency: 2.0,
  waveSpeed: 0.5,
  enableBubbleEffect: true,
  bubbleEffectStrength: 1.0,
  enableSteamEffect: true,
  steamEffectStrength: 1.0,
  enableSoundEffect: true,
  soundEffectVolume: 1.0,
  enableHeatDiffusion: true,
  heatDiffusionRange: 5.0,
  enableMineralEffect: true,
  mineralColor: new THREE.Color(0xc0a080)
});

// 添加到世界
world.addEntity(hotSpringEntity);
```

## 使用温泉预设

温泉预设提供了各种类型的温泉配置，可以快速创建不同类型的温泉。

```typescript
// 使用预设创建标准温泉
const standardHotSpringEntity = HotSpringPresets.createPreset(world, {
  type: HotSpringPresetType.STANDARD,
  position: new THREE.Vector3(0, 0, 0)
});

// 使用预设创建高温温泉
const highTemperatureHotSpringEntity = HotSpringPresets.createPreset(world, {
  type: HotSpringPresetType.HIGH_TEMPERATURE,
  position: new THREE.Vector3(20, 0, 0)
});

// 使用预设创建低温温泉
const lowTemperatureHotSpringEntity = HotSpringPresets.createPreset(world, {
  type: HotSpringPresetType.LOW_TEMPERATURE,
  position: new THREE.Vector3(-20, 0, 0)
});

// 使用预设创建硫磺温泉
const sulfurHotSpringEntity = HotSpringPresets.createPreset(world, {
  type: HotSpringPresetType.SULFUR,
  position: new THREE.Vector3(0, 0, 20)
});
```

## 自定义温泉属性

### 温度参数

```typescript
// 设置温度参数
hotSpringComponent.setTemperature(75);
```

### 波动参数

```typescript
// 设置波动参数
hotSpringComponent.setWaveAmplitude(0.15);
hotSpringComponent.setWaveFrequency(2.5);
hotSpringComponent.setWaveSpeed(0.7);
```

### 效果参数

```typescript
// 设置气泡效果
hotSpringComponent.setEnableBubbleEffect(true);
hotSpringComponent.setBubbleEffectStrength(1.2);

// 设置水蒸气效果
hotSpringComponent.setEnableSteamEffect(true);
hotSpringComponent.setSteamEffectStrength(1.5);

// 设置声音效果
hotSpringComponent.setEnableSoundEffect(true);
hotSpringComponent.setSoundEffectVolume(1.1);

// 设置热扩散效果
hotSpringComponent.setEnableHeatDiffusion(true);
hotSpringComponent.setHeatDiffusionRange(6.0);

// 设置矿物质效果
hotSpringComponent.setEnableMineralEffect(true);
hotSpringComponent.setMineralColor(new THREE.Color(0xd0d060));
```

## 温泉物理交互

温泉物理系统提供了多种物理交互效果，如波动、浮力等。

```typescript
// 配置水体物理系统
const waterPhysicsSystemConfig = {
  enabled: true,
  autoUpdate: true,
  enableBuoyancy: true,
  enableDrag: true,
  enableFlow: true,
  enableWaves: true,
  enableCollision: true,
  enableParticles: true,
  enableMultithreading: true,
  workerCount: 4
};

// 创建水体物理系统
const waterPhysicsSystem = new WaterPhysicsSystem(world, waterPhysicsSystemConfig);
world.addSystem(waterPhysicsSystem);
```

## 温泉粒子效果

温泉粒子系统提供了多种粒子效果，如气泡、水蒸气等。

```typescript
// 配置水下粒子系统
const underwaterParticleSystemConfig = {
  enabled: true,
  autoUpdate: true,
  maxParticles: 5000
};

// 创建水下粒子系统
const underwaterParticleSystem = new UnderwaterParticleSystem(world, underwaterParticleSystemConfig);
world.addSystem(underwaterParticleSystem);
```

## 温泉声音效果

温泉声音系统提供了温泉的声音效果。

```typescript
// 配置音频系统
const audioSystemConfig = {
  enabled: true,
  autoUpdate: true
};

// 创建音频系统
const audioSystem = new AudioSystem(world, audioSystemConfig);
world.addSystem(audioSystem);
```

## 温泉热效应

温泉热效应系统提供了温泉的热效应，如恢复生命值、添加状态效果等。

```typescript
// 获取温泉组件
const hotSpringComponent = hotSpringEntity.getComponent('HotSpringComponent') as HotSpringComponent;

// 设置温泉温度
hotSpringComponent.setTemperature(80);

// 设置热扩散范围
hotSpringComponent.setHeatDiffusionRange(7.0);

// 进入温泉的角色会受到热效应
// 在角色的更新方法中检查是否在温泉热扩散范围内
function updateCharacter(character, deltaTime) {
  // 获取角色位置
  const characterPosition = character.getPosition();

  // 获取温泉位置
  const hotSpringPosition = hotSpringComponent.getPosition();

  // 计算距离
  const distance = characterPosition.distanceTo(hotSpringPosition);

  // 如果在热扩散范围内，则应用热效应
  if (distance <= hotSpringComponent.getHeatDiffusionRange()) {
    // 计算热效应强度（距离越近，效果越强）
    const heatStrength = 1 - (distance / hotSpringComponent.getHeatDiffusionRange());

    // 应用热效应
    applyHeatEffect(character, heatStrength, deltaTime);
  }
}

// 应用热效应
function applyHeatEffect(character, strength, deltaTime) {
  // 获取角色的健康组件
  const healthComponent = character.getComponent('HealthComponent');
  if (healthComponent) {
    // 根据温泉温度和强度计算恢复量
    const recoveryAmount = strength * (hotSpringComponent.getTemperature() / 100) * deltaTime * 5;

    // 恢复生命值
    healthComponent.heal(recoveryAmount);
  }

  // 获取角色的状态组件
  const statusComponent = character.getComponent('StatusComponent');
  if (statusComponent) {
    // 添加"温暖"状态
    statusComponent.addStatus('warm', strength * 10, hotSpringComponent.getTemperature() / 10);
  }
}
```

## 矿物质效果

温泉矿物质效果系统提供了温泉的矿物质效果，如矿物质边缘等。

```typescript
// 获取温泉组件
const hotSpringComponent = hotSpringEntity.getComponent('HotSpringComponent') as HotSpringComponent;

// 设置矿物质效果
hotSpringComponent.setEnableMineralEffect(true);
hotSpringComponent.setMineralColor(new THREE.Color(0xd0d060));
```

## 性能优化

### 多线程计算

```typescript
// 启用多线程计算
waterPhysicsSystem.setConfig({
  enableMultithreading: true,
  workerCount: 4
});
```

### 自适应更新频率

```typescript
// 启用自适应更新频率
waterPhysicsSystem.setConfig({
  enableAdaptiveUpdate: true,
  minUpdateFrequency: 1,
  maxUpdateFrequency: 10
});
```

## 常见问题解答

### 如何创建多个温泉？

```typescript
// 创建多个温泉
const hotSpring1 = HotSpringPresets.createPreset(world, {
  type: HotSpringPresetType.STANDARD,
  position: new THREE.Vector3(0, 0, 0)
});

const hotSpring2 = HotSpringPresets.createPreset(world, {
  type: HotSpringPresetType.HIGH_TEMPERATURE,
  position: new THREE.Vector3(20, 0, 0)
});

const hotSpring3 = HotSpringPresets.createPreset(world, {
  type: HotSpringPresetType.SULFUR,
  position: new THREE.Vector3(-20, 0, 0)
});
```

### 如何调整温泉的大小？

```typescript
// 调整温泉的大小
const hotSpringEntity = HotSpringPresets.createPreset(world, {
  type: HotSpringPresetType.STANDARD,
  width: 15,
  height: 1.5,
  depth: 15,
  position: new THREE.Vector3(0, 0, 0)
});
```

### 如何调整温泉的颜色？

```typescript
// 调整温泉的颜色
const hotSpringEntity = HotSpringPresets.createPreset(world, {
  type: HotSpringPresetType.STANDARD,
  color: new THREE.Color(0x99ccff),
  opacity: 0.7,
  position: new THREE.Vector3(0, 0, 0)
});
```
