"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SceneLayerManager = void 0;
var SceneLayer_1 = require("./SceneLayer");
var EventEmitter_1 = require("../utils/EventEmitter");
/**
 * 场景图层管理器
 */
var SceneLayerManager = /** @class */ (function (_super) {
    __extends(SceneLayerManager, _super);
    /**
     * 创建场景图层管理器
     * @param scene 所属场景
     * @param options 选项
     */
    function SceneLayerManager(scene, options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this) || this;
        /** 图层映射 */
        _this.layers = new Map();
        /** 默认图层 */
        _this.defaultLayer = null;
        /** 是否已初始化 */
        _this.initialized = false;
        _this.scene = scene;
        // 是否自动创建默认图层
        var createDefaultLayers = options.createDefaultLayers !== undefined ? options.createDefaultLayers : true;
        if (createDefaultLayers) {
            _this.createDefaultLayers(options.defaultLayerCount || 1);
        }
        return _this;
    }
    /**
     * 初始化图层管理器
     */
    SceneLayerManager.prototype.initialize = function () {
        if (this.initialized) {
            return;
        }
        // 初始化所有图层
        for (var _i = 0, _a = Array.from(this.layers.values()); _i < _a.length; _i++) {
            var layer = _a[_i];
            layer.initialize();
        }
        this.initialized = true;
        this.emit('initialized');
    };
    /**
     * 创建默认图层
     * @param count 默认图层数量
     */
    SceneLayerManager.prototype.createDefaultLayers = function (count) {
        // 创建默认图层组
        var rootGroup = this.createLayer({
            id: 'root',
            name: '根图层组',
            type: SceneLayer_1.SceneLayerType.GROUP,
            visible: true,
            locked: false,
            order: 0,
            expanded: true
        });
        // 创建默认图层
        this.defaultLayer = this.createLayer({
            id: 'default',
            name: '默认图层',
            visible: true,
            locked: false,
            order: 0,
            parentId: 'root'
        });
        // 创建额外的默认图层
        for (var i = 1; i < count; i++) {
            this.createLayer({
                id: "layer_".concat(i),
                name: "\u56FE\u5C42 ".concat(i),
                visible: true,
                locked: false,
                order: i,
                parentId: 'root'
            });
        }
    };
    /**
     * 创建图层
     * @param options 图层选项
     * @returns 创建的图层
     */
    SceneLayerManager.prototype.createLayer = function (options) {
        // 检查图层ID是否已存在
        if (this.layers.has(options.id)) {
            throw new Error("\u56FE\u5C42ID\u5DF2\u5B58\u5728: ".concat(options.id));
        }
        // 创建图层
        var layer = new SceneLayer_1.SceneLayer(this.scene, options);
        // 添加到图层映射
        this.layers.set(options.id, layer);
        // 处理父子关系
        if (options.parentId) {
            var parentLayer = this.getLayer(options.parentId);
            if (parentLayer) {
                // 如果父图层不是组，则将其转换为组
                if (!parentLayer.isGroup()) {
                    parentLayer.setType(SceneLayer_1.SceneLayerType.GROUP);
                }
                // 添加到父图层的子图层列表
                parentLayer.addChild(options.id);
            }
            else {
                console.warn("\u7236\u56FE\u5C42\u4E0D\u5B58\u5728: ".concat(options.parentId));
            }
        }
        // 监听图层事件
        this.setupLayerEvents(layer);
        // 发出图层创建事件
        this.emit('layerCreated', layer);
        return layer;
    };
    /**
     * 设置图层事件监听
     * @param layer 图层
     */
    SceneLayerManager.prototype.setupLayerEvents = function (layer) {
        var _this = this;
        // 监听图层可见性变化事件
        layer.on('visibilityChanged', function (visible) {
            _this.emit('layerVisibilityChanged', layer, visible);
            // 如果是图层组，同步更新所有子图层的可见性
            if (layer.isGroup()) {
                _this.updateChildrenVisibility(layer.id, visible);
            }
        });
        // 监听图层锁定状态变化事件
        layer.on('lockChanged', function (locked) {
            _this.emit('layerLockChanged', layer, locked);
            // 如果是图层组，同步更新所有子图层的锁定状态
            if (layer.isGroup()) {
                _this.updateChildrenLock(layer.id, locked);
            }
        });
        // 监听图层顺序变化事件
        layer.on('orderChanged', function (order) {
            _this.emit('layerOrderChanged', layer, order);
        });
        // 监听图层实体添加事件
        layer.on('entityAdded', function (entity) {
            _this.emit('layerEntityAdded', layer, entity);
        });
        // 监听图层实体移除事件
        layer.on('entityRemoved', function (entity) {
            _this.emit('layerEntityRemoved', layer, entity);
        });
        // 监听图层类型变化事件
        layer.on('typeChanged', function (type) {
            _this.emit('layerTypeChanged', layer, type);
        });
        // 监听图层父图层变化事件
        layer.on('parentChanged', function (parentId, oldParentId) {
            // 从旧父图层中移除
            if (oldParentId) {
                var oldParent = _this.getLayer(oldParentId);
                if (oldParent) {
                    oldParent.removeChild(layer.id);
                }
            }
            // 添加到新父图层
            if (parentId) {
                var newParent = _this.getLayer(parentId);
                if (newParent) {
                    // 如果新父图层不是组，则将其转换为组
                    if (!newParent.isGroup()) {
                        newParent.setType(SceneLayer_1.SceneLayerType.GROUP);
                    }
                    newParent.addChild(layer.id);
                }
            }
            _this.emit('layerParentChanged', layer, parentId, oldParentId);
        });
        // 监听子图层添加事件
        layer.on('childAdded', function (childId) {
            _this.emit('layerChildAdded', layer, childId);
        });
        // 监听子图层移除事件
        layer.on('childRemoved', function (childId) {
            _this.emit('layerChildRemoved', layer, childId);
        });
        // 监听展开状态变化事件
        layer.on('expandedChanged', function (expanded) {
            _this.emit('layerExpandedChanged', layer, expanded);
        });
    };
    /**
     * 获取图层
     * @param id 图层ID
     * @returns 图层实例
     */
    SceneLayerManager.prototype.getLayer = function (id) {
        return this.layers.get(id) || null;
    };
    /**
     * 获取默认图层
     * @returns 默认图层
     */
    SceneLayerManager.prototype.getDefaultLayer = function () {
        return this.defaultLayer;
    };
    /**
     * 获取所有图层
     * @returns 图层数组
     */
    SceneLayerManager.prototype.getLayers = function () {
        return Array.from(this.layers.values());
    };
    /**
     * 获取根图层
     * @returns 根图层数组（没有父图层的图层）
     */
    SceneLayerManager.prototype.getRootLayers = function () {
        return Array.from(this.layers.values()).filter(function (layer) { return !layer.getParentId(); });
    };
    /**
     * 获取图层的子图层
     * @param layerId 图层ID
     * @returns 子图层数组
     */
    SceneLayerManager.prototype.getChildLayers = function (layerId) {
        var layer = this.getLayer(layerId);
        if (!layer) {
            return [];
        }
        var childrenIds = layer.getChildrenIds();
        var children = [];
        for (var _i = 0, childrenIds_1 = childrenIds; _i < childrenIds_1.length; _i++) {
            var childId = childrenIds_1[_i];
            var childLayer = this.getLayer(childId);
            if (childLayer) {
                children.push(childLayer);
            }
        }
        return children;
    };
    /**
     * 更新子图层的可见性
     * @param layerId 图层ID
     * @param visible 是否可见
     */
    SceneLayerManager.prototype.updateChildrenVisibility = function (layerId, visible) {
        var children = this.getChildLayers(layerId);
        for (var _i = 0, children_1 = children; _i < children_1.length; _i++) {
            var child = children_1[_i];
            child.setVisible(visible);
            // 递归更新子图层
            if (child.isGroup()) {
                this.updateChildrenVisibility(child.id, visible);
            }
        }
    };
    /**
     * 更新子图层的锁定状态
     * @param layerId 图层ID
     * @param locked 是否锁定
     */
    SceneLayerManager.prototype.updateChildrenLock = function (layerId, locked) {
        var children = this.getChildLayers(layerId);
        for (var _i = 0, children_2 = children; _i < children_2.length; _i++) {
            var child = children_2[_i];
            child.setLocked(locked);
            // 递归更新子图层
            if (child.isGroup()) {
                this.updateChildrenLock(child.id, locked);
            }
        }
    };
    /**
     * 创建图层组
     * @param name 组名称
     * @param parentId 父图层ID
     * @returns 创建的图层组
     */
    SceneLayerManager.prototype.createLayerGroup = function (name, parentId) {
        return this.createLayer({
            id: "group_".concat(Date.now()),
            name: name,
            type: SceneLayer_1.SceneLayerType.GROUP,
            visible: true,
            locked: false,
            order: this.layers.size,
            parentId: parentId,
            expanded: true
        });
    };
    /**
     * 获取图层数量
     * @returns 图层数量
     */
    SceneLayerManager.prototype.getLayerCount = function () {
        return this.layers.size;
    };
    /**
     * 查询图层
     * @param options 查询选项
     * @returns 匹配的图层数组
     */
    SceneLayerManager.prototype.queryLayers = function (options) {
        if (options === void 0) { options = {}; }
        // 合并选项
        var mergedOptions = {
            includeInvisible: options.includeInvisible !== undefined ? options.includeInvisible : false,
            includeLocked: options.includeLocked !== undefined ? options.includeLocked : true,
            tagFilter: options.tagFilter || [],
            nameFilter: options.nameFilter,
            customFilter: options.customFilter
        };
        // 过滤图层
        return Array.from(this.layers.values()).filter(function (layer) {
            // 检查可见性
            if (!mergedOptions.includeInvisible && !layer.isVisible()) {
                return false;
            }
            // 检查锁定状态
            if (!mergedOptions.includeLocked && layer.isLocked()) {
                return false;
            }
            // 检查标签过滤器
            if (mergedOptions.tagFilter && mergedOptions.tagFilter.length > 0) {
                var hasAnyTag = mergedOptions.tagFilter.some(function (tag) { return layer.hasTag(tag); });
                if (!hasAnyTag) {
                    return false;
                }
            }
            // 检查名称过滤器
            if (mergedOptions.nameFilter) {
                if (typeof mergedOptions.nameFilter === 'string') {
                    if (!layer.name.includes(mergedOptions.nameFilter)) {
                        return false;
                    }
                }
                else if (mergedOptions.nameFilter instanceof RegExp) {
                    if (!mergedOptions.nameFilter.test(layer.name)) {
                        return false;
                    }
                }
            }
            // 检查自定义过滤器
            if (mergedOptions.customFilter && !mergedOptions.customFilter(layer)) {
                return false;
            }
            return true;
        });
    };
    /**
     * 添加实体到图层
     * @param entity 实体
     * @param layerId 图层ID
     * @returns 是否成功添加
     */
    SceneLayerManager.prototype.addEntityToLayer = function (entity, layerId) {
        var layer = this.getLayer(layerId);
        if (!layer) {
            return false;
        }
        return layer.addEntity(entity);
    };
    /**
     * 从图层中移除实体
     * @param entity 实体
     * @param layerId 图层ID
     * @returns 是否成功移除
     */
    SceneLayerManager.prototype.removeEntityFromLayer = function (entity, layerId) {
        var layer = this.getLayer(layerId);
        if (!layer) {
            return false;
        }
        return layer.removeEntity(entity);
    };
    /**
     * 移除图层
     * @param id 图层ID
     * @param removeChildren 是否同时移除子图层
     * @returns 是否成功移除
     */
    SceneLayerManager.prototype.removeLayer = function (id, removeChildren) {
        if (removeChildren === void 0) { removeChildren = true; }
        // 不能移除默认图层和根图层组
        if ((this.defaultLayer && id === this.defaultLayer.id) || id === 'root') {
            return false;
        }
        var layer = this.getLayer(id);
        if (!layer) {
            return false;
        }
        // 处理子图层
        if (layer.isGroup() && layer.hasChildren()) {
            var childrenIds = __spreadArray([], layer.getChildrenIds(), true); // 创建副本，因为在循环中会修改集合
            if (removeChildren) {
                // 递归移除所有子图层
                for (var _i = 0, childrenIds_2 = childrenIds; _i < childrenIds_2.length; _i++) {
                    var childId = childrenIds_2[_i];
                    this.removeLayer(childId, true);
                }
            }
            else {
                // 将子图层移动到父图层
                var parentId_1 = layer.getParentId();
                for (var _a = 0, childrenIds_3 = childrenIds; _a < childrenIds_3.length; _a++) {
                    var childId = childrenIds_3[_a];
                    var childLayer = this.getLayer(childId);
                    if (childLayer) {
                        childLayer.setParentId(parentId_1);
                    }
                }
            }
        }
        // 从父图层中移除
        var parentId = layer.getParentId();
        if (parentId) {
            var parentLayer = this.getLayer(parentId);
            if (parentLayer) {
                parentLayer.removeChild(id);
            }
        }
        // 销毁图层
        layer.dispose();
        // 从图层映射中移除
        this.layers.delete(id);
        // 发出图层移除事件
        this.emit('layerRemoved', id);
        return true;
    };
    /**
     * 清空所有图层
     */
    SceneLayerManager.prototype.clearLayers = function () {
        // 保存默认图层
        var defaultLayer = this.defaultLayer;
        // 清空前发出事件
        this.emit('beforeClearLayers');
        // 销毁所有图层
        for (var _i = 0, _a = Array.from(this.layers.values()); _i < _a.length; _i++) {
            var layer = _a[_i];
            layer.dispose();
        }
        // 清空图层映射
        this.layers.clear();
        // 恢复默认图层
        if (defaultLayer) {
            this.layers.set(defaultLayer.id, defaultLayer);
            this.defaultLayer = defaultLayer;
        }
        // 发出清空事件
        this.emit('layersCleared');
    };
    /**
     * 销毁图层管理器
     */
    SceneLayerManager.prototype.dispose = function () {
        // 清空所有图层
        this.clearLayers();
        // 移除所有事件监听器
        this.removeAllListeners();
        this.initialized = false;
    };
    return SceneLayerManager;
}(EventEmitter_1.EventEmitter));
exports.SceneLayerManager = SceneLayerManager;
