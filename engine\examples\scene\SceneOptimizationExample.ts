/**
 * 场景优化示例
 * 展示如何使用LOD、视锥体剔除、实例化渲染和批处理等优化技术
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Transform } from '../../src/scene/Transform';
import { Camera } from '../../src/rendering/Camera';
import { Light, LightType } from '../../src/rendering/Light';
import {
  LODSystem,
  LODComponent,
  LODLevel,
  FrustumCullingSystem,
  CullableComponent,
  BatchingSystem
} from '../../src/rendering/optimization';
import { SceneManager } from '../../src/scene/SceneManager';
import { SceneSerializer } from '../../src/scene/SceneSerializer';
import { SceneFormatConverter, SceneFormat } from '../../src/scene/io/SceneFormatConverter';
import { SceneMerger } from '../../src/scene/io/SceneMerger';
import { AssetManager } from '../../src/assets/AssetManager';
import { Debug } from '../../src/utils/Debug';

/**
 * 场景优化示例
 */
export class SceneOptimizationExample {
  /** 引擎实例 */
  private engine: Engine;

  /** 场景管理器 */
  private sceneManager: SceneManager;

  /** 场景序列化器 */
  private sceneSerializer: SceneSerializer;

  /** 场景格式转换器 */
  private sceneFormatConverter: SceneFormatConverter;

  /** 场景合并器 */
  private sceneMerger: SceneMerger;

  /** 资产管理器 */
  private assetManager: AssetManager;

  /** LOD系统 */
  private lodSystem: LODSystem;

  /** 视锥体剔除系统 */
  private frustumCullingSystem: FrustumCullingSystem;

  /** 批处理系统 */
  private batchingSystem: BatchingSystem;

  /** 场景列表 */
  private scenes: Scene[] = [];

  /** 当前场景索引 */
  private currentSceneIndex: number = 0;

  /** UI容器 */
  private ui: HTMLElement;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建场景优化示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();

    // 创建资产管理器
    this.assetManager = new AssetManager();

    // 创建场景管理器
    this.sceneManager = new SceneManager({
      world: this.engine.getWorld(),
      assetManager: this.assetManager,
      enableSceneCache: true,
      maxSceneCacheCount: 5
    });

    // 创建场景序列化器
    this.sceneSerializer = new SceneSerializer(this.engine.getWorld());

    // 创建场景格式转换器
    this.sceneFormatConverter = new SceneFormatConverter(this.sceneSerializer);

    // 创建场景合并器
    this.sceneMerger = new SceneMerger(this.engine.getWorld());

    // 创建LOD系统
    this.lodSystem = new LODSystem({
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      useFrustumCheck: true,
      useDistanceCheck: true,
      useAutoLODGeneration: true
    });

    // 创建视锥体剔除系统
    this.frustumCullingSystem = new FrustumCullingSystem({
      enabled: true,
      autoUpdate: true,
      updateFrequency: 1,
      useBoundingBoxCulling: true,
      useBoundingSphereCulling: true
    });

    // 创建批处理系统
    this.batchingSystem = new BatchingSystem({
      enabled: true,
      autoUpdate: true,
      useStaticBatching: true,
      useDynamicBatching: true,
      useGPUInstancing: true
    });

    // 添加系统到引擎
    this.engine.getWorld().addSystem(this.lodSystem);
    this.engine.getWorld().addSystem(this.frustumCullingSystem);
    this.engine.getWorld().addSystem(this.batchingSystem);

    // 创建UI
    this.ui = this.createUI();
  }

  /**
   * 初始化示例
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    // 创建场景
    await this.createScenes();

    // 加载第一个场景
    this.loadScene(0);

    // 启动引擎
    this.engine.start();

    this.initialized = true;
  }

  /**
   * 创建场景
   */
  private async createScenes(): Promise<void> {
    // 创建场景1：LOD示例场景
    const lodScene = this.createLODScene();
    this.scenes.push(lodScene);

    // 创建场景2：视锥体剔除示例场景
    const frustumCullingScene = this.createFrustumCullingScene();
    this.scenes.push(frustumCullingScene);

    // 创建场景3：批处理示例场景
    const batchingScene = this.createBatchingScene();
    this.scenes.push(batchingScene);

    // 创建场景4：合并场景示例
    const mergedScene = this.createMergedScene();
    this.scenes.push(mergedScene);
  }

  /**
   * 创建LOD示例场景
   */
  private createLODScene(): Scene {
    // 创建场景
    const scene = this.engine.getWorld().createScene('LOD示例场景');

    // 设置天空盒
    scene.setSkybox('color', new THREE.Color(0x87CEEB));

    // 设置环境光
    scene.setAmbientLight(new THREE.Color(0xffffff), 0.5);

    // 创建相机
    const cameraEntity = new Entity('相机');
    cameraEntity.getTransform().setPosition(0, 5, 10);
    cameraEntity.getTransform().lookAt(new THREE.Vector3(0, 0, 0));
    const camera = new Camera();
    cameraEntity.addComponent(camera);
    scene.addEntity(cameraEntity);

    // 创建灯光
    const lightEntity = new Entity('灯光');
    lightEntity.getTransform().setPosition(5, 10, 5);
    const light = new Light(LightType.DIRECTIONAL);
    light.setIntensity(1.0);
    light.setCastShadow(true);
    lightEntity.addComponent(light);
    scene.addEntity(lightEntity);

    // 创建地面
    const ground = new Entity('地面');
    ground.getTransform().setPosition(0, -1, 0);
    ground.getTransform().setScale(50, 0.1, 50);
    const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
    const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x808080 });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.receiveShadow = true;
    ground.addComponent('MeshComponent', { mesh: groundMesh });
    scene.addEntity(ground);

    // 创建LOD模型
    for (let i = 0; i < 10; i++) {
      const x = Math.random() * 40 - 20;
      const z = Math.random() * 40 - 20;

      const entity = new Entity(`LOD模型_${i}`);
      entity.getTransform().setPosition(x, 0, z);

      // 创建不同细节级别的网格
      const highDetailGeometry = new THREE.SphereGeometry(1, 32, 32);
      const mediumDetailGeometry = new THREE.SphereGeometry(1, 16, 16);
      const lowDetailGeometry = new THREE.SphereGeometry(1, 8, 8);
      const veryLowDetailGeometry = new THREE.SphereGeometry(1, 4, 4);

      const material = new THREE.MeshStandardMaterial({ color: 0x00ff00 });

      const highDetailMesh = new THREE.Mesh(highDetailGeometry, material.clone());
      const mediumDetailMesh = new THREE.Mesh(mediumDetailGeometry, material.clone());
      const lowDetailMesh = new THREE.Mesh(lowDetailGeometry, material.clone());
      const veryLowDetailMesh = new THREE.Mesh(veryLowDetailGeometry, material.clone());

      highDetailMesh.castShadow = true;
      mediumDetailMesh.castShadow = true;
      lowDetailMesh.castShadow = true;
      veryLowDetailMesh.castShadow = true;

      // 创建LOD组件
      const lodComponent = new LODComponent();

      // 添加LOD级别
      lodComponent.addLevel({
        level: LODLevel.HIGH,
        distance: 5,
        mesh: highDetailMesh,
        visible: true
      });

      lodComponent.addLevel({
        level: LODLevel.MEDIUM,
        distance: 10,
        mesh: mediumDetailMesh,
        visible: false
      });

      lodComponent.addLevel({
        level: LODLevel.LOW,
        distance: 20,
        mesh: lowDetailMesh,
        visible: false
      });

      lodComponent.addLevel({
        level: LODLevel.VERY_LOW,
        distance: 30,
        mesh: veryLowDetailMesh,
        visible: false
      });

      // 添加LOD组件到实体
      entity.addComponent(lodComponent);

      // 添加实体到场景
      scene.addEntity(entity);
    }

    return scene;
  }

  /**
   * 创建视锥体剔除示例场景
   */
  private createFrustumCullingScene(): Scene {
    // 创建场景
    const scene = this.engine.getWorld().createScene('视锥体剔除示例场景');

    // 设置天空盒
    scene.setSkybox('color', new THREE.Color(0x87CEEB));

    // 设置环境光
    scene.setAmbientLight(new THREE.Color(0xffffff), 0.5);

    // 创建相机
    const cameraEntity = new Entity('相机');
    cameraEntity.getTransform().setPosition(0, 5, 10);
    cameraEntity.getTransform().lookAt(new THREE.Vector3(0, 0, 0));
    const camera = new Camera();
    cameraEntity.addComponent(camera);
    scene.addEntity(cameraEntity);

    // 创建灯光
    const lightEntity = new Entity('灯光');
    lightEntity.getTransform().setPosition(5, 10, 5);
    const light = new Light(LightType.DIRECTIONAL);
    light.setIntensity(1.0);
    light.setCastShadow(true);
    lightEntity.addComponent(light);
    scene.addEntity(lightEntity);

    // 创建地面
    const ground = new Entity('地面');
    ground.getTransform().setPosition(0, -1, 0);
    ground.getTransform().setScale(100, 0.1, 100);
    const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
    const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x808080 });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.receiveShadow = true;
    ground.addComponent('MeshComponent', { mesh: groundMesh });
    scene.addEntity(ground);

    // 创建大量可剔除对象
    for (let i = 0; i < 1000; i++) {
      const x = Math.random() * 100 - 50;
      const z = Math.random() * 100 - 50;

      const entity = new Entity(`可剔除对象_${i}`);
      entity.getTransform().setPosition(x, 0, z);

      // 创建网格
      const geometry = new THREE.BoxGeometry(1, 1, 1);
      const material = new THREE.MeshStandardMaterial({ color: 0xff0000 });
      const mesh = new THREE.Mesh(geometry, material);
      mesh.castShadow = true;

      entity.addComponent('MeshComponent', { mesh });

      // 创建可剔除组件
      const cullableComponent = new CullableComponent({
        autoComputeBoundingRadius: true,
        useBoundingBox: true,
        autoComputeBoundingBox: true,
        visible: true,
        cullable: true
      });

      entity.addComponent(cullableComponent);

      // 添加实体到场景
      scene.addEntity(entity);
    }

    return scene;
  }

  /**
   * 创建批处理示例场景
   */
  private createBatchingScene(): Scene {
    // 创建场景
    const scene = this.engine.getWorld().createScene('批处理示例场景');

    // 设置天空盒
    scene.setSkybox('color', new THREE.Color(0x87CEEB));

    // 设置环境光
    scene.setAmbientLight(new THREE.Color(0xffffff), 0.5);

    // 创建相机
    const cameraEntity = new Entity('相机');
    cameraEntity.getTransform().setPosition(0, 5, 10);
    cameraEntity.getTransform().lookAt(new THREE.Vector3(0, 0, 0));
    const camera = new Camera();
    cameraEntity.addComponent(camera);
    scene.addEntity(cameraEntity);

    // 创建灯光
    const lightEntity = new Entity('灯光');
    lightEntity.getTransform().setPosition(5, 10, 5);
    const light = new Light(LightType.DIRECTIONAL);
    light.setIntensity(1.0);
    light.setCastShadow(true);
    lightEntity.addComponent(light);
    scene.addEntity(lightEntity);

    // 创建地面
    const ground = new Entity('地面');
    ground.getTransform().setPosition(0, -1, 0);
    ground.getTransform().setScale(50, 0.1, 50);
    const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
    const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x808080 });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.receiveShadow = true;
    ground.addComponent('MeshComponent', { mesh: groundMesh });
    scene.addEntity(ground);

    // 创建静态批处理组
    const staticEntities: Entity[] = [];

    // 创建静态对象
    for (let i = 0; i < 100; i++) {
      const x = Math.random() * 40 - 20;
      const z = Math.random() * 40 - 20;

      const entity = new Entity(`静态对象_${i}`);
      entity.getTransform().setPosition(x, 0, z);

      // 创建网格
      const geometry = new THREE.BoxGeometry(1, 1, 1);
      const material = new THREE.MeshStandardMaterial({ color: 0x0000ff });
      const mesh = new THREE.Mesh(geometry, material);
      mesh.castShadow = true;

      entity.addComponent('MeshComponent', { mesh });

      // 添加实体到场景
      scene.addEntity(entity);

      // 添加到静态实体列表
      staticEntities.push(entity);
    }

    // 创建动态批处理组
    const dynamicEntities: Entity[] = [];

    // 创建动态对象
    for (let i = 0; i < 100; i++) {
      const x = Math.random() * 40 - 20;
      const z = Math.random() * 40 - 20;

      const entity = new Entity(`动态对象_${i}`);
      entity.getTransform().setPosition(x, 0, z);

      // 创建网格
      const geometry = new THREE.SphereGeometry(0.5, 16, 16);
      const material = new THREE.MeshStandardMaterial({ color: 0xff00ff });
      const mesh = new THREE.Mesh(geometry, material);
      mesh.castShadow = true;

      entity.addComponent('MeshComponent', { mesh });

      // 添加实体到场景
      scene.addEntity(entity);

      // 添加到动态实体列表
      dynamicEntities.push(entity);
    }

    // 创建批处理组
    this.batchingSystem.setActiveScene(scene);
    this.batchingSystem.createStaticBatch(staticEntities, '静态批处理组');
    this.batchingSystem.createDynamicBatch(dynamicEntities, '动态批处理组');

    return scene;
  }

  /**
   * 创建合并场景示例
   */
  private createMergedScene(): Scene {
    // 创建场景
    const scene = this.engine.getWorld().createScene('合并场景示例');

    // 设置天空盒
    scene.setSkybox('color', new THREE.Color(0x87CEEB));

    // 设置环境光
    scene.setAmbientLight(new THREE.Color(0xffffff), 0.5);

    // 创建相机
    const cameraEntity = new Entity('相机');
    cameraEntity.getTransform().setPosition(0, 5, 10);
    cameraEntity.getTransform().lookAt(new THREE.Vector3(0, 0, 0));
    const camera = new Camera();
    cameraEntity.addComponent(camera);
    scene.addEntity(cameraEntity);

    // 创建灯光
    const lightEntity = new Entity('灯光');
    lightEntity.getTransform().setPosition(5, 10, 5);
    const light = new Light(LightType.DIRECTIONAL);
    light.setIntensity(1.0);
    light.setCastShadow(true);
    lightEntity.addComponent(light);
    scene.addEntity(lightEntity);

    // 创建地面
    const ground = new Entity('地面');
    ground.getTransform().setPosition(0, -1, 0);
    ground.getTransform().setScale(50, 0.1, 50);
    const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
    const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x808080 });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.receiveShadow = true;
    ground.addComponent('MeshComponent', { mesh: groundMesh });
    scene.addEntity(ground);

    return scene;
  }

  /**
   * 加载场景
   * @param index 场景索引
   */
  private loadScene(index: number): void {
    if (index < 0 || index >= this.scenes.length) {
      Debug.error(`场景索引 ${index} 超出范围`);
      return;
    }

    // 获取场景
    const scene = this.scenes[index];

    // 设置当前场景
    this.currentSceneIndex = index;

    // 设置活跃场景
    this.engine.getWorld().setActiveScene(scene);

    // 设置LOD系统的活跃场景
    this.lodSystem.setActiveScene(scene);

    // 设置视锥体剔除系统的活跃场景
    this.frustumCullingSystem.setActiveScene(scene);

    // 设置批处理系统的活跃场景
    this.batchingSystem.setActiveScene(scene);

    // 更新UI
    this.updateUI();

    Debug.log(`已加载场景: ${scene.getName()}`);
  }

  /**
   * 创建UI
   */
  private createUI(): HTMLElement {
    // 创建UI容器
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.top = '10px';
    container.style.left = '10px';
    container.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    container.style.color = 'white';
    container.style.padding = '10px';
    container.style.borderRadius = '5px';
    container.style.fontFamily = 'Arial, sans-serif';
    container.style.zIndex = '1000';

    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '场景优化示例';
    title.style.margin = '0 0 10px 0';
    container.appendChild(title);

    // 创建场景选择器
    const sceneSelector = document.createElement('div');
    sceneSelector.style.marginBottom = '10px';

    const sceneLabel = document.createElement('label');
    sceneLabel.textContent = '场景: ';
    sceneSelector.appendChild(sceneLabel);

    const sceneSelect = document.createElement('select');
    sceneSelect.id = 'scene-select';
    sceneSelect.style.marginLeft = '5px';
    sceneSelect.style.padding = '5px';
    sceneSelect.style.backgroundColor = '#333';
    sceneSelect.style.color = 'white';
    sceneSelect.style.border = '1px solid #555';

    // 添加场景选项
    const sceneOptions = [
      '1. LOD示例场景',
      '2. 视锥体剔除示例场景',
      '3. 批处理示例场景',
      '4. 合并场景示例'
    ];

    for (let i = 0; i < sceneOptions.length; i++) {
      const option = document.createElement('option');
      option.value = i.toString();
      option.textContent = sceneOptions[i];
      sceneSelect.appendChild(option);
    }

    sceneSelect.addEventListener('change', () => {
      const index = parseInt(sceneSelect.value);
      this.loadScene(index);
    });

    sceneSelector.appendChild(sceneSelect);
    container.appendChild(sceneSelector);

    // 创建LOD控制
    const lodControl = document.createElement('div');
    lodControl.style.marginBottom = '10px';

    const lodLabel = document.createElement('label');
    lodLabel.textContent = 'LOD系统: ';
    lodControl.appendChild(lodLabel);

    const lodCheckbox = document.createElement('input');
    lodCheckbox.type = 'checkbox';
    lodCheckbox.id = 'lod-checkbox';
    lodCheckbox.checked = this.lodSystem.isEnabled();
    lodCheckbox.addEventListener('change', () => {
      this.lodSystem.setEnabled(lodCheckbox.checked);
    });

    lodControl.appendChild(lodCheckbox);
    container.appendChild(lodControl);

    // 创建视锥体剔除控制
    const cullingControl = document.createElement('div');
    cullingControl.style.marginBottom = '10px';

    const cullingLabel = document.createElement('label');
    cullingLabel.textContent = '视锥体剔除: ';
    cullingControl.appendChild(cullingLabel);

    const cullingCheckbox = document.createElement('input');
    cullingCheckbox.type = 'checkbox';
    cullingCheckbox.id = 'culling-checkbox';
    cullingCheckbox.checked = this.frustumCullingSystem.isEnabled();
    cullingCheckbox.addEventListener('change', () => {
      this.frustumCullingSystem.setEnabled(cullingCheckbox.checked);
    });

    cullingControl.appendChild(cullingCheckbox);
    container.appendChild(cullingControl);

    // 创建批处理控制
    const batchingControl = document.createElement('div');
    batchingControl.style.marginBottom = '10px';

    const batchingLabel = document.createElement('label');
    batchingLabel.textContent = '批处理: ';
    batchingControl.appendChild(batchingLabel);

    const batchingCheckbox = document.createElement('input');
    batchingCheckbox.type = 'checkbox';
    batchingCheckbox.id = 'batching-checkbox';
    batchingCheckbox.checked = this.batchingSystem.isEnabled();
    batchingCheckbox.addEventListener('change', () => {
      this.batchingSystem.setEnabled(batchingCheckbox.checked);
    });

    batchingControl.appendChild(batchingCheckbox);
    container.appendChild(batchingControl);

    // 创建场景合并按钮
    const mergeButton = document.createElement('button');
    mergeButton.textContent = '合并场景';
    mergeButton.style.padding = '5px 10px';
    mergeButton.style.backgroundColor = '#555';
    mergeButton.style.color = 'white';
    mergeButton.style.border = '1px solid #777';
    mergeButton.style.borderRadius = '3px';
    mergeButton.style.cursor = 'pointer';
    mergeButton.style.marginRight = '10px';

    mergeButton.addEventListener('click', () => {
      this.mergeScenes();
    });

    container.appendChild(mergeButton);

    // 创建场景导出按钮
    const exportButton = document.createElement('button');
    exportButton.textContent = '导出场景';
    exportButton.style.padding = '5px 10px';
    exportButton.style.backgroundColor = '#555';
    exportButton.style.color = 'white';
    exportButton.style.border = '1px solid #777';
    exportButton.style.borderRadius = '3px';
    exportButton.style.cursor = 'pointer';

    exportButton.addEventListener('click', () => {
      this.exportScene();
    });

    container.appendChild(exportButton);

    // 创建统计信息
    const stats = document.createElement('div');
    stats.id = 'stats';
    stats.style.marginTop = '10px';
    stats.style.padding = '5px';
    stats.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    stats.style.borderRadius = '3px';
    container.appendChild(stats);

    // 添加到文档
    document.body.appendChild(container);

    return container;
  }

  /**
   * 更新UI
   */
  private updateUI(): void {
    // 更新场景选择器
    const sceneSelect = document.getElementById('scene-select') as HTMLSelectElement;
    if (sceneSelect) {
      sceneSelect.value = this.currentSceneIndex.toString();
    }

    // 更新LOD控制
    const lodCheckbox = document.getElementById('lod-checkbox') as HTMLInputElement;
    if (lodCheckbox) {
      lodCheckbox.checked = this.lodSystem.isEnabled();
    }

    // 更新视锥体剔除控制
    const cullingCheckbox = document.getElementById('culling-checkbox') as HTMLInputElement;
    if (cullingCheckbox) {
      cullingCheckbox.checked = this.frustumCullingSystem.isEnabled();
    }

    // 更新批处理控制
    const batchingCheckbox = document.getElementById('batching-checkbox') as HTMLInputElement;
    if (batchingCheckbox) {
      batchingCheckbox.checked = this.batchingSystem.isEnabled();
    }

    // 更新统计信息
    this.updateStats();
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    const stats = document.getElementById('stats');
    if (!stats) {
      return;
    }

    const scene = this.scenes[this.currentSceneIndex];
    if (!scene) {
      return;
    }

    const entityCount = scene.getEntities().length;
    const threeScene = scene.getThreeScene();

    // 计算渲染对象数量
    let renderableCount = 0;
    threeScene.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        renderableCount++;
      }
    });

    // 计算三角形数量
    let triangleCount = 0;
    threeScene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.geometry) {
        if (object.geometry instanceof THREE.BufferGeometry) {
          if (object.geometry.index) {
            triangleCount += object.geometry.index.count / 3;
          } else if (object.geometry.attributes.position) {
            triangleCount += object.geometry.attributes.position.count / 3;
          }
        }
      }
    });

    // 获取LOD统计信息
    const lodStats = this.lodSystem.getStats();

    // 获取视锥体剔除统计信息
    const cullingStats = this.frustumCullingSystem.getStats();

    // 构建统计信息HTML
    let statsHtml = `
      <div>实体数量: ${entityCount}</div>
      <div>渲染对象数量: ${renderableCount}</div>
      <div>三角形数量: ${Math.round(triangleCount)}</div>
      <div>LOD组件数量: ${lodStats.componentCount}</div>
      <div>可见LOD级别: ${lodStats.visibleLevels.high} 高, ${lodStats.visibleLevels.medium} 中, ${lodStats.visibleLevels.low} 低, ${lodStats.visibleLevels.veryLow} 极低</div>
      <div>剔除对象数量: ${cullingStats.culledCount} / ${cullingStats.totalCount}</div>
    `;

    stats.innerHTML = statsHtml;
  }

  /**
   * 合并场景
   */
  private mergeScenes(): void {
    // 检查是否有足够的场景
    if (this.scenes.length < 2) {
      Debug.error('没有足够的场景可合并');
      return;
    }

    // 获取要合并的场景
    const scenesToMerge = [this.scenes[0], this.scenes[1]];

    // 合并场景
    const mergeResult = this.sceneMerger.mergeScenes(scenesToMerge, {
      preserveOriginalScenes: true,
      mergeMaterials: true,
      optimizeMergedScene: true,
      preserveHierarchy: true
    });

    // 获取合并后的场景
    const mergedScene = mergeResult.mergedScene;

    // 替换第四个场景
    this.scenes[3] = mergedScene;

    // 加载合并后的场景
    this.loadScene(3);

    Debug.log('场景合并完成');
  }

  /**
   * 导出场景
   */
  private async exportScene(): Promise<void> {
    // 获取当前场景
    const scene = this.scenes[this.currentSceneIndex];
    if (!scene) {
      Debug.error('没有可导出的场景');
      return;
    }

    try {
      // 导出为JSON
      const jsonData = await this.sceneFormatConverter.exportScene(scene, {
        format: SceneFormat.JSON,
        prettyPrint: true,
        exportMaterials: true,
        exportTextures: true,
        exportAnimations: true,
        exportCameras: true,
        exportLights: true
      });

      // 创建下载链接
      const blob = new Blob([jsonData as string], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `${scene.getName()}.json`;
      a.click();

      URL.revokeObjectURL(url);

      Debug.log('场景导出完成');
    } catch (error) {
      Debug.error('场景导出失败:', error);
    }
  }

  /**
   * 销毁示例
   */
  public dispose(): void {
    // 停止引擎
    this.engine.stop();

    // 销毁场景
    for (const scene of this.scenes) {
      scene.dispose();
    }

    // 清空场景列表
    this.scenes = [];

    // 移除UI
    if (this.ui && this.ui.parentNode) {
      this.ui.parentNode.removeChild(this.ui);
    }

    // 销毁系统
    this.lodSystem.dispose();
    this.frustumCullingSystem.dispose();
    this.batchingSystem.dispose();

    // 销毁引擎
    this.engine.dispose();

    this.initialized = false;
  }
}
