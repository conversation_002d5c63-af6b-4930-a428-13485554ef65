/**
 * 权限状态管理
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { CollaborationRole } from '../../services/CollaborationService';
import { Permission } from '../../services/PermissionService';
import { PermissionLog } from '../../services/PermissionLogService';
import { RootState } from '../index';

// 用户权限状态接口
interface UserPermission {
  userId: string;
  role: CollaborationRole;
  permissions: Permission[];
  customPermissions: Permission[];
}

// 角色权限状态接口
interface RolePermission {
  role: CollaborationRole;
  permissions: Permission[];
}

// 权限状态接口
interface PermissionState {
  users: UserPermission[];
  roles: RolePermission[];
  logs: PermissionLog[];
  isInitialized: boolean;
  enableLogging: boolean;
}

// 初始状态
const initialState: PermissionState = {
  users: [],
  roles: [],
  logs: [],
  isInitialized: false,
  enableLogging: true,
};

// 创建权限切片
export const permissionSlice = createSlice({
  name: 'permission',
  initialState,
  reducers: {
    // 初始化权限状态
    initializePermissions: (state) => {
      state.isInitialized = true;
    },

    // 设置用户角色
    setUserRole: (state, action: PayloadAction<{ userId: string; role: CollaborationRole }>) => {
      const { userId, role } = action.payload;
      const userIndex = state.users.findIndex(user => user.userId === userId);

      if (userIndex !== -1) {
        state.users[userIndex].role = role;
      } else {
        state.users.push({
          userId,
          role,
          permissions: [],
          customPermissions: [],
        });
      }
    },

    // 设置用户权限
    setUserPermissions: (state, action: PayloadAction<{ userId: string; permissions: Permission[] }>) => {
      const { userId, permissions } = action.payload;
      const userIndex = state.users.findIndex(user => user.userId === userId);

      if (userIndex !== -1) {
        state.users[userIndex].permissions = permissions;
      } else {
        state.users.push({
          userId,
          role: CollaborationRole.VIEWER, // 默认为查看者角色
          permissions,
          customPermissions: [],
        });
      }
    },

    // 添加用户自定义权限
    addCustomPermission: (state, action: PayloadAction<{ userId: string; permission: Permission }>) => {
      const { userId, permission } = action.payload;
      const userIndex = state.users.findIndex(user => user.userId === userId);

      if (userIndex !== -1) {
        if (!state.users[userIndex].customPermissions.includes(permission)) {
          state.users[userIndex].customPermissions.push(permission);
        }
      } else {
        state.users.push({
          userId,
          role: CollaborationRole.VIEWER, // 默认为查看者角色
          permissions: [],
          customPermissions: [permission],
        });
      }
    },

    // 移除用户自定义权限
    removeCustomPermission: (state, action: PayloadAction<{ userId: string; permission: Permission }>) => {
      const { userId, permission } = action.payload;
      const userIndex = state.users.findIndex(user => user.userId === userId);

      if (userIndex !== -1) {
        state.users[userIndex].customPermissions = state.users[userIndex].customPermissions.filter(
          p => p !== permission
        );
      }
    },

    // 设置角色权限
    setRolePermissions: (state, action: PayloadAction<{ role: CollaborationRole; permissions: Permission[] }>) => {
      const { role, permissions } = action.payload;
      const roleIndex = state.roles.findIndex(r => r.role === role);

      if (roleIndex !== -1) {
        state.roles[roleIndex].permissions = permissions;
      } else {
        state.roles.push({
          role,
          permissions,
        });
      }

      // 更新所有具有该角色的用户的权限
      state.users.forEach(user => {
        if (user.role === role) {
          user.permissions = permissions;
        }
      });
    },

    // 重置权限状态
    resetPermissions: (state) => {
      state.users = [];
      state.roles = [];
      state.isInitialized = false;
      state.logs = [];
    },

    // 添加权限日志
    addPermissionLog: (state, action: PayloadAction<PermissionLog>) => {
      if (state.enableLogging) {
        state.logs.push(action.payload);

        // 如果日志数量超过1000条，删除最旧的日志
        if (state.logs.length > 1000) {
          state.logs.splice(0, state.logs.length - 1000);
        }
      }
    },

    // 设置是否启用日志记录
    setEnableLogging: (state, action: PayloadAction<boolean>) => {
      state.enableLogging = action.payload;
    },

    // 清空权限日志
    clearPermissionLogs: (state) => {
      state.logs = [];
    },
  },
});

// 导出操作
export const {
  initializePermissions,
  setUserRole,
  setUserPermissions,
  addCustomPermission,
  removeCustomPermission,
  setRolePermissions,
  resetPermissions,
  addPermissionLog,
  setEnableLogging,
  clearPermissionLogs,
} = permissionSlice.actions;

// 选择器
export const selectIsPermissionInitialized = (state: RootState) => state.permission.isInitialized;
export const selectUserPermissions = (userId: string) => (state: RootState) =>
  state.permission.users.find(user => user.userId === userId);
export const selectRolePermissions = (role: CollaborationRole) => (state: RootState) =>
  state.permission.roles.find(r => r.role === role);
export const selectAllRoles = (state: RootState) => state.permission.roles;
export const selectAllUsers = (state: RootState) => state.permission.users;
export const selectPermissionLogs = (state: RootState) => state.permission.logs;
export const selectUserPermissionLogs = (userId: string) => (state: RootState) =>
  state.permission.logs.filter(log => log.userId === userId || log.targetUserId === userId);
export const selectIsLoggingEnabled = (state: RootState) => state.permission.enableLogging;

// 导出reducer
export default permissionSlice.reducer;
