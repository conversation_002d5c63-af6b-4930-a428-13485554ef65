# 视觉脚本AI节点使用指南

本文档介绍了DL（Digital Learning）引擎视觉脚本系统中的AI节点，包括AI模型支持、AI动画生成、情感分析等功能，以及它们的使用方法和示例。

## 目录

- [AI节点概述](#ai节点概述)
- [AI模型节点](#ai模型节点)
  - [加载AI模型节点](#加载ai模型节点)
  - [文本生成节点](#文本生成节点)
  - [图像生成节点](#图像生成节点)
  - [情感分析节点](#情感分析节点-1)
- [AI动画节点](#ai动画节点)
  - [生成身体动画节点](#生成身体动画节点)
  - [生成面部动画节点](#生成面部动画节点)
  - [生成组合动画节点](#生成组合动画节点)
- [情感分析节点](#情感分析节点)
- [自然语言处理节点](#自然语言处理节点)
- [示例](#示例)
  - [AI模型使用示例](#ai模型使用示例)
  - [AI动画生成示例](#ai动画生成示例)
  - [情感驱动动画示例](#情感驱动动画示例)
  - [安全的AI应用示例](#安全的ai应用示例)

## AI节点概述

AI节点提供了与AI系统交互的功能，包括使用AI模型、生成动画、分析情感、处理自然语言等。这些节点使用户能够在视觉脚本中轻松创建AI驱动的功能，无需编写复杂的AI代码。

## AI模型节点

AI模型节点提供了加载和使用各种AI模型的功能，包括GPT、Stable Diffusion和BERT等模型。

### 加载AI模型节点

加载指定类型的AI模型。

**节点类型**: `ai/model/load`

**输入插槽**:
- `flow` (Flow): 输入流程
- `modelType` (string): 模型类型，可选值：`gpt`、`stable-diffusion`、`bert`
- `config` (object): 模型配置
- `options` (object): 加载选项

**输出插槽**:
- `success` (Flow): 加载成功
- `fail` (Flow): 加载失败
- `model` (object): 加载的模型
- `progress` (number): 加载进度（0-1）

**使用示例**:
```json
{
  "id": "loadGPTModel",
  "type": "ai/model/load",
  "parameters": {
    "modelType": {
      "value": "gpt"
    },
    "config": {
      "value": {
        "version": "3.5-turbo",
        "temperature": 0.7,
        "maxTokens": 100
      }
    }
  }
}
```

### 文本生成节点

使用AI模型生成文本。

**节点类型**: `ai/model/generateText`

**输入插槽**:
- `flow` (Flow): 输入流程
- `model` (object): AI模型
- `prompt` (string): 提示文本
- `maxTokens` (number): 最大令牌数，默认值：100
- `temperature` (number): 温度（0-1），默认值：0.7
- `stream` (boolean): 是否使用流式响应，默认值：false

**输出插槽**:
- `success` (Flow): 生成成功
- `fail` (Flow): 生成失败
- `stream` (Flow): 流式响应
- `text` (string): 生成的文本
- `streamText` (string): 流式响应文本

**使用示例**:
```json
{
  "id": "generateText",
  "type": "ai/model/generateText",
  "parameters": {
    "model": {
      "reference": {
        "nodeId": "loadGPTModel",
        "socket": "model"
      }
    },
    "prompt": {
      "value": "写一首关于春天的诗"
    },
    "maxTokens": {
      "value": 200
    },
    "temperature": {
      "value": 0.8
    },
    "stream": {
      "value": true
    }
  }
}
```

### 图像生成节点

使用AI模型生成图像。

**节点类型**: `ai/model/generateImage`

**输入插槽**:
- `flow` (Flow): 输入流程
- `model` (object): AI模型
- `prompt` (string): 提示文本
- `width` (number): 图像宽度，默认值：512
- `height` (number): 图像高度，默认值：512
- `steps` (number): 生成步数，默认值：30
- `guidanceScale` (number): 引导比例，默认值：7.5
- `negativePrompt` (string): 负面提示，默认值：''

**输出插槽**:
- `success` (Flow): 生成成功
- `fail` (Flow): 生成失败
- `progress` (Flow): 生成进度
- `image` (Blob): 生成的图像
- `progressValue` (number): 生成进度（0-1）

**使用示例**:
```json
{
  "id": "generateImage",
  "type": "ai/model/generateImage",
  "parameters": {
    "model": {
      "reference": {
        "nodeId": "loadSDModel",
        "socket": "model"
      }
    },
    "prompt": {
      "value": "一只可爱的猫咪在草地上玩耍"
    },
    "width": {
      "value": 768
    },
    "height": {
      "value": 768
    },
    "steps": {
      "value": 50
    },
    "negativePrompt": {
      "value": "low quality, blurry"
    }
  }
}
```

### 情感分析节点

分析文本情感。

**节点类型**: `ai/emotion/analyze`

**输入插槽**:
- `flow` (Flow): 输入流程
- `model` (object): AI模型
- `text` (string): 要分析的文本

**输出插槽**:
- `success` (Flow): 分析成功
- `fail` (Flow): 分析失败
- `primaryEmotion` (string): 主要情感
- `intensity` (number): 情感强度（0-1）
- `scores` (object): 情感分数映射
- `confidence` (number): 置信度（0-1）

**使用示例**:
```json
{
  "id": "analyzeEmotion",
  "type": "ai/emotion/analyze",
  "parameters": {
    "model": {
      "reference": {
        "nodeId": "loadBERTModel",
        "socket": "model"
      }
    },
    "text": {
      "value": "我今天非常开心，因为我收到了一份礼物！"
    }
  }
}
```

## AI动画节点

### 生成身体动画节点

生成身体动画节点用于使用AI生成角色的身体动画。

**节点类型**: `ai/animation/generateBodyAnimation`

**输入插槽**:
- `flow` (Flow): 输入流程
- `entity` (Entity): 目标实体
- `prompt` (string): 提示文本，描述要生成的动画
- `duration` (number): 持续时间（秒），默认为5.0
- `loop` (boolean): 是否循环，默认为false
- `style` (string): 动画风格，可选
- `intensity` (number): 动画强度，默认为1.0，可选

**输出插槽**:
- `success` (Flow): 生成成功
- `fail` (Flow): 生成失败
- `animationClip` (AnimationClip): 生成的动画片段
- `generationTime` (number): 生成时间（毫秒）

**使用示例**:
```json
{
  "id": "generateWalkAnimation",
  "type": "ai/animation/generateBodyAnimation",
  "parameters": {
    "prompt": {
      "value": "走路"
    },
    "duration": {
      "value": 5.0
    },
    "loop": {
      "value": true
    },
    "style": {
      "value": "natural"
    }
  }
}
```

### 生成面部动画节点

生成面部动画节点用于使用AI生成角色的面部动画。

**节点类型**: `ai/animation/generateFacialAnimation`

**输入插槽**:
- `flow` (Flow): 输入流程
- `entity` (Entity): 目标实体
- `prompt` (string): 提示文本，描述要生成的面部表情或对话
- `duration` (number): 持续时间（秒），默认为3.0
- `loop` (boolean): 是否循环，默认为false

**输出插槽**:
- `success` (Flow): 生成成功
- `fail` (Flow): 生成失败
- `facialAnimationClip` (FacialAnimationClip): 生成的面部动画片段

**使用示例**:
```json
{
  "id": "generateTalkAnimation",
  "type": "ai/animation/generateFacialAnimation",
  "parameters": {
    "prompt": {
      "value": "你好，我是一个AI生成的角色。"
    },
    "duration": {
      "value": 3.0
    }
  }
}
```

### 生成组合动画节点

生成组合动画节点用于使用AI同时生成身体和面部动画。

**节点类型**: `ai/animation/generateCombinedAnimation`

**输入插槽**:
- `flow` (Flow): 输入流程
- `entity` (Entity): 目标实体
- `prompt` (string): 提示文本，描述要生成的动画
- `duration` (number): 持续时间（秒），默认为5.0
- `loop` (boolean): 是否循环，默认为false
- `style` (string): 动画风格，可选
- `intensity` (number): 动画强度，默认为1.0，可选

**输出插槽**:
- `success` (Flow): 生成成功
- `fail` (Flow): 生成失败
- `bodyAnimationClip` (AnimationClip): 生成的身体动画片段
- `facialAnimationClip` (FacialAnimationClip): 生成的面部动画片段

**使用示例**:
```json
{
  "id": "generateGreetingAnimation",
  "type": "ai/animation/generateCombinedAnimation",
  "parameters": {
    "prompt": {
      "value": "微笑着挥手说你好"
    },
    "duration": {
      "value": 5.0
    }
  }
}
```

## 情感分析节点

### 分析文本情感节点

分析文本情感节点用于分析文本中的情感。

**节点类型**: `ai/emotion/analyzeText`

**输入插槽**:
- `flow` (Flow): 输入流程
- `text` (string): 要分析的文本

**输出插槽**:
- `flow` (Flow): 输出流程
- `emotion` (object): 情感分析结果，包含各种情感的强度
- `dominantEmotion` (string): 主要情感

**使用示例**:
```json
{
  "id": "analyzeEmotion",
  "type": "ai/emotion/analyzeText",
  "parameters": {
    "text": {
      "value": "我今天非常开心！"
    }
  }
}
```

### 情感驱动动画节点

情感驱动动画节点用于根据情感分析结果生成相应的动画。

**节点类型**: `ai/emotion/driveAnimation`

**输入插槽**:
- `flow` (Flow): 输入流程
- `entity` (Entity): 目标实体
- `emotion` (object): 情感分析结果
- `intensity` (number): 情感强度，默认为1.0

**输出插槽**:
- `flow` (Flow): 输出流程
- `success` (boolean): 是否成功

**使用示例**:
```json
{
  "id": "driveEmotionAnimation",
  "type": "ai/emotion/driveAnimation",
  "parameters": {
    "intensity": {
      "value": 1.5
    }
  }
}
```

## 自然语言处理节点

### 文本生成节点

文本生成节点用于使用AI生成文本。

**节点类型**: `ai/nlp/generateText`

**输入插槽**:
- `flow` (Flow): 输入流程
- `prompt` (string): 提示文本
- `maxLength` (number): 最大生成长度，默认为100
- `temperature` (number): 生成温度，默认为0.7

**输出插槽**:
- `flow` (Flow): 输出流程
- `text` (string): 生成的文本

**使用示例**:
```json
{
  "id": "generateStory",
  "type": "ai/nlp/generateText",
  "parameters": {
    "prompt": {
      "value": "从前有一座山，山上有一座庙，庙里有一个老和尚在讲故事："
    },
    "maxLength": {
      "value": 200
    },
    "temperature": {
      "value": 0.8
    }
  }
}
```

### 文本分类节点

文本分类节点用于对文本进行分类。

**节点类型**: `ai/nlp/classifyText`

**输入插槽**:
- `flow` (Flow): 输入流程
- `text` (string): 要分类的文本
- `categories` (array): 分类类别

**输出插槽**:
- `flow` (Flow): 输出流程
- `category` (string): 分类结果
- `confidence` (number): 置信度

**使用示例**:
```json
{
  "id": "classifyText",
  "type": "ai/nlp/classifyText",
  "parameters": {
    "text": {
      "value": "这个产品非常好用，我很满意！"
    },
    "categories": {
      "value": ["正面", "负面", "中性"]
    }
  }
}
```

## 示例

### AI动画生成示例

以下是一个AI动画生成示例，演示如何使用AI节点生成角色动画：

```json
{
  "nodes": [
    {
      "id": "start",
      "type": "core/events/onStart",
      "metadata": {
        "positionX": 100,
        "positionY": 100
      },
      "flows": {
        "flow": {
          "nodeId": "getCharacter",
          "socket": "flow"
        }
      }
    },
    {
      "id": "getCharacter",
      "type": "entity/get",
      "metadata": {
        "positionX": 300,
        "positionY": 100
      },
      "parameters": {
        "id": {
          "value": "角色"
        }
      },
      "flows": {
        "flow": {
          "nodeId": "generateWalkAnimation",
          "socket": "flow"
        }
      }
    },
    {
      "id": "generateWalkAnimation",
      "type": "ai/animation/generateBodyAnimation",
      "metadata": {
        "positionX": 500,
        "positionY": 100
      },
      "parameters": {
        "entity": {
          "reference": {
            "nodeId": "getCharacter",
            "socket": "entity"
          }
        },
        "prompt": {
          "value": "走路"
        },
        "duration": {
          "value": 5.0
        },
        "loop": {
          "value": true
        }
      },
      "flows": {
        "success": {
          "nodeId": "logSuccess",
          "socket": "flow"
        },
        "fail": {
          "nodeId": "logFail",
          "socket": "flow"
        }
      }
    },
    {
      "id": "logSuccess",
      "type": "core/debug/log",
      "metadata": {
        "positionX": 700,
        "positionY": 50
      },
      "parameters": {
        "message": {
          "value": "生成走路动画成功"
        }
      }
    },
    {
      "id": "logFail",
      "type": "core/debug/log",
      "metadata": {
        "positionX": 700,
        "positionY": 150
      },
      "parameters": {
        "message": {
          "value": "生成走路动画失败"
        }
      }
    }
  ]
}
```

### 情感驱动动画示例

请参考 `examples/visualscript/AIAnimationExample.ts` 中的完整示例，该示例演示了如何使用视觉脚本系统的AI节点实现情感驱动的角色动画。

### AI模型使用示例

以下是一个AI模型使用示例，演示如何加载和使用不同类型的AI模型：

```json
{
  "nodes": [
    {
      "id": "start",
      "type": "core/events/onStart",
      "metadata": {
        "positionX": 100,
        "positionY": 100
      },
      "flows": {
        "flow": {
          "nodeId": "loadGPTModel",
          "socket": "flow"
        }
      }
    },
    {
      "id": "loadGPTModel",
      "type": "ai/model/load",
      "metadata": {
        "positionX": 300,
        "positionY": 100
      },
      "parameters": {
        "modelType": {
          "value": "gpt"
        },
        "config": {
          "value": {
            "version": "3.5-turbo",
            "temperature": 0.7
          }
        }
      },
      "flows": {
        "success": {
          "nodeId": "loadSDModel",
          "socket": "flow"
        },
        "fail": {
          "nodeId": "logGPTFail",
          "socket": "flow"
        }
      }
    },
    {
      "id": "loadSDModel",
      "type": "ai/model/load",
      "metadata": {
        "positionX": 500,
        "positionY": 100
      },
      "parameters": {
        "modelType": {
          "value": "stable-diffusion"
        },
        "config": {
          "value": {
            "version": "stable-diffusion-xl-1.0"
          }
        }
      },
      "flows": {
        "success": {
          "nodeId": "generateText",
          "socket": "flow"
        },
        "fail": {
          "nodeId": "logSDFail",
          "socket": "flow"
        }
      }
    },
    {
      "id": "generateText",
      "type": "ai/model/generateText",
      "metadata": {
        "positionX": 700,
        "positionY": 100
      },
      "parameters": {
        "model": {
          "reference": {
            "nodeId": "loadGPTModel",
            "socket": "model"
          }
        },
        "prompt": {
          "value": "描述一只可爱的猫咪"
        },
        "maxTokens": {
          "value": 100
        }
      },
      "flows": {
        "success": {
          "nodeId": "generateImage",
          "socket": "flow"
        },
        "fail": {
          "nodeId": "logTextFail",
          "socket": "flow"
        }
      }
    },
    {
      "id": "generateImage",
      "type": "ai/model/generateImage",
      "metadata": {
        "positionX": 900,
        "positionY": 100
      },
      "parameters": {
        "model": {
          "reference": {
            "nodeId": "loadSDModel",
            "socket": "model"
          }
        },
        "prompt": {
          "reference": {
            "nodeId": "generateText",
            "socket": "text"
          }
        },
        "width": {
          "value": 512
        },
        "height": {
          "value": 512
        }
      },
      "flows": {
        "success": {
          "nodeId": "displayImage",
          "socket": "flow"
        },
        "fail": {
          "nodeId": "logImageFail",
          "socket": "flow"
        }
      }
    },
    {
      "id": "displayImage",
      "type": "ui/displayImage",
      "metadata": {
        "positionX": 1100,
        "positionY": 100
      },
      "parameters": {
        "image": {
          "reference": {
            "nodeId": "generateImage",
            "socket": "image"
          }
        }
      }
    },
    {
      "id": "logGPTFail",
      "type": "core/debug/log",
      "metadata": {
        "positionX": 500,
        "positionY": 200
      },
      "parameters": {
        "message": {
          "value": "加载GPT模型失败"
        }
      }
    },
    {
      "id": "logSDFail",
      "type": "core/debug/log",
      "metadata": {
        "positionX": 700,
        "positionY": 200
      },
      "parameters": {
        "message": {
          "value": "加载Stable Diffusion模型失败"
        }
      }
    },
    {
      "id": "logTextFail",
      "type": "core/debug/log",
      "metadata": {
        "positionX": 900,
        "positionY": 200
      },
      "parameters": {
        "message": {
          "value": "生成文本失败"
        }
      }
    },
    {
      "id": "logImageFail",
      "type": "core/debug/log",
      "metadata": {
        "positionX": 1100,
        "positionY": 200
      },
      "parameters": {
        "message": {
          "value": "生成图像失败"
        }
      }
    }
  ]
}
```

### 安全的AI应用示例

请参考 `examples/ai-security-debug` 目录中的完整示例，该示例演示了如何创建一个安全的AI驱动多用户应用，包括：

- 使用AI模型生成文本、图像和进行情感分析
- 实现端到端加密通信
- 使用安全的用户认证和会话管理
- 使用调试工具进行故障排除和性能优化

详细文档请参考 [AI模型、网络安全和调试工具集成示例](../examples/ai-security-debug-example.md)。
