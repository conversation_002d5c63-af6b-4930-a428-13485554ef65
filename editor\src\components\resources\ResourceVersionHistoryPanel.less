/**
 * 资源版本历史面板样式
 */
.resource-version-history-panel {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  .version-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .ant-card-body {
      flex: 1;
      overflow: auto;
      padding: 0;
      display: flex;
      flex-direction: column;
    }
    
    .version-list {
      flex: 1;
      overflow: auto;
      
      .version-item {
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.3s;
        cursor: pointer;
        
        &:hover {
          background-color: #f5f5f5;
        }
        
        &.current {
          background-color: #e6f7ff;
        }
        
        &.selected {
          background-color: #f0f7ff;
          border-left: 3px solid #1890ff;
        }
        
        .ant-list-item-meta-title {
          margin-bottom: 8px;
        }
        
        .ant-list-item-meta-description {
          > div {
            margin-bottom: 4px;
          }
        }
        
        .add-tag-container {
          margin-top: 8px;
        }
      }
    }
    
    .version-info {
      padding: 16px;
      background-color: #f9f9f9;
    }
    
    .ant-divider {
      margin: 0;
    }
  }
}
