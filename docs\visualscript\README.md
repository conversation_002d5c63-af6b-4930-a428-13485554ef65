# IR-Engine 视觉脚本系统

IR-Engine 视觉脚本系统是一个强大的可视化编程工具，允许用户通过连接节点来创建复杂的逻辑和行为，而无需编写代码。本文档提供了视觉脚本系统的概述、使用指南和开发文档。

## 目录

- [概述](#概述)
- [基本概念](#基本概念)
- [使用指南](#使用指南)
- [节点类型](#节点类型)
- [调试功能](#调试功能)
- [性能优化](#性能优化)
- [扩展系统](#扩展系统)
- [示例](#示例)

## 概述

视觉脚本系统允许用户通过可视化界面创建复杂的逻辑和行为。用户可以通过拖放节点、连接节点和配置节点参数来构建脚本，而无需编写代码。这使得非程序员也能够创建复杂的交互和行为。

视觉脚本系统的主要特点包括：

- 直观的可视化编辑界面
- 丰富的预设节点库
- 强大的调试功能
- 高性能的执行引擎
- 可扩展的节点系统

## 基本概念

### 节点

节点是视觉脚本的基本构建块。每个节点代表一个特定的功能或操作，如数学运算、逻辑判断、实体操作等。节点有输入和输出插槽，用于接收和发送数据。

### 插槽

插槽是节点上的连接点，用于连接节点之间的数据流或执行流。插槽分为两种类型：

- **数据插槽**：用于传递数据，如数字、字符串、布尔值等。
- **流程插槽**：用于控制执行流程，决定节点的执行顺序。

### 连接

连接是节点之间的线，表示数据或执行流的传递。连接分为两种类型：

- **数据连接**：连接数据插槽，表示数据的传递。
- **流程连接**：连接流程插槽，表示执行流程的传递。

### 变量

变量是存储数据的容器，可以在不同节点之间共享数据。变量有不同的类型，如数字、字符串、布尔值等。

## 使用指南

### 创建视觉脚本

1. 打开编辑器，选择"视觉脚本"面板。
2. 点击"新建"按钮，创建一个新的视觉脚本。
3. 在编辑器中，右键点击空白处，选择"添加节点"，或者从节点库中拖拽节点到编辑器中。
4. 通过连接节点的插槽来构建脚本逻辑。
5. 配置节点的参数，如输入值、变量等。
6. 保存视觉脚本。

### 编辑视觉脚本

1. 选择要编辑的节点，可以在属性面板中修改节点的参数。
2. 拖动节点可以改变节点的位置。
3. 点击连接线可以选中连接，按 Delete 键可以删除连接。
4. 右键点击节点可以复制、粘贴、删除节点。

### 运行视觉脚本

1. 点击工具栏上的"运行"按钮，开始执行视觉脚本。
2. 点击"暂停"按钮，暂停执行。
3. 点击"停止"按钮，停止执行。
4. 点击"单步执行"按钮，逐步执行脚本。

## 节点类型

视觉脚本系统提供了丰富的预设节点，分为以下几类：

### 数学节点

数学节点用于执行数学运算，如加减乘除、三角函数、向量运算等。

- **加法节点**：计算两个数的和。
- **减法节点**：计算两个数的差。
- **乘法节点**：计算两个数的积。
- **除法节点**：计算两个数的商。
- **取模节点**：计算两个数的模。
- **幂运算节点**：计算一个数的幂。
- **平方根节点**：计算一个数的平方根。
- **三角函数节点**：计算三角函数值，如正弦、余弦、正切等。

### 逻辑节点

逻辑节点用于执行逻辑运算和控制流程，如条件判断、循环等。

- **分支节点**：根据条件选择执行路径。
- **比较节点**：比较两个值，如相等、大于、小于等。
- **逻辑运算节点**：执行逻辑运算，如与、或、非等。
- **开关节点**：在两个状态之间切换。

### 实体节点

实体节点用于操作实体和组件，如创建实体、获取组件、修改组件属性等。

- **获取实体节点**：根据ID获取实体。
- **获取组件节点**：获取实体上的组件。
- **添加组件节点**：向实体添加组件。
- **移除组件节点**：从实体移除组件。
- **检查组件节点**：检查实体是否有指定组件。

## 调试功能

视觉脚本系统提供了强大的调试功能，帮助用户调试和优化脚本。

### 断点

用户可以在节点上设置断点，当执行到断点时，脚本会暂停执行，用户可以查看当前的变量值和执行状态。

### 单步执行

用户可以逐步执行脚本，查看每一步的执行结果和变量值的变化。

### 变量监视

用户可以监视变量的值，查看变量在执行过程中的变化。

### 性能分析

用户可以查看节点的执行时间和执行次数，找出性能瓶颈。

## 性能优化

视觉脚本系统提供了多种性能优化技术，确保脚本的高效执行。

### 节点缓存

系统会缓存节点的执行结果，避免重复计算。

### 懒加载

系统只会加载当前视图中的节点，减少内存使用。

### 批处理

系统会批量处理节点的更新，减少更新次数。

## 扩展系统

视觉脚本系统是可扩展的，开发者可以创建自定义节点和功能。

### 创建自定义节点

开发者可以通过继承基础节点类来创建自定义节点，实现特定的功能。

### 注册节点

开发者需要将自定义节点注册到节点注册表中，使其可以在编辑器中使用。

## 示例

### 简单计算器

这个示例展示了如何使用数学节点创建一个简单的计算器。

1. 添加两个数字输入节点，分别输入两个数。
2. 添加一个加法节点，连接两个数字输入节点。
3. 添加一个显示节点，连接加法节点的结果输出。
4. 运行脚本，查看结果。

### 实体移动

这个示例展示了如何使用实体节点和数学节点控制实体的移动。

1. 添加一个获取实体节点，输入实体ID。
2. 添加一个获取组件节点，连接实体节点，获取Transform组件。
3. 添加一个向量加法节点，计算新的位置。
4. 添加一个设置组件属性节点，连接Transform组件和新位置，更新实体位置。
5. 运行脚本，查看实体移动。
