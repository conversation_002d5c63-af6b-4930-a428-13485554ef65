/**
 * 示例项目详情样式
 */
@import '../../styles/variables.less';

.example-detail {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: @component-background;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid @border-color-split;

    .back-button {
      display: flex;
      align-items: center;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .action-button {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .import-button {
        margin-left: 8px;
      }
    }
  }

  .detail-content {
    flex: 1;
    display: flex;
    overflow: hidden;

    .detail-main {
      flex: 1;
      padding: 24px;
      overflow-y: auto;

      .detail-gallery {
        margin-bottom: 24px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .gallery-item {
          height: 400px;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

      .detail-info {
        margin-bottom: 24px;

        .detail-title {
          margin: 0 0 16px;
          font-size: 24px;
          font-weight: 600;
          color: @heading-color;
        }

        .detail-meta {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 12px;
          margin-bottom: 16px;

          .detail-popularity,
          .detail-date,
          .detail-author {
            display: flex;
            align-items: center;
            gap: 4px;
            color: @text-color-secondary;
            font-size: 14px;
          }
        }

        .detail-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-bottom: 16px;
        }

        .detail-description {
          font-size: 16px;
          line-height: 1.6;
          color: @text-color;
          margin: 0;
        }
      }

      .detail-tabs {
        .tab-content {
          padding: 16px 0;
        }
      }
    }

    .detail-sidebar {
      width: 300px;
      padding: 24px;
      border-left: 1px solid @border-color-split;
      overflow-y: auto;

      .sidebar-card {
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .related-image {
        width: 48px;
        height: 48px;
        object-fit: cover;
        border-radius: 4px;
      }
    }
  }
}

.preview-modal {
  .preview-iframe {
    width: 100%;
    height: 80vh;
    border: none;
  }
}
