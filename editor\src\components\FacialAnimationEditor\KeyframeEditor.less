/**
 * 关键帧编辑器样式
 */
.keyframe-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .keyframe-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background-color: #2a2a2a;
    border-bottom: 1px solid #3a3a3a;
    
    .track-selector {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  .keyframe-list {
    flex: 1;
    overflow: auto;
    padding: 0 16px;
    
    :global(.ant-table) {
      background-color: transparent;
      
      :global(.ant-table-thead > tr > th) {
        background-color: #2a2a2a;
        color: #f0f0f0;
        border-bottom: 1px solid #3a3a3a;
      }
      
      :global(.ant-table-tbody > tr > td) {
        border-bottom: 1px solid #333;
      }
      
      :global(.ant-table-tbody > tr.selected-row) {
        background-color: rgba(24, 144, 255, 0.1);
      }
      
      :global(.ant-table-tbody > tr:hover > td) {
        background-color: rgba(255, 255, 255, 0.05);
      }
    }
  }
  
  .keyframe-form {
    padding: 16px;
    background-color: #252525;
    border-top: 1px solid #3a3a3a;
    
    h3 {
      margin-top: 0;
      margin-bottom: 16px;
      color: #f0f0f0;
    }
    
    :global(.ant-form-item-label > label) {
      color: #d0d0d0;
    }
  }
}
