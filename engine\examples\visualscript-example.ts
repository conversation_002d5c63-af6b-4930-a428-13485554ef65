/**
 * 视觉脚本系统示例
 * 演示如何使用视觉脚本系统
 */
import { Engine, World, Entity, Scene, Transform, Camera, VisualScriptComponent, VisualScriptSystem } from '../src';
import { registerCoreNodes } from '../src/visualscript/presets/CoreNodes';
import { NodeRegistry } from '../src/visualscript/nodes/NodeRegistry';
import { ValueTypeRegistry } from '../src/visualscript/values/ValueTypeRegistry';
import { GraphJSON } from '../src/visualscript/graph/GraphJSON';

// 创建引擎
const engine = new Engine({
  debug: true
});

// 创建世界
const world = new World(engine);

// 创建节点注册表
const nodeRegistry = new NodeRegistry();

// 注册核心节点
registerCoreNodes(nodeRegistry);

// 创建值类型注册表
const valueTypeRegistry = new ValueTypeRegistry();

// 创建视觉脚本系统
const visualScriptSystem = new VisualScriptSystem(world, {
  defaultDomain: 'default'
});

// 注册脚本域
visualScriptSystem.registerDomain('default', nodeRegistry, valueTypeRegistry);

// 添加视觉脚本系统到世界
world.addSystem(visualScriptSystem);

// 创建场景
const scene = new Scene(world, {
  name: '视觉脚本示例场景'
});

// 创建相机
const camera = new Entity('相机');
camera.addComponent(new Camera({
  type: 'perspective',
  fov: 60,
  near: 0.1,
  far: 1000
}));
camera.addComponent(new Transform({
  position: { x: 0, y: 2, z: 5 },
  rotation: { x: -0.2, y: 0, z: 0 }
}));

// 添加相机到场景
scene.addEntity(camera);

// 创建一个简单的视觉脚本
const simpleScript: GraphJSON = {
  nodes: [
    {
      id: '1',
      type: 'core/events/onStart',
      metadata: {
        positionX: 100,
        positionY: 100
      },
      flows: {
        flow: {
          nodeId: '2',
          socket: 'flow'
        }
      }
    },
    {
      id: '2',
      type: 'core/debug/print',
      metadata: {
        positionX: 400,
        positionY: 100
      },
      parameters: {
        message: {
          value: '视觉脚本系统启动成功！'
        },
        level: {
          value: 'log'
        }
      },
      flows: {
        flow: {
          nodeId: '3',
          socket: 'flow'
        }
      }
    },
    {
      id: '3',
      type: 'core/flow/delay',
      metadata: {
        positionX: 700,
        positionY: 100
      },
      parameters: {
        seconds: {
          value: 2
        }
      },
      flows: {
        flow: {
          nodeId: '4',
          socket: 'flow'
        }
      }
    },
    {
      id: '4',
      type: 'core/debug/print',
      metadata: {
        positionX: 1000,
        positionY: 100
      },
      parameters: {
        message: {
          value: '延时2秒后执行！'
        },
        level: {
          value: 'info'
        }
      },
      flows: {
        flow: {
          nodeId: '5',
          socket: 'flow'
        }
      }
    },
    {
      id: '5',
      type: 'core/flow/branch',
      metadata: {
        positionX: 1300,
        positionY: 100
      },
      parameters: {
        condition: {
          value: true
        }
      },
      flows: {
        true: {
          nodeId: '6',
          socket: 'flow'
        },
        false: {
          nodeId: '7',
          socket: 'flow'
        }
      }
    },
    {
      id: '6',
      type: 'core/debug/print',
      metadata: {
        positionX: 1600,
        positionY: 0
      },
      parameters: {
        message: {
          value: '条件为真！'
        },
        level: {
          value: 'log'
        }
      }
    },
    {
      id: '7',
      type: 'core/debug/print',
      metadata: {
        positionX: 1600,
        positionY: 200
      },
      parameters: {
        message: {
          value: '条件为假！'
        },
        level: {
          value: 'warn'
        }
      }
    }
  ],
  variables: [],
  customEvents: []
};

// 创建实体并添加视觉脚本组件
const scriptEntity = new Entity('脚本实体');
scriptEntity.addComponent(new VisualScriptComponent(scriptEntity, {
  script: simpleScript,
  autoRun: true,
  domain: 'default'
}));

// 添加实体到场景
scene.addEntity(scriptEntity);

// 设置当前场景
world.setActiveScene(scene);

// 初始化引擎
engine.initialize();

// 启动引擎
engine.start();

// 输出提示信息
console.log('视觉脚本示例已启动，请查看控制台输出。');
