/**
 * 基于情感的动画示例
 * 演示如何使用AI动画合成系统生成基于情感的面部动画
 */
import * as THREE from 'three';
import { Engine } from '../../core/Engine';
import { World } from '../../core/World';
import { Entity } from '../../core/Entity';
import { 
  FacialAnimationSystem, 
  FacialAnimationComponent, 
  FacialExpressionType, 
  VisemeType,
  FacialAnimationModelAdapterSystem,
  FacialAnimationModelType,
  AIAnimationSynthesisSystem,
  AIAnimationSynthesisComponent
} from '../../animation';
import { LocalAIAnimationModel } from '../../animation/ai';
import { GLTFLoader } from '../../loaders/GLTFLoader';
import { OrbitControls } from '../../utils/OrbitControls';

/**
 * 基于情感的动画示例
 */
export class EmotionBasedAnimationExample {
  /** 引擎 */
  private engine: Engine;
  /** 世界 */
  private world: World;
  /** 场景 */
  private scene: THREE.Scene;
  /** 相机 */
  private camera: THREE.PerspectiveCamera;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer;
  /** 控制器 */
  private controls: OrbitControls;
  /** 角色实体 */
  private characterEntity: Entity;
  /** 面部动画系统 */
  private facialAnimationSystem: FacialAnimationSystem;
  /** 面部动画模型适配器系统 */
  private modelAdapterSystem: FacialAnimationModelAdapterSystem;
  /** AI动画合成系统 */
  private aiAnimationSystem: AIAnimationSynthesisSystem;
  /** 是否运行中 */
  private running: boolean = false;
  /** 模型类型 */
  private modelType: FacialAnimationModelType = FacialAnimationModelType.GENERIC;
  /** 情感输入元素 */
  private emotionInput: HTMLInputElement | null = null;
  /** 生成按钮 */
  private generateButton: HTMLButtonElement | null = null;

  /**
   * 构造函数
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 获取世界
    this.world = this.engine.getWorld();
    
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x333333);
    
    // 创建相机
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.camera.position.set(0, 1.6, 2);
    
    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    document.body.appendChild(this.renderer.domElement);
    
    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 1.6, 0);
    this.controls.update();
    
    // 创建角色实体
    this.characterEntity = this.world.createEntity();
    
    // 创建面部动画系统
    this.facialAnimationSystem = new FacialAnimationSystem(this.world, {
      debug: true
    });
    
    // 创建面部动画模型适配器系统
    this.modelAdapterSystem = new FacialAnimationModelAdapterSystem(this.world, {
      debug: true,
      autoDetectBlendShapes: true
    });
    
    // 创建AI动画合成系统
    this.aiAnimationSystem = new AIAnimationSynthesisSystem(this.world, {
      debug: true,
      useLocalModel: true
    });
    
    // 设置模型适配器系统
    this.facialAnimationSystem.setModelAdapterSystem(this.modelAdapterSystem);
    
    // 添加系统到世界
    this.world.addSystem(this.facialAnimationSystem);
    this.world.addSystem(this.modelAdapterSystem);
    this.world.addSystem(this.aiAnimationSystem);
    
    // 设置窗口大小变化监听
    window.addEventListener('resize', this.onWindowResize.bind(this));
    
    // 创建UI
    this.createUI();
  }

  /**
   * 窗口大小变化处理
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    const uiContainer = document.createElement('div');
    uiContainer.style.position = 'absolute';
    uiContainer.style.top = '10px';
    uiContainer.style.left = '10px';
    uiContainer.style.padding = '10px';
    uiContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    uiContainer.style.borderRadius = '5px';
    uiContainer.style.color = 'white';
    document.body.appendChild(uiContainer);
    
    // 创建标题
    const title = document.createElement('h3');
    title.textContent = '基于情感的动画生成';
    title.style.margin = '0 0 10px 0';
    uiContainer.appendChild(title);
    
    // 创建情感输入
    const inputContainer = document.createElement('div');
    inputContainer.style.marginBottom = '10px';
    uiContainer.appendChild(inputContainer);
    
    const inputLabel = document.createElement('label');
    inputLabel.textContent = '情感描述: ';
    inputContainer.appendChild(inputLabel);
    
    this.emotionInput = document.createElement('input');
    this.emotionInput.type = 'text';
    this.emotionInput.value = '开心地笑';
    this.emotionInput.style.width = '200px';
    this.emotionInput.style.marginLeft = '5px';
    inputContainer.appendChild(this.emotionInput);
    
    // 创建生成按钮
    this.generateButton = document.createElement('button');
    this.generateButton.textContent = '生成动画';
    this.generateButton.style.padding = '5px 10px';
    this.generateButton.style.marginTop = '5px';
    this.generateButton.addEventListener('click', this.generateAnimation.bind(this));
    uiContainer.appendChild(this.generateButton);
    
    // 创建预设情感按钮
    const presetContainer = document.createElement('div');
    presetContainer.style.marginTop = '10px';
    uiContainer.appendChild(presetContainer);
    
    const presetLabel = document.createElement('div');
    presetLabel.textContent = '预设情感:';
    presetLabel.style.marginBottom = '5px';
    presetContainer.appendChild(presetLabel);
    
    const presets = [
      '开心地笑', '悲伤地哭', '愤怒地吼叫', '惊讶地张嘴', '害怕地颤抖', '厌恶地皱眉'
    ];
    
    for (const preset of presets) {
      const presetButton = document.createElement('button');
      presetButton.textContent = preset;
      presetButton.style.padding = '3px 8px';
      presetButton.style.margin = '0 5px 5px 0';
      presetButton.addEventListener('click', () => {
        if (this.emotionInput) {
          this.emotionInput.value = preset;
        }
      });
      presetContainer.appendChild(presetButton);
    }
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(ambientLight);
    
    // 添加方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 2, 3);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);
    
    // 添加地面
    const ground = new THREE.Mesh(
      new THREE.PlaneGeometry(10, 10),
      new THREE.MeshStandardMaterial({ color: 0x999999 })
    );
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    this.scene.add(ground);
    
    // 添加坐标轴辅助
    const axesHelper = new THREE.AxesHelper(1);
    this.scene.add(axesHelper);
  }

  /**
   * 加载模型
   * @param modelPath 模型路径
   */
  private loadModel(modelPath: string): void {
    // 创建GLTF加载器
    const loader = new GLTFLoader();
    
    // 加载角色模型
    loader.load(modelPath, (gltf) => {
      // 添加模型到场景
      this.scene.add(gltf.scene);
      
      // 设置阴影
      gltf.scene.traverse((object) => {
        if (object instanceof THREE.Mesh) {
          object.castShadow = true;
          object.receiveShadow = true;
        }
      });
      
      // 查找骨骼网格
      let skinnedMesh: THREE.SkinnedMesh | null = null;
      gltf.scene.traverse((object) => {
        if (object instanceof THREE.SkinnedMesh && !skinnedMesh) {
          skinnedMesh = object;
        }
      });
      
      if (skinnedMesh) {
        // 创建面部动画组件
        const facialAnimation = this.facialAnimationSystem.createFacialAnimation(this.characterEntity);
        
        // 将面部动画组件与模型绑定
        this.facialAnimationSystem.linkToModel(this.characterEntity, skinnedMesh);
        
        // 创建AI动画合成组件
        this.aiAnimationSystem.createAIAnimationSynthesis(this.characterEntity);
        
        console.log('模型加载完成，已绑定面部动画组件');
        console.log('混合形状字典:', skinnedMesh.morphTargetDictionary);
        console.log('混合形状影响:', skinnedMesh.morphTargetInfluences);
        
        // 启用生成按钮
        if (this.generateButton) {
          this.generateButton.disabled = false;
        }
      } else {
        console.warn('未找到骨骼网格');
      }
    }, undefined, (error) => {
      console.error('加载模型失败:', error);
    });
  }

  /**
   * 生成动画
   */
  private generateAnimation(): void {
    // 获取情感描述
    const emotionText = this.emotionInput ? this.emotionInput.value : '开心地笑';
    
    // 禁用生成按钮
    if (this.generateButton) {
      this.generateButton.disabled = true;
      this.generateButton.textContent = '生成中...';
    }
    
    // 获取AI动画合成组件
    const aiComponent = this.aiAnimationSystem.getAIAnimationSynthesis(this.characterEntity);
    if (!aiComponent) {
      console.error('未找到AI动画合成组件');
      return;
    }
    
    // 生成面部动画
    const requestId = this.aiAnimationSystem.generateFacialAnimation(
      this.characterEntity,
      emotionText,
      5.0,
      {
        loop: true,
        style: '自然',
        intensity: 0.8
      }
    );
    
    if (requestId) {
      console.log(`AI动画生成请求已提交: ${requestId}`);
      
      // 监听生成完成事件
      this.aiAnimationSystem.addEventListener('generationComplete', (data) => {
        if (data.result.id === requestId) {
          console.log('AI动画生成完成:', data.result);
          
          // 启用生成按钮
          if (this.generateButton) {
            this.generateButton.disabled = false;
            this.generateButton.textContent = '生成动画';
          }
        }
      });
    }
  }

  /**
   * 设置模型类型
   * @param type 模型类型
   */
  public setModelType(type: FacialAnimationModelType): void {
    this.modelType = type;
  }

  /**
   * 启动示例
   * @param modelPath 模型路径
   */
  public start(modelPath: string = 'models/character.glb'): void {
    // 创建场景
    this.createScene();
    
    // 加载模型
    this.loadModel(modelPath);
    
    // 启动引擎
    this.engine.start();
    
    // 开始动画循环
    this.running = true;
    this.animate();
    
    // 禁用生成按钮，直到模型加载完成
    if (this.generateButton) {
      this.generateButton.disabled = true;
    }
    
    console.log('基于情感的动画示例已启动');
  }

  /**
   * 停止示例
   */
  public stop(): void {
    this.running = false;
    this.engine.stop();
    console.log('基于情感的动画示例已停止');
  }

  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;
    
    requestAnimationFrame(this.animate.bind(this));
    
    // 更新控制器
    this.controls.update();
    
    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }
}

// 创建示例
const example = new EmotionBasedAnimationExample();

// 设置模型类型（根据实际模型选择）
example.setModelType(FacialAnimationModelType.GLTF);

// 启动示例
example.start('models/character_with_blendshapes.glb');
