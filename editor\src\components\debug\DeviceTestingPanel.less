/**
 * 设备测试面板样式
 */
.device-testing-panel {
  .ant-card-body {
    max-height: 700px;
    overflow-y: auto;
  }

  .setting-label {
    font-weight: 500;
    margin-bottom: 8px;
  }

  .ant-slider-mark-text {
    font-size: 12px;
  }

  .device-preview {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 16px 0;
    
    .device-frame {
      border: 2px solid #333;
      border-radius: 8px;
      padding: 4px;
      background-color: #f0f0f0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      
      .device-screen {
        width: 100%;
        height: 100%;
        background-color: #fff;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        
        .anticon {
          font-size: 24px;
          margin-bottom: 8px;
        }
        
        .device-info {
          font-size: 10px;
          text-align: center;
          
          div {
            margin-bottom: 2px;
          }
        }
      }
    }
  }

  .test-results {
    margin-top: 16px;
    
    .ant-card-small {
      .ant-card-head {
        min-height: 32px;
        padding: 0 8px;
        
        .ant-card-head-title {
          padding: 8px 0;
          font-size: 14px;
        }
      }
      
      .ant-card-body {
        padding: 12px;
      }
    }
  }

  .help-text {
    margin-top: 16px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
  }
}

// 响应式调整
@media (max-width: 576px) {
  .device-testing-panel {
    .ant-card-body {
      padding: 12px;
    }
    
    .ant-row {
      margin-right: -8px !important;
      margin-left: -8px !important;
      
      .ant-col {
        padding-right: 8px !important;
        padding-left: 8px !important;
      }
    }
  }
}
