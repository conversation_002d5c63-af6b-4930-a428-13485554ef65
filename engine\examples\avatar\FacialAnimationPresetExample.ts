/**
 * 面部动画预设示例
 * 演示如何使用面部动画预设系统
 */
import * as THREE from 'three';
import { Engine } from '../../core/Engine';
import { World } from '../../core/World';
import { Entity } from '../../core/Entity';
import { FacialAnimationComponent } from '../../avatar/components/FacialAnimationComponent';
import { FacialAnimationSystem } from '../../avatar/systems/FacialAnimationSystem';
import { FacialAnimationModelAdapterSystem, FacialAnimationModelType } from '../../avatar/systems/FacialAnimationModelAdapterSystem';
import { FacialAnimationPresetSystem, FacialAnimationPresetType } from '../../avatar/presets/FacialAnimationPresetSystem';
import { OrbitControls } from '../../utils/OrbitControls';

/**
 * 面部动画预设示例
 */
export class FacialAnimationPresetExample {
  /** 引擎 */
  private engine: Engine;
  /** 世界 */
  private world: World;
  /** 场景 */
  private scene: THREE.Scene;
  /** 相机 */
  private camera: THREE.PerspectiveCamera;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer;
  /** 控制器 */
  private controls: OrbitControls;
  /** 角色实体 */
  private characterEntity: Entity;
  /** 面部动画系统 */
  private facialAnimationSystem: FacialAnimationSystem;
  /** 面部动画模型适配器系统 */
  private modelAdapterSystem: FacialAnimationModelAdapterSystem;
  /** 面部动画预设系统 */
  private presetSystem: FacialAnimationPresetSystem;
  /** 是否运行中 */
  private running: boolean = false;
  /** 预设类型选择元素 */
  private presetTypeSelect: HTMLSelectElement | null = null;
  /** 预设选择元素 */
  private presetSelect: HTMLSelectElement | null = null;
  /** 文化选择元素 */
  private cultureSelect: HTMLSelectElement | null = null;
  /** 应用按钮 */
  private applyButton: HTMLButtonElement | null = null;
  /** 重置按钮 */
  private resetButton: HTMLButtonElement | null = null;
  /** 当前预设类型 */
  private currentPresetType: FacialAnimationPresetType = FacialAnimationPresetType.STANDARD;
  /** 当前预设名称 */
  private currentPresetName: string = 'neutral';
  /** 当前文化 */
  private currentCulture: string = 'global';

  /**
   * 构造函数
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    this.world = this.engine.world;

    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x333333);

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.camera.position.set(0, 1.6, 2);

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    document.body.appendChild(this.renderer.domElement);

    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 1.6, 0);
    this.controls.update();

    // 创建角色实体
    this.characterEntity = this.world.createEntity();

    // 创建面部动画系统
    this.facialAnimationSystem = new FacialAnimationSystem(this.world, {
      debug: true
    });

    // 创建面部动画模型适配器系统
    this.modelAdapterSystem = new FacialAnimationModelAdapterSystem(this.world, {
      debug: true,
      defaultModelType: FacialAnimationModelType.GLTF
    });

    // 创建面部动画预设系统
    this.presetSystem = new FacialAnimationPresetSystem(this.world, {
      debug: true,
      autoLoadPresets: true,
      defaultCulture: 'chinese'
    });

    // 添加系统到世界
    this.world.addSystem(this.facialAnimationSystem);
    this.world.addSystem(this.modelAdapterSystem);
    this.world.addSystem(this.presetSystem);

    // 创建灯光
    this.createLights();

    // 创建UI
    this.createUI();

    // 加载模型
    this.loadModel();

    // 添加窗口调整事件
    window.addEventListener('resize', this.onWindowResize.bind(this));
  }

  /**
   * 创建灯光
   */
  private createLights(): void {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(ambientLight);

    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 2, 3);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    const uiContainer = document.createElement('div');
    uiContainer.style.position = 'absolute';
    uiContainer.style.top = '10px';
    uiContainer.style.left = '10px';
    uiContainer.style.padding = '10px';
    uiContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    uiContainer.style.borderRadius = '5px';
    uiContainer.style.color = 'white';
    document.body.appendChild(uiContainer);

    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '面部动画预设示例';
    title.style.margin = '0 0 10px 0';
    uiContainer.appendChild(title);

    // 创建预设类型选择
    const presetTypeLabel = document.createElement('label');
    presetTypeLabel.textContent = '预设类型: ';
    uiContainer.appendChild(presetTypeLabel);

    this.presetTypeSelect = document.createElement('select');
    this.presetTypeSelect.style.margin = '0 10px 10px 0';
    this.presetTypeSelect.style.padding = '5px';

    // 添加预设类型选项
    const presetTypes = [
      { value: FacialAnimationPresetType.STANDARD, text: '标准表情' },
      { value: FacialAnimationPresetType.CULTURAL, text: '文化特定表情' },
      { value: FacialAnimationPresetType.EMOTION_COMBO, text: '情感组合' },
      { value: FacialAnimationPresetType.ANIMATION_SEQUENCE, text: '动画序列' },
      { value: FacialAnimationPresetType.CUSTOM, text: '自定义' }
    ];

    for (const type of presetTypes) {
      const option = document.createElement('option');
      option.value = type.value;
      option.textContent = type.text;
      this.presetTypeSelect.appendChild(option);
    }

    this.presetTypeSelect.addEventListener('change', this.onPresetTypeChange.bind(this));
    uiContainer.appendChild(this.presetTypeSelect);
    uiContainer.appendChild(document.createElement('br'));

    // 创建文化选择
    const cultureLabel = document.createElement('label');
    cultureLabel.textContent = '文化: ';
    uiContainer.appendChild(cultureLabel);

    this.cultureSelect = document.createElement('select');
    this.cultureSelect.style.margin = '0 10px 10px 0';
    this.cultureSelect.style.padding = '5px';

    // 添加文化选项
    const cultures = [
      { value: 'global', text: '全球' },
      { value: 'chinese', text: '中国' },
      { value: 'japanese', text: '日本' },
      { value: 'american', text: '美国' }
    ];

    for (const culture of cultures) {
      const option = document.createElement('option');
      option.value = culture.value;
      option.textContent = culture.text;
      this.cultureSelect.appendChild(option);
    }

    this.cultureSelect.addEventListener('change', this.onCultureChange.bind(this));
    uiContainer.appendChild(this.cultureSelect);
    uiContainer.appendChild(document.createElement('br'));

    // 创建预设选择
    const presetLabel = document.createElement('label');
    presetLabel.textContent = '预设: ';
    uiContainer.appendChild(presetLabel);

    this.presetSelect = document.createElement('select');
    this.presetSelect.style.margin = '0 10px 10px 0';
    this.presetSelect.style.padding = '5px';
    this.presetSelect.style.width = '200px';

    this.presetSelect.addEventListener('change', this.onPresetChange.bind(this));
    uiContainer.appendChild(this.presetSelect);
    uiContainer.appendChild(document.createElement('br'));

    // 创建应用按钮
    this.applyButton = document.createElement('button');
    this.applyButton.textContent = '应用预设';
    this.applyButton.style.padding = '5px 10px';
    this.applyButton.style.margin = '10px 10px 0 0';
    this.applyButton.addEventListener('click', this.onApplyPreset.bind(this));
    uiContainer.appendChild(this.applyButton);

    // 创建重置按钮
    this.resetButton = document.createElement('button');
    this.resetButton.textContent = '重置';
    this.resetButton.style.padding = '5px 10px';
    this.resetButton.style.margin = '10px 0 0 0';
    this.resetButton.addEventListener('click', this.onReset.bind(this));
    uiContainer.appendChild(this.resetButton);

    // 更新预设选择
    this.updatePresetSelect();
  }

  /**
   * 加载模型
   */
  private loadModel(): void {
    // 加载模型
    // 这里是加载模型的占位代码，实际实现需要根据具体模型
    // TODO: 实现模型加载

    // 创建临时几何体
    const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
    const material = new THREE.MeshStandardMaterial({ color: 0x00ff00 });
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.set(0, 1.6, 0);
    this.scene.add(mesh);

    // 创建面部动画组件
    const facialAnimation = this.facialAnimationSystem.createFacialAnimation(this.characterEntity);

    console.log('模型加载完成，已创建面部动画组件');
  }

  /**
   * 更新预设选择
   */
  private updatePresetSelect(): void {
    if (!this.presetSelect) return;

    // 清空当前选项
    this.presetSelect.innerHTML = '';

    // 获取预设
    let presets = [];

    if (this.currentPresetType === FacialAnimationPresetType.CULTURAL) {
      // 获取指定文化的预设
      presets = this.presetSystem.getPresetsByCulture(this.currentCulture);
    } else {
      // 获取指定类型的预设
      presets = this.presetSystem.getPresetsByType(this.currentPresetType);
    }

    // 添加预设选项
    for (const preset of presets) {
      const option = document.createElement('option');
      option.value = preset.name;
      option.textContent = `${preset.name} - ${preset.description || ''}`;
      this.presetSelect.appendChild(option);
    }

    // 如果没有预设，添加一个提示选项
    if (presets.length === 0) {
      const option = document.createElement('option');
      option.value = '';
      option.textContent = '没有可用的预设';
      option.disabled = true;
      this.presetSelect.appendChild(option);
    } else {
      // 设置当前选中的预设
      this.currentPresetName = presets[0].name;
      this.presetSelect.value = this.currentPresetName;
    }
  }

  /**
   * 窗口调整事件处理
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 预设类型变更事件处理
   */
  private onPresetTypeChange(event: Event): void {
    if (this.presetTypeSelect) {
      this.currentPresetType = this.presetTypeSelect.value as FacialAnimationPresetType;
      this.updatePresetSelect();
    }
  }

  /**
   * 文化变更事件处理
   */
  private onCultureChange(event: Event): void {
    if (this.cultureSelect) {
      this.currentCulture = this.cultureSelect.value;

      // 如果当前是文化特定预设，更新预设选择
      if (this.currentPresetType === FacialAnimationPresetType.CULTURAL) {
        this.updatePresetSelect();
      }
    }
  }

  /**
   * 预设变更事件处理
   */
  private onPresetChange(event: Event): void {
    if (this.presetSelect && this.presetSelect.value) {
      this.currentPresetName = this.presetSelect.value;
    }
  }

  /**
   * 应用预设事件处理
   */
  private onApplyPreset(): void {
    if (!this.currentPresetName) return;

    // 应用预设
    const success = this.presetSystem.applyPreset(this.characterEntity, this.currentPresetName);

    if (success) {
      console.log(`成功应用预设: ${this.currentPresetName}`);
    } else {
      console.warn(`应用预设失败: ${this.currentPresetName}`);
    }
  }

  /**
   * 重置事件处理
   */
  private onReset(): void {
    // 重置表情
    const facialAnimation = this.facialAnimationSystem.getFacialAnimation(this.characterEntity);
    if (facialAnimation) {
      facialAnimation.clearExpressions();
      facialAnimation.setExpression('neutral', 1.0);
    }

    // 重置UI
    if (this.presetTypeSelect) {
      this.presetTypeSelect.value = FacialAnimationPresetType.STANDARD;
      this.currentPresetType = FacialAnimationPresetType.STANDARD;
    }

    if (this.cultureSelect) {
      this.cultureSelect.value = 'global';
      this.currentCulture = 'global';
    }

    // 更新预设选择
    this.updatePresetSelect();
  }

  /**
   * 启动
   */
  public start(): void {
    if (this.running) return;

    this.running = true;
    this.animate();
  }

  /**
   * 停止
   */
  public stop(): void {
    this.running = false;
  }

  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;

    requestAnimationFrame(this.animate.bind(this));

    // 更新控制器
    this.controls.update();

    // 更新世界
    this.world.update();

    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }
}
