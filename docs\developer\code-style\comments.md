# DL（Digital Learning）引擎编辑器代码注释规范

## 概述

良好的代码注释对于提高代码可读性和可维护性至关重要。本文档规定了DL（Digital Learning）引擎编辑器项目的代码注释规范，包括注释风格、内容要求和示例。

## 目录

- [基本原则](#基本原则)
- [文件头注释](#文件头注释)
- [类注释](#类注释)
- [方法注释](#方法注释)
- [属性注释](#属性注释)
- [接口注释](#接口注释)
- [枚举注释](#枚举注释)
- [常量注释](#常量注释)
- [行内注释](#行内注释)
- [TODO注释](#todo注释)
- [JSDoc标签](#jsdoc标签)
- [示例](#示例)

## 基本原则

1. **使用中文注释**：所有注释应使用中文编写，保持与UI一致。
2. **注释应该解释"为什么"而不仅仅是"是什么"**：代码本身应该清晰地表达它做了什么，注释应该解释为什么要这样做。
3. **保持注释的更新**：当代码变更时，确保相关注释也得到更新。
4. **避免冗余注释**：不要注释那些从代码中已经明显的事情。
5. **使用完整的句子**：注释应该使用完整的句子，以大写字母开头，以句号结尾。
6. **保持一致性**：在整个项目中保持一致的注释风格。

## 文件头注释

每个源文件都应该包含文件头注释，说明文件的用途、作者和创建日期。

```typescript
/**
 * 文件名：EngineService.ts
 * 描述：引擎服务，负责与引擎核心交互
 * 作者：DL（Digital Learning）引擎团队
 * 创建日期：2023-05-10
 */
```

## 类注释

每个类都应该有注释，说明类的用途和职责。

```typescript
/**
 * 引擎服务类
 * 负责与引擎核心交互，提供了一组API来操作引擎
 */
export class EngineService {
  // ...
}
```

## 方法注释

每个公共方法都应该有注释，说明方法的用途、参数和返回值。

```typescript
/**
 * 初始化引擎
 * @param canvas 画布元素
 * @param options 引擎选项
 * @returns 初始化完成的Promise
 */
public async initialize(canvas: HTMLCanvasElement, options: Partial<EngineOptions> = {}): Promise<void> {
  // ...
}
```

## 属性注释

重要的属性应该有注释，说明属性的用途和类型。

```typescript
/**
 * 引擎实例
 */
private engine: Engine | null = null;

/**
 * 当前活动场景
 */
private activeScene: Scene | null = null;
```

## 接口注释

每个接口都应该有注释，说明接口的用途和职责。

```typescript
/**
 * 引擎选项接口
 * 定义了初始化引擎时的选项
 */
export interface EngineOptions {
  /**
   * 是否自动启动引擎
   */
  autoStart?: boolean;

  /**
   * 是否启用抗锯齿
   */
  antialias?: boolean;

  /**
   * 是否启用阴影
   */
  shadows?: boolean;

  /**
   * 是否启用物理引擎
   */
  physicsEnabled?: boolean;
}
```

## 枚举注释

每个枚举都应该有注释，说明枚举的用途和每个枚举值的含义。

```typescript
/**
 * 引擎事件类型
 * 定义了引擎可能触发的事件类型
 */
export enum EngineEventType {
  /**
   * 引擎初始化完成
   */
  INITIALIZED = 'engine:initialized',

  /**
   * 引擎启动
   */
  STARTED = 'engine:started',

  /**
   * 引擎停止
   */
  STOPPED = 'engine:stopped',

  /**
   * 场景加载完成
   */
  SCENE_LOADED = 'engine:scene:loaded',
}
```

## 常量注释

重要的常量应该有注释，说明常量的用途和值的含义。

```typescript
/**
 * 默认引擎选项
 */
export const DEFAULT_ENGINE_OPTIONS: EngineOptions = {
  autoStart: false,
  antialias: true,
  shadows: true,
  physicsEnabled: true,
};

/**
 * 最大撤销步数
 */
export const MAX_UNDO_STEPS = 20;
```

## 行内注释

行内注释应该用于解释复杂的代码逻辑或非显而易见的实现细节。

```typescript
// 使用射线检测与场景中的物体的交点
const raycaster = new THREE.Raycaster();
raycaster.setFromCamera(mouse, camera);

// 过滤掉不可选择的物体
const intersects = raycaster.intersectObjects(selectableObjects, true);

// 如果有交点，选中第一个物体
if (intersects.length > 0) {
  const object = intersects[0].object;
  // 向上遍历找到关联的实体
  const entity = this.findEntityByObject(object);
  if (entity) {
    this.selectEntity(entity);
  }
}
```

## TODO注释

使用TODO注释标记需要在将来完成的工作。

```typescript
// TODO: 实现物理碰撞检测
// TODO: 优化渲染性能
// TODO: 添加更多的材质类型
```

## JSDoc标签

使用JSDoc标签提供更详细的文档信息。

- `@param`：描述方法参数
- `@returns`：描述方法返回值
- `@throws`：描述方法可能抛出的异常
- `@example`：提供使用示例
- `@see`：引用相关内容
- `@deprecated`：标记已废弃的API

```typescript
/**
 * 加载场景
 * @param sceneId 场景ID
 * @returns 加载完成的场景对象
 * @throws 如果场景不存在或加载失败
 * @example
 * ```typescript
 * const scene = await engineService.loadScene('scene-123');
 * ```
 * @see Scene
 */
public async loadScene(sceneId: string): Promise<Scene> {
  // ...
}
```

## 示例

### 完整的类示例

```typescript
/**
 * 文件名：EngineService.ts
 * 描述：引擎服务，负责与引擎核心交互
 * 作者：DL（Digital Learning）引擎团队
 * 创建日期：2023-05-10
 */

import { EventEmitter } from 'events';
import { Engine, EngineOptions, Scene, Entity, Component, Camera } from 'ir-engine-core';

/**
 * 引擎事件类型
 * 定义了引擎可能触发的事件类型
 */
export enum EngineEventType {
  /**
   * 引擎初始化完成
   */
  INITIALIZED = 'engine:initialized',

  /**
   * 引擎启动
   */
  STARTED = 'engine:started',

  /**
   * 引擎停止
   */
  STOPPED = 'engine:stopped',

  /**
   * 场景加载完成
   */
  SCENE_LOADED = 'engine:scene:loaded',
}

/**
 * 引擎服务类
 * 负责与引擎核心交互，提供了一组API来操作引擎
 */
export class EngineService extends EventEmitter {
  /**
   * 单例实例
   */
  private static instance: EngineService;

  /**
   * 引擎实例
   */
  private engine: Engine | null = null;

  /**
   * 当前活动场景
   */
  private activeScene: Scene | null = null;

  /**
   * 当前活动相机
   */
  private activeCamera: Camera | null = null;

  /**
   * 当前选中的实体列表
   */
  private selectedEntities: Entity[] = [];

  /**
   * 私有构造函数，防止直接实例化
   */
  private constructor() {
    super();
  }

  /**
   * 获取EngineService实例
   * @returns EngineService实例
   */
  public static getInstance(): EngineService {
    if (!EngineService.instance) {
      EngineService.instance = new EngineService();
    }
    return EngineService.instance;
  }

  /**
   * 初始化引擎
   * @param canvas 画布元素
   * @param options 引擎选项
   * @returns 初始化完成的Promise
   * @throws 如果初始化失败
   */
  public async initialize(canvas: HTMLCanvasElement, options: Partial<EngineOptions> = {}): Promise<void> {
    if (this.engine) {
      return;
    }

    try {
      // 创建引擎实例
      this.engine = new Engine({
        canvas,
        autoStart: false,
        ...options,
      });

      // 初始化引擎
      this.engine.initialize();

      // 发出初始化事件
      this.emit(EngineEventType.INITIALIZED, this.engine);

      console.log('引擎初始化成功');
    } catch (error) {
      console.error('引擎初始化失败:', error);
      throw error;
    }
  }

  /**
   * 启动引擎
   * @throws 如果引擎未初始化
   */
  public start(): void {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    this.engine.start();
    this.emit(EngineEventType.STARTED);
  }

  /**
   * 停止引擎
   * @throws 如果引擎未初始化
   */
  public stop(): void {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    this.engine.stop();
    this.emit(EngineEventType.STOPPED);
  }

  /**
   * 加载场景
   * @param sceneId 场景ID
   * @returns 加载完成的场景对象
   * @throws 如果引擎未初始化或场景加载失败
   */
  public async loadScene(sceneId: string): Promise<Scene> {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    try {
      // 加载场景
      const scene = await this.engine.loadScene(sceneId);
      this.activeScene = scene;

      // 发出场景加载事件
      this.emit(EngineEventType.SCENE_LOADED, scene);

      return scene;
    } catch (error) {
      console.error('加载场景失败:', error);
      throw error;
    }
  }

  /**
   * 获取引擎实例
   * @returns 引擎实例，如果未初始化则返回null
   */
  public getEngine(): Engine | null {
    return this.engine;
  }

  /**
   * 获取当前活动场景
   * @returns 当前活动场景，如果未加载则返回null
   */
  public getActiveScene(): Scene | null {
    return this.activeScene;
  }

  /**
   * 获取当前选中的实体列表
   * @returns 当前选中的实体列表
   */
  public getSelectedEntities(): Entity[] {
    return [...this.selectedEntities];
  }

  /**
   * 销毁引擎服务
   */
  public dispose(): void {
    if (this.engine) {
      this.engine.dispose();
      this.engine = null;
    }

    this.activeScene = null;
    this.activeCamera = null;
    this.selectedEntities = [];

    this.removeAllListeners();
  }
}
```

### 完整的接口示例

```typescript
/**
 * 文件名：EngineOptions.ts
 * 描述：引擎选项接口
 * 作者：DL（Digital Learning）引擎团队
 * 创建日期：2023-05-10
 */

/**
 * 引擎选项接口
 * 定义了初始化引擎时的选项
 */
export interface EngineOptions {
  /**
   * 是否自动启动引擎
   * @default false
   */
  autoStart?: boolean;

  /**
   * 是否启用抗锯齿
   * @default true
   */
  antialias?: boolean;

  /**
   * 是否启用阴影
   * @default true
   */
  shadows?: boolean;

  /**
   * 是否启用物理引擎
   * @default true
   */
  physicsEnabled?: boolean;

  /**
   * 物理引擎重力
   * @default { x: 0, y: -9.8, z: 0 }
   */
  gravity?: {
    x: number;
    y: number;
    z: number;
  };

  /**
   * 渲染器像素比
   * @default window.devicePixelRatio
   */
  pixelRatio?: number;

  /**
   * 是否启用后处理
   * @default false
   */
  postProcessingEnabled?: boolean;

  /**
   * 是否启用调试模式
   * @default false
   */
  debug?: boolean;
}
```
