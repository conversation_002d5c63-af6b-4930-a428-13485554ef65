/**
 * 资源依赖管理器样式
 */
.resource-dependency-manager {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .manager-container {
    height: 100%;
  }

  .resources-column,
  .details-column {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .resources-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .ant-card-body {
      flex: 1;
      padding: 12px;
      overflow: auto;
    }

    .ant-table-wrapper {
      height: 100%;
    }

    .selected-row {
      background-color: #e6f7ff;
    }
  }

  .empty-details {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    background-color: #f5f5f5;
    border-radius: 4px;
    color: #999;
    font-size: 16px;
  }

  .resource-details {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .details-card {
      margin-bottom: 16px;
    }

    .details-tabs {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .ant-tabs-content {
        flex: 1;
        overflow: hidden;

        .ant-tabs-tabpane {
          height: 100%;
          overflow: auto;
        }
      }
    }

    .dependencies-card,
    .optimization-card {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .ant-card-body {
        flex: 1;
        padding: 12px;
        overflow: auto;
      }
    }

    .detail-item {
      margin-bottom: 8px;

      .detail-label {
        font-weight: 500;
        margin-bottom: 4px;
      }

      .detail-value {
        color: #666;
        word-break: break-all;
      }
    }

    .suggestion-list {
      list-style: none;
      padding: 0;
      margin: 0;

      .suggestion-item {
        margin-bottom: 16px;
        padding: 12px;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        background-color: #fafafa;

        &:last-child {
          margin-bottom: 0;
        }

        .suggestion-header {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .suggestion-title {
            margin-left: 8px;
            font-weight: 500;
          }
        }

        .suggestion-content {
          p {
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }
          }

          .ant-tag {
            cursor: pointer;
            margin-bottom: 4px;
          }
        }
      }
    }

    .empty-suggestions {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
      background-color: #f5f5f5;
      border-radius: 4px;
      color: #999;
      font-size: 16px;
    }
  }
}
