/**
 * 资源依赖面板
 * 用于在编辑器中管理资源依赖关系
 */
import React, { useState } from 'react';
import { Tabs, Button, Space, Tooltip } from 'antd';
import { 
  NodeIndexOutlined, 
  SettingOutlined, 
  BarChartOutlined, 
  InfoCircleOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import ResourceDependencyManager from './ResourceDependencyManager';
import ResourceDependencyVisualizerPanel from './ResourceDependencyVisualizerPanel';
import './ResourceDependencyPanel.less';

const { TabPane } = Tabs;

// 组件属性接口
interface ResourceDependencyPanelProps {
  className?: string;
}

/**
 * 资源依赖面板组件
 */
const ResourceDependencyPanel: React.FC<ResourceDependencyPanelProps> = ({
  className
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>('manager');
  const [helpVisible, setHelpVisible] = useState<boolean>(false);

  // 渲染帮助内容
  const renderHelp = () => {
    return (
      <div className="dependency-help">
        <h3>资源依赖管理器使用指南</h3>
        <p>资源依赖管理器用于管理和优化资源之间的依赖关系，提高资源加载效率和运行性能。</p>
        
        <h4>依赖类型</h4>
        <ul>
          <li><strong>强依赖</strong>：必须加载的依赖，资源加载时会自动加载其强依赖。</li>
          <li><strong>弱依赖</strong>：可选加载的依赖，资源加载时不会自动加载其弱依赖，但可以手动加载。</li>
          <li><strong>延迟依赖</strong>：按需加载的依赖，资源使用时才会加载其延迟依赖。</li>
          <li><strong>预加载依赖</strong>：提前加载的依赖，在资源加载前会预先加载其预加载依赖。</li>
        </ul>
        
        <h4>功能说明</h4>
        <ul>
          <li><strong>依赖管理</strong>：查看和编辑资源依赖关系。</li>
          <li><strong>依赖可视化</strong>：以图形方式展示资源依赖关系。</li>
          <li><strong>依赖分析</strong>：分析资源依赖关系，检测循环依赖、未使用资源等问题。</li>
          <li><strong>优化建议</strong>：根据分析结果提供优化建议，如转换依赖类型、移除冗余依赖等。</li>
        </ul>
        
        <h4>常见问题</h4>
        <ul>
          <li><strong>循环依赖</strong>：资源之间形成循环依赖关系，可能导致加载问题和内存泄漏。</li>
          <li><strong>冗余依赖</strong>：资源直接依赖了已经被其他依赖间接依赖的资源，造成重复加载。</li>
          <li><strong>未使用资源</strong>：没有被任何其他资源依赖的资源，可能是无用资源。</li>
        </ul>
        
        <h4>优化策略</h4>
        <ul>
          <li><strong>移除冗余依赖</strong>：移除直接依赖中已经被间接依赖的资源。</li>
          <li><strong>转换为弱依赖</strong>：将不必须的强依赖转换为弱依赖，减少加载时间。</li>
          <li><strong>使用延迟依赖</strong>：将不常用的依赖转换为延迟依赖，按需加载。</li>
          <li><strong>使用预加载依赖</strong>：将频繁使用的依赖转换为预加载依赖，提前加载。</li>
        </ul>
      </div>
    );
  };

  return (
    <div className={`resource-dependency-panel ${className || ''}`}>
      <div className="panel-header">
        <h2>{t('resources.dependency.title')}</h2>
        <Space>
          <Tooltip title={t('resources.dependency.help')}>
            <Button
              icon={<QuestionCircleOutlined />}
              onClick={() => setHelpVisible(!helpVisible)}
            />
          </Tooltip>
        </Space>
      </div>

      {helpVisible && renderHelp()}

      <Tabs activeKey={activeTab} onChange={setActiveTab} className="panel-tabs">
        <TabPane
          tab={<span><SettingOutlined />{t('resources.dependency.manager')}</span>}
          key="manager"
        >
          <ResourceDependencyManager />
        </TabPane>
        <TabPane
          tab={<span><NodeIndexOutlined />{t('resources.dependency.visualizer')}</span>}
          key="visualizer"
        >
          <ResourceDependencyVisualizerPanel showAllResources={true} />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default ResourceDependencyPanel;
