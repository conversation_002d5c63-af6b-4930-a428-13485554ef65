"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Entity = void 0;
var Transform_1 = require("../scene/Transform");
var EventEmitter_1 = require("../utils/EventEmitter");
var Entity = /** @class */ (function (_super) {
    __extends(Entity, _super);
    /**
     * 创建实体实例
     * @param name 实体名称
     */
    function Entity(name) {
        if (name === void 0) { name = '实体'; }
        var _this = _super.call(this) || this;
        /** 实体ID */
        _this.id = '';
        /** 是否活跃 */
        _this.active = true;
        /** 标签列表 */
        _this.tags = new Set();
        /** 组件映射 */
        _this.components = new Map();
        /** 子实体列表 */
        _this.children = [];
        /** 父实体 */
        _this.parent = null;
        /** 世界引用 */
        _this.world = null;
        _this.name = name;
        // 创建变换组件
        _this.transform = new Transform_1.Transform();
        _this.addComponent(_this.transform);
        return _this;
    }
    /**
     * 更新实体
     * @param deltaTime 帧间隔时间（秒）
     */
    Entity.prototype.update = function (deltaTime) {
        if (!this.active) {
            return;
        }
        // 更新所有组件
        for (var _i = 0, _a = Array.from(this.components.values()); _i < _a.length; _i++) {
            var component = _a[_i];
            if (component.isEnabled()) {
                component.update(deltaTime);
            }
        }
        // 更新所有子实体
        for (var _b = 0, _c = this.children; _b < _c.length; _b++) {
            var child = _c[_b];
            child.update(deltaTime);
        }
    };
    /**
     * 固定时间步长更新
     * @param fixedDeltaTime 固定帧间隔时间（秒）
     */
    Entity.prototype.fixedUpdate = function (fixedDeltaTime) {
        if (!this.active) {
            return;
        }
        // 固定更新所有组件
        for (var _i = 0, _a = Array.from(this.components.values()); _i < _a.length; _i++) {
            var component = _a[_i];
            if (component.isEnabled()) {
                component.fixedUpdate(fixedDeltaTime);
            }
        }
        // 固定更新所有子实体
        for (var _b = 0, _c = this.children; _b < _c.length; _b++) {
            var child = _c[_b];
            child.fixedUpdate(fixedDeltaTime);
        }
    };
    /**
     * 添加组件
     * @param component 组件实例
     * @returns 添加的组件
     */
    Entity.prototype.addComponent = function (component) {
        var type = component.getType();
        // 检查是否已存在同类型组件
        if (this.components.has(type)) {
            console.warn("\u5B9E\u4F53 ".concat(this.name, " \u5DF2\u7ECF\u6709\u4E00\u4E2A ").concat(type, " \u7EC4\u4EF6"));
            return this.components.get(type);
        }
        // 设置组件的实体引用
        component.setEntity(this);
        // 添加到组件映射
        this.components.set(type, component);
        // 发出组件添加事件
        this.emit('componentAdded', component);
        return component;
    };
    /**
     * 获取组件
     * @param type 组件类型
     * @returns 组件实例，如果不存在则返回null
     */
    Entity.prototype.getComponent = function (type) {
        return this.components.get(type) || null;
    };
    /**
     * 移除组件
     * @param component 组件实例或类型
     * @returns 是否成功移除
     */
    Entity.prototype.removeComponent = function (component) {
        var type = typeof component === 'string' ? component : component.getType();
        // 不能移除变换组件
        if (type === 'Transform') {
            console.warn('不能移除变换组件');
            return false;
        }
        if (!this.components.has(type)) {
            return false;
        }
        var componentToRemove = this.components.get(type);
        // 销毁组件
        componentToRemove.dispose();
        // 从组件映射中移除
        this.components.delete(type);
        // 发出组件移除事件
        this.emit('componentRemoved', componentToRemove);
        return true;
    };
    /**
     * 获取所有组件
     * @returns 组件数组
     */
    Entity.prototype.getAllComponents = function () {
        return Array.from(this.components.values());
    };
    /**
     * 是否有组件
     * @param type 组件类型
     * @returns 是否有该类型的组件
     */
    Entity.prototype.hasComponent = function (type) {
        return this.components.has(type);
    };
    /**
     * 添加子实体
     * @param child 子实体
     * @returns 添加的子实体
     */
    Entity.prototype.addChild = function (child) {
        // 如果已经是子实体，则不做任何操作
        if (this.children.includes(child)) {
            return child;
        }
        // 如果有父实体，则从父实体中移除
        if (child.parent) {
            child.parent.removeChild(child);
        }
        // 设置父实体
        child.parent = this;
        // 添加到子实体列表
        this.children.push(child);
        // 更新变换
        child.transform.setParent(this.transform);
        // 发出子实体添加事件
        this.emit('childAdded', child);
        return child;
    };
    /**
     * 移除子实体
     * @param child 子实体
     * @returns 是否成功移除
     */
    Entity.prototype.removeChild = function (child) {
        var index = this.children.indexOf(child);
        if (index === -1) {
            return false;
        }
        // 清除父实体引用
        child.parent = null;
        // 从子实体列表中移除
        this.children.splice(index, 1);
        // 更新变换
        child.transform.setParent(null);
        // 发出子实体移除事件
        this.emit('childRemoved', child);
        return true;
    };
    /**
     * 获取所有子实体
     * @returns 子实体数组
     */
    Entity.prototype.getChildren = function () {
        return __spreadArray([], this.children, true);
    };
    /**
     * 获取父实体
     * @returns 父实体，如果没有则返回null
     */
    Entity.prototype.getParent = function () {
        return this.parent;
    };
    /**
     * 获取变换组件
     * @returns 变换组件
     */
    Entity.prototype.getTransform = function () {
        return this.transform;
    };
    /**
     * 设置世界引用
     * @param world 世界实例
     */
    Entity.prototype.setWorld = function (world) {
        this.world = world;
    };
    /**
     * 获取世界引用
     * @returns 世界实例
     */
    Entity.prototype.getWorld = function () {
        return this.world;
    };
    /**
     * 设置活跃状态
     * @param active 是否活跃
     */
    Entity.prototype.setActive = function (active) {
        if (this.active === active) {
            return;
        }
        this.active = active;
        // 发出活跃状态变更事件
        this.emit('activeChanged', active);
        // 更新所有子实体的活跃状态
        for (var _i = 0, _a = this.children; _i < _a.length; _i++) {
            var child = _a[_i];
            child.setActive(active);
        }
    };
    /**
     * 是否活跃
     * @returns 是否活跃
     */
    Entity.prototype.isActive = function () {
        // 如果父实体不活跃，则子实体也不活跃
        if (this.parent && !this.parent.isActive()) {
            return false;
        }
        return this.active;
    };
    /**
     * 添加标签
     * @param tag 标签
     */
    Entity.prototype.addTag = function (tag) {
        this.tags.add(tag);
    };
    /**
     * 移除标签
     * @param tag 标签
     * @returns 是否成功移除
     */
    Entity.prototype.removeTag = function (tag) {
        return this.tags.delete(tag);
    };
    /**
     * 是否有标签
     * @param tag 标签
     * @returns 是否有该标签
     */
    Entity.prototype.hasTag = function (tag) {
        return this.tags.has(tag);
    };
    /**
     * 获取所有标签
     * @returns 标签数组
     */
    Entity.prototype.getTags = function () {
        return Array.from(this.tags);
    };
    /**
     * 销毁实体
     */
    Entity.prototype.dispose = function () {
        // 移除所有子实体
        while (this.children.length > 0) {
            var child = this.children[0];
            this.removeChild(child);
            child.dispose();
        }
        // 移除所有组件
        for (var _i = 0, _a = Array.from(this.components.values()); _i < _a.length; _i++) {
            var component = _a[_i];
            component.dispose();
        }
        this.components.clear();
        // 如果有父实体，从父实体中移除
        if (this.parent) {
            this.parent.removeChild(this);
        }
        // 清除世界引用
        this.world = null;
        // 移除所有事件监听器
        this.removeAllListeners();
    };
    return Entity;
}(EventEmitter_1.EventEmitter));
exports.Entity = Entity;
