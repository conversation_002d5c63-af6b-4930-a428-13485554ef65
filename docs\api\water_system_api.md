# 水体系统API参考

本文档提供了水体系统的API参考，包括水体组件、水体物理系统、水体交互系统、水下粒子系统和水体光照系统的接口说明。

## 目录

1. [WaterBodyComponent](#waterbodycomponent)
2. [WaterPhysicsSystem](#waterphysicssystem)
3. [WaterInteractionSystem](#waterinteractionsystem)
4. [UnderwaterParticleSystem](#underwaterparticlesystem)
5. [WaterLightingSystem](#waterlightingsystem)
6. [WaterPresets](#waterpresets)

## WaterBodyComponent

`WaterBodyComponent` 是水体组件，用于定义水体的基本属性，如尺寸、位置、颜色等。

### 构造函数

```typescript
constructor()
```

创建一个新的水体组件实例。

### 属性

| 属性名 | 类型 | 描述 |
| --- | --- | --- |
| `entityId` | `string` | 实体ID |
| `size` | `{ width: number, height: number, depth: number }` | 水体尺寸 |
| `position` | `THREE.Vector3` | 水体位置 |
| `rotation` | `THREE.Euler` | 水体旋转 |
| `color` | `THREE.Color` | 水体颜色 |
| `opacity` | `number` | 水体透明度 |
| `reflectivity` | `number` | 水体反射率 |
| `refractivity` | `number` | 水体折射率 |
| `resolution` | `number` | 水体分辨率 |
| `waterType` | `string` | 水体类型 |

### 方法

#### 基本属性设置

```typescript
setSize(size: { width: number, height: number, depth: number }): void
```

设置水体尺寸。

```typescript
setPosition(position: THREE.Vector3): void
```

设置水体位置。

```typescript
setRotation(rotation: THREE.Euler): void
```

设置水体旋转。

```typescript
setColor(color: THREE.Color): void
```

设置水体颜色。

```typescript
setOpacity(opacity: number): void
```

设置水体透明度。

```typescript
setReflectivity(reflectivity: number): void
```

设置水体反射率。

```typescript
setRefractivity(refractivity: number): void
```

设置水体折射率。

```typescript
setResolution(resolution: number): void
```

设置水体分辨率。

```typescript
setWaterType(type: string): void
```

设置水体类型。

#### 波动和流动

```typescript
setWaveParams(params: {
  amplitude: number,
  frequency: number,
  speed: number,
  direction: THREE.Vector2
}): void
```

设置水体波动参数。

```typescript
setFlowDirection(direction: THREE.Vector3): void
```

设置水体流向。

```typescript
setFlowSpeed(speed: number): void
```

设置水体流速。

#### 物理属性

```typescript
setDensity(density: number): void
```

设置水体密度。

```typescript
setViscosity(viscosity: number): void
```

设置水体粘度。

```typescript
setSurfaceTension(surfaceTension: number): void
```

设置水体表面张力。

#### 高度图和法线图

```typescript
getHeightMap(): Float32Array | null
```

获取水体高度图。

```typescript
setHeightMap(heightMap: Float32Array): void
```

设置水体高度图。

```typescript
getNormalMap(): Float32Array | null
```

获取水体法线图。

```typescript
setNormalMap(normalMap: Float32Array): void
```

设置水体法线图。

#### 材质和网格

```typescript
getMaterial(): THREE.Material | null
```

获取水体材质。

```typescript
setMaterial(material: THREE.Material): void
```

设置水体材质。

```typescript
getMesh(): THREE.Mesh | null
```

获取水体网格。

```typescript
updateWaterMesh(): void
```

更新水体网格。

#### 初始化和更新

```typescript
initialize(): void
```

初始化水体。

```typescript
update(deltaTime: number): void
```

更新水体。

```typescript
dispose(): void
```

销毁水体。

## WaterPhysicsSystem

`WaterPhysicsSystem` 是水体物理系统，用于模拟水体的物理行为，如波动、流动、浮力等。

### 构造函数

```typescript
constructor(world: World, config?: WaterPhysicsSystemConfig)
```

创建一个新的水体物理系统实例。

### 配置

```typescript
interface WaterPhysicsSystemConfig {
  enabled?: boolean;
  autoUpdate?: boolean;
  updateFrequency?: number;
  enableBuoyancy?: boolean;
  enableDrag?: boolean;
  enableFlow?: boolean;
  enableWaves?: boolean;
  enableCollision?: boolean;
  enableParticles?: boolean;
  enableDebugVisualization?: boolean;
  enablePerformanceMonitoring?: boolean;
  enableSpatialPartitioning?: boolean;
  spatialGridSize?: number;
  enableAdaptiveUpdate?: boolean;
  minUpdateFrequency?: number;
  maxUpdateFrequency?: number;
  enableMultithreading?: boolean;
  workerCount?: number;
  enableWaterFlowImpact?: boolean;
  enableWaterSplitting?: boolean;
}
```

### 方法

#### 水体管理

```typescript
addWaterBody(entityId: string, waterBody: WaterBodyComponent): void
```

添加水体。

```typescript
removeWaterBody(entityId: string): void
```

移除水体。

```typescript
getWaterBody(entityId: string): WaterBodyComponent | undefined
```

获取水体。

```typescript
getWaterBodies(): Map<string, WaterBodyComponent>
```

获取所有水体。

#### 系统配置

```typescript
setConfig(config: Partial<WaterPhysicsSystemConfig>): void
```

设置系统配置。

```typescript
getConfig(): WaterPhysicsSystemConfig
```

获取系统配置。

#### 特效创建

```typescript
createSplashEffect(
  waterEntityId: string,
  position: THREE.Vector3,
  strength: number,
  size: THREE.Vector3
): void
```

创建水花效果。

```typescript
createRippleEffect(
  waterEntityId: string,
  position: THREE.Vector3,
  strength: number,
  direction: THREE.Vector3
): void
```

创建水波纹效果。

```typescript
createDropletEffect(
  waterEntityId: string,
  position: THREE.Vector3,
  strength: number,
  velocity: THREE.Vector3
): void
```

创建水滴效果。

```typescript
createSplittingEffect(
  waterEntityId: string,
  position: THREE.Vector3,
  strength: number,
  direction: THREE.Vector3
): void
```

创建水体分裂效果。

#### 事件监听

```typescript
on(eventType: WaterPhysicsSystemEventType, callback: (data: any) => void): void
```

注册事件监听器。

```typescript
off(eventType: WaterPhysicsSystemEventType, callback: (data: any) => void): void
```

移除事件监听器。

#### 系统生命周期

```typescript
initialize(): void
```

初始化系统。

```typescript
update(deltaTime: number): Promise<void>
```

更新系统。

```typescript
dispose(): void
```

销毁系统。

## WaterInteractionSystem

`WaterInteractionSystem` 是水体交互系统，用于处理水体与其他物体的交互效果，如水花、水波纹等。

### 构造函数

```typescript
constructor(world: World, config?: WaterInteractionSystemConfig)
```

创建一个新的水体交互系统实例。

### 配置

```typescript
interface WaterInteractionSystemConfig {
  enabled?: boolean;
  autoUpdate?: boolean;
  updateFrequency?: number;
  enableSplashEffect?: boolean;
  enableRippleEffect?: boolean;
  enableDropletEffect?: boolean;
  enableFlowEffect?: boolean;
  enableSplittingEffect?: boolean;
  enableBuoyancyEffect?: boolean;
  enableDragEffect?: boolean;
  enablePerformanceMonitoring?: boolean;
  enableDebugVisualization?: boolean;
  splashEffectStrength?: number;
  rippleEffectStrength?: number;
  dropletEffectStrength?: number;
  flowEffectStrength?: number;
  splittingEffectStrength?: number;
  buoyancyEffectStrength?: number;
  dragEffectStrength?: number;
}
```

### 方法

#### 水体管理

```typescript
addWaterBody(entityId: string, waterBody: WaterBodyComponent): void
```

添加水体。

```typescript
removeWaterBody(entityId: string): void
```

移除水体。

```typescript
getWaterBody(entityId: string): WaterBodyComponent | undefined
```

获取水体。

```typescript
getWaterBodies(): Map<string, WaterBodyComponent>
```

获取所有水体。

#### 系统配置

```typescript
setConfig(config: Partial<WaterInteractionSystemConfig>): void
```

设置系统配置。

```typescript
getConfig(): WaterInteractionSystemConfig
```

获取系统配置。

#### 事件监听

```typescript
on(eventType: WaterInteractionSystemEventType, callback: (data: any) => void): void
```

注册事件监听器。

```typescript
off(eventType: WaterInteractionSystemEventType, callback: (data: any) => void): void
```

移除事件监听器。

#### 系统生命周期

```typescript
initialize(): void
```

初始化系统。

```typescript
update(deltaTime: number): void
```

更新系统。

```typescript
dispose(): void
```

销毁系统。

## UnderwaterParticleSystem

`UnderwaterParticleSystem` 是水下粒子系统，用于创建水下粒子效果，如气泡、悬浮物等。

### 构造函数

```typescript
constructor(world: World, config?: UnderwaterParticleSystemConfig)
```

创建一个新的水下粒子系统实例。

### 配置

```typescript
interface UnderwaterParticleSystemConfig {
  enabled?: boolean;
  autoUpdate?: boolean;
  updateFrequency?: number;
  maxParticles?: number;
  enablePerformanceMonitoring?: boolean;
}
```

### 方法

#### 水体管理

```typescript
addWaterBody(entityId: string, waterBody: WaterBodyComponent): void
```

添加水体。

```typescript
removeWaterBody(entityId: string): void
```

移除水体。

```typescript
getWaterBody(entityId: string): WaterBodyComponent | undefined
```

获取水体。

```typescript
getWaterBodies(): Map<string, WaterBodyComponent>
```

获取所有水体。

#### 粒子管理

```typescript
addParticleGroup(
  entityId: string,
  groupId: string,
  params: {
    type: UnderwaterParticleType;
    count: number;
    size: [number, number];
    color: number;
    opacity: number;
    lifetime: [number, number];
    speed?: [number, number];
    acceleration?: THREE.Vector3;
    rotation?: boolean;
    rotationSpeed?: [number, number];
    blending?: THREE.Blending;
    emissionArea: {
      shape: 'box' | 'sphere' | 'cylinder';
      size: THREE.Vector3;
      position: THREE.Vector3;
    };
  }
): void
```

添加粒子组。

```typescript
removeParticleGroup(entityId: string, groupId: string): void
```

移除粒子组。

```typescript
getParticleGroup(entityId: string, groupId: string): any
```

获取粒子组。

```typescript
getParticleGroups(entityId: string): Map<string, any>
```

获取所有粒子组。

#### 系统配置

```typescript
setConfig(config: Partial<UnderwaterParticleSystemConfig>): void
```

设置系统配置。

```typescript
getConfig(): UnderwaterParticleSystemConfig
```

获取系统配置。

#### 系统生命周期

```typescript
initialize(): void
```

初始化系统。

```typescript
update(deltaTime: number): void
```

更新系统。

```typescript
dispose(): void
```

销毁系统。

## WaterLightingSystem

`WaterLightingSystem` 是水体光照系统，用于处理水体的光照效果，如反射、折射、焦散等。

### 构造函数

```typescript
constructor(world: World, config?: WaterLightingSystemConfig)
```

创建一个新的水体光照系统实例。

### 配置

```typescript
interface WaterLightingSystemConfig {
  enabled?: boolean;
  autoUpdate?: boolean;
  updateFrequency?: number;
  enableReflection?: boolean;
  enableRefraction?: boolean;
  enableCaustics?: boolean;
  enableVolumetricLight?: boolean;
  enableUnderwaterFog?: boolean;
  enablePerformanceMonitoring?: boolean;
  reflectionMapResolution?: number;
  refractionMapResolution?: number;
  causticsMapResolution?: number;
  volumetricLightMapResolution?: number;
  reflectionIntensity?: number;
  refractionIntensity?: number;
  causticsIntensity?: number;
  volumetricLightIntensity?: number;
  underwaterFogIntensity?: number;
  underwaterFogColor?: THREE.Color;
  underwaterFogDensity?: number;
  enableHighQualityReflection?: boolean;
  enableHighQualityRefraction?: boolean;
  enableHighQualityCaustics?: boolean;
  enableHighQualityVolumetricLight?: boolean;
}
```

### 方法

#### 水体管理

```typescript
addWaterBody(entityId: string, waterBody: WaterBodyComponent): void
```

添加水体。

```typescript
removeWaterBody(entityId: string): void
```

移除水体。

```typescript
getWaterBody(entityId: string): WaterBodyComponent | undefined
```

获取水体。

```typescript
getWaterBodies(): Map<string, WaterBodyComponent>
```

获取所有水体。

#### 系统配置

```typescript
setConfig(config: Partial<WaterLightingSystemConfig>): void
```

设置系统配置。

```typescript
getConfig(): WaterLightingSystemConfig
```

获取系统配置。

#### 事件监听

```typescript
on(eventType: WaterLightingSystemEventType, callback: (data: any) => void): void
```

注册事件监听器。

```typescript
off(eventType: WaterLightingSystemEventType, callback: (data: any) => void): void
```

移除事件监听器。

#### 系统生命周期

```typescript
initialize(): void
```

初始化系统。

```typescript
update(deltaTime: number): void
```

更新系统。

```typescript
dispose(): void
```

销毁系统。

## WaterPresets

`WaterPresets` 是水体预设，提供各种类型的水体预设配置，如湖泊、河流、海洋等。

### 静态方法

```typescript
static createPreset(world: World, config: WaterPresetConfig): WaterBodyComponent
```

创建水体预设。

### 预设配置

```typescript
interface WaterPresetConfig {
  type: WaterPresetType;
  size?: {
    width: number;
    height: number;
    depth: number;
  };
  position?: THREE.Vector3;
  rotation?: THREE.Euler;
  color?: THREE.Color;
  opacity?: number;
  reflectivity?: number;
  refractivity?: number;
  waveParams?: {
    amplitude: number;
    frequency: number;
    speed: number;
    direction: THREE.Vector2;
  };
  flowParams?: {
    direction: THREE.Vector3;
    speed: number;
  };
  physicsParams?: {
    density: number;
    viscosity: number;
    surfaceTension: number;
  };
  lightingParams?: {
    reflectionIntensity: number;
    refractionIntensity: number;
    causticsIntensity: number;
    volumetricLightIntensity: number;
    underwaterFogIntensity: number;
    underwaterFogColor: THREE.Color;
    underwaterFogDensity: number;
  };
  particleParams?: {
    bubbleCount: number;
    debrisCount: number;
    lightShaftCount: number;
    fishSchoolCount: number;
    seaweedCount: number;
    planktonCount: number;
    waterCurrentCount: number;
    sedimentCount: number;
    foamCount: number;
    mistCount: number;
    splashCount: number;
    dropletCount: number;
    rippleCount: number;
    algaeCount: number;
  };
  interactionParams?: {
    splashEffectStrength: number;
    rippleEffectStrength: number;
    dropletEffectStrength: number;
    flowEffectStrength: number;
    splittingEffectStrength: number;
    buoyancyEffectStrength: number;
    dragEffectStrength: number;
  };
}
```

### 预设类型

```typescript
enum WaterPresetType {
  LAKE = 'lake',
  RIVER = 'river',
  OCEAN = 'ocean',
  POOL = 'pool',
  HOT_SPRING = 'hot_spring',
  UNDERGROUND_LAKE = 'underground_lake',
  UNDERGROUND_RIVER = 'underground_river',
  WATERFALL = 'waterfall',
  SHALLOW = 'shallow',
  SWAMP = 'swamp',
  ICE_LAKE = 'ice_lake',
  LAVA = 'lava'
}
```
