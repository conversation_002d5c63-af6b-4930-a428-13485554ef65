# 子片段文档

## 概述

子片段功能允许从现有动画中提取部分片段，并对其进行修改、组合和控制。通过子片段，可以创建更丰富、更灵活的动画效果。

## 基本子片段

基本子片段（SubClip）允许从动画中提取特定时间范围的片段。

```typescript
const walkClip = animations.find(clip => clip.name === 'walk');

// 创建行走开始子片段
const walkStart = new SubClip({
  name: 'walkStart',
  originalClip: walkClip,
  startTime: 0,
  endTime: walkClip.duration / 3,
  loop: false
});

// 创建行走循环子片段
const walkLoop = new SubClip({
  name: 'walkLoop',
  originalClip: walkClip,
  startTime: walkClip.duration / 3,
  endTime: walkClip.duration * 2 / 3,
  loop: true
});

// 创建行走结束子片段
const walkEnd = new SubClip({
  name: 'walkEnd',
  originalClip: walkClip,
  startTime: walkClip.duration * 2 / 3,
  endTime: walkClip.duration,
  loop: false
});
```

### 播放子片段

```typescript
// 获取子片段
const clip = walkLoop.getClip();

// 创建动作
const action = mixer.clipAction(clip);

// 播放动作
action.play();
```

## 高级子片段

高级子片段（AnimationSubClip）提供更多控制选项，如反向播放和时间缩放。

```typescript
const runClip = animations.find(clip => clip.name === 'run');

// 创建反向播放子片段
const runReverse = new AnimationSubClip({
  name: 'runReverse',
  originalClipName: 'run',
  startTime: 0,
  endTime: runClip.duration,
  loop: true,
  reverse: true
});
runReverse.setOriginalClip(runClip);

// 创建慢速播放子片段
const runSlow = new AnimationSubClip({
  name: 'runSlow',
  originalClipName: 'run',
  startTime: 0,
  endTime: runClip.duration,
  loop: true,
  timeScale: 0.5
});
runSlow.setOriginalClip(runClip);
```

### 播放高级子片段

```typescript
// 获取子片段
const clip = runReverse.getSubClip();

// 创建动作
const action = mixer.clipAction(clip);

// 播放动作
action.play();
```

## 子片段序列

子片段序列（SubClipSequence）允许按顺序播放多个子片段，创建复杂的动画序列。

```typescript
// 创建子片段序列
const sequence = new SubClipSequence({
  name: 'walkToRun',
  loop: true,
  autoPlay: false
});

// 添加子片段到序列
sequence.addSubClip(walkStart, 1.0, 0.3);
sequence.addSubClip(walkLoop, 2.0, 0.3);
sequence.addSubClip(runStart, 1.0, 0.3);
sequence.addSubClip(runLoop, 2.0, 0.3);
sequence.addSubClip(runEnd, 1.0, 0.3);
sequence.addSubClip(walkEnd, 1.0, 0.3);
```

### 控制序列

```typescript
// 播放序列
sequence.play();

// 暂停序列
sequence.pause();

// 停止序列
sequence.stop();

// 跳转到特定时间
sequence.setTime(2.5);

// 跳转到特定索引
sequence.setCurrentIndex(2);
```

### 监听序列事件

```typescript
// 监听序列开始事件
sequence.addEventListener(SubClipSequenceEventType.SEQUENCE_START, (data) => {
  console.log('序列开始:', data);
});

// 监听子片段开始事件
sequence.addEventListener(SubClipSequenceEventType.CLIP_START, (data) => {
  console.log('子片段开始:', data);
});

// 监听子片段结束事件
sequence.addEventListener(SubClipSequenceEventType.CLIP_END, (data) => {
  console.log('子片段结束:', data);
});

// 监听序列结束事件
sequence.addEventListener(SubClipSequenceEventType.SEQUENCE_END, (data) => {
  console.log('序列结束:', data);
});

// 监听序列循环事件
sequence.addEventListener(SubClipSequenceEventType.SEQUENCE_LOOP, (data) => {
  console.log('序列循环:', data);
});
```

## 子片段过渡

子片段过渡（SubClipTransition）允许在两个子片段之间平滑过渡。

```typescript
// 创建子片段过渡
const transition = new SubClipTransition({
  name: 'walkToRun',
  fromClip: walkLoop,
  toClip: runLoop,
  duration: 1.0,
  type: TransitionType.EASE_IN_OUT
});
```

### 控制过渡

```typescript
// 开始过渡
transition.start();

// 停止过渡
transition.stop();

// 获取过渡进度
const progress = transition.getProgress();
```

### 过渡类型

```typescript
enum TransitionType {
  /** 线性 */
  LINEAR = 'linear',
  /** 缓入 */
  EASE_IN = 'easeIn',
  /** 缓出 */
  EASE_OUT = 'easeOut',
  /** 缓入缓出 */
  EASE_IN_OUT = 'easeInOut',
  /** 自定义 */
  CUSTOM = 'custom'
}
```

### 自定义过渡函数

```typescript
// 创建自定义过渡
const customTransition = new SubClipTransition({
  name: 'customTransition',
  fromClip: walkLoop,
  toClip: runLoop,
  duration: 1.0,
  type: TransitionType.CUSTOM
});

// 设置自定义过渡函数
customTransition.setCustomTransition((t) => {
  // 弹性过渡
  const s = 1.70158;
  return t * t * ((s + 1) * t - s);
});
```

## 子片段变形

子片段变形（SubClipModifier）允许修改子片段的播放方式，如时间缩放、反向播放、循环等。

```typescript
// 创建时间缩放变形器
const timeScaleModifier = new SubClipModifier({
  name: 'timeScale',
  type: ModifierType.TIME_SCALE,
  params: { scale: 2.0 },
  enabled: true
});

// 创建反向播放变形器
const reverseModifier = new SubClipModifier({
  name: 'reverse',
  type: ModifierType.REVERSE,
  enabled: true
});

// 创建循环变形器
const loopModifier = new SubClipModifier({
  name: 'loop',
  type: ModifierType.LOOP,
  params: { count: 3, blendTime: 0.1 },
  enabled: true
});

// 创建镜像变形器
const mirrorModifier = new SubClipModifier({
  name: 'mirror',
  type: ModifierType.MIRROR,
  params: { axis: 'x' },
  enabled: true
});
```

### 应用变形

```typescript
// 获取原始片段
const originalClip = walkLoop.getClip();

// 应用变形
const modifiedClip = timeScaleModifier.apply(originalClip);

// 创建动作
const action = mixer.clipAction(modifiedClip);

// 播放动作
action.play();
```

### 变形类型

```typescript
enum ModifierType {
  /** 时间缩放 */
  TIME_SCALE = 'timeScale',
  /** 反向播放 */
  REVERSE = 'reverse',
  /** 循环 */
  LOOP = 'loop',
  /** 镜像 */
  MIRROR = 'mirror',
  /** 抖动 */
  JITTER = 'jitter',
  /** 延迟 */
  DELAY = 'delay',
  /** 平滑 */
  SMOOTH = 'smooth',
  /** 噪声 */
  NOISE = 'noise',
  /** 自定义 */
  CUSTOM = 'custom'
}
```

### 自定义变形

```typescript
// 创建自定义变形器
const customModifier = new SubClipModifier({
  name: 'custom',
  type: ModifierType.CUSTOM,
  enabled: true
});

// 设置自定义变形函数
customModifier.setCustomModifier((clip, params) => {
  // 创建新的动画片段
  const modifiedClip = THREE.AnimationClip.parse(THREE.AnimationClip.toJSON(clip));
  
  // 修改片段
  // ...
  
  return modifiedClip;
});
```

## 子片段事件

子片段事件（SubClipEvent）允许在子片段播放过程中触发事件。

```typescript
// 创建跳跃开始事件
const jumpStartEvent = new SubClipEvent({
  name: 'jumpStart',
  triggerType: EventTriggerType.START,
  callback: (event) => {
    console.log('跳跃开始:', event);
  },
  once: false,
  enabled: true
});

// 创建跳跃高点事件
const jumpPeakEvent = new SubClipEvent({
  name: 'jumpPeak',
  triggerType: EventTriggerType.PERCENT,
  triggerValue: 0.5,
  callback: (event) => {
    console.log('跳跃高点:', event);
  },
  once: true,
  enabled: true
});

// 创建跳跃结束事件
const jumpEndEvent = new SubClipEvent({
  name: 'jumpEnd',
  triggerType: EventTriggerType.END,
  callback: (event) => {
    console.log('跳跃结束:', event);
  },
  once: false,
  enabled: true
});
```

### 检查和触发事件

```typescript
// 创建子片段
const jumpSubClip = new SubClip({
  name: 'jumpFull',
  originalClip: jumpClip,
  startTime: 0,
  endTime: jumpClip.duration,
  loop: false
});

// 监听混合器更新
mixer.addEventListener('loop', (event) => {
  const time = action.time;
  const progress = time / jumpClip.duration;

  // 检查事件
  if (jumpStartEvent.shouldTrigger(time, progress, jumpSubClip)) {
    jumpStartEvent.trigger(time, progress, jumpSubClip);
  }
  if (jumpPeakEvent.shouldTrigger(time, progress, jumpSubClip)) {
    jumpPeakEvent.trigger(time, progress, jumpSubClip);
  }
  if (jumpEndEvent.shouldTrigger(time, progress, jumpSubClip)) {
    jumpEndEvent.trigger(time, progress, jumpSubClip);
  }
});
```

### 事件触发类型

```typescript
enum EventTriggerType {
  /** 时间点 */
  TIME = 'time',
  /** 百分比 */
  PERCENT = 'percent',
  /** 帧 */
  FRAME = 'frame',
  /** 开始 */
  START = 'start',
  /** 结束 */
  END = 'end',
  /** 循环 */
  LOOP = 'loop',
  /** 条件 */
  CONDITION = 'condition'
}
```

### 自定义触发条件

```typescript
// 创建自定义条件事件
const customEvent = new SubClipEvent({
  name: 'customEvent',
  triggerType: EventTriggerType.CONDITION,
  callback: (event) => {
    console.log('自定义事件:', event);
  },
  once: false,
  enabled: true
});

// 设置触发条件
customEvent.setTriggerCondition((time, progress, clip) => {
  // 在特定条件下触发
  return time > 1.0 && time < 2.0 && progress > 0.3;
});
```

## 子片段编辑器

子片段编辑器（SubClipEditor）提供可视化编辑子片段的功能。

```typescript
// 创建子片段编辑器
const editor = new SubClipEditor({
  name: 'subClipEditor',
  debug: true
});

// 添加子片段
editor.addSubClip(walkStart);
editor.addSubClip(walkLoop);
editor.addSubClip(walkEnd);

// 添加事件
editor.addEvent(jumpStartEvent);
editor.addEvent(jumpPeakEvent);
editor.addEvent(jumpEndEvent);

// 添加序列
editor.addSequence(sequence);

// 添加过渡
editor.addTransition(transition);

// 添加变形器
editor.addModifier(timeScaleModifier);
```

### 编辑操作

```typescript
// 选择子片段
editor.selectSubClip(walkLoop);

// 选择事件
editor.selectEvent(jumpPeakEvent);

// 选择序列
editor.selectSequence(sequence);

// 选择过渡
editor.selectTransition(transition);

// 选择变形器
editor.selectModifier(timeScaleModifier);

// 设置当前时间
editor.setCurrentTime(1.5);

// 开始预览
editor.startPreview();

// 停止预览
editor.stopPreview();

// 设置预览速度
editor.setPreviewSpeed(0.5);

// 撤销
editor.undo();

// 重做
editor.redo();
```

### 监听编辑器事件

```typescript
// 监听选择改变事件
editor.addEventListener(SubClipEditorEventType.SELECTION_CHANGED, (data) => {
  console.log('选择改变:', data);
});

// 监听时间改变事件
editor.addEventListener(SubClipEditorEventType.TIME_CHANGED, (data) => {
  console.log('时间改变:', data);
});

// 监听编辑操作事件
editor.addEventListener(SubClipEditorEventType.EDIT_OPERATION, (data) => {
  console.log('编辑操作:', data);
});

// 监听预览状态改变事件
editor.addEventListener(SubClipEditorEventType.PREVIEW_STATE_CHANGED, (data) => {
  console.log('预览状态改变:', data);
});
```

## 示例

### 创建行走循环

```typescript
const walkClip = animations.find(clip => clip.name === 'walk');

// 创建行走循环子片段
const walkLoop = new SubClip({
  name: 'walkLoop',
  originalClip: walkClip,
  startTime: walkClip.duration / 3,
  endTime: walkClip.duration * 2 / 3,
  loop: true
});

// 获取子片段
const clip = walkLoop.getClip();

// 创建动作
const action = mixer.clipAction(clip);

// 播放动作
action.play();
```

### 创建行走到跑步序列

```typescript
// 创建子片段序列
const sequence = new SubClipSequence({
  name: 'walkToRun',
  loop: true,
  autoPlay: false
});

// 添加子片段到序列
sequence.addSubClip(walkStart, 1.0, 0.3);
sequence.addSubClip(walkLoop, 2.0, 0.3);
sequence.addSubClip(runStart, 1.0, 0.3);
sequence.addSubClip(runLoop, 2.0, 0.3);
sequence.addSubClip(runEnd, 1.0, 0.3);
sequence.addSubClip(walkEnd, 1.0, 0.3);

// 播放序列
sequence.play();
```

### 创建行走到跑步过渡

```typescript
// 创建子片段过渡
const transition = new SubClipTransition({
  name: 'walkToRun',
  fromClip: walkLoop,
  toClip: runLoop,
  duration: 1.0,
  type: TransitionType.EASE_IN_OUT
});

// 开始过渡
transition.start();
```

### 创建加速跑步效果

```typescript
// 创建时间缩放变形器
const timeScaleModifier = new SubClipModifier({
  name: 'timeScale',
  type: ModifierType.TIME_SCALE,
  params: { scale: 2.0 },
  enabled: true
});

// 获取原始片段
const originalClip = runLoop.getClip();

// 应用变形
const modifiedClip = timeScaleModifier.apply(originalClip);

// 创建动作
const action = mixer.clipAction(modifiedClip);

// 播放动作
action.play();
```

## 最佳实践

1. 使用子片段提取动画中的关键部分，如开始、循环和结束。
2. 使用子片段序列创建复杂的动画序列。
3. 使用子片段过渡创建平滑的动画过渡。
4. 使用子片段变形创建特殊效果，如加速、减速、反向播放等。
5. 使用子片段事件在关键时刻触发回调，如播放音效、生成粒子等。
6. 使用子片段编辑器可视化编辑和管理子片段。
7. 结合混合模式和遮罩，创建更复杂的动画效果。
8. 测试不同的子片段组合，以获得最佳效果。
