"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Engine = void 0;
/**
 * 引擎核心类
 * 负责管理世界、系统和渲染循环
 */
var World_1 = require("./World");
var Time_1 = require("../utils/Time");
var EventEmitter_1 = require("../utils/EventEmitter");
var RenderSystem_1 = require("../rendering/RenderSystem");
var Renderer_1 = require("../rendering/Renderer");
var AssetManager_1 = require("../assets/AssetManager");
var I18n_1 = require("../i18n/I18n");
var Engine = /** @class */ (function (_super) {
    __extends(Engine, _super);
    /**
     * 创建引擎实例
     * @param options 引擎选项
     */
    function Engine(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this) || this;
        /** 系统列表 */
        _this.systems = [];
        /** 活跃相机 */
        _this.activeCamera = null;
        /** 是否正在运行 */
        _this.running = false;
        /** 是否已初始化 */
        _this.initialized = false;
        /** 动画帧ID */
        _this.animationFrameId = 0;
        /** 上一帧时间戳 */
        _this.lastFrameTime = 0;
        /** 固定更新累积时间 */
        _this.fixedUpdateAccumulator = 0;
        /** 固定更新时间步长（秒） */
        _this.fixedUpdateTimeStep = 1 / 60;
        // 创建世界
        _this.world = new World_1.World(_this);
        // 创建渲染器
        _this.renderer = new Renderer_1.Renderer({
            canvas: options.canvas,
        });
        // 创建资产管理器
        _this.assetManager = new AssetManager_1.AssetManager();
        // 创建国际化实例
        _this.i18n = new I18n_1.I18n({
            language: options.language || 'zh-CN',
        });
        // 设置调试模式
        _this.debug = options.debug || false;
        // 如果设置了自动开始，则初始化并开始渲染循环
        if (options.autoStart) {
            _this.initialize();
            _this.start();
        }
        return _this;
    }
    /**
     * 初始化引擎
     */
    Engine.prototype.initialize = function () {
        if (this.initialized) {
            return;
        }
        // 初始化时间
        Time_1.Time.initialize();
        // 初始化资产管理器
        this.assetManager.initialize();
        // 添加默认系统
        this.addSystem(new RenderSystem_1.RenderSystem(this.renderer));
        // 初始化所有系统
        for (var _i = 0, _a = this.systems; _i < _a.length; _i++) {
            var system = _a[_i];
            system.initialize();
        }
        this.initialized = true;
        this.emit('initialized');
    };
    /**
     * 开始渲染循环
     */
    Engine.prototype.start = function () {
        if (this.running) {
            return;
        }
        // 确保引擎已初始化
        if (!this.initialized) {
            this.initialize();
        }
        this.running = true;
        this.lastFrameTime = performance.now();
        this.animationFrameId = requestAnimationFrame(this.update.bind(this));
        this.emit('started');
    };
    /**
     * 停止渲染循环
     */
    Engine.prototype.stop = function () {
        if (!this.running) {
            return;
        }
        this.running = false;
        cancelAnimationFrame(this.animationFrameId);
        this.emit('stopped');
    };
    /**
     * 更新循环
     * @param timestamp 当前时间戳
     */
    Engine.prototype.update = function (timestamp) {
        if (!this.running) {
            return;
        }
        // 计算帧间隔时间
        var deltaTime = (timestamp - this.lastFrameTime) / 1000;
        this.lastFrameTime = timestamp;
        // 更新时间
        Time_1.Time.update(deltaTime);
        // 更新世界
        this.world.update(deltaTime);
        // 固定时间步长更新
        this.fixedUpdateAccumulator += deltaTime;
        while (this.fixedUpdateAccumulator >= this.fixedUpdateTimeStep) {
            // 固定更新世界
            this.world.fixedUpdate(this.fixedUpdateTimeStep);
            // 固定更新系统
            for (var _i = 0, _a = this.systems; _i < _a.length; _i++) {
                var system = _a[_i];
                system.fixedUpdate(this.fixedUpdateTimeStep);
            }
            this.fixedUpdateAccumulator -= this.fixedUpdateTimeStep;
        }
        // 更新系统
        for (var _b = 0, _c = this.systems; _b < _c.length; _b++) {
            var system = _c[_b];
            system.update(deltaTime);
        }
        // 后更新系统
        for (var _d = 0, _e = this.systems; _d < _e.length; _d++) {
            var system = _e[_d];
            system.lateUpdate(deltaTime);
        }
        // 发出更新事件
        this.emit('update', deltaTime);
        // 继续渲染循环
        this.animationFrameId = requestAnimationFrame(this.update.bind(this));
    };
    /**
     * 添加系统
     * @param system 系统实例
     * @returns 添加的系统
     */
    Engine.prototype.addSystem = function (system) {
        // 设置系统的引擎引用
        system.setEngine(this);
        // 添加到系统列表
        this.systems.push(system);
        // 如果引擎已初始化，则初始化系统
        if (this.initialized) {
            system.initialize();
        }
        // 按优先级排序系统
        this.systems.sort(function (a, b) { return a.getPriority() - b.getPriority(); });
        return system;
    };
    /**
     * 获取系统
     * @param type 系统类型
     * @returns 系统实例，如果不存在则返回null
     */
    Engine.prototype.getSystem = function (type) {
        for (var _i = 0, _a = this.systems; _i < _a.length; _i++) {
            var system = _a[_i];
            if (system.getType() === type) {
                return system;
            }
        }
        return null;
    };
    /**
     * 移除系统
     * @param system 系统实例或类型
     * @returns 是否成功移除
     */
    Engine.prototype.removeSystem = function (system) {
        var index = typeof system === 'string'
            ? this.systems.findIndex(function (s) { return s.getType() === system; })
            : this.systems.indexOf(system);
        if (index !== -1) {
            var removedSystem = this.systems[index];
            removedSystem.dispose();
            this.systems.splice(index, 1);
            return true;
        }
        return false;
    };
    /**
     * 获取世界
     * @returns 世界实例
     */
    Engine.prototype.getWorld = function () {
        return this.world;
    };
    /**
     * 获取渲染器
     * @returns 渲染器实例
     */
    Engine.prototype.getRenderer = function () {
        return this.renderer;
    };
    /**
     * 设置活跃相机
     * @param camera 相机实例
     */
    Engine.prototype.setActiveCamera = function (camera) {
        this.activeCamera = camera;
    };
    /**
     * 获取活跃相机
     * @returns 活跃相机实例
     */
    Engine.prototype.getActiveCamera = function () {
        return this.activeCamera;
    };
    /**
     * 获取资产管理器
     * @returns 资产管理器实例
     */
    Engine.prototype.getAssetManager = function () {
        return this.assetManager;
    };
    /**
     * 获取国际化实例
     * @returns 国际化实例
     */
    Engine.prototype.getI18n = function () {
        return this.i18n;
    };
    /**
     * 是否处于调试模式
     * @returns 是否处于调试模式
     */
    Engine.prototype.isDebug = function () {
        return this.debug;
    };
    /**
     * 设置调试模式
     * @param debug 是否启用调试模式
     */
    Engine.prototype.setDebug = function (debug) {
        this.debug = debug;
    };
    /**
     * 是否正在运行
     * @returns 是否正在运行
     */
    Engine.prototype.isRunning = function () {
        return this.running;
    };
    /**
     * 销毁引擎
     */
    Engine.prototype.dispose = function () {
        // 停止渲染循环
        this.stop();
        // 销毁所有系统
        for (var _i = 0, _a = this.systems; _i < _a.length; _i++) {
            var system = _a[_i];
            system.dispose();
        }
        this.systems = [];
        // 销毁世界
        this.world.dispose();
        // 销毁渲染器
        this.renderer.dispose();
        // 销毁资产管理器
        this.assetManager.dispose();
        // 移除所有事件监听器
        this.removeAllListeners();
        this.initialized = false;
    };
    return Engine;
}(EventEmitter_1.EventEmitter));
exports.Engine = Engine;
