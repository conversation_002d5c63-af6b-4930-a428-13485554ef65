# 水体系统教程

本教程将介绍如何使用引擎的水体系统创建各种类型的水体效果，包括湖泊、河流、海洋、地下水体等。

## 目录

1. [水体系统概述](#水体系统概述)
2. [创建基本水体](#创建基本水体)
3. [使用水体预设](#使用水体预设)
4. [自定义水体属性](#自定义水体属性)
5. [水体物理交互](#水体物理交互)
6. [水体粒子效果](#水体粒子效果)
7. [水体光照效果](#水体光照效果)
8. [高级水体效果](#高级水体效果)
9. [性能优化](#性能优化)
10. [常见问题解答](#常见问题解答)

## 水体系统概述

水体系统由以下几个主要组件组成：

- **WaterBodyComponent**：水体组件，用于定义水体的基本属性，如尺寸、位置、颜色等。
- **WaterPhysicsSystem**：水体物理系统，用于模拟水体的物理行为，如波动、流动、浮力等。
- **WaterInteractionSystem**：水体交互系统，用于处理水体与其他物体的交互效果，如水花、水波纹等。
- **UnderwaterParticleSystem**：水下粒子系统，用于创建水下粒子效果，如气泡、悬浮物等。
- **WaterLightingSystem**：水体光照系统，用于处理水体的光照效果，如反射、折射、焦散等。
- **WaterPresets**：水体预设，提供各种类型的水体预设配置，如湖泊、河流、海洋等。

## 创建基本水体

### 步骤1：创建水体组件

```typescript
// 创建水体组件
const waterBody = new WaterBodyComponent();

// 设置水体基本属性
waterBody.setSize({ width: 100, height: 10, depth: 100 });
waterBody.setPosition(new THREE.Vector3(0, 0, 0));
waterBody.setColor(new THREE.Color(0x0077be));
waterBody.setOpacity(0.8);

// 初始化水体
waterBody.initialize();
```

### 步骤2：添加到系统

```typescript
// 获取系统
const waterPhysicsSystem = world.getSystem('WaterPhysicsSystem');
const waterInteractionSystem = world.getSystem('WaterInteractionSystem');
const underwaterParticleSystem = world.getSystem('UnderwaterParticleSystem');
const waterLightingSystem = world.getSystem('WaterLightingSystem');

// 添加到系统
waterPhysicsSystem.addWaterBody(waterBody.getEntityId(), waterBody);
waterInteractionSystem.addWaterBody(waterBody.getEntityId(), waterBody);
underwaterParticleSystem.addWaterBody(waterBody.getEntityId(), waterBody);
waterLightingSystem.addWaterBody(waterBody.getEntityId(), waterBody);
```

## 使用水体预设

水体预设提供了各种类型的水体配置，可以快速创建不同类型的水体。

```typescript
// 使用预设创建湖泊
const lakeWaterBody = WaterPresets.createPreset(world, {
  type: WaterPresetType.LAKE,
  size: { width: 100, height: 10, depth: 100 },
  position: new THREE.Vector3(0, 0, 0)
});

// 使用预设创建河流
const riverWaterBody = WaterPresets.createPreset(world, {
  type: WaterPresetType.RIVER,
  size: { width: 20, height: 5, depth: 100 },
  position: new THREE.Vector3(0, 0, 0),
  rotation: new THREE.Euler(0, Math.PI / 4, 0)
});

// 使用预设创建海洋
const oceanWaterBody = WaterPresets.createPreset(world, {
  type: WaterPresetType.OCEAN,
  size: { width: 1000, height: 50, depth: 1000 },
  position: new THREE.Vector3(0, 0, 0)
});

// 使用预设创建地下湖泊
const undergroundLakeWaterBody = WaterPresets.createPreset(world, {
  type: WaterPresetType.UNDERGROUND_LAKE,
  size: { width: 80, height: 8, depth: 80 },
  position: new THREE.Vector3(0, -20, 0)
});

// 使用预设创建温泉
const hotSpringWaterBody = WaterPresets.createPreset(world, {
  type: WaterPresetType.HOT_SPRING,
  size: { width: 10, height: 3, depth: 10 },
  position: new THREE.Vector3(0, 0, 0)
});
```

## 自定义水体属性

### 波动参数

```typescript
// 设置波动参数
waterBody.setWaveParams({
  amplitude: 0.1,  // 波动振幅
  frequency: 0.5,  // 波动频率
  speed: 0.3,      // 波动速度
  direction: new THREE.Vector2(1, 1)  // 波动方向
});
```

### 流动参数

```typescript
// 设置流动参数
waterBody.setFlowDirection(new THREE.Vector3(1, 0, 0));  // 流向
waterBody.setFlowSpeed(0.5);  // 流速
```

### 物理参数

```typescript
// 设置物理参数
waterBody.setDensity(1.0);  // 密度
waterBody.setViscosity(0.5);  // 粘度
waterBody.setSurfaceTension(0.7);  // 表面张力
```

### 光照参数

```typescript
// 设置光照参数
waterBody.setReflectivity(0.5);  // 反射率
waterBody.setRefractivity(0.7);  // 折射率
```

## 水体物理交互

水体物理系统提供了多种物理交互效果，如浮力、阻力、水流冲击等。

```typescript
// 配置水体物理系统
const waterPhysicsSystemConfig = {
  enabled: true,
  autoUpdate: true,
  updateFrequency: 1,
  enableBuoyancy: true,
  enableDrag: true,
  enableFlow: true,
  enableWaves: true,
  enableCollision: true,
  enableParticles: true,
  enableMultithreading: true,
  workerCount: 4
};

// 创建水体物理系统
const waterPhysicsSystem = new WaterPhysicsSystem(world, waterPhysicsSystemConfig);
world.addSystem(waterPhysicsSystem);
```

## 水体粒子效果

水下粒子系统提供了多种粒子效果，如气泡、悬浮物、光束等。

```typescript
// 配置水下粒子系统
const underwaterParticleSystemConfig = {
  enabled: true,
  autoUpdate: true,
  updateFrequency: 1,
  maxParticles: 1000
};

// 创建水下粒子系统
const underwaterParticleSystem = new UnderwaterParticleSystem(world, underwaterParticleSystemConfig);
world.addSystem(underwaterParticleSystem);
```

## 水体光照效果

水体光照系统提供了多种光照效果，如反射、折射、焦散、体积光等。

```typescript
// 配置水体光照系统
const waterLightingSystemConfig = {
  enabled: true,
  autoUpdate: true,
  updateFrequency: 1,
  enableReflection: true,
  enableRefraction: true,
  enableCaustics: true,
  enableVolumetricLight: true,
  enableUnderwaterFog: true,
  reflectionMapResolution: 512,
  refractionMapResolution: 512,
  causticsMapResolution: 512,
  volumetricLightMapResolution: 256
};

// 创建水体光照系统
const waterLightingSystem = new WaterLightingSystem(world, waterLightingSystemConfig);
world.addSystem(waterLightingSystem);
```

## 高级水体效果

### 水体交互效果

```typescript
// 配置水体交互系统
const waterInteractionSystemConfig = {
  enabled: true,
  autoUpdate: true,
  updateFrequency: 1,
  enableSplashEffect: true,
  enableRippleEffect: true,
  enableDropletEffect: true,
  enableFlowEffect: true,
  enableSplittingEffect: true,
  enableBuoyancyEffect: true,
  enableDragEffect: true
};

// 创建水体交互系统
const waterInteractionSystem = new WaterInteractionSystem(world, waterInteractionSystemConfig);
world.addSystem(waterInteractionSystem);
```

### 水体分裂效果

```typescript
// 创建水体分裂效果
waterPhysicsSystem.createSplittingEffect(
  waterBody.getEntityId(),
  new THREE.Vector3(0, 0, 0),
  0.5,
  new THREE.Vector3(1, 0, 0)
);
```

## 性能优化

### 多线程计算

```typescript
// 启用多线程计算
waterPhysicsSystem.setConfig({
  enableMultithreading: true,
  workerCount: 4
});
```

### 空间分区

```typescript
// 启用空间分区
waterPhysicsSystem.setConfig({
  enableSpatialPartitioning: true,
  spatialGridSize: 10
});
```

### 自适应更新频率

```typescript
// 启用自适应更新频率
waterPhysicsSystem.setConfig({
  enableAdaptiveUpdate: true,
  minUpdateFrequency: 1,
  maxUpdateFrequency: 10
});
```

## 常见问题解答

### 如何创建瀑布效果？

```typescript
// 使用预设创建瀑布
const waterfallWaterBody = WaterPresets.createPreset(world, {
  type: WaterPresetType.WATERFALL,
  size: { width: 10, height: 20, depth: 5 },
  position: new THREE.Vector3(0, 10, 0),
  rotation: new THREE.Euler(-Math.PI / 4, 0, 0)
});
```

### 如何创建水下环境？

```typescript
// 使用预设创建地下湖泊
const undergroundLakeWaterBody = WaterPresets.createPreset(world, {
  type: WaterPresetType.UNDERGROUND_LAKE,
  size: { width: 80, height: 8, depth: 80 },
  position: new THREE.Vector3(0, -20, 0)
});

// 配置水下雾效
waterLightingSystem.setConfig({
  enableUnderwaterFog: true,
  underwaterFogIntensity: 1.0,
  underwaterFogColor: new THREE.Color(0x0055aa),
  underwaterFogDensity: 0.1
});
```

### 如何创建熔岩效果？

```typescript
// 使用预设创建熔岩
const lavaWaterBody = WaterPresets.createPreset(world, {
  type: WaterPresetType.LAVA,
  size: { width: 50, height: 5, depth: 50 },
  position: new THREE.Vector3(0, 0, 0)
});
```
