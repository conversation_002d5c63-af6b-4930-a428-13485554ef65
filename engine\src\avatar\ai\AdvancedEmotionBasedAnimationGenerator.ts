/**
 * 高级情感动画生成器
 * 用于基于情感分析生成更自然、更丰富的面部动画
 */
import { FacialExpressionType, VisemeType } from '../components/FacialAnimationComponent';
import { AnimationGenerationRequest, AnimationGenerationResult } from './AnimationGenerationTypes';
import { AIModel } from './AIModel';
import { FacialAnimationClip } from '../animation/FacialAnimationClip';

/**
 * 情感变化点
 */
export interface EmotionChangePoint {
  /** 时间点（0-1范围内的相对时间） */
  time: number;
  /** 情感类型 */
  emotion: string;
  /** 情感强度 */
  intensity: number;
}

/**
 * 情感分析结果
 */
export interface EmotionAnalysisResult {
  /** 主要情感 */
  primaryEmotion: string;
  /** 主要情感强度 */
  primaryIntensity: number;
  /** 次要情感 */
  secondaryEmotion?: string;
  /** 次要情感强度 */
  secondaryIntensity?: number;
  /** 情感变化点 */
  emotionChanges?: EmotionChangePoint[];
  /** 情感分数 */
  scores?: Record<string, number>;
  /** 置信度 */
  confidence?: number;
  /** 详细情感数据 */
  detailedEmotions?: Record<string, any>;
}

/**
 * 高级情感动画生成器配置
 */
export interface AdvancedEmotionBasedAnimationGeneratorConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否使用本地模型 */
  useLocalModel?: boolean;
  /** 模型路径 */
  modelPath?: string;
  /** 是否使用GPU */
  useGPU?: boolean;
  /** 关键帧密度（每秒关键帧数） */
  keyframeDensity?: number;
  /** 是否启用表情混合 */
  enableExpressionBlending?: boolean;
  /** 是否启用微表情 */
  enableMicroExpressions?: boolean;
  /** 是否启用情感过渡 */
  enableEmotionTransitions?: boolean;
  /** 是否启用自然变化 */
  enableNaturalVariation?: boolean;
  /** 模型类型 */
  modelType?: 'bert' | 'roberta' | 'distilbert' | 'albert' | 'xlnet' | 'custom';
  /** 模型变体 */
  modelVariant?: string;
  /** 是否使用量化模型 */
  useQuantized?: boolean;
  /** 量化位数 */
  quantizationBits?: 8 | 16 | 32;
  /** 批处理大小 */
  batchSize?: number;
  /** 情感类别 */
  emotionCategories?: string[];
  /** 是否使用缓存 */
  useCache?: boolean;
  /** 缓存大小 */
  cacheSize?: number;
  /** 是否使用上下文 */
  useContext?: boolean;
  /** 上下文窗口大小 */
  contextWindowSize?: number;
  /** 微表情配置 */
  microExpressionConfig?: any;
  /** 混合配置 */
  blendConfig?: any;
  /** 是否使用物理模拟 */
  usePhysics?: boolean;
  /** 物理参数 */
  physicsParams?: Record<string, any>;
}

/**
 * 表情映射
 */
const EMOTION_TO_EXPRESSION_MAP: Record<string, FacialExpressionType> = {
  'happy': FacialExpressionType.HAPPY,
  '开心': FacialExpressionType.HAPPY,
  '高兴': FacialExpressionType.HAPPY,
  'sad': FacialExpressionType.SAD,
  '悲伤': FacialExpressionType.SAD,
  '伤心': FacialExpressionType.SAD,
  'angry': FacialExpressionType.ANGRY,
  '愤怒': FacialExpressionType.ANGRY,
  '生气': FacialExpressionType.ANGRY,
  'surprised': FacialExpressionType.SURPRISED,
  '惊讶': FacialExpressionType.SURPRISED,
  '吃惊': FacialExpressionType.SURPRISED,
  'fear': FacialExpressionType.FEAR,
  '恐惧': FacialExpressionType.FEAR,
  '害怕': FacialExpressionType.FEAR,
  'disgust': FacialExpressionType.DISGUST,
  '厌恶': FacialExpressionType.DISGUST,
  '反感': FacialExpressionType.DISGUST,
  'contempt': FacialExpressionType.CONTEMPT,
  '蔑视': FacialExpressionType.CONTEMPT,
  '鄙视': FacialExpressionType.CONTEMPT,
  'neutral': FacialExpressionType.NEUTRAL,
  '中性': FacialExpressionType.NEUTRAL,
  '平静': FacialExpressionType.NEUTRAL
};

/**
 * 高级情感动画生成器
 */
export class AdvancedEmotionBasedAnimationGenerator {
  /** 配置 */
  private config: AdvancedEmotionBasedAnimationGeneratorConfig;

  /** AI模型 */
  private aiModel: AIModel;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否启用调试 */
  private debug: boolean = false;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: AdvancedEmotionBasedAnimationGeneratorConfig = {}) {
    this.config = {
      debug: false,
      useLocalModel: false,
      modelPath: '',
      useGPU: false,
      keyframeDensity: 4,
      enableExpressionBlending: true,
      enableMicroExpressions: true,
      enableEmotionTransitions: true,
      enableNaturalVariation: true,
      ...config
    };

    this.debug = this.config.debug || false;

    // 创建AI模型
    this.aiModel = new AIModel({
      useLocalModel: this.config.useLocalModel,
      modelPath: this.config.modelPath,
      useGPU: this.config.useGPU,
      debug: this.debug,
      modelType: this.config.modelType || 'bert',
      modelVariant: this.config.modelVariant || 'base',
      useQuantized: this.config.useQuantized || false,
      quantizationBits: this.config.quantizationBits || 8,
      batchSize: this.config.batchSize || 1,
      emotionCategories: this.config.emotionCategories,
      useCache: this.config.useCache || true,
      cacheSize: this.config.cacheSize || 100
    });

    if (this.debug) {
      console.log('高级情感动画生成器创建');
    }
  }

  /**
   * 初始化
   * @returns 是否成功初始化
   */
  public async initialize(): Promise<boolean> {
    if (this.initialized) {
      return true;
    }

    try {
      // 初始化AI模型
      const success = await this.aiModel.initialize();
      this.initialized = success;

      if (this.debug) {
        console.log('高级情感动画生成器初始化', success ? '成功' : '失败');
      }

      return success;
    } catch (error) {
      console.error('初始化高级情感动画生成器失败:', error);
      return false;
    }
  }

  /**
   * 生成面部动画
   * @param request 生成请求
   * @returns 生成结果
   */
  public async generateFacialAnimation(request: AnimationGenerationRequest): Promise<AnimationGenerationResult> {
    try {
      // 确保已初始化
      if (!this.initialized) {
        await this.initialize();
      }

      // 分析文本情感
      const emotionResult = await this.aiModel.analyzeEmotion(request.prompt, {
        detail: 'high',
        includeSecondary: true,
        includeChanges: true
      });

      if (this.debug) {
        console.log('情感分析结果:', emotionResult);
      }

      // 创建面部动画片段
      const clip = await this.createAdvancedEmotionBasedFacialClip(request, emotionResult);

      return {
        id: request.id,
        success: true,
        clip,
        generationTime: 0,
        userData: request.userData
      };
    } catch (error) {
      if (this.debug) {
        console.error('生成面部动画失败:', error);
      }

      return {
        id: request.id,
        success: false,
        error: error instanceof Error ? error.message : String(error),
        userData: request.userData
      };
    }
  }

  /**
   * 创建高级情感面部动画片段
   * @param request 请求
   * @param emotionResult 情感分析结果
   * @returns 面部动画片段
   */
  private async createAdvancedEmotionBasedFacialClip(
    request: AnimationGenerationRequest,
    emotionResult: EmotionAnalysisResult
  ): Promise<FacialAnimationClip> {
    // 创建动画片段
    const clip = new FacialAnimationClip(request.prompt);

    // 设置持续时间
    clip.duration = request.duration;

    // 设置循环
    clip.loop = request.loop || false;

    // 添加表情关键帧
    this.addExpressionKeyframes(clip, request, emotionResult);

    // 添加口型关键帧
    this.addVisemeKeyframes(clip, request);

    // 添加自然变化
    if (this.config.enableNaturalVariation) {
      this.addNaturalVariation(clip, request);
    }

    // 添加微表情
    if (this.config.enableMicroExpressions) {
      this.addMicroExpressions(clip, request, emotionResult);
    }

    return clip;
  }

  /**
   * 添加表情关键帧
   * @param clip 动画片段
   * @param request 请求
   * @param emotionResult 情感分析结果
   */
  private addExpressionKeyframes(
    clip: FacialAnimationClip,
    request: AnimationGenerationRequest,
    emotionResult: EmotionAnalysisResult
  ): void {
    // 获取主要表情
    const primaryExpression = this.getExpressionForEmotion(emotionResult.primaryEmotion);

    // 获取次要表情
    const secondaryExpression = emotionResult.secondaryEmotion
      ? this.getExpressionForEmotion(emotionResult.secondaryEmotion)
      : FacialExpressionType.NEUTRAL;

    // 计算强度
    const primaryIntensity = emotionResult.primaryIntensity * (request.intensity || 1.0);
    const secondaryIntensity = (emotionResult.secondaryIntensity || 0) * (request.intensity || 1.0);

    // 如果有情感变化，创建关键帧
    if (emotionResult.emotionChanges && emotionResult.emotionChanges.length > 0 && this.config.enableEmotionTransitions) {
      // 添加初始关键帧（中性表情）
      clip.addExpressionKeyframe(0, FacialExpressionType.NEUTRAL, 1.0);

      // 添加情感变化关键帧
      for (const change of emotionResult.emotionChanges) {
        const expression = this.getExpressionForEmotion(change.emotion);
        const time = change.time * request.duration;
        const intensity = change.intensity * (request.intensity || 1.0);

        clip.addExpressionKeyframe(time, expression, intensity);
      }

      // 添加结束关键帧
      if (request.loop) {
        clip.addExpressionKeyframe(request.duration, FacialExpressionType.NEUTRAL, 1.0);
      } else {
        // 使用最后一个情感变化的表情
        const lastChange = emotionResult.emotionChanges[emotionResult.emotionChanges.length - 1];
        const lastExpression = this.getExpressionForEmotion(lastChange.emotion);
        const lastIntensity = lastChange.intensity * (request.intensity || 1.0) * 0.8; // 稍微减弱

        clip.addExpressionKeyframe(request.duration, lastExpression, lastIntensity);
      }
    } else {
      // 没有情感变化，创建简单的表情过渡

      // 添加初始关键帧（中性表情）
      clip.addExpressionKeyframe(0, FacialExpressionType.NEUTRAL, 1.0);

      // 添加主要表情关键帧
      const riseTime = request.duration * 0.2; // 上升时间
      clip.addExpressionKeyframe(riseTime, primaryExpression, primaryIntensity);

      // 如果有次要表情，添加次要表情关键帧
      if (emotionResult.secondaryEmotion && secondaryIntensity > 0.2) {
        const secondaryTime = request.duration * 0.6; // 次要表情时间
        clip.addExpressionKeyframe(secondaryTime, secondaryExpression, secondaryIntensity);
      }

      // 添加结束关键帧
      if (request.loop) {
        clip.addExpressionKeyframe(request.duration, FacialExpressionType.NEUTRAL, 1.0);
      } else {
        // 保持最后的表情
        clip.addExpressionKeyframe(request.duration, primaryExpression, primaryIntensity * 0.8); // 稍微减弱
      }
    }
  }

  /**
   * 添加口型关键帧
   * @param clip 动画片段
   * @param request 请求
   */
  private addVisemeKeyframes(
    clip: FacialAnimationClip,
    request: AnimationGenerationRequest
  ): void {
    if (request.prompt.length === 0) return;

    // 简单的口型同步
    const words = request.prompt.split(/\s+/);
    const wordDuration = request.duration / Math.max(words.length, 1);

    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      const startTime = i * wordDuration;

      // 为每个单词生成口型序列
      this.generateVisemeSequenceForWord(clip, word, startTime, wordDuration);
    }
  }

  /**
   * 为单词生成口型序列
   * @param clip 动画片段
   * @param word 单词
   * @param startTime 开始时间
   * @param duration 持续时间
   */
  private generateVisemeSequenceForWord(
    clip: FacialAnimationClip,
    word: string,
    startTime: number,
    duration: number
  ): void {
    if (word.length === 0) return;

    // 简化的口型映射
    const charToViseme: Record<string, VisemeType> = {
      'a': VisemeType.AA,
      'e': VisemeType.E,
      'i': VisemeType.IH,
      'o': VisemeType.OH,
      'u': VisemeType.OU,
      'p': VisemeType.PP,
      'b': VisemeType.PP,
      'm': VisemeType.PP,
      'f': VisemeType.FF,
      'v': VisemeType.FF,
      's': VisemeType.SS,
      'z': VisemeType.SS,
      't': VisemeType.DD,
      'd': VisemeType.DD,
      'n': VisemeType.NN,
      'r': VisemeType.R,
      'k': VisemeType.K,
      'g': VisemeType.K,
      'ch': VisemeType.CH,
      'j': VisemeType.CH,
      'th': VisemeType.TH
    };

    // 计算每个字符的持续时间
    const charDuration = duration / Math.max(word.length, 1);

    // 添加口型关键帧
    for (let i = 0; i < word.length; i++) {
      const char = word[i].toLowerCase();
      const time = startTime + i * charDuration;

      // 获取口型
      let viseme = charToViseme[char] || VisemeType.NEUTRAL;

      // 检查双字符口型
      if (i < word.length - 1) {
        const twoChars = char + word[i + 1].toLowerCase();
        if (charToViseme[twoChars]) {
          viseme = charToViseme[twoChars];
          i++; // 跳过下一个字符
        }
      }

      // 添加口型关键帧
      clip.addVisemeKeyframe(time, viseme, 1.0);

      // 添加回到中性的过渡
      clip.addVisemeKeyframe(time + charDuration * 0.7, VisemeType.NEUTRAL, 0.3);
    }

    // 确保最后回到中性口型
    clip.addVisemeKeyframe(startTime + duration, VisemeType.NEUTRAL, 0.0);
  }

  /**
   * 添加自然变化
   * @param clip 动画片段
   * @param request 请求
   */
  private addNaturalVariation(
    clip: FacialAnimationClip,
    request: AnimationGenerationRequest
  ): void {
    // 添加自然的眨眼
    this.addBlinking(clip, request.duration);

    // 添加轻微的头部运动
    this.addHeadMovement(clip, request.duration);
  }

  /**
   * 添加眨眼
   * @param clip 动画片段
   * @param duration 持续时间
   */
  private addBlinking(clip: FacialAnimationClip, duration: number): void {
    // 平均每3-5秒眨眼一次
    const blinkInterval = 3 + Math.random() * 2;
    let time = 1 + Math.random() * 2; // 第一次眨眼在1-3秒后

    while (time < duration) {
      // 眨眼持续时间约0.15秒
      const blinkDuration = 0.15;

      // 添加眨眼关键帧
      clip.addBlendShapeKeyframe(time, 'eyeBlink', 0.0); // 开始眨眼
      clip.addBlendShapeKeyframe(time + blinkDuration * 0.5, 'eyeBlink', 1.0); // 眼睛闭合
      clip.addBlendShapeKeyframe(time + blinkDuration, 'eyeBlink', 0.0); // 眼睛睁开

      // 下一次眨眼时间
      time += blinkInterval + Math.random() * 2; // 添加一些随机性
    }
  }

  /**
   * 添加头部运动
   * @param clip 动画片段
   * @param duration 持续时间
   */
  private addHeadMovement(clip: FacialAnimationClip, duration: number): void {
    // 添加轻微的头部运动
    const movementCount = Math.floor(duration / 2) + 1; // 每2秒左右一次头部运动

    for (let i = 0; i < movementCount; i++) {
      const time = i * 2 + Math.random(); // 添加一些随机性
      if (time >= duration) break;

      // 随机选择头部运动方向
      const direction = Math.floor(Math.random() * 3); // 0: 点头, 1: 左右摇头, 2: 倾斜
      const intensity = 0.1 + Math.random() * 0.2; // 轻微的运动

      switch (direction) {
        case 0: // 点头
          clip.addBlendShapeKeyframe(time, 'headNod', 0.0);
          clip.addBlendShapeKeyframe(time + 0.5, 'headNod', intensity);
          clip.addBlendShapeKeyframe(time + 1.0, 'headNod', 0.0);
          break;
        case 1: // 左右摇头
          clip.addBlendShapeKeyframe(time, 'headShake', 0.0);
          clip.addBlendShapeKeyframe(time + 0.5, 'headShake', intensity);
          clip.addBlendShapeKeyframe(time + 1.0, 'headShake', 0.0);
          break;
        case 2: // 倾斜
          clip.addBlendShapeKeyframe(time, 'headTilt', 0.0);
          clip.addBlendShapeKeyframe(time + 0.5, 'headTilt', intensity);
          clip.addBlendShapeKeyframe(time + 1.0, 'headTilt', 0.0);
          break;
      }
    }
  }

  /**
   * 添加微表情
   * @param clip 动画片段
   * @param request 请求
   * @param emotionResult 情感分析结果
   */
  private addMicroExpressions(
    clip: FacialAnimationClip,
    request: AnimationGenerationRequest,
    emotionResult: EmotionAnalysisResult
  ): void {
    // 微表情通常持续0.2-0.5秒
    const microExpressionDuration = 0.2 + Math.random() * 0.3;

    // 根据主要情感添加相关的微表情
    const primaryEmotion = emotionResult.primaryEmotion.toLowerCase();

    // 每2-4秒添加一次微表情
    const interval = 2 + Math.random() * 2;
    let time = interval;

    while (time < request.duration) {
      // 选择微表情类型
      let microExpression = this.getMicroExpressionForEmotion(primaryEmotion);
      const intensity = 0.3 + Math.random() * 0.3; // 微表情强度

      // 添加微表情关键帧
      clip.addBlendShapeKeyframe(time, microExpression, 0.0);
      clip.addBlendShapeKeyframe(time + microExpressionDuration * 0.5, microExpression, intensity);
      clip.addBlendShapeKeyframe(time + microExpressionDuration, microExpression, 0.0);

      // 下一次微表情时间
      time += interval + Math.random() * 2;
    }
  }

  /**
   * 获取情感对应的微表情
   * @param emotion 情感
   * @returns 微表情混合形状名称
   */
  private getMicroExpressionForEmotion(emotion: string): string {
    // 根据情感类型返回相应的微表情混合形状
    const emotionToMicroExpression: Record<string, string[]> = {
      'happy': ['browRaiseInner', 'cheekRaiseRight', 'cheekRaiseLeft', 'mouthSmileRight', 'mouthSmileLeft'],
      '开心': ['browRaiseInner', 'cheekRaiseRight', 'cheekRaiseLeft', 'mouthSmileRight', 'mouthSmileLeft'],
      '高兴': ['browRaiseInner', 'cheekRaiseRight', 'cheekRaiseLeft', 'mouthSmileRight', 'mouthSmileLeft'],
      'sad': ['browLowerInner', 'mouthFrownRight', 'mouthFrownLeft', 'mouthShrugUpper'],
      '悲伤': ['browLowerInner', 'mouthFrownRight', 'mouthFrownLeft', 'mouthShrugUpper'],
      '伤心': ['browLowerInner', 'mouthFrownRight', 'mouthFrownLeft', 'mouthShrugUpper'],
      'angry': ['browLowerInner', 'browLowerRight', 'browLowerLeft', 'noseSneer', 'mouthPress'],
      '愤怒': ['browLowerInner', 'browLowerRight', 'browLowerLeft', 'noseSneer', 'mouthPress'],
      '生气': ['browLowerInner', 'browLowerRight', 'browLowerLeft', 'noseSneer', 'mouthPress'],
      'surprised': ['browRaiseInner', 'browRaiseOuter', 'eyeWiden', 'mouthOpen'],
      '惊讶': ['browRaiseInner', 'browRaiseOuter', 'eyeWiden', 'mouthOpen'],
      '吃惊': ['browRaiseInner', 'browRaiseOuter', 'eyeWiden', 'mouthOpen'],
      'fear': ['browRaiseInner', 'eyeWiden', 'mouthStretch', 'mouthOpen'],
      '恐惧': ['browRaiseInner', 'eyeWiden', 'mouthStretch', 'mouthOpen'],
      '害怕': ['browRaiseInner', 'eyeWiden', 'mouthStretch', 'mouthOpen'],
      'disgust': ['noseSneer', 'mouthRaiseUpper', 'mouthPress'],
      '厌恶': ['noseSneer', 'mouthRaiseUpper', 'mouthPress'],
      '反感': ['noseSneer', 'mouthRaiseUpper', 'mouthPress'],
      'contempt': ['mouthRaiseUpper', 'mouthDimpleRight', 'mouthDimpleLeft'],
      '蔑视': ['mouthRaiseUpper', 'mouthDimpleRight', 'mouthDimpleLeft'],
      '鄙视': ['mouthRaiseUpper', 'mouthDimpleRight', 'mouthDimpleLeft'],
      'neutral': ['browInnerUp', 'mouthRollLower', 'mouthRollUpper', 'mouthShrugLower'],
      '中性': ['browInnerUp', 'mouthRollLower', 'mouthRollUpper', 'mouthShrugLower'],
      '平静': ['browInnerUp', 'mouthRollLower', 'mouthRollUpper', 'mouthShrugLower']
    };

    // 获取情感对应的微表情列表
    const microExpressions = emotionToMicroExpression[emotion] || emotionToMicroExpression['neutral'];

    // 随机选择一个微表情
    return microExpressions[Math.floor(Math.random() * microExpressions.length)];
  }

  /**
   * 获取情感对应的表情
   * @param emotion 情感
   * @returns 表情
   */
  private getExpressionForEmotion(emotion: string): FacialExpressionType {
    const lowerEmotion = emotion.toLowerCase();
    return EMOTION_TO_EXPRESSION_MAP[lowerEmotion] || FacialExpressionType.NEUTRAL;
  }
}
