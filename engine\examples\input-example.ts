/**
 * 输入系统示例
 * 演示如何使用输入系统
 */
import {
  Engine,
  World,
  Scene,
  Camera,
  Renderer,
  InputSystem,
  InputManager,
  InputDevice,
  InputAction,
  InputBinding,
  InputMapping,
  InputComponent,
  ButtonInputAction,
  ValueInputAction,
  VectorInputAction,
  ButtonInputMapping,
  AxisInputMapping,
  VectorInputMapping,
  KeyboardDevice,
  MouseDevice,
  GamepadDevice,
  TouchDevice,
  InputMappingType,
  InputActionType
} from '../src';

// 创建引擎
const engine = new Engine();

// 创建世界
const world = new World();
engine.setWorld(world);

// 创建输入系统
const inputSystem = new InputSystem();
world.addSystem(inputSystem);

// 创建输入管理器
const inputManager = InputManager.getInstance();
inputManager.initialize();

// 创建渲染系统
const renderer = new Renderer({
  width: window.innerWidth,
  height: window.innerHeight,
  antialias: true
});
document.body.appendChild(renderer.getDomElement());

// 创建场景
const scene = new Scene();
world.addEntity(scene);

// 创建相机
const camera = new Camera({
  type: 'perspective',
  fov: 75,
  aspect: window.innerWidth / window.innerHeight,
  near: 0.1,
  far: 1000
});
camera.position.set(0, 5, 10);
camera.lookAt(0, 0, 0);
world.addEntity(camera);

// 创建玩家实体
const player = world.createEntity();
player.name = '玩家';

// 创建输入动作
const moveAction = new VectorInputAction('move');
const jumpAction = new ButtonInputAction('jump');
const fireAction = new ButtonInputAction('fire');
const aimAction = new ValueInputAction('aim');

// 创建输入映射
const keyboardMoveMapping = new VectorInputMapping(
  'keyboardMove',
  'keyboard',
  'KeyD', // 右
  'KeyW', // 上
  1,
  0.1
);

const gamepadMoveMapping = new VectorInputMapping(
  'gamepadMove',
  'gamepad',
  '0:axis:0', // 左摇杆X
  '0:axis:1', // 左摇杆Y
  1,
  0.1
);

const keyboardJumpMapping = new ButtonInputMapping(
  'keyboardJump',
  'keyboard',
  'Space'
);

const gamepadJumpMapping = new ButtonInputMapping(
  'gamepadJump',
  'gamepad',
  '0:button:0' // A按钮
);

const mouseFireMapping = new ButtonInputMapping(
  'mouseFire',
  'mouse',
  'button:0' // 左键
);

const gamepadFireMapping = new ButtonInputMapping(
  'gamepadFire',
  'gamepad',
  '0:button:7' // 右扳机
);

const mouseAimMapping = new AxisInputMapping(
  'mouseAim',
  'mouse',
  'wheel:delta',
  0.01
);

const gamepadAimMapping = new AxisInputMapping(
  'gamepadAim',
  'gamepad',
  '0:axis:2', // 右摇杆Y
  1,
  0,
  0.1
);

// 创建输入绑定
const moveBinding = new InputBinding('move', 'keyboardMove');
const jumpBinding = new InputBinding('jump', 'keyboardJump');
const fireBinding = new InputBinding('fire', 'mouseFire');
const aimBinding = new InputBinding('aim', 'mouseAim');

// 创建输入组件
const inputComponent = new InputComponent(player, {
  actions: [moveAction, jumpAction, fireAction, aimAction],
  bindings: [moveBinding, jumpBinding, fireBinding, aimBinding],
  mappings: [
    keyboardMoveMapping,
    gamepadMoveMapping,
    keyboardJumpMapping,
    gamepadJumpMapping,
    mouseFireMapping,
    gamepadFireMapping,
    mouseAimMapping,
    gamepadAimMapping
  ]
});

// 添加输入组件到玩家实体
player.addComponent(inputComponent);

// 创建UI元素
const createUI = () => {
  const container = document.createElement('div');
  container.style.position = 'absolute';
  container.style.top = '10px';
  container.style.left = '10px';
  container.style.color = 'white';
  container.style.fontFamily = 'Arial, sans-serif';
  container.style.fontSize = '14px';
  container.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
  container.style.padding = '10px';
  container.style.borderRadius = '5px';
  container.style.width = '300px';

  const title = document.createElement('h2');
  title.textContent = '输入系统示例';
  title.style.margin = '0 0 10px 0';
  container.appendChild(title);

  const moveInfo = document.createElement('div');
  moveInfo.id = 'moveInfo';
  moveInfo.textContent = '移动: [0, 0]';
  container.appendChild(moveInfo);

  const jumpInfo = document.createElement('div');
  jumpInfo.id = 'jumpInfo';
  jumpInfo.textContent = '跳跃: 未按下';
  container.appendChild(jumpInfo);

  const fireInfo = document.createElement('div');
  fireInfo.id = 'fireInfo';
  fireInfo.textContent = '开火: 未按下';
  container.appendChild(fireInfo);

  const aimInfo = document.createElement('div');
  aimInfo.id = 'aimInfo';
  aimInfo.textContent = '瞄准: 0';
  container.appendChild(aimInfo);

  document.body.appendChild(container);
};

// 更新UI
const updateUI = () => {
  const moveInfo = document.getElementById('moveInfo');
  const jumpInfo = document.getElementById('jumpInfo');
  const fireInfo = document.getElementById('fireInfo');
  const aimInfo = document.getElementById('aimInfo');

  if (moveInfo) {
    const move = moveAction.getValue();
    moveInfo.textContent = `移动: [${move[0].toFixed(2)}, ${move[1].toFixed(2)}]`;
  }

  if (jumpInfo) {
    jumpInfo.textContent = `跳跃: ${jumpAction.isPressed() ? '按下' : '未按下'}`;
  }

  if (fireInfo) {
    fireInfo.textContent = `开火: ${fireAction.isPressed() ? '按下' : '未按下'}`;
  }

  if (aimInfo) {
    aimInfo.textContent = `瞄准: ${aimAction.getValue().toFixed(2)}`;
  }
};

// 创建UI
createUI();

// 游戏循环
const gameLoop = () => {
  // 更新输入管理器
  inputManager.update(engine.deltaTime);

  // 更新输入组件
  inputComponent.update(engine.deltaTime);

  // 更新UI
  updateUI();

  // 处理输入
  const move = moveAction.getValue();
  const jump = jumpAction.isPressed();
  const fire = fireAction.isPressed();
  const aim = aimAction.getValue();

  // 在这里处理玩家移动、跳跃、开火等逻辑
  // ...

  // 更新世界
  world.update(engine.deltaTime);

  // 渲染场景
  renderer.render(scene, camera);

  // 请求下一帧
  requestAnimationFrame(gameLoop);
};

// 开始游戏循环
gameLoop();

// 窗口大小调整
window.addEventListener('resize', () => {
  renderer.setSize(window.innerWidth, window.innerHeight);
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
});
