/**
 * 气球物理示例
 * 展示如何使用软体物理系统创建气球效果
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Vector3 } from '../../src/math/Vector3';
import { Quaternion } from '../../src/math/Quaternion';
import { 
  PhysicsSystem, 
  PhysicsBodyComponent, 
  PhysicsColliderComponent,
  BodyType,
  ColliderType,
  PhysicsMaterialFactory
} from '../../src/physics';
import { SoftBodySystem } from '../../src/physics/softbody/SoftBodySystem';
import { SoftBodyComponent, SoftBodyType } from '../../src/physics/softbody/SoftBodyComponent';
import { InputSystem } from '../../src/input/InputSystem';
import { KeyCode } from '../../src/input/KeyCode';

/**
 * 气球物理示例
 */
export class BalloonExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  
  /** 软体物理系统 */
  private softBodySystem: SoftBodySystem;
  
  /** 输入系统 */
  private inputSystem: InputSystem;
  
  /** 地面实体 */
  private ground: Entity;
  
  /** 气球实体 */
  private balloon: Entity;
  
  /** 针实体 */
  private needle: Entity;
  
  /** 是否已初始化 */
  private initialized: boolean = false;
  
  /** 气球压力 */
  private balloonPressure: number = 10;
  
  /** 是否已刺破气球 */
  private balloonPopped: boolean = false;

  /**
   * 创建气球物理示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建场景
    this.scene = new Scene('气球物理示例场景');
    
    // 创建物理系统
    this.physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.82, z: 0 },
      debug: true
    });
    
    // 创建软体物理系统
    this.softBodySystem = new SoftBodySystem({
      physicsSystem: this.physicsSystem,
      debug: true,
      useSoftBodyCutter: true
    });
    
    // 创建输入系统
    this.inputSystem = new InputSystem();
    
    // 添加系统到引擎
    this.engine.addSystem(this.physicsSystem);
    this.engine.addSystem(this.softBodySystem);
    this.engine.addSystem(this.inputSystem);
    
    // 初始化物理材质工厂
    PhysicsMaterialFactory.initialize();
    
    // 创建地面
    this.ground = this.createGround();
    
    // 添加地面到场景
    this.scene.addEntity(this.ground);
    
    // 注册更新回调
    this.engine.onUpdate.add(this.update.bind(this));
  }

  /**
   * 初始化示例
   */
  public initialize(): void {
    if (this.initialized) return;
    
    // 创建气球
    this.balloon = this.createBalloon();
    this.scene.addEntity(this.balloon);
    
    // 创建针
    this.needle = this.createNeedle();
    this.scene.addEntity(this.needle);
    
    // 设置场景为活跃场景
    this.engine.getWorld().setActiveScene(this.scene);
    
    this.initialized = true;
  }

  /**
   * 更新
   * @param deltaTime 帧间隔时间（秒）
   */
  private update(deltaTime: number): void {
    // 处理输入
    this.handleInput();
    
    // 检查针是否刺破气球
    this.checkNeedleCollision();
  }

  /**
   * 处理输入
   */
  private handleInput(): void {
    // 移动针
    if (this.inputSystem.isKeyPressed(KeyCode.W)) {
      this.moveNeedle(0, 0, -0.1);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.S)) {
      this.moveNeedle(0, 0, 0.1);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.A)) {
      this.moveNeedle(-0.1, 0, 0);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.D)) {
      this.moveNeedle(0.1, 0, 0);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.Q)) {
      this.moveNeedle(0, 0.1, 0);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.E)) {
      this.moveNeedle(0, -0.1, 0);
    }
    
    // 控制气球压力
    if (this.inputSystem.isKeyPressed(KeyCode.UP)) {
      this.increaseBalloonPressure();
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.DOWN)) {
      this.decreaseBalloonPressure();
    }
    
    // 切割气球
    if (this.inputSystem.isKeyPressed(KeyCode.SPACE)) {
      this.popBalloon();
    }
  }

  /**
   * 移动针
   * @param x X轴移动量
   * @param y Y轴移动量
   * @param z Z轴移动量
   */
  private moveNeedle(x: number, y: number, z: number): void {
    const transform = this.needle.getTransform();
    const position = transform.getPosition();
    position.x += x;
    position.y += y;
    position.z += z;
    transform.setPosition(position.x, position.y, position.z);
    
    // 更新物理体位置
    const physicsBody = this.needle.getComponent(PhysicsBodyComponent);
    if (physicsBody) {
      physicsBody.setPosition(position);
    }
  }

  /**
   * 增加气球压力
   */
  private increaseBalloonPressure(): void {
    if (this.balloonPopped) return;
    
    this.balloonPressure += 1;
    console.log(`气球压力: ${this.balloonPressure}`);
    
    // 更新气球参数
    const softBody = this.balloon.getComponent(SoftBodyComponent);
    if (softBody) {
      softBody.setParameter('pressure', this.balloonPressure);
    }
  }

  /**
   * 减少气球压力
   */
  private decreaseBalloonPressure(): void {
    if (this.balloonPopped) return;
    
    this.balloonPressure = Math.max(0, this.balloonPressure - 1);
    console.log(`气球压力: ${this.balloonPressure}`);
    
    // 更新气球参数
    const softBody = this.balloon.getComponent(SoftBodyComponent);
    if (softBody) {
      softBody.setParameter('pressure', this.balloonPressure);
    }
  }

  /**
   * 检查针是否刺破气球
   */
  private checkNeedleCollision(): void {
    if (this.balloonPopped) return;
    
    const needleTransform = this.needle.getTransform();
    const needlePosition = needleTransform.getPosition();
    
    const balloonTransform = this.balloon.getTransform();
    const balloonPosition = balloonTransform.getPosition();
    
    // 计算针尖位置（针的前方）
    const needleTip = new THREE.Vector3();
    needleTip.copy(needlePosition);
    needleTip.y -= 0.5; // 针尖在针的下方
    
    // 计算针尖到气球中心的距离
    const distance = needleTip.distanceTo(balloonPosition);
    
    // 如果距离小于气球半径，刺破气球
    if (distance < 1.0) {
      this.popBalloon();
    }
  }

  /**
   * 刺破气球
   */
  private popBalloon(): void {
    if (this.balloonPopped) return;
    
    console.log('气球被刺破了！');
    
    // 创建切割平面
    const needleTransform = this.needle.getTransform();
    const needlePosition = needleTransform.getPosition();
    const needleDirection = new THREE.Vector3(0, -1, 0); // 针朝下
    
    // 使用针的位置和方向创建切割平面
    const plane = {
      normal: needleDirection,
      point: needlePosition
    };
    
    // 切割气球
    this.softBodySystem.cutSoftBodyWithPlane(this.balloon.id, plane);
    
    // 设置气球压力为0
    const softBody = this.balloon.getComponent(SoftBodyComponent);
    if (softBody) {
      softBody.setParameter('pressure', 0);
    }
    
    this.balloonPopped = true;
  }

  /**
   * 创建地面
   * @returns 地面实体
   */
  private createGround(): Entity {
    // 创建地面实体
    const ground = new Entity('ground');
    
    // 添加变换组件
    const transform = ground.getTransform();
    transform.setPosition(0, -2, 0);
    
    // 创建地面网格
    const geometry = new THREE.BoxGeometry(20, 1, 20);
    const material = new THREE.MeshStandardMaterial({ color: 0x999999 });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    ground.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    ground.addComponent(new PhysicsBodyComponent({
      type: BodyType.STATIC,
      mass: 0
    }));
    
    // 添加碰撞器组件
    ground.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: 10, y: 0.5, z: 10 }
      }
    }));
    
    return ground;
  }

  /**
   * 创建气球
   * @returns 气球实体
   */
  private createBalloon(): Entity {
    // 创建气球实体
    const balloon = new Entity('balloon');
    
    // 添加变换组件
    const transform = balloon.getTransform();
    transform.setPosition(0, 5, 0);
    
    // 添加软体组件
    const softBodyComponent = new SoftBodyComponent({
      type: SoftBodyType.BALLOON,
      mass: 0.5,
      stiffness: 50,
      damping: 0.1,
      params: {
        radius: 1,
        segments: 16,
        pressure: this.balloonPressure
      }
    });
    
    balloon.addComponent(softBodyComponent);
    
    // 将软体组件添加到软体系统
    this.softBodySystem.addSoftBody(softBodyComponent);
    
    return balloon;
  }

  /**
   * 创建针
   * @returns 针实体
   */
  private createNeedle(): Entity {
    // 创建针实体
    const needle = new Entity('needle');
    
    // 添加变换组件
    const transform = needle.getTransform();
    transform.setPosition(0, 8, 0);
    
    // 创建针网格
    const geometry = new THREE.CylinderGeometry(0.05, 0.01, 1, 8);
    const material = new THREE.MeshStandardMaterial({ color: 0xcccccc });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    needle.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    needle.addComponent(new PhysicsBodyComponent({
      type: BodyType.KINEMATIC,
      mass: 1
    }));
    
    // 添加碰撞器组件
    needle.addComponent(new PhysicsColliderComponent({
      type: ColliderType.CYLINDER,
      params: {
        radiusTop: 0.05,
        radiusBottom: 0.01,
        height: 1
      }
    }));
    
    return needle;
  }
}
