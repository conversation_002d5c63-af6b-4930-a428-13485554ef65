.network-status-visualizer {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  padding: 16px;
}

.network-status-visualizer .visualizer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.network-status-visualizer .visualizer-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.network-status-visualizer .visualizer-content {
  min-height: 400px;
}

.network-status-visualizer .realtime-status {
  margin-top: 16px;
}

.network-status-visualizer .no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #999;
}

.network-status-visualizer .no-data-message p {
  margin-top: 16px;
  font-size: 16px;
}

.network-status-visualizer .ant-card {
  height: 100%;
}

.network-status-visualizer h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
}

.network-status-visualizer .ant-statistic {
  text-align: center;
}

.network-status-visualizer .ant-statistic-content {
  font-size: 24px;
  font-weight: 500;
}

.network-status-visualizer .network-type-icon {
  margin: 16px 0;
}

.network-status-visualizer .network-type-text {
  font-size: 16px;
  margin-top: 8px;
}

.network-status-visualizer .connection-type {
  font-size: 14px;
  color: #999;
  margin-top: 4px;
}

.network-status-visualizer .ant-progress-text {
  font-size: 12px;
}

.network-status-visualizer .chart-container {
  height: 300px;
  margin-top: 16px;
}

.network-status-visualizer .heatmap-container {
  height: 400px;
  margin-top: 16px;
}

.network-status-visualizer .correlation-container {
  height: 400px;
  margin-top: 16px;
}

.network-status-visualizer .chart-legend {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.network-status-visualizer .chart-legend-item {
  display: flex;
  align-items: center;
  margin: 0 8px;
}

.network-status-visualizer .chart-legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 4px;
}

.network-status-visualizer .chart-tooltip {
  background-color: rgba(0, 0, 0, 0.75);
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
}

.network-status-visualizer .chart-tooltip-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.network-status-visualizer .chart-tooltip-item {
  display: flex;
  justify-content: space-between;
  margin: 2px 0;
}

.network-status-visualizer .chart-tooltip-value {
  margin-left: 16px;
  font-weight: 500;
}
