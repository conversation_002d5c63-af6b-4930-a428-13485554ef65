.help-page {
  height: 100vh;
  display: flex;
  flex-direction: column;

  .help-header {
    display: flex;
    align-items: center;
    padding: 0 24px;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 10;

    .logo {
      font-size: 20px;
      font-weight: bold;
      margin-right: 24px;
      white-space: nowrap;
    }

    .search-container {
      flex: 1;
      max-width: 500px;
      margin: 0 auto;
    }
  }

  .help-sider {
    background: #fff;
    border-right: 1px solid #f0f0f0;
    overflow-y: auto;
  }

  .help-content-layout {
    padding: 0;
    background: #fff;
    display: flex;
    flex-direction: column;

    .help-tabs {
      flex: 1;
      display: flex;
      flex-direction: column;

      .ant-tabs-content {
        flex: 1;
        overflow: hidden;
      }
    }
  }

  .help-main-content {
    padding: 24px;
    overflow-y: auto;
    height: 100%;
  }

  .help-content {
    max-width: 800px;
    margin: 0 auto;

    .help-breadcrumb {
      margin-bottom: 16px;
    }

    .help-tags {
      margin: 16px 0;
    }

    .markdown-content {
      margin: 24px 0;
      
      h1, h2, h3, h4, h5, h6 {
        margin-top: 24px;
        margin-bottom: 16px;
      }

      p {
        margin-bottom: 16px;
        line-height: 1.6;
      }

      ul, ol {
        margin-bottom: 16px;
        padding-left: 24px;
      }

      code {
        background-color: #f5f5f5;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
      }

      pre {
        background-color: #f5f5f5;
        padding: 16px;
        border-radius: 3px;
        overflow: auto;
        margin-bottom: 16px;

        code {
          background-color: transparent;
          padding: 0;
        }
      }

      blockquote {
        border-left: 4px solid #ddd;
        padding-left: 16px;
        margin-left: 0;
        margin-bottom: 16px;
        color: #666;
      }

      img {
        max-width: 100%;
        margin: 16px 0;
      }

      table {
        border-collapse: collapse;
        width: 100%;
        margin-bottom: 16px;

        th, td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
        }

        th {
          background-color: #f5f5f5;
        }

        tr:nth-child(even) {
          background-color: #f9f9f9;
        }
      }
    }

    .video-section {
      margin: 24px 0;
    }

    .related-topics {
      margin: 24px 0;
    }

    .help-navigation {
      display: flex;
      justify-content: space-between;
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;
    }
  }

  .help-list-item {
    cursor: pointer;
    padding: 12px;
    border-radius: 4px;
    transition: background-color 0.3s;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}

// 暗色主题适配
.dark-theme {
  .help-page {
    background-color: #141414;
    color: rgba(255, 255, 255, 0.85);

    .help-header {
      background-color: #1f1f1f;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.45);

      .logo {
        color: #fff;
      }
    }

    .help-sider {
      background-color: #1f1f1f;
      border-right-color: #303030;
    }

    .help-content-layout {
      background-color: #141414;
    }

    .help-content {
      .markdown-content {
        code {
          background-color: #2a2a2a;
        }

        pre {
          background-color: #2a2a2a;
        }

        blockquote {
          border-left-color: #444;
          color: #aaa;
        }

        table {
          th, td {
            border-color: #444;
          }

          th {
            background-color: #2a2a2a;
          }

          tr:nth-child(even) {
            background-color: #252525;
          }
        }
      }

      .help-navigation {
        border-top-color: #303030;
      }
    }

    .help-list-item {
      &:hover {
        background-color: #2a2a2a;
      }
    }
  }
}
