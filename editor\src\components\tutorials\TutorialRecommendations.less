/**
 * 教程推荐组件样式
 */
.tutorial-recommendations {
  padding: 0 10px;

  .tutorial-item-card {
    height: 100%;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .tutorial-item-cover {
      position: relative;
      height: 160px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s;
      }

      &:hover img {
        transform: scale(1.05);
      }

      .tutorial-completed-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #52c41a;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 16px;
      }
    }

    .tutorial-item-title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .popular-icon {
        color: #ff4d4f;
        font-size: 16px;
        margin-left: 8px;
      }
    }

    .tutorial-item-description {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .tutorial-item-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-top: 5px;
    }

    .tutorial-progress {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-top: 5px;

      .progress-bar {
        flex: 1;
        height: 6px;
        background-color: #f0f0f0;
        border-radius: 3px;
        overflow: hidden;

        .progress-fill {
          height: 100%;
          background-color: #1890ff;
          border-radius: 3px;
        }
      }
    }

    .tutorial-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-top: 8px;
      align-items: center;

      .tutorial-tag {
        margin-right: 0;
      }
    }
  }

  .recommended-series {
    margin-bottom: 20px;

    .series-carousel-item {
      padding: 10px;
    }

    .carousel-dots {
      bottom: -25px;
    }
  }

  .tutorial-series-card {
    height: 100%;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .tutorial-series-cover {
      position: relative;
      height: 180px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s;
      }

      &:hover img {
        transform: scale(1.05);
      }

      .series-featured-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #faad14;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 16px;
      }
    }

    .tutorial-series-title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .popular-icon {
        color: #ff4d4f;
        font-size: 16px;
        margin-left: 8px;
      }
    }

    .tutorial-series-description {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .tutorial-series-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-top: 5px;
    }

    .view-series-button {
      margin-top: 8px;
      align-self: flex-end;
      padding: 0;
    }
  }

  .tutorial-skeleton {
    padding: 20px 0;
  }
}
