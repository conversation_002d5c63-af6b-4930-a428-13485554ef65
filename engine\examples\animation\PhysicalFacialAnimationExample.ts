/**
 * 物理驱动的面部动画示例
 * 演示如何使用物理驱动的面部动画系统
 */
import * as THREE from 'three';
import { Engine } from '../../core/Engine';
import { World } from '../../core/World';
import { Entity } from '../../core/Entity';
import { FacialAnimationComponent, VisemeType } from '../../avatar/components/FacialAnimationComponent';
import { FacialAnimationSystem } from '../../avatar/systems/FacialAnimationSystem';
import { FacialAnimationModelAdapterSystem, FacialAnimationModelType } from '../../avatar/systems/FacialAnimationModelAdapterSystem';
import { FacialMuscleSimulationSystem } from '../../animation/physics/FacialMuscleSimulationSystem';
import { EnhancedFacialMuscleSimulationSystem, SoftBodyType } from '../../animation/physics/EnhancedFacialMuscleSimulationSystem';
import { FacialExpressionType } from '../../animation/FacialAnimation';
import { OrbitControls } from '../../utils/OrbitControls';

/**
 * 物理驱动的面部动画示例
 */
export class PhysicalFacialAnimationExample {
  /** 引擎 */
  private engine: Engine;
  /** 世界 */
  private world: World;
  /** 场景 */
  private scene: THREE.Scene;
  /** 相机 */
  private camera: THREE.PerspectiveCamera;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer;
  /** 控制器 */
  private controls: OrbitControls;
  /** 角色实体 */
  private characterEntity: Entity;
  /** 面部动画系统 */
  private facialAnimationSystem: FacialAnimationSystem;
  /** 面部动画模型适配器系统 */
  private modelAdapterSystem: FacialAnimationModelAdapterSystem;
  /** 面部肌肉模拟系统 */
  private facialMuscleSystem: FacialMuscleSimulationSystem;
  /** 增强版面部肌肉模拟系统 */
  private enhancedFacialMuscleSystem: EnhancedFacialMuscleSimulationSystem;
  /** 是否运行中 */
  private running: boolean = false;
  /** 表情选择元素 */
  private expressionSelect: HTMLSelectElement | null = null;
  /** 权重滑块 */
  private weightSlider: HTMLInputElement | null = null;
  /** 物理驱动切换 */
  private physicsToggle: HTMLInputElement | null = null;
  /** 软体类型选择 */
  private softBodyTypeSelect: HTMLSelectElement | null = null;
  /** 应用按钮 */
  private applyButton: HTMLButtonElement | null = null;
  /** 重置按钮 */
  private resetButton: HTMLButtonElement | null = null;
  /** 当前表情 */
  private currentExpression: FacialExpressionType = FacialExpressionType.NEUTRAL;
  /** 当前权重 */
  private currentWeight: number = 0.5;
  /** 是否使用物理驱动 */
  private usePhysics: boolean = true;
  /** 当前软体类型 */
  private currentSoftBodyType: SoftBodyType = SoftBodyType.CLOTH;
  /** 软体ID */
  private softBodyId: string = '';

  /**
   * 构造函数
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    this.world = this.engine.world;

    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x333333);

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.camera.position.set(0, 1.6, 2);

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    document.body.appendChild(this.renderer.domElement);

    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 1.6, 0);
    this.controls.update();

    // 创建角色实体
    this.characterEntity = this.world.createEntity();

    // 创建面部动画系统
    this.facialAnimationSystem = new FacialAnimationSystem(this.world, {
      debug: true
    });

    // 创建面部动画模型适配器系统
    this.modelAdapterSystem = new FacialAnimationModelAdapterSystem(this.world, {
      debug: true,
      defaultModelType: FacialAnimationModelType.GLTF
    });

    // 创建面部肌肉模拟系统
    this.facialMuscleSystem = new FacialMuscleSimulationSystem(this.world, {
      debug: true,
      useSoftBodies: false
    });

    // 创建增强版面部肌肉模拟系统
    this.enhancedFacialMuscleSystem = new EnhancedFacialMuscleSimulationSystem(this.world, {
      debug: true,
      useSoftBodies: true,
      useGPU: false,
      useAdaptiveTimeStep: true,
      useCollisionDetection: true,
      usePhysicsDebugRenderer: true
    });

    // 添加系统到世界
    this.world.addSystem(this.facialAnimationSystem);
    this.world.addSystem(this.modelAdapterSystem);
    this.world.addSystem(this.facialMuscleSystem);
    this.world.addSystem(this.enhancedFacialMuscleSystem);

    // 创建灯光
    this.createLights();

    // 创建UI
    this.createUI();

    // 加载模型
    this.loadModel();

    // 添加窗口调整事件
    window.addEventListener('resize', this.onWindowResize.bind(this));
  }

  /**
   * 创建灯光
   */
  private createLights(): void {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(ambientLight);

    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 2, 3);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);
  }

  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    const uiContainer = document.createElement('div');
    uiContainer.style.position = 'absolute';
    uiContainer.style.top = '10px';
    uiContainer.style.left = '10px';
    uiContainer.style.padding = '10px';
    uiContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    uiContainer.style.borderRadius = '5px';
    uiContainer.style.color = 'white';
    document.body.appendChild(uiContainer);

    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '物理驱动的面部动画示例';
    title.style.margin = '0 0 10px 0';
    uiContainer.appendChild(title);

    // 创建表情选择
    const expressionLabel = document.createElement('label');
    expressionLabel.textContent = '表情: ';
    uiContainer.appendChild(expressionLabel);

    this.expressionSelect = document.createElement('select');
    this.expressionSelect.style.margin = '0 10px 10px 0';
    this.expressionSelect.style.padding = '5px';

    // 添加表情选项
    const expressions = [
      { value: FacialExpressionType.NEUTRAL, text: '中性' },
      { value: FacialExpressionType.HAPPY, text: '开心' },
      { value: FacialExpressionType.SAD, text: '悲伤' },
      { value: FacialExpressionType.ANGRY, text: '愤怒' },
      { value: FacialExpressionType.SURPRISED, text: '惊讶' },
      { value: FacialExpressionType.FEARFUL, text: '恐惧' },
      { value: FacialExpressionType.DISGUSTED, text: '厌恶' },
      { value: FacialExpressionType.CONTEMPT, text: '蔑视' }
    ];

    for (const expression of expressions) {
      const option = document.createElement('option');
      option.value = expression.value;
      option.textContent = expression.text;
      this.expressionSelect.appendChild(option);
    }

    this.expressionSelect.addEventListener('change', this.onExpressionChange.bind(this));
    uiContainer.appendChild(this.expressionSelect);
    uiContainer.appendChild(document.createElement('br'));

    // 创建权重滑块
    const weightLabel = document.createElement('label');
    weightLabel.textContent = '权重: ';
    uiContainer.appendChild(weightLabel);

    this.weightSlider = document.createElement('input');
    this.weightSlider.type = 'range';
    this.weightSlider.min = '0';
    this.weightSlider.max = '1';
    this.weightSlider.step = '0.1';
    this.weightSlider.value = '0.5';
    this.weightSlider.style.width = '200px';
    this.weightSlider.style.margin = '0 10px 10px 0';
    this.weightSlider.addEventListener('input', this.onWeightChange.bind(this));
    uiContainer.appendChild(this.weightSlider);
    uiContainer.appendChild(document.createElement('br'));

    // 创建物理驱动切换
    const physicsLabel = document.createElement('label');
    physicsLabel.textContent = '使用物理驱动: ';
    uiContainer.appendChild(physicsLabel);

    this.physicsToggle = document.createElement('input');
    this.physicsToggle.type = 'checkbox';
    this.physicsToggle.checked = this.usePhysics;
    this.physicsToggle.addEventListener('change', this.onPhysicsToggle.bind(this));
    uiContainer.appendChild(this.physicsToggle);
    uiContainer.appendChild(document.createElement('br'));

    // 创建软体类型选择
    const softBodyTypeLabel = document.createElement('label');
    softBodyTypeLabel.textContent = '软体类型: ';
    uiContainer.appendChild(softBodyTypeLabel);

    this.softBodyTypeSelect = document.createElement('select');
    this.softBodyTypeSelect.style.margin = '0 10px 10px 0';
    this.softBodyTypeSelect.style.padding = '5px';

    // 添加软体类型选项
    const softBodyTypes = [
      { value: SoftBodyType.CLOTH, text: '布料' },
      { value: SoftBodyType.ROPE, text: '绳索' },
      { value: SoftBodyType.DEFORMABLE, text: '可变形物体' },
      { value: SoftBodyType.BALLOON, text: '气球' },
      { value: SoftBodyType.JELLY, text: '果冻' }
    ];

    for (const type of softBodyTypes) {
      const option = document.createElement('option');
      option.value = type.value;
      option.textContent = type.text;
      this.softBodyTypeSelect.appendChild(option);
    }

    this.softBodyTypeSelect.addEventListener('change', this.onSoftBodyTypeChange.bind(this));
    uiContainer.appendChild(this.softBodyTypeSelect);
    uiContainer.appendChild(document.createElement('br'));

    // 创建应用按钮
    this.applyButton = document.createElement('button');
    this.applyButton.textContent = '应用表情';
    this.applyButton.style.padding = '5px 10px';
    this.applyButton.style.margin = '10px 10px 0 0';
    this.applyButton.addEventListener('click', this.onApplyExpression.bind(this));
    uiContainer.appendChild(this.applyButton);

    // 创建重置按钮
    this.resetButton = document.createElement('button');
    this.resetButton.textContent = '重置';
    this.resetButton.style.padding = '5px 10px';
    this.resetButton.style.margin = '10px 0 0 0';
    this.resetButton.addEventListener('click', this.onReset.bind(this));
    uiContainer.appendChild(this.resetButton);
  }

  /**
   * 加载模型
   */
  private loadModel(): void {
    // 加载模型
    // 这里是加载模型的占位代码，实际实现需要根据具体模型
    // TODO: 实现模型加载

    // 创建临时几何体
    const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
    const material = new THREE.MeshStandardMaterial({ color: 0x00ff00 });
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.set(0, 1.6, 0);
    this.scene.add(mesh);

    // 创建面部动画组件
    const facialAnimation = this.facialAnimationSystem.createFacialAnimation(this.characterEntity);

    // 创建面部肌肉模拟组件
    const facialMuscle = this.facialMuscleSystem.createFacialMuscleSimulation(this.characterEntity);

    // 创建默认肌肉
    this.facialMuscleSystem.createDefaultMuscles(this.characterEntity);

    // 创建软体
    this.createSoftBody();

    console.log('模型加载完成，已创建面部动画组件和面部肌肉模拟组件');
  }

  /**
   * 创建软体
   */
  private createSoftBody(): void {
    // 如果已存在软体，先移除
    if (this.softBodyId) {
      this.enhancedFacialMuscleSystem.removeSoftBody(this.softBodyId);
      this.softBodyId = '';
    }

    // 创建软体
    this.softBodyId = this.enhancedFacialMuscleSystem.createSoftBody(this.characterEntity, {
      type: this.currentSoftBodyType,
      name: 'face',
      position: new THREE.Vector3(0, 1.6, 0),
      size: new THREE.Vector3(0.5, 0.5, 0.5),
      mass: 0.1,
      stiffness: 100,
      damping: 0.5,
      resolution: new THREE.Vector2(10, 10),
      fixedEdges: true,
      useGravity: true
    });

    if (this.softBodyId) {
      console.log(`创建软体成功: ${this.softBodyId}`);
    } else {
      console.warn('创建软体失败');
    }
  }

  /**
   * 窗口调整事件处理
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 表情变更事件处理
   */
  private onExpressionChange(event: Event): void {
    if (this.expressionSelect) {
      this.currentExpression = this.expressionSelect.value as FacialExpressionType;
    }
  }

  /**
   * 权重变更事件处理
   */
  private onWeightChange(event: Event): void {
    if (this.weightSlider) {
      this.currentWeight = parseFloat(this.weightSlider.value);
    }
  }

  /**
   * 物理驱动切换事件处理
   */
  private onPhysicsToggle(event: Event): void {
    if (this.physicsToggle) {
      this.usePhysics = this.physicsToggle.checked;
    }
  }

  /**
   * 软体类型变更事件处理
   */
  private onSoftBodyTypeChange(event: Event): void {
    if (this.softBodyTypeSelect) {
      this.currentSoftBodyType = this.softBodyTypeSelect.value as SoftBodyType;
      this.createSoftBody();
    }
  }

  /**
   * 应用表情事件处理
   */
  private onApplyExpression(): void {
    if (this.usePhysics) {
      // 使用物理驱动
      if (this.softBodyId) {
        // 使用软体
        this.enhancedFacialMuscleSystem.applySoftBodyExpression(
          this.characterEntity,
          this.currentExpression,
          this.currentWeight
        );
      } else {
        // 使用肌肉模拟
        this.facialMuscleSystem.applyExpression(
          this.characterEntity,
          this.currentExpression,
          this.currentWeight
        );
      }
    } else {
      // 使用传统方式
      const facialAnimation = this.facialAnimationSystem.getFacialAnimation(this.characterEntity);
      if (facialAnimation) {
        facialAnimation.setExpression(this.currentExpression, this.currentWeight);
      }
    }
  }

  /**
   * 重置事件处理
   */
  private onReset(): void {
    // 重置表情
    if (this.usePhysics) {
      // 使用物理驱动
      if (this.softBodyId) {
        // 使用软体
        this.enhancedFacialMuscleSystem.applySoftBodyExpression(
          this.characterEntity,
          FacialExpressionType.NEUTRAL,
          1.0
        );
      } else {
        // 使用肌肉模拟
        this.facialMuscleSystem.applyExpression(
          this.characterEntity,
          FacialExpressionType.NEUTRAL,
          1.0
        );
      }
    } else {
      // 使用传统方式
      const facialAnimation = this.facialAnimationSystem.getFacialAnimation(this.characterEntity);
      if (facialAnimation) {
        facialAnimation.setExpression(FacialExpressionType.NEUTRAL, 1.0);
      }
    }

    // 重置UI
    if (this.expressionSelect) {
      this.expressionSelect.value = FacialExpressionType.NEUTRAL;
      this.currentExpression = FacialExpressionType.NEUTRAL;
    }

    if (this.weightSlider) {
      this.weightSlider.value = '0.5';
      this.currentWeight = 0.5;
    }
  }

  /**
   * 启动
   */
  public start(): void {
    if (this.running) return;

    this.running = true;
    this.animate();
  }

  /**
   * 停止
   */
  public stop(): void {
    this.running = false;
  }

  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;

    requestAnimationFrame(this.animate.bind(this));

    // 更新控制器
    this.controls.update();

    // 更新世界
    this.world.update();

    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }
}
