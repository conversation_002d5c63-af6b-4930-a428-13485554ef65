/**
 * 高级后处理示例
 * 展示如何使用高级后处理效果
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { World } from '../../src/core/World';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Renderer } from '../../src/rendering/Renderer';
import { Camera } from '../../src/rendering/Camera';
import { Light, LightType } from '../../src/rendering/Light';
import { Transform } from '../../src/scene/Transform';
import { RenderSystem } from '../../src/rendering/RenderSystem';
import { 
  PostProcessingSystem,
  FXAAEffect,
  BloomEffect,
  SSAOEffect,
  SSREffect,
  SSGIEffect,
  MotionBlurEffect,
  ToneMappingEffect,
  ToneMappingType,
  ColorCorrectionEffect
} from '../../src/rendering/postprocessing';

/**
 * 高级后处理示例类
 */
export class AdvancedPostProcessingExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 世界实例 */
  private world: World;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 相机实体 */
  private cameraEntity: Entity;
  
  /** 方向光实体 */
  private directionalLightEntity: Entity;
  
  /** 环境光实体 */
  private ambientLightEntity: Entity;
  
  /** 渲染系统 */
  private renderSystem: RenderSystem;
  
  /** 后处理系统 */
  private postProcessingSystem: PostProcessingSystem;
  
  /** 球体实体列表 */
  private sphereEntities: Entity[] = [];
  
  /** 立方体实体列表 */
  private boxEntities: Entity[] = [];
  
  /** 地面实体 */
  private groundEntity: Entity;
  
  /** 动画ID */
  private animationId: number = 0;
  
  /** 是否正在运行 */
  private running: boolean = false;
  
  /** 当前效果索引 */
  private currentEffectIndex: number = 0;
  
  /** 效果切换计时器 */
  private effectSwitchTimer: number = 0;
  
  /** 效果切换间隔（秒） */
  private effectSwitchInterval: number = 5;
  
  /** 效果列表 */
  private effects: any[] = [];
  
  /** 效果名称列表 */
  private effectNames: string[] = [
    'FXAA抗锯齿',
    '泛光效果',
    '环境光遮蔽',
    '屏幕空间反射',
    '屏幕空间全局光照',
    '运动模糊',
    '色调映射',
    '颜色校正',
    '所有效果'
  ];
  
  /**
   * 创建示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建世界
    this.world = new World();
    this.engine.setWorld(this.world);
    
    // 创建渲染器
    const renderer = new Renderer({
      width: window.innerWidth,
      height: window.innerHeight,
      antialias: true,
      shadows: true,
      shadowMapType: THREE.PCFSoftShadowMap
    });
    
    // 创建渲染系统
    this.renderSystem = new RenderSystem(renderer, {
      enableShadows: true,
      enablePostProcessing: true
    });
    this.world.addSystem(this.renderSystem);
    
    // 获取后处理系统
    this.postProcessingSystem = this.renderSystem.getPostProcessingSystem()!;
    
    // 创建场景
    this.scene = new Scene('高级后处理示例场景');
    this.world.addEntity(this.scene);
    
    // 设置活跃场景
    this.renderSystem.setActiveScene(this.scene);
    
    // 创建相机
    this.cameraEntity = new Entity('相机');
    this.cameraEntity.addComponent(new Camera({
      type: 'perspective',
      fov: 60,
      near: 0.1,
      far: 1000,
      aspect: window.innerWidth / window.innerHeight
    }));
    this.cameraEntity.addComponent(new Transform({
      position: { x: 0, y: 5, z: 15 },
      rotation: { x: -0.3, y: 0, z: 0 }
    }));
    this.world.addEntity(this.cameraEntity);
    this.scene.addEntity(this.cameraEntity);
    
    // 设置活跃相机
    this.renderSystem.setActiveCamera(this.cameraEntity.getComponent('Camera') as Camera);
    
    // 创建方向光
    this.directionalLightEntity = new Entity('方向光');
    this.directionalLightEntity.addComponent(new Light({
      type: LightType.DIRECTIONAL,
      color: 0xffffff,
      intensity: 1,
      castShadow: true,
      shadowMapSize: 2048,
      shadowCameraNear: 0.1,
      shadowCameraFar: 500,
      shadowCameraLeft: -50,
      shadowCameraRight: 50,
      shadowCameraTop: 50,
      shadowCameraBottom: -50,
      shadowBias: -0.0005
    }));
    this.directionalLightEntity.addComponent(new Transform({
      position: { x: 20, y: 30, z: 20 },
      rotation: { x: -Math.PI / 4, y: Math.PI / 4, z: 0 }
    }));
    this.world.addEntity(this.directionalLightEntity);
    this.scene.addEntity(this.directionalLightEntity);
    
    // 创建环境光
    this.ambientLightEntity = new Entity('环境光');
    this.ambientLightEntity.addComponent(new Light({
      type: LightType.AMBIENT,
      color: 0x404040,
      intensity: 0.5
    }));
    this.world.addEntity(this.ambientLightEntity);
    this.scene.addEntity(this.ambientLightEntity);
    
    // 创建场景内容
    this.createSceneContent();
    
    // 创建后处理效果
    this.createPostProcessingEffects();
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', this.handleResize.bind(this));
    
    // 添加DOM元素
    this.addDomElements();
  }
  
  /**
   * 创建场景内容
   */
  private createSceneContent(): void {
    // 创建地面
    this.groundEntity = new Entity('地面');
    this.groundEntity.addComponent(new Transform({
      position: { x: 0, y: -0.5, z: 0 },
      scale: { x: 100, y: 1, z: 100 }
    }));
    
    // 创建地面网格
    const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
    const groundMaterial = new THREE.MeshStandardMaterial({
      color: 0x808080,
      roughness: 0.7,
      metalness: 0.1
    });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.receiveShadow = true;
    
    // 添加到实体
    this.groundEntity.getComponent('Transform')?.getObject3D().add(groundMesh);
    
    // 添加到场景
    this.world.addEntity(this.groundEntity);
    this.scene.addEntity(this.groundEntity);
    
    // 创建多个球体
    for (let i = 0; i < 10; i++) {
      const sphereEntity = new Entity(`球体${i}`);
      sphereEntity.addComponent(new Transform({
        position: {
          x: Math.random() * 20 - 10,
          y: Math.random() * 5 + 1,
          z: Math.random() * 20 - 10
        },
        scale: { x: 1, y: 1, z: 1 }
      }));
      
      // 创建球体网格
      const sphereGeometry = new THREE.SphereGeometry(1, 32, 32);
      const sphereMaterial = new THREE.MeshStandardMaterial({
        color: new THREE.Color(Math.random(), Math.random(), Math.random()),
        roughness: Math.random() * 0.5 + 0.25,
        metalness: Math.random() * 0.8
      });
      const sphereMesh = new THREE.Mesh(sphereGeometry, sphereMaterial);
      sphereMesh.castShadow = true;
      sphereMesh.receiveShadow = true;
      
      // 添加到实体
      sphereEntity.getComponent('Transform')?.getObject3D().add(sphereMesh);
      
      // 添加到场景
      this.world.addEntity(sphereEntity);
      this.scene.addEntity(sphereEntity);
      
      // 添加到列表
      this.sphereEntities.push(sphereEntity);
    }
    
    // 创建多个立方体
    for (let i = 0; i < 10; i++) {
      const boxEntity = new Entity(`立方体${i}`);
      boxEntity.addComponent(new Transform({
        position: {
          x: Math.random() * 20 - 10,
          y: Math.random() * 5 + 1,
          z: Math.random() * 20 - 10
        },
        rotation: {
          x: Math.random() * Math.PI,
          y: Math.random() * Math.PI,
          z: Math.random() * Math.PI
        },
        scale: { x: 1, y: 1, z: 1 }
      }));
      
      // 创建立方体网格
      const boxGeometry = new THREE.BoxGeometry(1, 1, 1);
      const boxMaterial = new THREE.MeshStandardMaterial({
        color: new THREE.Color(Math.random(), Math.random(), Math.random()),
        roughness: Math.random() * 0.5 + 0.25,
        metalness: Math.random() * 0.8
      });
      const boxMesh = new THREE.Mesh(boxGeometry, boxMaterial);
      boxMesh.castShadow = true;
      boxMesh.receiveShadow = true;
      
      // 添加到实体
      boxEntity.getComponent('Transform')?.getObject3D().add(boxMesh);
      
      // 添加到场景
      this.world.addEntity(boxEntity);
      this.scene.addEntity(boxEntity);
      
      // 添加到列表
      this.boxEntities.push(boxEntity);
    }
  }
  
  /**
   * 创建后处理效果
   */
  private createPostProcessingEffects(): void {
    // 创建FXAA抗锯齿效果
    const fxaaEffect = new FXAAEffect({
      name: 'FXAA',
      enabled: false,
      edgeThreshold: 0.1,
      edgeThresholdMin: 0.05
    });
    this.postProcessingSystem.addEffect(fxaaEffect);
    this.effects.push(fxaaEffect);
    
    // 创建泛光效果
    const bloomEffect = new BloomEffect({
      name: 'Bloom',
      enabled: false,
      strength: 1.5,
      radius: 0.5,
      threshold: 0.6
    });
    this.postProcessingSystem.addEffect(bloomEffect);
    this.effects.push(bloomEffect);
    
    // 创建环境光遮蔽效果
    const ssaoEffect = new SSAOEffect({
      name: 'SSAO',
      enabled: false,
      kernelRadius: 8,
      minDistance: 0.005,
      maxDistance: 0.1
    });
    ssaoEffect.setSceneAndCamera(this.scene.getThreeScene(), this.cameraEntity.getComponent('Camera')!.getThreeCamera());
    this.postProcessingSystem.addEffect(ssaoEffect);
    this.effects.push(ssaoEffect);
    
    // 创建屏幕空间反射效果
    const ssrEffect = new SSREffect({
      name: 'SSR',
      enabled: false,
      intensity: 0.5,
      maxSteps: 20,
      maxDistance: 50,
      stride: 1,
      roughness: 0.5,
      thickness: 0.5
    });
    ssrEffect.setSceneAndCamera(this.scene.getThreeScene(), this.cameraEntity.getComponent('Camera')!.getThreeCamera());
    this.postProcessingSystem.addEffect(ssrEffect);
    this.effects.push(ssrEffect);
    
    // 创建屏幕空间全局光照效果
    const ssgiEffect = new SSGIEffect({
      name: 'SSGI',
      enabled: false,
      intensity: 0.5,
      radius: 5.0,
      bias: 0.05,
      samples: 16,
      falloff: 1.0
    });
    ssgiEffect.setSceneAndCamera(this.scene.getThreeScene(), this.cameraEntity.getComponent('Camera')!.getThreeCamera());
    this.postProcessingSystem.addEffect(ssgiEffect);
    this.effects.push(ssgiEffect);
    
    // 创建运动模糊效果
    const motionBlurEffect = new MotionBlurEffect({
      name: 'MotionBlur',
      enabled: false,
      intensity: 0.5,
      samples: 32
    });
    motionBlurEffect.setSceneAndCamera(this.scene.getThreeScene(), this.cameraEntity.getComponent('Camera')!.getThreeCamera());
    this.postProcessingSystem.addEffect(motionBlurEffect);
    this.effects.push(motionBlurEffect);
    
    // 创建色调映射效果
    const toneMappingEffect = new ToneMappingEffect({
      name: 'ToneMapping',
      enabled: false,
      type: ToneMappingType.ACESFilmic,
      exposure: 1.0,
      gamma: 2.2,
      contrast: 1.0,
      saturation: 1.0,
      brightness: 1.0
    });
    this.postProcessingSystem.addEffect(toneMappingEffect);
    this.effects.push(toneMappingEffect);
    
    // 创建颜色校正效果
    const colorCorrectionEffect = new ColorCorrectionEffect({
      name: 'ColorCorrection',
      enabled: false,
      brightness: 1.0,
      contrast: 1.0,
      saturation: 1.0,
      hue: 0.0,
      gamma: 1.0,
      temperature: 6500.0,
      tint: 0.0
    });
    this.postProcessingSystem.addEffect(colorCorrectionEffect);
    this.effects.push(colorCorrectionEffect);
  }
  
  /**
   * 添加DOM元素
   */
  private addDomElements(): void {
    // 创建信息面板
    const infoPanel = document.createElement('div');
    infoPanel.style.position = 'absolute';
    infoPanel.style.top = '10px';
    infoPanel.style.left = '10px';
    infoPanel.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    infoPanel.style.color = 'white';
    infoPanel.style.padding = '10px';
    infoPanel.style.borderRadius = '5px';
    infoPanel.style.fontFamily = 'Arial, sans-serif';
    infoPanel.innerHTML = `
      <h2>高级后处理示例</h2>
      <p>这个示例展示了如何使用高级后处理效果。</p>
      <p>每隔5秒会自动切换效果。</p>
      <p>当前效果: <span id="current-effect">${this.effectNames[this.currentEffectIndex]}</span></p>
    `;
    document.body.appendChild(infoPanel);
  }
  
  /**
   * 处理窗口大小变化
   */
  private handleResize(): void {
    // 更新相机宽高比
    const camera = this.cameraEntity.getComponent('Camera') as Camera;
    camera.setAspect(window.innerWidth / window.innerHeight);
  }
  
  /**
   * 更新场景
   * @param deltaTime 帧间隔时间（秒）
   */
  private update(deltaTime: number): void {
    // 旋转相机
    const cameraTransform = this.cameraEntity.getComponent('Transform');
    if (cameraTransform) {
      const rotation = cameraTransform.getRotation();
      rotation.y += deltaTime * 0.1;
      cameraTransform.setRotation(rotation.x, rotation.y, rotation.z);
    }
    
    // 旋转球体
    for (let i = 0; i < this.sphereEntities.length; i++) {
      const transform = this.sphereEntities[i].getComponent('Transform');
      if (transform) {
        const position = transform.getPosition();
        position.y = Math.max(1, position.y + Math.sin(Date.now() * 0.001 + i) * 0.05);
        transform.setPosition(position.x, position.y, position.z);
      }
    }
    
    // 旋转立方体
    for (let i = 0; i < this.boxEntities.length; i++) {
      const transform = this.boxEntities[i].getComponent('Transform');
      if (transform) {
        const rotation = transform.getRotation();
        rotation.x += deltaTime * 0.5;
        rotation.y += deltaTime * 0.3;
        transform.setRotation(rotation.x, rotation.y, rotation.z);
      }
    }
    
    // 更新效果切换计时器
    this.effectSwitchTimer += deltaTime;
    if (this.effectSwitchTimer >= this.effectSwitchInterval) {
      this.effectSwitchTimer = 0;
      this.switchEffect();
    }
    
    // 更新世界
    this.world.update(deltaTime);
  }
  
  /**
   * 切换效果
   */
  private switchEffect(): void {
    // 禁用所有效果
    for (const effect of this.effects) {
      effect.setEnabled(false);
    }
    
    // 切换到下一个效果
    this.currentEffectIndex = (this.currentEffectIndex + 1) % this.effectNames.length;
    
    // 启用当前效果
    if (this.currentEffectIndex < this.effects.length) {
      this.effects[this.currentEffectIndex].setEnabled(true);
    } else {
      // 启用所有效果
      for (const effect of this.effects) {
        effect.setEnabled(true);
      }
    }
    
    // 更新效果名称
    document.getElementById('current-effect')!.textContent = this.effectNames[this.currentEffectIndex];
  }
  
  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;
    
    const now = performance.now();
    const deltaTime = Math.min((now - (this._lastTime || now)) / 1000, 0.1);
    this._lastTime = now;
    
    this.update(deltaTime);
    
    this.animationId = requestAnimationFrame(this.animate.bind(this));
  }
  
  /** 上一帧时间 */
  private _lastTime: number = 0;
  
  /**
   * 启动示例
   */
  public start(): void {
    if (this.running) return;
    
    this.running = true;
    this._lastTime = performance.now();
    this.animate();
  }
  
  /**
   * 停止示例
   */
  public stop(): void {
    if (!this.running) return;
    
    this.running = false;
    cancelAnimationFrame(this.animationId);
  }
  
  /**
   * 销毁示例
   */
  public dispose(): void {
    this.stop();
    
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize.bind(this));
    
    // 销毁引擎
    this.engine.dispose();
  }
}
