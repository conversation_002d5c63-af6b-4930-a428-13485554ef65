/**
 * 面板容器样式
 */

.panel-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
  background-color: #fff;
  border-radius: 4px;
  
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
    user-select: none;
    
    .panel-title {
      display: flex;
      align-items: center;
      font-weight: 500;
      color: #333;
      
      .panel-icon {
        margin-right: 8px;
        font-size: 16px;
      }
      
      .panel-title-text {
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    
    .panel-actions {
      display: flex;
      align-items: center;
      
      .panel-action-button {
        cursor: pointer;
        padding: 4px;
        font-size: 16px;
        color: #666;
        transition: color 0.3s;
        
        &:hover {
          color: #1890ff;
        }
      }
    }
  }
  
  .panel-content {
    flex: 1;
    overflow: auto;
    padding: 8px;
  }
}

// 暗色主题样式
:global(.dark-theme) {
  .panel-container {
    background-color: #1e1e1e;
    
    .panel-header {
      background-color: #252526;
      border-bottom-color: #303030;
      
      .panel-title {
        color: #ccc;
      }
      
      .panel-actions {
        .panel-action-button {
          color: #aaa;
          
          &:hover {
            color: #1890ff;
          }
        }
      }
    }
  }
}
