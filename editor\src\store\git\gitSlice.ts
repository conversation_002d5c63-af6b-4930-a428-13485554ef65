/**
 * Git状态管理Slice
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../../store';
import { 
  GitStatus, 
  GitBranch, 
  GitCommit, 
  GitFileStatus, 
  GitConflict,
  GitRemote
} from '../../services/GitService';

// Git状态接口
export interface GitState {
  status: GitStatus | null;
  branches: GitBranch[];
  currentBranch: string;
  commitHistory: GitCommit[];
  unstagedFiles: GitFileStatus[];
  stagedFiles: GitFileStatus[];
  conflictFiles: GitFileStatus[];
  remotes: GitRemote[];
  currentRemote: string;
  isLoading: boolean;
  isMerging: boolean;
  mergeConflicts: GitConflict[];
  showGitPanel: boolean;
  showCommitPanel: boolean;
  showBranchPanel: boolean;
  showConflictPanel: boolean;
}

// 初始状态
const initialState: GitState = {
  status: null,
  branches: [],
  currentBranch: '',
  commitHistory: [],
  unstagedFiles: [],
  stagedFiles: [],
  conflictFiles: [],
  remotes: [],
  currentRemote: '',
  isLoading: false,
  isMerging: false,
  mergeConflicts: [],
  showGitPanel: false,
  showCommitPanel: false,
  showBranchPanel: false,
  showConflictPanel: false
};

// 创建Slice
export const gitSlice = createSlice({
  name: 'git',
  initialState,
  reducers: {
    // 设置Git状态
    setGitStatus: (state, action: PayloadAction<GitStatus | null>) => {
      state.status = action.payload;
    },
    
    // 设置Git分支列表
    setGitBranches: (state, action: PayloadAction<GitBranch[]>) => {
      state.branches = action.payload;
    },
    
    // 设置当前分支
    setCurrentBranch: (state, action: PayloadAction<string>) => {
      state.currentBranch = action.payload;
    },
    
    // 设置提交历史
    setCommitHistory: (state, action: PayloadAction<GitCommit[]>) => {
      state.commitHistory = action.payload;
    },
    
    // 设置未暂存文件
    setUnstagedFiles: (state, action: PayloadAction<GitFileStatus[]>) => {
      state.unstagedFiles = action.payload;
    },
    
    // 设置已暂存文件
    setStagedFiles: (state, action: PayloadAction<GitFileStatus[]>) => {
      state.stagedFiles = action.payload;
    },
    
    // 设置冲突文件
    setConflictFiles: (state, action: PayloadAction<GitFileStatus[]>) => {
      state.conflictFiles = action.payload;
    },
    
    // 设置远程仓库列表
    setRemotes: (state, action: PayloadAction<GitRemote[]>) => {
      state.remotes = action.payload;
    },
    
    // 设置当前远程仓库
    setCurrentRemote: (state, action: PayloadAction<string>) => {
      state.currentRemote = action.payload;
    },
    
    // 设置加载状态
    setIsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    
    // 设置合并状态
    setIsMerging: (state, action: PayloadAction<boolean>) => {
      state.isMerging = action.payload;
    },
    
    // 设置合并冲突
    setMergeConflicts: (state, action: PayloadAction<GitConflict[]>) => {
      state.mergeConflicts = action.payload;
    },
    
    // 显示/隐藏Git面板
    setShowGitPanel: (state, action: PayloadAction<boolean>) => {
      state.showGitPanel = action.payload;
    },
    
    // 显示/隐藏提交面板
    setShowCommitPanel: (state, action: PayloadAction<boolean>) => {
      state.showCommitPanel = action.payload;
    },
    
    // 显示/隐藏分支面板
    setShowBranchPanel: (state, action: PayloadAction<boolean>) => {
      state.showBranchPanel = action.payload;
    },
    
    // 显示/隐藏冲突面板
    setShowConflictPanel: (state, action: PayloadAction<boolean>) => {
      state.showConflictPanel = action.payload;
    },
    
    // 重置状态
    resetGitState: (state) => {
      Object.assign(state, initialState);
    }
  }
});

// 导出actions
export const {
  setGitStatus,
  setGitBranches,
  setCurrentBranch,
  setCommitHistory,
  setUnstagedFiles,
  setStagedFiles,
  setConflictFiles,
  setRemotes,
  setCurrentRemote,
  setIsLoading,
  setIsMerging,
  setMergeConflicts,
  setShowGitPanel,
  setShowCommitPanel,
  setShowBranchPanel,
  setShowConflictPanel,
  resetGitState
} = gitSlice.actions;

// 选择器
export const selectGitStatus = (state: RootState) => state.git.status;
export const selectGitBranches = (state: RootState) => state.git.branches;
export const selectCurrentBranch = (state: RootState) => state.git.currentBranch;
export const selectCommitHistory = (state: RootState) => state.git.commitHistory;
export const selectUnstagedFiles = (state: RootState) => state.git.unstagedFiles;
export const selectStagedFiles = (state: RootState) => state.git.stagedFiles;
export const selectConflictFiles = (state: RootState) => state.git.conflictFiles;
export const selectRemotes = (state: RootState) => state.git.remotes;
export const selectCurrentRemote = (state: RootState) => state.git.currentRemote;
export const selectIsLoading = (state: RootState) => state.git.isLoading;
export const selectIsMerging = (state: RootState) => state.git.isMerging;
export const selectMergeConflicts = (state: RootState) => state.git.mergeConflicts;
export const selectShowGitPanel = (state: RootState) => state.git.showGitPanel;
export const selectShowCommitPanel = (state: RootState) => state.git.showCommitPanel;
export const selectShowBranchPanel = (state: RootState) => state.git.showBranchPanel;
export const selectShowConflictPanel = (state: RootState) => state.git.showConflictPanel;

// 导出reducer
export default gitSlice.reducer;
