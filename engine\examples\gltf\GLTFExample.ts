/**
 * GLTF示例
 * 展示如何加载和使用GLTF模型
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Camera } from '../../src/rendering/Camera';
import { Transform } from '../../src/scene/Transform';
import { GLTFLoader, GLTFModelComponent, GLTFAnimationComponent, AnimationLoopMode } from '../../src/gltf';
import { GLTFSystem } from '../../src/gltf/systems/GLTFSystem';

/**
 * GLTF示例类
 */
export class GLTFExample {
  /** 引擎实例 */
  private engine: Engine;

  /** 场景 */
  private scene: Scene;

  /** 相机实体 */
  private cameraEntity: Entity;

  /** 模型实体 */
  private modelEntity: Entity;

  /** GLTF系统 */
  private gltfSystem: GLTFSystem;

  /** 模型URL */
  private modelUrl: string = 'https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/Duck/glTF/Duck.gltf';

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 旋转速度 */
  private rotationSpeed: number = 0.5;

  /**
   * 创建GLTF示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine({
      canvas: 'canvas',
      autoStart: false
    });

    // 创建GLTF系统
    this.gltfSystem = new GLTFSystem({
      autoPlayAnimations: true,
      useDraco: true
    });

    // 添加GLTF系统到引擎
    this.engine.addSystem(this.gltfSystem);

    // 获取世界
    const world = this.engine.getWorld();

    // 创建场景
    this.scene = world.createScene('GLTF示例场景');

    // 设置场景为活跃场景
    world.setActiveScene(this.scene);

    // 创建相机
    this.cameraEntity = this.createCamera();

    // 创建灯光
    this.createLights();

    // 创建地面
    this.createGround();

    // 创建模型
    this.modelEntity = this.createModel();

    // 初始化引擎
    this.engine.initialize();

    // 设置活跃相机
    this.engine.setActiveCamera(this.cameraEntity.getComponent<Camera>(Camera.type));

    this.initialized = true;
  }

  /**
   * 创建相机
   * @returns 相机实体
   */
  private createCamera(): Entity {
    const entity = new Entity('相机');

    // 添加变换组件
    const transform = new Transform();
    transform.setPosition(0, 2, 5);
    transform.lookAt(new THREE.Vector3(0, 0, 0));
    entity.addComponent(transform);

    // 添加相机组件
    const camera = new Camera({
      type: 'perspective',
      fov: 60,
      near: 0.1,
      far: 1000
    });
    entity.addComponent(camera);

    // 添加到场景
    this.scene.addEntity(entity);

    return entity;
  }

  /**
   * 创建灯光
   */
  private createLights(): void {
    // 创建环境光
    const ambientLight = new Entity('环境光');
    ambientLight.addComponent(new Transform());
    ambientLight.addComponent(new THREE.AmbientLight(0x404040, 1));
    this.scene.addEntity(ambientLight);

    // 创建方向光
    const directionalLight = new Entity('方向光');
    const directionalLightTransform = new Transform();
    directionalLightTransform.setPosition(1, 2, 1);
    directionalLight.addComponent(directionalLightTransform);

    const light = new THREE.DirectionalLight(0xffffff, 1);
    light.castShadow = true;
    light.shadow.mapSize.width = 1024;
    light.shadow.mapSize.height = 1024;
    light.shadow.camera.near = 0.1;
    light.shadow.camera.far = 10;
    light.shadow.camera.left = -5;
    light.shadow.camera.right = 5;
    light.shadow.camera.top = 5;
    light.shadow.camera.bottom = -5;
    directionalLight.addComponent(light);

    this.scene.addEntity(directionalLight);
  }

  /**
   * 创建地面
   */
  private createGround(): void {
    const ground = new Entity('地面');

    // 添加变换组件
    const transform = new Transform();
    transform.setPosition(0, -0.5, 0);
    transform.setScale(10, 0.1, 10);
    ground.addComponent(transform);

    // 创建网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({
      color: 0x808080,
      roughness: 0.7,
      metalness: 0.1
    });
    const mesh = new THREE.Mesh(geometry, material);
    mesh.receiveShadow = true;

    // 添加到实体
    ground.addComponent(mesh);

    // 添加到场景
    this.scene.addEntity(ground);
  }

  /**
   * 创建模型
   * @returns 模型实体
   */
  private createModel(): Entity {
    const entity = new Entity('GLTF模型');

    // 添加变换组件
    const transform = new Transform();
    transform.setPosition(0, 0, 0);
    entity.addComponent(transform);

    // 添加GLTF模型组件
    const modelComponent = new GLTFModelComponent(null);
    modelComponent.setURL(this.modelUrl);
    entity.addComponent(modelComponent);

    // 添加到场景
    this.scene.addEntity(entity);

    return entity;
  }

  /**
   * 启动示例
   */
  public start(): void {
    if (!this.initialized) {
      console.error('示例未初始化');
      return;
    }

    // 启动引擎
    this.engine.start();

    // 添加更新回调
    this.engine.on('update', this.update.bind(this));

    console.log('GLTF示例已启动');
  }

  /**
   * 更新回调
   * @param deltaTime 帧间隔时间（秒）
   */
  private update(deltaTime: number): void {
    // 旋转模型
    const transform = this.modelEntity.getTransform();
    if (transform) {
      const rotation = transform.getRotation();
      rotation.y += this.rotationSpeed * deltaTime;
      transform.setRotation(rotation.x, rotation.y, rotation.z);
    }

    // 检查模型是否已加载
    const modelComponent = this.modelEntity.getComponent<GLTFModelComponent>(GLTFModelComponent.type);
    if (modelComponent && modelComponent.isLoaded() && !this.modelLoaded) {
      this.modelLoaded = true;
      console.log('模型已加载');

      // 获取动画组件
      const animationComponent = this.modelEntity.getComponent<GLTFAnimationComponent>(GLTFAnimationComponent.type);
      if (animationComponent) {
        // 获取所有动画剪辑
        const clips = animationComponent.getClips();
        console.log(`模型包含 ${clips.length} 个动画`);

        // 如果有动画，播放第一个
        if (clips.length > 0) {
          animationComponent.play(clips[0].name, {
            loopMode: AnimationLoopMode.REPEAT,
            timeScale: 1.0
          });
        }
      }
    }
  }

  /**
   * 停止示例
   */
  public stop(): void {
    // 停止引擎
    this.engine.stop();

    console.log('GLTF示例已停止');
  }

  /**
   * 销毁示例
   */
  public dispose(): void {
    // 停止引擎
    this.stop();

    // 销毁引擎
    this.engine.dispose();

    console.log('GLTF示例已销毁');
  }

  /** 模型是否已加载 */
  private modelLoaded: boolean = false;

  /**
   * 加载模型
   * @param url 模型URL
   */
  public loadModel(url: string): void {
    // 保存URL
    this.modelUrl = url;

    // 移除旧模型
    this.scene.removeEntity(this.modelEntity);

    // 创建新模型
    this.modelEntity = this.createModel();

    // 重置标志
    this.modelLoaded = false;
  }

  /**
   * 导出模型
   */
  public exportModel(): void {
    // 检查模型是否已加载
    const modelComponent = this.modelEntity.getComponent<GLTFModelComponent>(GLTFModelComponent.type);
    if (!modelComponent || !modelComponent.isLoaded()) {
      console.warn('模型未加载，无法导出');
      return;
    }

    // 导出模型
    this.gltfSystem.exportEntity(this.modelEntity, {
      binary: true
    }).then((gltf) => {
      // 保存文件
      const filename = this.modelUrl.split('/').pop() || 'model.glb';
      this.gltfSystem.getExporter().saveAs(gltf, filename);
    }).catch((error) => {
      console.error('导出模型失败:', error);
    });
  }

  /**
   * 播放动画
   */
  public playAnimation(): void {
    // 获取动画组件
    const animationComponent = this.modelEntity.getComponent<GLTFAnimationComponent>(GLTFAnimationComponent.type);
    if (!animationComponent) {
      console.warn('模型没有动画组件');
      return;
    }

    // 获取所有动画剪辑
    const clips = animationComponent.getClips();
    if (clips.length === 0) {
      console.warn('模型没有动画');
      return;
    }

    // 播放第一个动画
    animationComponent.play(clips[0].name);
  }

  /**
   * 暂停动画
   */
  public pauseAnimation(): void {
    // 获取动画组件
    const animationComponent = this.modelEntity.getComponent<GLTFAnimationComponent>(GLTFAnimationComponent.type);
    if (!animationComponent) {
      return;
    }

    // 暂停动画
    animationComponent.pause();
  }

  /**
   * 停止动画
   */
  public stopAnimation(): void {
    // 获取动画组件
    const animationComponent = this.modelEntity.getComponent<GLTFAnimationComponent>(GLTFAnimationComponent.type);
    if (!animationComponent) {
      return;
    }

    // 停止动画
    animationComponent.stop();
  }

  /**
   * 调整大小
   */
  public resize(): void {
    // 获取相机组件
    const camera = this.cameraEntity.getComponent<Camera>(Camera.type);
    if (camera) {
      // 更新相机宽高比
      camera.updateAspect();
    }
  }
}
