.network-diagnostic-panel {
  margin-bottom: 16px;
}

.network-diagnostic-panel .ant-card-head-title {
  display: flex;
  align-items: center;
}

.network-diagnostic-panel .ant-card-head-title .anticon {
  margin-right: 8px;
  font-size: 16px;
}

.network-diagnostic-panel .diagnostic-content {
  padding: 16px 0;
}

.network-diagnostic-panel .diagnostic-result-container {
  margin-top: 24px;
  min-height: 200px;
}

.network-diagnostic-panel .diagnostic-running {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
}

.network-diagnostic-panel .diagnostic-running .ant-spin {
  margin-bottom: 16px;
}

.network-diagnostic-panel .diagnostic-start {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
}

.network-diagnostic-panel .test-result {
  margin-bottom: 16px;
}

.network-diagnostic-panel .test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.network-diagnostic-panel .test-content {
  padding-left: 24px;
}

.network-diagnostic-panel .test-metrics {
  display: flex;
  justify-content: space-between;
  margin: 8px 0;
  background-color: #f5f5f5;
  padding: 8px 16px;
  border-radius: 4px;
}

.network-diagnostic-panel .test-issues,
.network-diagnostic-panel .test-solutions {
  margin-top: 8px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.network-diagnostic-panel .ant-list-item {
  padding: 8px 0;
}

.network-diagnostic-panel .ant-collapse-header {
  align-items: center !important;
}

.network-diagnostic-panel .ant-collapse-header .ant-space {
  align-items: center;
}

.network-diagnostic-panel .ant-steps-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.network-diagnostic-panel .ant-result {
  padding: 24px 0;
}

.network-diagnostic-panel .ant-divider {
  margin: 24px 0 16px;
}

.network-diagnostic-panel .ant-collapse {
  background-color: #fff;
}

.network-diagnostic-panel .ant-collapse > .ant-collapse-item > .ant-collapse-header {
  padding: 8px 16px;
}

.network-diagnostic-panel .ant-collapse-content > .ant-collapse-content-box {
  padding: 8px 16px;
}
