/**
 * 组织权限管理面板组件
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Tree,
  Table,
  Select,
  Button,
  Space,
  Tag,
  Switch,
  Tooltip,
  Divider,
  Modal,
  message,
  Typography,
  Input,
  Form,
  Radio
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  TeamOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
  BranchesOutlined,
  ApartmentOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { selectUsers } from '../../store/collaboration/collaborationSlice';
import { CollaborationRole, collaborationService } from '../../services/CollaborationService';
import { Permission, permissionService } from '../../services/PermissionService';
import { permissionCheckService } from '../../services/PermissionCheckService';
import {
  OrganizationNode,
  OrganizationNodeType,
  PermissionInheritanceStrategy,
  organizationPermissionService
} from '../../services/OrganizationPermissionService';

const { Text, Title } = Typography;
const { Option } = Select;
const { confirm } = Modal;
const { DirectoryTree } = Tree;
const { TextArea } = Input;

interface OrganizationPermissionPanelProps {
  className?: string;
}

/**
 * 组织权限管理面板组件
 */
const OrganizationPermissionPanel: React.FC<OrganizationPermissionPanelProps> = ({ className }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // 从Redux获取数据
  const users = useSelector(selectUsers);
  const currentUserId = collaborationService.getUserId();
  
  // 本地状态
  const [nodes, setNodes] = useState<OrganizationNode[]>([]);
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [inheritanceStrategy, setInheritanceStrategy] = useState<PermissionInheritanceStrategy>(
    PermissionInheritanceStrategy.ADDITIVE
  );
  const [enableOrganizationPermissions, setEnableOrganizationPermissions] = useState<boolean>(false);
  const [nodeModalVisible, setNodeModalVisible] = useState<boolean>(false);
  const [nodeForm] = Form.useForm();
  const [editingNodeId, setEditingNodeId] = useState<string | null>(null);
  
  // 检查当前用户是否有管理权限
  const canManagePermissions = permissionCheckService.canManagePermissions();
  
  // 初始化组织结构
  useEffect(() => {
    // 这里可以从后端加载组织结构数据
    // 暂时使用模拟数据
    const initialNodes: OrganizationNode[] = [
      {
        id: 'org1',
        name: '示例组织',
        type: OrganizationNodeType.ORGANIZATION,
        children: ['dept1', 'dept2'],
      },
      {
        id: 'dept1',
        name: '研发部门',
        type: OrganizationNodeType.DEPARTMENT,
        parentId: 'org1',
        children: ['team1', 'team2'],
      },
      {
        id: 'dept2',
        name: '设计部门',
        type: OrganizationNodeType.DEPARTMENT,
        parentId: 'org1',
        children: ['team3'],
      },
      {
        id: 'team1',
        name: '前端团队',
        type: OrganizationNodeType.TEAM,
        parentId: 'dept1',
        children: [],
      },
      {
        id: 'team2',
        name: '后端团队',
        type: OrganizationNodeType.TEAM,
        parentId: 'dept1',
        children: [],
      },
      {
        id: 'team3',
        name: 'UI设计团队',
        type: OrganizationNodeType.TEAM,
        parentId: 'dept2',
        children: [],
      },
    ];
    
    setNodes(initialNodes);
    
    // 初始化组织权限服务
    initialNodes.forEach(node => {
      organizationPermissionService.addNode(node);
    });
    
    // 设置默认展开的节点
    setExpandedKeys(['org1', 'dept1', 'dept2']);
  }, []);
  
  // 监听组织权限服务的事件
  useEffect(() => {
    const handleNodeAdded = (node: OrganizationNode) => {
      setNodes(prevNodes => [...prevNodes, node]);
    };
    
    const handleNodeUpdated = (node: OrganizationNode) => {
      setNodes(prevNodes => prevNodes.map(n => n.id === node.id ? node : n));
    };
    
    const handleNodeRemoved = (nodeId: string) => {
      setNodes(prevNodes => prevNodes.filter(n => n.id !== nodeId));
    };
    
    organizationPermissionService.on('nodeAdded', handleNodeAdded);
    organizationPermissionService.on('nodeUpdated', handleNodeUpdated);
    organizationPermissionService.on('nodeRemoved', handleNodeRemoved);
    
    return () => {
      organizationPermissionService.off('nodeAdded', handleNodeAdded);
      organizationPermissionService.off('nodeUpdated', handleNodeUpdated);
      organizationPermissionService.off('nodeRemoved', handleNodeRemoved);
    };
  }, []);
  
  // 处理继承策略变更
  const handleInheritanceStrategyChange = (strategy: PermissionInheritanceStrategy) => {
    setInheritanceStrategy(strategy);
    organizationPermissionService.setInheritanceStrategy(strategy);
  };
  
  // 处理启用/禁用组织权限
  const handleEnableOrganizationPermissions = (enabled: boolean) => {
    setEnableOrganizationPermissions(enabled);
    permissionService.setUseOrganizationPermissions(enabled);
  };
  
  // 获取节点图标
  const getNodeIcon = (node: OrganizationNode) => {
    switch (node.type) {
      case OrganizationNodeType.ORGANIZATION:
        return <ApartmentOutlined />;
      case OrganizationNodeType.DEPARTMENT:
        return <BranchesOutlined />;
      case OrganizationNodeType.TEAM:
        return <TeamOutlined />;
      case OrganizationNodeType.USER:
        return <UserOutlined />;
      default:
        return <InfoCircleOutlined />;
    }
  };
  
  // 构建树数据
  const buildTreeData = () => {
    // 构建节点映射
    const nodeMap = new Map<string, OrganizationNode>();
    nodes.forEach(node => {
      nodeMap.set(node.id, node);
    });
    
    // 构建树数据
    const treeData = nodes
      .filter(node => !node.parentId) // 只取根节点
      .map(node => buildTreeNode(node, nodeMap));
    
    return treeData;
  };
  
  // 构建树节点
  const buildTreeNode = (node: OrganizationNode, nodeMap: Map<string, OrganizationNode>) => {
    const children = node.children?.map(childId => {
      const childNode = nodeMap.get(childId);
      return childNode ? buildTreeNode(childNode, nodeMap) : null;
    }).filter(Boolean);
    
    return {
      key: node.id,
      title: (
        <Space>
          {getNodeIcon(node)}
          <span>{node.name}</span>
          {node.role && (
            <Tag color="blue">{t(`permission.roles.${node.role}`)}</Tag>
          )}
        </Space>
      ),
      children: children?.length ? children : undefined,
    };
  };
  
  // 处理节点选择
  const handleNodeSelect = (selectedKeys: React.Key[]) => {
    if (selectedKeys.length > 0) {
      setSelectedNodeId(selectedKeys[0] as string);
    } else {
      setSelectedNodeId(null);
    }
  };
  
  // 处理节点展开/折叠
  const handleNodeExpand = (expandedKeys: React.Key[]) => {
    setExpandedKeys(expandedKeys as string[]);
  };
  
  // 获取选中的节点
  const getSelectedNode = (): OrganizationNode | null => {
    if (!selectedNodeId) return null;
    return nodes.find(node => node.id === selectedNodeId) || null;
  };
  
  // 处理添加节点
  const handleAddNode = () => {
    setEditingNodeId(null);
    nodeForm.resetFields();
    setNodeModalVisible(true);
  };
  
  // 处理编辑节点
  const handleEditNode = () => {
    const node = getSelectedNode();
    if (!node) return;
    
    setEditingNodeId(node.id);
    nodeForm.setFieldsValue({
      name: node.name,
      type: node.type,
      parentId: node.parentId || '',
      role: node.role || '',
    });
    setNodeModalVisible(true);
  };
  
  // 处理删除节点
  const handleDeleteNode = () => {
    const node = getSelectedNode();
    if (!node) return;
    
    confirm({
      title: t('organization.confirmations.deleteNode'),
      icon: <ExclamationCircleOutlined />,
      content: t('organization.confirmations.deleteNode'),
      onOk() {
        organizationPermissionService.removeNode(node.id);
        setSelectedNodeId(null);
        message.success(t('organization.success.nodeDeleted'));
      },
    });
  };
  
  // 处理节点表单提交
  const handleNodeFormSubmit = (values: any) => {
    if (editingNodeId) {
      // 更新节点
      organizationPermissionService.updateNode(editingNodeId, {
        name: values.name,
        type: values.type,
        parentId: values.parentId || undefined,
        role: values.role || undefined,
      });
      message.success(t('organization.success.nodeUpdated'));
    } else {
      // 添加节点
      const newNode: OrganizationNode = {
        id: `node_${Date.now()}`,
        name: values.name,
        type: values.type,
        parentId: values.parentId || undefined,
        role: values.role || undefined,
        children: [],
      };
      organizationPermissionService.addNode(newNode);
      message.success(t('organization.success.nodeAdded'));
    }
    
    setNodeModalVisible(false);
  };
  
  // 渲染节点详情
  const renderNodeDetails = () => {
    const node = getSelectedNode();
    if (!node) {
      return (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Text type="secondary">{t('organization.selectNode')}</Text>
        </div>
      );
    }
    
    return (
      <div>
        <Card
          title={
            <Space>
              {getNodeIcon(node)}
              <Text strong>{node.name}</Text>
              <Tag color="blue">{t(`organization.nodeTypes.${node.type}`)}</Tag>
            </Space>
          }
          extra={
            <Space>
              <Button
                icon={<EditOutlined />}
                onClick={handleEditNode}
                disabled={!canManagePermissions}
              >
                {t('edit')}
              </Button>
              <Button
                icon={<DeleteOutlined />}
                danger
                onClick={handleDeleteNode}
                disabled={!canManagePermissions}
              >
                {t('delete')}
              </Button>
            </Space>
          }
          style={{ marginBottom: 16 }}
        >
          <div>
            <Text strong>{t('organization.nodeId')}:</Text> {node.id}
          </div>
          {node.parentId && (
            <div>
              <Text strong>{t('organization.parentNode')}:</Text>{' '}
              {nodes.find(n => n.id === node.parentId)?.name || node.parentId}
            </div>
          )}
          {node.role && (
            <div>
              <Text strong>{t('permission.roles.title')}:</Text>{' '}
              {t(`permission.roles.${node.role}`)}
            </div>
          )}
        </Card>
        
        {/* 这里可以添加更多节点详情，如权限列表等 */}
      </div>
    );
  };
  
  return (
    <div className={`organization-permission-panel ${className || ''}`}>
      <Card
        title={
          <Space>
            <ApartmentOutlined />
            {t('organization.title')}
          </Space>
        }
        extra={
          <Space>
            <Select
              value={inheritanceStrategy}
              onChange={handleInheritanceStrategyChange}
              style={{ width: 150 }}
              disabled={!canManagePermissions}
            >
              <Option value={PermissionInheritanceStrategy.STRICT}>{t('organization.inheritanceStrategies.strict')}</Option>
              <Option value={PermissionInheritanceStrategy.ADDITIVE}>{t('organization.inheritanceStrategies.additive')}</Option>
              <Option value={PermissionInheritanceStrategy.OVERRIDE}>{t('organization.inheritanceStrategies.override')}</Option>
              <Option value={PermissionInheritanceStrategy.CUSTOM}>{t('organization.inheritanceStrategies.custom')}</Option>
            </Select>
            <Switch
              checkedChildren={t('enabled')}
              unCheckedChildren={t('disabled')}
              checked={enableOrganizationPermissions}
              onChange={handleEnableOrganizationPermissions}
              disabled={!canManagePermissions}
            />
          </Space>
        }
      >
        <div style={{ display: 'flex' }}>
          <div style={{ width: '40%', borderRight: '1px solid #f0f0f0', paddingRight: 16 }}>
            <div style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddNode}
                disabled={!canManagePermissions}
              >
                {t('organization.addNode')}
              </Button>
            </div>
            <DirectoryTree
              treeData={buildTreeData()}
              selectedKeys={selectedNodeId ? [selectedNodeId] : []}
              expandedKeys={expandedKeys}
              onSelect={handleNodeSelect}
              onExpand={handleNodeExpand}
            />
          </div>
          <div style={{ width: '60%', paddingLeft: 16 }}>
            {renderNodeDetails()}
          </div>
        </div>
        
        <Modal
          title={editingNodeId ? t('organization.editNode') : t('organization.addNode')}
          open={nodeModalVisible}
          onCancel={() => setNodeModalVisible(false)}
          footer={null}
        >
          <Form
            form={nodeForm}
            layout="vertical"
            onFinish={handleNodeFormSubmit}
          >
            <Form.Item
              name="name"
              label={t('organization.nodeName')}
              rules={[{ required: true, message: t('organization.errors.nameRequired') }]}
            >
              <Input />
            </Form.Item>
            
            <Form.Item
              name="type"
              label={t('organization.nodeType')}
              rules={[{ required: true, message: t('organization.errors.typeRequired') }]}
            >
              <Select>
                <Option value={OrganizationNodeType.ORGANIZATION}>{t('organization.nodeTypes.organization')}</Option>
                <Option value={OrganizationNodeType.DEPARTMENT}>{t('organization.nodeTypes.department')}</Option>
                <Option value={OrganizationNodeType.TEAM}>{t('organization.nodeTypes.team')}</Option>
                <Option value={OrganizationNodeType.USER}>{t('organization.nodeTypes.user')}</Option>
              </Select>
            </Form.Item>
            
            <Form.Item
              name="parentId"
              label={t('organization.parentNode')}
            >
              <Select allowClear>
                {nodes.map(node => (
                  <Option key={node.id} value={node.id}>
                    {node.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            
            <Form.Item
              name="role"
              label={t('permission.roles.title')}
            >
              <Select allowClear>
                <Option value={CollaborationRole.VIEWER}>{t('permission.roles.viewer')}</Option>
                <Option value={CollaborationRole.EDITOR}>{t('permission.roles.editor')}</Option>
                <Option value={CollaborationRole.ADMIN}>{t('permission.roles.admin')}</Option>
                <Option value={CollaborationRole.OWNER}>{t('permission.roles.owner')}</Option>
              </Select>
            </Form.Item>
            
            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  {t('save')}
                </Button>
                <Button onClick={() => setNodeModalVisible(false)}>
                  {t('cancel')}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    </div>
  );
};

export default OrganizationPermissionPanel;
