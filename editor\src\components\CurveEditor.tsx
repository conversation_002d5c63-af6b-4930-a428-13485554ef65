/**
 * 曲线编辑器组件
 * 提供直观的动画曲线编辑功能
 */
import React, { useState, useEffect, useRef } from 'react';
import { Button, Select, Input, Switch, Tooltip, message, Space, Divider, Card, Tabs, Radio } from 'antd';
import { PlusOutlined, DeleteOutlined, SaveOutlined, UndoOutlined, RedoOutlined, CopyOutlined, ScissorOutlined, SettingOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store/rootReducer';
import './CurveEditor.css';

const { Option } = Select;
const { TabPane } = Tabs;

/**
 * 曲线类型
 */
export enum CurveType {
  /** 线性 */
  LINEAR = 'linear',
  /** 缓入 */
  EASE_IN = 'ease_in',
  /** 缓出 */
  EASE_OUT = 'ease_out',
  /** 缓入缓出 */
  EASE_IN_OUT = 'ease_in_out',
  /** 弹性 */
  ELASTIC = 'elastic',
  /** 弹跳 */
  BOUNCE = 'bounce',
  /** 贝塞尔 */
  BEZIER = 'bezier',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 关键帧点
 */
interface KeyPoint {
  /** 时间 */
  time: number;
  /** 值 */
  value: number;
  /** 左侧控制点 */
  leftHandle?: { x: number, y: number };
  /** 右侧控制点 */
  rightHandle?: { x: number, y: number };
  /** 是否自动平滑 */
  autoSmooth?: boolean;
}

/**
 * 曲线数据
 */
interface CurveData {
  /** 曲线ID */
  id: string;
  /** 曲线名称 */
  name: string;
  /** 曲线类型 */
  type: CurveType;
  /** 关键帧点 */
  keyPoints: KeyPoint[];
  /** 颜色 */
  color: string;
  /** 是否可见 */
  visible: boolean;
  /** 是否锁定 */
  locked: boolean;
  /** 自定义属性 */
  customProperties?: any;
}

/**
 * 曲线编辑器属性
 */
interface CurveEditorProps {
  /** 实体ID */
  entityId?: string;
  /** 是否可编辑 */
  editable?: boolean;
  /** 组件宽度 */
  width?: number | string;
  /** 组件高度 */
  height?: number | string;
  /** 最小时间 */
  minTime?: number;
  /** 最大时间 */
  maxTime?: number;
  /** 最小值 */
  minValue?: number;
  /** 最大值 */
  maxValue?: number;
  /** 网格大小 */
  gridSize?: number;
  /** 是否显示网格 */
  showGrid?: boolean;
  /** 是否显示值标签 */
  showValueLabels?: boolean;
  /** 是否显示时间标签 */
  showTimeLabels?: boolean;
  /** 是否自动缩放 */
  autoScale?: boolean;
  /** 曲线变更回调 */
  onCurveChange?: (curveId: string, properties: any) => void;
  /** 关键帧变更回调 */
  onKeyPointChange?: (curveId: string, keyPointIndex: number, properties: any) => void;
  /** 关键帧添加回调 */
  onKeyPointAdd?: (curveId: string, keyPoint: KeyPoint) => void;
  /** 关键帧删除回调 */
  onKeyPointDelete?: (curveId: string, keyPointIndex: number) => void;
  /** 曲线添加回调 */
  onCurveAdd?: (curve: CurveData) => void;
  /** 曲线删除回调 */
  onCurveDelete?: (curveId: string) => void;
  /** 保存回调 */
  onSave?: () => void;
}

/**
 * 曲线编辑器组件
 */
export const CurveEditor: React.FC<CurveEditorProps> = ({
  entityId,
  editable = true,
  width = '100%',
  height = '100%',
  minTime = 0,
  maxTime = 10,
  minValue = 0,
  maxValue = 1,
  gridSize = 0.1,
  showGrid = true,
  showValueLabels = true,
  showTimeLabels = true,
  autoScale = false,
  onCurveChange,
  onKeyPointChange,
  onKeyPointAdd,
  onKeyPointDelete,
  onCurveAdd,
  onCurveDelete,
  onSave
}) => {
  // 状态
  const [curves, setCurves] = useState<CurveData[]>([]);
  const [selectedCurveId, setSelectedCurveId] = useState<string | null>(null);
  const [selectedKeyPointIndex, setSelectedKeyPointIndex] = useState<number | null>(null);
  const [isAddingCurve, setIsAddingCurve] = useState<boolean>(false);
  const [newCurveType, setNewCurveType] = useState<CurveType>(CurveType.LINEAR);
  const [newCurveName, setNewCurveName] = useState<string>('');
  const [viewMinTime, setViewMinTime] = useState<number>(minTime);
  const [viewMaxTime, setViewMaxTime] = useState<number>(maxTime);
  const [viewMinValue, setViewMinValue] = useState<number>(minValue);
  const [viewMaxValue, setViewMaxValue] = useState<number>(maxValue);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragStartPos, setDragStartPos] = useState<{ x: number, y: number } | null>(null);
  const [dragTarget, setDragTarget] = useState<'keypoint' | 'handle_left' | 'handle_right' | 'canvas' | null>(null);
  const [undoStack, setUndoStack] = useState<CurveData[][]>([]);
  const [redoStack, setRedoStack] = useState<CurveData[][]>([]);
  
  // 引用
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Redux
  const dispatch = useDispatch();
  const curveState = useSelector((state: RootState) => state.animationCurves);
  
  // 曲线类型列表
  const curveTypes = [
    { value: CurveType.LINEAR, label: '线性' },
    { value: CurveType.EASE_IN, label: '缓入' },
    { value: CurveType.EASE_OUT, label: '缓出' },
    { value: CurveType.EASE_IN_OUT, label: '缓入缓出' },
    { value: CurveType.ELASTIC, label: '弹性' },
    { value: CurveType.BOUNCE, label: '弹跳' },
    { value: CurveType.BEZIER, label: '贝塞尔' },
    { value: CurveType.CUSTOM, label: '自定义' }
  ];
  
  // 模拟数据
  useEffect(() => {
    // 这里应该从Redux或API获取数据
    // 这里使用模拟数据
    const mockCurves: CurveData[] = [
      {
        id: 'curve_1',
        name: '线性曲线',
        type: CurveType.LINEAR,
        keyPoints: [
          { time: 0, value: 0 },
          { time: 5, value: 0.5 },
          { time: 10, value: 1 }
        ],
        color: '#ff0000',
        visible: true,
        locked: false
      },
      {
        id: 'curve_2',
        name: '缓入曲线',
        type: CurveType.EASE_IN,
        keyPoints: [
          { time: 0, value: 0 },
          { time: 5, value: 0.2, leftHandle: { x: 4, y: 0.1 }, rightHandle: { x: 6, y: 0.3 } },
          { time: 10, value: 1 }
        ],
        color: '#00ff00',
        visible: true,
        locked: false
      },
      {
        id: 'curve_3',
        name: '弹性曲线',
        type: CurveType.ELASTIC,
        keyPoints: [
          { time: 0, value: 0 },
          { time: 2.5, value: 0.8 },
          { time: 5, value: 0.6 },
          { time: 7.5, value: 0.9 },
          { time: 10, value: 1 }
        ],
        color: '#0000ff',
        visible: true,
        locked: false
      }
    ];
    
    setCurves(mockCurves);
  }, []);
  
  // 初始化画布
  useEffect(() => {
    drawCanvas();
  }, [curves, selectedCurveId, selectedKeyPointIndex, viewMinTime, viewMaxTime, viewMinValue, viewMaxValue, showGrid, showValueLabels, showTimeLabels]);
  
  // 绘制画布
  const drawCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // 获取画布尺寸
    const width = canvas.width;
    const height = canvas.height;
    
    // 清空画布
    ctx.clearRect(0, 0, width, height);
    
    // 绘制网格
    if (showGrid) {
      drawGrid(ctx, width, height);
    }
    
    // 绘制坐标轴
    drawAxes(ctx, width, height);
    
    // 绘制曲线
    curves.forEach(curve => {
      if (curve.visible) {
        drawCurve(ctx, curve, width, height);
      }
    });
    
    // 绘制选中的关键帧点
    if (selectedCurveId && selectedKeyPointIndex !== null) {
      const curve = curves.find(c => c.id === selectedCurveId);
      if (curve && curve.keyPoints[selectedKeyPointIndex]) {
        drawSelectedKeyPoint(ctx, curve, selectedKeyPointIndex, width, height);
      }
    }
  };
  
  // 绘制网格
  const drawGrid = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    ctx.strokeStyle = '#e0e0e0';
    ctx.lineWidth = 0.5;
    
    // 计算网格间距
    const timeRange = viewMaxTime - viewMinTime;
    const valueRange = viewMaxValue - viewMinValue;
    const timeStep = gridSize;
    const valueStep = gridSize;
    
    // 绘制垂直网格线
    for (let t = Math.ceil(viewMinTime / timeStep) * timeStep; t <= viewMaxTime; t += timeStep) {
      const x = ((t - viewMinTime) / timeRange) * width;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
      
      // 绘制时间标签
      if (showTimeLabels) {
        ctx.fillStyle = '#666666';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(t.toFixed(1), x, height - 5);
      }
    }
    
    // 绘制水平网格线
    for (let v = Math.ceil(viewMinValue / valueStep) * valueStep; v <= viewMaxValue; v += valueStep) {
      const y = height - ((v - viewMinValue) / valueRange) * height;
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
      
      // 绘制值标签
      if (showValueLabels) {
        ctx.fillStyle = '#666666';
        ctx.font = '10px Arial';
        ctx.textAlign = 'right';
        ctx.fillText(v.toFixed(1), 20, y + 4);
      }
    }
  };
  
  // 绘制坐标轴
  const drawAxes = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    
    // X轴
    ctx.beginPath();
    const yAxisPos = height - ((0 - viewMinValue) / (viewMaxValue - viewMinValue)) * height;
    ctx.moveTo(0, yAxisPos);
    ctx.lineTo(width, yAxisPos);
    ctx.stroke();
    
    // Y轴
    ctx.beginPath();
    const xAxisPos = ((0 - viewMinTime) / (viewMaxTime - viewMinTime)) * width;
    ctx.moveTo(xAxisPos, 0);
    ctx.lineTo(xAxisPos, height);
    ctx.stroke();
  };
  
  // 绘制曲线
  const drawCurve = (ctx: CanvasRenderingContext2D, curve: CurveData, width: number, height: number) => {
    if (curve.keyPoints.length === 0) return;
    
    ctx.strokeStyle = curve.color;
    ctx.lineWidth = curve.id === selectedCurveId ? 2 : 1;
    
    // 时间和值的范围
    const timeRange = viewMaxTime - viewMinTime;
    const valueRange = viewMaxValue - viewMinValue;
    
    // 绘制曲线
    ctx.beginPath();
    
    // 根据曲线类型绘制
    switch (curve.type) {
      case CurveType.LINEAR:
        // 线性插值
        curve.keyPoints.forEach((point, index) => {
          const x = ((point.time - viewMinTime) / timeRange) * width;
          const y = height - ((point.value - viewMinValue) / valueRange) * height;
          
          if (index === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        });
        break;
        
      case CurveType.BEZIER:
      case CurveType.CUSTOM:
        // 贝塞尔曲线
        for (let i = 0; i < curve.keyPoints.length - 1; i++) {
          const p1 = curve.keyPoints[i];
          const p2 = curve.keyPoints[i + 1];
          
          const x1 = ((p1.time - viewMinTime) / timeRange) * width;
          const y1 = height - ((p1.value - viewMinValue) / valueRange) * height;
          const x2 = ((p2.time - viewMinTime) / timeRange) * width;
          const y2 = height - ((p2.value - viewMinValue) / valueRange) * height;
          
          if (i === 0) {
            ctx.moveTo(x1, y1);
          }
          
          if (p1.rightHandle && p2.leftHandle) {
            // 使用控制点
            const cp1x = ((p1.time + p1.rightHandle.x - viewMinTime) / timeRange) * width;
            const cp1y = height - ((p1.value + p1.rightHandle.y - viewMinValue) / valueRange) * height;
            const cp2x = ((p2.time + p2.leftHandle.x - viewMinTime) / timeRange) * width;
            const cp2y = height - ((p2.value + p2.leftHandle.y - viewMinValue) / valueRange) * height;
            
            ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x2, y2);
          } else {
            // 没有控制点，使用直线
            ctx.lineTo(x2, y2);
          }
        }
        break;
        
      default:
        // 其他曲线类型使用预设函数
        const steps = 100;
        const stepSize = timeRange / steps;
        
        for (let i = 0; i <= steps; i++) {
          const t = viewMinTime + i * stepSize;
          const v = evaluateCurve(curve, t);
          
          const x = (i / steps) * width;
          const y = height - ((v - viewMinValue) / valueRange) * height;
          
          if (i === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        }
        break;
    }
    
    ctx.stroke();
    
    // 绘制关键帧点
    curve.keyPoints.forEach((point, index) => {
      const x = ((point.time - viewMinTime) / timeRange) * width;
      const y = height - ((point.value - viewMinValue) / valueRange) * height;
      
      // 绘制点
      ctx.fillStyle = curve.id === selectedCurveId ? curve.color : '#ffffff';
      ctx.strokeStyle = curve.color;
      ctx.lineWidth = 1;
      
      ctx.beginPath();
      ctx.arc(x, y, curve.id === selectedCurveId && index === selectedKeyPointIndex ? 6 : 4, 0, Math.PI * 2);
      ctx.fill();
      ctx.stroke();
      
      // 如果是选中的曲线，绘制控制点和手柄
      if (curve.id === selectedCurveId && (point.leftHandle || point.rightHandle)) {
        // 左侧控制点和手柄
        if (point.leftHandle) {
          const handleX = ((point.time + point.leftHandle.x - viewMinTime) / timeRange) * width;
          const handleY = height - ((point.value + point.leftHandle.y - viewMinValue) / valueRange) * height;
          
          // 手柄线
          ctx.strokeStyle = '#999999';
          ctx.beginPath();
          ctx.moveTo(x, y);
          ctx.lineTo(handleX, handleY);
          ctx.stroke();
          
          // 控制点
          ctx.fillStyle = '#ffffff';
          ctx.strokeStyle = '#999999';
          ctx.beginPath();
          ctx.arc(handleX, handleY, 3, 0, Math.PI * 2);
          ctx.fill();
          ctx.stroke();
        }
        
        // 右侧控制点和手柄
        if (point.rightHandle) {
          const handleX = ((point.time + point.rightHandle.x - viewMinTime) / timeRange) * width;
          const handleY = height - ((point.value + point.rightHandle.y - viewMinValue) / valueRange) * height;
          
          // 手柄线
          ctx.strokeStyle = '#999999';
          ctx.beginPath();
          ctx.moveTo(x, y);
          ctx.lineTo(handleX, handleY);
          ctx.stroke();
          
          // 控制点
          ctx.fillStyle = '#ffffff';
          ctx.strokeStyle = '#999999';
          ctx.beginPath();
          ctx.arc(handleX, handleY, 3, 0, Math.PI * 2);
          ctx.fill();
          ctx.stroke();
        }
      }
    });
  };
  
  // 绘制选中的关键帧点
  const drawSelectedKeyPoint = (ctx: CanvasRenderingContext2D, curve: CurveData, keyPointIndex: number, width: number, height: number) => {
    const point = curve.keyPoints[keyPointIndex];
    if (!point) return;
    
    // 时间和值的范围
    const timeRange = viewMaxTime - viewMinTime;
    const valueRange = viewMaxValue - viewMinValue;
    
    const x = ((point.time - viewMinTime) / timeRange) * width;
    const y = height - ((point.value - viewMinValue) / valueRange) * height;
    
    // 绘制选中点的高亮
    ctx.fillStyle = 'rgba(255, 255, 0, 0.3)';
    ctx.strokeStyle = '#ffcc00';
    ctx.lineWidth = 2;
    
    ctx.beginPath();
    ctx.arc(x, y, 8, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
  };
  
  // 评估曲线在给定时间的值
  const evaluateCurve = (curve: CurveData, time: number): number => {
    // 如果时间超出范围，返回边界值
    if (time <= curve.keyPoints[0].time) {
      return curve.keyPoints[0].value;
    }
    
    if (time >= curve.keyPoints[curve.keyPoints.length - 1].time) {
      return curve.keyPoints[curve.keyPoints.length - 1].value;
    }
    
    // 找到时间所在的区间
    let i = 0;
    while (i < curve.keyPoints.length - 1 && time > curve.keyPoints[i + 1].time) {
      i++;
    }
    
    const p1 = curve.keyPoints[i];
    const p2 = curve.keyPoints[i + 1];
    
    // 计算区间内的插值因子
    const t = (time - p1.time) / (p2.time - p1.time);
    
    // 根据曲线类型计算插值
    switch (curve.type) {
      case CurveType.LINEAR:
        // 线性插值
        return p1.value + (p2.value - p1.value) * t;
        
      case CurveType.EASE_IN:
        // 缓入插值
        return p1.value + (p2.value - p1.value) * (t * t);
        
      case CurveType.EASE_OUT:
        // 缓出插值
        return p1.value + (p2.value - p1.value) * (1 - (1 - t) * (1 - t));
        
      case CurveType.EASE_IN_OUT:
        // 缓入缓出插值
        return p1.value + (p2.value - p1.value) * (t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2);
        
      case CurveType.ELASTIC:
        // 弹性插值
        const c4 = (2 * Math.PI) / 3;
        return p1.value + (p2.value - p1.value) * (
          t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1
        );
        
      case CurveType.BOUNCE:
        // 弹跳插值
        const n1 = 7.5625;
        const d1 = 2.75;
        let newT = t;
        
        if (newT < 1 / d1) {
          return p1.value + (p2.value - p1.value) * (n1 * newT * newT);
        } else if (newT < 2 / d1) {
          return p1.value + (p2.value - p1.value) * (n1 * (newT -= 1.5 / d1) * newT + 0.75);
        } else if (newT < 2.5 / d1) {
          return p1.value + (p2.value - p1.value) * (n1 * (newT -= 2.25 / d1) * newT + 0.9375);
        } else {
          return p1.value + (p2.value - p1.value) * (n1 * (newT -= 2.625 / d1) * newT + 0.984375);
        }
        
      case CurveType.BEZIER:
      case CurveType.CUSTOM:
        // 贝塞尔插值
        if (p1.rightHandle && p2.leftHandle) {
          // 使用控制点计算贝塞尔曲线
          const cp1 = { x: p1.time + p1.rightHandle.x, y: p1.value + p1.rightHandle.y };
          const cp2 = { x: p2.time + p2.leftHandle.x, y: p2.value + p2.leftHandle.y };
          
          // 三次贝塞尔曲线插值
          return cubicBezier(p1.value, cp1.y, cp2.y, p2.value, t);
        } else {
          // 没有控制点，使用线性插值
          return p1.value + (p2.value - p1.value) * t;
        }
        
      default:
        // 默认使用线性插值
        return p1.value + (p2.value - p1.value) * t;
    }
  };
  
  // 三次贝塞尔曲线插值
  const cubicBezier = (p0: number, p1: number, p2: number, p3: number, t: number): number => {
    const mt = 1 - t;
    return mt * mt * mt * p0 + 3 * mt * mt * t * p1 + 3 * mt * t * t * p2 + t * t * t * p3;
  };
  
  return (
    <div className="curve-editor" style={{ width, height }} ref={containerRef}>
      <div className="curve-editor-header">
        <Space>
          <Select
            placeholder="选择曲线"
            value={selectedCurveId || undefined}
            onChange={setSelectedCurveId}
            style={{ width: 150 }}
          >
            {curves.map(curve => (
              <Option key={curve.id} value={curve.id}>{curve.name}</Option>
            ))}
          </Select>
          
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsAddingCurve(!isAddingCurve)}
            disabled={!editable}
          >
            {isAddingCurve ? '取消' : '添加曲线'}
          </Button>
          
          {isAddingCurve && (
            <>
              <Input
                placeholder="曲线名称"
                value={newCurveName}
                onChange={e => setNewCurveName(e.target.value)}
                style={{ width: 150 }}
              />
              
              <Select
                placeholder="曲线类型"
                value={newCurveType}
                onChange={setNewCurveType}
                style={{ width: 120 }}
              >
                {curveTypes.map(type => (
                  <Option key={type.value} value={type.value}>{type.label}</Option>
                ))}
              </Select>
              
              <Button
                type="primary"
                onClick={() => {
                  // 添加新曲线
                  if (!newCurveName) {
                    message.error('请输入曲线名称');
                    return;
                  }
                  
                  const newCurve: CurveData = {
                    id: `curve_${Date.now()}`,
                    name: newCurveName,
                    type: newCurveType,
                    keyPoints: [
                      { time: 0, value: 0 },
                      { time: 10, value: 1 }
                    ],
                    color: `#${Math.floor(Math.random() * 16777215).toString(16)}`,
                    visible: true,
                    locked: false
                  };
                  
                  setCurves([...curves, newCurve]);
                  setSelectedCurveId(newCurve.id);
                  setIsAddingCurve(false);
                  setNewCurveName('');
                  
                  if (onCurveAdd) {
                    onCurveAdd(newCurve);
                  }
                  
                  message.success('添加曲线成功');
                }}
              >
                确认添加
              </Button>
            </>
          )}
          
          <Button
            icon={<SaveOutlined />}
            onClick={() => {
              if (onSave) {
                onSave();
              }
              message.success('保存成功');
            }}
            disabled={!editable}
          >
            保存
          </Button>
        </Space>
      </div>
      
      <div className="curve-editor-content">
        <canvas
          ref={canvasRef}
          width={800}
          height={400}
          className="curve-canvas"
        />
      </div>
    </div>
  );
};
