# 面部动画系统

面部动画系统用于控制角色的面部表情和口型同步，支持基于音频的实时口型生成、表情混合和AI驱动的面部动画。

## 主要功能

- 面部表情控制：精确控制角色的面部表情
- 实时口型同步：基于音频实时生成口型动画
- 面部动画编辑器：直观的界面创建和编辑面部动画
- AI驱动的面部动画合成：基于文本描述生成面部动画
- 物理驱动的面部动画：基于物理模拟驱动面部动画
- GPU加速：使用GPU加速面部动画计算，提高性能
- 优化的音频分析：高效的音频分析算法，减少CPU使用率

## 基础用法

### 创建面部动画系统

```typescript
import { FacialAnimationSystem, FacialAnimationComponent, FacialExpressionType, VisemeType } from '../../animation';

// 创建面部动画系统
const facialAnimationSystem = new FacialAnimationSystem(world, {
  debug: true,
  autoDetectAudio: true,
  useWebcam: false
});

// 添加系统到世界
world.addSystem(facialAnimationSystem);

// 创建面部动画组件
const facialAnimation = facialAnimationSystem.createFacialAnimation(characterEntity);
```

### 控制表情

```typescript
// 设置表情
facialAnimation.setExpression(FacialExpressionType.HAPPY, 1.0, 0.5);

// 混合表情
facialAnimation.setExpression(FacialExpressionType.SURPRISED, 0.5, 0.3);

// 重置表情
facialAnimation.resetExpression();
```

### 控制口型

```typescript
// 设置口型
facialAnimation.setViseme(VisemeType.AA, 1.0, 0.2);

// 重置口型
facialAnimation.resetViseme();
```

## 口型同步系统

口型同步系统可以根据音频实时生成口型动画。

### 创建口型同步系统

```typescript
import { LipSyncSystem, LipSyncComponent } from '../../animation';

// 创建口型同步系统
const lipSyncSystem = new LipSyncSystem(world, {
  debug: true,
  fftSize: 1024,
  volumeThreshold: 0.01
});

// 添加系统到世界
world.addSystem(lipSyncSystem);

// 创建口型同步组件
const lipSync = lipSyncSystem.createLipSync(characterEntity);
```

### 启动口型同步

```typescript
// 创建音频元素
const audioElement = new Audio('audio/speech.mp3');

// 启动口型同步跟踪
lipSyncSystem.startTracking(audioElement);

// 播放音频
audioElement.play();

// 停止口型同步跟踪
lipSyncSystem.stopTracking();
```

## 面部动画编辑器

面部动画编辑器用于创建和编辑面部动画序列。它提供了直观的界面，让用户可以轻松创建表情和口型动画，并支持时间轴编辑、关键帧编辑和曲线编辑等功能。

详细信息请参考 [面部动画编辑器](FacialAnimationEditor.md) 文档。

### 创建面部动画编辑器

```typescript
import { FacialAnimationEditorSystem, FacialAnimationEditorComponent } from '../../animation';

// 创建面部动画编辑器系统
const editorSystem = new FacialAnimationEditorSystem(world, {
  debug: true,
  defaultFrameRate: 30
});

// 添加系统到世界
world.addSystem(editorSystem);

// 创建面部动画编辑器组件
const editor = editorSystem.createEditor(characterEntity);
```

### 创建和编辑动画

```typescript
// 创建动画片段
const clip = editor.createClip('happy', 2.0, true);

// 添加关键帧
editor.addKeyframe(0.0, {
  expression: FacialExpressionType.NEUTRAL,
  expressionWeight: 0.0
});

editor.addKeyframe(1.0, {
  expression: FacialExpressionType.HAPPY,
  expressionWeight: 1.0
});

editor.addKeyframe(2.0, {
  expression: FacialExpressionType.NEUTRAL,
  expressionWeight: 0.0
});

// 播放动画
editor.setCurrentClip('happy');
editor.play();
```

### 导出和导入动画

```typescript
// 导出动画为JSON
const json = editorSystem.exportClipToJSON(characterEntity, 'happy');

// 导入动画
const importedClip = editorSystem.importClipFromJSON(characterEntity, json);
```

## AI动画合成

AI动画合成系统可以基于文本描述生成面部动画。

### 创建AI动画合成系统

```typescript
import { AIAnimationSynthesisSystem } from '../../animation';

// 创建AI动画合成系统
const aiSystem = new AIAnimationSynthesisSystem(world, {
  debug: true,
  useLocalModel: true
});

// 添加系统到世界
world.addSystem(aiSystem);
```

### 生成面部动画

```typescript
// 生成面部动画
const requestId = aiSystem.generateFacialAnimation(
  characterEntity,
  '开心地说话',
  5.0,
  {
    loop: true,
    style: '卡通',
    intensity: 0.8
  }
);

// 监听生成完成事件
aiSystem.addEventListener('generationComplete', (data) => {
  if (data.result.id === requestId) {
    console.log('AI动画生成完成:', data.result);

    // 获取生成的动画片段
    const clip = data.result.clip;

    // 使用生成的动画
    // ...
  }
});
```

## 物理驱动动画

物理驱动动画系统可以基于物理模拟驱动面部动画，例如模拟肌肉、皮肤和软组织的物理特性。

### 创建物理驱动动画系统

```typescript
import { PhysicsBasedAnimationSystem, PhysicsBasedAnimationComponent } from '../../animation';

// 创建物理驱动动画系统
const physicsSystem = new PhysicsBasedAnimationSystem(world, {
  debug: true,
  useSubsteps: true,
  substeps: 2
});

// 添加系统到世界
world.addSystem(physicsSystem);

// 创建物理驱动动画组件
const physicsAnimation = physicsSystem.createPhysicsBasedAnimation(characterEntity);
```

### 添加物理骨骼和约束

```typescript
// 添加物理骨骼
physicsAnimation.addPhysicsBone({
  name: 'jaw',
  mass: 0.1,
  radius: 0.05,
  length: 0.1,
  damping: 0.5,
  isKinematic: false
});

// 添加物理约束
physicsAnimation.addPhysicsConstraint({
  name: 'jaw_hinge',
  boneA: 'head',
  boneB: 'jaw',
  type: 'hinge',
  localAxisA: new THREE.Vector3(1, 0, 0),
  localAxisB: new THREE.Vector3(1, 0, 0),
  minAngle: -Math.PI / 6,
  maxAngle: 0
});

// 初始化物理对象
physicsSystem.initPhysicsObjects(characterEntity);
```

## 高级功能

### 事件监听

```typescript
// 监听表情变化事件
facialAnimation.addEventListener('expressionChange', (data) => {
  console.log(`表情变化: ${data.expression}, 权重: ${data.weight}`);
});

// 监听口型变化事件
facialAnimation.addEventListener('visemeChange', (data) => {
  console.log(`口型变化: ${data.viseme}, 权重: ${data.weight}`);
});
```

### 自定义混合

```typescript
// 获取表情混合映射
const expressionBlendMap = facialAnimation.getExpressionBlendMap();

// 获取口型混合映射
const visemeBlendMap = facialAnimation.getVisemeBlendMap();

// 应用自定义混合
for (const [expression, weight] of expressionBlendMap.entries()) {
  // 应用到模型的混合形状
  // ...
}
```

### 与其他系统集成

```typescript
// 与动画状态机集成
stateMachine.addState('talking', {
  onEnter: () => {
    lipSyncSystem.startTracking(audioElement);
    audioElement.play();
  },
  onExit: () => {
    lipSyncSystem.stopTracking();
    audioElement.pause();
  },
  update: (deltaTime) => {
    // 更新逻辑
  }
});
```

## 示例

完整的面部动画示例可以在 `examples/animation/FacialAnimationExample.ts` 中找到。

```typescript
// 创建示例
const example = new FacialAnimationExample();

// 启动示例
example.start();
```

## 性能优化

### GPU加速

使用GPU加速可以显著提高面部动画的性能，特别是对于复杂模型和大量角色的场景。

```typescript
import { GPUFacialAnimationSystem } from '../../animation';

// 创建GPU面部动画系统
const gpuSystem = new GPUFacialAnimationSystem(world, {
  debug: true,
  useComputeShader: false,
  maxBlendShapes: 32,
  textureSize: 16
});

// 添加系统到世界
world.addSystem(gpuSystem);

// 创建GPU面部动画组件
const gpuComponent = gpuSystem.createGPUFacialAnimation(characterEntity, skinnedMesh);
```

详细信息请参考 [GPU加速面部动画](GPUFacialAnimation.md) 文档。

### 优化音频分析

优化音频分析可以减少CPU使用率，提高口型同步的性能。

```typescript
// 创建口型同步系统
const lipSyncSystem = new LipSyncSystem(world, {
  debug: true,
  fftSize: 512,            // 降低FFT大小
  analysisInterval: 100,   // 设置分析间隔（毫秒）
  useWorker: true,         // 使用工作线程
  numFrequencyBands: 8     // 设置频率带数量
});
```

### 其他优化技巧

- 使用适当的FFT大小和更新频率
- 对于大量角色，考虑使用动画实例化
- 使用GPU蒙皮加速面部动画
- 根据距离和可见性调整更新频率
- 使用LOD（细节层次）技术，远处的角色使用简化的面部动画
- 对不可见的角色暂停面部动画更新
- 使用动画缓存，避免重复计算
