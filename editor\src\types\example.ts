/**
 * 示例项目类型定义
 */

/**
 * 示例项目类别
 */
export type ExampleCategory = 
  | 'basic'
  | 'material'
  | 'animation'
  | 'physics'
  | 'visualscript'
  | 'performance'
  | 'collaboration'
  | 'tutorial';

/**
 * 示例项目难度级别
 */
export type ExampleDifficulty = 
  | 'beginner'
  | 'intermediate'
  | 'advanced'
  | 'expert';

/**
 * 示例项目特性
 */
export interface ExampleFeature {
  title: string;
  description: string;
}

/**
 * 示例项目文件
 */
export interface ExampleFile {
  name: string;
  size: string;
  updatedAt: string;
}

/**
 * 示例项目教程
 */
export interface ExampleTutorial {
  title: string;
  description: string;
  url?: string;
}

/**
 * 示例项目要求
 */
export interface ExampleRequirements {
  engineVersion: string;
  editorVersion: string;
  dependencies: string[];
}

/**
 * 示例项目相关项目
 */
export interface RelatedExample {
  id: string;
  title: string;
  description: string;
  previewImage: string;
}

/**
 * 示例项目
 */
export interface Example {
  id: string;
  title: string;
  description: string;
  category: ExampleCategory;
  tags: string[];
  previewImage: string;
  images: string[];
  author: string;
  createdAt: string;
  updatedAt: string;
  popularity: number;
  difficulty?: ExampleDifficulty;
  content?: string;
  features?: ExampleFeature[];
  files?: ExampleFile[];
  tutorials?: ExampleTutorial[];
  requirements?: ExampleRequirements;
  relatedExamples?: RelatedExample[];
  previewUrl?: string;
  favorited?: boolean;
}
