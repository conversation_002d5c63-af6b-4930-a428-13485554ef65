/**
 * 场景状态切片测试
 */
import sceneReducer, {
  setCurrentScene,
  selectEntity,
  toggleNodeExpansion,
  addEntity,
  removeEntity,
  updateEntity,
} from './sceneSlice';

// 模拟场景数据
const mockScene = {
  id: 'scene1',
  name: '测试场景',
  entities: [
    {
      id: 'entity1',
      name: '实体1',
      type: 'mesh',
      children: [
        {
          id: 'entity3',
          name: '实体3',
          type: 'mesh',
          children: [],
        },
      ],
    },
    {
      id: 'entity2',
      name: '实体2',
      type: 'light',
      children: [],
    },
  ],
};

describe('场景状态切片', () => {
  // 初始状态测试
  it('应该返回初始状态', () => {
    const initialState = sceneReducer(undefined, { type: 'unknown' });
    expect(initialState).toEqual({
      currentScene: null,
      selectedEntityId: null,
      expandedNodes: {},
      isLoading: false,
      error: null,
    });
  });

  // 设置当前场景测试
  it('应该处理setCurrentScene', () => {
    const previousState = {
      currentScene: null,
      selectedEntityId: null,
      expandedNodes: {},
      isLoading: false,
      error: null,
    };

    const nextState = sceneReducer(previousState, setCurrentScene(mockScene));

    expect(nextState.currentScene).toEqual(mockScene);
    expect(nextState.selectedEntityId).toBeNull();
    expect(nextState.expandedNodes).toEqual({});
  });

  // 选择实体测试
  it('应该处理selectEntity', () => {
    const previousState = {
      currentScene: mockScene,
      selectedEntityId: null,
      expandedNodes: {},
      isLoading: false,
      error: null,
    };

    const nextState = sceneReducer(previousState, selectEntity('entity2'));

    expect(nextState.selectedEntityId).toEqual('entity2');
  });

  // 展开/折叠节点测试
  it('应该处理toggleNodeExpansion', () => {
    const previousState = {
      currentScene: mockScene,
      selectedEntityId: null,
      expandedNodes: {},
      isLoading: false,
      error: null,
    };

    // 展开节点
    let nextState = sceneReducer(previousState, toggleNodeExpansion('entity1'));
    expect(nextState.expandedNodes).toEqual({ entity1: true });

    // 折叠节点
    nextState = sceneReducer(nextState, toggleNodeExpansion('entity1'));
    expect(nextState.expandedNodes).toEqual({ entity1: false });
  });

  // 添加实体测试
  it('应该处理addEntity', () => {
    const previousState = {
      currentScene: mockScene,
      selectedEntityId: null,
      expandedNodes: {},
      isLoading: false,
      error: null,
    };

    const newEntity = {
      id: 'entity4',
      name: '新实体',
      type: 'camera',
      children: [],
    };

    // 添加到根级别
    let nextState = sceneReducer(previousState, addEntity({ entity: newEntity, parentId: null }));
    expect(nextState.currentScene.entities.length).toBe(3);
    expect(nextState.currentScene.entities[2]).toEqual(newEntity);

    // 添加到父实体
    nextState = sceneReducer(previousState, addEntity({ entity: newEntity, parentId: 'entity1' }));
    expect(nextState.currentScene.entities[0].children.length).toBe(2);
    expect(nextState.currentScene.entities[0].children[1]).toEqual(newEntity);
  });

  // 移除实体测试
  it('应该处理removeEntity', () => {
    const previousState = {
      currentScene: mockScene,
      selectedEntityId: null,
      expandedNodes: {},
      isLoading: false,
      error: null,
    };

    // 移除顶级实体
    let nextState = sceneReducer(previousState, removeEntity('entity2'));
    expect(nextState.currentScene.entities.length).toBe(1);
    expect(nextState.currentScene.entities[0].id).toBe('entity1');

    // 移除子实体
    nextState = sceneReducer(previousState, removeEntity('entity3'));
    expect(nextState.currentScene.entities[0].children.length).toBe(0);
  });

  // 更新实体测试
  it('应该处理updateEntity', () => {
    const previousState = {
      currentScene: mockScene,
      selectedEntityId: null,
      expandedNodes: {},
      isLoading: false,
      error: null,
    };

    const updatedEntity = {
      id: 'entity1',
      name: '更新后的实体',
      type: 'mesh',
      position: { x: 1, y: 2, z: 3 },
    };

    const nextState = sceneReducer(previousState, updateEntity(updatedEntity));
    expect(nextState.currentScene.entities[0].name).toBe('更新后的实体');
    expect(nextState.currentScene.entities[0].position).toEqual({ x: 1, y: 2, z: 3 });
    // 确保子实体没有丢失
    expect(nextState.currentScene.entities[0].children).toEqual(mockScene.entities[0].children);
  });
});
