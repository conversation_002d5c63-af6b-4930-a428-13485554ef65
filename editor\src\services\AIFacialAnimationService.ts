/**
 * AI面部动画服务
 * 用于连接编辑器和引擎的AI面部动画生成功能
 */
import { message } from 'antd';
import { AIModelType } from '../components/FacialAnimationEditor/AIFacialAnimationGenerator';
import { EditorEventBus } from './EditorEventBus';
import { EntityService } from './EntityService';
import { i18n } from '../i18n';

// 生成请求
export interface AnimationGenerationRequest {
  id: string;
  prompt: string;
  duration: number;
  loop: boolean;
  style?: string;
  intensity?: number;
  modelType: AIModelType;
  useAdvancedFeatures: boolean;
}

// 生成结果
export interface AnimationGenerationResult {
  id: string;
  success: boolean;
  error?: string;
  clipId?: string;
}

// 生成配置
export interface AIFacialAnimationConfig {
  defaultModelType: AIModelType;
  useAdvancedFeatures: boolean;
  useLocalModel: boolean;
  modelPath?: string;
  useGPU: boolean;
}

/**
 * AI面部动画服务
 */
export class AIFacialAnimationService {
  private static instance: AIFacialAnimationService;
  
  private config: AIFacialAnimationConfig;
  private pendingRequests: Map<string, AnimationGenerationRequest>;
  private generatedAnimations: Map<string, string>; // 请求ID -> 动画片段ID
  
  /**
   * 获取实例
   * @returns 服务实例
   */
  public static getInstance(): AIFacialAnimationService {
    if (!AIFacialAnimationService.instance) {
      AIFacialAnimationService.instance = new AIFacialAnimationService();
    }
    
    return AIFacialAnimationService.instance;
  }
  
  /**
   * 构造函数
   */
  private constructor() {
    this.config = {
      defaultModelType: AIModelType.ADVANCED,
      useAdvancedFeatures: true,
      useLocalModel: true,
      useGPU: false
    };
    
    this.pendingRequests = new Map();
    this.generatedAnimations = new Map();
    
    // 监听生成完成事件
    EditorEventBus.getInstance().on('ai:facialAnimation:generationComplete', this.handleGenerationComplete);
  }
  
  /**
   * 生成面部动画
   * @param entityId 实体ID
   * @param request 生成请求
   * @returns 是否成功提交请求
   */
  public async generateFacialAnimation(
    entityId: string,
    request: AnimationGenerationRequest
  ): Promise<boolean> {
    try {
      // 存储请求
      this.pendingRequests.set(request.id, request);
      
      // 获取实体服务
      const entityService = EntityService.getInstance();
      
      // 获取模型类型
      const modelType = this.getModelTypeForRequest(request);
      
      // 创建引擎请求
      const engineRequest = {
        id: request.id,
        prompt: request.prompt,
        duration: request.duration,
        loop: request.loop,
        style: request.style,
        intensity: request.intensity,
        modelType,
        useAdvancedFeatures: request.useAdvancedFeatures
      };
      
      // 调用引擎生成面部动画
      const success = await entityService.callEntityMethod(
        entityId,
        'generateAIFacialAnimation',
        engineRequest
      );
      
      if (!success) {
        message.error(i18n.t('editor.ai.requestFailed'));
        this.pendingRequests.delete(request.id);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('生成面部动画失败:', error);
      message.error(i18n.t('editor.ai.generationError'));
      this.pendingRequests.delete(request.id);
      return false;
    }
  }
  
  /**
   * 应用生成的动画
   * @param entityId 实体ID
   * @param requestId 请求ID
   * @returns 是否成功应用
   */
  public async applyGeneratedAnimation(
    entityId: string,
    requestId: string
  ): Promise<boolean> {
    try {
      // 获取动画片段ID
      const clipId = this.generatedAnimations.get(requestId);
      
      if (!clipId) {
        message.error(i18n.t('editor.ai.animationNotFound'));
        return false;
      }
      
      // 获取实体服务
      const entityService = EntityService.getInstance();
      
      // 调用引擎应用动画
      const success = await entityService.callEntityMethod(
        entityId,
        'applyFacialAnimation',
        { clipId }
      );
      
      if (!success) {
        message.error(i18n.t('editor.ai.applyFailed'));
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('应用面部动画失败:', error);
      message.error(i18n.t('editor.ai.applyError'));
      return false;
    }
  }
  
  /**
   * 预览生成的动画
   * @param entityId 实体ID
   * @param requestId 请求ID
   * @returns 是否成功预览
   */
  public async previewGeneratedAnimation(
    entityId: string,
    requestId: string
  ): Promise<boolean> {
    try {
      // 获取动画片段ID
      const clipId = this.generatedAnimations.get(requestId);
      
      if (!clipId) {
        message.error(i18n.t('editor.ai.animationNotFound'));
        return false;
      }
      
      // 获取实体服务
      const entityService = EntityService.getInstance();
      
      // 调用引擎预览动画
      const success = await entityService.callEntityMethod(
        entityId,
        'previewFacialAnimation',
        { clipId }
      );
      
      if (!success) {
        message.error(i18n.t('editor.ai.previewFailed'));
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('预览面部动画失败:', error);
      message.error(i18n.t('editor.ai.previewError'));
      return false;
    }
  }
  
  /**
   * 处理生成完成事件
   * @param result 生成结果
   */
  private handleGenerationComplete = (result: AnimationGenerationResult) => {
    // 移除请求
    this.pendingRequests.delete(result.id);
    
    if (result.success && result.clipId) {
      // 存储生成的动画
      this.generatedAnimations.set(result.id, result.clipId);
    }
  };
  
  /**
   * 获取请求的模型类型
   * @param request 生成请求
   * @returns 模型类型字符串
   */
  private getModelTypeForRequest(request: AnimationGenerationRequest): string {
    switch (request.modelType) {
      case AIModelType.BASIC:
        return 'basic';
      case AIModelType.ADVANCED:
        return 'advanced';
      case AIModelType.BERT:
        return 'bert';
      case AIModelType.CUSTOM:
        return 'custom';
      default:
        return 'advanced';
    }
  }
  
  /**
   * 更新配置
   * @param config 配置
   */
  public updateConfig(config: Partial<AIFacialAnimationConfig>): void {
    this.config = {
      ...this.config,
      ...config
    };
  }
  
  /**
   * 获取配置
   * @returns 配置
   */
  public getConfig(): AIFacialAnimationConfig {
    return { ...this.config };
  }
}
