/**
 * 教程高亮组件
 * 用于在教程中高亮显示UI元素
 */
import React, { useEffect, useState, useRef } from 'react';
import { createPortal } from 'react-dom';
import { Tooltip } from 'antd';
import { tutorialValidationService } from '../../services/TutorialValidationService';
import './TutorialHighlight.css';

/**
 * 教程高亮组件属性
 */
interface TutorialHighlightProps {
  selector?: string;
  elementId?: string;
  message?: string;
  active: boolean;
  pulse?: boolean;
  arrow?: 'top' | 'right' | 'bottom' | 'left';
  zIndex?: number;
  onClick?: () => void;
}

/**
 * 教程高亮组件
 */
export const TutorialHighlight: React.FC<TutorialHighlightProps> = ({
  selector,
  elementId,
  message,
  active,
  pulse = true,
  arrow,
  zIndex = 1000,
  onClick
}) => {
  const [position, setPosition] = useState<{ top: number; left: number; width: number; height: number } | null>(null);
  const [targetElement, setTargetElement] = useState<Element | null>(null);
  const highlightRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<MutationObserver | null>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);
  
  // 查找目标元素
  const findTargetElement = (): Element | null => {
    if (elementId) {
      return document.getElementById(elementId);
    } else if (selector) {
      return document.querySelector(selector);
    }
    return null;
  };
  
  // 计算元素位置
  const calculatePosition = (element: Element) => {
    const rect = element.getBoundingClientRect();
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    
    return {
      top: rect.top + scrollTop,
      left: rect.left + scrollLeft,
      width: rect.width,
      height: rect.height
    };
  };
  
  // 更新高亮位置
  const updateHighlightPosition = () => {
    const element = findTargetElement();
    if (element) {
      setTargetElement(element);
      setPosition(calculatePosition(element));
    } else {
      setTargetElement(null);
      setPosition(null);
    }
  };
  
  // 监听目标元素变化
  useEffect(() => {
    if (!active) {
      setTargetElement(null);
      setPosition(null);
      return;
    }
    
    updateHighlightPosition();
    
    // 创建DOM变化观察器
    if (!observerRef.current) {
      observerRef.current = new MutationObserver(() => {
        updateHighlightPosition();
      });
    }
    
    // 创建大小变化观察器
    if (!resizeObserverRef.current) {
      resizeObserverRef.current = new ResizeObserver(() => {
        updateHighlightPosition();
      });
    }
    
    // 监听DOM变化
    observerRef.current.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true
    });
    
    // 监听窗口大小变化
    window.addEventListener('resize', updateHighlightPosition);
    window.addEventListener('scroll', updateHighlightPosition, true);
    
    return () => {
      // 清理观察器
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
      
      // 移除事件监听器
      window.removeEventListener('resize', updateHighlightPosition);
      window.removeEventListener('scroll', updateHighlightPosition, true);
    };
  }, [active, selector, elementId]);
  
  // 监听目标元素变化，添加大小观察器
  useEffect(() => {
    if (targetElement && resizeObserverRef.current) {
      resizeObserverRef.current.observe(targetElement);
      
      return () => {
        if (resizeObserverRef.current && targetElement) {
          resizeObserverRef.current.unobserve(targetElement);
        }
      };
    }
  }, [targetElement]);
  
  // 如果没有位置信息或不活跃，不渲染
  if (!position || !active) {
    return null;
  }
  
  // 计算箭头位置
  const getArrowStyle = () => {
    if (!arrow) return {};
    
    switch (arrow) {
      case 'top':
        return {
          bottom: '100%',
          left: '50%',
          transform: 'translateX(-50%)'
        };
      case 'right':
        return {
          left: '100%',
          top: '50%',
          transform: 'translateY(-50%)'
        };
      case 'bottom':
        return {
          top: '100%',
          left: '50%',
          transform: 'translateX(-50%)'
        };
      case 'left':
        return {
          right: '100%',
          top: '50%',
          transform: 'translateY(-50%)'
        };
    }
  };
  
  // 渲染高亮元素
  const highlightElement = (
    <div
      ref={highlightRef}
      className={`tutorial-highlight ${pulse ? 'pulse' : ''}`}
      style={{
        top: `${position.top}px`,
        left: `${position.left}px`,
        width: `${position.width}px`,
        height: `${position.height}px`,
        zIndex
      }}
      onClick={onClick}
    >
      {arrow && (
        <div
          className={`tutorial-highlight-arrow tutorial-highlight-arrow-${arrow}`}
          style={getArrowStyle()}
        />
      )}
      
      {message && (
        <Tooltip
          title={message}
          visible={true}
          placement={arrow || 'bottom'}
          overlayClassName="tutorial-highlight-tooltip"
        >
          <div className="tutorial-highlight-tooltip-trigger" />
        </Tooltip>
      )}
    </div>
  );
  
  // 使用Portal渲染到body
  return createPortal(highlightElement, document.body);
};

/**
 * 教程高亮容器组件
 * 用于管理当前活动教程的高亮元素
 */
export const TutorialHighlightContainer: React.FC = () => {
  const [currentHighlight, setCurrentHighlight] = useState<{
    selector?: string;
    elementId?: string;
    message?: string;
  } | null>(null);
  
  // 监听教程步骤变化
  useEffect(() => {
    const handleStepChanged = (data: { 
      tutorialId: string; 
      stepIndex: number;
      step: any;
    }) => {
      const step = data.step;
      if (step.highlightSelector || step.highlightElement) {
        setCurrentHighlight({
          selector: step.highlightSelector,
          elementId: step.highlightElement,
          message: step.highlightMessage
        });
      } else {
        setCurrentHighlight(null);
      }
    };
    
    const handleTutorialCancelled = () => {
      setCurrentHighlight(null);
    };
    
    const handleTutorialCompleted = () => {
      setCurrentHighlight(null);
    };
    
    // 添加事件监听器
    tutorialValidationService.on('stepChanged', handleStepChanged);
    tutorialValidationService.on('tutorialCancelled', handleTutorialCancelled);
    tutorialValidationService.on('tutorialCompleted', handleTutorialCompleted);
    
    // 获取当前活动教程
    const activeTutorial = tutorialValidationService.getActiveTutorial();
    if (activeTutorial) {
      const currentStep = activeTutorial.steps[activeTutorial.currentStepIndex];
      if (currentStep && (currentStep.highlightSelector || currentStep.highlightElement)) {
        setCurrentHighlight({
          selector: currentStep.highlightSelector,
          elementId: currentStep.highlightElement,
          message: currentStep.highlightMessage
        });
      }
    }
    
    return () => {
      // 移除事件监听器
      tutorialValidationService.off('stepChanged', handleStepChanged);
      tutorialValidationService.off('tutorialCancelled', handleTutorialCancelled);
      tutorialValidationService.off('tutorialCompleted', handleTutorialCompleted);
    };
  }, []);
  
  if (!currentHighlight) {
    return null;
  }
  
  return (
    <TutorialHighlight
      selector={currentHighlight.selector}
      elementId={currentHighlight.elementId}
      message={currentHighlight.message}
      active={true}
      pulse={true}
    />
  );
};

export default TutorialHighlightContainer;
