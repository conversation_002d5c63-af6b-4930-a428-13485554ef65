/**
 * 动画遮罩
 * 用于控制动画只影响特定的骨骼或属性
 */

import * as THREE from 'three';

export interface MaskRule {
  /** 目标路径（骨骼名称或属性路径） */
  path: string;
  /** 权重（0-1） */
  weight: number;
  /** 是否递归应用到子骨骼 */
  recursive?: boolean;
}

export class AnimationMask {
  /** 遮罩名称 */
  public readonly name: string;

  /** 遮罩规则 */
  private rules: Map<string, MaskRule> = new Map();

  /** 默认权重 */
  private defaultWeight: number = 0;

  constructor(name: string, defaultWeight: number = 0) {
    this.name = name;
    this.defaultWeight = defaultWeight;
  }

  /**
   * 添加遮罩规则
   */
  public addRule(path: string, weight: number, recursive: boolean = false): void {
    this.rules.set(path, { path, weight, recursive });
  }

  /**
   * 移除遮罩规则
   */
  public removeRule(path: string): boolean {
    return this.rules.delete(path);
  }

  /**
   * 获取遮罩规则
   */
  public getRule(path: string): MaskRule | null {
    return this.rules.get(path) || null;
  }

  /**
   * 获取所有规则
   */
  public getRules(): MaskRule[] {
    return Array.from(this.rules.values());
  }

  /**
   * 获取路径的权重
   */
  public getWeight(path: string): number {
    const rule = this.rules.get(path);
    if (rule) {
      return rule.weight;
    }

    // 检查是否有父路径的递归规则
    for (const [rulePath, ruleData] of this.rules.entries()) {
      if (ruleData.recursive && path.startsWith(rulePath + '.')) {
        return ruleData.weight;
      }
    }

    return this.defaultWeight;
  }

  /**
   * 设置默认权重
   */
  public setDefaultWeight(weight: number): void {
    this.defaultWeight = weight;
  }

  /**
   * 获取默认权重
   */
  public getDefaultWeight(): number {
    return this.defaultWeight;
  }

  /**
   * 应用遮罩到动画片段
   */
  public applyToClip(clip: THREE.AnimationClip): THREE.AnimationClip {
    // 创建新的轨道数组
    const maskedTracks: THREE.KeyframeTrack[] = [];

    for (const track of clip.tracks) {
      const weight = this.getWeight(track.name);

      if (weight > 0) {
        // 如果权重不是1，需要调整轨道的值
        if (weight < 1) {
          const maskedTrack = this.applyWeightToTrack(track, weight);
          maskedTracks.push(maskedTrack);
        } else {
          maskedTracks.push(track);
        }
      }
      // 权重为0的轨道被完全过滤掉
    }

    // 创建新的动画片段
    return new THREE.AnimationClip(
      `${clip.name}_masked_${this.name}`,
      clip.duration,
      maskedTracks,
      clip.blendMode
    );
  }

  /**
   * 应用权重到轨道
   */
  private applyWeightToTrack(track: THREE.KeyframeTrack, weight: number): THREE.KeyframeTrack {
    // 复制轨道
    const TrackConstructor = track.constructor as any;
    const newTrack = new TrackConstructor(track.name, track.times.slice(), track.values.slice());

    // 根据轨道类型调整值
    if (track instanceof THREE.VectorKeyframeTrack ||
        track instanceof THREE.QuaternionKeyframeTrack ||
        track instanceof THREE.NumberKeyframeTrack) {

      // 对于数值类型的轨道，直接乘以权重
      for (let i = 0; i < newTrack.values.length; i++) {
        newTrack.values[i] *= weight;
      }
    } else if (track instanceof THREE.ColorKeyframeTrack) {
      // 对于颜色轨道，需要特殊处理
      for (let i = 0; i < newTrack.values.length; i += 3) {
        newTrack.values[i] *= weight;     // R
        newTrack.values[i + 1] *= weight; // G
        newTrack.values[i + 2] *= weight; // B
      }
    }

    return newTrack;
  }

  /**
   * 合并遮罩
   */
  public merge(other: AnimationMask): AnimationMask {
    const merged = new AnimationMask(`${this.name}_merged_${other.name}`, this.defaultWeight);

    // 添加当前遮罩的规则
    for (const rule of this.rules.values()) {
      merged.addRule(rule.path, rule.weight, rule.recursive);
    }

    // 添加其他遮罩的规则（如果路径相同，则取平均值）
    for (const rule of other.rules.values()) {
      const existingRule = merged.getRule(rule.path);
      if (existingRule) {
        const averageWeight = (existingRule.weight + rule.weight) / 2;
        merged.addRule(rule.path, averageWeight, rule.recursive || existingRule.recursive);
      } else {
        merged.addRule(rule.path, rule.weight, rule.recursive);
      }
    }

    return merged;
  }

  /**
   * 反转遮罩
   */
  public invert(): AnimationMask {
    const inverted = new AnimationMask(`${this.name}_inverted`, 1 - this.defaultWeight);

    for (const rule of this.rules.values()) {
      inverted.addRule(rule.path, 1 - rule.weight, rule.recursive);
    }

    return inverted;
  }

  /**
   * 克隆遮罩
   */
  public clone(): AnimationMask {
    const cloned = new AnimationMask(this.name, this.defaultWeight);

    for (const rule of this.rules.values()) {
      cloned.addRule(rule.path, rule.weight, rule.recursive);
    }

    return cloned;
  }

  /**
   * 序列化遮罩
   */
  public serialize(): any {
    return {
      name: this.name,
      defaultWeight: this.defaultWeight,
      rules: Array.from(this.rules.values())
    };
  }

  /**
   * 反序列化遮罩
   */
  public static deserialize(data: any): AnimationMask {
    const mask = new AnimationMask(data.name, data.defaultWeight);

    if (data.rules) {
      for (const rule of data.rules) {
        mask.addRule(rule.path, rule.weight, rule.recursive);
      }
    }

    return mask;
  }

  /**
   * 获取遮罩名称
   */
  public getName(): string {
    return this.name;
  }

  /**
   * 创建上半身遮罩
   */
  public static createUpperBodyMask(): AnimationMask {
    const mask = new AnimationMask('UpperBody', 0);

    // 添加上半身骨骼
    const upperBodyBones = [
      'Spine', 'Spine1', 'Spine2', 'Spine3',
      'Neck', 'Head',
      'LeftShoulder', 'LeftArm', 'LeftForeArm', 'LeftHand',
      'RightShoulder', 'RightArm', 'RightForeArm', 'RightHand'
    ];

    for (const bone of upperBodyBones) {
      mask.addRule(bone, 1.0, true);
    }

    return mask;
  }

  /**
   * 创建下半身遮罩
   */
  public static createLowerBodyMask(): AnimationMask {
    const mask = new AnimationMask('LowerBody', 0);

    // 添加下半身骨骼
    const lowerBodyBones = [
      'Hips',
      'LeftUpLeg', 'LeftLeg', 'LeftFoot', 'LeftToeBase',
      'RightUpLeg', 'RightLeg', 'RightFoot', 'RightToeBase'
    ];

    for (const bone of lowerBodyBones) {
      mask.addRule(bone, 1.0, true);
    }

    return mask;
  }

  /**
   * 创建左手遮罩
   */
  public static createLeftHandMask(): AnimationMask {
    const mask = new AnimationMask('LeftHand', 0);

    // 添加左手骨骼
    const leftHandBones = [
      'LeftHand', 'LeftHandThumb1', 'LeftHandThumb2', 'LeftHandThumb3',
      'LeftHandIndex1', 'LeftHandIndex2', 'LeftHandIndex3',
      'LeftHandMiddle1', 'LeftHandMiddle2', 'LeftHandMiddle3',
      'LeftHandRing1', 'LeftHandRing2', 'LeftHandRing3',
      'LeftHandPinky1', 'LeftHandPinky2', 'LeftHandPinky3'
    ];

    for (const bone of leftHandBones) {
      mask.addRule(bone, 1.0, true);
    }

    return mask;
  }

  /**
   * 创建右手遮罩
   */
  public static createRightHandMask(): AnimationMask {
    const mask = new AnimationMask('RightHand', 0);

    // 添加右手骨骼
    const rightHandBones = [
      'RightHand', 'RightHandThumb1', 'RightHandThumb2', 'RightHandThumb3',
      'RightHandIndex1', 'RightHandIndex2', 'RightHandIndex3',
      'RightHandMiddle1', 'RightHandMiddle2', 'RightHandMiddle3',
      'RightHandRing1', 'RightHandRing2', 'RightHandRing3',
      'RightHandPinky1', 'RightHandPinky2', 'RightHandPinky3'
    ];

    for (const bone of rightHandBones) {
      mask.addRule(bone, 1.0, true);
    }

    return mask;
  }

  /**
   * 设置缓存启用状态
   */
  public setCacheEnabled(enabled: boolean): void {
    // 实现缓存启用逻辑
    console.log(`AnimationMask 设置缓存启用状态: ${enabled}`);
  }

  /**
   * 设置对象池启用状态
   */
  public setObjectPoolEnabled(enabled: boolean): void {
    // 实现对象池启用逻辑
    console.log(`AnimationMask 设置对象池启用状态: ${enabled}`);
  }
}
