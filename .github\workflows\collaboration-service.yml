name: Collaboration Service CI/CD

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'newsystem/server/collaboration-service/**'
      - 'newsystem/.github/workflows/collaboration-service.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'newsystem/server/collaboration-service/**'

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: 'newsystem/server/collaboration-service/package.json'
    
    - name: Install dependencies
      run: |
        cd newsystem/server/collaboration-service
        npm ci
    
    - name: Run tests
      run: |
        cd newsystem/server/collaboration-service
        npm test
    
    - name: Run linting
      run: |
        cd newsystem/server/collaboration-service
        npm run lint
  
  build:
    name: Build
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to DockerHub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}
    
    - name: Build and push
      uses: docker/build-push-action@v4
      with:
        context: ./newsystem/server/collaboration-service
        push: true
        tags: irengine/collaboration-service:latest
        cache-from: type=registry,ref=irengine/collaboration-service:buildcache
        cache-to: type=registry,ref=irengine/collaboration-service:buildcache,mode=max
  
  deploy-dev:
    name: Deploy to Dev
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'push' && github.ref == 'refs/heads/develop'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Copy docker-compose.yml to dev server
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.DEV_HOST }}
        username: ${{ secrets.DEV_USERNAME }}
        key: ${{ secrets.DEV_KEY }}
        source: "newsystem/docker-compose.yml,newsystem/server/collaboration-service/nginx.conf"
        target: "/opt/ir-engine"
    
    - name: Deploy to dev server
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.DEV_HOST }}
        username: ${{ secrets.DEV_USERNAME }}
        key: ${{ secrets.DEV_KEY }}
        script: |
          cd /opt/ir-engine
          docker-compose pull collaboration-service-1 collaboration-service-2
          docker-compose up -d collaboration-service-1 collaboration-service-2 collaboration-load-balancer
  
  deploy-prod:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Copy docker-compose.yml to production server
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.PROD_HOST }}
        username: ${{ secrets.PROD_USERNAME }}
        key: ${{ secrets.PROD_KEY }}
        source: "newsystem/docker-compose.yml,newsystem/server/collaboration-service/nginx.conf"
        target: "/opt/ir-engine"
    
    - name: Deploy to production server
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.PROD_HOST }}
        username: ${{ secrets.PROD_USERNAME }}
        key: ${{ secrets.PROD_KEY }}
        script: |
          cd /opt/ir-engine
          docker-compose pull collaboration-service-1 collaboration-service-2
          docker-compose up -d collaboration-service-1 collaboration-service-2 collaboration-load-balancer
