/**
 * 动作捕捉示例
 * 演示如何使用动作捕捉系统
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { World } from '../../src/core/World';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Camera } from '../../src/scene/Camera';
import { Transform } from '../../src/scene/Transform';
import { Renderer } from '../../src/rendering/Renderer';
import { MotionCaptureSystem } from '../../src/mocap/MotionCaptureSystem';
import { MotionCaptureComponent } from '../../src/mocap/components/MotionCaptureComponent';
import { MotionCapturePoseComponent } from '../../src/mocap/components/MotionCapturePoseComponent';
import { AvatarComponent, AvatarType } from '../../src/avatar/components/AvatarComponent';
import { AvatarRigComponent } from '../../src/avatar/components/AvatarRigComponent';
import { VRMHumanBoneName } from '../../src/avatar/types/VRMHumanBoneName';
import { InputSystem } from '../../src/input/InputSystem';
import { NetworkManager } from '../../src/network/NetworkManager';
import { Debug } from '../../src/utils/Debug';
import { LandmarkData, WorldLandmarkData } from '../../src/mocap/types/LandmarkData';
import { LandmarkIndices } from '../../src/mocap/constants/LandmarkIndices';

// 创建引擎
const engine = new Engine();

// 创建世界
const world = new World();
engine.setWorld(world);

// 创建场景
const scene = new Scene();
world.addEntity(scene);

// 创建相机
const camera = new Camera();
camera.getComponent<Transform>(Transform.TYPE)!.position.set(0, 1.6, 3);
camera.getComponent<Transform>(Transform.TYPE)!.lookAt(new THREE.Vector3(0, 1, 0));
world.addEntity(camera);

// 创建渲染器
const renderer = new Renderer({
  canvas: document.getElementById('canvas') as HTMLCanvasElement,
  width: window.innerWidth,
  height: window.innerHeight,
  antialias: true
});
world.addSystem(renderer);

// 创建输入系统
const inputSystem = new InputSystem();
world.addSystem(inputSystem);

// 创建网络管理器
const networkManager = new NetworkManager({
  isHost: true,
  localUserId: 'local-user'
});
world.addSystem(networkManager);

// 创建动作捕捉系统
const motionCaptureSystem = new MotionCaptureSystem(world, {
  debug: true,
  enableNetworkSync: true,
  enablePoseEvaluation: true,
  smoothingFactor: 0.5,
  visibilityThreshold: 0.1
});
world.addSystem(motionCaptureSystem);

// 创建角色实体
const avatarEntity = new Entity(world);
avatarEntity.name = '角色';

// 添加角色组件
const avatarComponent = new AvatarComponent(avatarEntity, {
  type: AvatarType.LOCAL_PLAYER,
  userId: 'local-user',
  name: '本地玩家',
  modelUrl: 'models/avatar.glb'
});
avatarEntity.addComponent(avatarComponent);

// 添加变换组件
const avatarTransform = new Transform({
  position: new THREE.Vector3(0, 0, 0),
  rotation: new THREE.Quaternion(),
  scale: new THREE.Vector3(1, 1, 1)
});
avatarEntity.addComponent(avatarTransform);

// 添加骨骼组件
const avatarRigComponent = new AvatarRigComponent(avatarEntity);
avatarEntity.addComponent(avatarRigComponent);

// 添加动作捕捉组件
const motionCaptureComponent = new MotionCaptureComponent(avatarEntity, {
  smoothingFactor: 0.5,
  visibilityThreshold: 0.1
});
avatarEntity.addComponent(motionCaptureComponent);

// 添加姿势组件
const motionCapturePoseComponent = new MotionCapturePoseComponent(avatarEntity, {
  poseHoldThreshold: 0.25,
  poseAngleThreshold: 1.25
});
avatarEntity.addComponent(motionCapturePoseComponent);

// 注册动作捕捉组件到系统
motionCaptureSystem.registerMotionCaptureComponent(avatarEntity, motionCaptureComponent);
motionCaptureSystem.registerPoseComponent(avatarEntity, motionCapturePoseComponent);

// 添加角色到场景
world.addEntity(avatarEntity);

// 创建骨骼实体
function createBoneEntity(boneName: VRMHumanBoneName, parent: Entity): Entity {
  const boneEntity = new Entity(world);
  boneEntity.name = `骨骼_${boneName}`;
  
  // 添加变换组件
  const boneTransform = new Transform({
    position: new THREE.Vector3(0, 0, 0),
    rotation: new THREE.Quaternion(),
    scale: new THREE.Vector3(1, 1, 1)
  });
  boneEntity.addComponent(boneTransform);
  
  // 设置父实体
  boneEntity.setParent(parent);
  
  // 注册到骨骼组件
  avatarRigComponent.setBoneEntity(boneName, boneEntity);
  
  // 创建骨骼对象
  const bone = new THREE.Bone();
  avatarRigComponent.setBoneObject(boneName, bone);
  
  return boneEntity;
}

// 创建骨骼层级
const hipsEntity = createBoneEntity(VRMHumanBoneName.Hips, avatarEntity);
const spineEntity = createBoneEntity(VRMHumanBoneName.Spine, hipsEntity);
const chestEntity = createBoneEntity(VRMHumanBoneName.Chest, spineEntity);
const upperChestEntity = createBoneEntity(VRMHumanBoneName.UpperChest, chestEntity);
const neckEntity = createBoneEntity(VRMHumanBoneName.Neck, upperChestEntity);
const headEntity = createBoneEntity(VRMHumanBoneName.Head, neckEntity);

// 创建左臂骨骼
const leftShoulderEntity = createBoneEntity(VRMHumanBoneName.LeftShoulder, upperChestEntity);
const leftUpperArmEntity = createBoneEntity(VRMHumanBoneName.LeftUpperArm, leftShoulderEntity);
const leftLowerArmEntity = createBoneEntity(VRMHumanBoneName.LeftLowerArm, leftUpperArmEntity);
const leftHandEntity = createBoneEntity(VRMHumanBoneName.LeftHand, leftLowerArmEntity);

// 创建右臂骨骼
const rightShoulderEntity = createBoneEntity(VRMHumanBoneName.RightShoulder, upperChestEntity);
const rightUpperArmEntity = createBoneEntity(VRMHumanBoneName.RightUpperArm, rightShoulderEntity);
const rightLowerArmEntity = createBoneEntity(VRMHumanBoneName.RightLowerArm, rightUpperArmEntity);
const rightHandEntity = createBoneEntity(VRMHumanBoneName.RightHand, rightLowerArmEntity);

// 创建左腿骨骼
const leftUpperLegEntity = createBoneEntity(VRMHumanBoneName.LeftUpperLeg, hipsEntity);
const leftLowerLegEntity = createBoneEntity(VRMHumanBoneName.LeftLowerLeg, leftUpperLegEntity);
const leftFootEntity = createBoneEntity(VRMHumanBoneName.LeftFoot, leftLowerLegEntity);
const leftToesEntity = createBoneEntity(VRMHumanBoneName.LeftToes, leftFootEntity);

// 创建右腿骨骼
const rightUpperLegEntity = createBoneEntity(VRMHumanBoneName.RightUpperLeg, hipsEntity);
const rightLowerLegEntity = createBoneEntity(VRMHumanBoneName.RightLowerLeg, rightUpperLegEntity);
const rightFootEntity = createBoneEntity(VRMHumanBoneName.RightFoot, rightLowerLegEntity);
const rightToesEntity = createBoneEntity(VRMHumanBoneName.RightToes, rightFootEntity);

// 创建地面
const groundEntity = new Entity(world);
groundEntity.name = '地面';

// 添加变换组件
const groundTransform = new Transform({
  position: new THREE.Vector3(0, 0, 0),
  rotation: new THREE.Quaternion(),
  scale: new THREE.Vector3(10, 0.1, 10)
});
groundEntity.addComponent(groundTransform);

// 添加地面到场景
world.addEntity(groundEntity);

// 创建灯光
const lightEntity = new Entity(world);
lightEntity.name = '灯光';

// 添加变换组件
const lightTransform = new Transform({
  position: new THREE.Vector3(5, 10, 5),
  rotation: new THREE.Quaternion(),
  scale: new THREE.Vector3(1, 1, 1)
});
lightEntity.addComponent(lightTransform);

// 添加灯光到场景
world.addEntity(lightEntity);

// 模拟动作捕捉数据
function simulateMotionCaptureData(): { worldLandmarks: WorldLandmarkData[], landmarks: LandmarkData[] } {
  // 创建33个关键点（MediaPipe Pose模型）
  const worldLandmarks: WorldLandmarkData[] = [];
  const landmarks: LandmarkData[] = [];
  
  // 模拟T姿势
  for (let i = 0; i < 33; i++) {
    // 默认可见度
    const visibility = 0.9;
    
    // 默认位置
    let x = 0;
    let y = 0;
    let z = 0;
    
    // 根据关键点索引设置位置
    switch (i) {
      // 头部
      case LandmarkIndices.NOSE:
        x = 0; y = 0; z = -0.1;
        break;
      case LandmarkIndices.LEFT_EYE:
      case LandmarkIndices.LEFT_EYE_INNER:
      case LandmarkIndices.LEFT_EYE_OUTER:
        x = -0.03; y = 0.03; z = -0.1;
        break;
      case LandmarkIndices.RIGHT_EYE:
      case LandmarkIndices.RIGHT_EYE_INNER:
      case LandmarkIndices.RIGHT_EYE_OUTER:
        x = 0.03; y = 0.03; z = -0.1;
        break;
      case LandmarkIndices.LEFT_EAR:
        x = -0.08; y = 0; z = 0;
        break;
      case LandmarkIndices.RIGHT_EAR:
        x = 0.08; y = 0; z = 0;
        break;
      case LandmarkIndices.MOUTH_LEFT:
        x = -0.03; y = -0.03; z = -0.1;
        break;
      case LandmarkIndices.MOUTH_RIGHT:
        x = 0.03; y = -0.03; z = -0.1;
        break;
        
      // 躯干
      case LandmarkIndices.LEFT_SHOULDER:
        x = -0.2; y = -0.2; z = 0;
        break;
      case LandmarkIndices.RIGHT_SHOULDER:
        x = 0.2; y = -0.2; z = 0;
        break;
      case LandmarkIndices.LEFT_HIP:
        x = -0.1; y = -0.7; z = 0;
        break;
      case LandmarkIndices.RIGHT_HIP:
        x = 0.1; y = -0.7; z = 0;
        break;
        
      // 左臂
      case LandmarkIndices.LEFT_ELBOW:
        x = -0.4; y = -0.2; z = 0;
        break;
      case LandmarkIndices.LEFT_WRIST:
        x = -0.6; y = -0.2; z = 0;
        break;
      case LandmarkIndices.LEFT_PINKY:
        x = -0.65; y = -0.22; z = 0;
        break;
      case LandmarkIndices.LEFT_INDEX:
        x = -0.65; y = -0.18; z = 0;
        break;
      case LandmarkIndices.LEFT_THUMB:
        x = -0.62; y = -0.15; z = 0;
        break;
        
      // 右臂
      case LandmarkIndices.RIGHT_ELBOW:
        x = 0.4; y = -0.2; z = 0;
        break;
      case LandmarkIndices.RIGHT_WRIST:
        x = 0.6; y = -0.2; z = 0;
        break;
      case LandmarkIndices.RIGHT_PINKY:
        x = 0.65; y = -0.22; z = 0;
        break;
      case LandmarkIndices.RIGHT_INDEX:
        x = 0.65; y = -0.18; z = 0;
        break;
      case LandmarkIndices.RIGHT_THUMB:
        x = 0.62; y = -0.15; z = 0;
        break;
        
      // 左腿
      case LandmarkIndices.LEFT_KNEE:
        x = -0.1; y = -1.1; z = 0;
        break;
      case LandmarkIndices.LEFT_ANKLE:
        x = -0.1; y = -1.5; z = 0;
        break;
      case LandmarkIndices.LEFT_HEEL:
        x = -0.1; y = -1.5; z = 0.1;
        break;
      case LandmarkIndices.LEFT_FOOT_INDEX:
        x = -0.1; y = -1.5; z = -0.1;
        break;
        
      // 右腿
      case LandmarkIndices.RIGHT_KNEE:
        x = 0.1; y = -1.1; z = 0;
        break;
      case LandmarkIndices.RIGHT_ANKLE:
        x = 0.1; y = -1.5; z = 0;
        break;
      case LandmarkIndices.RIGHT_HEEL:
        x = 0.1; y = -1.5; z = 0.1;
        break;
      case LandmarkIndices.RIGHT_FOOT_INDEX:
        x = 0.1; y = -1.5; z = -0.1;
        break;
    }
    
    // 添加世界坐标系关键点
    worldLandmarks.push({
      x,
      y,
      z,
      visibility
    });
    
    // 添加屏幕坐标系关键点（归一化到0-1范围）
    landmarks.push({
      x: (x + 0.7) / 1.4,
      y: (y + 1.5) / 3.0,
      z: 0,
      visibility
    });
  }
  
  return { worldLandmarks, landmarks };
}

// 每帧更新模拟数据
let frameCount = 0;
engine.onUpdate = (deltaTime: number) => {
  frameCount++;
  
  // 每60帧更新一次模拟数据
  if (frameCount % 60 === 0) {
    const { worldLandmarks, landmarks } = simulateMotionCaptureData();
    
    // 设置动作捕捉数据
    motionCaptureComponent.setWorldLandmarks(worldLandmarks);
    motionCaptureComponent.setLandmarks(landmarks);
    
    // 发送动作捕捉数据
    motionCaptureSystem.sendMotionCaptureResults({
      worldLandmarks,
      landmarks
    });
    
    Debug.log('MotionCaptureExample', 'Sent motion capture data');
  }
};

// 启动引擎
engine.start();

// 输出调试信息
Debug.log('MotionCaptureExample', 'Motion capture example started');

// 导出变量供调试使用
(window as any).engine = engine;
(window as any).world = world;
(window as any).scene = scene;
(window as any).camera = camera;
(window as any).avatarEntity = avatarEntity;
(window as any).motionCaptureSystem = motionCaptureSystem;
(window as any).motionCaptureComponent = motionCaptureComponent;
(window as any).motionCapturePoseComponent = motionCapturePoseComponent;
