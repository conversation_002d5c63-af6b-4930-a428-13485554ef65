"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Camera = exports.CameraType = void 0;
/**
 * 相机类
 * 表示场景中的相机
 */
var THREE = require("three");
var Component_1 = require("../core/Component");
var CameraType;
(function (CameraType) {
    CameraType["PERSPECTIVE"] = "perspective";
    CameraType["ORTHOGRAPHIC"] = "orthographic";
})(CameraType || (exports.CameraType = CameraType = {}));
var Camera = exports.Camera = /** @class */ (function (_super) {
    __extends(Camera, _super);
    /**
     * 创建相机组件
     * @param options 相机选项
     */
    function Camera(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, Camera.type) || this;
        /** 调整大小监听器 */
        _this.resizeListener = null;
        _this.cameraType = options.type || CameraType.PERSPECTIVE;
        _this.autoAspect = options.autoAspect !== undefined ? options.autoAspect : true;
        // 创建Three.js相机
        if (_this.cameraType === CameraType.PERSPECTIVE) {
            var fov = options.fov || 75;
            var aspect = options.aspect || 1;
            var near = options.near || 0.1;
            var far = options.far || 1000;
            _this.camera = new THREE.PerspectiveCamera(fov, aspect, near, far);
        }
        else {
            var left = options.left || -1;
            var right = options.right || 1;
            var top_1 = options.top || 1;
            var bottom = options.bottom || -1;
            var near = options.near || 0.1;
            var far = options.far || 1000;
            _this.camera = new THREE.OrthographicCamera(left, right, top_1, bottom, near, far);
        }
        // 如果启用自动更新宽高比，添加窗口调整大小事件监听器
        if (_this.autoAspect) {
            _this.resizeListener = _this.handleResize.bind(_this);
            window.addEventListener('resize', _this.resizeListener);
        }
        return _this;
    }
    /**
     * 当组件附加到实体时调用
     */
    Camera.prototype.onAttach = function () {
        if (!this.entity)
            return;
        // 获取实体的变换组件
        var transform = this.entity.getTransform();
        if (transform) {
            // 将相机添加到变换的Three.js对象
            transform.getObject3D().add(this.camera);
        }
        // 如果启用自动更新宽高比，立即更新宽高比
        if (this.autoAspect) {
            this.updateAspect();
        }
    };
    /**
     * 处理窗口调整大小事件
     */
    Camera.prototype.handleResize = function () {
        this.updateAspect();
    };
    /**
     * 更新宽高比
     */
    Camera.prototype.updateAspect = function () {
        if (this.cameraType !== CameraType.PERSPECTIVE) {
            return;
        }
        var perspectiveCamera = this.camera;
        // 获取窗口宽高比
        var aspect = window.innerWidth / window.innerHeight;
        // 更新相机宽高比
        perspectiveCamera.aspect = aspect;
        perspectiveCamera.updateProjectionMatrix();
    };
    /**
     * 设置视野角度（透视相机）
     * @param fov 视野角度
     */
    Camera.prototype.setFov = function (fov) {
        if (this.cameraType !== CameraType.PERSPECTIVE) {
            console.warn('只有透视相机才能设置视野角度');
            return;
        }
        var perspectiveCamera = this.camera;
        perspectiveCamera.fov = fov;
        perspectiveCamera.updateProjectionMatrix();
    };
    /**
     * 获取视野角度（透视相机）
     * @returns 视野角度
     */
    Camera.prototype.getFov = function () {
        if (this.cameraType !== CameraType.PERSPECTIVE) {
            console.warn('只有透视相机才能获取视野角度');
            return 0;
        }
        return this.camera.fov;
    };
    /**
     * 设置宽高比（透视相机）
     * @param aspect 宽高比
     */
    Camera.prototype.setAspect = function (aspect) {
        if (this.cameraType !== CameraType.PERSPECTIVE) {
            console.warn('只有透视相机才能设置宽高比');
            return;
        }
        var perspectiveCamera = this.camera;
        perspectiveCamera.aspect = aspect;
        perspectiveCamera.updateProjectionMatrix();
    };
    /**
     * 获取宽高比（透视相机）
     * @returns 宽高比
     */
    Camera.prototype.getAspect = function () {
        if (this.cameraType !== CameraType.PERSPECTIVE) {
            console.warn('只有透视相机才能获取宽高比');
            return 0;
        }
        return this.camera.aspect;
    };
    /**
     * 设置近裁剪面
     * @param near 近裁剪面
     */
    Camera.prototype.setNear = function (near) {
        if (this.cameraType === CameraType.PERSPECTIVE) {
            var perspectiveCamera = this.camera;
            perspectiveCamera.near = near;
            perspectiveCamera.updateProjectionMatrix();
        }
        else {
            var orthographicCamera = this.camera;
            orthographicCamera.near = near;
            orthographicCamera.updateProjectionMatrix();
        }
    };
    /**
     * 获取近裁剪面
     * @returns 近裁剪面
     */
    Camera.prototype.getNear = function () {
        if (this.cameraType === CameraType.PERSPECTIVE) {
            return this.camera.near;
        }
        else {
            return this.camera.near;
        }
    };
    /**
     * 设置远裁剪面
     * @param far 远裁剪面
     */
    Camera.prototype.setFar = function (far) {
        if (this.cameraType === CameraType.PERSPECTIVE) {
            var perspectiveCamera = this.camera;
            perspectiveCamera.far = far;
            perspectiveCamera.updateProjectionMatrix();
        }
        else {
            var orthographicCamera = this.camera;
            orthographicCamera.far = far;
            orthographicCamera.updateProjectionMatrix();
        }
    };
    /**
     * 获取远裁剪面
     * @returns 远裁剪面
     */
    Camera.prototype.getFar = function () {
        if (this.cameraType === CameraType.PERSPECTIVE) {
            return this.camera.far;
        }
        else {
            return this.camera.far;
        }
    };
    /**
     * 设置正交相机参数
     * @param left 左平面
     * @param right 右平面
     * @param top 上平面
     * @param bottom 下平面
     */
    Camera.prototype.setOrthographicSize = function (left, right, top, bottom) {
        if (this.cameraType !== CameraType.ORTHOGRAPHIC) {
            console.warn('只有正交相机才能设置正交大小');
            return;
        }
        var orthographicCamera = this.camera;
        orthographicCamera.left = left;
        orthographicCamera.right = right;
        orthographicCamera.top = top;
        orthographicCamera.bottom = bottom;
        orthographicCamera.updateProjectionMatrix();
    };
    /**
     * 设置正交相机缩放
     * @param zoom 缩放
     */
    Camera.prototype.setZoom = function (zoom) {
        if (this.cameraType === CameraType.PERSPECTIVE) {
            var perspectiveCamera = this.camera;
            perspectiveCamera.zoom = zoom;
            perspectiveCamera.updateProjectionMatrix();
        }
        else {
            var orthographicCamera = this.camera;
            orthographicCamera.zoom = zoom;
            orthographicCamera.updateProjectionMatrix();
        }
    };
    /**
     * 获取正交相机缩放
     * @returns 缩放
     */
    Camera.prototype.getZoom = function () {
        if (this.cameraType === CameraType.PERSPECTIVE) {
            return this.camera.zoom;
        }
        else {
            return this.camera.zoom;
        }
    };
    /**
     * 获取相机类型
     * @returns 相机类型
     */
    Camera.prototype.getType = function () {
        return this.cameraType;
    };
    /**
     * 获取Three.js相机
     * @returns Three.js相机
     */
    Camera.prototype.getThreeCamera = function () {
        return this.camera;
    };
    /**
     * 获取视图矩阵
     * @returns 视图矩阵
     */
    Camera.prototype.getViewMatrix = function () {
        return this.camera.matrixWorldInverse.clone();
    };
    /**
     * 获取投影矩阵
     * @returns 投影矩阵
     */
    Camera.prototype.getProjectionMatrix = function () {
        return this.camera.projectionMatrix.clone();
    };
    /**
     * 获取视图投影矩阵
     * @returns 视图投影矩阵
     */
    Camera.prototype.getViewProjectionMatrix = function () {
        var viewProjectionMatrix = new THREE.Matrix4();
        return viewProjectionMatrix.multiplyMatrices(this.camera.projectionMatrix, this.camera.matrixWorldInverse);
    };
    /**
     * 获取世界位置
     * @returns 世界位置
     */
    Camera.prototype.getPosition = function () {
        var position = new THREE.Vector3();
        this.camera.getWorldPosition(position);
        return position;
    };
    /**
     * 获取前方向
     * @returns 前方向
     */
    Camera.prototype.getForward = function () {
        var forward = new THREE.Vector3(0, 0, -1);
        forward.applyQuaternion(this.camera.quaternion);
        return forward;
    };
    /**
     * 获取右方向
     * @returns 右方向
     */
    Camera.prototype.getRight = function () {
        var right = new THREE.Vector3(1, 0, 0);
        right.applyQuaternion(this.camera.quaternion);
        return right;
    };
    /**
     * 获取上方向
     * @returns 上方向
     */
    Camera.prototype.getUp = function () {
        var up = new THREE.Vector3(0, 1, 0);
        up.applyQuaternion(this.camera.quaternion);
        return up;
    };
    /**
     * 设置自动更新宽高比
     * @param autoAspect 是否自动更新宽高比
     */
    Camera.prototype.setAutoAspect = function (autoAspect) {
        if (this.autoAspect === autoAspect) {
            return;
        }
        this.autoAspect = autoAspect;
        if (autoAspect) {
            // 添加窗口调整大小事件监听器
            if (!this.resizeListener) {
                this.resizeListener = this.handleResize.bind(this);
            }
            window.addEventListener('resize', this.resizeListener);
            // 立即更新宽高比
            this.updateAspect();
        }
        else {
            // 移除窗口调整大小事件监听器
            if (this.resizeListener) {
                window.removeEventListener('resize', this.resizeListener);
            }
        }
    };
    /**
     * 是否自动更新宽高比
     * @returns 是否自动更新宽高比
     */
    Camera.prototype.isAutoAspect = function () {
        return this.autoAspect;
    };
    /**
     * 销毁组件
     */
    Camera.prototype.dispose = function () {
        // 移除窗口调整大小事件监听器
        if (this.resizeListener) {
            window.removeEventListener('resize', this.resizeListener);
            this.resizeListener = null;
        }
        _super.prototype.dispose.call(this);
    };
    /** 组件类型 */
    Camera.type = 'Camera';
    return Camera;
}(Component_1.Component));
