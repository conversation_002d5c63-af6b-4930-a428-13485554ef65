/**
 * 测试会话管理组件
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Table,
  Modal,
  Form,
  Input,
  Select,
  Divider,
  Tag,
  Tooltip,
  message,
  Popconfirm
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  ExportOutlined,
  ImportOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  FileTextOutlined,
  HistoryOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { userTestingService, TestSession, TestTask } from '../../services/UserTestingService';
import { testReportGenerator, ReportFormat } from './TestReportGenerator';
import { userBehaviorAnalyzer } from './UserBehaviorAnalyzer';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 测试会话管理组件属性
 */
interface TestSessionManagerProps {
  onSessionStart?: (sessionId: string) => void;
  onSessionEnd?: (session: TestSession) => void;
}

/**
 * 测试会话管理组件
 */
const TestSessionManager: React.FC<TestSessionManagerProps> = ({
  onSessionStart,
  onSessionEnd
}) => {
  const { t } = useTranslation();
  
  // 本地状态
  const [sessions, setSessions] = useState<TestSession[]>([]);
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isExportModalVisible, setIsExportModalVisible] = useState(false);
  const [exportFormat, setExportFormat] = useState<ReportFormat>(ReportFormat.HTML);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  
  // 加载会话列表
  useEffect(() => {
    loadSessions();
  }, []);
  
  // 加载会话列表
  const loadSessions = () => {
    // 这里应该从服务器或本地存储加载会话列表
    // 暂时使用模拟数据
    const mockSessions: TestSession[] = [
      {
        id: 'session_1',
        userId: 'user1',
        userName: '测试用户1',
        startTime: Date.now() - 3600000,
        endTime: Date.now() - 1800000,
        tasks: [
          {
            id: 'task1',
            title: '创建场景',
            description: '创建一个新的场景',
            completed: true,
            startTime: Date.now() - 3500000,
            endTime: Date.now() - 3300000,
            timeSpent: 200000
          },
          {
            id: 'task2',
            title: '添加对象',
            description: '向场景中添加一个立方体',
            completed: true,
            startTime: Date.now() - 3200000,
            endTime: Date.now() - 2900000,
            timeSpent: 300000
          }
        ],
        feedback: [],
        metadata: {
          projectId: 'project1',
          sceneId: 'scene1'
        }
      }
    ];
    
    setSessions(mockSessions);
  };
  
  // 处理创建会话
  const handleCreateSession = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      // 解析任务
      const tasks: Omit<TestTask, 'completed' | 'startTime' | 'endTime' | 'timeSpent'>[] = values.tasks.split('\n')
        .filter((line: string) => line.trim() !== '')
        .map((line: string, index: number) => {
          const parts = line.split(':');
          const title = parts[0].trim();
          const description = parts.length > 1 ? parts[1].trim() : '';
          
          return {
            id: `task_${Date.now()}_${index}`,
            title,
            description
          };
        });
      
      if (tasks.length === 0) {
        message.error('请至少添加一个任务');
        setLoading(false);
        return;
      }
      
      // 创建会话
      const sessionId = userTestingService.startSession(tasks, {
        projectId: values.projectId,
        sceneId: values.sceneId,
        description: values.description
      });
      
      // 更新会话列表
      loadSessions();
      
      // 关闭对话框
      setIsCreateModalVisible(false);
      form.resetFields();
      
      // 回调
      if (onSessionStart) {
        onSessionStart(sessionId);
      }
      
      message.success('测试会话已创建');
    } catch (error) {
      console.error('创建会话失败:', error);
      message.error('创建会话失败');
    } finally {
      setLoading(false);
    }
  };
  
  // 处理结束会话
  const handleEndSession = (sessionId: string) => {
    try {
      const session = userTestingService.endSession();
      
      if (session) {
        // 更新会话列表
        loadSessions();
        
        // 回调
        if (onSessionEnd) {
          onSessionEnd(session);
        }
        
        message.success('测试会话已结束');
      }
    } catch (error) {
      console.error('结束会话失败:', error);
      message.error('结束会话失败');
    }
  };
  
  // 处理删除会话
  const handleDeleteSession = (sessionId: string) => {
    // 这里应该调用服务删除会话
    // 暂时只从本地列表中移除
    setSessions(sessions.filter(session => session.id !== sessionId));
    message.success('测试会话已删除');
  };
  
  // 处理导出会话
  const handleExportSession = (sessionId: string) => {
    setSelectedSessionId(sessionId);
    setIsExportModalVisible(true);
  };
  
  // 处理导出确认
  const handleExportConfirm = () => {
    if (!selectedSessionId) {
      return;
    }
    
    try {
      // 查找会话
      const session = sessions.find(s => s.id === selectedSessionId);
      
      if (!session) {
        message.error('找不到会话');
        return;
      }
      
      // 获取用户行为数据
      const actions = userBehaviorAnalyzer.getActionHistory();
      
      // 生成报告
      const report = testReportGenerator.generateReport(session, actions);
      
      // 导出报告
      const exportData = testReportGenerator.exportReport(report, exportFormat);
      
      // 下载报告
      if (exportData instanceof Blob) {
        const url = URL.createObjectURL(exportData);
        const a = document.createElement('a');
        a.href = url;
        a.download = `test_report_${selectedSessionId}.${exportFormat.toLowerCase()}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } else {
        // 处理字符串格式
        const blob = new Blob([exportData], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `test_report_${selectedSessionId}.${exportFormat.toLowerCase()}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
      
      message.success('报告已导出');
    } catch (error) {
      console.error('导出报告失败:', error);
      message.error('导出报告失败');
    } finally {
      setIsExportModalVisible(false);
    }
  };
  
  // 表格列定义
  const columns = [
    {
      title: '会话ID',
      dataIndex: 'id',
      key: 'id',
      render: (text: string) => <Text ellipsis>{text}</Text>
    },
    {
      title: '用户',
      dataIndex: 'userName',
      key: 'userName'
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      render: (time: number) => new Date(time).toLocaleString()
    },
    {
      title: '状态',
      key: 'status',
      render: (text: string, record: TestSession) => (
        <Tag color={record.endTime ? 'green' : 'blue'}>
          {record.endTime ? '已完成' : '进行中'}
        </Tag>
      )
    },
    {
      title: '任务',
      key: 'tasks',
      render: (text: string, record: TestSession) => {
        const completed = record.tasks.filter(t => t.completed).length;
        const total = record.tasks.length;
        return `${completed}/${total}`;
      }
    },
    {
      title: '反馈',
      dataIndex: 'feedback',
      key: 'feedback',
      render: (feedback: any[]) => feedback.length
    },
    {
      title: '操作',
      key: 'action',
      render: (text: string, record: TestSession) => (
        <Space size="small">
          {!record.endTime && (
            <Tooltip title="结束会话">
              <Button
                type="text"
                icon={<PauseCircleOutlined />}
                onClick={() => handleEndSession(record.id)}
              />
            </Tooltip>
          )}
          <Tooltip title="导出报告">
            <Button
              type="text"
              icon={<ExportOutlined />}
              onClick={() => handleExportSession(record.id)}
            />
          </Tooltip>
          <Tooltip title="删除会话">
            <Popconfirm
              title="确定要删除这个测试会话吗？"
              onConfirm={() => handleDeleteSession(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ];
  
  return (
    <div className="test-session-manager">
      <Card
        title={
          <Space>
            <HistoryOutlined />
            <span>测试会话管理</span>
          </Space>
        }
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsCreateModalVisible(true)}
            >
              创建会话
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadSessions}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <Table
          dataSource={sessions}
          columns={columns}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Card>
      
      {/* 创建会话对话框 */}
      <Modal
        title="创建测试会话"
        open={isCreateModalVisible}
        onOk={handleCreateSession}
        onCancel={() => setIsCreateModalVisible(false)}
        confirmLoading={loading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            projectId: '',
            sceneId: '',
            description: '',
            tasks: '创建场景: 创建一个新的场景\n添加对象: 向场景中添加一个立方体\n邀请协作者: 邀请另一个用户加入协作编辑\n解决冲突: 尝试同时编辑同一个对象，并解决产生的冲突'
          }}
        >
          <Form.Item
            name="projectId"
            label="项目ID"
          >
            <Input placeholder="请输入项目ID" />
          </Form.Item>
          
          <Form.Item
            name="sceneId"
            label="场景ID"
          >
            <Input placeholder="请输入场景ID" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="会话描述"
          >
            <Input placeholder="请输入会话描述" />
          </Form.Item>
          
          <Form.Item
            name="tasks"
            label="测试任务"
            help="每行一个任务，格式：任务标题: 任务描述"
            rules={[{ required: true, message: '请至少添加一个任务' }]}
          >
            <TextArea
              rows={6}
              placeholder="任务标题: 任务描述"
            />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 导出报告对话框 */}
      <Modal
        title="导出测试报告"
        open={isExportModalVisible}
        onOk={handleExportConfirm}
        onCancel={() => setIsExportModalVisible(false)}
        width={400}
      >
        <Form layout="vertical">
          <Form.Item
            label="报告格式"
          >
            <Select
              value={exportFormat}
              onChange={setExportFormat}
              style={{ width: '100%' }}
            >
              <Option value={ReportFormat.HTML}>HTML</Option>
              <Option value={ReportFormat.JSON}>JSON</Option>
              <Option value={ReportFormat.CSV}>CSV</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TestSessionManager;
