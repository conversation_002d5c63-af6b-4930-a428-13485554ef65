# 面部动画编辑器

面部动画编辑器是DL（Digital Learning）引擎中用于创建和编辑面部动画的工具。它提供了直观的界面，让用户可以轻松创建表情和口型动画，并支持时间轴编辑、关键帧编辑和曲线编辑等功能。

## 主要功能

- 时间轴编辑：在时间轴上直观地编辑动画
- 关键帧编辑：精确控制关键帧的属性
- 曲线编辑：通过曲线调整动画的过渡效果
- 实时预览：实时查看动画效果
- 导入/导出：支持导入和导出动画数据
- GPU加速：使用GPU加速面部动画计算，提高性能

## 编辑器界面

面部动画编辑器界面由以下几个主要部分组成：

1. **预览区域**：显示当前动画的实时预览
2. **时间轴面板**：显示动画的时间轴和关键帧
3. **关键帧编辑面板**：编辑选中关键帧的属性
4. **曲线编辑面板**：通过曲线调整动画的过渡效果
5. **工具栏**：提供各种工具和操作按钮

### 预览区域

预览区域显示当前动画的实时预览，支持以下功能：

- 旋转、缩放和平移视图
- 切换显示模式（默认、线框等）
- 截图
- 全屏预览

### 时间轴面板

时间轴面板显示动画的时间轴和关键帧，支持以下功能：

- 添加、删除和移动关键帧
- 添加和删除轨道
- 缩放时间轴
- 播放、暂停和跳转动画

### 关键帧编辑面板

关键帧编辑面板用于编辑选中关键帧的属性，支持以下功能：

- 编辑关键帧的时间
- 编辑表情类型和权重
- 编辑口型类型和权重
- 编辑缓动类型

### 曲线编辑面板

曲线编辑面板用于通过曲线调整动画的过渡效果，支持以下功能：

- 编辑动画曲线
- 添加和删除控制点
- 调整曲线的形状
- 预设曲线类型

## 基本用法

### 创建面部动画编辑器

```typescript
import { FacialAnimationEditorSystem, FacialAnimationEditorComponent } from '../../animation';

// 创建面部动画编辑器系统
const editorSystem = new FacialAnimationEditorSystem(world, {
  debug: true,
  defaultFrameRate: 30,
  defaultDuration: 5.0
});

// 添加系统到世界
world.addSystem(editorSystem);

// 创建面部动画编辑器组件
const editor = editorSystem.createEditor(characterEntity);
```

### 创建动画片段

```typescript
// 创建动画片段
const clip = editor.createClip('happy', 5.0, true);

// 添加表情关键帧
editor.addKeyframe(0.0, {
  expression: FacialExpressionType.NEUTRAL,
  expressionWeight: 0.0
});

editor.addKeyframe(2.5, {
  expression: FacialExpressionType.HAPPY,
  expressionWeight: 1.0
});

editor.addKeyframe(5.0, {
  expression: FacialExpressionType.NEUTRAL,
  expressionWeight: 0.0
});

// 添加口型关键帧
editor.addKeyframe(1.0, {
  viseme: VisemeType.AA,
  visemeWeight: 1.0
});

editor.addKeyframe(3.0, {
  viseme: VisemeType.OH,
  visemeWeight: 1.0
});
```

### 播放动画

```typescript
// 设置当前片段
editor.setCurrentClip('happy');

// 播放动画
editor.play();

// 暂停动画
editor.pause();

// 停止动画
editor.stop();

// 设置当前时间
editor.setCurrentTime(2.5);
```

### 导出和导入动画

```typescript
// 导出动画为JSON
const json = editorSystem.exportClipToJSON(characterEntity, 'happy');

// 导入动画
const importedClip = editorSystem.importClipFromJSON(characterEntity, json);
```

## 高级功能

### GPU加速

面部动画编辑器支持使用GPU加速面部动画计算，提高性能。

```typescript
import { GPUFacialAnimationSystem } from '../../animation';

// 创建GPU面部动画系统
const gpuSystem = new GPUFacialAnimationSystem(world, {
  debug: true,
  useComputeShader: false
});

// 添加系统到世界
world.addSystem(gpuSystem);

// 创建GPU面部动画组件
const gpuComponent = gpuSystem.createGPUFacialAnimation(characterEntity, skinnedMesh);
```

### 事件监听

面部动画编辑器支持事件监听，可以在动画播放过程中执行自定义操作。

```typescript
// 监听时间变化事件
editor.addEventListener('timeChanged', (data) => {
  console.log(`当前时间: ${data.time}`);
});

// 监听播放完成事件
editor.addEventListener('completed', () => {
  console.log('动画播放完成');
});

// 监听循环事件
editor.addEventListener('looped', (data) => {
  console.log(`动画循环: ${data.clip.name}`);
});
```

### 自定义缓动函数

面部动画编辑器支持自定义缓动函数，可以创建更加丰富的动画效果。

```typescript
// 添加自定义缓动函数
editor.addEasingFunction('customEasing', (t) => {
  // 自定义缓动函数
  return t * t * (3 - 2 * t);
});

// 使用自定义缓动函数
editor.addKeyframe(2.0, {
  expression: FacialExpressionType.HAPPY,
  expressionWeight: 1.0,
  easing: 'customEasing'
});
```

## 与其他系统集成

### 与面部动画系统集成

面部动画编辑器可以与面部动画系统集成，实现更加复杂的面部动画效果。

```typescript
import { FacialAnimationSystem } from '../../animation';

// 创建面部动画系统
const facialAnimationSystem = new FacialAnimationSystem(world, {
  debug: true
});

// 添加系统到世界
world.addSystem(facialAnimationSystem);

// 创建面部动画组件
const facialAnimation = facialAnimationSystem.createFacialAnimation(characterEntity);

// 将面部动画组件与模型绑定
facialAnimationSystem.linkToModel(characterEntity, skinnedMesh);
```

### 与口型同步系统集成

面部动画编辑器可以与口型同步系统集成，实现基于音频的口型动画。

```typescript
import { LipSyncSystem } from '../../animation';

// 创建口型同步系统
const lipSyncSystem = new LipSyncSystem(world, {
  debug: true,
  fftSize: 1024,
  volumeThreshold: 0.01
});

// 添加系统到世界
world.addSystem(lipSyncSystem);

// 创建口型同步组件
const lipSync = lipSyncSystem.createLipSync(characterEntity);

// 启动口型同步跟踪
lipSyncSystem.startTracking(audioElement);
```

## 性能优化

### 使用GPU加速

使用GPU加速可以显著提高面部动画的性能，特别是对于复杂模型和大量角色的场景。

```typescript
// 创建GPU面部动画系统
const gpuSystem = new GPUFacialAnimationSystem(world, {
  debug: true,
  useComputeShader: true,  // 如果支持，使用计算着色器
  maxBlendShapes: 32,      // 设置最大混合形状数量
  textureSize: 16          // 设置纹理大小
});
```

### 优化音频分析

优化音频分析可以减少CPU使用率，提高口型同步的性能。

```typescript
// 创建口型同步系统
const lipSyncSystem = new LipSyncSystem(world, {
  debug: true,
  fftSize: 512,            // 降低FFT大小
  analysisInterval: 100,   // 设置分析间隔（毫秒）
  useWorker: true,         // 使用工作线程
  numFrequencyBands: 8     // 设置频率带数量
});
```

## 示例

完整的面部动画编辑器示例可以在 `examples/animation/FacialAnimationEditorExample.ts` 中找到。

```typescript
// 创建示例
const example = new FacialAnimationEditorExample();

// 启动示例
example.start();
```

## 最佳实践

1. **使用适当的关键帧数量**：过多的关键帧会增加计算负担，但过少的关键帧可能无法表达复杂的动画。
2. **使用合适的缓动函数**：不同的缓动函数会产生不同的动画效果，选择合适的缓动函数可以使动画更加自然。
3. **优化性能**：对于复杂场景，使用GPU加速和优化音频分析可以显著提高性能。
4. **使用事件监听**：通过事件监听可以在动画播放过程中执行自定义操作，实现更加复杂的交互效果。
5. **导出和备份动画**：定期导出和备份动画数据，避免数据丢失。
