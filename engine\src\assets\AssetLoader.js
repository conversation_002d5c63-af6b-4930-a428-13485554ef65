"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetLoader = void 0;
/**
 * 资产加载器类
 * 负责加载各种类型的资产
 */
var THREE = require("three");
var GLTFLoader_1 = require("three/examples/jsm/loaders/GLTFLoader");
var FBXLoader_1 = require("three/examples/jsm/loaders/FBXLoader");
var OBJLoader_1 = require("three/examples/jsm/loaders/OBJLoader");
var FontLoader_1 = require("three/examples/jsm/loaders/FontLoader");
var ResourceManager_1 = require("./ResourceManager");
var AssetLoader = /** @class */ (function () {
    /**
     * 创建资产加载器实例
     */
    function AssetLoader() {
        // 创建加载管理器
        this.manager = new THREE.LoadingManager();
        // 创建各种加载器
        this.textureLoader = new THREE.TextureLoader(this.manager);
        this.gltfLoader = new GLTFLoader_1.GLTFLoader(this.manager);
        this.fbxLoader = new FBXLoader_1.FBXLoader(this.manager);
        this.objLoader = new OBJLoader_1.OBJLoader(this.manager);
        this.cubeTextureLoader = new THREE.CubeTextureLoader(this.manager);
        this.audioLoader = new THREE.AudioLoader(this.manager);
        this.fontLoader = new FontLoader_1.FontLoader(this.manager);
        this.fileLoader = new THREE.FileLoader(this.manager);
    }
    /**
     * 加载资产
     * @param type 资产类型
     * @param url 资产URL
     * @returns Promise，解析为加载的资产数据
     */
    AssetLoader.prototype.load = function (type, url) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (type) {
                    case ResourceManager_1.AssetType.TEXTURE:
                        return [2 /*return*/, this.loadTexture(url)];
                    case ResourceManager_1.AssetType.MODEL:
                        return [2 /*return*/, this.loadModel(url)];
                    case ResourceManager_1.AssetType.AUDIO:
                        return [2 /*return*/, this.loadAudio(url)];
                    case ResourceManager_1.AssetType.FONT:
                        return [2 /*return*/, this.loadFont(url)];
                    case ResourceManager_1.AssetType.JSON:
                        return [2 /*return*/, this.loadJSON(url)];
                    case ResourceManager_1.AssetType.TEXT:
                        return [2 /*return*/, this.loadText(url)];
                    case ResourceManager_1.AssetType.BINARY:
                        return [2 /*return*/, this.loadBinary(url)];
                    case ResourceManager_1.AssetType.CUBEMAP:
                        return [2 /*return*/, this.loadCubeTexture(url)];
                    default:
                        throw new Error("\u4E0D\u652F\u6301\u7684\u8D44\u4EA7\u7C7B\u578B: ".concat(type));
                }
                return [2 /*return*/];
            });
        });
    };
    /**
     * 加载纹理
     * @param url 纹理URL
     * @returns Promise，解析为加载的纹理
     */
    AssetLoader.prototype.loadTexture = function (url) {
        var _this = this;
        return new Promise(function (resolve, reject) {
            _this.textureLoader.load(url, function (texture) {
                // 设置纹理属性
                texture.colorSpace = THREE.SRGBColorSpace;
                texture.needsUpdate = true;
                resolve(texture);
            }, undefined, function (error) { return reject(new Error("\u52A0\u8F7D\u7EB9\u7406\u5931\u8D25: ".concat(error.message))); });
        });
    };
    /**
     * 加载模型
     * @param url 模型URL
     * @returns Promise，解析为加载的模型
     */
    AssetLoader.prototype.loadModel = function (url) {
        var _a;
        // 根据文件扩展名选择加载器
        var extension = (_a = url.split('.').pop()) === null || _a === void 0 ? void 0 : _a.toLowerCase();
        switch (extension) {
            case 'gltf':
            case 'glb':
                return this.loadGLTF(url);
            case 'fbx':
                return this.loadFBX(url);
            case 'obj':
                return this.loadOBJ(url);
            default:
                throw new Error("\u4E0D\u652F\u6301\u7684\u6A21\u578B\u683C\u5F0F: ".concat(extension));
        }
    };
    /**
     * 加载GLTF模型
     * @param url GLTF模型URL
     * @returns Promise，解析为加载的GLTF模型
     */
    AssetLoader.prototype.loadGLTF = function (url) {
        var _this = this;
        return new Promise(function (resolve, reject) {
            _this.gltfLoader.load(url, function (gltf) { return resolve(gltf); }, undefined, function (error) { return reject(new Error("\u52A0\u8F7DGLTF\u6A21\u578B\u5931\u8D25: ".concat(error.message))); });
        });
    };
    /**
     * 加载FBX模型
     * @param url FBX模型URL
     * @returns Promise，解析为加载的FBX模型
     */
    AssetLoader.prototype.loadFBX = function (url) {
        var _this = this;
        return new Promise(function (resolve, reject) {
            _this.fbxLoader.load(url, function (fbx) { return resolve(fbx); }, undefined, function (error) { return reject(new Error("\u52A0\u8F7DFBX\u6A21\u578B\u5931\u8D25: ".concat(error.message))); });
        });
    };
    /**
     * 加载OBJ模型
     * @param url OBJ模型URL
     * @returns Promise，解析为加载的OBJ模型
     */
    AssetLoader.prototype.loadOBJ = function (url) {
        var _this = this;
        return new Promise(function (resolve, reject) {
            _this.objLoader.load(url, function (obj) { return resolve(obj); }, undefined, function (error) { return reject(new Error("\u52A0\u8F7DOBJ\u6A21\u578B\u5931\u8D25: ".concat(error.message))); });
        });
    };
    /**
     * 加载音频
     * @param url 音频URL
     * @returns Promise，解析为加载的音频数据
     */
    AssetLoader.prototype.loadAudio = function (url) {
        var _this = this;
        return new Promise(function (resolve, reject) {
            _this.audioLoader.load(url, function (buffer) { return resolve(buffer); }, undefined, function (error) { return reject(new Error("\u52A0\u8F7D\u97F3\u9891\u5931\u8D25: ".concat(error.message))); });
        });
    };
    /**
     * 加载字体
     * @param url 字体URL
     * @returns Promise，解析为加载的字体
     */
    AssetLoader.prototype.loadFont = function (url) {
        var _this = this;
        return new Promise(function (resolve, reject) {
            _this.fontLoader.load(url, function (font) { return resolve(font); }, undefined, function (error) { return reject(new Error("\u52A0\u8F7D\u5B57\u4F53\u5931\u8D25: ".concat(error.message))); });
        });
    };
    /**
     * 加载JSON
     * @param url JSON URL
     * @returns Promise，解析为加载的JSON数据
     */
    AssetLoader.prototype.loadJSON = function (url) {
        var _this = this;
        return new Promise(function (resolve, reject) {
            _this.fileLoader.setResponseType('json');
            _this.fileLoader.load(url, function (data) { return resolve(data); }, undefined, function (error) { return reject(new Error("\u52A0\u8F7DJSON\u5931\u8D25: ".concat(error.message))); });
        });
    };
    /**
     * 加载文本
     * @param url 文本URL
     * @returns Promise，解析为加载的文本
     */
    AssetLoader.prototype.loadText = function (url) {
        var _this = this;
        return new Promise(function (resolve, reject) {
            _this.fileLoader.setResponseType('text');
            _this.fileLoader.load(url, function (data) { return resolve(data); }, undefined, function (error) { return reject(new Error("\u52A0\u8F7D\u6587\u672C\u5931\u8D25: ".concat(error.message))); });
        });
    };
    /**
     * 加载二进制数据
     * @param url 二进制数据URL
     * @returns Promise，解析为加载的二进制数据
     */
    AssetLoader.prototype.loadBinary = function (url) {
        var _this = this;
        return new Promise(function (resolve, reject) {
            _this.fileLoader.setResponseType('arraybuffer');
            _this.fileLoader.load(url, function (data) { return resolve(data); }, undefined, function (error) { return reject(new Error("\u52A0\u8F7D\u4E8C\u8FDB\u5236\u6570\u636E\u5931\u8D25: ".concat(error.message))); });
        });
    };
    /**
     * 加载立方体纹理
     * @param urls 立方体纹理URL数组（顺序：右、左、上、下、前、后）
     * @returns Promise，解析为加载的立方体纹理
     */
    AssetLoader.prototype.loadCubeTexture = function (urls) {
        var _this = this;
        // 如果是单个URL，解析为数组
        var urlArray = typeof urls === 'string' ? JSON.parse(urls) : urls;
        if (!Array.isArray(urlArray) || urlArray.length !== 6) {
            throw new Error('立方体纹理需要6个面的贴图路径');
        }
        return new Promise(function (resolve, reject) {
            _this.cubeTextureLoader.load(urlArray, function (texture) {
                // 设置纹理属性
                texture.colorSpace = THREE.SRGBColorSpace;
                resolve(texture);
            }, undefined, function (error) { return reject(new Error("\u52A0\u8F7D\u7ACB\u65B9\u4F53\u7EB9\u7406\u5931\u8D25: ".concat(error.message))); });
        });
    };
    /**
     * 获取加载管理器
     * @returns 加载管理器
     */
    AssetLoader.prototype.getManager = function () {
        return this.manager;
    };
    /**
     * 设置加载基础路径
     * @param path 基础路径
     */
    AssetLoader.prototype.setPath = function (path) {
        this.textureLoader.setPath(path);
        this.gltfLoader.setPath(path);
        this.fbxLoader.setPath(path);
        this.objLoader.setPath(path);
        this.cubeTextureLoader.setPath(path);
        this.audioLoader.setPath(path);
        this.fontLoader.setPath(path);
        this.fileLoader.setPath(path);
    };
    /**
     * 设置跨域
     * @param crossOrigin 跨域设置
     */
    AssetLoader.prototype.setCrossOrigin = function (crossOrigin) {
        this.textureLoader.setCrossOrigin(crossOrigin);
        this.cubeTextureLoader.setCrossOrigin(crossOrigin);
        this.fileLoader.setCrossOrigin(crossOrigin);
    };
    /**
     * 销毁加载器
     */
    AssetLoader.prototype.dispose = function () {
        // 清除加载管理器的事件监听器
        this.manager.onStart = function () { };
        this.manager.onLoad = function () { };
        this.manager.onProgress = function () { };
        this.manager.onError = function () { };
    };
    return AssetLoader;
}());
exports.AssetLoader = AssetLoader;
