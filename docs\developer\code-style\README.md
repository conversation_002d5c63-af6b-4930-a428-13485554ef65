# DL（Digital Learning）引擎编辑器代码风格指南

## 概述

本文档规定了DL（Digital Learning）引擎编辑器项目的代码风格规范，旨在保持代码的一致性和可维护性。所有参与项目开发的开发者都应遵循这些规范。

## 目录

- [基本原则](#基本原则)
- [文件组织](#文件组织)
- [命名约定](#命名约定)
- [代码格式化](#代码格式化)
- [TypeScript规范](#typescript规范)
- [React规范](#react规范)
- [Redux规范](#redux规范)
- [注释规范](#注释规范)
- [导入规范](#导入规范)
- [测试规范](#测试规范)
- [工具配置](#工具配置)

## 基本原则

1. **可读性优先**：代码应该易于阅读和理解。
2. **一致性**：在整个项目中保持一致的代码风格。
3. **简洁性**：代码应该简洁明了，避免不必要的复杂性。
4. **可维护性**：代码应该易于维护和扩展。
5. **可测试性**：代码应该易于测试。

## 文件组织

### 目录结构

```
src/
├── assets/             # 资源文件
├── components/         # 组件
│   ├── common/         # 通用组件
│   ├── layout/         # 布局组件
│   ├── panels/         # 面板组件
│   └── ...             # 其他组件
├── hooks/              # 自定义钩子
├── pages/              # 页面组件
├── services/           # 服务
├── store/              # Redux状态管理
│   ├── auth/           # 认证状态
│   ├── project/        # 项目状态
│   └── ...             # 其他状态
├── styles/             # 样式文件
├── types/              # 类型定义
├── utils/              # 工具函数
├── App.tsx             # 应用根组件
├── i18n.ts             # 国际化配置
└── main.tsx            # 入口文件
```

### 文件命名

- **组件文件**：使用PascalCase，如`Button.tsx`、`ScenePanel.tsx`。
- **非组件文件**：使用camelCase，如`engineService.ts`、`mathUtils.ts`。
- **样式文件**：与组件同名，使用`.less`或`.css`扩展名，如`Button.less`、`ScenePanel.less`。
- **测试文件**：与被测试文件同名，添加`.test`或`.spec`后缀，如`Button.test.tsx`、`engineService.spec.ts`。
- **类型定义文件**：使用`.d.ts`扩展名，如`engine.d.ts`。

### 文件内容

- 每个文件应该只包含一个主要的组件、类或功能。
- 相关的辅助组件、类或函数可以放在同一个文件中。
- 文件不应过长，通常不超过500行。如果文件过长，应考虑拆分。

## 命名约定

### 通用命名规则

- 使用有意义的名称，避免使用缩写（除非是广泛接受的缩写，如`id`、`url`等）。
- 名称应该清晰地表达其用途或含义。
- 避免使用单个字母的名称（除了循环计数器或临时变量）。

### 特定命名规则

- **组件**：使用PascalCase，如`Button`、`ScenePanel`。
- **接口**：使用PascalCase，通常以`I`开头，如`IEngineOptions`、`IEntity`。
- **类型**：使用PascalCase，如`EngineOptions`、`Entity`。
- **枚举**：使用PascalCase，如`PanelType`、`ToolType`。
- **变量和函数**：使用camelCase，如`engineService`、`loadScene`。
- **常量**：使用大写下划线分隔，如`MAX_UNDO_STEPS`、`DEFAULT_OPTIONS`。
- **私有成员**：使用下划线前缀，如`_engine`、`_activeScene`。
- **布尔变量和函数**：使用`is`、`has`、`can`等前缀，如`isLoading`、`hasChanges`、`canUndo`。

## 代码格式化

DL（Digital Learning）引擎编辑器项目使用ESLint和Prettier进行代码格式化和检查。

### 基本规则

- 使用2个空格进行缩进。
- 行长度不超过100个字符。
- 使用单引号（`'`）而不是双引号（`"`）。
- 语句末尾使用分号（`;`）。
- 大括号始终使用Egyptian风格（左括号与语句在同一行）。
- 运算符前后、逗号后、冒号和分号后添加空格。
- 函数名和括号之间不添加空格。
- 对象和数组的最后一个元素后不添加逗号。

### 示例

```typescript
// 好的风格
function calculateArea(width: number, height: number): number {
  return width * height;
}

const person = {
  name: 'John',
  age: 30,
  address: {
    city: 'Beijing',
    country: 'China'
  }
};

const numbers = [1, 2, 3, 4, 5];

if (condition) {
  doSomething();
} else {
  doSomethingElse();
}

// 不好的风格
function calculateArea ( width:number,height:number ):number
{
  return width*height;
}

const person={name:"John",age:30,address:{city:"Beijing",country:"China"},};

const numbers=[1,2,3,4,5,];

if(condition){doSomething();}
else
{
  doSomethingElse();
}
```

## TypeScript规范

DL（Digital Learning）引擎编辑器项目使用TypeScript开发，应遵循以下TypeScript规范：

### 类型定义

- 尽可能使用TypeScript的类型系统，避免使用`any`类型。
- 为函数参数和返回值添加类型注解。
- 使用接口定义对象结构，使用类型别名定义联合类型和交叉类型。
- 使用枚举定义有限的选项集合。
- 使用泛型提高代码的复用性和类型安全性。

### 示例

```typescript
// 好的风格
interface Person {
  name: string;
  age: number;
  address?: Address;
}

interface Address {
  city: string;
  country: string;
}

type ID = string | number;

enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  GUEST = 'guest'
}

function getUser<T extends ID>(id: T): Promise<User> {
  return fetch(`/api/users/${id}`).then(response => response.json());
}

// 不好的风格
function getUser(id): any {
  return fetch('/api/users/' + id).then(response => response.json());
}
```

## React规范

DL（Digital Learning）引擎编辑器项目使用React开发，应遵循以下React规范：

### 组件定义

- 使用函数组件和Hooks，而不是类组件。
- 使用TypeScript的类型系统定义组件的props和state。
- 使用解构赋值获取props和state。
- 使用React.memo优化组件性能。

### Hooks使用

- 遵循Hooks的使用规则，不在条件语句、循环或嵌套函数中调用Hooks。
- 使用自定义Hooks封装复杂的逻辑。
- 使用useCallback和useMemo优化性能。
- 使用useEffect处理副作用，并正确指定依赖数组。

### 示例

```typescript
// 好的风格
import React, { useState, useEffect, useCallback } from 'react';

interface ButtonProps {
  text: string;
  onClick: () => void;
  disabled?: boolean;
}

const Button: React.FC<ButtonProps> = ({ text, onClick, disabled = false }) => {
  const handleClick = useCallback(() => {
    if (!disabled) {
      onClick();
    }
  }, [onClick, disabled]);

  return (
    <button
      className="button"
      onClick={handleClick}
      disabled={disabled}
    >
      {text}
    </button>
  );
};

export default React.memo(Button);

// 不好的风格
class Button extends React.Component {
  render() {
    return (
      <button
        className="button"
        onClick={this.props.onClick}
        disabled={this.props.disabled}
      >
        {this.props.text}
      </button>
    );
  }
}
```

## Redux规范

DL（Digital Learning）引擎编辑器项目使用Redux进行状态管理，应遵循以下Redux规范：

### 状态设计

- 使用Redux Toolkit简化Redux的使用。
- 按功能域划分状态切片。
- 状态应该是规范化的，避免嵌套和冗余。
- 使用选择器（selectors）访问状态。

### Action和Reducer

- 使用createSlice创建状态切片，包括reducer和action creators。
- Action类型应该是描述性的，使用`domain/eventName`格式。
- Reducer应该是纯函数，不应该有副作用。
- 使用immer进行不可变更新（Redux Toolkit内置）。

### 异步操作

- 使用createAsyncThunk处理异步操作。
- 处理异步操作的三种状态：pending、fulfilled和rejected。
- 使用extraReducers处理异步action。

### 示例

```typescript
// 好的风格
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';
import { ProjectService } from '../../services/ProjectService';

export interface Project {
  id: string;
  name: string;
  description: string;
}

interface ProjectState {
  projects: Record<string, Project>;
  currentProjectId: string | null;
  loading: boolean;
  error: string | null;
}

const initialState: ProjectState = {
  projects: {},
  currentProjectId: null,
  loading: false,
  error: null,
};

export const fetchProjects = createAsyncThunk(
  'project/fetchProjects',
  async (_, { rejectWithValue }) => {
    try {
      const projectService = ProjectService.getInstance();
      const projects = await projectService.getProjects();
      return projects;
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const projectSlice = createSlice({
  name: 'project',
  initialState,
  reducers: {
    setCurrentProject: (state, action: PayloadAction<string>) => {
      state.currentProjectId = action.payload;
    },
    addProject: (state, action: PayloadAction<Project>) => {
      state.projects[action.payload.id] = action.payload;
    },
    removeProject: (state, action: PayloadAction<string>) => {
      delete state.projects[action.payload];
      if (state.currentProjectId === action.payload) {
        state.currentProjectId = null;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchProjects.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProjects.fulfilled, (state, action) => {
        state.loading = false;
        state.projects = action.payload.reduce((acc, project) => {
          acc[project.id] = project;
          return acc;
        }, {} as Record<string, Project>);
      })
      .addCase(fetchProjects.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setCurrentProject, addProject, removeProject } = projectSlice.actions;

export const selectProjects = (state: RootState) => Object.values(state.project.projects);
export const selectCurrentProjectId = (state: RootState) => state.project.currentProjectId;
export const selectCurrentProject = (state: RootState) => {
  const currentProjectId = state.project.currentProjectId;
  return currentProjectId ? state.project.projects[currentProjectId] : null;
};
export const selectProjectsLoading = (state: RootState) => state.project.loading;
export const selectProjectsError = (state: RootState) => state.project.error;

export default projectSlice.reducer;
```

## 注释规范

DL（Digital Learning）引擎编辑器项目使用JSDoc风格的注释，详细的注释规范请参考[注释规范](./comments.md)。

## 导入规范

### 导入顺序

导入语句应按以下顺序排列：

1. 外部库和框架（如React、Redux、Ant Design等）
2. 内部模块（按相对路径深度排序）
3. 样式文件

每组导入之间应有一个空行。

### 示例

```typescript
// 好的风格
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Button, Input, Select } from 'antd';

import { RootState } from '../../store';
import { fetchProjects, selectProjects } from '../../store/project/projectSlice';
import { EngineService } from '../../services/EngineService';
import { ProjectCard } from '../common/ProjectCard';

import './ProjectList.less';

// 不好的风格
import './ProjectList.less';
import { ProjectCard } from '../common/ProjectCard';
import { Button, Input, Select } from 'antd';
import { EngineService } from '../../services/EngineService';
import React, { useState, useEffect } from 'react';
import { fetchProjects, selectProjects } from '../../store/project/projectSlice';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
```

## 测试规范

DL（Digital Learning）引擎编辑器项目使用Jest和React Testing Library进行测试，应遵循以下测试规范：

### 测试文件组织

- 测试文件应与被测试文件放在同一目录下，使用`.test.tsx`或`.spec.tsx`后缀。
- 测试文件应该按照被测试文件的结构组织。

### 测试命名

- 测试套件（describe）应该使用被测试模块的名称。
- 测试用例（it/test）应该清晰地描述测试的行为和预期结果。

### 测试内容

- 组件测试应该测试渲染、交互和状态变化。
- 服务测试应该测试方法的行为和返回值。
- Redux测试应该测试reducer、action creator和选择器。

### 示例

```typescript
// Button.test.tsx
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import Button from './Button';

describe('Button', () => {
  it('renders with text', () => {
    render(<Button text="Click me" onClick={() => {}} />);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button text="Click me" onClick={handleClick} />);
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('is disabled when disabled prop is true', () => {
    render(<Button text="Click me" onClick={() => {}} disabled />);
    expect(screen.getByText('Click me')).toBeDisabled();
  });
});
```

## 工具配置

DL（Digital Learning）引擎编辑器项目使用以下工具进行代码风格检查和格式化：

- **ESLint**：代码风格检查
- **Prettier**：代码格式化
- **TypeScript**：类型检查
- **Husky**：Git钩子
- **lint-staged**：对暂存的文件运行linter

详细的工具配置请参考项目根目录下的配置文件：

- `.eslintrc.js`：ESLint配置
- `.prettierrc`：Prettier配置
- `tsconfig.json`：TypeScript配置
- `.husky/`：Husky配置
- `package.json`中的`lint-staged`字段：lint-staged配置
