/**
 * 批处理器
 * 用于批量处理AI模型请求，提高性能
 */
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 批处理请求
 */
export interface BatchRequest<T, R> {
  /** 请求ID */
  id: string;
  /** 输入数据 */
  input: T;
  /** 选项 */
  options?: any;
  /** 解析回调 */
  resolve: (result: R) => void;
  /** 拒绝回调 */
  reject: (error: Error) => void;
  /** 提交时间 */
  submitTime: number;
}

/**
 * 批处理配置
 */
export interface BatchProcessorConfig {
  /** 最大批处理大小 */
  maxBatchSize?: number;
  /** 最大等待时间（毫秒） */
  maxWaitTime?: number;
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否启用动态批处理大小 */
  dynamicBatchSize?: boolean;
  /** 最小批处理大小 */
  minBatchSize?: number;
  /** 性能监控窗口大小 */
  performanceWindowSize?: number;
  /** 批处理超时时间（毫秒） */
  batchTimeout?: number;
  /** 最大队列大小 */
  maxQueueSize?: number;
}

/**
 * 批处理性能统计
 */
export interface BatchPerformanceStats {
  /** 批处理总数 */
  batchCount: number;
  /** 请求总数 */
  requestCount: number;
  /** 平均批处理大小 */
  averageBatchSize: number;
  /** 平均处理时间（毫秒） */
  averageProcessTime: number;
  /** 平均等待时间（毫秒） */
  averageWaitTime: number;
  /** 当前队列大小 */
  queueSize: number;
  /** 当前批处理大小 */
  currentBatchSize: number;
  /** 最大批处理大小 */
  maxBatchSize: number;
  /** 最大等待时间（毫秒） */
  maxWaitTime: number;
}

/**
 * 批处理器
 */
export class BatchProcessor<T, R> {
  /** 配置 */
  private config: Required<BatchProcessorConfig>;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: Required<BatchProcessorConfig> = {
    maxBatchSize: 16,
    maxWaitTime: 100,
    debug: false,
    dynamicBatchSize: true,
    minBatchSize: 1,
    performanceWindowSize: 10,
    batchTimeout: 5000,
    maxQueueSize: 1000
  };

  /** 请求队列 */
  private queue: BatchRequest<T, R>[] = [];

  /** 处理函数 */
  private processFn: (inputs: T[], options?: any[]) => Promise<R[]>;

  /** 是否正在处理 */
  private isProcessing: boolean = false;

  /** 定时器 */
  private timer: NodeJS.Timeout | null = null;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 批处理计数 */
  private batchCount: number = 0;

  /** 请求计数 */
  private requestCount: number = 0;

  /** 处理时间历史 */
  private processTimeHistory: number[] = [];

  /** 等待时间历史 */
  private waitTimeHistory: number[] = [];

  /** 批处理大小历史 */
  private batchSizeHistory: number[] = [];

  /** 当前批处理大小 */
  private currentBatchSize: number;

  /**
   * 构造函数
   * @param processFn 处理函数
   * @param config 配置
   */
  constructor(
    processFn: (inputs: T[], options?: any[]) => Promise<R[]>,
    config: BatchProcessorConfig = {}
  ) {
    this.processFn = processFn;

    this.config = {
      ...BatchProcessor.DEFAULT_CONFIG,
      ...config
    };

    this.currentBatchSize = this.config.maxBatchSize;
  }

  /**
   * 提交请求
   * @param input 输入数据
   * @param options 选项
   * @returns 处理结果
   */
  public async process(input: T, options?: any): Promise<R> {
    // 检查队列是否已满
    if (this.queue.length >= this.config.maxQueueSize) {
      throw new Error('批处理队列已满');
    }

    // 创建请求
    return new Promise<R>((resolve, reject) => {
      const request: BatchRequest<T, R> = {
        id: `batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        input,
        options,
        resolve,
        reject,
        submitTime: Date.now()
      };

      // 添加到队列
      this.queue.push(request);
      this.requestCount++;

      // 如果队列长度达到批处理大小，立即处理
      if (this.queue.length >= this.currentBatchSize) {
        this.processBatch();
      } else if (!this.timer) {
        // 否则，设置定时器
        this.timer = setTimeout(() => {
          this.processBatch();
        }, this.config.maxWaitTime);
      }
    });
  }

  /**
   * 处理批次
   */
  private async processBatch(): Promise<void> {
    // 如果队列为空或正在处理，直接返回
    if (this.queue.length === 0 || this.isProcessing) {
      return;
    }

    // 清除定时器
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }

    // 设置处理状态
    this.isProcessing = true;

    // 获取批次大小
    const batchSize = Math.min(this.queue.length, this.currentBatchSize);

    // 获取批次请求
    const batch = this.queue.splice(0, batchSize);

    // 提取输入和选项
    const inputs = batch.map(request => request.input);
    const options = batch.map(request => request.options);

    // 记录批处理大小
    this.batchSizeHistory.push(batchSize);
    if (this.batchSizeHistory.length > this.config.performanceWindowSize) {
      this.batchSizeHistory.shift();
    }

    // 记录等待时间
    const now = Date.now();
    const waitTimes = batch.map(request => now - request.submitTime);
    const averageWaitTime = waitTimes.reduce((sum, time) => sum + time, 0) / waitTimes.length;
    this.waitTimeHistory.push(averageWaitTime);
    if (this.waitTimeHistory.length > this.config.performanceWindowSize) {
      this.waitTimeHistory.shift();
    }

    // 设置批处理超时
    const timeoutId = setTimeout(() => {
      // 如果批处理超时，拒绝所有请求
      batch.forEach(request => {
        request.reject(new Error('批处理超时'));
      });

      // 重置处理状态
      this.isProcessing = false;

      // 如果队列不为空，继续处理
      if (this.queue.length > 0) {
        this.processBatch();
      }
    }, this.config.batchTimeout);

    try {
      // 处理批次
      const startTime = Date.now();
      const results = await this.processFn(inputs, options);
      const endTime = Date.now();

      // 清除超时
      clearTimeout(timeoutId);

      // 记录处理时间
      const processTime = endTime - startTime;
      this.processTimeHistory.push(processTime);
      if (this.processTimeHistory.length > this.config.performanceWindowSize) {
        this.processTimeHistory.shift();
      }

      // 更新批处理计数
      this.batchCount++;

      // 如果启用动态批处理大小，调整批处理大小
      if (this.config.dynamicBatchSize) {
        this.adjustBatchSize(processTime, batchSize);
      }

      // 如果结果数量与请求数量不匹配，拒绝所有请求
      if (results.length !== batch.length) {
        batch.forEach(request => {
          request.reject(new Error('批处理结果数量与请求数量不匹配'));
        });
      } else {
        // 否则，解析每个请求
        batch.forEach((request, index) => {
          request.resolve(results[index]);
        });
      }

      // 触发批处理完成事件
      this.eventEmitter.emit('batchComplete', {
        batchSize,
        processTime,
        waitTime: averageWaitTime
      });

      // 如果启用调试，输出日志
      if (this.config.debug) {
        console.log(`[BatchProcessor] 批处理完成: ${batchSize}个请求, 处理时间: ${processTime}ms, 等待时间: ${averageWaitTime.toFixed(2)}ms`);
      }
    } catch (error) {
      // 清除超时
      clearTimeout(timeoutId);

      // 拒绝所有请求
      batch.forEach(request => {
        request.reject(error as Error);
      });

      // 触发批处理错误事件
      this.eventEmitter.emit('batchError', {
        error,
        batchSize
      });

      // 如果启用调试，输出错误日志
      if (this.config.debug) {
        console.error(`[BatchProcessor] 批处理错误: ${error}`);
      }
    } finally {
      // 重置处理状态
      this.isProcessing = false;

      // 如果队列不为空，继续处理
      if (this.queue.length > 0) {
        this.processBatch();
      }
    }
  }

  /**
   * 调整批处理大小
   * @param processTime 处理时间
   * @param batchSize 批处理大小
   */
  private adjustBatchSize(processTime: number, batchSize: number): void {
    // 如果处理时间历史不足，直接返回
    if (this.processTimeHistory.length < this.config.performanceWindowSize) {
      return;
    }

    // 计算平均处理时间
    const averageProcessTime = this.processTimeHistory.reduce((sum, time) => sum + time, 0) / this.processTimeHistory.length;

    // 计算平均等待时间（虽然当前未使用，但保留用于未来扩展）
    // const averageWaitTime = this.waitTimeHistory.reduce((sum, time) => sum + time, 0) / this.waitTimeHistory.length;

    // 如果平均处理时间小于最大等待时间的一半，增加批处理大小
    if (averageProcessTime < this.config.maxWaitTime / 2 && this.currentBatchSize < this.config.maxBatchSize) {
      this.currentBatchSize = Math.min(this.currentBatchSize * 1.5, this.config.maxBatchSize);

      if (this.config.debug) {
        console.log(`[BatchProcessor] 增加批处理大小: ${this.currentBatchSize}`);
      }
    }
    // 如果平均处理时间大于最大等待时间，减小批处理大小
    else if (averageProcessTime > this.config.maxWaitTime && this.currentBatchSize > this.config.minBatchSize) {
      this.currentBatchSize = Math.max(this.currentBatchSize / 1.5, this.config.minBatchSize);

      if (this.config.debug) {
        console.log(`[BatchProcessor] 减小批处理大小: ${this.currentBatchSize}`);
      }
    }
  }

  /**
   * 获取性能统计
   * @returns 性能统计
   */
  public getPerformanceStats(): BatchPerformanceStats {
    // 计算平均批处理大小
    const averageBatchSize = this.batchSizeHistory.length > 0
      ? this.batchSizeHistory.reduce((sum, size) => sum + size, 0) / this.batchSizeHistory.length
      : 0;

    // 计算平均处理时间
    const averageProcessTime = this.processTimeHistory.length > 0
      ? this.processTimeHistory.reduce((sum, time) => sum + time, 0) / this.processTimeHistory.length
      : 0;

    // 计算平均等待时间
    const averageWaitTime = this.waitTimeHistory.length > 0
      ? this.waitTimeHistory.reduce((sum, time) => sum + time, 0) / this.waitTimeHistory.length
      : 0;

    return {
      batchCount: this.batchCount,
      requestCount: this.requestCount,
      averageBatchSize,
      averageProcessTime,
      averageWaitTime,
      queueSize: this.queue.length,
      currentBatchSize: this.currentBatchSize,
      maxBatchSize: this.config.maxBatchSize,
      maxWaitTime: this.config.maxWaitTime
    };
  }

  /**
   * 监听事件
   * @param event 事件名称
   * @param listener 监听器
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 取消监听事件
   * @param event 事件名称
   * @param listener 监听器
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 清空队列
   */
  public clearQueue(): void {
    // 拒绝所有请求
    this.queue.forEach(request => {
      request.reject(new Error('批处理队列已清空'));
    });

    // 清空队列
    this.queue = [];

    // 清除定时器
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }

  /**
   * 销毁
   */
  public dispose(): void {
    this.clearQueue();
    this.eventEmitter.removeAllListeners();
  }
}
