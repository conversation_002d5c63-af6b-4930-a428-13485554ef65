/**
 * 测试报告生成器
 * 用于生成用户测试报告
 */
import { TestSession, TestTask, UserFeedback } from '../../services/UserTestingService';
import { UserAction, UserActionType } from './UserBehaviorAnalyzer';

/**
 * 测试报告接口
 */
export interface TestReport {
  /** 报告ID */
  id: string;
  /** 会话ID */
  sessionId: string;
  /** 用户ID */
  userId: string;
  /** 用户名 */
  userName: string;
  /** 生成时间 */
  generatedAt: number;
  /** 会话开始时间 */
  sessionStartTime: number;
  /** 会话结束时间 */
  sessionEndTime: number;
  /** 会话总时长（毫秒） */
  sessionDuration: number;
  /** 任务统计 */
  taskStats: {
    /** 总任务数 */
    total: number;
    /** 已完成任务数 */
    completed: number;
    /** 完成率 */
    completionRate: number;
    /** 平均任务完成时间（毫秒） */
    averageCompletionTime: number;
    /** 任务详情 */
    tasks: TestTaskReport[];
  };
  /** 反馈统计 */
  feedbackStats: {
    /** 总反馈数 */
    total: number;
    /** 按类型统计 */
    byType: Record<string, number>;
    /** 平均评分 */
    averageRating: number;
    /** 反馈详情 */
    feedback: UserFeedback[];
  };
  /** 用户行为统计 */
  behaviorStats?: {
    /** 总操作数 */
    totalActions: number;
    /** 按类型统计 */
    byType: Record<string, number>;
    /** 热点区域 */
    hotspots?: { x: number; y: number; value: number }[];
    /** 组件使用统计 */
    componentUsage?: Record<string, number>;
    /** 导航路径 */
    navigationPaths?: { from: string; to: string; count: number }[];
    /** 视图停留时间 */
    viewTimeSpent?: Record<string, number>;
    /** 错误统计 */
    errors?: Record<string, number>;
  };
  /** 元数据 */
  metadata?: {
    /** 项目ID */
    projectId?: string;
    /** 场景ID */
    sceneId?: string;
    /** 浏览器信息 */
    browser?: string;
    /** 操作系统信息 */
    os?: string;
    /** 屏幕尺寸 */
    screenSize?: string;
    [key: string]: any;
  };
}

/**
 * 任务报告接口
 */
export interface TestTaskReport extends TestTask {
  /** 完成状态 */
  status: 'completed' | 'in_progress' | 'not_started';
  /** 完成百分比 */
  completionPercentage: number;
}

/**
 * 报告格式
 */
export enum ReportFormat {
  JSON = 'json',
  HTML = 'html',
  PDF = 'pdf',
  CSV = 'csv'
}

/**
 * 测试报告生成器配置
 */
export interface TestReportGeneratorConfig {
  /** 是否包含用户行为数据 */
  includeBehaviorData?: boolean;
  /** 是否包含详细任务数据 */
  includeDetailedTaskData?: boolean;
  /** 是否包含详细反馈数据 */
  includeDetailedFeedbackData?: boolean;
  /** 是否包含元数据 */
  includeMetadata?: boolean;
  /** 是否生成热图数据 */
  generateHeatmap?: boolean;
  /** 是否生成导航路径数据 */
  generateNavigationPaths?: boolean;
  /** 是否生成组件使用统计 */
  generateComponentUsage?: boolean;
  /** 是否生成错误统计 */
  generateErrorStats?: boolean;
}

/**
 * 测试报告生成器类
 */
export class TestReportGenerator {
  private config: Required<TestReportGeneratorConfig>;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: TestReportGeneratorConfig = {}) {
    this.config = {
      includeBehaviorData: config.includeBehaviorData ?? true,
      includeDetailedTaskData: config.includeDetailedTaskData ?? true,
      includeDetailedFeedbackData: config.includeDetailedFeedbackData ?? true,
      includeMetadata: config.includeMetadata ?? true,
      generateHeatmap: config.generateHeatmap ?? true,
      generateNavigationPaths: config.generateNavigationPaths ?? true,
      generateComponentUsage: config.generateComponentUsage ?? true,
      generateErrorStats: config.generateErrorStats ?? true
    };
  }

  /**
   * 生成测试报告
   * @param session 测试会话
   * @param actions 用户行为数据
   * @returns 测试报告
   */
  public generateReport(session: TestSession, actions: UserAction[] = []): TestReport {
    // 计算会话时长
    const sessionDuration = (session.endTime || Date.now()) - session.startTime;
    
    // 计算任务统计
    const completedTasks = session.tasks.filter(task => task.completed);
    const completionRate = session.tasks.length > 0 ? completedTasks.length / session.tasks.length : 0;
    
    // 计算平均任务完成时间
    const completionTimes = completedTasks
      .filter(task => task.startTime && task.endTime)
      .map(task => (task.endTime as number) - (task.startTime as number));
    
    const averageCompletionTime = completionTimes.length > 0
      ? completionTimes.reduce((sum, time) => sum + time, 0) / completionTimes.length
      : 0;
    
    // 生成任务报告
    const taskReports: TestTaskReport[] = session.tasks.map(task => {
      let status: 'completed' | 'in_progress' | 'not_started' = 'not_started';
      let completionPercentage = 0;
      
      if (task.completed) {
        status = 'completed';
        completionPercentage = 100;
      } else if (task.startTime) {
        status = 'in_progress';
        // 估算完成百分比（基于平均完成时间）
        if (averageCompletionTime > 0) {
          const elapsedTime = Date.now() - task.startTime;
          completionPercentage = Math.min(Math.round((elapsedTime / averageCompletionTime) * 100), 99);
        } else {
          completionPercentage = 50; // 默认值
        }
      }
      
      return {
        ...task,
        status,
        completionPercentage
      };
    });
    
    // 计算反馈统计
    const feedbackByType: Record<string, number> = {};
    let totalRating = 0;
    let ratingCount = 0;
    
    session.feedback.forEach(feedback => {
      feedbackByType[feedback.type] = (feedbackByType[feedback.type] || 0) + 1;
      
      if (feedback.type === 'rating' && typeof feedback.rating === 'number') {
        totalRating += feedback.rating;
        ratingCount++;
      }
    });
    
    const averageRating = ratingCount > 0 ? totalRating / ratingCount : 0;
    
    // 生成行为统计（如果有）
    let behaviorStats = undefined;
    
    if (this.config.includeBehaviorData && actions.length > 0) {
      const actionsByType: Record<string, number> = {};
      
      actions.forEach(action => {
        actionsByType[action.type] = (actionsByType[action.type] || 0) + 1;
      });
      
      behaviorStats = {
        totalActions: actions.length,
        byType: actionsByType
      };
      
      // 添加其他行为统计数据
      // 这里需要根据实际情况添加热图、组件使用、导航路径等数据
    }
    
    // 生成报告
    const report: TestReport = {
      id: `report_${Date.now()}_${Math.floor(Math.random() * 1000)}`,
      sessionId: session.id,
      userId: session.userId,
      userName: session.userName,
      generatedAt: Date.now(),
      sessionStartTime: session.startTime,
      sessionEndTime: session.endTime || Date.now(),
      sessionDuration,
      taskStats: {
        total: session.tasks.length,
        completed: completedTasks.length,
        completionRate,
        averageCompletionTime,
        tasks: this.config.includeDetailedTaskData ? taskReports : []
      },
      feedbackStats: {
        total: session.feedback.length,
        byType: feedbackByType,
        averageRating,
        feedback: this.config.includeDetailedFeedbackData ? session.feedback : []
      },
      behaviorStats,
      metadata: this.config.includeMetadata ? session.metadata : undefined
    };
    
    return report;
  }

  /**
   * 导出报告
   * @param report 测试报告
   * @param format 报告格式
   * @returns 导出的报告数据
   */
  public exportReport(report: TestReport, format: ReportFormat = ReportFormat.JSON): string | Blob {
    switch (format) {
      case ReportFormat.JSON:
        return JSON.stringify(report, null, 2);
      case ReportFormat.HTML:
        return this.generateHtmlReport(report);
      case ReportFormat.CSV:
        return this.generateCsvReport(report);
      case ReportFormat.PDF:
        // PDF生成需要额外的库支持
        throw new Error('PDF格式导出暂不支持');
      default:
        throw new Error(`不支持的报告格式: ${format}`);
    }
  }

  /**
   * 生成HTML报告
   * @param report 测试报告
   * @returns HTML Blob
   */
  private generateHtmlReport(report: TestReport): Blob {
    // 实现HTML报告生成
    const html = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>用户测试报告 - ${report.sessionId}</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
    h1, h2, h3 { color: #0066cc; }
    .summary { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    .section { margin-bottom: 20px; }
    table { border-collapse: collapse; width: 100%; margin-bottom: 15px; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .progress-bar { background-color: #e0e0e0; border-radius: 4px; height: 20px; }
    .progress-bar-fill { background-color: #4caf50; height: 100%; border-radius: 4px; }
    .task-completed { color: green; }
    .task-in-progress { color: orange; }
    .task-not-started { color: gray; }
    .chart { margin: 20px 0; }
  </style>
</head>
<body>
  <h1>用户测试报告</h1>
  
  <div class="summary">
    <h2>测试会话摘要</h2>
    <p><strong>会话ID:</strong> ${report.sessionId}</p>
    <p><strong>用户:</strong> ${report.userName} (${report.userId})</p>
    <p><strong>开始时间:</strong> ${new Date(report.sessionStartTime).toLocaleString()}</p>
    <p><strong>结束时间:</strong> ${new Date(report.sessionEndTime).toLocaleString()}</p>
    <p><strong>总时长:</strong> ${this.formatDuration(report.sessionDuration)}</p>
  </div>
  
  <!-- 任务统计 -->
  <div class="section">
    <h2>任务完成情况</h2>
    <p><strong>总任务数:</strong> ${report.taskStats.total}</p>
    <p><strong>已完成任务:</strong> ${report.taskStats.completed}</p>
    <p><strong>完成率:</strong> ${(report.taskStats.completionRate * 100).toFixed(2)}%</p>
    <p><strong>平均完成时间:</strong> ${this.formatDuration(report.taskStats.averageCompletionTime)}</p>
    
    <h3>任务详情</h3>
    <table>
      <tr>
        <th>任务</th>
        <th>状态</th>
        <th>开始时间</th>
        <th>结束时间</th>
        <th>耗时</th>
        <th>完成度</th>
      </tr>
      ${report.taskStats.tasks.map(task => `
      <tr>
        <td>${task.title}</td>
        <td class="task-${task.status}">${this.getStatusText(task.status)}</td>
        <td>${task.startTime ? new Date(task.startTime).toLocaleString() : '-'}</td>
        <td>${task.endTime ? new Date(task.endTime).toLocaleString() : '-'}</td>
        <td>${task.timeSpent ? this.formatDuration(task.timeSpent) : '-'}</td>
        <td>
          <div class="progress-bar">
            <div class="progress-bar-fill" style="width: ${task.completionPercentage}%"></div>
          </div>
          ${task.completionPercentage}%
        </td>
      </tr>
      `).join('')}
    </table>
  </div>
  
  <!-- 反馈统计 -->
  <div class="section">
    <h2>用户反馈</h2>
    <p><strong>总反馈数:</strong> ${report.feedbackStats.total}</p>
    <p><strong>平均评分:</strong> ${report.feedbackStats.averageRating.toFixed(1)}/5</p>
    
    <h3>反馈类型分布</h3>
    <table>
      <tr>
        <th>类型</th>
        <th>数量</th>
      </tr>
      ${Object.entries(report.feedbackStats.byType).map(([type, count]) => `
      <tr>
        <td>${this.getFeedbackTypeText(type)}</td>
        <td>${count}</td>
      </tr>
      `).join('')}
    </table>
    
    <h3>反馈详情</h3>
    ${report.feedbackStats.feedback.map(feedback => `
    <div style="border: 1px solid #ddd; padding: 10px; margin-bottom: 10px; border-radius: 5px;">
      <p><strong>类型:</strong> ${this.getFeedbackTypeText(feedback.type)}</p>
      ${feedback.rating ? `<p><strong>评分:</strong> ${feedback.rating}/5</p>` : ''}
      <p><strong>内容:</strong> ${feedback.content}</p>
      <p><strong>时间:</strong> ${new Date(feedback.timestamp).toLocaleString()}</p>
    </div>
    `).join('')}
  </div>
  
  <!-- 行为统计 -->
  ${report.behaviorStats ? `
  <div class="section">
    <h2>用户行为分析</h2>
    <p><strong>总操作数:</strong> ${report.behaviorStats.totalActions}</p>
    
    <h3>操作类型分布</h3>
    <table>
      <tr>
        <th>类型</th>
        <th>数量</th>
      </tr>
      ${Object.entries(report.behaviorStats.byType).map(([type, count]) => `
      <tr>
        <td>${this.getActionTypeText(type)}</td>
        <td>${count}</td>
      </tr>
      `).join('')}
    </table>
  </div>
  ` : ''}
  
  <!-- 元数据 -->
  ${report.metadata ? `
  <div class="section">
    <h2>测试环境</h2>
    <p><strong>项目ID:</strong> ${report.metadata.projectId || '-'}</p>
    <p><strong>场景ID:</strong> ${report.metadata.sceneId || '-'}</p>
    <p><strong>浏览器:</strong> ${report.metadata.browser || '-'}</p>
    <p><strong>操作系统:</strong> ${report.metadata.os || '-'}</p>
    <p><strong>屏幕尺寸:</strong> ${report.metadata.screenSize || '-'}</p>
  </div>
  ` : ''}
  
  <div class="footer">
    <p>报告生成时间: ${new Date(report.generatedAt).toLocaleString()}</p>
  </div>
</body>
</html>`;
    
    return new Blob([html], { type: 'text/html' });
  }

  /**
   * 生成CSV报告
   * @param report 测试报告
   * @returns CSV字符串
   */
  private generateCsvReport(report: TestReport): string {
    // 实现CSV报告生成
    return '';
  }

  /**
   * 格式化时长
   * @param duration 时长（毫秒）
   * @returns 格式化后的时长
   */
  private formatDuration(duration: number): string {
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    const remainingMinutes = minutes % 60;
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return `${hours}小时 ${remainingMinutes}分钟 ${remainingSeconds}秒`;
    } else if (minutes > 0) {
      return `${minutes}分钟 ${remainingSeconds}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 获取状态文本
   * @param status 状态
   * @returns 状态文本
   */
  private getStatusText(status: string): string {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'in_progress':
        return '进行中';
      case 'not_started':
        return '未开始';
      default:
        return status;
    }
  }

  /**
   * 获取反馈类型文本
   * @param type 反馈类型
   * @returns 反馈类型文本
   */
  private getFeedbackTypeText(type: string): string {
    switch (type) {
      case 'bug':
        return '问题报告';
      case 'suggestion':
        return '改进建议';
      case 'rating':
        return '功能评分';
      case 'comment':
        return '一般评论';
      default:
        return type;
    }
  }

  /**
   * 获取操作类型文本
   * @param type 操作类型
   * @returns 操作类型文本
   */
  private getActionTypeText(type: string): string {
    switch (type) {
      case UserActionType.MOUSE_MOVE:
        return '鼠标移动';
      case UserActionType.MOUSE_CLICK:
        return '鼠标点击';
      case UserActionType.KEY_PRESS:
        return '键盘输入';
      case UserActionType.SCROLL:
        return '滚动';
      case UserActionType.COMPONENT_INTERACTION:
        return '组件交互';
      case UserActionType.MENU_SELECTION:
        return '菜单选择';
      case UserActionType.TOOL_USAGE:
        return '工具使用';
      case UserActionType.PANEL_SWITCH:
        return '面板切换';
      case UserActionType.DIALOG_INTERACTION:
        return '对话框交互';
      case UserActionType.SCENE_INTERACTION:
        return '场景交互';
      case UserActionType.OBJECT_SELECTION:
        return '对象选择';
      case UserActionType.PROPERTY_CHANGE:
        return '属性修改';
      case UserActionType.NAVIGATION:
        return '导航';
      case UserActionType.SEARCH:
        return '搜索';
      case UserActionType.UNDO_REDO:
        return '撤销/重做';
      case UserActionType.SAVE_LOAD:
        return '保存/加载';
      case UserActionType.COLLABORATION:
        return '协作';
      case UserActionType.ERROR:
        return '错误';
      default:
        return type;
    }
  }
}

// 创建单例实例
export const testReportGenerator = new TestReportGenerator();
