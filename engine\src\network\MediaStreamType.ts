/**
 * 媒体流类型定义
 */

export enum MediaStreamType {
  /** 音频流 */
  AUDIO = 'audio',
  /** 视频流 */
  VIDEO = 'video',
  /** 音视频流 */
  AUDIO_VIDEO = 'audioVideo',
  /** 屏幕共享 */
  SCREEN_SHARE = 'screenShare',
  /** 摄像头 */
  CAMERA = 'camera',
  /** 麦克风 */
  MICROPHONE = 'microphone'
}

export interface MediaStreamConfig {
  /** 流类型 */
  type: MediaStreamType;
  /** 是否启用音频 */
  audio?: boolean;
  /** 是否启用视频 */
  video?: boolean;
  /** 音频约束 */
  audioConstraints?: MediaTrackConstraints;
  /** 视频约束 */
  videoConstraints?: MediaTrackConstraints;
}

export class MediaStreamManager {
  private streams: Map<string, MediaStream> = new Map();

  public async createStream(config: MediaStreamConfig): Promise<MediaStream | null> {
    try {
      let constraints: MediaStreamConstraints = {};

      switch (config.type) {
        case MediaStreamType.AUDIO:
          constraints.audio = config.audioConstraints || true;
          break;
        case MediaStreamType.VIDEO:
          constraints.video = config.videoConstraints || true;
          break;
        case MediaStreamType.AUDIO_VIDEO:
          constraints.audio = config.audioConstraints || true;
          constraints.video = config.videoConstraints || true;
          break;
        case MediaStreamType.SCREEN_SHARE:
          // @ts-ignore - getDisplayMedia 可能不在所有浏览器中可用
          return await navigator.mediaDevices.getDisplayMedia({
            video: true,
            audio: config.audio || false
          });
      }

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      return stream;
    } catch (error) {
      console.error('创建媒体流失败:', error);
      return null;
    }
  }

  public addStream(id: string, stream: MediaStream): void {
    this.streams.set(id, stream);
  }

  public getStream(id: string): MediaStream | undefined {
    return this.streams.get(id);
  }

  public removeStream(id: string): void {
    const stream = this.streams.get(id);
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      this.streams.delete(id);
    }
  }

  public stopAllStreams(): void {
    this.streams.forEach((stream, id) => {
      this.removeStream(id);
    });
  }
}
