<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>材质系统示例 - IR Engine</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      background-color: #000;
    }
    canvas {
      display: block;
      width: 100%;
      height: 100%;
    }
    #info {
      position: absolute;
      top: 10px;
      width: 100%;
      text-align: center;
      color: white;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 100;
      pointer-events: none;
    }
  </style>
</head>
<body>
  <div id="info">材质系统示例 - IR Engine</div>
  <script type="module">
    import { MaterialSystemExample } from './MaterialSystemExample.ts';
    
    // 创建示例
    const example = new MaterialSystemExample();
    
    // 启动示例
    example.start();
    
    // 添加窗口关闭事件监听
    window.addEventListener('beforeunload', () => {
      example.dispose();
    });
  </script>
</body>
</html>
