/**
 * 响应式面板组件
 * 提供自适应不同设备和屏幕尺寸的面板
 */
import React, { useState, useEffect, ReactNode } from 'react';
import { Card, Collapse, Button, Tooltip } from 'antd';
import { ExpandOutlined, ShrinkOutlined, SettingOutlined, CloseOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import ResponsiveDesignService, { ResponsiveBreakpoint, ControlSize } from '../../services/ResponsiveDesignService';
import MobileDeviceService, { DeviceType, ScreenOrientation } from '../../services/MobileDeviceService';
import './ResponsivePanel.less';

const { Panel } = Collapse;

// 面板大小枚举
export enum PanelSize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
  FULL = 'full'
}

// 面板位置枚举
export enum PanelPosition {
  LEFT = 'left',
  RIGHT = 'right',
  TOP = 'top',
  BOTTOM = 'bottom',
  CENTER = 'center',
  FLOAT = 'float'
}

// 响应式面板属性接口
interface ResponsivePanelProps {
  // 面板标题
  title: string;
  // 面板内容
  children: ReactNode;
  // 面板大小
  size?: PanelSize;
  // 面板位置
  position?: PanelPosition;
  // 是否可折叠
  collapsible?: boolean;
  // 是否可调整大小
  resizable?: boolean;
  // 是否可关闭
  closable?: boolean;
  // 是否可拖动（仅当position为FLOAT时有效）
  draggable?: boolean;
  // 默认是否折叠
  defaultCollapsed?: boolean;
  // 额外的头部内容
  extra?: ReactNode;
  // 关闭回调
  onClose?: () => void;
  // 折叠状态变化回调
  onCollapseChange?: (collapsed: boolean) => void;
  // 大小变化回调
  onSizeChange?: (size: PanelSize) => void;
  // 位置变化回调
  onPositionChange?: (position: PanelPosition) => void;
  // 类名
  className?: string;
  // 样式
  style?: React.CSSProperties;
}

/**
 * 响应式面板组件
 */
const ResponsivePanel: React.FC<ResponsivePanelProps> = ({
  title,
  children,
  size = PanelSize.MEDIUM,
  position = PanelPosition.RIGHT,
  collapsible = true,
  resizable = true,
  closable = true,
  draggable = false,
  defaultCollapsed = false,
  extra,
  onClose,
  onCollapseChange,
  onSizeChange,
  onPositionChange,
  className = '',
  style = {}
}) => {
  const { t } = useTranslation();
  const [collapsed, setCollapsed] = useState<boolean>(defaultCollapsed);
  const [currentSize, setCurrentSize] = useState<PanelSize>(size);
  const [currentPosition, setCurrentPosition] = useState<PanelPosition>(position);
  const [breakpoint, setBreakpoint] = useState<ResponsiveBreakpoint>(
    ResponsiveDesignService.getCurrentBreakpoint()
  );
  const [controlSize, setControlSize] = useState<ControlSize>(
    ResponsiveDesignService.getCurrentControlSize()
  );
  const [deviceType, setDeviceType] = useState<DeviceType>(
    MobileDeviceService.getDeviceInfo().type
  );
  const [orientation, setOrientation] = useState<ScreenOrientation>(
    MobileDeviceService.getDeviceInfo().orientation
  );
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragPosition, setDragPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

  // 监听响应式设计服务事件
  useEffect(() => {
    const handleBreakpointChange = (data: any) => {
      setBreakpoint(data.newBreakpoint);
      adjustSizeForBreakpoint(data.newBreakpoint);
    };

    const handleControlSizeChange = (data: any) => {
      setControlSize(data.newControlSize);
    };

    const handleDeviceTypeChange = (data: any) => {
      setDeviceType(data.deviceType);
      adjustSizeForDeviceType(data.deviceType);
    };

    const handleOrientationChange = (data: any) => {
      setOrientation(data.orientation);
      adjustSizeForOrientation(data.orientation);
    };

    // 添加事件监听
    ResponsiveDesignService.on('breakpointChanged', handleBreakpointChange);
    ResponsiveDesignService.on('controlSizeChanged', handleControlSizeChange);
    ResponsiveDesignService.on('deviceTypeChanged', handleDeviceTypeChange);
    ResponsiveDesignService.on('orientationChanged', handleOrientationChange);

    // 清理函数
    return () => {
      ResponsiveDesignService.off('breakpointChanged', handleBreakpointChange);
      ResponsiveDesignService.off('controlSizeChanged', handleControlSizeChange);
      ResponsiveDesignService.off('deviceTypeChanged', handleDeviceTypeChange);
      ResponsiveDesignService.off('orientationChanged', handleOrientationChange);
    };
  }, []);

  // 根据断点调整大小
  const adjustSizeForBreakpoint = (newBreakpoint: ResponsiveBreakpoint) => {
    let newSize = currentSize;

    // 在小屏幕上使用较小的面板
    if (newBreakpoint === ResponsiveBreakpoint.XS || newBreakpoint === ResponsiveBreakpoint.SM) {
      if (currentSize === PanelSize.LARGE) {
        newSize = PanelSize.MEDIUM;
      }
    }
    // 在大屏幕上可以使用较大的面板
    else if (newBreakpoint === ResponsiveBreakpoint.XL || newBreakpoint === ResponsiveBreakpoint.XXL) {
      if (currentSize === PanelSize.SMALL) {
        newSize = PanelSize.MEDIUM;
      }
    }

    if (newSize !== currentSize) {
      setCurrentSize(newSize);
      if (onSizeChange) {
        onSizeChange(newSize);
      }
    }
  };

  // 根据设备类型调整大小
  const adjustSizeForDeviceType = (newDeviceType: DeviceType) => {
    let newSize = currentSize;
    let newPosition = currentPosition;

    // 在移动设备上使用全屏或较大的面板
    if (newDeviceType === DeviceType.MOBILE) {
      newSize = PanelSize.FULL;
      newPosition = PanelPosition.CENTER;
    }
    // 在平板设备上使用较大的面板
    else if (newDeviceType === DeviceType.TABLET) {
      if (currentSize === PanelSize.SMALL) {
        newSize = PanelSize.MEDIUM;
      }
    }

    if (newSize !== currentSize) {
      setCurrentSize(newSize);
      if (onSizeChange) {
        onSizeChange(newSize);
      }
    }

    if (newPosition !== currentPosition) {
      setCurrentPosition(newPosition);
      if (onPositionChange) {
        onPositionChange(newPosition);
      }
    }
  };

  // 根据屏幕方向调整大小
  const adjustSizeForOrientation = (newOrientation: ScreenOrientation) => {
    // 只在移动设备上根据方向调整
    if (deviceType !== DeviceType.DESKTOP) {
      let newSize = currentSize;
      let newPosition = currentPosition;

      // 在横屏模式下使用侧边面板
      if (newOrientation === ScreenOrientation.LANDSCAPE) {
        newPosition = PanelPosition.RIGHT;
        if (currentSize === PanelSize.FULL) {
          newSize = PanelSize.LARGE;
        }
      }
      // 在竖屏模式下使用底部或全屏面板
      else {
        if (deviceType === DeviceType.MOBILE) {
          newPosition = PanelPosition.BOTTOM;
          newSize = PanelSize.LARGE;
        }
      }

      if (newSize !== currentSize) {
        setCurrentSize(newSize);
        if (onSizeChange) {
          onSizeChange(newSize);
        }
      }

      if (newPosition !== currentPosition) {
        setCurrentPosition(newPosition);
        if (onPositionChange) {
          onPositionChange(newPosition);
        }
      }
    }
  };

  // 切换折叠状态
  const toggleCollapse = () => {
    const newCollapsed = !collapsed;
    setCollapsed(newCollapsed);
    if (onCollapseChange) {
      onCollapseChange(newCollapsed);
    }
  };

  // 切换大小
  const toggleSize = () => {
    let newSize: PanelSize;

    switch (currentSize) {
      case PanelSize.SMALL:
        newSize = PanelSize.MEDIUM;
        break;
      case PanelSize.MEDIUM:
        newSize = PanelSize.LARGE;
        break;
      case PanelSize.LARGE:
        newSize = PanelSize.FULL;
        break;
      case PanelSize.FULL:
        newSize = PanelSize.SMALL;
        break;
      default:
        newSize = PanelSize.MEDIUM;
    }

    setCurrentSize(newSize);
    if (onSizeChange) {
      onSizeChange(newSize);
    }
  };

  // 处理关闭
  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  // 处理拖动开始
  const handleDragStart = (e: React.MouseEvent<HTMLDivElement>) => {
    if (draggable && currentPosition === PanelPosition.FLOAT) {
      setIsDragging(true);
      setDragPosition({
        x: e.clientX,
        y: e.clientY
      });
    }
  };

  // 处理拖动
  const handleDrag = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isDragging) {
      const deltaX = e.clientX - dragPosition.x;
      const deltaY = e.clientY - dragPosition.y;

      // 更新面板位置
      const panel = e.currentTarget.parentElement;
      if (panel) {
        const rect = panel.getBoundingClientRect();
        panel.style.left = `${rect.left + deltaX}px`;
        panel.style.top = `${rect.top + deltaY}px`;
      }

      setDragPosition({
        x: e.clientX,
        y: e.clientY
      });
    }
  };

  // 处理拖动结束
  const handleDragEnd = () => {
    setIsDragging(false);
  };

  // 获取面板类名
  const getPanelClassName = () => {
    return `responsive-panel ${className} 
      size-${currentSize} 
      position-${currentPosition} 
      ${collapsed ? 'collapsed' : ''} 
      ${isDragging ? 'dragging' : ''}
      device-${deviceType.toLowerCase()}
      orientation-${orientation.toLowerCase()}
      breakpoint-${breakpoint.toLowerCase()}`;
  };

  // 获取面板样式
  const getPanelStyle = (): React.CSSProperties => {
    return {
      ...style,
      // 可以根据需要添加其他样式
    };
  };

  // 渲染面板头部
  const renderPanelHeader = () => {
    return (
      <div 
        className="panel-header"
        onMouseDown={handleDragStart}
        onMouseMove={handleDrag}
        onMouseUp={handleDragEnd}
        onMouseLeave={handleDragEnd}
      >
        <div className="panel-title">{title}</div>
        <div className="panel-actions">
          {extra}
          {resizable && (
            <Tooltip title={t('panel.toggleSize')}>
              <Button
                type="text"
                size={controlSize === ControlSize.SMALL ? 'small' : 'middle'}
                icon={currentSize === PanelSize.FULL ? <ShrinkOutlined /> : <ExpandOutlined />}
                onClick={toggleSize}
              />
            </Tooltip>
          )}
          {collapsible && (
            <Tooltip title={collapsed ? t('panel.expand') : t('panel.collapse')}>
              <Button
                type="text"
                size={controlSize === ControlSize.SMALL ? 'small' : 'middle'}
                icon={<SettingOutlined />}
                onClick={toggleCollapse}
              />
            </Tooltip>
          )}
          {closable && (
            <Tooltip title={t('panel.close')}>
              <Button
                type="text"
                size={controlSize === ControlSize.SMALL ? 'small' : 'middle'}
                icon={<CloseOutlined />}
                onClick={handleClose}
              />
            </Tooltip>
          )}
        </div>
      </div>
    );
  };

  // 渲染面板内容
  const renderPanelContent = () => {
    if (collapsed) {
      return null;
    }
    return <div className="panel-content">{children}</div>;
  };

  return (
    <div className={getPanelClassName()} style={getPanelStyle()}>
      <Card
        className="panel-card"
        title={renderPanelHeader()}
        bordered={true}
        bodyStyle={{ padding: collapsed ? 0 : undefined, height: collapsed ? 0 : undefined }}
      >
        {renderPanelContent()}
      </Card>
    </div>
  );
};

export default ResponsivePanel;
