/**
 * 触控控制面板样式
 */
.touch-control-panel {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  
  // 横屏模式
  &.landscape {
    height: 250px;
    padding: 8px;
  }
  
  // 竖屏模式
  &.portrait {
    height: 300px;
    padding: 8px;
  }
  
  // 标签页样式
  .ant-tabs {
    height: 100%;
    
    .ant-tabs-nav {
      margin-bottom: 8px;
      
      .ant-tabs-tab {
        padding: 8px 16px;
        font-size: 14px;
        
        &.ant-tabs-tab-active {
          font-weight: 500;
        }
      }
    }
    
    .ant-tabs-content {
      height: calc(100% - 46px);
      overflow-y: auto;
      
      .ant-tabs-tabpane {
        padding: 0 4px;
      }
    }
  }
  
  // 变换控制面板
  .transform-panel {
    .control-grid {
      display: flex;
      flex-direction: column;
      gap: 8px;
      
      .grid-row {
        display: flex;
        justify-content: space-between;
        gap: 8px;
        
        .grid-cell {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
          
          .ant-btn {
            width: 100%;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            
            &:active {
              transform: scale(0.95);
            }
          }
          
          .ant-slider {
            width: 100%;
            margin: 0;
          }
        }
      }
    }
    
    .ant-card {
      margin-bottom: 8px;
      
      .ant-card-head {
        min-height: 40px;
        padding: 0 12px;
        
        .ant-card-head-title {
          padding: 8px 0;
          font-size: 14px;
        }
      }
      
      .ant-card-body {
        padding: 12px;
      }
    }
  }
  
  // 工具面板
  .tools-panel {
    padding: 8px 0;
    
    .ant-btn {
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &:active {
        transform: scale(0.95);
      }
    }
  }
  
  // 视图面板
  .view-panel {
    padding: 8px 0;
    
    .ant-card {
      margin-bottom: 8px;
      
      .ant-card-head {
        min-height: 40px;
        padding: 0 12px;
        
        .ant-card-head-title {
          padding: 8px 0;
          font-size: 14px;
        }
      }
      
      .ant-card-body {
        padding: 12px;
      }
    }
    
    .center-content {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 40px;
    }
    
    .ant-btn {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &:active {
        transform: scale(0.95);
      }
    }
  }
  
  // 帮助面板
  .help-panel {
    padding: 8px 0;
    
    .ant-card {
      .ant-card-head {
        min-height: 40px;
        padding: 0 12px;
        
        .ant-card-head-title {
          padding: 8px 0;
          font-size: 14px;
        }
      }
      
      .ant-card-body {
        padding: 12px;
      }
    }
    
    .gesture-list {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        font-size: 13px;
        line-height: 1.5;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 辅助类
.mt-2 {
  margin-top: 8px;
}

// 响应式调整
@media (max-width: 576px) {
  .touch-control-panel {
    &.landscape {
      height: 200px;
    }
    
    &.portrait {
      height: 250px;
    }
    
    .ant-tabs-nav {
      .ant-tabs-tab {
        padding: 8px 12px;
        font-size: 13px;
      }
    }
    
    .transform-panel {
      .control-grid {
        .grid-row {
          .grid-cell {
            .ant-btn {
              height: 36px;
            }
          }
        }
      }
    }
    
    .tools-panel {
      .ant-btn {
        height: 44px;
      }
    }
  }
}

// 暗色主题
.dark-theme {
  .touch-control-panel {
    background-color: rgba(22, 22, 22, 0.9);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.3);
    
    .ant-tabs-tab {
      color: rgba(255, 255, 255, 0.85);
    }
    
    .ant-card {
      background-color: #1f1f1f;
      border-color: #303030;
      
      .ant-card-head {
        background-color: #141414;
        border-bottom-color: #303030;
        
        .ant-card-head-title {
          color: rgba(255, 255, 255, 0.85);
        }
      }
    }
    
    .gesture-list {
      color: rgba(255, 255, 255, 0.85);
    }
  }
}
