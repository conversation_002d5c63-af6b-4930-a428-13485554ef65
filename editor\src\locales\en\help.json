{"help": {"title": "Help", "search": "Search", "browse": "Browse", "faq": "FAQ", "tutorials": "Tutorials", "searchPlaceholder": "Search help documentation...", "noResults": "No matching results found", "enterSearchTerm": "Please enter a search term", "selectTopic": "Please select a topic", "loading": "Loading content...", "view": "View", "category": "Category", "clickForHelp": "Click for help", "help": "Help", "categories": {"reference": "Reference Documentation", "features": "Feature Guides", "gettingStarted": "Getting Started", "bestPractices": "Best Practices", "faq": "FAQ", "tutorials": "Tutorials"}, "topics": {"componentsReference": "Component Reference", "shadersReference": "Shader Reference", "scriptingReference": "Scripting Reference", "cliReference": "Command Line Tool Reference", "visualScripting": "Visual Scripting", "sceneEditing": "Scene Editing", "materialEditing": "Material Editing", "animationSystem": "Animation System", "physicsSystem": "Physics System", "interactionSystem": "Interaction System", "collaborativeEditing": "Collaborative Editing"}, "errorLoadingContent": "Error loading content, please try again later."}, "tutorials": {"title": "Tutorials", "description": "Learn the features of the IR Engine Editor through interactive tutorials.", "allTutorials": "All Tutorials", "recommended": "Recommended", "completed": "Completed", "recommendedForYou": "Recommended for You", "completedTutorials": "Completed Tutorials", "start": "Start", "continue": "Continue", "restart": "<PERSON><PERSON>", "next": "Next", "previous": "Previous", "skip": "<PERSON><PERSON>", "exit": "Exit", "step": "Step", "duration": "Estimated Time", "minutes": "minutes", "progressStatus": "Progress: {current}/{total}", "completedTitle": "Tutorial Completed", "completedMessage": "Congratulations! You have completed the \"{title}\" tutorial.", "difficulty": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced"}, "editorBasics": {"title": "Editor Basics", "description": "Learn the basic interface and operations of the IR Engine Editor.", "steps": {"welcome": {"title": "Welcome", "description": "Welcome to the IR Engine Editor! This tutorial will help you understand the basic interface and operations of the editor."}, "interfaceOverview": {"title": "Interface Overview", "description": "The IR Engine Editor interface consists of multiple panels, including Scene View, Hierarchy Panel, Properties Panel, etc. You can adjust the layout of these panels as needed."}}}, "visualScripting": {"title": "Visual Scripting Basics", "description": "Learn how to create interactive content using the visual scripting system.", "steps": {"introduction": {"title": "Introduction", "description": "Visual scripting is a way to create interactive content and game logic without writing code. This tutorial will help you understand the basic concepts and usage of the visual scripting system."}}}}}