.help-button {
  &.ant-btn-text {
    color: rgba(0, 0, 0, 0.45);
    
    &:hover {
      color: #1890ff;
      background-color: rgba(24, 144, 255, 0.1);
    }
  }
}

.help-button-popover {
  .ant-popover-inner-content {
    padding: 12px;
  }
  
  .help-popover-content {
    .help-content-summary {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 8px;
    }
    
    .help-search-section {
      margin-bottom: 8px;
      
      .help-search-input {
        padding: 4px 11px;
        border: 1px solid #d9d9d9;
        border-right: none;
        border-radius: 2px 0 0 2px;
        outline: none;
        transition: all 0.3s;
        
        &:focus {
          border-color: #40a9ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }
  }
}

// 暗色主题适配
.dark-theme {
  .help-button {
    &.ant-btn-text {
      color: rgba(255, 255, 255, 0.45);
      
      &:hover {
        color: #177ddc;
        background-color: rgba(23, 125, 220, 0.1);
      }
    }
  }
  
  .help-button-popover {
    .help-popover-content {
      .help-search-section {
        .help-search-input {
          background-color: #141414;
          border-color: #434343;
          color: rgba(255, 255, 255, 0.85);
          
          &:focus {
            border-color: #177ddc;
            box-shadow: 0 0 0 2px rgba(23, 125, 220, 0.2);
          }
        }
      }
    }
  }
}
