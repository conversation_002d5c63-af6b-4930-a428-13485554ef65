{
  "help": {
    "title": "帮助",
    "search": "搜索",
    "browse": "浏览",
    "faq": "常见问题",
    "tutorials": "教程",
    "searchPlaceholder": "搜索帮助文档...",
    "noResults": "没有找到匹配的结果",
    "enterSearchTerm": "请输入搜索关键词",
    "selectTopic": "请选择一个主题",
    "loading": "正在加载内容...",
    "view": "查看",
    "category": "分类",
    "clickForHelp": "点击获取帮助",
    "help": "帮助",
    "categories": {
      "reference": "参考文档",
      "features": "功能指南",
      "gettingStarted": "入门指南",
      "bestPractices": "最佳实践",
      "faq": "常见问题",
      "tutorials": "教程"
    },
    "topics": {
      "componentsReference": "组件参考",
      "shadersReference": "着色器参考",
      "scriptingReference": "脚本参考",
      "cliReference": "命令行工具参考",
      "visualScripting": "视觉脚本",
      "sceneEditing": "场景编辑",
      "materialEditing": "材质编辑",
      "animationSystem": "动画系统",
      "physicsSystem": "物理系统",
      "interactionSystem": "交互系统",
      "collaborativeEditing": "协作编辑"
    },
    "errorLoadingContent": "加载内容时出错，请稍后重试。"
  },
  "tutorials": {
    "title": "教程",
    "description": "通过交互式教程学习DL（Digital Learning）引擎编辑器的各项功能。",
    "allTutorials": "所有教程",
    "recommended": "推荐",
    "completed": "已完成",
    "recommendedForYou": "为您推荐",
    "completedTutorials": "已完成的教程",
    "start": "开始",
    "continue": "继续",
    "restart": "重新开始",
    "next": "下一步",
    "previous": "上一步",
    "skip": "跳过",
    "exit": "退出",
    "step": "步骤",
    "duration": "预计时间",
    "minutes": "分钟",
    "progressStatus": "进度：{current}/{total}",
    "completedTitle": "教程完成",
    "completedMessage": "恭喜！您已完成"{title}"教程。",
    "difficulty": {
      "beginner": "初级",
      "intermediate": "中级",
      "advanced": "高级"
    },
    "editorBasics": {
      "title": "编辑器基础",
      "description": "学习DL（Digital Learning）引擎编辑器的基本界面和操作。",
      "steps": {
        "welcome": {
          "title": "欢迎",
          "description": "欢迎使用DL（Digital Learning）引擎编辑器！本教程将帮助您了解编辑器的基本界面和操作。"
        },
        "interfaceOverview": {
          "title": "界面概览",
          "description": "DL（Digital Learning）引擎编辑器界面由多个面板组成，包括场景视图、层级面板、属性面板等。您可以根据需要调整这些面板的布局。"
        }
      }
    },
    "visualScripting": {
      "title": "视觉脚本入门",
      "description": "学习如何使用视觉脚本系统创建交互式内容。",
      "steps": {
        "introduction": {
          "title": "介绍",
          "description": "视觉脚本是一种无需编写代码的方式来创建交互式内容和游戏逻辑。本教程将帮助您了解视觉脚本系统的基本概念和用法。"
        }
      }
    }
  }
}
