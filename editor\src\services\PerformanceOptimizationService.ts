/**
 * 性能优化服务
 * 提供编辑器性能监控和优化功能
 */
import {
  PerformanceMonitor,
  PerformanceMetricType,
  PerformanceBottleneckType,
  PerformanceTrendType,
  PerformanceReport
} from '../../../engine/src/utils/PerformanceMonitor';
import { SceneOptimizer } from 'ir-engine-core/debug/SceneOptimizer';
import { EnhancedLODGenerator } from 'ir-engine-core/rendering/optimization/EnhancedLODGenerator';
import { MaterialOptimizer } from 'ir-engine-core/rendering/materials/MaterialOptimizer';
import { BatchingSystem } from 'ir-engine-core/rendering/optimization/BatchingSystem';
import { InstancedRenderingSystem } from 'ir-engine-core/rendering/optimization/InstancedRenderingSystem';
import { FrustumCullingSystem } from 'ir-engine-core/rendering/optimization/FrustumCullingSystem';
import { OcclusionCullingSystem } from 'ir-engine-core/rendering/optimization/OcclusionCullingSystem';
import { EventEmitter } from 'ir-engine-core/utils/EventEmitter';
import { MemoryAnalyzer, ResourceType } from '../../../engine/src/utils/MemoryAnalyzer';
import { ResourceTracker } from '../../../engine/src/utils/ResourceTracker';
import { EngineService } from './EngineService';
import { SceneService } from './SceneService';

/**
 * 性能优化服务配置
 */
export interface PerformanceOptimizationServiceConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否启用自动优化 */
  enableAutoOptimize?: boolean;
  /** 自动优化间隔（毫秒） */
  autoOptimizeInterval?: number;
  /** 性能监控采样间隔（毫秒） */
  monitorSampleInterval?: number;
  /** 性能监控历史长度 */
  monitorHistoryLength?: number;
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否启用性能警告 */
  enableWarnings?: boolean;
  /** FPS警告阈值 */
  fpsWarningThreshold?: number;
  /** 内存警告阈值（MB） */
  memoryWarningThreshold?: number;
  /** 渲染时间警告阈值（毫秒） */
  renderTimeWarningThreshold?: number;
  /** 是否启用渲染优化 */
  enableRenderingOptimization?: boolean;
  /** 是否启用内存优化 */
  enableMemoryOptimization?: boolean;
  /** 是否启用UI优化 */
  enableUIOptimization?: boolean;
  /** 是否启用资源优化 */
  enableResourceOptimization?: boolean;
  /** 是否启用瓶颈检测 */
  enableBottleneckDetection?: boolean;
  /** 是否启用趋势分析 */
  enableTrendAnalysis?: boolean;
  /** 是否启用性能评分 */
  enablePerformanceScoring?: boolean;
  /** 是否启用优化建议 */
  enableOptimizationSuggestions?: boolean;
  /** 是否启用GPU指标收集 */
  collectGPUMetrics?: boolean;
  /** 是否启用CPU指标收集 */
  collectCPUMetrics?: boolean;
  /** 是否启用网络指标收集 */
  collectNetworkMetrics?: boolean;
  /** 是否启用资源指标收集 */
  collectResourceMetrics?: boolean;
  /** 是否启用事件指标收集 */
  collectEventMetrics?: boolean;
  /** 是否启用垃圾回收指标收集 */
  collectGCMetrics?: boolean;
  /** 是否启用性能报告导出 */
  enableReportExport?: boolean;
  /** 性能报告导出间隔（毫秒） */
  reportExportInterval?: number;
  /** 性能报告导出格式 */
  reportExportFormat?: 'json' | 'csv' | 'html';
  /** 性能报告导出路径 */
  reportExportPath?: string;
}

/**
 * 性能优化服务事件类型
 */
export enum PerformanceOptimizationEventType {
  /** 性能警告 */
  PERFORMANCE_WARNING = 'performanceWarning',
  /** 优化开始 */
  OPTIMIZATION_START = 'optimizationStart',
  /** 优化完成 */
  OPTIMIZATION_COMPLETE = 'optimizationComplete',
  /** 优化失败 */
  OPTIMIZATION_FAILED = 'optimizationFailed',
  /** 性能报告更新 */
  PERFORMANCE_REPORT_UPDATED = 'performanceReportUpdated',
  /** 资源瓶颈检测 */
  RESOURCE_BOTTLENECK_DETECTED = 'resourceBottleneckDetected',
  /** 优化建议生成 */
  OPTIMIZATION_SUGGESTIONS_GENERATED = 'optimizationSuggestionsGenerated',
  /** 资源优化完成 */
  RESOURCE_OPTIMIZATION_COMPLETE = 'resourceOptimizationComplete',
}

/**
 * 性能优化服务
 */
export class PerformanceOptimizationService {
  /** 单例实例 */
  private static instance: PerformanceOptimizationService;

  /** 配置 */
  private config: PerformanceOptimizationServiceConfig = {
    enabled: true,
    enableAutoOptimize: false,
    autoOptimizeInterval: 60000, // 1分钟
    monitorSampleInterval: 1000, // 1秒
    monitorHistoryLength: 60, // 60个样本
    debug: false,
    enableWarnings: true,
    fpsWarningThreshold: 30,
    memoryWarningThreshold: 500,
    renderTimeWarningThreshold: 16,
    enableRenderingOptimization: true,
    enableMemoryOptimization: true,
    enableUIOptimization: true,
    enableResourceOptimization: true,
    enableBottleneckDetection: true,
    enableTrendAnalysis: true,
    enablePerformanceScoring: true,
    enableOptimizationSuggestions: true,
    collectGPUMetrics: true,
    collectCPUMetrics: true,
    collectNetworkMetrics: true,
    collectResourceMetrics: true,
    collectEventMetrics: true,
    collectGCMetrics: true,
    enableReportExport: false,
    reportExportInterval: 60000, // 1分钟
    reportExportFormat: 'json',
    reportExportPath: './performance-reports',
  };

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否正在监控 */
  private monitoring: boolean = false;

  /** 是否正在优化 */
  private optimizing: boolean = false;

  /** 自动优化定时器ID */
  private autoOptimizeTimerId: number | null = null;

  /** 性能监控定时器ID */
  private monitorTimerId: number | null = null;

  /** 场景优化器 */
  private sceneOptimizer: SceneOptimizer | null = null;

  /** LOD生成器 */
  private lodGenerator: EnhancedLODGenerator | null = null;

  /** 材质优化器 */
  private materialOptimizer: MaterialOptimizer | null = null;

  /** 批处理系统 */
  private batchingSystem: BatchingSystem | null = null;

  /** 实例化渲染系统 */
  private instancedRenderingSystem: InstancedRenderingSystem | null = null;

  /** 视锥体剔除系统 */
  private frustumCullingSystem: FrustumCullingSystem | null = null;

  /** 遮挡剔除系统 */
  private occlusionCullingSystem: OcclusionCullingSystem | null = null;

  /** 内存分析器 */
  private memoryAnalyzer: MemoryAnalyzer | null = null;

  /** 资源跟踪器 */
  private resourceTracker: ResourceTracker | null = null;

  /** 资源瓶颈缓存 */
  private resourceBottlenecks: any[] = [];

  /** 优化建议缓存 */
  private optimizationSuggestions: any[] = [];

  /**
   * 获取单例实例
   */
  public static getInstance(): PerformanceOptimizationService {
    if (!PerformanceOptimizationService.instance) {
      PerformanceOptimizationService.instance = new PerformanceOptimizationService();
    }
    return PerformanceOptimizationService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {}

  /**
   * 初始化服务
   * @param config 配置
   */
  public initialize(config: Partial<PerformanceOptimizationServiceConfig> = {}): void {
    if (this.initialized) {
      return;
    }

    // 合并配置
    this.config = {
      ...this.config,
      ...config,
    };

    // 初始化性能监控器
    PerformanceMonitor.getInstance().configure({
      enabled: this.config.enabled,
      sampleInterval: this.config.monitorSampleInterval,
      historyLimit: this.config.monitorHistoryLength,
      autoSample: true,
      debug: this.config.debug,
      enableWarnings: this.config.enableWarnings,
      collectRenderMetrics: true,
      collectPhysicsMetrics: true,
      collectMemoryMetrics: true,
      collectSystemMetrics: true,
      collectGPUMetrics: this.config.collectGPUMetrics,
      collectCPUMetrics: this.config.collectCPUMetrics,
      collectNetworkMetrics: this.config.collectNetworkMetrics,
      collectResourceMetrics: this.config.collectResourceMetrics,
      collectEventMetrics: this.config.collectEventMetrics,
      collectGCMetrics: this.config.collectGCMetrics,
      enableBottleneckDetection: this.config.enableBottleneckDetection,
      enableTrendAnalysis: this.config.enableTrendAnalysis,
      enablePerformanceScoring: this.config.enablePerformanceScoring,
      enableOptimizationSuggestions: this.config.enableOptimizationSuggestions,
      enableAutoOptimization: this.config.enableAutoOptimize,
      enableReportExport: this.config.enableReportExport,
      reportExportInterval: this.config.reportExportInterval,
      reportExportFormat: this.config.reportExportFormat,
      reportExportPath: this.config.reportExportPath,
    });

    // 初始化场景优化器
    this.sceneOptimizer = new SceneOptimizer({
      debug: this.config.debug,
    });

    // 初始化LOD生成器
    this.lodGenerator = new EnhancedLODGenerator({
      debug: this.config.debug,
    });

    // 初始化材质优化器
    this.materialOptimizer = new MaterialOptimizer({
      debug: this.config.debug,
    });

    // 初始化内存分析器
    this.memoryAnalyzer = MemoryAnalyzer.getInstance();
    this.memoryAnalyzer.configure({
      enabled: true,
      enableAutoSnapshot: true,
      autoSnapshotInterval: 60000, // 1分钟
      enableLeakDetection: true,
      debug: this.config.debug,
      collectDetailedInfo: true,
      enableWarnings: this.config.enableWarnings
    });

    // 初始化资源跟踪器
    this.resourceTracker = ResourceTracker.getInstance();
    this.resourceTracker.configure({
      enabled: true,
      autoTrackAll: true,
      autoCalculateSize: true,
      debug: this.config.debug
    });

    // 启动内存分析器和资源跟踪器
    this.memoryAnalyzer.start();
    this.resourceTracker.start();

    // 如果启用自动优化，启动定时器
    if (this.config.enableAutoOptimize) {
      this.startAutoOptimize();
    }

    this.initialized = true;

    if (this.config.debug) {
      console.log('性能优化服务已初始化');
    }
  }

  /**
   * 启动性能监控
   */
  public startMonitoring(): void {
    if (!this.initialized) {
      this.initialize();
    }

    if (this.monitoring) {
      return;
    }

    // 启动性能监控器
    PerformanceMonitor.getInstance().start();

    // 设置定时器定期获取性能报告
    this.monitorTimerId = window.setInterval(() => {
      this.updatePerformanceReport();
    }, this.config.monitorSampleInterval);

    this.monitoring = true;

    if (this.config.debug) {
      console.log('性能监控已启动');
    }
  }

  /**
   * 停止性能监控
   */
  public stopMonitoring(): void {
    if (!this.monitoring) {
      return;
    }

    // 停止性能监控器
    PerformanceMonitor.getInstance().stop();

    // 清除定时器
    if (this.monitorTimerId !== null) {
      clearInterval(this.monitorTimerId);
      this.monitorTimerId = null;
    }

    this.monitoring = false;

    if (this.config.debug) {
      console.log('性能监控已停止');
    }
  }

  /**
   * 更新性能报告
   */
  private updatePerformanceReport(): void {
    const report = PerformanceMonitor.getInstance().getReport();

    // 发出性能报告更新事件
    this.eventEmitter.emit(PerformanceOptimizationEventType.PERFORMANCE_REPORT_UPDATED, report);

    // 检查性能警告
    this.checkPerformanceWarnings(report);

    // 分析性能瓶颈
    if (this.config.enableBottleneckDetection && report.bottlenecks && report.bottlenecks.length > 0) {
      this.analyzeBottlenecks(report);
    }

    // 分析性能趋势
    if (this.config.enableTrendAnalysis && report.trends && report.trends.length > 0) {
      this.analyzeTrends(report);
    }

    // 生成优化建议
    if (this.config.enableOptimizationSuggestions) {
      this.generateOptimizationSuggestions(report);
    }

    if (this.config.debug) {
      console.log('性能报告更新', {
        fps: report.metrics[PerformanceMetricType.FPS]?.value,
        score: report.overallScore,
        status: report.status,
        bottlenecks: report.bottlenecks?.length || 0,
        trends: report.trends?.length || 0
      });
    }
  }

  /**
   * 分析性能瓶颈
   * @param report 性能报告
   */
  private analyzeBottlenecks(report: PerformanceReport): void {
    // 处理瓶颈信息
    report.bottlenecks.forEach(bottleneck => {
      // 根据瓶颈类型和严重程度发出事件
      if (bottleneck.severity > 0.7) {
        this.eventEmitter.emit('bottleneckDetected', bottleneck);

        if (this.config.debug) {
          console.warn('检测到严重性能瓶颈', bottleneck);
        }
      }
    });
  }

  /**
   * 分析性能趋势
   * @param report 性能报告
   */
  private analyzeTrends(report: PerformanceReport): void {
    // 处理趋势信息
    report.trends.forEach(trend => {
      // 只关注恶化的趋势
      if (trend.type === PerformanceTrendType.DEGRADING && Math.abs(trend.changeRate) > 0.1) {
        this.eventEmitter.emit('performanceDegrading', trend);

        if (this.config.debug) {
          console.warn('检测到性能恶化趋势', trend);
        }
      }
    });
  }

  /**
   * 生成优化建议
   * @param report 性能报告
   */
  private generateOptimizationSuggestions(report: PerformanceReport): void {
    // 根据瓶颈和趋势生成优化建议
    const suggestions: any[] = [];

    // 从瓶颈生成建议
    if (report.bottlenecks && report.bottlenecks.length > 0) {
      report.bottlenecks.forEach(bottleneck => {
        if (bottleneck.optimizationSuggestions && bottleneck.optimizationSuggestions.length > 0) {
          suggestions.push({
            type: bottleneck.type,
            severity: bottleneck.severity,
            suggestions: bottleneck.optimizationSuggestions
          });
        }
      });
    }

    // 检测资源相关的瓶颈和建议
    this.detectResourceBottlenecks().then(resourceBottlenecks => {
      if (resourceBottlenecks && resourceBottlenecks.length > 0) {
        // 添加资源瓶颈相关的建议
        resourceBottlenecks.forEach(bottleneck => {
          suggestions.push({
            type: bottleneck.type,
            severity: bottleneck.severity,
            suggestions: bottleneck.suggestions
          });
        });

        // 更新资源瓶颈缓存
        this.resourceBottlenecks = resourceBottlenecks;
      }

      // 更新优化建议缓存
      this.optimizationSuggestions = suggestions;

      // 如果有建议，发出事件
      if (suggestions.length > 0) {
        this.eventEmitter.emit(PerformanceOptimizationEventType.OPTIMIZATION_SUGGESTIONS_GENERATED, suggestions);

        if (this.config.debug) {
          console.log('生成优化建议', suggestions);
        }
      }
    });
  }

  /**
   * 检测资源瓶颈
   * @returns 资源瓶颈列表
   */
  public async detectResourceBottlenecks(): Promise<any[]> {
    if (!this.initialized || !this.memoryAnalyzer) {
      return [];
    }

    const bottlenecks: any[] = [];

    try {
      // 获取引擎
      const engine = EngineService.getInstance().getEngine();
      if (!engine) {
        return bottlenecks;
      }

      // 获取资源管理器
      const resourceManager = engine.getResourceManager();
      if (!resourceManager) {
        return bottlenecks;
      }

      // 获取资源列表
      const resources = await resourceManager.getResources();

      // 检查大型纹理
      const largeTextures = resources.filter((r: any) =>
        r.type === 'texture' &&
        r.size > 4 * 1024 * 1024 // 大于4MB的纹理
      );

      if (largeTextures.length > 0) {
        bottlenecks.push({
          id: `large-textures-${Date.now()}`,
          type: 'large-textures',
          name: '大型纹理',
          severity: Math.min(1, largeTextures.length / 10), // 根据大型纹理数量计算严重程度
          description: `检测到 ${largeTextures.length} 个大型纹理，可能导致内存压力`,
          suggestions: [
            '压缩纹理或降低分辨率',
            '使用纹理图集合并小纹理',
            '对远处物体使用低分辨率纹理',
            '实现纹理流式加载',
            '使用纹理缓存管理策略'
          ],
          resources: largeTextures
        });
      }

      // 检查未优化的几何体
      const unoptimizedGeometries = resources.filter((r: any) =>
        r.type === 'geometry' &&
        r.vertexCount > 10000 &&
        !r.optimized
      );

      if (unoptimizedGeometries.length > 0) {
        bottlenecks.push({
          id: `unoptimized-geometries-${Date.now()}`,
          type: 'unoptimized-geometries',
          name: '未优化几何体',
          severity: Math.min(1, unoptimizedGeometries.length / 5), // 根据未优化几何体数量计算严重程度
          description: `检测到 ${unoptimizedGeometries.length} 个未优化的高面数几何体`,
          suggestions: [
            '生成LOD级别',
            '简化高面数模型',
            '使用实例化渲染',
            '合并静态几何体',
            '对远处物体使用简化模型'
          ],
          resources: unoptimizedGeometries
        });
      }

      // 检查内存泄漏
      if (this.memoryAnalyzer) {
        const { potentialLeaks, totalLeakedMemory } = this.memoryAnalyzer.detectLeaks();

        if (potentialLeaks.length > 0) {
          bottlenecks.push({
            id: `memory-leaks-${Date.now()}`,
            type: 'memory-leaks',
            name: '内存泄漏',
            severity: Math.min(1, totalLeakedMemory / (1024 * 1024 * 100)), // 根据泄漏内存大小计算严重程度
            description: `检测到 ${potentialLeaks.length} 个潜在内存泄漏，总计 ${this.formatBytes(totalLeakedMemory)}`,
            suggestions: [
              '检查长时间未释放的资源',
              '确保不再使用的资源被正确释放',
              '实现资源自动清理机制',
              '使用弱引用管理临时资源',
              '定期执行垃圾回收'
            ],
            resources: potentialLeaks
          });
        }
      }

      // 检查资源加载性能
      const resourceLoadMetric = PerformanceMonitor.getInstance().getMetric(PerformanceMetricType.RESOURCE_LOAD_TIME);
      if (resourceLoadMetric && resourceLoadMetric.value > 1000) {
        bottlenecks.push({
          id: `resource-loading-${Date.now()}`,
          type: 'resource-loading',
          name: '资源加载性能',
          severity: Math.min(1, (resourceLoadMetric.value - 1000) / 4000), // 根据加载时间计算严重程度
          description: `资源加载时间过长: ${resourceLoadMetric.value.toFixed(0)} ms`,
          suggestions: [
            '实现资源预加载',
            '优化资源加载顺序',
            '使用资源流式加载',
            '压缩资源文件',
            '实现资源分块加载'
          ]
        });
      }

      // 检查资源碎片化
      const fragmentedResources = resources.filter((r: any) =>
        r.fragmented === true
      );

      if (fragmentedResources.length > 10) {
        bottlenecks.push({
          id: `fragmented-resources-${Date.now()}`,
          type: 'fragmented-resources',
          name: '资源碎片化',
          severity: Math.min(1, fragmentedResources.length / 50), // 根据碎片化资源数量计算严重程度
          description: `检测到 ${fragmentedResources.length} 个碎片化资源`,
          suggestions: [
            '合并小型资源',
            '使用资源图集',
            '实现资源池管理',
            '定期执行资源整理',
            '优化资源布局'
          ],
          resources: fragmentedResources
        });
      }

      return bottlenecks;
    } catch (error) {
      console.error('检测资源瓶颈失败:', error);
      return [];
    }
  }

  /**
   * 获取资源瓶颈
   * @returns 资源瓶颈列表
   */
  public getResourceBottlenecks(): any[] {
    return this.resourceBottlenecks;
  }

  /**
   * 获取优化建议
   * @returns 优化建议列表
   */
  public getOptimizationSuggestions(): any[] {
    return this.optimizationSuggestions;
  }

  /**
   * 获取性能报告
   * @returns 性能报告
   */
  public getReport(): PerformanceReport {
    return PerformanceMonitor.getInstance().getReport();
  }

  /**
   * 生成资源优化建议
   * @returns 优化建议列表
   */
  public async generateOptimizationSuggestions(): Promise<any[]> {
    if (!this.initialized) {
      return [];
    }

    try {
      // 获取性能报告
      const report = PerformanceMonitor.getInstance().getReport();

      // 检测资源瓶颈
      const resourceBottlenecks = await this.detectResourceBottlenecks();

      // 生成优化建议
      const suggestions: any[] = [];

      // 从性能报告中提取建议
      if (report.bottlenecks && report.bottlenecks.length > 0) {
        report.bottlenecks.forEach(bottleneck => {
          if (bottleneck.optimizationSuggestions && bottleneck.optimizationSuggestions.length > 0) {
            suggestions.push({
              id: `bottleneck-${bottleneck.type}-${Date.now()}`,
              title: this.getBottleneckTitle(bottleneck.type),
              description: bottleneck.description,
              impact: this.getSeverityLevel(bottleneck.severity),
              category: this.getBottleneckCategory(bottleneck.type),
              steps: bottleneck.optimizationSuggestions,
              benefits: [
                '提高性能',
                '减少资源使用',
                '改善用户体验'
              ],
              automated: this.isAutomatedOptimization(bottleneck.type),
              applied: false
            });
          }
        });
      }

      // 从资源瓶颈中提取建议
      if (resourceBottlenecks && resourceBottlenecks.length > 0) {
        resourceBottlenecks.forEach(bottleneck => {
          suggestions.push({
            id: bottleneck.id,
            title: bottleneck.name,
            description: bottleneck.description,
            impact: this.getSeverityLevel(bottleneck.severity),
            category: this.getResourceBottleneckCategory(bottleneck.type),
            steps: bottleneck.suggestions,
            benefits: [
              '减少内存使用',
              '提高加载速度',
              '改善渲染性能'
            ],
            automated: this.isAutomatedResourceOptimization(bottleneck.type),
            applied: false
          });
        });
      }

      // 更新优化建议缓存
      this.optimizationSuggestions = suggestions;

      return suggestions;
    } catch (error) {
      console.error('生成优化建议失败:', error);
      return [];
    }
  }

  /**
   * 获取瓶颈标题
   * @param type 瓶颈类型
   * @returns 瓶颈标题
   */
  private getBottleneckTitle(type: string): string {
    switch (type) {
      case PerformanceBottleneckType.CPU:
        return 'CPU性能优化';
      case PerformanceBottleneckType.GPU:
        return 'GPU性能优化';
      case PerformanceBottleneckType.MEMORY:
        return '内存使用优化';
      case PerformanceBottleneckType.NETWORK:
        return '网络性能优化';
      case PerformanceBottleneckType.RENDERING:
        return '渲染性能优化';
      case PerformanceBottleneckType.PHYSICS:
        return '物理性能优化';
      case PerformanceBottleneckType.SCRIPT:
        return '脚本性能优化';
      case PerformanceBottleneckType.RESOURCES:
        return '资源加载优化';
      default:
        return '性能优化';
    }
  }

  /**
   * 获取瓶颈类别
   * @param type 瓶颈类型
   * @returns 瓶颈类别
   */
  private getBottleneckCategory(type: string): string {
    switch (type) {
      case PerformanceBottleneckType.RENDERING:
      case PerformanceBottleneckType.GPU:
        return 'geometry';
      case PerformanceBottleneckType.MEMORY:
        return 'memory';
      case PerformanceBottleneckType.RESOURCES:
        return 'loading';
      default:
        return 'other';
    }
  }

  /**
   * 获取资源瓶颈类别
   * @param type 资源瓶颈类型
   * @returns 资源瓶颈类别
   */
  private getResourceBottleneckCategory(type: string): string {
    switch (type) {
      case 'large-textures':
        return 'texture';
      case 'unoptimized-geometries':
        return 'geometry';
      case 'memory-leaks':
        return 'memory';
      case 'resource-loading':
        return 'loading';
      case 'fragmented-resources':
        return 'memory';
      default:
        return 'other';
    }
  }

  /**
   * 获取严重程度级别
   * @param severity 严重程度值 (0-1)
   * @returns 严重程度级别
   */
  private getSeverityLevel(severity: number): 'high' | 'medium' | 'low' {
    if (severity >= 0.7) {
      return 'high';
    } else if (severity >= 0.4) {
      return 'medium';
    } else {
      return 'low';
    }
  }

  /**
   * 判断是否可自动优化
   * @param type 瓶颈类型
   * @returns 是否可自动优化
   */
  private isAutomatedOptimization(type: string): boolean {
    switch (type) {
      case PerformanceBottleneckType.RENDERING:
      case PerformanceBottleneckType.MEMORY:
      case PerformanceBottleneckType.RESOURCES:
        return true;
      default:
        return false;
    }
  }

  /**
   * 判断资源优化是否可自动执行
   * @param type 资源瓶颈类型
   * @returns 是否可自动优化
   */
  private isAutomatedResourceOptimization(type: string): boolean {
    switch (type) {
      case 'large-textures':
      case 'unoptimized-geometries':
      case 'memory-leaks':
      case 'resource-loading':
        return true;
      default:
        return false;
    }
  }

  /**
   * 格式化字节大小
   * @param bytes 字节数
   * @returns 格式化后的字符串
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 检查性能警告
   * @param report 性能报告
   */
  private checkPerformanceWarnings(report: any): void {
    if (!this.config.enableWarnings) {
      return;
    }

    const warnings = [];

    // 检查FPS
    const fps = report.metrics[PerformanceMetricType.FPS]?.value;
    if (fps !== undefined && fps < this.config.fpsWarningThreshold!) {
      warnings.push({
        type: 'fps',
        message: `FPS过低: ${fps.toFixed(1)}`,
        value: fps,
        threshold: this.config.fpsWarningThreshold,
      });
    }

    // 检查内存使用
    const memory = report.metrics[PerformanceMetricType.MEMORY_USAGE]?.value;
    if (memory !== undefined && memory > this.config.memoryWarningThreshold!) {
      warnings.push({
        type: 'memory',
        message: `内存使用过高: ${memory.toFixed(1)} MB`,
        value: memory,
        threshold: this.config.memoryWarningThreshold,
      });
    }

    // 检查渲染时间
    const renderTime = report.metrics[PerformanceMetricType.RENDER_TIME]?.value;
    if (renderTime !== undefined && renderTime > this.config.renderTimeWarningThreshold!) {
      warnings.push({
        type: 'renderTime',
        message: `渲染时间过长: ${renderTime.toFixed(1)} ms`,
        value: renderTime,
        threshold: this.config.renderTimeWarningThreshold,
      });
    }

    // 如果有警告，发出事件
    if (warnings.length > 0) {
      this.eventEmitter.emit(PerformanceOptimizationEventType.PERFORMANCE_WARNING, warnings);
    }
  }

  /**
   * 启动自动优化
   */
  public startAutoOptimize(): void {
    if (this.autoOptimizeTimerId !== null) {
      return;
    }

    // 设置定时器定期执行优化
    this.autoOptimizeTimerId = window.setInterval(() => {
      this.optimizeScene();
    }, this.config.autoOptimizeInterval);

    if (this.config.debug) {
      console.log('自动优化已启动');
    }
  }

  /**
   * 停止自动优化
   */
  public stopAutoOptimize(): void {
    if (this.autoOptimizeTimerId === null) {
      return;
    }

    // 清除定时器
    clearInterval(this.autoOptimizeTimerId);
    this.autoOptimizeTimerId = null;

    if (this.config.debug) {
      console.log('自动优化已停止');
    }
  }

  /**
   * 优化场景
   * @returns 优化结果
   */
  public async optimizeScene(): Promise<any> {
    if (!this.initialized || this.optimizing) {
      return null;
    }

    this.optimizing = true;

    // 发出优化开始事件
    this.eventEmitter.emit(PerformanceOptimizationEventType.OPTIMIZATION_START);

    try {
      // 获取当前场景
      const scene = SceneService.getInstance().getActiveScene();
      if (!scene) {
        throw new Error('没有活动场景');
      }

      // 优化结果
      const results: any = {
        rendering: null,
        memory: null,
        ui: null,
        resource: null,
      };

      // 优化渲染性能
      if (this.config.enableRenderingOptimization) {
        results.rendering = await this.optimizeRendering(scene);
      }

      // 优化内存使用
      if (this.config.enableMemoryOptimization) {
        results.memory = await this.optimizeMemory();
      }

      // 优化UI性能
      if (this.config.enableUIOptimization) {
        results.ui = await this.optimizeUI();
      }

      // 优化资源加载
      if (this.config.enableResourceOptimization) {
        results.resource = await this.optimizeResources();
      }

      // 发出优化完成事件
      this.eventEmitter.emit(PerformanceOptimizationEventType.OPTIMIZATION_COMPLETE, results);

      this.optimizing = false;
      return results;
    } catch (error) {
      // 发出优化失败事件
      this.eventEmitter.emit(PerformanceOptimizationEventType.OPTIMIZATION_FAILED, error);

      this.optimizing = false;
      throw error;
    }
  }

  /**
   * 优化渲染性能
   * @param scene 场景
   * @returns 优化结果
   */
  private async optimizeRendering(scene: any): Promise<any> {
    if (!this.sceneOptimizer) {
      return null;
    }

    const results: any = {
      lodGenerated: 0,
      materialOptimized: 0,
      batchesCreated: 0,
      instancesCreated: 0,
      cullingEnabled: false,
    };

    // 分析场景
    const analysis = await this.sceneOptimizer.analyzeScene(scene);

    // 应用LOD优化
    if (analysis.highPolyMeshes && analysis.highPolyMeshes.length > 0) {
      for (const mesh of analysis.highPolyMeshes) {
        if (this.lodGenerator) {
          const lodResult = await this.lodGenerator.generateEnhanced(mesh);
          if (lodResult && lodResult.levels.length > 0) {
            results.lodGenerated++;
          }
        }
      }
    }

    // 应用材质优化
    if (analysis.complexMaterials && analysis.complexMaterials.length > 0) {
      for (const material of analysis.complexMaterials) {
        if (this.materialOptimizer) {
          const optimizedMaterial = this.materialOptimizer.optimizeMaterial(material);
          if (optimizedMaterial !== material) {
            results.materialOptimized++;
          }
        }
      }
    }

    // 应用批处理优化
    if (analysis.batchableMeshes && analysis.batchableMeshes.length > 0) {
      if (!this.batchingSystem) {
        this.batchingSystem = new BatchingSystem({
          debug: this.config.debug,
        });
        EngineService.getInstance().getEngine().addSystem(this.batchingSystem);
      }

      const batchId = this.batchingSystem.createStaticBatch(analysis.batchableMeshes, '自动批处理');
      if (batchId) {
        results.batchesCreated++;
      }
    }

    // 应用实例化渲染优化
    if (analysis.instanceableMeshes && analysis.instanceableMeshes.length > 0) {
      if (!this.instancedRenderingSystem) {
        this.instancedRenderingSystem = new InstancedRenderingSystem({
          debug: this.config.debug,
        });
        EngineService.getInstance().getEngine().addSystem(this.instancedRenderingSystem);
      }

      for (const group of analysis.instanceableMeshes) {
        const instanceId = this.instancedRenderingSystem.createInstancedMesh(group);
        if (instanceId) {
          results.instancesCreated++;
        }
      }
    }

    // 应用视锥体剔除优化
    if (!this.frustumCullingSystem) {
      this.frustumCullingSystem = new FrustumCullingSystem({
        debug: this.config.debug,
      });
      EngineService.getInstance().getEngine().addSystem(this.frustumCullingSystem);
      results.cullingEnabled = true;
    }

    // 应用遮挡剔除优化（如果支持）
    if (analysis.supportsOcclusionCulling && !this.occlusionCullingSystem) {
      this.occlusionCullingSystem = new OcclusionCullingSystem({
        debug: this.config.debug,
      });
      EngineService.getInstance().getEngine().addSystem(this.occlusionCullingSystem);
    }

    return results;
  }

  /**
   * 优化内存使用
   * @returns 优化结果
   */
  private async optimizeMemory(): Promise<any> {
    const results: any = {
      texturesOptimized: 0,
      geometriesOptimized: 0,
      resourcesReleased: 0,
    };

    // 获取引擎
    const engine = EngineService.getInstance().getEngine();
    if (!engine) {
      return results;
    }

    // 优化纹理
    const textureManager = engine.getTextureManager();
    if (textureManager) {
      results.texturesOptimized = await textureManager.optimizeAll();
    }

    // 优化几何体
    const geometryManager = engine.getGeometryManager();
    if (geometryManager) {
      results.geometriesOptimized = await geometryManager.optimizeAll();
    }

    // 释放未使用的资源
    const resourceManager = engine.getResourceManager();
    if (resourceManager) {
      results.resourcesReleased = await resourceManager.cleanup();
    }

    return results;
  }

  /**
   * 优化UI性能
   * @returns 优化结果
   */
  private async optimizeUI(): Promise<any> {
    const results: any = {
      componentsOptimized: 0,
      eventListenersOptimized: 0,
    };

    // 获取UI系统
    const engine = EngineService.getInstance().getEngine();
    if (!engine) {
      return results;
    }

    const uiSystem = engine.getSystem('UISystem');
    if (!uiSystem) {
      return results;
    }

    // 优化UI组件
    results.componentsOptimized = await uiSystem.optimizeComponents();

    // 优化事件监听器
    results.eventListenersOptimized = await uiSystem.optimizeEventListeners();

    return results;
  }

  /**
   * 优化资源加载
   * @returns 优化结果
   */
  private async optimizeResources(): Promise<any> {
    const results: any = {
      resourcesOptimized: 0,
      loadingStrategyUpdated: false,
      texturesOptimized: 0,
      geometriesOptimized: 0,
      resourcesReleased: 0,
    };

    // 获取资源管理器
    const engine = EngineService.getInstance().getEngine();
    if (!engine) {
      return results;
    }

    const resourceManager = engine.getResourceManager();
    if (!resourceManager) {
      return results;
    }

    // 优化资源加载策略
    const loadingStrategyUpdated = await resourceManager.optimizeLoadingStrategy();
    if (loadingStrategyUpdated) {
      results.loadingStrategyUpdated = true;
    }

    // 优化资源
    results.resourcesOptimized = await resourceManager.optimizeResources();

    // 优化纹理
    const textureManager = engine.getTextureManager();
    if (textureManager) {
      results.texturesOptimized = await textureManager.optimizeAll();
    }

    // 优化几何体
    const geometryManager = engine.getGeometryManager();
    if (geometryManager) {
      results.geometriesOptimized = await geometryManager.optimizeAll();
    }

    // 释放未使用的资源
    if (resourceManager) {
      results.resourcesReleased = await resourceManager.cleanup();
    }

    return results;
  }

  /**
   * 优化纹理
   * @returns 优化结果
   */
  public async optimizeTextures(): Promise<any> {
    const results: any = {
      texturesOptimized: 0,
      memorySaved: 0,
    };

    try {
      // 获取引擎
      const engine = EngineService.getInstance().getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 获取纹理管理器
      const textureManager = engine.getTextureManager();
      if (!textureManager) {
        throw new Error('纹理管理器未初始化');
      }

      // 优化纹理
      const optimizationResult = await textureManager.optimizeAll();

      results.texturesOptimized = optimizationResult.count;
      results.memorySaved = optimizationResult.memorySaved;

      return results;
    } catch (error) {
      console.error('优化纹理失败:', error);
      throw error;
    }
  }

  /**
   * 创建纹理图集
   * @returns 优化结果
   */
  public async createTextureAtlases(): Promise<any> {
    const results: any = {
      atlasesCreated: 0,
      texturesIncluded: 0,
      memorySaved: 0,
    };

    try {
      // 获取引擎
      const engine = EngineService.getInstance().getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 获取纹理管理器
      const textureManager = engine.getTextureManager();
      if (!textureManager) {
        throw new Error('纹理管理器未初始化');
      }

      // 创建纹理图集
      const atlasResult = await textureManager.createAtlases();

      results.atlasesCreated = atlasResult.atlasCount;
      results.texturesIncluded = atlasResult.textureCount;
      results.memorySaved = atlasResult.memorySaved;

      return results;
    } catch (error) {
      console.error('创建纹理图集失败:', error);
      throw error;
    }
  }

  /**
   * 生成LOD级别
   * @returns 优化结果
   */
  public async generateLODs(): Promise<any> {
    const results: any = {
      modelsOptimized: 0,
      levelsGenerated: 0,
      memorySaved: 0,
    };

    try {
      // 获取引擎
      const engine = EngineService.getInstance().getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 获取当前场景
      const scene = SceneService.getInstance().getActiveScene();
      if (!scene) {
        throw new Error('没有活动场景');
      }

      // 分析场景
      const analysis = await this.sceneOptimizer?.analyzeScene(scene);
      if (!analysis) {
        throw new Error('场景分析失败');
      }

      // 应用LOD优化
      if (analysis.highPolyMeshes && analysis.highPolyMeshes.length > 0) {
        for (const mesh of analysis.highPolyMeshes) {
          if (this.lodGenerator) {
            const lodResult = await this.lodGenerator.generateEnhanced(mesh);
            if (lodResult && lodResult.levels.length > 0) {
              results.modelsOptimized++;
              results.levelsGenerated += lodResult.levels.length;
              results.memorySaved += lodResult.memorySaved;
            }
          }
        }
      }

      return results;
    } catch (error) {
      console.error('生成LOD级别失败:', error);
      throw error;
    }
  }

  /**
   * 启用实例化渲染
   * @returns 优化结果
   */
  public async enableInstancing(): Promise<any> {
    const results: any = {
      instancesCreated: 0,
      objectsInstanced: 0,
      drawCallsReduced: 0,
    };

    try {
      // 获取引擎
      const engine = EngineService.getInstance().getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 获取当前场景
      const scene = SceneService.getInstance().getActiveScene();
      if (!scene) {
        throw new Error('没有活动场景');
      }

      // 分析场景
      const analysis = await this.sceneOptimizer?.analyzeScene(scene);
      if (!analysis) {
        throw new Error('场景分析失败');
      }

      // 应用实例化渲染优化
      if (analysis.instanceableMeshes && analysis.instanceableMeshes.length > 0) {
        if (!this.instancedRenderingSystem) {
          this.instancedRenderingSystem = new InstancedRenderingSystem({
            debug: this.config.debug,
          });
          engine.addSystem(this.instancedRenderingSystem);
        }

        for (const group of analysis.instanceableMeshes) {
          const instanceId = this.instancedRenderingSystem.createInstancedMesh(group);
          if (instanceId) {
            results.instancesCreated++;
            results.objectsInstanced += group.length;
            results.drawCallsReduced += group.length - 1;
          }
        }
      }

      return results;
    } catch (error) {
      console.error('启用实例化渲染失败:', error);
      throw error;
    }
  }

  /**
   * 清理内存
   * @returns 优化结果
   */
  public async cleanupMemory(): Promise<any> {
    const results: any = {
      resourcesReleased: 0,
      memorySaved: 0,
    };

    try {
      // 获取引擎
      const engine = EngineService.getInstance().getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 获取资源管理器
      const resourceManager = engine.getResourceManager();
      if (!resourceManager) {
        throw new Error('资源管理器未初始化');
      }

      // 执行内存清理
      const cleanupResult = await resourceManager.cleanup();

      results.resourcesReleased = cleanupResult.count;
      results.memorySaved = cleanupResult.memorySaved;

      return results;
    } catch (error) {
      console.error('清理内存失败:', error);
      throw error;
    }
  }

  /**
   * 启用延迟加载
   * @returns 优化结果
   */
  public async enableLazyLoading(): Promise<any> {
    const results: any = {
      enabled: false,
      resourcesAffected: 0,
    };

    try {
      // 获取引擎
      const engine = EngineService.getInstance().getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 获取资源管理器
      const resourceManager = engine.getResourceManager();
      if (!resourceManager) {
        throw new Error('资源管理器未初始化');
      }

      // 启用延迟加载
      const lazyLoadingResult = await resourceManager.enableLazyLoading();

      results.enabled = lazyLoadingResult.enabled;
      results.resourcesAffected = lazyLoadingResult.resourcesAffected;

      return results;
    } catch (error) {
      console.error('启用延迟加载失败:', error);
      throw error;
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param callback 回调函数
   */
  public on(event: string, callback: Function): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param callback 回调函数
   */
  public off(event: string, callback: Function): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 获取性能报告
   * @returns 性能报告
   */
  public getPerformanceReport(): any {
    return PerformanceMonitor.getInstance().getReport();
  }

  /**
   * 获取配置
   * @returns 配置
   */
  public getConfig(): PerformanceOptimizationServiceConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   * @param config 新配置
   */
  public updateConfig(config: Partial<PerformanceOptimizationServiceConfig>): void {
    // 更新配置
    this.config = {
      ...this.config,
      ...config,
    };

    // 更新性能监控器配置
    PerformanceMonitor.getInstance().configure({
      enabled: this.config.enabled,
      sampleInterval: this.config.monitorSampleInterval,
      historyLimit: this.config.monitorHistoryLength,
      autoSample: true,
      debug: this.config.debug,
      enableWarnings: this.config.enableWarnings,
      collectRenderMetrics: true,
      collectPhysicsMetrics: true,
      collectMemoryMetrics: true,
      collectSystemMetrics: true,
      collectGPUMetrics: this.config.collectGPUMetrics,
      collectCPUMetrics: this.config.collectCPUMetrics,
      collectNetworkMetrics: this.config.collectNetworkMetrics,
      collectResourceMetrics: this.config.collectResourceMetrics,
      collectEventMetrics: this.config.collectEventMetrics,
      collectGCMetrics: this.config.collectGCMetrics,
      enableBottleneckDetection: this.config.enableBottleneckDetection,
      enableTrendAnalysis: this.config.enableTrendAnalysis,
      enablePerformanceScoring: this.config.enablePerformanceScoring,
      enableOptimizationSuggestions: this.config.enableOptimizationSuggestions,
      enableAutoOptimization: this.config.enableAutoOptimize,
      enableReportExport: this.config.enableReportExport,
      reportExportInterval: this.config.reportExportInterval,
      reportExportFormat: this.config.reportExportFormat,
      reportExportPath: this.config.reportExportPath,
    });

    // 如果启用自动优化，启动定时器
    if (this.config.enableAutoOptimize) {
      this.startAutoOptimize();
    } else {
      this.stopAutoOptimize();
    }
  }
}
