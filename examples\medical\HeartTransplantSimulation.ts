import { World, Entity } from '../../engine/core';
import { HeartTransplantProcedure } from '../../engine/src/surgery/HeartTransplantProcedure';
import { SurgicalToolSystem } from '../../engine/src/surgery/SurgicalToolSystem';
import { VascularSystem } from '../../engine/src/physics/softbody/specialized/VascularSystem';
import { PatientMonitoringSystem } from '../../engine/src/medical/PatientMonitoringSystem';

/**
 * 心脏移植手术模拟示例
 */
export class HeartTransplantSimulation {
  private world: World;
  private patientEntity: Entity;
  private donorHeartEntity: Entity;
  private surgicalToolSystem: SurgicalToolSystem;
  private transplantProcedure: HeartTransplantProcedure;
  
  constructor() {
    // 创建世界
    this.world = new World();
    
    // 添加必要的系统
    this.surgicalToolSystem = new SurgicalToolSystem(this.world);
    const vascularSystem = new VascularSystem(this.world);
    const monitoringSystem = new PatientMonitoringSystem(this.world);
    
    this.world.addSystem(this.surgicalToolSystem);
    this.world.addSystem(vascularSystem);
    this.world.addSystem(monitoringSystem);
    
    // 创建患者和供体心脏
    this.patientEntity = this.createPatient();
    this.donorHeartEntity = this.createDonorHeart();
    
    // 创建手术流程管理器
    this.transplantProcedure = new HeartTransplantProcedure(
      this.world,
      this.patientEntity,
      this.donorHeartEntity,
      this.surgicalToolSystem,
      vascularSystem,
      monitoringSystem
    );
  }
  
  /**
   * 开始手术模拟
   */
  public startSimulation(): void {
    // 初始化系统
    this.world.initialize();
    
    // 开始手术流程
    this.transplantProcedure.start();
    
    // 启动引擎循环
    this.world.start();
  }
}