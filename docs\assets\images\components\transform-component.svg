<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <style>
    .panel {
      fill: #f5f5f5;
      stroke: #ddd;
      stroke-width: 1;
    }
    .header {
      fill: #e0e0e0;
      stroke: #ddd;
      stroke-width: 1;
    }
    .title {
      font-family: 'Arial', sans-serif;
      font-size: 16px;
      font-weight: bold;
    }
    .label {
      font-family: 'Arial', sans-serif;
      font-size: 14px;
    }
    .value {
      font-family: 'Consolas', monospace;
      font-size: 14px;
    }
    .input {
      fill: white;
      stroke: #ccc;
      stroke-width: 1;
    }
    .button {
      fill: #4a90e2;
      stroke: #3a80d2;
      stroke-width: 1;
      rx: 4;
      ry: 4;
    }
    .button-text {
      font-family: 'Arial', sans-serif;
      font-size: 12px;
      fill: white;
      text-anchor: middle;
      dominant-baseline: middle;
    }
  </style>
  
  <!-- Inspector Panel -->
  <rect x="50" y="50" width="500" height="300" class="panel" rx="5" ry="5" />
  
  <!-- Header -->
  <rect x="50" y="50" width="500" height="40" class="header" rx="5" ry="5" />
  <text x="70" y="75" class="title">变换组件 (Transform)</text>
  
  <!-- Position -->
  <text x="70" y="120" class="label">位置 (Position)</text>
  <rect x="200" y="105" width="60" height="20" class="input" />
  <text x="230" y="120" class="value" text-anchor="middle">0</text>
  <rect x="270" y="105" width="60" height="20" class="input" />
  <text x="300" y="120" class="value" text-anchor="middle">1</text>
  <rect x="340" y="105" width="60" height="20" class="input" />
  <text x="370" y="120" class="value" text-anchor="middle">0</text>
  <rect x="410" y="105" width="60" height="20" class="button" />
  <text x="440" y="115" class="button-text">重置</text>
  
  <!-- Rotation -->
  <text x="70" y="160" class="label">旋转 (Rotation)</text>
  <rect x="200" y="145" width="60" height="20" class="input" />
  <text x="230" y="160" class="value" text-anchor="middle">0</text>
  <rect x="270" y="145" width="60" height="20" class="input" />
  <text x="300" y="160" class="value" text-anchor="middle">45</text>
  <rect x="340" y="145" width="60" height="20" class="input" />
  <text x="370" y="160" class="value" text-anchor="middle">0</text>
  <rect x="410" y="145" width="60" height="20" class="button" />
  <text x="440" y="155" class="button-text">重置</text>
  
  <!-- Scale -->
  <text x="70" y="200" class="label">缩放 (Scale)</text>
  <rect x="200" y="185" width="60" height="20" class="input" />
  <text x="230" y="200" class="value" text-anchor="middle">2</text>
  <rect x="270" y="185" width="60" height="20" class="input" />
  <text x="300" y="200" class="value" text-anchor="middle">2</text>
  <rect x="340" y="185" width="60" height="20" class="input" />
  <text x="370" y="200" class="value" text-anchor="middle">2</text>
  <rect x="410" y="185" width="60" height="20" class="button" />
  <text x="440" y="195" class="button-text">重置</text>
  
  <!-- Local/World Toggle -->
  <text x="70" y="240" class="label">坐标空间</text>
  <rect x="200" y="225" width="80" height="20" class="input" />
  <text x="240" y="240" class="value" text-anchor="middle">世界</text>
  <rect x="290" y="225" width="80" height="20" class="input" />
  <text x="330" y="240" class="value" text-anchor="middle">局部</text>
  
  <!-- Parent -->
  <text x="70" y="280" class="label">父级</text>
  <rect x="200" y="265" width="200" height="20" class="input" />
  <text x="210" y="280" class="value">MainScene</text>
  <rect x="410" y="265" width="60" height="20" class="button" />
  <text x="440" y="275" class="button-text">选择</text>
  
  <!-- Coordinate Axes -->
  <line x1="480" y1="110" x2="510" y2="110" stroke="red" stroke-width="2" />
  <text x="520" y="115" fill="red" class="label">X</text>
  
  <line x1="480" y1="130" x2="510" y2="130" stroke="green" stroke-width="2" />
  <text x="520" y="135" fill="green" class="label">Y</text>
  
  <line x1="480" y1="150" x2="510" y2="150" stroke="blue" stroke-width="2" />
  <text x="520" y="155" fill="blue" class="label">Z</text>
</svg>
