/**
 * 面部动画预览样式
 */
.facial-animation-preview {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #1a1a1a;
  
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1000;
  }
  
  .preview-toolbar {
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 10;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 8px;
    border-radius: 4px;
    backdrop-filter: blur(4px);
  }
  
  canvas {
    width: 100%;
    height: 100%;
  }
}
