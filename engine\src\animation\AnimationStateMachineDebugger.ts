/**
 * 动画状态机调试器
 * 用于调试和可视化动画状态机
 */
import { AnimationState, AnimationStateMachine, AnimationStateMachineEventType, TransitionRule } from './AnimationStateMachine';

/**
 * 调试事件类型
 */
export enum DebugEventType {
  /** 状态进入 */
  STATE_ENTER = 'stateEnter',
  /** 状态退出 */
  STATE_EXIT = 'stateExit',
  /** 状态转换开始 */
  TRANSITION_START = 'transitionStart',
  /** 状态转换结束 */
  TRANSITION_END = 'transitionEnd',
  /** 条件评估 */
  CONDITION_EVALUATE = 'conditionEvaluate',
  /** 参数变化 */
  PARAMETER_CHANGE = 'parameterChange'
}

/**
 * 调试事件
 */
export interface DebugEvent {
  /** 事件类型 */
  type: DebugEventType;
  /** 事件时间 */
  time: number;
  /** 事件数据 */
  data: any;
}

/**
 * 动画状态机调试器
 */
export class AnimationStateMachineDebugger {
  /** 状态机 */
  private stateMachine: AnimationStateMachine;
  /** 调试事件列表 */
  private events: DebugEvent[] = [];
  /** 最大事件数量 */
  private maxEvents: number = 100;
  /** 是否启用 */
  private enabled: boolean = false;
  /** 开始时间 */
  private startTime: number = 0;
  /** 原始参数设置方法 */
  private originalSetParameter: (name: string, value: any) => void;
  /** 监听器映射 */
  private listeners: Map<DebugEventType, ((event: DebugEvent) => void)[]> = new Map();

  /**
   * 构造函数
   * @param stateMachine 状态机
   * @param maxEvents 最大事件数量
   */
  constructor(stateMachine: AnimationStateMachine, maxEvents: number = 100) {
    this.stateMachine = stateMachine;
    this.maxEvents = maxEvents;
    this.startTime = Date.now();

    // 保存原始方法
    this.originalSetParameter = stateMachine.setParameter.bind(stateMachine);

    // 添加事件监听器
    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听状态机事件
    this.stateMachine.addEventListener(AnimationStateMachineEventType.STATE_ENTER, (state) => {
      if (!this.enabled) return;
      this.addEvent(DebugEventType.STATE_ENTER, { state });
    });

    this.stateMachine.addEventListener(AnimationStateMachineEventType.STATE_EXIT, (state) => {
      if (!this.enabled) return;
      this.addEvent(DebugEventType.STATE_EXIT, { state });
    });

    this.stateMachine.addEventListener(AnimationStateMachineEventType.TRANSITION_START, (transition) => {
      if (!this.enabled) return;
      this.addEvent(DebugEventType.TRANSITION_START, { transition });
    });

    this.stateMachine.addEventListener(AnimationStateMachineEventType.TRANSITION_END, (transition) => {
      if (!this.enabled) return;
      this.addEvent(DebugEventType.TRANSITION_END, { transition });
    });

    // 重写setParameter方法以捕获参数变化
    this.stateMachine.setParameter = (name: string, value: any): void => {
      const oldValue = this.stateMachine.getParameter(name);
      
      // 调用原始方法
      this.originalSetParameter(name, value);
      
      // 添加调试事件
      if (this.enabled && oldValue !== value) {
        this.addEvent(DebugEventType.PARAMETER_CHANGE, { name, oldValue, newValue: value });
      }
    };
  }

  /**
   * 添加调试事件
   * @param type 事件类型
   * @param data 事件数据
   */
  private addEvent(type: DebugEventType, data: any): void {
    const event: DebugEvent = {
      type,
      time: (Date.now() - this.startTime) / 1000,
      data
    };

    // 添加事件
    this.events.push(event);

    // 如果超过最大事件数量，则移除最旧的事件
    if (this.events.length > this.maxEvents) {
      this.events.shift();
    }

    // 触发监听器
    this.triggerListeners(event);
  }

  /**
   * 触发监听器
   * @param event 事件
   */
  private triggerListeners(event: DebugEvent): void {
    const listeners = this.listeners.get(event.type);
    if (listeners) {
      for (const listener of listeners) {
        try {
          listener(event);
        } catch (error) {
          console.error('调试事件监听器错误:', error);
        }
      }
    }
  }

  /**
   * 启用调试器
   */
  public enable(): void {
    this.enabled = true;
    this.stateMachine.setDebugMode(true);
  }

  /**
   * 禁用调试器
   */
  public disable(): void {
    this.enabled = false;
    this.stateMachine.setDebugMode(false);
  }

  /**
   * 获取是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 清空事件
   */
  public clearEvents(): void {
    this.events = [];
    this.startTime = Date.now();
  }

  /**
   * 获取事件
   * @returns 事件列表
   */
  public getEvents(): DebugEvent[] {
    return [...this.events];
  }

  /**
   * 获取最近的事件
   * @param count 事件数量
   * @returns 事件列表
   */
  public getRecentEvents(count: number = 10): DebugEvent[] {
    return this.events.slice(-count);
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addEventListener(type: DebugEventType, listener: (event: DebugEvent) => void): void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, []);
    }
    this.listeners.get(type)!.push(listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeEventListener(type: DebugEventType, listener: (event: DebugEvent) => void): void {
    if (!this.listeners.has(type)) return;
    
    const listeners = this.listeners.get(type)!;
    const index = listeners.indexOf(listener);
    
    if (index !== -1) {
      listeners.splice(index, 1);
    }
  }

  /**
   * 销毁调试器
   */
  public destroy(): void {
    this.disable();
    this.events = [];
    this.listeners.clear();
    
    // 恢复原始方法
    this.stateMachine.setParameter = this.originalSetParameter;
  }
}
