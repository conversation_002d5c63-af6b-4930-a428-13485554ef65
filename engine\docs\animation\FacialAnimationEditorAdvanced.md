# 面部动画编辑器高级功能

本文档介绍面部动画编辑器的高级功能，包括时间轴编辑、关键帧编辑、曲线编辑、实时预览等功能的详细使用方法。

## 高级时间轴编辑

时间轴编辑是面部动画编辑器的核心功能，支持多轨道编辑、嵌套时间轴、标记点和事件触发等高级功能。

### 多轨道编辑

多轨道编辑允许同时编辑多个动画属性，如表情、口型、眼睛动画等。

```typescript
// 创建表情轨道
const expressionTrack = editor.createTrack('表情', {
  type: 'expression',
  color: '#ff5555'
});

// 创建口型轨道
const visemeTrack = editor.createTrack('口型', {
  type: 'viseme',
  color: '#55ff55'
});

// 创建眼睛轨道
const eyeTrack = editor.createTrack('眼睛', {
  type: 'eye',
  color: '#5555ff'
});
```

### 嵌套时间轴

嵌套时间轴允许创建复杂的动画层次结构，实现更灵活的动画控制。

```typescript
// 创建父时间轴
const parentTimeline = editor.createTimeline('主动画');

// 创建子时间轴
const childTimeline = editor.createTimeline('表情动画');

// 将子时间轴添加到父时间轴
parentTimeline.addChild(childTimeline, 1.0); // 在1秒处添加子时间轴
```

### 标记点和事件触发

标记点和事件触发允许在动画播放过程中触发特定事件，如音效、粒子效果等。

```typescript
// 添加标记点
editor.addMarker('音效触发', 2.5);

// 添加事件监听
editor.addEventListener('markerReached', (data) => {
  if (data.marker === '音效触发') {
    // 播放音效
    audioSystem.playSound('laugh.mp3');
  }
});
```

## 高级关键帧编辑

关键帧编辑支持多种插值方式、关键帧组、关键帧模板等高级功能。

### 多种插值方式

支持多种插值方式，如线性、贝塞尔曲线、阶跃等，实现更丰富的动画效果。

```typescript
// 添加线性插值关键帧
editor.addKeyframe(0.0, {
  expression: FacialExpressionType.NEUTRAL,
  interpolation: 'linear'
});

// 添加贝塞尔曲线插值关键帧
editor.addKeyframe(1.0, {
  expression: FacialExpressionType.HAPPY,
  interpolation: 'bezier',
  bezierControls: [0.25, 0.1, 0.25, 1.0]
});

// 添加阶跃插值关键帧
editor.addKeyframe(2.0, {
  expression: FacialExpressionType.SURPRISED,
  interpolation: 'step'
});
```

### 关键帧组

关键帧组允许同时编辑多个关键帧，实现更高效的动画编辑。

```typescript
// 创建关键帧组
const keyframeGroup = editor.createKeyframeGroup('表情变化');

// 添加关键帧到组
keyframeGroup.addKeyframe(0.0, {
  expression: FacialExpressionType.NEUTRAL
});

keyframeGroup.addKeyframe(1.0, {
  expression: FacialExpressionType.HAPPY
});

// 移动整个关键帧组
keyframeGroup.move(0.5); // 将整个组向后移动0.5秒
```

### 关键帧模板

关键帧模板允许保存和重用常用的关键帧设置，提高编辑效率。

```typescript
// 保存关键帧模板
editor.saveKeyframeTemplate('开心表情', {
  expression: FacialExpressionType.HAPPY,
  expressionWeight: 1.0,
  interpolation: 'bezier',
  bezierControls: [0.25, 0.1, 0.25, 1.0]
});

// 使用关键帧模板
editor.addKeyframeFromTemplate(1.0, '开心表情');
```

## 高级曲线编辑

曲线编辑支持多点编辑、曲线预设、曲线导入导出等高级功能。

### 多点编辑

多点编辑允许同时编辑多个控制点，实现更精确的曲线控制。

```typescript
// 添加多个控制点
editor.addCurveControlPoint('表情', 0.0, 0.0);
editor.addCurveControlPoint('表情', 0.5, 0.8);
editor.addCurveControlPoint('表情', 1.0, 1.0);

// 选择多个控制点
editor.selectCurveControlPoints('表情', [0, 1]);

// 移动选中的控制点
editor.moveSelectedCurveControlPoints(0.1, 0.2);
```

### 曲线预设

曲线预设提供常用的曲线类型，如线性、缓入、缓出、弹性等。

```typescript
// 应用线性曲线预设
editor.applyCurvePreset('表情', 'linear');

// 应用缓入曲线预设
editor.applyCurvePreset('表情', 'easeIn');

// 应用弹性曲线预设
editor.applyCurvePreset('表情', 'elastic');
```

### 曲线导入导出

曲线导入导出允许保存和重用曲线设置，实现跨项目的曲线共享。

```typescript
// 导出曲线
const curveData = editor.exportCurve('表情');

// 导入曲线
editor.importCurve('口型', curveData);
```

## 实时预览增强

实时预览支持多视角预览、对比预览、慢动作预览等高级功能。

### 多视角预览

多视角预览允许同时从多个角度查看动画效果，更全面地评估动画质量。

```typescript
// 添加预览视角
editor.addPreviewCamera('正面', {
  position: new THREE.Vector3(0, 1.6, 2),
  target: new THREE.Vector3(0, 1.6, 0)
});

editor.addPreviewCamera('侧面', {
  position: new THREE.Vector3(2, 1.6, 0),
  target: new THREE.Vector3(0, 1.6, 0)
});

// 切换预览视角
editor.setActivePreviewCamera('侧面');
```

### 对比预览

对比预览允许同时查看多个动画的效果，方便比较和调整。

```typescript
// 启用对比预览
editor.enableComparisonPreview(['表情动画', '口型动画']);

// 设置对比模式
editor.setComparisonMode('sideBySide'); // 或 'overlay'
```

### 慢动作预览

慢动作预览允许以较慢的速度查看动画细节，方便精细调整。

```typescript
// 设置播放速度
editor.setPlaybackSpeed(0.5); // 半速播放

// 恢复正常速度
editor.setPlaybackSpeed(1.0);
```

## 高级导入导出

高级导入导出支持多种格式、批量操作、版本控制等功能。

### 多种格式支持

支持多种动画格式，如JSON、FBX、BVH等，实现与其他工具的互操作。

```typescript
// 导出为JSON
const jsonData = editor.exportToJSON();

// 导出为FBX
const fbxData = editor.exportToFBX();

// 导入FBX
editor.importFromFBX(fbxData);
```

### 批量操作

批量操作允许同时处理多个动画片段，提高工作效率。

```typescript
// 批量导出
const exportData = editor.batchExport(['表情动画', '口型动画']);

// 批量导入
editor.batchImport(exportData);
```

### 版本控制

版本控制允许保存动画的多个版本，方便回溯和比较。

```typescript
// 保存版本
const versionId = editor.saveVersion('初始版本');

// 加载版本
editor.loadVersion(versionId);

// 比较版本
editor.compareVersions(versionId1, versionId2);
```
