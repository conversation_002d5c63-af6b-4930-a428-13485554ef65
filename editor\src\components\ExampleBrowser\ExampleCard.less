/**
 * 示例项目卡片样式
 */
@import '../../styles/variables.less';

.example-card {
  height: 100%;
  transition: all 0.3s;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .example-image-container {
    position: relative;
    height: 160px;
    overflow: hidden;

    .example-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s;
    }

    .example-category {
      position: absolute;
      top: 8px;
      left: 8px;
      z-index: 1;
    }

    .example-difficulty {
      position: absolute;
      top: 8px;
      right: 8px;
      z-index: 1;
    }
  }

  .example-card-content {
    padding: 16px;
    display: flex;
    flex-direction: column;
    height: calc(100% - 160px);

    .example-title {
      margin: 0 0 8px;
      font-size: 16px;
      font-weight: 600;
      color: @heading-color;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .example-description {
      margin: 0 0 12px;
      font-size: 14px;
      color: @text-color-secondary;
      line-height: 1.5;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      flex: 1;
    }

    .example-meta {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 12px;

      .example-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
      }

      .example-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: @text-color-secondary;

        .example-popularity {
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .example-date {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }

    .example-actions {
      display: flex;
      justify-content: flex-end;
      gap: 16px;

      .example-action-icon {
        font-size: 16px;
        color: @text-color-secondary;
        cursor: pointer;
        transition: color 0.3s;

        &:hover {
          color: @primary-color;
        }
      }
    }
  }
}
