.threshold-settings {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .threshold-toolbar {
    padding: 12px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .threshold-content {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
  }
  
  .preset-card {
    margin-bottom: 16px;
  }
  
  .ant-collapse {
    background-color: #fff;
    
    .ant-collapse-item {
      .ant-collapse-header {
        font-weight: 500;
      }
    }
  }
  
  .ant-form-item-label {
    label {
      height: auto;
      margin-bottom: 4px;
    }
  }
}

// 暗色主题样式
.dark-theme {
  .threshold-settings {
    .threshold-toolbar {
      background-color: #1e1e1e;
      border-bottom: 1px solid #303030;
    }
    
    .ant-collapse {
      background-color: #1e1e1e;
    }
  }
}
