/**
 * AI解析器状态管理
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AIResolution, AIResolverStatus, AIModelType } from '../../services/AIConflictResolverService';

// 状态接口
interface AIResolverState {
  enabled: boolean;
  status: AIResolverStatus;
  resolutions: AIResolution[];
  selectedResolutionId: string | null;
  defaultModel: AIModelType;
  autoApply: boolean;
  autoApplyThreshold: number;
  confidenceThreshold: number;
}

// 初始状态
const initialState: AIResolverState = {
  enabled: false,
  status: AIResolverStatus.IDLE,
  resolutions: [],
  selectedResolutionId: null,
  defaultModel: AIModelType.ADVANCED,
  autoApply: false,
  autoApplyThreshold: 0.9,
  confidenceThreshold: 0.7
};

// 创建Slice
export const aiResolverSlice = createSlice({
  name: 'aiResolver',
  initialState,
  reducers: {
    // 设置是否启用
    setAIResolverEnabled: (state, action: PayloadAction<boolean>) => {
      state.enabled = action.payload;
    },
    
    // 设置状态
    setAIResolverStatus: (state, action: PayloadAction<AIResolverStatus>) => {
      state.status = action.payload;
    },
    
    // 添加解决方案
    addAIResolution: (state, action: PayloadAction<AIResolution>) => {
      // 检查是否已存在相同ID的解决方案
      const existingIndex = state.resolutions.findIndex(r => r.id === action.payload.id);
      
      if (existingIndex >= 0) {
        // 更新现有解决方案
        state.resolutions[existingIndex] = action.payload;
      } else {
        // 添加新解决方案
        state.resolutions.push(action.payload);
      }
    },
    
    // 更新解决方案
    updateAIResolution: (state, action: PayloadAction<AIResolution>) => {
      const index = state.resolutions.findIndex(r => r.id === action.payload.id);
      
      if (index >= 0) {
        state.resolutions[index] = action.payload;
      }
    },
    
    // 移除解决方案
    removeAIResolution: (state, action: PayloadAction<string>) => {
      state.resolutions = state.resolutions.filter(r => r.id !== action.payload);
      
      // 如果移除的是当前选中的解决方案，清除选中状态
      if (state.selectedResolutionId === action.payload) {
        state.selectedResolutionId = null;
      }
    },
    
    // 清除所有解决方案
    clearAIResolutions: (state) => {
      state.resolutions = [];
      state.selectedResolutionId = null;
    },
    
    // 清除已应用的解决方案
    clearAppliedAIResolutions: (state) => {
      state.resolutions = state.resolutions.filter(r => !r.applied);
      
      // 如果清除了当前选中的解决方案，清除选中状态
      if (state.selectedResolutionId && 
          !state.resolutions.some(r => r.id === state.selectedResolutionId)) {
        state.selectedResolutionId = null;
      }
    },
    
    // 设置选中的解决方案
    setSelectedAIResolutionId: (state, action: PayloadAction<string | null>) => {
      state.selectedResolutionId = action.payload;
    },
    
    // 设置默认模型
    setDefaultModel: (state, action: PayloadAction<AIModelType>) => {
      state.defaultModel = action.payload;
    },
    
    // 设置自动应用
    setAutoApply: (state, action: PayloadAction<boolean>) => {
      state.autoApply = action.payload;
    },
    
    // 设置自动应用阈值
    setAutoApplyThreshold: (state, action: PayloadAction<number>) => {
      state.autoApplyThreshold = Math.max(0, Math.min(1, action.payload));
    },
    
    // 设置置信度阈值
    setConfidenceThreshold: (state, action: PayloadAction<number>) => {
      state.confidenceThreshold = Math.max(0, Math.min(1, action.payload));
    }
  }
});

// 导出Actions
export const {
  setAIResolverEnabled,
  setAIResolverStatus,
  addAIResolution,
  updateAIResolution,
  removeAIResolution,
  clearAIResolutions,
  clearAppliedAIResolutions,
  setSelectedAIResolutionId,
  setDefaultModel,
  setAutoApply,
  setAutoApplyThreshold,
  setConfidenceThreshold
} = aiResolverSlice.actions;

// 导出Selectors
export const selectAIResolverEnabled = (state: { aiResolver: AIResolverState }) => 
  state.aiResolver.enabled;

export const selectAIResolverStatus = (state: { aiResolver: AIResolverState }) => 
  state.aiResolver.status;

export const selectAIResolutions = (state: { aiResolver: AIResolverState }) => 
  state.aiResolver.resolutions;

export const selectAppliedAIResolutions = (state: { aiResolver: AIResolverState }) => 
  state.aiResolver.resolutions.filter(r => r.applied);

export const selectPendingAIResolutions = (state: { aiResolver: AIResolverState }) => 
  state.aiResolver.resolutions.filter(r => !r.applied);

export const selectSelectedAIResolutionId = (state: { aiResolver: AIResolverState }) => 
  state.aiResolver.selectedResolutionId;

export const selectSelectedAIResolution = (state: { aiResolver: AIResolverState }) => 
  state.aiResolver.resolutions.find(r => r.id === state.aiResolver.selectedResolutionId);

export const selectDefaultModel = (state: { aiResolver: AIResolverState }) => 
  state.aiResolver.defaultModel;

export const selectAutoApply = (state: { aiResolver: AIResolverState }) => 
  state.aiResolver.autoApply;

export const selectAutoApplyThreshold = (state: { aiResolver: AIResolverState }) => 
  state.aiResolver.autoApplyThreshold;

export const selectConfidenceThreshold = (state: { aiResolver: AIResolverState }) => 
  state.aiResolver.confidenceThreshold;

// 导出Reducer
export default aiResolverSlice.reducer;
