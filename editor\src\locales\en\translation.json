{"common": {"ok": "OK", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "create": "Create", "add": "Add", "remove": "Remove", "close": "Close", "apply": "Apply", "reset": "Reset", "loading": "Loading...", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "more": "More", "less": "Less", "expand": "Expand", "collapse": "Collapse", "back": "Back", "next": "Next", "previous": "Previous", "finish": "Finish", "yes": "Yes", "no": "No", "on": "On", "off": "Off", "enabled": "Enabled", "disabled": "Disabled", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "help": "Help", "settings": "Settings", "preferences": "Preferences", "properties": "Properties", "details": "Details", "preview": "Preview", "view": "View", "download": "Download", "upload": "Upload", "import": "Import", "export": "Export", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "select": "Select", "selectAll": "Select All", "deselectAll": "Deselect All", "name": "Name", "description": "Description", "type": "Type", "size": "Size", "date": "Date", "time": "Time", "duration": "Duration", "status": "Status", "category": "Category", "tag": "Tag", "tags": "Tags", "color": "Color", "opacity": "Opacity", "visibility": "Visibility", "visible": "Visible", "hidden": "Hidden", "position": "Position", "rotation": "Rotation", "scale": "Scale", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "depth": "De<PERSON><PERSON>", "x": "X", "y": "Y", "z": "Z", "min": "Min", "max": "Max", "value": "Value", "default": "<PERSON><PERSON><PERSON>", "custom": "Custom", "auto": "Auto", "manual": "Manual", "none": "None", "all": "All", "other": "Other", "unknown": "Unknown", "untitled": "Untitled", "new": "New", "open": "Open", "file": "File", "folder": "Folder", "project": "Project", "scene": "Scene", "asset": "<PERSON><PERSON>", "assets": "Assets", "material": "Material", "texture": "Texture", "model": "Model", "animation": "Animation", "sound": "Sound", "script": "<PERSON><PERSON><PERSON>", "camera": "Camera", "light": "Light", "object": "Object", "component": "Component", "entity": "Entity", "system": "System", "physics": "Physics", "rendering": "Rendering", "input": "Input", "output": "Output", "network": "Network", "ui": "UI", "debug": "Debug", "performance": "Performance", "optimization": "Optimization", "tools": "Tools", "examples": "Examples", "tutorials": "Tutorials", "documentation": "Documentation", "about": "About", "version": "Version", "language": "Language", "theme": "Theme", "lightTheme": "Light Theme", "darkTheme": "Dark Theme", "autoTheme": "Auto Theme", "layout": "Layout", "resetLayout": "Reset Layout", "saveLayout": "Save Layout", "loadLayout": "Load Layout", "fullscreen": "Fullscreen", "exitFullscreen": "Exit Fullscreen", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "resetZoom": "Reset Zoom", "fitToScreen": "Fit to Screen", "grid": "Grid", "showGrid": "Show Grid", "hideGrid": "Hide Grid", "gridSize": "<PERSON><PERSON>", "gridSnap": "<PERSON><PERSON>", "axes": "Axes", "showAxes": "Show Axes", "hideAxes": "Hide Axes", "gizmo": "<PERSON><PERSON><PERSON>", "showGizmo": "Show Gizmo", "hideGizmo": "<PERSON><PERSON> Giz<PERSON>", "transform": "Transform", "translate": "Translate", "rotate": "Rotate", "local": "Local", "global": "Global", "play": "Play", "pause": "Pause", "stop": "Stop", "restart": "<PERSON><PERSON>", "speed": "Speed", "loop": "Loop", "frame": "<PERSON>ame", "fps": "FPS", "quality": "Quality", "low": "Low", "medium": "Medium", "high": "High", "ultra": "Ultra"}, "editor": {"title": "IR Engine Editor", "welcome": "Welcome to IR Engine Editor", "newProject": "New Project", "openProject": "Open Project", "saveProject": "Save Project", "saveProjectAs": "Save Project As", "closeProject": "Close Project", "importAsset": "Import Asset", "exportScene": "Export Scene", "exportProject": "Export Project", "publish": "Publish", "preview": "Preview", "build": "Build", "deploy": "Deploy", "settings": "Settings", "preferences": "Preferences", "shortcuts": "Shortcuts", "about": "About", "exit": "Exit", "panels": {"hierarchy": "Hierarchy", "inspector": "Inspector", "scene": "Scene", "assets": "Assets", "console": "<PERSON><PERSON><PERSON>", "animation": "Animation", "physics": "Physics", "network": "Network", "debug": "Debug", "performance": "Performance", "collaboration": "Collaboration", "userTesting": "User Testing", "resourceHotUpdate": "Resource Hot Update", "environment": "Environment"}, "mobile": {"loading": "Loading mobile device adaptation...", "warning": "Mobile Device Warning", "understand": "I Understand", "performanceWarning": "You are using a mobile device, which may experience performance issues.", "performanceWarningDetail": "The editor may run slower on mobile devices. For a better experience, consider using a desktop device.", "networkWarning": "Unstable network connection detected.", "networkWarningDetail": "Unstable network connection may affect collaborative editing and asset loading.", "grid": "Grid", "axes": "Axes", "camera": "Camera", "transform": "Transform", "tools": "Tools", "view": "View", "help": "Help"}}, "panel": {"toggleSize": "Toggle Size", "expand": "Expand", "collapse": "Collapse", "close": "Close", "maximize": "Maximize", "restore": "Rest<PERSON>", "settings": "Settings"}, "gyroscope": {"enable": "Enable Gyroscope", "disable": "Disable Gyroscope", "calibrate": "Calibrate Gyroscope", "settings": "Gyroscope Settings", "sensitivity": "Sensitivity", "smoothing": "Smoothing", "enabled": "Gyroscope Enabled", "disabled": "Gyroscope Disabled", "calibrating": "Calibrating Gyroscope...", "calibrationComplete": "Gyroscope Calibration Complete", "unavailable": "Gyroscope Unavailable", "requestPermission": "Request Permission", "permissionGranted": "Gyroscope Permission Granted", "permissionDenied": "Gyroscope Permission Denied", "permissionError": "Error Requesting Gyroscope Permission", "enabledSuccess": "Gyroscope Control Enabled"}, "responsive": {"deviceDetected": "{{device}} Device Detected", "orientationChanged": "Screen Orientation Changed to {{orientation}}", "layoutOptimized": "Layout Optimized", "touchMode": "Touch Mode", "desktopMode": "Desktop Mode", "portrait": "Portrait", "landscape": "Landscape", "mobile": "Mobile", "tablet": "Tablet", "desktop": "Desktop", "smallScreen": "Small Screen", "mediumScreen": "Medium Screen", "largeScreen": "Large Screen", "extraLargeScreen": "Extra Large Screen"}}