"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Component = void 0;
var EventEmitter_1 = require("../utils/EventEmitter");
var Component = /** @class */ (function (_super) {
    __extends(Component, _super);
    /**
     * 创建组件实例
     * @param type 组件类型
     */
    function Component(type) {
        var _this = _super.call(this) || this;
        /** 实体引用 */
        _this.entity = null;
        /** 是否启用 */
        _this.enabled = true;
        _this.type = type;
        return _this;
    }
    /**
     * 获取组件类型
     * @returns 组件类型
     */
    Component.prototype.getType = function () {
        return this.type;
    };
    /**
     * 设置实体引用
     * @param entity 实体实例
     */
    Component.prototype.setEntity = function (entity) {
        this.entity = entity;
        this.onAttach();
    };
    /**
     * 获取实体引用
     * @returns 实体实例
     */
    Component.prototype.getEntity = function () {
        return this.entity;
    };
    /**
     * 设置启用状态
     * @param enabled 是否启用
     */
    Component.prototype.setEnabled = function (enabled) {
        if (this.enabled === enabled) {
            return;
        }
        this.enabled = enabled;
        if (enabled) {
            this.onEnable();
        }
        else {
            this.onDisable();
        }
        // 发出启用状态变更事件
        this.emit('enabledChanged', enabled);
    };
    /**
     * 是否启用
     * @returns 是否启用
     */
    Component.prototype.isEnabled = function () {
        return this.enabled;
    };
    /**
     * 当组件附加到实体时调用
     */
    Component.prototype.onAttach = function () {
        // 子类可以重写此方法
    };
    /**
     * 当组件启用时调用
     */
    Component.prototype.onEnable = function () {
        // 子类可以重写此方法
    };
    /**
     * 当组件禁用时调用
     */
    Component.prototype.onDisable = function () {
        // 子类可以重写此方法
    };
    /**
     * 更新组件
     * @param deltaTime 帧间隔时间（秒）
     */
    Component.prototype.update = function (deltaTime) {
        // 子类可以重写此方法
    };
    /**
     * 固定时间步长更新
     * @param fixedDeltaTime 固定帧间隔时间（秒）
     */
    Component.prototype.fixedUpdate = function (fixedDeltaTime) {
        // 子类可以重写此方法
    };
    /**
     * 销毁组件
     */
    Component.prototype.dispose = function () {
        // 清除实体引用
        this.entity = null;
        // 移除所有事件监听器
        this.removeAllListeners();
    };
    return Component;
}(EventEmitter_1.EventEmitter));
exports.Component = Component;
