.entity-component-compare-panel {
  margin: 16px;
  
  .compare-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    
    .comparison-summary {
      margin: 16px 0;
      display: flex;
      justify-content: center;
    }
    
    .table-view {
      .table-header {
        margin-bottom: 16px;
        display: flex;
        justify-content: flex-end;
      }
      
      .component-changes {
        padding: 16px;
        background-color: #f5f5f5;
        border-radius: 4px;
        
        .change-section {
          margin-bottom: 16px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    
    .json-view {
      .json-container {
        display: flex;
        
        .json-column {
          flex: 1;
          padding: 8px;
          overflow: auto;
          max-height: 600px;
        }
        
        .json-divider {
          height: auto;
        }
      }
    }
    
    .diff-view {
      padding: 16px;
      background-color: #f5f5f5;
      border-radius: 4px;
      max-height: 600px;
      overflow: auto;
    }
  }
}

// 差异高亮样式
.different-value {
  background-color: rgba(255, 192, 105, 0.2);
  padding: 2px 4px;
  border-radius: 2px;
}

.added-entity {
  color: #52c41a;
}

.removed-entity {
  color: #f5222d;
}

.modified-entity {
  color: #1890ff;
}
