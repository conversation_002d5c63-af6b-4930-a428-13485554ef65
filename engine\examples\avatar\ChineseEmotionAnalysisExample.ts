/**
 * 中文情感分析示例
 * 演示如何使用中文情感分析模型生成面部动画
 */
import * as THREE from 'three';
import { Engine } from '../../core/Engine';
import { World } from '../../core/World';
import { Entity } from '../../core/Entity';
import { 
  FacialAnimationSystem, 
  FacialAnimationComponent, 
  FacialExpressionType, 
  VisemeType 
} from '../../animation/FacialAnimation';
import { 
  FacialAnimationModelAdapterSystem, 
  FacialAnimationModelType 
} from '../../animation/adapters/FacialAnimationModelAdapterSystem';
import { AIAnimationSynthesisSystem } from '../../animation/AIAnimationSynthesisSystem';
import { EmotionModelFactory, EmotionModelType, EmotionModelVariant } from '../../avatar/ai/EmotionModelFactory';
import { ChineseBERTEmotionModel } from '../../avatar/ai/ChineseBERTEmotionModel';
import { GLTFLoader } from '../../loaders/GLTFLoader';
import { OrbitControls } from '../../utils/OrbitControls';

/**
 * 中文情感分析示例
 */
export class ChineseEmotionAnalysisExample {
  /** 引擎 */
  private engine: Engine;
  
  /** 世界 */
  private world: World;
  
  /** 场景 */
  private scene: THREE.Scene;
  
  /** 相机 */
  private camera: THREE.PerspectiveCamera;
  
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer;
  
  /** 控制器 */
  private controls: OrbitControls;
  
  /** 角色实体 */
  private characterEntity: Entity;
  
  /** 面部动画系统 */
  private facialAnimationSystem: FacialAnimationSystem;
  
  /** 面部动画模型适配器系统 */
  private modelAdapterSystem: FacialAnimationModelAdapterSystem;
  
  /** AI动画合成系统 */
  private aiAnimationSystem: AIAnimationSynthesisSystem;
  
  /** 情感模型工厂 */
  private emotionModelFactory: EmotionModelFactory;
  
  /** 中文情感模型 */
  private chineseEmotionModel: ChineseBERTEmotionModel;
  
  /** 情感输入元素 */
  private emotionInput: HTMLInputElement | null = null;
  
  /** 生成按钮 */
  private generateButton: HTMLButtonElement | null = null;
  
  /** 语言选择器 */
  private languageSelect: HTMLSelectElement | null = null;
  
  /** 模型选择器 */
  private modelSelect: HTMLSelectElement | null = null;
  
  /** 当前语言 */
  private currentLanguage: string = 'zh-CN';
  
  /** 当前模型 */
  private currentModel: string = 'chinese-bert';
  
  /**
   * 构造函数
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 获取世界
    this.world = this.engine.getWorld();
    
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x333333);
    
    // 创建相机
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.camera.position.set(0, 1.6, 2);
    
    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    document.body.appendChild(this.renderer.domElement);
    
    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 1.6, 0);
    this.controls.update();
    
    // 创建角色实体
    this.characterEntity = this.world.createEntity();
    
    // 创建面部动画系统
    this.facialAnimationSystem = new FacialAnimationSystem(this.world, {
      debug: true,
      autoDetectAudio: true
    });
    
    // 创建面部动画模型适配器系统
    this.modelAdapterSystem = new FacialAnimationModelAdapterSystem(this.world, {
      debug: true,
      autoDetectBlendShapes: true
    });
    
    // 创建AI动画合成系统
    this.aiAnimationSystem = new AIAnimationSynthesisSystem(this.world, {
      debug: true,
      useLocalModel: true
    });
    
    // 创建情感模型工厂
    this.emotionModelFactory = new EmotionModelFactory({
      debug: true,
      useCache: true,
      useGPU: false,
      useRemoteAPI: false
    });
    
    // 创建中文情感模型
    this.chineseEmotionModel = this.emotionModelFactory.createChineseBERTModel();
    
    // 添加系统到世界
    this.world.addSystem(this.facialAnimationSystem);
    this.world.addSystem(this.modelAdapterSystem);
    this.world.addSystem(this.aiAnimationSystem);
    
    // 创建灯光
    this.createLights();
    
    // 创建UI
    this.createUI();
    
    // 加载模型
    this.loadModel();
    
    // 开始渲染循环
    this.animate();
    
    // 处理窗口大小变化
    window.addEventListener('resize', this.onWindowResize.bind(this));
  }
  
  /**
   * 创建灯光
   */
  private createLights(): void {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(ambientLight);
    
    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 2, 3);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);
  }
  
  /**
   * 创建UI
   */
  private createUI(): void {
    // 创建UI容器
    const uiContainer = document.createElement('div');
    uiContainer.style.position = 'absolute';
    uiContainer.style.top = '10px';
    uiContainer.style.left = '10px';
    uiContainer.style.padding = '10px';
    uiContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    uiContainer.style.borderRadius = '5px';
    uiContainer.style.color = 'white';
    document.body.appendChild(uiContainer);
    
    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '中文情感分析示例';
    uiContainer.appendChild(title);
    
    // 创建语言选择器
    const languageLabel = document.createElement('label');
    languageLabel.textContent = '语言: ';
    uiContainer.appendChild(languageLabel);
    
    this.languageSelect = document.createElement('select');
    this.languageSelect.style.marginBottom = '10px';
    this.languageSelect.style.width = '200px';
    
    const zhOption = document.createElement('option');
    zhOption.value = 'zh-CN';
    zhOption.textContent = '中文';
    zhOption.selected = true;
    this.languageSelect.appendChild(zhOption);
    
    const enOption = document.createElement('option');
    enOption.value = 'en-US';
    enOption.textContent = 'English';
    this.languageSelect.appendChild(enOption);
    
    this.languageSelect.addEventListener('change', this.onLanguageChange.bind(this));
    uiContainer.appendChild(this.languageSelect);
    uiContainer.appendChild(document.createElement('br'));
    
    // 创建模型选择器
    const modelLabel = document.createElement('label');
    modelLabel.textContent = '模型: ';
    uiContainer.appendChild(modelLabel);
    
    this.modelSelect = document.createElement('select');
    this.modelSelect.style.marginBottom = '10px';
    this.modelSelect.style.width = '200px';
    
    const chineseBertOption = document.createElement('option');
    chineseBertOption.value = 'chinese-bert';
    chineseBertOption.textContent = '中文BERT';
    chineseBertOption.selected = true;
    this.modelSelect.appendChild(chineseBertOption);
    
    const multilingualOption = document.createElement('option');
    multilingualOption.value = 'multilingual';
    multilingualOption.textContent = '多语言BERT';
    this.modelSelect.appendChild(multilingualOption);
    
    const bertOption = document.createElement('option');
    bertOption.value = 'bert';
    bertOption.textContent = '标准BERT';
    this.modelSelect.appendChild(bertOption);
    
    this.modelSelect.addEventListener('change', this.onModelChange.bind(this));
    uiContainer.appendChild(this.modelSelect);
    uiContainer.appendChild(document.createElement('br'));
    
    // 创建情感输入
    const emotionLabel = document.createElement('label');
    emotionLabel.textContent = '情感描述: ';
    uiContainer.appendChild(emotionLabel);
    uiContainer.appendChild(document.createElement('br'));
    
    this.emotionInput = document.createElement('input');
    this.emotionInput.type = 'text';
    this.emotionInput.value = '角色感到非常开心';
    this.emotionInput.style.width = '300px';
    this.emotionInput.style.marginBottom = '10px';
    uiContainer.appendChild(this.emotionInput);
    uiContainer.appendChild(document.createElement('br'));
    
    // 创建生成按钮
    this.generateButton = document.createElement('button');
    this.generateButton.textContent = '生成面部动画';
    this.generateButton.disabled = true;
    this.generateButton.addEventListener('click', this.generateAnimation.bind(this));
    uiContainer.appendChild(this.generateButton);
  }
  
  /**
   * 语言变化处理
   */
  private onLanguageChange(event: Event): void {
    const select = event.target as HTMLSelectElement;
    this.currentLanguage = select.value;
    
    // 根据语言更新UI
    if (this.currentLanguage === 'zh-CN') {
      if (this.emotionInput) {
        this.emotionInput.value = '角色感到非常开心';
      }
      if (this.generateButton) {
        this.generateButton.textContent = '生成面部动画';
      }
    } else {
      if (this.emotionInput) {
        this.emotionInput.value = 'The character feels very happy';
      }
      if (this.generateButton) {
        this.generateButton.textContent = 'Generate Facial Animation';
      }
    }
  }
  
  /**
   * 模型变化处理
   */
  private onModelChange(event: Event): void {
    const select = event.target as HTMLSelectElement;
    this.currentModel = select.value;
    
    // 根据选择的模型更新
    switch (this.currentModel) {
      case 'chinese-bert':
        this.chineseEmotionModel = this.emotionModelFactory.createChineseBERTModel();
        break;
      case 'multilingual':
        this.chineseEmotionModel = this.emotionModelFactory.createMultilingualModel();
        break;
      case 'bert':
        this.chineseEmotionModel = this.emotionModelFactory.createModel(EmotionModelType.BERT);
        break;
    }
  }
  
  /**
   * 加载模型
   */
  private loadModel(): void {
    const loader = new GLTFLoader();
    loader.load('models/avatar/head.glb', (gltf) => {
      // 添加模型到场景
      this.scene.add(gltf.scene);
      
      // 查找骨骼网格
      let skinnedMesh: THREE.SkinnedMesh | null = null;
      gltf.scene.traverse((object) => {
        if (object instanceof THREE.SkinnedMesh) {
          skinnedMesh = object;
        }
      });
      
      if (skinnedMesh) {
        // 创建面部动画组件
        const facialAnimation = this.facialAnimationSystem.createFacialAnimation(this.characterEntity);
        
        // 将面部动画组件与模型绑定
        this.facialAnimationSystem.linkToModel(this.characterEntity, skinnedMesh);
        
        // 创建AI动画合成组件
        this.aiAnimationSystem.createAIAnimationSynthesis(this.characterEntity);
        
        console.log('模型加载完成，已绑定面部动画组件');
        console.log('混合形状字典:', skinnedMesh.morphTargetDictionary);
        console.log('混合形状影响:', skinnedMesh.morphTargetInfluences);
        
        // 启用生成按钮
        if (this.generateButton) {
          this.generateButton.disabled = false;
        }
      } else {
        console.warn('未找到骨骼网格');
      }
    }, undefined, (error) => {
      console.error('加载模型失败:', error);
    });
  }
  
  /**
   * 生成动画
   */
  private generateAnimation(): void {
    // 获取情感描述
    const emotionText = this.emotionInput ? this.emotionInput.value : '角色感到非常开心';
    
    // 禁用生成按钮
    if (this.generateButton) {
      this.generateButton.disabled = true;
      this.generateButton.textContent = this.currentLanguage === 'zh-CN' ? '生成中...' : 'Generating...';
    }
    
    // 分析情感
    this.chineseEmotionModel.analyzeEmotion(emotionText)
      .then(result => {
        console.log('情感分析结果:', result);
        
        // 获取AI动画合成组件
        const aiComponent = this.aiAnimationSystem.getAIAnimationSynthesis(this.characterEntity);
        if (!aiComponent) {
          console.error('未找到AI动画合成组件');
          return;
        }
        
        // 生成面部动画
        const requestId = this.aiAnimationSystem.generateFacialAnimation(
          this.characterEntity,
          emotionText,
          5.0,
          {
            loop: true,
            style: '自然',
            intensity: 0.8
          }
        );
        
        console.log('生成面部动画请求ID:', requestId);
        
        // 启用生成按钮
        if (this.generateButton) {
          this.generateButton.disabled = false;
          this.generateButton.textContent = this.currentLanguage === 'zh-CN' ? '生成面部动画' : 'Generate Facial Animation';
        }
      })
      .catch(error => {
        console.error('情感分析失败:', error);
        
        // 启用生成按钮
        if (this.generateButton) {
          this.generateButton.disabled = false;
          this.generateButton.textContent = this.currentLanguage === 'zh-CN' ? '生成面部动画' : 'Generate Facial Animation';
        }
      });
  }
  
  /**
   * 窗口大小变化处理
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }
  
  /**
   * 动画循环
   */
  private animate(): void {
    requestAnimationFrame(this.animate.bind(this));
    
    // 更新控制器
    this.controls.update();
    
    // 更新世界
    this.world.update();
    
    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }
}
