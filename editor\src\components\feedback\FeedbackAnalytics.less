.feedback-analytics {
  width: 100%;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  .feedback-analytics-header {
    margin-bottom: 24px;

    h2 {
      margin-bottom: 16px;
    }

    .feedback-filters {
      margin-bottom: 16px;
    }
  }

  .feedback-analytics-content {
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 48px 0;

      p {
        margin-top: 16px;
        color: rgba(0, 0, 0, 0.45);
      }
    }

    .stat-cards {
      margin-bottom: 24px;
    }

    .charts {
      margin-bottom: 24px;
    }

    .ant-empty {
      margin: 32px 0;
    }
  }

  .ant-tabs-tab {
    padding: 12px 16px;
  }

  .ant-statistic-title {
    font-size: 14px;
  }

  .ant-statistic-content {
    font-size: 24px;
  }
}

// 暗色主题适配
.dark-theme {
  .feedback-analytics {
    background-color: #1f1f1f;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

    .loading-container {
      p {
        color: rgba(255, 255, 255, 0.45);
      }
    }
  }
}
