#!/usr/bin/env node

/**
 * 检查TypeScript编译错误的脚本
 */

const { spawn } = require('child_process');
const path = require('path');

function checkErrors() {
  return new Promise((resolve, reject) => {
    const tsc = spawn('npx', ['tsc', '--noEmit', '--pretty'], {
      cwd: __dirname,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let stdout = '';
    let stderr = '';

    tsc.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    tsc.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    tsc.on('close', (code) => {
      console.log('TypeScript编译检查完成');
      console.log('退出代码:', code);
      
      if (stdout) {
        console.log('\n=== 标准输出 ===');
        console.log(stdout);
      }
      
      if (stderr) {
        console.log('\n=== 错误输出 ===');
        console.log(stderr);
      }
      
      resolve({ code, stdout, stderr });
    });

    tsc.on('error', (error) => {
      console.error('启动TypeScript编译器失败:', error);
      reject(error);
    });
  });
}

async function main() {
  try {
    console.log('开始检查TypeScript编译错误...');
    await checkErrors();
  } catch (error) {
    console.error('检查失败:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
