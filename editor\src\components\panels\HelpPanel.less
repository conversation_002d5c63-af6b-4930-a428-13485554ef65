.help-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  
  .help-panel-header {
    padding: 12px;
    border-bottom: 1px solid #e8e8e8;
  }
  
  .help-panel-body {
    display: flex;
    flex-direction: row;
    height: 100%;
    overflow: hidden;
    
    .ant-tabs {
      width: 300px;
      border-right: 1px solid #e8e8e8;
      
      .ant-tabs-content {
        height: 100%;
        overflow: auto;
      }
    }
  }
  
  .help-panel-content-container {
    flex: 1;
    padding: 16px;
    overflow: auto;
  }
  
  .help-panel-content {
    max-width: 800px;
    margin: 0 auto;
    
    h1, h2, h3, h4, h5, h6 {
      margin-top: 24px;
      margin-bottom: 16px;
    }
    
    p {
      margin-bottom: 16px;
      line-height: 1.6;
    }
    
    ul, ol {
      margin-bottom: 16px;
      padding-left: 24px;
      
      li {
        margin-bottom: 8px;
      }
    }
    
    table {
      width: 100%;
      margin-bottom: 16px;
      border-collapse: collapse;
      
      th, td {
        padding: 8px 12px;
        border: 1px solid #e8e8e8;
      }
      
      th {
        background-color: #f5f5f5;
        font-weight: bold;
      }
      
      tr:nth-child(even) {
        background-color: #fafafa;
      }
    }
    
    code {
      padding: 2px 4px;
      background-color: #f5f5f5;
      border-radius: 3px;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    }
    
    pre {
      padding: 16px;
      margin-bottom: 16px;
      background-color: #f5f5f5;
      border-radius: 3px;
      overflow: auto;
      
      code {
        padding: 0;
        background-color: transparent;
      }
    }
    
    blockquote {
      padding: 0 16px;
      margin-bottom: 16px;
      border-left: 4px solid #e8e8e8;
      color: #666;
    }
    
    img {
      max-width: 100%;
      margin-bottom: 16px;
      border: 1px solid #e8e8e8;
      border-radius: 3px;
    }
  }
  
  .help-panel-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    
    .ant-spin {
      margin-bottom: 16px;
    }
  }
}
