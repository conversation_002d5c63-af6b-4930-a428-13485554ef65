/**
 * 情感模型工厂
 * 用于创建和管理不同类型的情感分析模型
 */
import { BERTEmotionModel } from './BERTEmotionModel';
import { RoBERTaEmotionModel } from './RoBERTaEmotionModel';
import { DistilBERTEmotionModel } from './DistilBERTEmotionModel';
import { ChineseBERTEmotionModel, ChineseDialectType } from './ChineseBERTEmotionModel';
import { MultilingualEmotionModel, SupportedLanguage } from './MultilingualEmotionModel';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 情感模型类型
 */
export enum EmotionModelType {
  BERT = 'bert',
  ROBERTA = 'roberta',
  DISTILBERT = 'distilbert',
  CHINESE_BERT = 'chinese-bert',
  MULTILINGUAL = 'multilingual'
}

/**
 * 情感模型变体
 */
export enum EmotionModelVariant {
  BASE = 'base',
  LARGE = 'large',
  DISTILLED = 'distilled',
  MULTILINGUAL = 'multilingual'
}

/**
 * 情感模型工厂配置
 */
export interface EmotionModelFactoryConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否使用缓存 */
  useCache?: boolean;
  /** 缓存大小 */
  cacheSize?: number;
  /** 是否使用GPU */
  useGPU?: boolean;
  /** 是否使用远程API */
  useRemoteAPI?: boolean;
  /** 远程API URL */
  remoteAPIUrl?: string;
  /** API密钥 */
  apiKey?: string;
  /** 模型路径前缀 */
  modelPathPrefix?: string;
}

/**
 * 情感模型工厂
 */
export class EmotionModelFactory extends EventEmitter {
  /** 配置 */
  private config: EmotionModelFactoryConfig;

  /** 模型缓存 */
  private modelCache: Map<string, any> = new Map();

  /** 是否启用调试 */
  private debug: boolean;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: EmotionModelFactoryConfig = {}) {
    super();

    this.config = {
      debug: false,
      useCache: true,
      cacheSize: 5,
      useGPU: false,
      useRemoteAPI: false,
      modelPathPrefix: 'models/',
      ...config
    };

    this.debug = this.config.debug || false;

    if (this.debug) {
      console.log('情感模型工厂创建', this.config);
    }
  }

  /**
   * 创建情感模型
   * @param type 模型类型
   * @param variant 模型变体
   * @param config 模型配置
   * @returns 情感模型
   */
  public createModel(
    type: EmotionModelType,
    variant: EmotionModelVariant = EmotionModelVariant.BASE,
    config: any = {}
  ): any {
    // 生成缓存键
    const cacheKey = `${type}:${variant}:${JSON.stringify(config)}`;

    // 检查缓存
    if (this.config.useCache && this.modelCache.has(cacheKey)) {
      if (this.debug) {
        console.log(`使用缓存的情感模型: ${type}:${variant}`);
      }
      return this.modelCache.get(cacheKey);
    }

    // 基础配置
    const baseConfig = {
      debug: this.config.debug,
      useGPU: this.config.useGPU,
      useRemoteAPI: this.config.useRemoteAPI,
      remoteAPIUrl: this.config.remoteAPIUrl,
      apiKey: this.config.apiKey,
      modelVariant: variant,
      ...config
    };

    // 创建模型
    let model;

    switch (type) {
      case EmotionModelType.BERT:
        model = new BERTEmotionModel({
          ...baseConfig,
          modelPath: `${this.config.modelPathPrefix}bert-emotion/${variant}`
        });
        break;

      case EmotionModelType.ROBERTA:
        model = new RoBERTaEmotionModel({
          ...baseConfig,
          modelPath: `${this.config.modelPathPrefix}roberta-emotion/${variant}`
        });
        break;

      case EmotionModelType.DISTILBERT:
        model = new DistilBERTEmotionModel({
          ...baseConfig,
          modelPath: `${this.config.modelPathPrefix}distilbert-emotion/${variant}`
        });
        break;

      case EmotionModelType.CHINESE_BERT:
        model = new ChineseBERTEmotionModel({
          ...baseConfig,
          modelPath: `${this.config.modelPathPrefix}chinese-bert-emotion/${variant}`,
          useChineseTokenizer: true,
          dialectType: config.dialectType || ChineseDialectType.MANDARIN,
          useDictionaryEnhancement: config.useDictionaryEnhancement !== undefined ? config.useDictionaryEnhancement : true,
          useContextAnalysis: config.useContextAnalysis !== undefined ? config.useContextAnalysis : false,
          contextWindowSize: config.contextWindowSize || 5
        });
        break;

      case EmotionModelType.MULTILINGUAL:
        model = new MultilingualEmotionModel({
          ...baseConfig,
          modelPath: `${this.config.modelPathPrefix}multilingual-emotion`,
          defaultLanguage: config.defaultLanguage || SupportedLanguage.AUTO,
          autoDetectLanguage: config.autoDetectLanguage !== undefined ? config.autoDetectLanguage : true,
          useContextAnalysis: config.useContextAnalysis !== undefined ? config.useContextAnalysis : false,
          contextWindowSize: config.contextWindowSize || 5,
          chineseDialectType: config.chineseDialectType || ChineseDialectType.MANDARIN
        });
        break;

      default:
        throw new Error(`不支持的情感模型类型: ${type}`);
    }

    // 添加到缓存
    if (this.config.useCache) {
      this.modelCache.set(cacheKey, model);

      // 限制缓存大小
      if (this.modelCache.size > (this.config.cacheSize || 5)) {
        const firstKey = this.modelCache.keys().next().value;
        this.modelCache.delete(firstKey);
      }
    }

    if (this.debug) {
      console.log(`创建情感模型: ${type}:${variant}`);
    }

    return model;
  }

  /**
   * 创建中文BERT情感模型
   * @param variant 模型变体
   * @param config 模型配置
   * @returns 中文BERT情感模型
   */
  public createChineseBERTModel(
    variant: EmotionModelVariant = EmotionModelVariant.BASE,
    config: any = {}
  ): ChineseBERTEmotionModel {
    return this.createModel(EmotionModelType.CHINESE_BERT, variant, config);
  }

  /**
   * 创建多语言情感模型
   * @param config 模型配置
   * @returns 多语言情感模型
   */
  public createMultilingualModel(config: any = {}): MultilingualEmotionModel {
    return this.createModel(EmotionModelType.MULTILINGUAL, EmotionModelVariant.MULTILINGUAL, config);
  }

  /**
   * 创建多语言BERT情感模型（旧版本，保留兼容性）
   * @param config 模型配置
   * @returns 多语言BERT情感模型
   * @deprecated 使用 createMultilingualModel 代替
   */
  public createMultilingualBERTModel(config: any = {}): BERTEmotionModel {
    return this.createModel(EmotionModelType.BERT, EmotionModelVariant.MULTILINGUAL, config);
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.modelCache.clear();

    if (this.debug) {
      console.log('情感模型缓存已清除');
    }
  }

  /**
   * 获取缓存大小
   * @returns 缓存大小
   */
  public getCacheSize(): number {
    return this.modelCache.size;
  }
}
