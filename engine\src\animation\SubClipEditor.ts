/**
 * 子片段编辑器
 * 用于可视化编辑子片段
 */
import * as THREE from 'three';
import { EventEmitter } from '../utils/EventEmitter';
import { SubClip } from './SubClip';
import { AnimationSubClip } from './AnimationSubClip';
import { SubClipEvent } from './SubClipEvent';
import { SubClipSequence } from './SubClipSequence';
import { SubClipTransition } from './SubClipTransition';
import { SubClipModifier } from './SubClipModifier';

/**
 * 子片段编辑器事件类型
 */
export enum SubClipEditorEventType {
  /** 选择改变 */
  SELECTION_CHANGED = 'selectionChanged',
  /** 时间改变 */
  TIME_CHANGED = 'timeChanged',
  /** 编辑操作 */
  EDIT_OPERATION = 'editOperation',
  /** 预览状态改变 */
  PREVIEW_STATE_CHANGED = 'previewStateChanged'
}

/**
 * 编辑操作类型
 */
export enum EditOperationType {
  /** 添加 */
  ADD = 'add',
  /** 移除 */
  REMOVE = 'remove',
  /** 修改 */
  MODIFY = 'modify',
  /** 复制 */
  DUPLICATE = 'duplicate',
  /** 分割 */
  SPLIT = 'split',
  /** 合并 */
  MERGE = 'merge'
}

/**
 * 子片段编辑器配置
 */
export interface SubClipEditorConfig {
  /** 编辑器名称 */
  name?: string;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 子片段编辑器
 */
export class SubClipEditor {
  /** 编辑器名称 */
  private name: string;
  /** 是否启用调试 */
  private debug: boolean;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 子片段列表 */
  private subClips: (SubClip | AnimationSubClip)[] = [];
  /** 事件列表 */
  private events: SubClipEvent[] = [];
  /** 序列列表 */
  private sequences: SubClipSequence[] = [];
  /** 过渡列表 */
  private transitions: SubClipTransition[] = [];
  /** 变形器列表 */
  private modifiers: SubClipModifier[] = [];
  /** 当前选中的子片段 */
  private selectedSubClip: SubClip | AnimationSubClip | null = null;
  /** 当前选中的事件 */
  private selectedEvent: SubClipEvent | null = null;
  /** 当前选中的序列 */
  private selectedSequence: SubClipSequence | null = null;
  /** 当前选中的过渡 */
  private selectedTransition: SubClipTransition | null = null;
  /** 当前选中的变形器 */
  private selectedModifier: SubClipModifier | null = null;
  /** 当前时间 */
  private currentTime: number = 0;
  /** 是否正在预览 */
  private isPreviewPlaying: boolean = false;
  /** 预览速度 */
  private previewSpeed: number = 1.0;
  /** 撤销栈 */
  private undoStack: any[] = [];
  /** 重做栈 */
  private redoStack: any[] = [];
  /** 最大撤销步数 */
  private maxUndoSteps: number = 20;

  /**
   * 创建子片段编辑器
   * @param config 配置
   */
  constructor(config: SubClipEditorConfig = {}) {
    this.name = config.name || 'subClipEditor';
    this.debug = config.debug !== undefined ? config.debug : false;
  }

  /**
   * 获取编辑器名称
   * @returns 编辑器名称
   */
  public getName(): string {
    return this.name;
  }

  /**
   * 设置编辑器名称
   * @param name 编辑器名称
   */
  public setName(name: string): void {
    this.name = name;
  }

  /**
   * 添加子片段
   * @param subClip 子片段
   */
  public addSubClip(subClip: SubClip | AnimationSubClip): void {
    this.subClips.push(subClip);
    this.saveState();
  }

  /**
   * 移除子片段
   * @param subClip 子片段
   */
  public removeSubClip(subClip: SubClip | AnimationSubClip): void {
    const index = this.subClips.indexOf(subClip);
    if (index !== -1) {
      this.subClips.splice(index, 1);
      this.saveState();
    }
  }

  /**
   * 获取子片段列表
   * @returns 子片段列表
   */
  public getSubClips(): (SubClip | AnimationSubClip)[] {
    return [...this.subClips];
  }

  /**
   * 添加事件
   * @param event 事件
   */
  public addEvent(event: SubClipEvent): void {
    this.events.push(event);
    this.saveState();
  }

  /**
   * 移除事件
   * @param event 事件
   */
  public removeEvent(event: SubClipEvent): void {
    const index = this.events.indexOf(event);
    if (index !== -1) {
      this.events.splice(index, 1);
      this.saveState();
    }
  }

  /**
   * 获取事件列表
   * @returns 事件列表
   */
  public getEvents(): SubClipEvent[] {
    return [...this.events];
  }

  /**
   * 添加序列
   * @param sequence 序列
   */
  public addSequence(sequence: SubClipSequence): void {
    this.sequences.push(sequence);
    this.saveState();
  }

  /**
   * 移除序列
   * @param sequence 序列
   */
  public removeSequence(sequence: SubClipSequence): void {
    const index = this.sequences.indexOf(sequence);
    if (index !== -1) {
      this.sequences.splice(index, 1);
      this.saveState();
    }
  }

  /**
   * 获取序列列表
   * @returns 序列列表
   */
  public getSequences(): SubClipSequence[] {
    return [...this.sequences];
  }

  /**
   * 添加过渡
   * @param transition 过渡
   */
  public addTransition(transition: SubClipTransition): void {
    this.transitions.push(transition);
    this.saveState();
  }

  /**
   * 移除过渡
   * @param transition 过渡
   */
  public removeTransition(transition: SubClipTransition): void {
    const index = this.transitions.indexOf(transition);
    if (index !== -1) {
      this.transitions.splice(index, 1);
      this.saveState();
    }
  }

  /**
   * 获取过渡列表
   * @returns 过渡列表
   */
  public getTransitions(): SubClipTransition[] {
    return [...this.transitions];
  }

  /**
   * 添加变形器
   * @param modifier 变形器
   */
  public addModifier(modifier: SubClipModifier): void {
    this.modifiers.push(modifier);
    this.saveState();
  }

  /**
   * 移除变形器
   * @param modifier 变形器
   */
  public removeModifier(modifier: SubClipModifier): void {
    const index = this.modifiers.indexOf(modifier);
    if (index !== -1) {
      this.modifiers.splice(index, 1);
      this.saveState();
    }
  }

  /**
   * 获取变形器列表
   * @returns 变形器列表
   */
  public getModifiers(): SubClipModifier[] {
    return [...this.modifiers];
  }

  /**
   * 选择子片段
   * @param subClip 子片段
   */
  public selectSubClip(subClip: SubClip | AnimationSubClip | null): void {
    this.selectedSubClip = subClip;
    this.eventEmitter.emit(SubClipEditorEventType.SELECTION_CHANGED, {
      type: 'subClip',
      selected: subClip
    });
  }

  /**
   * 获取当前选中的子片段
   * @returns 当前选中的子片段
   */
  public getSelectedSubClip(): SubClip | AnimationSubClip | null {
    return this.selectedSubClip;
  }

  /**
   * 选择事件
   * @param event 事件
   */
  public selectEvent(event: SubClipEvent | null): void {
    this.selectedEvent = event;
    this.eventEmitter.emit(SubClipEditorEventType.SELECTION_CHANGED, {
      type: 'event',
      selected: event
    });
  }

  /**
   * 获取当前选中的事件
   * @returns 当前选中的事件
   */
  public getSelectedEvent(): SubClipEvent | null {
    return this.selectedEvent;
  }

  /**
   * 选择序列
   * @param sequence 序列
   */
  public selectSequence(sequence: SubClipSequence | null): void {
    this.selectedSequence = sequence;
    this.eventEmitter.emit(SubClipEditorEventType.SELECTION_CHANGED, {
      type: 'sequence',
      selected: sequence
    });
  }

  /**
   * 获取当前选中的序列
   * @returns 当前选中的序列
   */
  public getSelectedSequence(): SubClipSequence | null {
    return this.selectedSequence;
  }

  /**
   * 选择过渡
   * @param transition 过渡
   */
  public selectTransition(transition: SubClipTransition | null): void {
    this.selectedTransition = transition;
    this.eventEmitter.emit(SubClipEditorEventType.SELECTION_CHANGED, {
      type: 'transition',
      selected: transition
    });
  }

  /**
   * 获取当前选中的过渡
   * @returns 当前选中的过渡
   */
  public getSelectedTransition(): SubClipTransition | null {
    return this.selectedTransition;
  }

  /**
   * 选择变形器
   * @param modifier 变形器
   */
  public selectModifier(modifier: SubClipModifier | null): void {
    this.selectedModifier = modifier;
    this.eventEmitter.emit(SubClipEditorEventType.SELECTION_CHANGED, {
      type: 'modifier',
      selected: modifier
    });
  }

  /**
   * 获取当前选中的变形器
   * @returns 当前选中的变形器
   */
  public getSelectedModifier(): SubClipModifier | null {
    return this.selectedModifier;
  }

  /**
   * 设置当前时间
   * @param time 时间
   */
  public setCurrentTime(time: number): void {
    this.currentTime = time;
    this.eventEmitter.emit(SubClipEditorEventType.TIME_CHANGED, {
      time
    });
  }

  /**
   * 获取当前时间
   * @returns 当前时间
   */
  public getCurrentTime(): number {
    return this.currentTime;
  }

  /**
   * 开始预览
   */
  public startPreview(): void {
    this.isPreviewPlaying = true;
    this.eventEmitter.emit(SubClipEditorEventType.PREVIEW_STATE_CHANGED, {
      isPlaying: true
    });
  }

  /**
   * 停止预览
   */
  public stopPreview(): void {
    this.isPreviewPlaying = false;
    this.eventEmitter.emit(SubClipEditorEventType.PREVIEW_STATE_CHANGED, {
      isPlaying: false
    });
  }

  /**
   * 是否正在预览
   * @returns 是否正在预览
   */
  public getIsPreviewPlaying(): boolean {
    return this.isPreviewPlaying;
  }

  /**
   * 设置预览速度
   * @param speed 速度
   */
  public setPreviewSpeed(speed: number): void {
    this.previewSpeed = speed;
  }

  /**
   * 获取预览速度
   * @returns 预览速度
   */
  public getPreviewSpeed(): number {
    return this.previewSpeed;
  }

  /**
   * 保存当前状态
   */
  private saveState(): void {
    // 创建当前状态的快照
    const state = {
      subClips: this.subClips.map(clip => ({
        name: clip.getName(),
        // 其他属性...
      })),
      events: this.events.map(event => ({
        name: event.getName(),
        // 其他属性...
      })),
      sequences: this.sequences.map(sequence => ({
        name: sequence.getName(),
        // 其他属性...
      })),
      transitions: this.transitions.map(transition => ({
        name: transition.getName(),
        // 其他属性...
      })),
      modifiers: this.modifiers.map(modifier => ({
        name: modifier.getName(),
        // 其他属性...
      }))
    };

    // 添加到撤销栈
    this.undoStack.push(state);

    // 清空重做栈
    this.redoStack = [];

    // 限制撤销栈大小
    if (this.undoStack.length > this.maxUndoSteps) {
      this.undoStack.shift();
    }
  }

  /**
   * 撤销
   */
  public undo(): void {
    if (this.undoStack.length === 0) return;

    // 保存当前状态到重做栈
    const currentState = {
      subClips: this.subClips.map(clip => ({
        name: clip.getName(),
        // 其他属性...
      })),
      events: this.events.map(event => ({
        name: event.getName(),
        // 其他属性...
      })),
      sequences: this.sequences.map(sequence => ({
        name: sequence.getName(),
        // 其他属性...
      })),
      transitions: this.transitions.map(transition => ({
        name: transition.getName(),
        // 其他属性...
      })),
      modifiers: this.modifiers.map(modifier => ({
        name: modifier.getName(),
        // 其他属性...
      }))
    };

    this.redoStack.push(currentState);

    // 恢复上一个状态
    const previousState = this.undoStack.pop();
    if (previousState) {
      // 恢复状态
      // 注意：这里只是示例，实际实现需要更复杂的逻辑
      // 这里应该根据保存的状态重新创建对象
    }
  }

  /**
   * 重做
   */
  public redo(): void {
    if (this.redoStack.length === 0) return;

    // 保存当前状态到撤销栈
    const currentState = {
      subClips: this.subClips.map(clip => ({
        name: clip.getName(),
        // 其他属性...
      })),
      events: this.events.map(event => ({
        name: event.getName(),
        // 其他属性...
      })),
      sequences: this.sequences.map(sequence => ({
        name: sequence.getName(),
        // 其他属性...
      })),
      transitions: this.transitions.map(transition => ({
        name: transition.getName(),
        // 其他属性...
      })),
      modifiers: this.modifiers.map(modifier => ({
        name: modifier.getName(),
        // 其他属性...
      }))
    };

    this.undoStack.push(currentState);

    // 恢复下一个状态
    const nextState = this.redoStack.pop();
    if (nextState) {
      // 恢复状态
      // 注意：这里只是示例，实际实现需要更复杂的逻辑
      // 这里应该根据保存的状态重新创建对象
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public addEventListener(event: SubClipEditorEventType, listener: (data: any) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public removeEventListener(event: SubClipEditorEventType, listener: (data: any) => void): void {
    this.eventEmitter.off(event, listener);
  }
}
