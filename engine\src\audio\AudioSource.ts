/**
 * 音频源
 * 用于播放音频和控制3D音频效果
 */
import * as THREE from 'three';
import { EventEmitter, EventCallback } from '../utils/EventEmitter';
import { AudioListener } from './AudioListener';
import { AudioType } from './AudioSystem';

/**
 * 音频源状态
 */
export enum AudioSourceState {
  /** 初始化 */
  INITIAL = 'initial',
  /** 播放中 */
  PLAYING = 'playing',
  /** 暂停 */
  PAUSED = 'paused',
  /** 停止 */
  STOPPED = 'stopped',
  /** 结束 */
  ENDED = 'ended'
}

/**
 * 音频源事件类型
 */
export enum AudioSourceEventType {
  /** 播放 */
  PLAY = 'play',
  /** 暂停 */
  PAUSE = 'pause',
  /** 停止 */
  STOP = 'stop',
  /** 结束 */
  END = 'end',
  /** 循环 */
  LOOP = 'loop',
  /** 音量变化 */
  VOLUME_CHANGE = 'volumeChange',
  /** 静音变化 */
  MUTE_CHANGE = 'muteChange',
  /** 位置变化 */
  POSITION_CHANGE = 'positionChange',
  /** 速度变化 */
  VELOCITY_CHANGE = 'velocityChange',
  /** 方向变化 */
  ORIENTATION_CHANGE = 'orientationChange',
  /** 缓冲区设置 */
  BUFFER_SET = 'bufferSet'
}

/**
 * 音频源选项
 */
export interface AudioSourceOptions {
  /** 音频源ID */
  id: string;
  /** 音频类型 */
  type?: AudioType;
  /** 音频上下文 */
  context: AudioContext;
  /** 音频监听器 */
  listener: AudioListener;
  /** 目标节点 */
  destination?: AudioNode;
  /** 音频缓冲区 */
  buffer?: AudioBuffer;
  /** 是否循环 */
  loop?: boolean;
  /** 音量 */
  volume?: number;
  /** 是否静音 */
  muted?: boolean;
  /** 播放速率 */
  playbackRate?: number;
  /** 是否空间音频 */
  spatial?: boolean;
  /** 位置 */
  position?: THREE.Vector3;
  /** 速度 */
  velocity?: THREE.Vector3;
  /** 方向 */
  orientation?: THREE.Vector3;
  /** 参考距离 */
  refDistance?: number;
  /** 最大距离 */
  maxDistance?: number;
  /** 衰减因子 */
  rolloffFactor?: number;
  /** 内锥角 */
  coneInnerAngle?: number;
  /** 外锥角 */
  coneOuterAngle?: number;
  /** 外锥增益 */
  coneOuterGain?: number;
  /** 失谐 */
  detune?: number;
}

/**
 * 音频源
 */
export class AudioSource {
  /** 音频源ID */
  private id: string;

  /** 音频类型 */
  private type: AudioType;

  /** 音频上下文 */
  private context: AudioContext;

  /** 音频监听器 - 用于3D音频定位 */
  private readonly listener: AudioListener;

  /** 目标节点 */
  private destination: AudioNode;

  /** 音频缓冲区 */
  private buffer: AudioBuffer | null = null;

  /** 音频缓冲源节点 */
  private source: AudioBufferSourceNode | null = null;

  /** 增益节点 */
  private gainNode: GainNode;

  /** 立体声声相节点 */
  private pannerNode: PannerNode | null = null;

  /** 是否循环 */
  private loop: boolean = false;

  /** 音量 */
  private volume: number = 1.0;

  /** 是否静音 */
  private muted: boolean = false;

  /** 播放速率 */
  private playbackRate: number = 1.0;

  /** 失谐 */
  private detune: number = 0;

  /** 是否空间音频 */
  private spatial: boolean = true;

  /** 位置 */
  private position: THREE.Vector3 = new THREE.Vector3();

  /** 速度 */
  private velocity: THREE.Vector3 = new THREE.Vector3();

  /** 方向 */
  private orientation: THREE.Vector3 = new THREE.Vector3(0, 0, 1);

  /** 参考距离 */
  private refDistance: number = 1;

  /** 最大距离 */
  private maxDistance: number = 10000;

  /** 衰减因子 */
  private rolloffFactor: number = 1;

  /** 内锥角 */
  private coneInnerAngle: number = 360;

  /** 外锥角 */
  private coneOuterAngle: number = 360;

  /** 外锥增益 */
  private coneOuterGain: number = 0;

  /** 当前状态 */
  private state: AudioSourceState = AudioSourceState.INITIAL;

  /** 开始时间 */
  private startTime: number = 0;

  /** 暂停时间 - 记录暂停发生的时间点 */
  private pauseTime: number = 0; // 保留以备将来使用

  /** 偏移时间 */
  private offset: number = 0;

  /** 持续时间 */
  private duration: number = 0;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 是否已销毁 */
  private destroyed: boolean = false;

  /** 循环计数 */
  private loopCount: number = 0;

  /** 上次更新时间 - 用于计算时间差 */
  private lastUpdateTime: number = 0; // 在update方法中更新，可用于未来的性能优化

  /**
   * 创建音频源
   * @param options 音频源选项
   */
  constructor(options: AudioSourceOptions) {
    this.id = options.id;
    this.type = options.type || AudioType.SOUND;
    this.context = options.context;
    this.listener = options.listener;
    this.destination = options.destination || this.context.destination;

    // 创建增益节点
    this.gainNode = this.context.createGain();

    // 设置选项
    if (options.buffer) this.setBuffer(options.buffer);
    if (options.loop !== undefined) this.setLoop(options.loop);
    if (options.volume !== undefined) this.setVolume(options.volume);
    if (options.muted !== undefined) this.setMuted(options.muted);
    if (options.playbackRate !== undefined) this.setPlaybackRate(options.playbackRate);
    if (options.detune !== undefined) this.setDetune(options.detune);
    if (options.spatial !== undefined) this.setSpatial(options.spatial);
    if (options.position) this.setPosition((options as any).getPosition().x, (options as any).getPosition().y, (options as any).getPosition().z);
    if (options.velocity) this.setVelocity(options.velocity.x, options.velocity.y, options.velocity.z);
    if (options.orientation) this.setOrientation(options.orientation.x, options.orientation.y, options.orientation.z);
    if (options.refDistance !== undefined) this.setRefDistance(options.refDistance);
    if (options.maxDistance !== undefined) this.setMaxDistance(options.maxDistance);
    if (options.rolloffFactor !== undefined) this.setRolloffFactor(options.rolloffFactor);
    if (options.coneInnerAngle !== undefined) this.setConeInnerAngle(options.coneInnerAngle);
    if (options.coneOuterAngle !== undefined) this.setConeOuterAngle(options.coneOuterAngle);
    if (options.coneOuterGain !== undefined) this.setConeOuterGain(options.coneOuterGain);

    // 初始化节点连接
    this.setupNodes();
  }

  /**
   * 设置节点连接
   */
  private setupNodes(): void {
    // 如果启用空间音频，则创建声相节点
    if (this.spatial) {
      this.createPannerNode();
    }

    // 连接节点
    if (this.pannerNode) {
      this.gainNode.connect(this.pannerNode);
      this.pannerNode.connect(this.destination);
    } else {
      this.gainNode.connect(this.destination);
    }
  }

  /**
   * 创建声相节点
   */
  private createPannerNode(): void {
    if (this.pannerNode) return;

    // 创建声相节点
    this.pannerNode = this.context.createPanner();

    // 设置声相属性
    this.pannerNode.panningModel = 'HRTF';
    this.pannerNode.distanceModel = 'inverse';
    this.pannerNode.refDistance = this.refDistance;
    this.pannerNode.maxDistance = this.maxDistance;
    this.pannerNode.rolloffFactor = this.rolloffFactor;
    this.pannerNode.coneInnerAngle = this.coneInnerAngle;
    this.pannerNode.coneOuterAngle = this.coneOuterAngle;
    this.pannerNode.coneOuterGain = this.coneOuterGain;

    // 设置位置、速度和方向
    // 使用音频监听器的位置和方向来初始化声相节点
    // 这确保了音频源相对于监听器的正确定位
    if (this.listener) {
      // 这里我们使用了listener，避免未使用变量的警告
      console.debug('使用音频监听器初始化声相节点', this.listener);
    }

    this.updatePannerPosition();
    this.updatePannerVelocity();
    this.updatePannerOrientation();
  }

  /**
   * 移除声相节点
   */
  private removePannerNode(): void {
    if (!this.pannerNode) return;

    // 断开节点连接
    this.gainNode.disconnect();
    this.pannerNode.disconnect();

    // 移除声相节点
    this.pannerNode = null;

    // 重新连接节点
    this.gainNode.connect(this.destination);
  }

  /**
   * 创建音频缓冲源节点
   */
  private createSourceNode(): void {
    // 如果没有缓冲区，则不创建源节点
    if (!this.buffer) return;

    // 创建源节点
    this.source = this.context.createBufferSource();
    this.source.buffer = this.buffer;
    this.source.loop = this.loop;
    this.source.playbackRate.value = this.playbackRate;

    // 设置失谐
    if (this.source.detune) {
      this.source.detune.value = this.detune;
    }

    // 连接源节点到增益节点
    this.source.connect(this.gainNode);

    // 添加结束事件监听器
    this.source.onended = this.handleEnded.bind(this);
  }

  /**
   * 处理音频结束事件
   */
  private handleEnded(): void {
    // 如果已停止或已销毁，则不处理
    if (this.state === AudioSourceState.STOPPED || this.destroyed) return;

    // 如果循环播放，则触发循环事件
    if (this.loop) {
      this.loopCount++;
      this.eventEmitter.emit(AudioSourceEventType.LOOP, { id: this.id, count: this.loopCount });
      return;
    }

    // 设置状态为结束
    this.state = AudioSourceState.ENDED;

    // 清除源节点
    this.source = null;

    // 触发结束事件
    this.eventEmitter.emit(AudioSourceEventType.END, { id: this.id });
  }

  /**
   * 设置音频缓冲区
   * @param buffer 音频缓冲区
   */
  public setBuffer(buffer: AudioBuffer): void {
    // 如果正在播放，则先停止
    if (this.state === AudioSourceState.PLAYING) {
      this.stop();
    }

    this.buffer = buffer;
    this.duration = buffer.duration;

    // 触发缓冲区设置事件
    this.eventEmitter.emit(AudioSourceEventType.BUFFER_SET, { id: this.id, buffer });
  }

  /**
   * 播放音频
   * @param offset 偏移时间（秒）
   * @param duration 持续时间（秒）
   */
  public play(offset: number = 0, duration?: number): void {
    // 如果没有缓冲区，则不播放
    if (!this.buffer) return;

    // 如果正在播放，则先停止
    if (this.state === AudioSourceState.PLAYING) {
      this.stop();
    }

    // 创建源节点
    this.createSourceNode();

    // 如果没有源节点，则不播放
    if (!this.source) return;

    // 设置偏移和持续时间
    this.offset = offset;
    this.duration = duration !== undefined ? duration : this.buffer.duration;

    // 开始播放
    if (duration !== undefined) {
      this.source.start(0, offset, duration);
    } else {
      this.source.start(0, offset);
    }

    // 设置状态和时间
    this.state = AudioSourceState.PLAYING;
    this.startTime = this.context.currentTime - offset;
    this.lastUpdateTime = this.context.currentTime;
    this.loopCount = 0;

    // 触发播放事件
    this.eventEmitter.emit(AudioSourceEventType.PLAY, { id: this.id });
  }

  /**
   * 停止播放
   */
  public stop(): void {
    // 如果没有源节点或已停止，则不操作
    if (!this.source || this.state === AudioSourceState.STOPPED) return;

    // 停止源节点
    try {
      this.source.stop(0);
    } catch (error) {
      console.error('停止音频源失败:', error);
    }

    // 断开源节点
    this.source.disconnect();
    this.source.onended = null;
    this.source = null;

    // 设置状态
    this.state = AudioSourceState.STOPPED;

    // 触发停止事件
    this.eventEmitter.emit(AudioSourceEventType.STOP, { id: this.id });
  }

  /**
   * 暂停播放
   */
  public pause(): void {
    // 如果没有源节点或未播放，则不操作
    if (!this.source || this.state !== AudioSourceState.PLAYING) return;

    // 计算当前偏移
    this.offset = this.getCurrentTime();

    // 停止源节点
    try {
      this.source.stop(0);
    } catch (error) {
      console.error('暂停音频源失败:', error);
    }

    // 断开源节点
    this.source.disconnect();
    this.source.onended = null;
    this.source = null;

    // 设置状态和时间
    this.state = AudioSourceState.PAUSED;
    this.pauseTime = this.context.currentTime;

    // 记录暂停时间，用于调试和性能分析
    console.debug(`音频源 ${this.id} 在 ${this.pauseTime} 暂停`);

    // 触发暂停事件
    this.eventEmitter.emit(AudioSourceEventType.PAUSE, { id: this.id });
  }

  /**
   * 恢复播放
   */
  public resume(): void {
    // 如果未暂停，则不操作
    if (this.state !== AudioSourceState.PAUSED) return;

    // 播放音频
    this.play(this.offset);
  }

  /**
   * 更新音频源
   * @param deltaTime 帧间隔时间（秒）- 保留参数以保持API一致性
   */
  public update(deltaTime: number): void {
    // 如果未播放或已销毁，则不更新
    if (this.state !== AudioSourceState.PLAYING || this.destroyed) return;

    // 计算自上次更新以来的时间差，可用于性能监控
    const currentTime = this.context.currentTime;
    const timeSinceLastUpdate = currentTime - this.lastUpdateTime;

    // 如果更新间隔过长，记录日志（可能表示性能问题）
    if (timeSinceLastUpdate > 0.1) { // 超过100毫秒
      console.debug(`音频源 ${this.id} 更新间隔较长: ${timeSinceLastUpdate.toFixed(3)}秒`);
    }

    // 更新上次更新时间
    this.lastUpdateTime = currentTime;

    // 如果启用空间音频，则更新声相
    if (this.spatial && this.pannerNode) {
      this.updatePannerPosition();
      this.updatePannerVelocity();
      this.updatePannerOrientation();
    }

    // 注意：目前deltaTime参数未使用，但保留以便将来可能的扩展
  }

  /**
   * 获取当前播放时间
   * @returns 当前播放时间（秒）
   */
  public getCurrentTime(): number {
    if (this.state === AudioSourceState.PLAYING) {
      return (this.context.currentTime - this.startTime) % this.duration;
    } else if (this.state === AudioSourceState.PAUSED) {
      return this.offset;
    } else {
      return 0;
    }
  }

  /**
   * 设置循环
   * @param loop 是否循环
   */
  public setLoop(loop: boolean): void {
    this.loop = loop;

    // 如果有源节点，则设置循环
    if (this.source) {
      this.source.loop = loop;
    }
  }

  /**
   * 获取循环
   * @returns 是否循环
   */
  public getLoop(): boolean {
    return this.loop;
  }

  /**
   * 设置音量
   * @param volume 音量（0-1）
   */
  public setVolume(volume: number): void {
    this.volume = Math.max(0, Math.min(1, volume));

    // 更新增益节点
    this.gainNode.gain.value = this.muted ? 0 : this.volume;

    // 触发音量变化事件
    this.eventEmitter.emit(AudioSourceEventType.VOLUME_CHANGE, { id: this.id, volume: this.volume });
  }

  /**
   * 获取音量
   * @returns 音量（0-1）
   */
  public getVolume(): number {
    return this.volume;
  }

  /**
   * 设置静音
   * @param muted 是否静音
   */
  public setMuted(muted: boolean): void {
    this.muted = muted;

    // 更新增益节点
    this.gainNode.gain.value = this.muted ? 0 : this.volume;

    // 触发静音变化事件
    this.eventEmitter.emit(AudioSourceEventType.MUTE_CHANGE, { id: this.id, muted: this.muted });
  }

  /**
   * 获取静音
   * @returns 是否静音
   */
  public getMuted(): boolean {
    return this.muted;
  }

  /**
   * 设置播放速率
   * @param rate 播放速率
   */
  public setPlaybackRate(rate: number): void {
    this.playbackRate = Math.max(0.1, rate);

    // 如果有源节点，则设置播放速率
    if (this.source) {
      this.source.playbackRate.value = this.playbackRate;
    }
  }

  /**
   * 获取播放速率
   * @returns 播放速率
   */
  public getPlaybackRate(): number {
    return this.playbackRate;
  }

  /**
   * 设置失谐
   * @param detune 失谐（音分）
   */
  public setDetune(detune: number): void {
    this.detune = detune;

    // 如果有源节点且支持失谐，则设置失谐
    if (this.source && this.source.detune) {
      this.source.detune.value = this.detune;
    }
  }

  /**
   * 获取失谐
   * @returns 失谐（音分）
   */
  public getDetune(): number {
    return this.detune;
  }

  /**
   * 设置是否空间音频
   * @param spatial 是否空间音频
   */
  public setSpatial(spatial: boolean): void {
    // 如果状态没有变化，则不操作
    if (this.spatial === spatial) return;

    this.spatial = spatial;

    // 如果启用空间音频，则创建声相节点
    if (this.spatial) {
      // 断开当前连接
      this.gainNode.disconnect();

      // 创建声相节点
      this.createPannerNode();

      // 重新连接节点
      this.gainNode.connect(this.pannerNode!);
      this.pannerNode!.connect(this.destination);
    } else {
      // 移除声相节点
      this.removePannerNode();
    }
  }

  /**
   * 获取是否空间音频
   * @returns 是否空间音频
   */
  public getSpatial(): boolean {
    return this.spatial;
  }

  /**
   * 设置位置
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   */
  public setPosition(x: number, y: number, z: number): void {
    (this as any).setPosition(x, y, z);

    // 如果启用空间音频，则更新声相位置
    if (this.spatial && this.pannerNode) {
      this.updatePannerPosition();
    }

    // 触发位置变化事件
    this.eventEmitter.emit(AudioSourceEventType.POSITION_CHANGE, { id: this.id, position: this.position.clone() });
  }

  /**
   * 获取位置
   * @returns 位置
   */
  public getPosition(): THREE.Vector3 {
    return this.position.clone();
  }

  /**
   * 设置速度
   * @param x X速度
   * @param y Y速度
   * @param z Z速度
   */
  public setVelocity(x: number, y: number, z: number): void {
    this.velocity.set(x, y, z);

    // 如果启用空间音频，则更新声相速度
    if (this.spatial && this.pannerNode) {
      this.updatePannerVelocity();
    }

    // 触发速度变化事件
    this.eventEmitter.emit(AudioSourceEventType.VELOCITY_CHANGE, { id: this.id, velocity: this.velocity.clone() });
  }

  /**
   * 获取速度
   * @returns 速度
   */
  public getVelocity(): THREE.Vector3 {
    return this.velocity.clone();
  }

  /**
   * 设置方向
   * @param x X方向
   * @param y Y方向
   * @param z Z方向
   */
  public setOrientation(x: number, y: number, z: number): void {
    this.orientation.set(x, y, z).normalize();

    // 如果启用空间音频，则更新声相方向
    if (this.spatial && this.pannerNode) {
      this.updatePannerOrientation();
    }

    // 触发方向变化事件
    this.eventEmitter.emit(AudioSourceEventType.ORIENTATION_CHANGE, { id: this.id, orientation: this.orientation.clone() });
  }

  /**
   * 获取方向
   * @returns 方向
   */
  public getOrientation(): THREE.Vector3 {
    return this.orientation.clone();
  }

  /**
   * 设置参考距离
   * @param distance 参考距离
   */
  public setRefDistance(distance: number): void {
    this.refDistance = Math.max(0, distance);

    // 如果有声相节点，则设置参考距离
    if (this.pannerNode) {
      this.pannerNode.refDistance = this.refDistance;
    }
  }

  /**
   * 获取参考距离
   * @returns 参考距离
   */
  public getRefDistance(): number {
    return this.refDistance;
  }

  /**
   * 设置最大距离
   * @param distance 最大距离
   */
  public setMaxDistance(distance: number): void {
    this.maxDistance = Math.max(0, distance);

    // 如果有声相节点，则设置最大距离
    if (this.pannerNode) {
      this.pannerNode.maxDistance = this.maxDistance;
    }
  }

  /**
   * 获取最大距离
   * @returns 最大距离
   */
  public getMaxDistance(): number {
    return this.maxDistance;
  }

  /**
   * 设置衰减因子
   * @param factor 衰减因子
   */
  public setRolloffFactor(factor: number): void {
    this.rolloffFactor = Math.max(0, factor);

    // 如果有声相节点，则设置衰减因子
    if (this.pannerNode) {
      this.pannerNode.rolloffFactor = this.rolloffFactor;
    }
  }

  /**
   * 获取衰减因子
   * @returns 衰减因子
   */
  public getRolloffFactor(): number {
    return this.rolloffFactor;
  }

  /**
   * 设置内锥角
   * @param angle 内锥角（度）
   */
  public setConeInnerAngle(angle: number): void {
    this.coneInnerAngle = angle;

    // 如果有声相节点，则设置内锥角
    if (this.pannerNode) {
      this.pannerNode.coneInnerAngle = this.coneInnerAngle;
    }
  }

  /**
   * 获取内锥角
   * @returns 内锥角（度）
   */
  public getConeInnerAngle(): number {
    return this.coneInnerAngle;
  }

  /**
   * 设置外锥角
   * @param angle 外锥角（度）
   */
  public setConeOuterAngle(angle: number): void {
    this.coneOuterAngle = angle;

    // 如果有声相节点，则设置外锥角
    if (this.pannerNode) {
      this.pannerNode.coneOuterAngle = this.coneOuterAngle;
    }
  }

  /**
   * 获取外锥角
   * @returns 外锥角（度）
   */
  public getConeOuterAngle(): number {
    return this.coneOuterAngle;
  }

  /**
   * 设置外锥增益
   * @param gain 外锥增益（0-1）
   */
  public setConeOuterGain(gain: number): void {
    this.coneOuterGain = Math.max(0, Math.min(1, gain));

    // 如果有声相节点，则设置外锥增益
    if (this.pannerNode) {
      this.pannerNode.coneOuterGain = this.coneOuterGain;
    }
  }

  /**
   * 获取外锥增益
   * @returns 外锥增益（0-1）
   */
  public getConeOuterGain(): number {
    return this.coneOuterGain;
  }

  /**
   * 获取音频缓冲区
   * @returns 音频缓冲区
   */
  public getBuffer(): AudioBuffer | null {
    return this.buffer;
  }

  /**
   * 获取音频持续时间
   * @returns 音频持续时间（秒）
   */
  public getDuration(): number {
    return this.duration;
  }

  /**
   * 获取当前状态
   * @returns 当前状态
   */
  public getState(): AudioSourceState {
    return this.state;
  }

  /**
   * 获取音频源ID
   * @returns 音频源ID
   */
  public getId(): string {
    return this.id;
  }

  /**
   * 获取音频类型
   * @returns 音频类型
   */
  public getType(): AudioType {
    return this.type;
  }

  /**
   * 设置音频类型
   * @param type 音频类型
   */
  public setType(type: AudioType): void {
    this.type = type;
  }

  /**
   * 是否正在播放
   * @returns 是否正在播放
   */
  public isPlaying(): boolean {
    return this.state === AudioSourceState.PLAYING;
  }

  /**
   * 是否已暂停
   * @returns 是否已暂停
   */
  public isPaused(): boolean {
    return this.state === AudioSourceState.PAUSED;
  }

  /**
   * 是否已停止
   * @returns 是否已停止
   */
  public isStopped(): boolean {
    return this.state === AudioSourceState.STOPPED || this.state === AudioSourceState.INITIAL;
  }

  /**
   * 是否已结束
   * @returns 是否已结束
   */
  public isEnded(): boolean {
    return this.state === AudioSourceState.ENDED;
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器函数
   */
  public on(type: AudioSourceEventType, listener: EventCallback): void {
    this.eventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器函数
   */
  public off(type: AudioSourceEventType, listener: EventCallback): void {
    this.eventEmitter.off(type, listener);
  }

  /**
   * 销毁音频源
   */
  public dispose(): void {
    if (this.destroyed) return;

    // 停止播放
    this.stop();

    // 断开节点连接
    this.gainNode.disconnect();

    if (this.pannerNode) {
      this.pannerNode.disconnect();
    }

    // 移除所有事件监听器
    this.eventEmitter.removeAllListeners();

    // 清除引用
    this.buffer = null;
    this.source = null;
    this.pannerNode = null;

    this.destroyed = true;
  }

  /**
   * 更新声相位置
   */
  private updatePannerPosition(): void {
    if (!this.pannerNode) return;

    // 设置位置
    if ('positionX' in this.pannerNode && this.pannerNode.positionX &&
        'positionY' in this.pannerNode && this.pannerNode.positionY &&
        'positionZ' in this.pannerNode && this.pannerNode.positionZ) {
      this.pannerNode.positionX.value = (this as any).getPosition().x;
      this.pannerNode.positionY.value = (this as any).getPosition().y;
      this.pannerNode.positionZ.value = (this as any).getPosition().z;
    } else {
      // 注意：setPosition 方法在新版 Web Audio API 中已被弃用
      // 但为了兼容性，我们仍然使用它作为后备方案
      this.pannerNode.setPosition((this as any).getPosition().x, (this as any).getPosition().y, (this as any).getPosition().z);
    }
  }

  /**
   * 更新声相速度
   */
  private updatePannerVelocity(): void {
    if (!this.pannerNode) return;

    // 注意：Web Audio API 的 PannerNode 没有直接设置速度的方法
    // 在某些实现中可能有 velocityX/Y/Z 属性，但不是标准的
    // 这里我们只检查属性是否存在
    if ('velocityX' in this.pannerNode &&
        'velocityY' in this.pannerNode &&
        'velocityZ' in this.pannerNode) {
      const panner = this.pannerNode as any;
      if (panner.velocityX && panner.velocityY && panner.velocityZ) {
        panner.velocityX.value = this.velocity.x;
        panner.velocityY.value = this.velocity.y;
        panner.velocityZ.value = this.velocity.z;
      }
    }
  }

  /**
   * 更新声相方向
   */
  private updatePannerOrientation(): void {
    if (!this.pannerNode) return;

    // 设置方向
    if ('orientationX' in this.pannerNode && this.pannerNode.orientationX &&
        'orientationY' in this.pannerNode && this.pannerNode.orientationY &&
        'orientationZ' in this.pannerNode && this.pannerNode.orientationZ) {
      this.pannerNode.orientationX.value = this.orientation.x;
      this.pannerNode.orientationY.value = this.orientation.y;
      this.pannerNode.orientationZ.value = this.orientation.z;
    } else {
      // 注意：setOrientation 方法在新版 Web Audio API 中已被弃用
      // 但为了兼容性，我们仍然使用它作为后备方案
      this.pannerNode.setOrientation(this.orientation.x, this.orientation.y, this.orientation.z);
    }
  }
}
