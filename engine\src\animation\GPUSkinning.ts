/**
 * GPU蒙皮系统
 * 使用GPU加速骨骼动画计算
 */
import * as THREE from 'three';
import type { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { System } from '../core/System';
import type { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * GPU蒙皮配置
 */
export interface GPUSkinningConfig {
  /** 最大骨骼数量 */
  maxBones?: number;
  /** 是否使用计算着色器 */
  useComputeShader?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * GPU蒙皮组件
 */
export class GPUSkinningComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'GPUSkinning';

  /** 骨骼网格 */
  private skinnedMesh: THREE.SkinnedMesh | null = null;
  /** 骨骼 */
  private skeleton: THREE.Skeleton | null = null;
  /** 骨骼数量 */
  private boneCount: number = 0;
  /** 骨骼矩阵纹理 */
  private boneTexture: THREE.DataTexture | null = null;
  /** 骨骼矩阵 */
  private boneMatrices: Float32Array | null = null;
  /** 是否启用 */
  protected enabled: boolean = true;
  /** 是否需要更新 */
  private needsUpdate: boolean = true;
  /** 自定义着色器材质 */
  private gpuMaterial: THREE.ShaderMaterial | null = null;
  /** 原始材质 */
  private originalMaterial: THREE.Material | THREE.Material[] | null = null;

  /**
   * 创建GPU蒙皮组件
   */
  constructor() {
    super(GPUSkinningComponent.type);
  }

  /**
   * 设置骨骼网格
   * @param mesh 骨骼网格
   */
  public setSkinnedMesh(mesh: THREE.SkinnedMesh): void {
    this.skinnedMesh = mesh;
    this.skeleton = mesh.skeleton;
    this.boneCount = mesh.skeleton.bones.length;
    this.originalMaterial = mesh.material;
    this.needsUpdate = true;
  }

  /**
   * 获取骨骼网格
   * @returns 骨骼网格
   */
  public getSkinnedMesh(): THREE.SkinnedMesh | null {
    return this.skinnedMesh;
  }

  /**
   * 获取骨骼
   * @returns 骨骼
   */
  public getSkeleton(): THREE.Skeleton | null {
    return this.skeleton;
  }

  /**
   * 获取骨骼数量
   * @returns 骨骼数量
   */
  public getBoneCount(): number {
    return this.boneCount;
  }

  /**
   * 是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 设置启用状态
   * @param enabled 启用状态
   */
  public setEnabled(enabled: boolean): void {
    if (this.enabled !== enabled) {
      this.enabled = enabled;
      this.needsUpdate = true;

      // 如果禁用，恢复原始材质
      if (!enabled && this.skinnedMesh && this.originalMaterial) {
        this.skinnedMesh.material = this.originalMaterial;
      }
    }
  }

  /**
   * 是否需要更新
   * @returns 是否需要更新
   */
  public needsUpdateBones(): boolean {
    return this.needsUpdate;
  }

  /**
   * 设置需要更新
   * @param needsUpdate 是否需要更新
   */
  public setNeedsUpdate(needsUpdate: boolean): void {
    this.needsUpdate = needsUpdate;
  }

  /**
   * 获取骨骼矩阵纹理
   * @returns 骨骼矩阵纹理
   */
  public getBoneTexture(): THREE.DataTexture | null {
    return this.boneTexture;
  }

  /**
   * 获取GPU材质
   * @returns GPU材质
   */
  public getGPUMaterial(): THREE.ShaderMaterial | null {
    return this.gpuMaterial;
  }

  /**
   * 设置GPU材质
   * @param material GPU材质
   */
  public setGPUMaterial(material: THREE.ShaderMaterial): void {
    this.gpuMaterial = material;

    // 应用材质
    if (this.enabled && this.skinnedMesh) {
      this.skinnedMesh.material = material;
    }
  }

  /**
   * 获取原始材质
   * @returns 原始材质
   */
  public getOriginalMaterial(): THREE.Material | THREE.Material[] | null {
    return this.originalMaterial;
  }

  /**
   * 更新骨骼矩阵
   */
  public updateBoneMatrices(): void {
    if (!this.skeleton || !this.enabled) return;

    // 更新骨骼矩阵
    this.skeleton.update();

    // 如果有骨骼纹理，更新纹理
    if (this.boneTexture) {
      this.boneTexture.needsUpdate = true;
    }

    this.needsUpdate = false;
  }
}

/**
 * GPU蒙皮系统
 */
export class GPUSkinningSystem extends System {
  /** 配置 */
  private config: GPUSkinningConfig;
  /** 组件列表 */
  private components: Map<Entity, GPUSkinningComponent> = new Map();
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 是否支持GPU蒙皮 */
  private supportsGPUSkinning: boolean = false;
  /** 是否支持计算着色器 */
  private supportsComputeShader: boolean = false;
  /** 顶点着色器 */
  private vertexShader: string = '';
  /** 片段着色器 */
  private fragmentShader: string = '';
  /** 计算着色器 */
  private computeShader: string = '';

  /**
   * 创建GPU蒙皮系统
   * @param config 配置
   */
  constructor(config: GPUSkinningConfig = {}) {
    super(100); // 设置优先级
    this.config = {
      maxBones: config.maxBones || 60,
      useComputeShader: config.useComputeShader !== undefined ? config.useComputeShader : false,
      debug: config.debug || false
    };

    // 检查是否支持GPU蒙皮
    this.checkGPUSkinningSupport();

    // 初始化着色器
    this.initShaders();
  }

  /**
   * 检查是否支持GPU蒙皮
   */
  private checkGPUSkinningSupport(): void {
    // 检查WebGL2和相关扩展
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2');

    if (gl) {
      // WebGL2支持
      this.supportsGPUSkinning = true;

      // 检查计算着色器支持
      const computeExt = gl.getExtension('WEBGL_compute_shader');
      this.supportsComputeShader = !!computeExt;

      if (this.config.debug) {
        console.log('GPU蒙皮支持: WebGL2');
        console.log('计算着色器支持:', this.supportsComputeShader);
      }
    } else {
      // 检查WebGL1扩展
      const gl1 = canvas.getContext('webgl');
      if (gl1) {
        // 检查浮点纹理支持
        const floatExt = gl1.getExtension('OES_texture_float');
        this.supportsGPUSkinning = !!floatExt;
        this.supportsComputeShader = false;

        if (this.config.debug) {
          console.log('GPU蒙皮支持: WebGL1 + OES_texture_float');
          console.log('计算着色器支持: false');
        }
      } else {
        this.supportsGPUSkinning = false;
        this.supportsComputeShader = false;

        if (this.config.debug) {
          console.warn('GPU蒙皮不支持: WebGL不可用');
        }
      }
    }

    // 如果不支持GPU蒙皮，禁用相关功能
    if (!this.supportsGPUSkinning) {
      if (this.config.debug) {
        console.warn('已禁用GPU蒙皮，将使用CPU蒙皮');
      }
    }

    // 如果不支持计算着色器，禁用相关功能
    if (!this.supportsComputeShader) {
      this.config.useComputeShader = false;

      if (this.config.debug && this.supportsGPUSkinning) {
        console.warn('已禁用计算着色器，将使用标准GPU蒙皮');
      }
    }
  }

  /**
   * 初始化着色器
   */
  private initShaders(): void {
    if (!this.supportsGPUSkinning) return;

    // 初始化顶点着色器
    this.vertexShader = `
      // 骨骼矩阵纹理
      uniform sampler2D boneTexture;
      uniform int boneTextureSize;

      // 获取骨骼矩阵
      mat4 getBoneMatrix(const in float i) {
        float j = i * 4.0;
        float x = mod(j, float(boneTextureSize));
        float y = floor(j / float(boneTextureSize));

        float dx = 1.0 / float(boneTextureSize);
        float dy = 1.0 / float(boneTextureSize);

        y = dy * (y + 0.5);

        vec4 v1 = texture2D(boneTexture, vec2(dx * (x + 0.5), y));
        vec4 v2 = texture2D(boneTexture, vec2(dx * (x + 1.5), y));
        vec4 v3 = texture2D(boneTexture, vec2(dx * (x + 2.5), y));
        vec4 v4 = texture2D(boneTexture, vec2(dx * (x + 3.5), y));

        return mat4(v1, v2, v3, v4);
      }

      // 主函数
      void main() {
        // 骨骼权重
        vec4 skinWeight = skinWeight;

        // 骨骼索引
        vec4 skinIndex = skinIndex;

        // 计算蒙皮矩阵
        mat4 skinMatrix = mat4(0.0);
        skinMatrix += skinWeight.x * getBoneMatrix(skinIndex.x);
        skinMatrix += skinWeight.y * getBoneMatrix(skinIndex.y);
        skinMatrix += skinWeight.z * getBoneMatrix(skinIndex.z);
        skinMatrix += skinWeight.w * getBoneMatrix(skinIndex.w);

        // 应用蒙皮矩阵
        vec4 transformedPosition = skinMatrix * vec4(position, 1.0);
        vec4 transformedNormal = skinMatrix * vec4(normal, 0.0);

        // 输出位置
        gl_Position = projectionMatrix * modelViewMatrix * transformedPosition;

        // 传递法线和UV
        vNormal = transformedNormal.xyz;
        vUv = uv;
      }
    `;

    // 初始化片段着色器
    this.fragmentShader = `
      // 输入变量
      varying vec3 vNormal;
      varying vec2 vUv;

      // 材质属性
      uniform vec3 diffuse;
      uniform float opacity;
      uniform sampler2D map;

      // 主函数
      void main() {
        // 计算法线
        vec3 normal = normalize(vNormal);

        // 基础光照
        float light = max(dot(normal, vec3(0.0, 1.0, 0.0)), 0.3);

        // 纹理颜色
        vec4 texColor = texture2D(map, vUv);

        // 最终颜色
        vec4 finalColor = vec4(diffuse * light, opacity) * texColor;

        gl_FragColor = finalColor;
      }
    `;

    // 如果支持计算着色器，初始化计算着色器
    if (this.supportsComputeShader) {
      this.computeShader = `
        // 计算着色器
        #version 310 es

        // 输入骨骼矩阵
        layout(std140, binding = 0) buffer BoneMatricesIn {
          mat4 boneMatricesIn[];
        };

        // 输出骨骼矩阵
        layout(std140, binding = 1) buffer BoneMatricesOut {
          mat4 boneMatricesOut[];
        };

        // 动画数据
        layout(std140, binding = 2) buffer AnimationData {
          float time;
          float weight;
          int boneCount;
          int clipCount;
        };

        // 工作组大小
        layout(local_size_x = 1, local_size_y = 1, local_size_z = 1) in;

        // 主函数
        void main() {
          // 获取骨骼索引
          uint boneIndex = gl_GlobalInvocationID.x;

          // 检查是否超出范围
          if (boneIndex >= boneCount) {
            return;
          }

          // 复制骨骼矩阵
          boneMatricesOut[boneIndex] = boneMatricesIn[boneIndex];
        }
      `;
    }
  }

  /**
   * 创建GPU蒙皮组件
   * @param entity 实体
   * @param skinnedMesh 骨骼网格
   * @returns GPU蒙皮组件
   */
  public createGPUSkinning(entity: Entity, skinnedMesh: THREE.SkinnedMesh): GPUSkinningComponent {
    // 创建组件
    const component = new GPUSkinningComponent();
    component.setSkinnedMesh(skinnedMesh);

    // 添加到实体
    entity.addComponent(component);

    // 添加到映射
    this.components.set(entity, component);

    // 初始化GPU蒙皮
    this.initGPUSkinning(component);

    return component;
  }

  /**
   * 初始化GPU蒙皮
   * @param component GPU蒙皮组件
   */
  private initGPUSkinning(component: GPUSkinningComponent): void {
    if (!this.supportsGPUSkinning) return;

    const skinnedMesh = component.getSkinnedMesh();
    const skeleton = component.getSkeleton();

    if (!skinnedMesh || !skeleton) return;

    // 获取骨骼数量
    const boneCount = component.getBoneCount();

    // 创建骨骼纹理
    const size = Math.sqrt(boneCount * 4);
    const boneMatrices = new Float32Array(size * size * 4);

    // 创建数据纹理
    const boneTexture = new THREE.DataTexture(
      boneMatrices,
      size,
      size,
      THREE.RGBAFormat,
      THREE.FloatType
    );
    boneTexture.needsUpdate = true;

    // 创建GPU材质
    const originalMaterial = component.getOriginalMaterial();
    if (!originalMaterial) return;

    // 克隆原始材质的属性
    const gpuMaterial = new THREE.ShaderMaterial({
      uniforms: {
        boneTexture: { value: boneTexture },
        boneTextureSize: { value: size },
        diffuse: { value: new THREE.Color(0xffffff) },
        opacity: { value: 1.0 },
        map: { value: null }
      },
      vertexShader: this.vertexShader,
      fragmentShader: this.fragmentShader,
      transparent: Array.isArray(originalMaterial) ? originalMaterial[0].transparent : originalMaterial.transparent,
      side: Array.isArray(originalMaterial) ? originalMaterial[0].side : originalMaterial.side
    });

    // 手动设置skinning
    (gpuMaterial as any).skinning = true;

    // 如果原始材质有纹理，设置纹理
    const material = Array.isArray(originalMaterial) ? originalMaterial[0] : originalMaterial;
    if ('map' in material && material.map) {
      gpuMaterial.uniforms.map.value = material.map;
    }

    // 设置GPU材质
    component.setGPUMaterial(gpuMaterial);
  }

  /**
   * 移除GPU蒙皮组件
   * @param entity 实体
   * @returns 是否成功移除
   */
  public removeGPUSkinning(entity: Entity): boolean {
    // 检查实体是否有GPU蒙皮组件
    const component = this.components.get(entity);
    if (!component) {
      return false;
    }

    // 恢复原始材质
    const skinnedMesh = component.getSkinnedMesh();
    const originalMaterial = component.getOriginalMaterial();
    if (skinnedMesh && originalMaterial) {
      skinnedMesh.material = originalMaterial;
    }

    // 从映射中移除
    this.components.delete(entity);

    // 从实体中移除
    entity.removeComponent(GPUSkinningComponent.type);

    return true;
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.supportsGPUSkinning) return;

    // 更新所有GPU蒙皮组件
    for (const [entity, component] of this.components) {
      if (component.isEnabled() && component.needsUpdateBones()) {
        component.updateBoneMatrices();
      }
    }
  }
}