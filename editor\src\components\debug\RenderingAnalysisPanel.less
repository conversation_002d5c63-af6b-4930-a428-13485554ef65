/**
 * 渲染分析面板样式
 */

.rendering-analysis-panel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .rendering-toolbar {
    padding: 12px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
    
    .ant-space {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
  }
  
  .rendering-content {
    padding: 16px;
    overflow: auto;
    flex: 1;
  }
  
  .rendering-bottlenecks {
    .rendering-bottleneck-item {
      margin-bottom: 16px;
      padding: 12px;
      border-radius: 4px;
      background-color: #f9f9f9;
      
      .anticon {
        margin-right: 8px;
      }
      
      .ant-typography {
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  
  .rendering-warning {
    color: #faad14;
  }
  
  .rendering-error {
    color: #f5222d;
  }
  
  .rendering-good {
    color: #52c41a;
  }
}

// 暗色主题样式
.dark-theme {
  .rendering-analysis-panel {
    .rendering-toolbar {
      background-color: #1e1e1e;
      border-bottom: 1px solid #303030;
    }
    
    .rendering-bottlenecks {
      .rendering-bottleneck-item {
        background-color: #2a2a2a;
      }
    }
  }
}
