/**
 * 场景图示例
 * 展示如何使用场景图功能
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { Entity } from '../../src/core/Entity';
import { Scene, SceneGraph, SceneGraphNode, Skybox } from '../../src/scene';

export class SceneGraphExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 场景图实例 */
  private sceneGraph: SceneGraph;
  
  /** UI元素 */
  private ui: HTMLElement;
  
  /** 场景图树元素 */
  private treeElement: HTMLElement;
  
  /** 选中的节点 */
  private selectedNode: SceneGraphNode | null = null;
  
  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建场景图示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建场景
    this.scene = this.engine.getWorld().createScene('场景图示例');
    this.engine.getWorld().setActiveScene(this.scene);
    
    // 获取场景图
    this.sceneGraph = this.scene.getSceneGraph();
    
    // 创建UI
    this.ui = this.createUI();
    this.treeElement = document.createElement('div');
    this.ui.appendChild(this.treeElement);
  }

  /**
   * 初始化示例
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }
    
    // 创建场景内容
    await this.createSceneContent();
    
    // 初始化场景图
    this.sceneGraph.initialize();
    
    // 监听场景图事件
    this.sceneGraph.on('built', () => {
      this.updateSceneGraphTree();
    });
    
    this.sceneGraph.on('nodeAdded', () => {
      this.updateSceneGraphTree();
    });
    
    this.sceneGraph.on('nodeRemoved', () => {
      this.updateSceneGraphTree();
    });
    
    // 更新场景图树
    this.updateSceneGraphTree();
    
    // 启动引擎
    this.engine.start();
    
    this.initialized = true;
  }

  /**
   * 创建场景内容
   */
  private async createSceneContent(): Promise<void> {
    // 设置天空盒
    this.scene.setSkybox(new Skybox('color', new THREE.Color(0x87CEEB)));
    
    // 设置环境光
    this.scene.setAmbientLight(new THREE.Color(0xffffff), 0.5);
    
    // 创建地面
    const ground = new Entity('地面');
    ground.getTransform().setPosition(0, -1, 0);
    ground.getTransform().setScale(10, 0.1, 10);
    
    // 添加网格组件
    const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
    const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x808080 });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.addComponent('MeshComponent', { mesh: groundMesh });
    
    // 添加到场景
    this.scene.addEntity(ground);
    
    // 创建父实体
    const parent = new Entity('父实体');
    parent.getTransform().setPosition(0, 1, 0);
    
    // 添加网格组件
    const parentGeometry = new THREE.BoxGeometry(1, 1, 1);
    const parentMaterial = new THREE.MeshStandardMaterial({ color: 0xff0000 });
    const parentMesh = new THREE.Mesh(parentGeometry, parentMaterial);
    parent.addComponent('MeshComponent', { mesh: parentMesh });
    
    // 添加到场景
    this.scene.addEntity(parent);
    
    // 创建子实体1
    const child1 = new Entity('子实体1');
    child1.getTransform().setPosition(1.5, 0, 0);
    child1.getTransform().setScale(0.5, 0.5, 0.5);
    
    // 添加网格组件
    const child1Geometry = new THREE.SphereGeometry(0.5, 32, 32);
    const child1Material = new THREE.MeshStandardMaterial({ color: 0x00ff00 });
    const child1Mesh = new THREE.Mesh(child1Geometry, child1Material);
    child1.addComponent('MeshComponent', { mesh: child1Mesh });
    
    // 添加到父实体
    parent.addChild(child1);
    
    // 创建子实体2
    const child2 = new Entity('子实体2');
    child2.getTransform().setPosition(-1.5, 0, 0);
    child2.getTransform().setScale(0.5, 0.5, 0.5);
    
    // 添加网格组件
    const child2Geometry = new THREE.ConeGeometry(0.5, 1, 32);
    const child2Material = new THREE.MeshStandardMaterial({ color: 0x0000ff });
    const child2Mesh = new THREE.Mesh(child2Geometry, child2Material);
    child2.addComponent('MeshComponent', { mesh: child2Mesh });
    
    // 添加到父实体
    parent.addChild(child2);
    
    // 创建孙实体
    const grandchild = new Entity('孙实体');
    grandchild.getTransform().setPosition(0, 1, 0);
    grandchild.getTransform().setScale(0.5, 0.5, 0.5);
    
    // 添加网格组件
    const grandchildGeometry = new THREE.TorusGeometry(0.3, 0.1, 16, 32);
    const grandchildMaterial = new THREE.MeshStandardMaterial({ color: 0xffff00 });
    const grandchildMesh = new THREE.Mesh(grandchildGeometry, grandchildMaterial);
    grandchild.addComponent('MeshComponent', { mesh: grandchildMesh });
    
    // 添加到子实体1
    child1.addChild(grandchild);
    
    // 添加标签
    parent.addTag('重要');
    child1.addTag('可交互');
    child2.addTag('静态');
    grandchild.addTag('可交互');
  }

  /**
   * 创建UI
   * @returns UI元素
   */
  private createUI(): HTMLElement {
    const ui = document.createElement('div');
    ui.style.position = 'absolute';
    ui.style.top = '10px';
    ui.style.left = '10px';
    ui.style.width = '300px';
    ui.style.maxHeight = '80vh';
    ui.style.overflowY = 'auto';
    ui.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    ui.style.color = 'white';
    ui.style.padding = '10px';
    ui.style.borderRadius = '5px';
    ui.style.fontFamily = 'Arial, sans-serif';
    
    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '场景图示例';
    title.style.margin = '0 0 10px 0';
    ui.appendChild(title);
    
    // 创建描述
    const description = document.createElement('p');
    description.textContent = '这个示例展示了如何使用场景图功能。点击节点可以选择实体。';
    description.style.margin = '0 0 10px 0';
    ui.appendChild(description);
    
    // 创建查询按钮
    const queryContainer = document.createElement('div');
    queryContainer.style.marginBottom = '10px';
    
    const queryButton = document.createElement('button');
    queryButton.textContent = '查询可交互实体';
    queryButton.style.marginRight = '5px';
    queryButton.addEventListener('click', () => {
      this.queryInteractiveEntities();
    });
    queryContainer.appendChild(queryButton);
    
    const resetButton = document.createElement('button');
    resetButton.textContent = '重置';
    resetButton.addEventListener('click', () => {
      this.resetSelection();
    });
    queryContainer.appendChild(resetButton);
    
    ui.appendChild(queryContainer);
    
    // 添加到文档
    document.body.appendChild(ui);
    
    return ui;
  }

  /**
   * 更新场景图树
   */
  private updateSceneGraphTree(): void {
    // 清空树元素
    this.treeElement.innerHTML = '';
    
    // 获取根节点
    const rootNode = this.sceneGraph.getRootNode();
    
    if (!rootNode) {
      return;
    }
    
    // 创建树
    const tree = this.createTreeNode(rootNode);
    this.treeElement.appendChild(tree);
  }

  /**
   * 创建树节点
   * @param node 场景图节点
   * @returns 树节点元素
   */
  private createTreeNode(node: SceneGraphNode): HTMLElement {
    const container = document.createElement('div');
    
    // 创建节点
    const nodeElement = document.createElement('div');
    nodeElement.style.padding = '5px';
    nodeElement.style.cursor = 'pointer';
    nodeElement.style.borderRadius = '3px';
    
    // 如果是选中的节点，设置背景色
    if (this.selectedNode && this.selectedNode.id === node.id) {
      nodeElement.style.backgroundColor = 'rgba(0, 120, 215, 0.5)';
    }
    
    // 添加点击事件
    nodeElement.addEventListener('click', (event) => {
      event.stopPropagation();
      this.selectNode(node);
    });
    
    // 创建节点内容
    const nodeContent = document.createElement('span');
    nodeContent.textContent = `${node.name} (${node.type})`;
    
    // 如果有子节点，添加展开/折叠图标
    if (node.children.length > 0) {
      const expandIcon = document.createElement('span');
      expandIcon.textContent = node.expanded ? '▼ ' : '► ';
      expandIcon.style.marginRight = '5px';
      expandIcon.style.display = 'inline-block';
      expandIcon.style.width = '15px';
      
      // 添加点击事件
      expandIcon.addEventListener('click', (event) => {
        event.stopPropagation();
        node.expanded = !node.expanded;
        this.updateSceneGraphTree();
      });
      
      nodeElement.appendChild(expandIcon);
    } else {
      // 如果没有子节点，添加空白占位
      const placeholder = document.createElement('span');
      placeholder.textContent = '  ';
      placeholder.style.marginRight = '5px';
      placeholder.style.display = 'inline-block';
      placeholder.style.width = '15px';
      nodeElement.appendChild(placeholder);
    }
    
    nodeElement.appendChild(nodeContent);
    container.appendChild(nodeElement);
    
    // 如果有子节点且节点是展开的，创建子节点
    if (node.children.length > 0 && node.expanded) {
      const childrenContainer = document.createElement('div');
      childrenContainer.style.paddingLeft = '20px';
      
      for (const child of node.children) {
        const childElement = this.createTreeNode(child);
        childrenContainer.appendChild(childElement);
      }
      
      container.appendChild(childrenContainer);
    }
    
    return container;
  }

  /**
   * 选择节点
   * @param node 场景图节点
   */
  private selectNode(node: SceneGraphNode): void {
    this.selectedNode = node;
    
    // 获取实体
    const entity = this.sceneGraph.getEntity(node.id);
    
    if (entity) {
      console.log('选中实体:', entity);
      
      // 高亮显示实体
      // 这里可以添加高亮显示的代码
    }
    
    // 更新场景图树
    this.updateSceneGraphTree();
  }

  /**
   * 查询可交互实体
   */
  private queryInteractiveEntities(): void {
    // 查询带有"可交互"标签的节点
    const nodes = this.sceneGraph.queryNodes({
      tagFilter: ['可交互'],
      includeInvisible: false,
      recursive: true
    });
    
    console.log('可交互实体:', nodes);
    
    // 高亮显示查询结果
    if (nodes.length > 0) {
      this.selectNode(nodes[0]);
    }
  }

  /**
   * 重置选择
   */
  private resetSelection(): void {
    this.selectedNode = null;
    this.updateSceneGraphTree();
  }

  /**
   * 销毁示例
   */
  public dispose(): void {
    // 停止引擎
    this.engine.stop();
    
    // 移除UI
    if (this.ui && this.ui.parentNode) {
      this.ui.parentNode.removeChild(this.ui);
    }
    
    // 销毁引擎
    this.engine.dispose();
  }
}
