/**
 * 物理状态管理模块
 * 用于管理物理系统和调试器的状态
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

/**
 * 物理系统设置
 */
export interface PhysicsSystemSettings {
  /** 重力 */
  gravity: { x: number; y: number; z: number };
  /** 物理更新频率（Hz） */
  updateFrequency: number;
  /** 是否启用休眠 */
  allowSleep: boolean;
  /** 是否启用连续碰撞检测 */
  enableCCD: boolean;
  /** 连续碰撞检测选项 */
  ccdOptions: {
    /** 最大子步数 */
    maxSubSteps: number;
    /** 最小子步时间 */
    minSubStepTime: number;
    /** 速度阈值 - 超过此值启用CCD */
    velocityThreshold: number;
    /** 是否对所有物体启用CCD */
    enableForAll: boolean;
  };
  /** 迭代次数 */
  iterations: number;
}

/**
 * 物理调试器设置
 */
export interface PhysicsDebuggerSettings {
  /** 是否启用调试器 */
  enabled: boolean;
  /** 是否使用增强型调试器 */
  useEnhancedDebugger: boolean;
  /** 物理体颜色 */
  bodyColor: string;
  /** 约束颜色 */
  constraintColor: string;
  /** 是否显示速度向量 */
  showVelocities: boolean;
  /** 是否显示力向量 */
  showForces: boolean;
  /** 是否显示质心 */
  showCenterOfMass: boolean;
  /** 是否显示睡眠状态 */
  showSleepState: boolean;
  /** 是否显示物理性能统计 */
  showPerformanceStats: boolean;
  /** 速度向量颜色 */
  velocityColor: string;
  /** 力向量颜色 */
  forceColor: string;
  /** 质心颜色 */
  centerOfMassColor: string;
  /** 睡眠状态颜色 */
  sleepStateColor: string;
  /** 向量缩放因子 */
  vectorScale: number;
}

/**
 * 物理状态
 */
export interface PhysicsState {
  /** 物理系统设置 */
  systemSettings: PhysicsSystemSettings;
  /** 物理调试器设置 */
  debuggerSettings: PhysicsDebuggerSettings;
}

/**
 * 初始状态
 */
const initialState: PhysicsState = {
  systemSettings: {
    gravity: { x: 0, y: -9.81, z: 0 },
    updateFrequency: 60,
    allowSleep: true,
    enableCCD: false,
    ccdOptions: {
      maxSubSteps: 5,
      minSubStepTime: 1 / 240,
      velocityThreshold: 5,
      enableForAll: false
    },
    iterations: 10
  },
  debuggerSettings: {
    enabled: false,
    useEnhancedDebugger: false,
    bodyColor: '#ff0000',
    constraintColor: '#00ff00',
    showVelocities: false,
    showForces: false,
    showCenterOfMass: false,
    showSleepState: false,
    showPerformanceStats: false,
    velocityColor: '#00ffff',
    forceColor: '#ff00ff',
    centerOfMassColor: '#ffff00',
    sleepStateColor: '#888888',
    vectorScale: 0.1
  }
};

/**
 * 物理状态切片
 */
const physicsSlice = createSlice({
  name: 'physics',
  initialState,
  reducers: {
    /**
     * 更新物理系统设置
     */
    updatePhysicsSystemSettings: (state, action: PayloadAction<Partial<PhysicsSystemSettings>>) => {
      state.systemSettings = {
        ...state.systemSettings,
        ...action.payload
      };
    },
    
    /**
     * 更新物理调试器设置
     */
    updatePhysicsDebuggerSettings: (state, action: PayloadAction<Partial<PhysicsDebuggerSettings>>) => {
      state.debuggerSettings = {
        ...state.debuggerSettings,
        ...action.payload
      };
    },
    
    /**
     * 重置物理系统设置
     */
    resetPhysicsSystemSettings: (state) => {
      state.systemSettings = initialState.systemSettings;
    },
    
    /**
     * 重置物理调试器设置
     */
    resetPhysicsDebuggerSettings: (state) => {
      state.debuggerSettings = initialState.debuggerSettings;
    }
  }
});

// 导出动作创建器
export const {
  updatePhysicsSystemSettings,
  updatePhysicsDebuggerSettings,
  resetPhysicsSystemSettings,
  resetPhysicsDebuggerSettings
} = physicsSlice.actions;

// 导出reducer
export default physicsSlice.reducer;
