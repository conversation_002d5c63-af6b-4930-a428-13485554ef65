"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CSM = exports.CSMModes = void 0;
/**
 * 级联阴影映射(CSM)实现
 * 基于Three.js实现的级联阴影映射系统
 */
var THREE = require("three");
var Frustum_1 = require("./Frustum");
var Shader_1 = require("./Shader");
var EventEmitter_1 = require("../../utils/EventEmitter");
// 原始着色器代码
var originalLightsFragmentBegin = THREE.ShaderChunk.lights_fragment_begin;
var originalLightsParsBegin = THREE.ShaderChunk.lights_pars_begin;
// 辅助矩阵
var _lightOrientationMatrix = new THREE.Matrix4();
var _lightOrientationMatrixInverse = new THREE.Matrix4();
var _cameraToLightMatrix = new THREE.Matrix4();
var _lightSpaceFrustum = new Frustum_1.Frustum();
var _center = new THREE.Vector3();
var _bbox = new THREE.Box3();
var _uniformArray = [];
var _logArray = [];
/**
 * CSM模式枚举
 */
exports.CSMModes = {
    UNIFORM: 'UNIFORM',
    LOGARITHMIC: 'LOGARITHMIC',
    PRACTICAL: 'PRACTICAL',
    CUSTOM: 'CUSTOM' // 自定义分割
};
/**
 * 级联阴影映射类
 */
var CSM = /** @class */ (function (_super) {
    __extends(CSM, _super);
    /**
     * 创建CSM实例
     * @param params CSM参数
     */
    function CSM(params) {
        if (params === void 0) { params = {}; }
        var _this = this;
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
        _this = _super.call(this) || this;
        _this.lightEntities = [];
        _this.shaders = new Map();
        _this.materials = new Set();
        _this.needsUpdate = false;
        _this.cascades = (_a = params.cascades) !== null && _a !== void 0 ? _a : 4;
        _this.maxFar = (_b = params.maxFar) !== null && _b !== void 0 ? _b : 100;
        _this.mode = (_c = params.mode) !== null && _c !== void 0 ? _c : exports.CSMModes.PRACTICAL;
        _this.shadowMapSize = (_d = params.shadowMapSize) !== null && _d !== void 0 ? _d : 2048;
        _this.shadowBias = (_e = params.shadowBias) !== null && _e !== void 0 ? _e : -0.000001;
        _this.shadowNormalBias = 0;
        _this.lightDirection = (_f = params.lightDirection) !== null && _f !== void 0 ? _f : new THREE.Vector3(1, -1, 1).normalize();
        _this.lightDirectionUp = (_g = params.lightDirectionUp) !== null && _g !== void 0 ? _g : THREE.Object3D.DEFAULT_UP.clone();
        _this.lightColor = (_h = params.lightColor) !== null && _h !== void 0 ? _h : 0xffffff;
        _this.lightIntensity = (_j = params.lightIntensity) !== null && _j !== void 0 ? _j : 1;
        _this.lightMargin = (_k = params.lightMargin) !== null && _k !== void 0 ? _k : 200;
        _this.customSplitsCallback = params.customSplitsCallback;
        _this.fade = (_l = params.fade) !== null && _l !== void 0 ? _l : true;
        _this.mainFrustum = new Frustum_1.Frustum();
        _this.frustums = [];
        _this.breaks = [];
        _this.lights = [];
        _this.createLights(params.light);
        _this.updateFrustums();
        _this.injectInclude();
        return _this;
    }
    /**
     * 创建光源
     * @param sourceLight 源光源
     */
    CSM.prototype.createLights = function (sourceLight) {
        if (sourceLight) {
            this.sourceLight = sourceLight;
            this.shadowBias = sourceLight.shadow.bias;
            this.lightIntensity = sourceLight.intensity;
            this.lightColor = sourceLight.color.clone();
            for (var i = 0; i < this.cascades; i++) {
                var light = sourceLight.clone();
                this.createLight(light, i);
            }
            return;
        }
        // 如果没有提供光源，创建默认光源
        for (var i = 0; i < this.cascades; i++) {
            var light = new THREE.DirectionalLight(this.lightColor, this.lightIntensity);
            this.createLight(light, i);
        }
    };
    /**
     * 创建单个光源
     * @param light 光源
     * @param index 索引
     */
    CSM.prototype.createLight = function (light, index) {
        light.castShadow = true;
        light.frustumCulled = false;
        light.shadow.mapSize.width = this.shadowMapSize;
        light.shadow.mapSize.height = this.shadowMapSize;
        light.shadow.camera.near = 0;
        light.shadow.camera.far = 1;
        light.intensity = this.lightIntensity;
        this.lights.push(light);
        light.name = 'CSM_' + light.name;
        light.target.name = 'CSM_' + light.target.name;
        this.emit('lightCreated', light, index);
    };
    /**
     * 更新
     */
    CSM.prototype.update = function (camera) {
        if (this.sourceLight) {
            this.lightDirection.subVectors(this.sourceLight.target.position, this.sourceLight.position);
        }
        if (this.needsUpdate) {
            this.injectInclude();
            this.updateFrustums();
            for (var _i = 0, _a = this.lights; _i < _a.length; _i++) {
                var light = _a[_i];
                if (light.shadow.map) {
                    light.shadow.map.dispose();
                    light.shadow.map = null;
                }
                light.shadow.camera.updateProjectionMatrix();
                light.shadow.needsUpdate = true;
            }
            this.needsUpdate = false;
        }
        // 更新光源位置和方向
        this.updateLights(camera);
    };
    /**
     * 更新光源位置和方向
     */
    CSM.prototype.updateLights = function (camera) {
        var frustums = this.frustums;
        for (var i = 0; i < frustums.length; i++) {
            var light = this.lights[i];
            var frustum = frustums[i];
            var shadowCam = light.shadow.camera;
            // 计算光源位置和方向
            _lightOrientationMatrix.lookAt(new THREE.Vector3(0, 0, 0), this.lightDirection, this.lightDirectionUp);
            _lightOrientationMatrixInverse.copy(_lightOrientationMatrix).invert();
            // 计算相机到光源的变换矩阵
            _cameraToLightMatrix.multiplyMatrices(_lightOrientationMatrixInverse, camera.matrixWorldInverse);
            // 变换视锥体到光源空间
            _lightSpaceFrustum.copy(frustum);
            _lightSpaceFrustum.applyMatrix4(_cameraToLightMatrix);
            // 获取视锥体包围盒
            var lightSpaceBbox = _lightSpaceFrustum.getBoundingBox(_bbox);
            lightSpaceBbox.getCenter(_center);
            lightSpaceBbox.getSize(_center);
            // 设置光源位置和目标
            var position = light.position.copy(_center);
            position.x += this.lightDirection.x * this.lightMargin;
            position.y += this.lightDirection.y * this.lightMargin;
            position.z += this.lightDirection.z * this.lightMargin;
            light.target.position.copy(_center);
            // 更新光源矩阵
            light.updateMatrixWorld();
            light.target.updateMatrixWorld();
            // 设置阴影相机参数
            shadowCam.updateMatrixWorld();
            shadowCam.matrixWorldInverse.copy(shadowCam.matrixWorld).invert();
        }
    };
    /**
     * 注入着色器代码
     */
    CSM.prototype.injectInclude = function () {
        THREE.ShaderChunk.lights_fragment_begin = Shader_1.Shader.lights_fragment_begin(this.cascades);
        THREE.ShaderChunk.lights_pars_begin = Shader_1.Shader.lights_pars_begin();
    };
    /**
     * 移除着色器代码
     */
    CSM.prototype.removeInclude = function () {
        THREE.ShaderChunk.lights_fragment_begin = originalLightsFragmentBegin;
        THREE.ShaderChunk.lights_pars_begin = originalLightsParsBegin;
    };
    /**
     * 设置材质
     * @param mesh 网格
     */
    CSM.prototype.setupMaterial = function (mesh) {
        var material = mesh.material;
        if (!material.userData)
            material.userData = {};
        var materials = this.materials;
        if (material.userData.IGNORE_CSM)
            return;
        if (materials.has(material))
            return;
        materials.add(material);
        material.defines = material.defines || {};
        material.defines.USE_CSM = 1;
        material.defines.CSM_CASCADES = this.cascades;
        if (this.fade)
            material.defines.CSM_FADE = '';
        material.needsUpdate = true;
    };
    /**
     * 清理材质
     * @param material 材质
     */
    CSM.prototype.teardownMaterial = function (material) {
        if (!(material === null || material === void 0 ? void 0 : material.isMaterial))
            return;
        if (!material.userData)
            material.userData = {};
        if (material.defines) {
            delete material.defines.USE_CSM;
            delete material.defines.CSM_CASCADES;
            delete material.defines.CSM_FADE;
        }
        material.needsUpdate = true;
        this.shaders.delete(material);
        this.materials.delete(material);
    };
    /**
     * 计算分割点
     */
    CSM.prototype.getBreaks = function () {
        var amount = this.cascades;
        var near = 0.1;
        var far = this.maxFar;
        this.breaks.length = 0;
        switch (this.mode) {
            case exports.CSMModes.UNIFORM:
                this.getUniformBreaks(amount, near, far);
                break;
            case exports.CSMModes.LOGARITHMIC:
                this.getLogarithmicBreaks(amount, near, far);
                break;
            case exports.CSMModes.PRACTICAL:
                this.getPracticalBreaks(amount, near, far);
                break;
            case exports.CSMModes.CUSTOM:
                if (this.customSplitsCallback) {
                    this.customSplitsCallback(amount, near, far, this.breaks);
                }
                break;
        }
    };
    /**
     * 获取均匀分割点
     */
    CSM.prototype.getUniformBreaks = function (amount, near, far) {
        for (var i = 1; i <= amount; i++) {
            this.breaks.push((near + (far - near) * i / amount));
        }
    };
    /**
     * 获取对数分割点
     */
    CSM.prototype.getLogarithmicBreaks = function (amount, near, far) {
        for (var i = 1; i <= amount; i++) {
            this.breaks.push(near * Math.pow(far / near, i / amount));
        }
    };
    /**
     * 获取实用分割点
     */
    CSM.prototype.getPracticalBreaks = function (amount, near, far) {
        var lambda = 0.5;
        for (var i = 1; i <= amount; i++) {
            var log = near * Math.pow(far / near, i / amount);
            var uniform = near + (far - near) * i / amount;
            this.breaks.push(lambda * log + (1 - lambda) * uniform);
        }
    };
    /**
     * 初始化级联
     */
    CSM.prototype.initCascades = function () {
        this.frustums.length = 0;
        for (var i = 0; i < this.cascades; i++) {
            var frustum = new Frustum_1.Frustum();
            this.frustums.push(frustum);
        }
    };
    /**
     * 更新阴影边界
     */
    CSM.prototype.updateShadowBounds = function () {
        // 更新主视锥体
        this.mainFrustum.setFromProjectionMatrix(new THREE.Matrix4().makeOrthographic(-1, 1, 1, -1, 0.1, this.maxFar), this.maxFar);
        // 更新各级联的视锥体
        for (var i = 0; i < this.cascades; i++) {
            var near = i === 0 ? 0.1 : this.breaks[i - 1];
            var far = this.breaks[i];
            this.frustums[i].setFromProjectionMatrix(new THREE.Matrix4().makeOrthographic(-1, 1, 1, -1, near, far), far);
        }
    };
    /**
     * 更改光源
     */
    CSM.prototype.changeLights = function (light) {
        this.sourceLight = light;
        this.lightDirection.subVectors(light.target.position, light.position).normalize();
        this.lightIntensity = light.intensity;
        this.lightColor = light.color.clone();
        this.shadowBias = light.shadow.bias;
        // 更新所有级联光源
        for (var i = 0; i < this.lights.length; i++) {
            var cascadeLight = this.lights[i];
            cascadeLight.intensity = this.lightIntensity;
            cascadeLight.color.copy(light.color);
            cascadeLight.shadow.bias = this.shadowBias;
        }
        this.needsUpdate = true;
    };
    /**
     * 更新视锥体
     */
    CSM.prototype.updateFrustums = function () {
        this.getBreaks();
        this.initCascades();
        this.updateShadowBounds();
    };
    /**
     * 销毁
     */
    CSM.prototype.dispose = function () {
        var _this = this;
        this.materials.forEach(function (material) {
            _this.teardownMaterial(material);
        });
        this.materials.clear();
        this.shaders.clear();
        for (var _i = 0, _a = this.lights; _i < _a.length; _i++) {
            var light = _a[_i];
            if (light.shadow.map) {
                light.shadow.map.dispose();
            }
            light.dispose();
        }
        this.lights = [];
        this.removeInclude();
    };
    return CSM;
}(EventEmitter_1.EventEmitter));
exports.CSM = CSM;
