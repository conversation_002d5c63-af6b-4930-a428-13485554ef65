/**
 * 场景面板样式
 */
.scene-panel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    h3 {
      margin: 0;
      font-size: 16px;
    }
  }

  .search-input {
    margin-bottom: 12px;
  }

  .tree-container {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 8px;
    background: #fff;
  }

  .entity-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .entity-title {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .entity-actions {
      display: flex;
      opacity: 0;
      transition: opacity 0.2s;
    }

    &:hover .entity-actions {
      opacity: 1;
    }
  }

  .entity-node {
    &.entity-locked {
      color: #999;
    }

    &.entity-hidden {
      color: #ccc;
      font-style: italic;
    }
  }
}
