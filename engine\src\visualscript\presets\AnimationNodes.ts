/**
 * 动画相关的可视化脚本节点
 */

import { VisualScriptNode } from '../VisualScriptNode';
import { NodeRegistry } from '../NodeRegistry';

/**
 * 播放动画节点
 */
export class PlayAnimationNode extends VisualScriptNode {
  constructor() {
    super('PlayAnimation', '播放动画');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('entity', 'entity', '实体');
    this.addInput('clipName', 'string', '动画名称');
    this.addOutput('completed', 'exec', '完成');
  }

  public execute(inputs: any): any {
    if (inputs.trigger && inputs.entity && inputs.clipName) {
      const animationComponent = inputs.entity.getComponent('AnimationComponent') as any;
      if (animationComponent) {
        animationComponent.play(inputs.clipName);
        return { completed: true };
      }
    }
    return {};
  }
}

/**
 * 停止动画节点
 */
export class StopAnimationNode extends VisualScriptNode {
  constructor() {
    super('StopAnimation', '停止动画');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('entity', 'entity', '实体');
    this.addOutput('completed', 'exec', '完成');
  }

  public execute(inputs: any): any {
    if (inputs.trigger && inputs.entity) {
      const animationComponent = inputs.entity.getComponent('AnimationComponent') as any;
      if (animationComponent) {
        animationComponent.stop();
        return { completed: true };
      }
    }
    return {};
  }
}

/**
 * 设置动画速度节点
 */
export class SetAnimationSpeedNode extends VisualScriptNode {
  constructor() {
    super('SetAnimationSpeed', '设置动画速度');
    this.addInput('trigger', 'exec', '触发');
    this.addInput('entity', 'entity', '实体');
    this.addInput('speed', 'number', '速度');
    this.addOutput('completed', 'exec', '完成');
  }

  public execute(inputs: any): any {
    if (inputs.trigger && inputs.entity && typeof inputs.speed === 'number') {
      const animationComponent = inputs.entity.getComponent('AnimationComponent') as any;
      if (animationComponent) {
        const animator = animationComponent.getAnimator();
        if (animator) {
          animator.setTimeScale(inputs.speed);
          return { completed: true };
        }
      }
    }
    return {};
  }
}

/**
 * 获取动画状态节点
 */
export class GetAnimationStateNode extends VisualScriptNode {
  constructor() {
    super('GetAnimationState', '获取动画状态');
    this.addInput('entity', 'entity', '实体');
    this.addOutput('isPlaying', 'boolean', '是否播放中');
    this.addOutput('currentClip', 'string', '当前动画');
    this.addOutput('time', 'number', '播放时间');
  }

  public execute(inputs: any): any {
    if (inputs.entity) {
      const animationComponent = inputs.entity.getComponent('AnimationComponent') as any;
      if (animationComponent) {
        const animator = animationComponent.getAnimator();
        return {
          isPlaying: animationComponent.getIsPlaying(),
          currentClip: animationComponent.getCurrentClip(),
          time: animator ? animator.getTime() : 0
        };
      }
    }
    return {
      isPlaying: false,
      currentClip: null,
      time: 0
    };
  }
}

/**
 * 注册动画节点
 */
export function registerAnimationNodes(): void {
  NodeRegistry.register('PlayAnimation', PlayAnimationNode);
  NodeRegistry.register('StopAnimation', StopAnimationNode);
  NodeRegistry.register('SetAnimationSpeed', SetAnimationSpeedNode);
  NodeRegistry.register('GetAnimationState', GetAnimationStateNode);
}
