<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GLTF示例 - DL（Digital Learning）引擎</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      background-color: #000;
    }
    
    #canvas-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    
    canvas {
      display: block;
      width: 100%;
      height: 100%;
    }
    
    #ui {
      position: absolute;
      top: 10px;
      left: 10px;
      color: white;
      font-family: Arial, sans-serif;
      background-color: rgba(0, 0, 0, 0.5);
      padding: 10px;
      border-radius: 5px;
    }
    
    button {
      margin: 5px;
      padding: 8px 12px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    
    button:hover {
      background-color: #45a049;
    }
    
    #model-selector {
      margin: 5px;
      padding: 8px;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <div id="canvas-container">
    <canvas id="canvas"></canvas>
  </div>
  
  <div id="ui">
    <h2>GLTF模型加载示例</h2>
    <div>
      <label for="model-selector">选择模型：</label>
      <select id="model-selector">
        <option value="https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/Duck/glTF/Duck.gltf">鸭子</option>
        <option value="https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/BoxAnimated/glTF/BoxAnimated.gltf">动画盒子</option>
        <option value="https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/BrainStem/glTF/BrainStem.gltf">机器人</option>
        <option value="https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/CesiumMan/glTF/CesiumMan.gltf">人物</option>
      </select>
    </div>
    <div>
      <button id="load-btn">加载模型</button>
      <button id="export-btn">导出模型</button>
    </div>
    <div>
      <button id="play-btn">播放动画</button>
      <button id="pause-btn">暂停动画</button>
      <button id="stop-btn">停止动画</button>
    </div>
    <div id="status">状态：等待加载</div>
  </div>

  <script type="module">
    import { GLTFExample } from './GLTFExample.js';
    
    // 创建GLTF示例
    const example = new GLTFExample();
    
    // 启动示例
    example.start();
    
    // 获取UI元素
    const modelSelector = document.getElementById('model-selector');
    const loadBtn = document.getElementById('load-btn');
    const exportBtn = document.getElementById('export-btn');
    const playBtn = document.getElementById('play-btn');
    const pauseBtn = document.getElementById('pause-btn');
    const stopBtn = document.getElementById('stop-btn');
    const statusEl = document.getElementById('status');
    
    // 加载模型按钮点击事件
    loadBtn.addEventListener('click', () => {
      const url = modelSelector.value;
      example.loadModel(url);
      statusEl.textContent = '状态：正在加载模型...';
    });
    
    // 导出模型按钮点击事件
    exportBtn.addEventListener('click', () => {
      example.exportModel();
      statusEl.textContent = '状态：模型已导出';
    });
    
    // 播放动画按钮点击事件
    playBtn.addEventListener('click', () => {
      example.playAnimation();
      statusEl.textContent = '状态：动画播放中';
    });
    
    // 暂停动画按钮点击事件
    pauseBtn.addEventListener('click', () => {
      example.pauseAnimation();
      statusEl.textContent = '状态：动画已暂停';
    });
    
    // 停止动画按钮点击事件
    stopBtn.addEventListener('click', () => {
      example.stopAnimation();
      statusEl.textContent = '状态：动画已停止';
    });
    
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      example.resize();
    });
    
    // 页面关闭时销毁示例
    window.addEventListener('beforeunload', () => {
      example.dispose();
    });
  </script>
</body>
</html>
