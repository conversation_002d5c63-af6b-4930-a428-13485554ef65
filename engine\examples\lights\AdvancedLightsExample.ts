/**
 * 高级光源示例
 * 展示如何使用高级光源类型
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { World } from '../../src/core/World';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Renderer } from '../../src/rendering/Renderer';
import { Camera } from '../../src/rendering/Camera';
import { Light, LightType } from '../../src/rendering/Light';
import { Transform } from '../../src/scene/Transform';
import { RenderSystem } from '../../src/rendering/RenderSystem';
import {
  AreaLightType,
  RectAreaLightComponent,
  SphereAreaLightComponent,
  DiskAreaLightComponent,
  TubeAreaLightComponent,
  IESLight,
  IESLightType
} from '../../src/rendering/lights';

/**
 * 高级光源示例类
 */
export class AdvancedLightsExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 世界实例 */
  private world: World;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 相机实体 */
  private cameraEntity: Entity;
  
  /** 环境光实体 */
  private ambientLightEntity: Entity;
  
  /** 渲染系统 */
  private renderSystem: RenderSystem;
  
  /** 矩形区域光实体 */
  private rectAreaLightEntity: Entity;
  
  /** 球形区域光实体 */
  private sphereAreaLightEntity: Entity;
  
  /** 圆盘区域光实体 */
  private diskAreaLightEntity: Entity;
  
  /** 管状区域光实体 */
  private tubeAreaLightEntity: Entity;
  
  /** IES光源实体 */
  private iesLightEntity: Entity;
  
  /** 物体实体列表 */
  private objectEntities: Entity[] = [];
  
  /** 动画ID */
  private animationId: number = 0;
  
  /** 是否正在运行 */
  private running: boolean = false;
  
  /** 当前光源索引 */
  private currentLightIndex: number = 0;
  
  /** 光源切换计时器 */
  private lightSwitchTimer: number = 0;
  
  /** 光源切换间隔（秒） */
  private lightSwitchInterval: number = 5;
  
  /** 光源实体列表 */
  private lightEntities: Entity[] = [];
  
  /** 光源名称列表 */
  private lightNames: string[] = [
    '矩形区域光',
    '球形区域光',
    '圆盘区域光',
    '管状区域光',
    'IES光源',
    '所有光源'
  ];
  
  /**
   * 创建示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建世界
    this.world = new World();
    this.engine.setWorld(this.world);
    
    // 创建渲染器
    const renderer = new Renderer({
      width: window.innerWidth,
      height: window.innerHeight,
      antialias: true,
      shadows: true,
      shadowMapType: THREE.PCFSoftShadowMap
    });
    
    // 创建渲染系统
    this.renderSystem = new RenderSystem(renderer, {
      enableShadows: true,
      enablePostProcessing: false
    });
    this.world.addSystem(this.renderSystem);
    
    // 创建场景
    this.scene = new Scene('高级光源示例场景');
    this.world.addEntity(this.scene);
    
    // 设置活跃场景
    this.renderSystem.setActiveScene(this.scene);
    
    // 创建相机
    this.cameraEntity = new Entity('相机');
    this.cameraEntity.addComponent(new Camera({
      type: 'perspective',
      fov: 60,
      near: 0.1,
      far: 1000,
      aspect: window.innerWidth / window.innerHeight
    }));
    this.cameraEntity.addComponent(new Transform({
      position: { x: 0, y: 5, z: 15 },
      rotation: { x: -0.3, y: 0, z: 0 }
    }));
    this.world.addEntity(this.cameraEntity);
    this.scene.addEntity(this.cameraEntity);
    
    // 设置活跃相机
    this.renderSystem.setActiveCamera(this.cameraEntity.getComponent('Camera') as Camera);
    
    // 创建环境光
    this.ambientLightEntity = new Entity('环境光');
    this.ambientLightEntity.addComponent(new Light({
      type: LightType.AMBIENT,
      color: 0x404040,
      intensity: 0.2
    }));
    this.world.addEntity(this.ambientLightEntity);
    this.scene.addEntity(this.ambientLightEntity);
    
    // 创建场景内容
    this.createSceneContent();
    
    // 创建光源
    this.createLights();
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', this.handleResize.bind(this));
    
    // 添加DOM元素
    this.addDomElements();
  }
  
  /**
   * 创建场景内容
   */
  private createSceneContent(): void {
    // 创建地面
    const groundEntity = new Entity('地面');
    groundEntity.addComponent(new Transform({
      position: { x: 0, y: -0.5, z: 0 },
      scale: { x: 100, y: 1, z: 100 }
    }));
    
    // 创建地面网格
    const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
    const groundMaterial = new THREE.MeshStandardMaterial({
      color: 0x808080,
      roughness: 0.7,
      metalness: 0.1
    });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.receiveShadow = true;
    
    // 添加到实体
    groundEntity.getComponent('Transform')?.getObject3D().add(groundMesh);
    
    // 添加到场景
    this.world.addEntity(groundEntity);
    this.scene.addEntity(groundEntity);
    
    // 创建多个物体
    const objectCount = 20;
    const radius = 8;
    
    for (let i = 0; i < objectCount; i++) {
      const angle = (i / objectCount) * Math.PI * 2;
      const x = Math.cos(angle) * radius;
      const z = Math.sin(angle) * radius;
      
      const objectEntity = new Entity(`物体${i}`);
      objectEntity.addComponent(new Transform({
        position: { x, y: 1, z },
        scale: { x: 1, y: 1, z: 1 }
      }));
      
      // 创建物体网格
      let geometry;
      let material;
      
      // 随机选择几何体
      const geometryType = Math.floor(Math.random() * 3);
      switch (geometryType) {
        case 0:
          geometry = new THREE.BoxGeometry(1, 1, 1);
          break;
        case 1:
          geometry = new THREE.SphereGeometry(0.5, 32, 32);
          break;
        case 2:
          geometry = new THREE.ConeGeometry(0.5, 1, 32);
          break;
      }
      
      // 创建材质
      material = new THREE.MeshStandardMaterial({
        color: new THREE.Color(Math.random(), Math.random(), Math.random()),
        roughness: Math.random() * 0.5 + 0.25,
        metalness: Math.random() * 0.8
      });
      
      const mesh = new THREE.Mesh(geometry, material);
      mesh.castShadow = true;
      mesh.receiveShadow = true;
      
      // 添加到实体
      objectEntity.getComponent('Transform')?.getObject3D().add(mesh);
      
      // 添加到场景
      this.world.addEntity(objectEntity);
      this.scene.addEntity(objectEntity);
      
      // 添加到列表
      this.objectEntities.push(objectEntity);
    }
  }
  
  /**
   * 创建光源
   */
  private createLights(): void {
    // 创建矩形区域光
    this.rectAreaLightEntity = new Entity('矩形区域光');
    this.rectAreaLightEntity.addComponent(new Transform({
      position: { x: 0, y: 5, z: 0 },
      rotation: { x: -Math.PI / 2, y: 0, z: 0 }
    }));
    this.rectAreaLightEntity.addComponent(new RectAreaLightComponent({
      type: AreaLightType.RECT,
      color: 0xff0000,
      intensity: 5,
      width: 4,
      height: 4,
      showHelper: true,
      usePhysicalUnits: true,
      power: 100,
      efficacy: 80
    }));
    this.world.addEntity(this.rectAreaLightEntity);
    this.scene.addEntity(this.rectAreaLightEntity);
    this.lightEntities.push(this.rectAreaLightEntity);
    
    // 创建球形区域光
    this.sphereAreaLightEntity = new Entity('球形区域光');
    this.sphereAreaLightEntity.addComponent(new Transform({
      position: { x: 0, y: 5, z: 0 }
    }));
    this.sphereAreaLightEntity.addComponent(new SphereAreaLightComponent({
      type: AreaLightType.SPHERE,
      color: 0x00ff00,
      intensity: 5,
      radius: 1,
      showHelper: true,
      usePhysicalUnits: true,
      power: 100,
      efficacy: 80,
      castShadow: true
    }));
    this.world.addEntity(this.sphereAreaLightEntity);
    this.scene.addEntity(this.sphereAreaLightEntity);
    this.lightEntities.push(this.sphereAreaLightEntity);
    
    // 创建圆盘区域光
    this.diskAreaLightEntity = new Entity('圆盘区域光');
    this.diskAreaLightEntity.addComponent(new Transform({
      position: { x: 0, y: 5, z: 0 },
      rotation: { x: -Math.PI / 2, y: 0, z: 0 }
    }));
    this.diskAreaLightEntity.addComponent(new DiskAreaLightComponent({
      type: AreaLightType.DISK,
      color: 0x0000ff,
      intensity: 5,
      radius: 2,
      showHelper: true,
      usePhysicalUnits: true,
      power: 100,
      efficacy: 80,
      castShadow: true
    }));
    this.world.addEntity(this.diskAreaLightEntity);
    this.scene.addEntity(this.diskAreaLightEntity);
    this.lightEntities.push(this.diskAreaLightEntity);
    
    // 创建管状区域光
    this.tubeAreaLightEntity = new Entity('管状区域光');
    this.tubeAreaLightEntity.addComponent(new Transform({
      position: { x: 0, y: 5, z: 0 }
    }));
    this.tubeAreaLightEntity.addComponent(new TubeAreaLightComponent({
      type: AreaLightType.TUBE,
      color: 0xffff00,
      intensity: 5,
      length: 4,
      radius: 0.5,
      showHelper: true,
      usePhysicalUnits: true,
      power: 100,
      efficacy: 80,
      castShadow: true
    }));
    this.world.addEntity(this.tubeAreaLightEntity);
    this.scene.addEntity(this.tubeAreaLightEntity);
    this.lightEntities.push(this.tubeAreaLightEntity);
    
    // 创建IES光源
    this.iesLightEntity = new Entity('IES光源');
    this.iesLightEntity.addComponent(new Transform({
      position: { x: 0, y: 5, z: 0 }
    }));
    this.iesLightEntity.addComponent(new IESLight({
      type: IESLightType.SPOT,
      color: 0xffffff,
      intensity: 5,
      castShadow: true,
      distance: 0,
      decay: 2,
      // 注意：实际使用时需要提供IES文件路径或数据
      useIESTexture: false
    }));
    this.world.addEntity(this.iesLightEntity);
    this.scene.addEntity(this.iesLightEntity);
    this.lightEntities.push(this.iesLightEntity);
    
    // 初始状态：只显示第一个光源
    for (let i = 0; i < this.lightEntities.length; i++) {
      this.lightEntities[i].setActive(i === 0);
    }
  }
  
  /**
   * 添加DOM元素
   */
  private addDomElements(): void {
    // 创建信息面板
    const infoPanel = document.createElement('div');
    infoPanel.style.position = 'absolute';
    infoPanel.style.top = '10px';
    infoPanel.style.left = '10px';
    infoPanel.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    infoPanel.style.color = 'white';
    infoPanel.style.padding = '10px';
    infoPanel.style.borderRadius = '5px';
    infoPanel.style.fontFamily = 'Arial, sans-serif';
    infoPanel.innerHTML = `
      <h2>高级光源示例</h2>
      <p>这个示例展示了如何使用高级光源类型。</p>
      <p>每隔5秒会自动切换光源。</p>
      <p>当前光源: <span id="current-light">${this.lightNames[this.currentLightIndex]}</span></p>
    `;
    document.body.appendChild(infoPanel);
  }
  
  /**
   * 处理窗口大小变化
   */
  private handleResize(): void {
    // 更新相机宽高比
    const camera = this.cameraEntity.getComponent('Camera') as Camera;
    camera.setAspect(window.innerWidth / window.innerHeight);
  }
  
  /**
   * 更新场景
   * @param deltaTime 帧间隔时间（秒）
   */
  private update(deltaTime: number): void {
    // 旋转相机
    const cameraTransform = this.cameraEntity.getComponent('Transform');
    if (cameraTransform) {
      const rotation = cameraTransform.getRotation();
      rotation.y += deltaTime * 0.1;
      cameraTransform.setRotation(rotation.x, rotation.y, rotation.z);
    }
    
    // 更新光源切换计时器
    this.lightSwitchTimer += deltaTime;
    if (this.lightSwitchTimer >= this.lightSwitchInterval) {
      this.lightSwitchTimer = 0;
      this.switchLight();
    }
    
    // 更新世界
    this.world.update(deltaTime);
  }
  
  /**
   * 切换光源
   */
  private switchLight(): void {
    // 切换到下一个光源
    this.currentLightIndex = (this.currentLightIndex + 1) % this.lightNames.length;
    
    // 更新光源状态
    if (this.currentLightIndex < this.lightEntities.length) {
      // 只显示当前光源
      for (let i = 0; i < this.lightEntities.length; i++) {
        this.lightEntities[i].setActive(i === this.currentLightIndex);
      }
    } else {
      // 显示所有光源
      for (const lightEntity of this.lightEntities) {
        lightEntity.setActive(true);
      }
    }
    
    // 更新光源名称
    document.getElementById('current-light')!.textContent = this.lightNames[this.currentLightIndex];
  }
  
  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;
    
    const now = performance.now();
    const deltaTime = Math.min((now - (this._lastTime || now)) / 1000, 0.1);
    this._lastTime = now;
    
    this.update(deltaTime);
    
    this.animationId = requestAnimationFrame(this.animate.bind(this));
  }
  
  /** 上一帧时间 */
  private _lastTime: number = 0;
  
  /**
   * 启动示例
   */
  public start(): void {
    if (this.running) return;
    
    this.running = true;
    this._lastTime = performance.now();
    this.animate();
  }
  
  /**
   * 停止示例
   */
  public stop(): void {
    if (!this.running) return;
    
    this.running = false;
    cancelAnimationFrame(this.animationId);
  }
  
  /**
   * 销毁示例
   */
  public dispose(): void {
    this.stop();
    
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize.bind(this));
    
    // 销毁引擎
    this.engine.dispose();
  }
}
