/**
 * 锁定面板样式
 */
.lock-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 12px;
  overflow: hidden;

  .lock-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    h4.ant-typography {
      margin: 0;
    }
  }

  .lock-panel-stats {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;

    .ant-card {
      flex: 1;
    }
  }

  .ant-table-wrapper {
    flex: 1;
    overflow: auto;
  }

  .selected-row {
    background-color: #e6f7ff;
  }
}
