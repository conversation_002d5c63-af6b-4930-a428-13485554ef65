# 动画系统高级功能

本文档介绍了DL（Digital Learning）引擎动画系统的高级功能，包括动画实例化、GPU蒙皮、动画事件系统和角色控制器。

## 动画实例化 (Animation Instancing)

动画实例化是一种优化技术，用于在场景中高效地渲染大量相同的动画对象。通过共享动画数据和使用实例化渲染，可以显著提高性能。

### 主要特性

- 支持大量相同动画的高效渲染
- 实例化网格和骨骼
- 时间偏移和时间缩放
- 权重控制

### 使用示例

```typescript
import { AnimationInstancingSystem, AnimationInstanceComponent } from '../../animation';

// 创建动画实例化系统
const instancingSystem = new AnimationInstancingSystem(world, {
  useGPUSkinning: true,
  maxInstances: 1000,
  debug: true
});

// 添加到世界
world.addSystem(instancingSystem);

// 创建实例组
const groupId = 'soldiers';
instancingSystem.createInstanceGroup(groupId, walkClip);

// 创建多个实例
for (let i = 0; i < 100; i++) {
  // 创建实体
  const entity = world.createEntity();
  
  // 创建实例组件
  instancingSystem.createInstance(entity, groupId, {
    clip: walkClip,
    timeOffset: Math.random() * 2.0, // 随机时间偏移
    timeScale: 0.8 + Math.random() * 0.4, // 随机时间缩放
    loop: true
  });
  
  // 设置位置
  const transform = entity.getTransform();
  transform.setPosition(new THREE.Vector3(
    (Math.random() - 0.5) * 20,
    0,
    (Math.random() - 0.5) * 20
  ));
}
```

## GPU蒙皮 (GPU Skinning)

GPU蒙皮是一种将骨骼动画计算从CPU转移到GPU的技术，可以显著提高动画性能，特别是对于复杂模型和大量角色的场景。

### 主要特性

- 骨骼矩阵纹理
- 自定义着色器
- 支持计算着色器（如果可用）
- 自动降级到CPU蒙皮

### 使用示例

```typescript
import { GPUSkinningSystem, GPUSkinningComponent } from '../../animation';

// 创建GPU蒙皮系统
const gpuSkinningSystem = new GPUSkinningSystem(world, {
  maxBones: 60,
  useComputeShader: true,
  debug: true
});

// 添加到世界
world.addSystem(gpuSkinningSystem);

// 加载模型
loader.load('models/character.glb', (gltf) => {
  // 获取骨骼网格
  const skinnedMesh = gltf.scene.getObjectByName('Character') as THREE.SkinnedMesh;
  
  // 创建实体
  const entity = world.createEntity();
  
  // 创建GPU蒙皮组件
  gpuSkinningSystem.createGPUSkinning(entity, skinnedMesh);
  
  // 添加到场景
  world.getScene().add(gltf.scene);
});
```

## 动画事件系统 (Animation Event System)

动画事件系统允许在动画播放过程中的特定时间点触发事件，用于同步音效、粒子效果、游戏逻辑等。

### 主要特性

- 自定义事件
- 内置事件类型（开始、结束、循环）
- 事件参数
- 全局和局部事件监听

### 使用示例

```typescript
import { AnimationEventSystem, AnimationEventType, AnimationEventData } from '../../animation';

// 创建动画事件系统
const eventSystem = new AnimationEventSystem(world, true);

// 添加到世界
world.addSystem(eventSystem);

// 创建动画事件组件
const eventComponent = eventSystem.createAnimationEvent(entity, animator);

// 添加事件
eventSystem.addEvent(entity, 'attack', {
  type: AnimationEventType.CUSTOM,
  name: 'attack_hit',
  time: 0.5, // 动画播放到0.5秒时触发
  clipName: 'attack',
  params: { damage: 20 }
});

// 添加事件监听器
eventSystem.addEventListener(entity, 'attack_hit', (event) => {
  console.log('攻击命中:', event);
  // 播放音效
  audioSystem.playSound('hit.mp3');
  // 创建粒子效果
  particleSystem.createEffect('impact', { position: target.position });
  // 应用伤害
  target.applyDamage(event.params.damage);
});
```

## 角色控制器 (Character Controller)

角色控制器是一个高级组件，用于管理角色的动画、输入和物理，提供了一个完整的角色控制解决方案。

### 主要特性

- 状态管理（空闲、行走、跑步、跳跃等）
- 输入处理
- 物理集成
- 动画混合
- 事件处理

### 使用示例

```typescript
import { CharacterController, CharacterState } from '../../animation';

// 创建角色控制器
const controller = new CharacterController({
  walkSpeed: 2.0,
  runSpeed: 5.0,
  jumpForce: 5.0,
  gravity: 9.8,
  useStateMachine: true,
  useBlendSpace: true,
  debug: true
});

// 添加到实体
entity.addComponent(controller);

// 初始化控制器
controller.initialize(entity, skeletonAnimation, inputSystem, physicsSystem);

// 在游戏循环中更新
function update(deltaTime) {
  controller.update(deltaTime);
}

// 监听事件
eventSystem.addEventListener(entity, 'attack_hit', (event) => {
  // 处理攻击命中事件
});

// 手动触发动作
function jump() {
  controller.jump();
}

function attack() {
  controller.attack();
}
```

## 性能优化建议

1. **使用动画实例化**：对于大量相同的角色，使用动画实例化可以显著提高性能。

2. **启用GPU蒙皮**：对于复杂模型，启用GPU蒙皮可以减轻CPU负担。

3. **动画LOD**：根据距离使用不同复杂度的动画。

4. **骨骼简化**：对于远处的角色，可以减少骨骼数量。

5. **动画缓存**：缓存常用的动画计算结果。

6. **动画压缩**：减少关键帧数量和精度。

7. **批处理更新**：将动画更新分批处理，避免单帧过多计算。

## 调试工具

动画系统提供了多种调试工具，帮助开发者排查问题：

1. **骨骼可视化**：显示骨骼层次结构和变换。

2. **动画时间线**：可视化动画时间线和关键帧。

3. **事件标记**：在时间线上标记事件触发点。

4. **性能监控**：监控动画系统的性能指标。

5. **状态机可视化**：显示状态机的状态和转换。

## 未来计划

1. **动画编辑器**：可视化编辑动画和状态机。

2. **动画重定向改进**：更智能的骨骼映射和姿势调整。

3. **动画合成**：基于AI的动画合成和过渡。

4. **物理驱动动画**：更好的物理和动画集成。

5. **面部动画**：支持面部表情和口型同步。
