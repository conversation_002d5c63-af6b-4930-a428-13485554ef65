# 情感响应系统教程

本教程将介绍如何使用情感响应系统为角色添加基于情感的面部动画。情感响应系统能够根据环境和事件自动生成面部表情，使角色更加生动和富有表现力。

## 目录

1. [系统概述](#系统概述)
2. [基本用法](#基本用法)
3. [情感事件](#情感事件)
4. [情感混合](#情感混合)
5. [情感记忆](#情感记忆)
6. [情感传染](#情感传染)
7. [高级用法](#高级用法)
8. [示例](#示例)

## 系统概述

情感响应系统是一个用于处理角色对环境和事件的情感反应的系统。它能够根据情感事件自动生成面部表情，并支持情感混合、情感记忆和情感传染等高级功能。

主要组件：

- **EmotionResponseSystem**：情感响应系统的核心类，负责处理情感事件和生成情感响应。
- **EmotionBlendController**：情感混合控制器，用于控制多种情感的混合和过渡。
- **EmotionEventType**：情感事件类型枚举，定义了各种情感事件类型。
- **EmotionEventData**：情感事件数据接口，包含事件类型、强度、持续时间等信息。
- **EmotionResponseData**：情感响应数据接口，包含响应实体、表情、强度等信息。

## 基本用法

### 1. 创建情感响应系统

首先，需要创建情感响应系统并添加到世界中：

```typescript
import { World } from '../../engine/src/core/World';
import { EmotionResponseSystem } from '../../engine/src/avatar/systems/EmotionResponseSystem';

// 创建世界
const world = new World();

// 创建情感响应系统
const emotionResponseSystem = new EmotionResponseSystem(world, {
  debug: true,
  enableEmotionBlending: true,
  enableEmotionMemory: true,
  enableEmotionContagion: false
});

// 添加系统到世界
world.addSystem(emotionResponseSystem);
```

### 2. 触发情感事件

可以通过触发情感事件来生成情感响应：

```typescript
import { EmotionEventType, EmotionEventData } from '../../engine/src/avatar/systems/EmotionResponseSystem';

// 创建事件数据
const eventData: EmotionEventData = {
  type: EmotionEventType.SURPRISE,
  intensity: 0.8,
  duration: 3.0,
  description: '惊喜情感事件'
};

// 触发事件
emotionResponseSystem.triggerEmotionEvent(eventData);
```

### 3. 监听情感响应事件

可以监听情感响应事件来获取响应信息：

```typescript
// 监听情感响应事件
emotionResponseSystem.addEventListener('emotionResponse', (data) => {
  console.log(`情感响应: ${data.description}，表情: ${data.expression}，强度: ${data.intensity}`);
});

// 监听情感响应结束事件
emotionResponseSystem.addEventListener('emotionResponseEnd', (data) => {
  console.log(`情感响应结束: ${data.description}`);
});
```

## 情感事件

情感事件是情感响应系统的输入，它定义了角色应该对什么事件产生什么样的情感反应。

### 情感事件类型

情感响应系统支持以下情感事件类型：

- **STARTLE**：惊吓，突然的刺激导致的短暂惊吓反应。
- **SURPRISE**：惊喜，意外但不一定是负面的惊讶反应。
- **THREAT**：威胁，感知到危险或威胁的反应。
- **JOY**：喜悦，积极的快乐情感。
- **SADNESS**：悲伤，消极的伤心情感。
- **ANGER**：愤怒，对不公正或挫折的强烈反应。
- **FEAR**：恐惧，对危险的持续性恐惧反应。
- **DISGUST**：厌恶，对令人不快事物的排斥反应。
- **NEUTRAL**：中性，没有明显情感的状态。
- **CUSTOM**：自定义，用户定义的情感类型。

### 创建情感事件

创建情感事件需要指定事件类型、强度、持续时间等信息：

```typescript
const eventData: EmotionEventData = {
  type: EmotionEventType.JOY,
  intensity: 0.8,        // 情感强度（0-1）
  duration: 5.0,         // 持续时间（秒）
  description: '喜悦情感事件',  // 事件描述
  position: new Vector3(0, 0, 0),  // 事件位置（可选）
  range: 10.0,           // 事件范围（可选）
  source: sourceEntity   // 事件源（可选）
};
```

## 情感混合

情感混合允许多种情感同时影响角色的表情，使表情更加自然和丰富。

### 启用情感混合

在创建情感响应系统时启用情感混合：

```typescript
const emotionResponseSystem = new EmotionResponseSystem(world, {
  enableEmotionBlending: true
});
```

### 情感混合模式

情感混合控制器支持以下混合模式：

- **override**：覆盖模式，只显示优先级最高的表情。
- **add**：加法模式，将所有活跃表情叠加显示。
- **multiply**：乘法模式，将所有活跃表情权重相乘后显示。
- **weighted**：加权模式，根据权重混合所有活跃表情（默认模式）。

### 手动控制情感混合

可以通过情感混合控制器手动控制表情混合：

```typescript
// 获取情感混合控制器
const blendController = emotionResponseSystem.getEmotionBlendController(entity);

// 添加表情
blendController.addExpression(
  FacialExpressionType.HAPPY,  // 表情类型
  0.7,                         // 权重
  3.0,                         // 持续时间（秒）
  0.3,                         // 过渡时间（秒）
  1                            // 优先级
);

// 添加另一个表情
blendController.addExpression(
  FacialExpressionType.SURPRISED,
  0.3,
  2.0,
  0.2,
  0
);
```

## 情感记忆

情感记忆允许角色记住过去的情感事件，并影响未来的情感响应。

### 启用情感记忆

在创建情感响应系统时启用情感记忆：

```typescript
const emotionResponseSystem = new EmotionResponseSystem(world, {
  enableEmotionMemory: true,
  emotionMemoryDuration: 60.0  // 情感记忆持续时间（秒）
});
```

### 情感记忆效果

情感记忆会影响角色对同类型事件的响应强度：

- 如果角色最近经历过同类型的情感事件，对新事件的响应强度会增强。
- 情感记忆会随时间衰减，直到完全消失。

### 管理情感记忆

可以获取和清除角色的情感记忆：

```typescript
// 获取情感记忆
const memories = emotionResponseSystem.getEmotionMemories(entity);
console.log('情感记忆:', memories);

// 清除情感记忆
emotionResponseSystem.clearEmotionMemories(entity);
```

## 情感传染

情感传染允许角色的情感影响周围的其他角色，模拟情感在群体中的传播。

### 启用情感传染

在创建情感响应系统时启用情感传染：

```typescript
const emotionResponseSystem = new EmotionResponseSystem(world, {
  enableEmotionContagion: true,
  emotionContagionRange: 5.0,       // 情感传染范围
  emotionContagionStrength: 0.3     // 情感传染强度
});
```

### 情感传染效果

当一个角色产生情感响应时，会影响范围内的其他角色：

- 传染强度会随距离衰减。
- 传染的情感持续时间较短。
- 传染只会发生在活跃的情感响应之间。

## 高级用法

### 微表情

微表情是短暂的、低强度的表情变化，可以使角色的表情更加生动：

```typescript
// 启用微表情
const emotionResponseSystem = new EmotionResponseSystem(world, {
  enableEmotionBlending: true
});

// 获取情感混合控制器
const blendController = emotionResponseSystem.getEmotionBlendController(entity);

// 配置微表情
blendController.config.enableMicroExpressions = true;
blendController.config.microExpressionFrequency = 4.0;  // 每分钟次数
blendController.config.microExpressionIntensity = 0.3;  // 强度
blendController.config.microExpressionDuration = 0.2;   // 持续时间（秒）
```

### 自然变化

自然变化是表情强度的随机波动，可以使表情看起来更加自然：

```typescript
// 启用自然变化
const blendController = emotionResponseSystem.getEmotionBlendController(entity);
blendController.config.enableNaturalVariation = true;
blendController.config.naturalVariationAmount = 0.1;    // 变化幅度
blendController.config.naturalVariationFrequency = 0.5; // 变化频率（每秒）
```

## 示例

### 基本示例

```typescript
// 创建情感响应系统
const emotionResponseSystem = new EmotionResponseSystem(world, {
  debug: true,
  enableEmotionBlending: true,
  enableEmotionMemory: true
});

// 添加系统到世界
world.addSystem(emotionResponseSystem);

// 监听情感响应事件
emotionResponseSystem.addEventListener('emotionResponse', (data) => {
  console.log(`情感响应: ${data.description}，表情: ${data.expression}，强度: ${data.intensity}`);
});

// 触发惊喜事件
emotionResponseSystem.triggerEmotionEvent({
  type: EmotionEventType.SURPRISE,
  intensity: 0.8,
  duration: 3.0,
  description: '惊喜情感事件'
});

// 触发喜悦事件
setTimeout(() => {
  emotionResponseSystem.triggerEmotionEvent({
    type: EmotionEventType.JOY,
    intensity: 0.7,
    duration: 4.0,
    description: '喜悦情感事件'
  });
}, 4000);
```

### 完整示例

请查看 `newsystem/examples/emotion-response/index.html` 和 `newsystem/engine/examples/avatar/EmotionResponseExample.ts` 获取完整示例。

## 总结

情感响应系统为角色添加了基于情感的面部动画，使角色更加生动和富有表现力。通过情感混合、情感记忆和情感传染等高级功能，可以创建更加自然和丰富的角色表情。
