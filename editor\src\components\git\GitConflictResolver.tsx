/**
 * Git冲突解决工具组件
 * 用于解决Git合并冲突
 */
import React, { useState, useEffect } from 'react';
import { Card, Button, Tabs, Space, Tooltip, Radio, Divider, message, Modal } from 'antd';
import {
  ExclamationCircleOutlined,
  CheckOutlined,
  CloseOutlined,
  MergeCellsOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
  UndoOutlined,
  SaveOutlined,
  DiffOutlined,
  FileOutlined,
  EditOutlined,
  CodeOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { gitService, GitConflict } from '../../services/GitService';
import { setShowConflictPanel, setMergeConflicts } from '../../store/git/gitSlice';
import './GitConflictResolver.less';

const { TabPane } = Tabs;

/**
 * Git冲突解决工具组件
 */
const GitConflictResolver: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { mergeConflicts, isMerging } = useSelector((state: RootState) => state.git);
  
  const [currentConflictIndex, setCurrentConflictIndex] = useState<number>(0);
  const [resolutionChoice, setResolutionChoice] = useState<'ours' | 'theirs' | 'both' | 'custom'>('both');
  const [customContent, setCustomContent] = useState<string>('');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isConfirmModalVisible, setIsConfirmModalVisible] = useState<boolean>(false);

  // 当前冲突
  const currentConflict = mergeConflicts[currentConflictIndex];

  // 初始化
  useEffect(() => {
    if (currentConflict) {
      setCustomContent(currentConflict.content);
    }
  }, [currentConflictIndex, mergeConflicts]);

  // 处理关闭面板
  const handleClose = () => {
    if (mergeConflicts.length > 0) {
      setIsConfirmModalVisible(true);
    } else {
      dispatch(setShowConflictPanel(false));
    }
  };

  // 处理确认关闭
  const handleConfirmClose = () => {
    dispatch(setShowConflictPanel(false));
    setIsConfirmModalVisible(false);
  };

  // 处理取消关闭
  const handleCancelClose = () => {
    setIsConfirmModalVisible(false);
  };

  // 处理上一个冲突
  const handlePrevConflict = () => {
    if (currentConflictIndex > 0) {
      setCurrentConflictIndex(currentConflictIndex - 1);
      setResolutionChoice('both');
      setIsEditing(false);
    }
  };

  // 处理下一个冲突
  const handleNextConflict = () => {
    if (currentConflictIndex < mergeConflicts.length - 1) {
      setCurrentConflictIndex(currentConflictIndex + 1);
      setResolutionChoice('both');
      setIsEditing(false);
    }
  };

  // 处理解决冲突
  const handleResolveConflict = () => {
    if (!currentConflict) return;

    let resolvedContent = '';
    
    switch (resolutionChoice) {
      case 'ours':
        resolvedContent = currentConflict.ourContent;
        break;
      case 'theirs':
        resolvedContent = currentConflict.theirContent;
        break;
      case 'both':
        resolvedContent = `${currentConflict.ourContent}\n${currentConflict.theirContent}`;
        break;
      case 'custom':
        resolvedContent = customContent;
        break;
    }

    // 这里应该调用gitService的resolveConflict方法
    // gitService.resolveConflict(currentConflict.path, resolvedContent);

    // 更新冲突列表
    const updatedConflicts = [...mergeConflicts];
    updatedConflicts.splice(currentConflictIndex, 1);
    dispatch(setMergeConflicts(updatedConflicts));

    // 如果没有更多冲突，则完成合并
    if (updatedConflicts.length === 0) {
      message.success(t('git.allConflictsResolved'));
      dispatch(setShowConflictPanel(false));
    } else {
      // 调整当前索引
      if (currentConflictIndex >= updatedConflicts.length) {
        setCurrentConflictIndex(updatedConflicts.length - 1);
      }
      message.success(t('git.conflictResolved'));
    }
  };

  // 处理解决所有冲突
  const handleResolveAllConflicts = () => {
    Modal.confirm({
      title: t('git.resolveAllConfirmTitle'),
      content: t('git.resolveAllConfirmContent', { choice: t(`git.${resolutionChoice}`) }),
      okText: t('git.resolveAll'),
      cancelText: t('common.cancel'),
      onOk: () => {
        // 这里应该调用gitService的resolveAllConflicts方法
        // gitService.resolveAllConflicts(resolutionChoice);
        
        message.success(t('git.allConflictsResolved'));
        dispatch(setMergeConflicts([]));
        dispatch(setShowConflictPanel(false));
      },
    });
  };

  // 处理编辑自定义内容
  const handleEditCustomContent = () => {
    setIsEditing(true);
    setResolutionChoice('custom');
  };

  // 处理保存自定义内容
  const handleSaveCustomContent = () => {
    setIsEditing(false);
  };

  // 渲染冲突内容
  const renderConflictContent = () => {
    if (!currentConflict) {
      return (
        <div className="git-conflict-empty">
          <p>{t('git.noConflicts')}</p>
        </div>
      );
    }

    return (
      <div className="git-conflict-content">
        <div className="git-conflict-file">
          <FileOutlined />
          <span className="git-conflict-file-path">{currentConflict.path}</span>
        </div>

        <Tabs defaultActiveKey="comparison">
          <TabPane
            tab={
              <span>
                <DiffOutlined />
                {t('git.comparison')}
              </span>
            }
            key="comparison"
          >
            <div className="git-conflict-comparison">
              <div className="git-conflict-side">
                <div className="git-conflict-side-header">
                  <h4>{t('git.ourChanges')}</h4>
                </div>
                <pre className="git-conflict-code">{currentConflict.ourContent}</pre>
              </div>
              <div className="git-conflict-side">
                <div className="git-conflict-side-header">
                  <h4>{t('git.theirChanges')}</h4>
                </div>
                <pre className="git-conflict-code">{currentConflict.theirContent}</pre>
              </div>
            </div>
          </TabPane>
          <TabPane
            tab={
              <span>
                <CodeOutlined />
                {t('git.editor')}
              </span>
            }
            key="editor"
          >
            <div className="git-conflict-editor">
              {isEditing ? (
                <>
                  <textarea
                    className="git-conflict-textarea"
                    value={customContent}
                    onChange={(e) => setCustomContent(e.target.value)}
                  />
                  <div className="git-conflict-editor-actions">
                    <Button
                      icon={<SaveOutlined />}
                      onClick={handleSaveCustomContent}
                    >
                      {t('git.save')}
                    </Button>
                  </div>
                </>
              ) : (
                <>
                  <pre className="git-conflict-code">{
                    resolutionChoice === 'ours' ? currentConflict.ourContent :
                    resolutionChoice === 'theirs' ? currentConflict.theirContent :
                    resolutionChoice === 'both' ? `${currentConflict.ourContent}\n${currentConflict.theirContent}` :
                    customContent
                  }</pre>
                  <div className="git-conflict-editor-actions">
                    <Button
                      icon={<EditOutlined />}
                      onClick={handleEditCustomContent}
                    >
                      {t('git.edit')}
                    </Button>
                  </div>
                </>
              )}
            </div>
          </TabPane>
        </Tabs>

        <div className="git-conflict-resolution">
          <h4>{t('git.resolveConflict')}</h4>
          <Radio.Group
            value={resolutionChoice}
            onChange={(e) => setResolutionChoice(e.target.value)}
          >
            <Radio value="ours">{t('git.useOurs')}</Radio>
            <Radio value="theirs">{t('git.useTheirs')}</Radio>
            <Radio value="both">{t('git.useBoth')}</Radio>
            <Radio value="custom">{t('git.useCustom')}</Radio>
          </Radio.Group>
        </div>
      </div>
    );
  };

  return (
    <div className="git-conflict-resolver">
      <Card
        title={
          <Space>
            <ExclamationCircleOutlined style={{ color: '#faad14' }} />
            <span>{t('git.resolveConflicts')}</span>
            <span className="git-conflict-count">
              ({currentConflictIndex + 1}/{mergeConflicts.length})
            </span>
          </Space>
        }
        extra={
          <Button
            icon={<CloseOutlined />}
            onClick={handleClose}
          />
        }
        className="git-conflict-card"
      >
        {renderConflictContent()}

        <div className="git-conflict-actions">
          <Space>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={handlePrevConflict}
              disabled={currentConflictIndex === 0 || mergeConflicts.length === 0}
            >
              {t('git.previous')}
            </Button>
            <Button
              icon={<ArrowRightOutlined />}
              onClick={handleNextConflict}
              disabled={currentConflictIndex === mergeConflicts.length - 1 || mergeConflicts.length === 0}
            >
              {t('git.next')}
            </Button>
            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={handleResolveConflict}
              disabled={mergeConflicts.length === 0}
            >
              {t('git.resolve')}
            </Button>
            <Button
              icon={<MergeCellsOutlined />}
              onClick={handleResolveAllConflicts}
              disabled={mergeConflicts.length === 0}
            >
              {t('git.resolveAll')}
            </Button>
          </Space>
        </div>
      </Card>

      <Modal
        title={t('git.confirmCloseTitle')}
        open={isConfirmModalVisible}
        onOk={handleConfirmClose}
        onCancel={handleCancelClose}
        okText={t('git.confirmClose')}
        cancelText={t('common.cancel')}
      >
        <p>{t('git.confirmCloseContent')}</p>
      </Modal>
    </div>
  );
};

export default GitConflictResolver;
