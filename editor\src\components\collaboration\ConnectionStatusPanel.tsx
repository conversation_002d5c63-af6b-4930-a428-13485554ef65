/**
 * 连接状态面板组件
 * 用于显示WebSocket连接的状态和统计信息
 */
import React, { useEffect, useState } from 'react';
import { Card, Descriptions, Badge, Progress, Button, Tooltip, Space, Statistic, Row, Col, Divider } from 'antd';
import { ReloadOutlined, DisconnectOutlined, SettingOutlined, LineChartOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { ConnectionStatus, ConnectionStats } from '../../services/WebSocketConnectionManager';
import { collaborationService } from '../../services/CollaborationService';
import { formatBytes, formatDuration, formatNumber } from '../../utils/formatters';

// 状态颜色映射
const statusColorMap: Record<ConnectionStatus, string> = {
  [ConnectionStatus.CONNECTED]: 'success',
  [ConnectionStatus.CONNECTING]: 'processing',
  [ConnectionStatus.DISCONNECTED]: 'default',
  [ConnectionStatus.RECONNECTING]: 'warning',
  [ConnectionStatus.ERROR]: 'error'
};

// 状态文本映射
const statusTextMap: Record<ConnectionStatus, string> = {
  [ConnectionStatus.CONNECTED]: '已连接',
  [ConnectionStatus.CONNECTING]: '连接中',
  [ConnectionStatus.DISCONNECTED]: '已断开',
  [ConnectionStatus.RECONNECTING]: '重连中',
  [ConnectionStatus.ERROR]: '连接错误'
};

/**
 * 连接状态面板组件
 */
const ConnectionStatusPanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  // 从Redux获取连接状态
  const connectionStatus = useSelector((state: RootState) => state.connection.status);
  
  // 本地状态
  const [stats, setStats] = useState<ConnectionStats | null>(null);
  const [expanded, setExpanded] = useState<boolean>(false);
  const [refreshKey, setRefreshKey] = useState<number>(0);
  
  // 定时刷新统计信息
  useEffect(() => {
    const updateStats = () => {
      if (collaborationService.connectionManager) {
        setStats(collaborationService.connectionManager.getStats());
      }
    };
    
    // 初始更新
    updateStats();
    
    // 设置定时器
    const timerId = setInterval(updateStats, 1000);
    
    return () => {
      clearInterval(timerId);
    };
  }, [refreshKey]);
  
  // 监听连接状态变化
  useEffect(() => {
    if (collaborationService.connectionManager) {
      const handleStatusChange = () => {
        setRefreshKey(prev => prev + 1);
      };
      
      collaborationService.connectionManager.on('statusChange', handleStatusChange);
      collaborationService.connectionManager.on('connected', handleStatusChange);
      collaborationService.connectionManager.on('disconnected', handleStatusChange);
      collaborationService.connectionManager.on('error', handleStatusChange);
      
      return () => {
        collaborationService.connectionManager.off('statusChange', handleStatusChange);
        collaborationService.connectionManager.off('connected', handleStatusChange);
        collaborationService.connectionManager.off('disconnected', handleStatusChange);
        collaborationService.connectionManager.off('error', handleStatusChange);
      };
    }
  }, []);
  
  // 处理重新连接
  const handleReconnect = () => {
    if (collaborationService.connectionManager) {
      if (connectionStatus === ConnectionStatus.CONNECTED) {
        collaborationService.connectionManager.disconnect();
      }
      collaborationService.connectionManager.connect();
    }
  };
  
  // 处理断开连接
  const handleDisconnect = () => {
    if (collaborationService.connectionManager) {
      collaborationService.connectionManager.disconnect();
    }
  };
  
  // 处理展开/折叠
  const handleToggleExpand = () => {
    setExpanded(!expanded);
  };
  
  // 渲染连接状态徽章
  const renderStatusBadge = () => {
    const color = statusColorMap[connectionStatus] || 'default';
    const text = statusTextMap[connectionStatus] || '未知状态';
    
    return <Badge status={color as any} text={text} />;
  };
  
  // 渲染连接质量
  const renderConnectionQuality = () => {
    if (!stats || connectionStatus !== ConnectionStatus.CONNECTED) {
      return <Progress percent={0} size="small" status="exception" />;
    }
    
    // 根据延迟计算连接质量
    let quality = 100;
    
    if (stats.lastLatency > 0) {
      if (stats.lastLatency < 100) {
        quality = 100;
      } else if (stats.lastLatency < 200) {
        quality = 80;
      } else if (stats.lastLatency < 500) {
        quality = 60;
      } else if (stats.lastLatency < 1000) {
        quality = 40;
      } else {
        quality = 20;
      }
    }
    
    let status: 'success' | 'normal' | 'exception' | 'active' = 'normal';
    
    if (quality >= 80) {
      status = 'success';
    } else if (quality < 40) {
      status = 'exception';
    }
    
    return <Progress percent={quality} size="small" status={status} />;
  };
  
  // 渲染基本信息
  const renderBasicInfo = () => {
    return (
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Statistic 
            title={t('collaboration.connection.status')} 
            value={statusTextMap[connectionStatus]} 
            valueStyle={{ color: connectionStatus === ConnectionStatus.CONNECTED ? '#3f8600' : '#cf1322' }}
          />
        </Col>
        <Col span={12}>
          <Statistic 
            title={t('collaboration.connection.latency')} 
            value={stats?.lastLatency || 0} 
            suffix="ms"
            valueStyle={{ color: (stats?.lastLatency || 0) < 200 ? '#3f8600' : '#cf1322' }}
          />
        </Col>
      </Row>
    );
  };
  
  // 渲染详细统计信息
  const renderDetailedStats = () => {
    if (!stats) {
      return null;
    }
    
    return (
      <>
        <Divider orientation="left">{t('collaboration.connection.statistics')}</Divider>
        <Descriptions size="small" column={2} bordered>
          <Descriptions.Item label={t('collaboration.connection.messagesReceived')}>
            {formatNumber(stats.messagesReceived)}
          </Descriptions.Item>
          <Descriptions.Item label={t('collaboration.connection.messagesSent')}>
            {formatNumber(stats.messagesSent)}
          </Descriptions.Item>
          <Descriptions.Item label={t('collaboration.connection.bytesReceived')}>
            {formatBytes(stats.bytesReceived)}
          </Descriptions.Item>
          <Descriptions.Item label={t('collaboration.connection.bytesSent')}>
            {formatBytes(stats.bytesSent)}
          </Descriptions.Item>
          <Descriptions.Item label={t('collaboration.connection.averageLatency')}>
            {formatNumber(stats.averageLatency)} ms
          </Descriptions.Item>
          <Descriptions.Item label={t('collaboration.connection.maxLatency')}>
            {formatNumber(stats.maxLatency)} ms
          </Descriptions.Item>
          <Descriptions.Item label={t('collaboration.connection.connectionDuration')}>
            {formatDuration(stats.connectionDuration)}
          </Descriptions.Item>
          <Descriptions.Item label={t('collaboration.connection.reconnectAttempts')}>
            {formatNumber(stats.reconnectAttempts)}
          </Descriptions.Item>
        </Descriptions>
      </>
    );
  };
  
  return (
    <Card 
      title={t('collaboration.connection.title')}
      extra={
        <Space>
          <Tooltip title={t('collaboration.connection.reconnect')}>
            <Button 
              icon={<ReloadOutlined />} 
              size="small" 
              onClick={handleReconnect}
              disabled={connectionStatus === ConnectionStatus.CONNECTING || connectionStatus === ConnectionStatus.RECONNECTING}
            />
          </Tooltip>
          <Tooltip title={t('collaboration.connection.disconnect')}>
            <Button 
              icon={<DisconnectOutlined />} 
              size="small" 
              onClick={handleDisconnect}
              disabled={connectionStatus !== ConnectionStatus.CONNECTED}
            />
          </Tooltip>
          <Tooltip title={expanded ? t('collaboration.connection.collapse') : t('collaboration.connection.expand')}>
            <Button 
              icon={<InfoCircleOutlined />} 
              size="small" 
              onClick={handleToggleExpand}
            />
          </Tooltip>
        </Space>
      }
      size="small"
      className="connection-status-panel"
    >
      {renderBasicInfo()}
      
      <Divider orientation="left">{t('collaboration.connection.quality')}</Divider>
      {renderConnectionQuality()}
      
      {expanded && renderDetailedStats()}
    </Card>
  );
};

export default ConnectionStatusPanel;
