/**
 * 测试工具函数
 */
import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { store } from '../../store';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../i18n';

/**
 * 自定义渲染函数，包含Redux Provider、Router和Antd配置
 * @param ui 要渲染的组件
 * @param options 渲染选项
 * @returns 渲染结果
 */
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => {
  const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
    return (
      <Provider store={store}>
        <BrowserRouter>
          <I18nextProvider i18n={i18n}>
            <ConfigProvider locale={zhCN}>
              {children}
            </ConfigProvider>
          </I18nextProvider>
        </BrowserRouter>
      </Provider>
    );
  };
  
  return render(ui, { wrapper: AllTheProviders, ...options });
};

// 重新导出testing-library的所有内容
export * from '@testing-library/react';

// 覆盖render方法
export { customRender as render };
