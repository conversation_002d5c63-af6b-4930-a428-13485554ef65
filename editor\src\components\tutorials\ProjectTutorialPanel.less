/**
 * 项目案例教程面板样式
 */
.project-tutorial-panel {
  padding: 0 10px;

  .project-panel-header {
    margin-bottom: 20px;

    .project-panel-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 15px;
      flex-wrap: wrap;
      gap: 10px;

      .search-filter-group {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        flex: 1;

        .search-input {
          min-width: 200px;
          max-width: 300px;
        }

        .category-select,
        .difficulty-select,
        .sort-select {
          min-width: 150px;
        }
      }

      .view-mode-group {
        display: flex;
        gap: 5px;
      }
    }
  }

  .project-card {
    height: 100%;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .project-cover {
      position: relative;
      height: 160px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s;
      }

      &:hover img {
        transform: scale(1.05);
      }

      .project-viewed-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #1890ff;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 16px;
      }
    }

    .project-title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .popular-icon {
        color: #ff4d4f;
        font-size: 16px;
        margin-left: 8px;
      }
    }

    .project-description {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .project-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-top: 5px;
    }

    .project-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-top: 8px;
      align-items: center;

      .project-tag {
        margin-right: 0;
      }
    }

    .favorited {
      color: #faad14;
    }
  }

  .project-list-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .project-list-thumbnail {
      width: 120px;
      height: 120px;
      flex-shrink: 0;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 4px;
      }

      .project-list-thumbnail-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f0f0f0;
        border-radius: 4px;
        font-size: 32px;
        color: #bfbfbf;
      }

      .list-badge {
        top: 5px;
        right: 5px;
        width: 24px;
        height: 24px;
        font-size: 12px;
      }
    }

    .project-list-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;
      overflow: hidden;

      .project-list-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        flex-wrap: wrap;
        gap: 10px;

        .project-list-title {
          margin: 0;
          display: flex;
          align-items: center;
          gap: 8px;

          .popular-icon {
            color: #ff4d4f;
            font-size: 16px;
          }
        }

        .project-list-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 5px;
        }
      }

      .project-list-description {
        margin: 0;
        color: rgba(0, 0, 0, 0.65);
      }

      .project-list-meta-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        align-items: center;
        margin-top: auto;
      }
    }

    .project-list-actions {
      display: flex;
      flex-direction: column;
      gap: 10px;
      justify-content: center;

      .favorited {
        color: #faad14;
      }
    }
  }

  .import-project-info {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;

    .import-project-details {
      display: flex;
      gap: 15px;
      margin-top: 10px;

      .import-project-thumbnail {
        width: 80px;
        height: 80px;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 4px;
        }

        .import-project-thumbnail-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f0f0f0;
          border-radius: 4px;
          font-size: 24px;
          color: #bfbfbf;
        }
      }

      .import-project-meta {
        display: flex;
        flex-direction: column;
        gap: 5px;

        .import-project-tags {
          display: flex;
          gap: 5px;
          margin-top: 5px;
        }
      }
    }
  }

  .project-skeleton {
    padding: 20px 0;
  }
}
