/**
 * 高级输入系统示例
 * 演示输入系统的高级用法，包括组合输入、条件输入和上下文切换
 */
import {
  Engine,
  World,
  Scene,
  Camera,
  Renderer,
  InputSystem,
  InputManager,
  InputDevice,
  InputAction,
  InputBinding,
  InputMapping,
  InputComponent,
  ButtonInputAction,
  ValueInputAction,
  VectorInputAction,
  ButtonInputMapping,
  AxisInputMapping,
  VectorInputMapping,
  CompositeInputBinding,
  CompositeBindingType,
  KeyboardDevice,
  MouseDevice,
  GamepadDevice,
  TouchDevice,
  InputMappingType,
  InputActionType
} from '../src';

// 创建引擎
const engine = new Engine();

// 创建世界
const world = new World();
engine.setWorld(world);

// 创建输入系统
const inputSystem = new InputSystem();
world.addSystem(inputSystem);

// 创建输入管理器
const inputManager = InputManager.getInstance();
inputManager.initialize();

// 创建渲染系统
const renderer = new Renderer({
  width: window.innerWidth,
  height: window.innerHeight,
  antialias: true
});
document.body.appendChild(renderer.getDomElement());

// 创建场景
const scene = new Scene();
world.addEntity(scene);

// 创建相机
const camera = new Camera({
  type: 'perspective',
  fov: 75,
  aspect: window.innerWidth / window.innerHeight,
  near: 0.1,
  far: 1000
});
camera.position.set(0, 5, 10);
camera.lookAt(0, 0, 0);
world.addEntity(camera);

// 创建玩家实体
const player = world.createEntity();
player.name = '玩家';

// 定义输入上下文
enum InputContext {
  GAMEPLAY = 'gameplay',
  MENU = 'menu',
  DIALOG = 'dialog'
}

// 当前输入上下文
let currentContext: InputContext = InputContext.GAMEPLAY;

// 创建输入动作
// 游戏玩法动作
const moveAction = new VectorInputAction('move');
const jumpAction = new ButtonInputAction('jump');
const fireAction = new ButtonInputAction('fire');
const aimAction = new ValueInputAction('aim');
const runAction = new ButtonInputAction('run');
const crouchAction = new ButtonInputAction('crouch');
const reloadAction = new ButtonInputAction('reload');
const switchWeaponAction = new ValueInputAction('switchWeapon');
const useAction = new ButtonInputAction('use');
const pauseAction = new ButtonInputAction('pause');

// 菜单动作
const menuUpAction = new ButtonInputAction('menuUp');
const menuDownAction = new ButtonInputAction('menuDown');
const menuLeftAction = new ButtonInputAction('menuLeft');
const menuRightAction = new ButtonInputAction('menuRight');
const menuSelectAction = new ButtonInputAction('menuSelect');
const menuBackAction = new ButtonInputAction('menuBack');

// 对话动作
const dialogNextAction = new ButtonInputAction('dialogNext');
const dialogSkipAction = new ButtonInputAction('dialogSkip');
const dialogChoiceAction = new ValueInputAction('dialogChoice');

// 创建输入映射
// 键盘映射
const keyboardMoveMapping = new VectorInputMapping(
  'keyboardMove',
  'keyboard',
  'KeyD', // 右
  'KeyW', // 上
  1,
  0.1
);

const keyboardJumpMapping = new ButtonInputMapping(
  'keyboardJump',
  'keyboard',
  'Space'
);

const keyboardFireMapping = new ButtonInputMapping(
  'keyboardFire',
  'keyboard',
  'KeyF'
);

const keyboardRunMapping = new ButtonInputMapping(
  'keyboardRun',
  'keyboard',
  'ShiftLeft'
);

const keyboardCrouchMapping = new ButtonInputMapping(
  'keyboardCrouch',
  'keyboard',
  'ControlLeft'
);

const keyboardReloadMapping = new ButtonInputMapping(
  'keyboardReload',
  'keyboard',
  'KeyR'
);

const keyboardUseMapping = new ButtonInputMapping(
  'keyboardUse',
  'keyboard',
  'KeyE'
);

const keyboardPauseMapping = new ButtonInputMapping(
  'keyboardPause',
  'keyboard',
  'Escape'
);

const keyboardMenuUpMapping = new ButtonInputMapping(
  'keyboardMenuUp',
  'keyboard',
  'ArrowUp'
);

const keyboardMenuDownMapping = new ButtonInputMapping(
  'keyboardMenuDown',
  'keyboard',
  'ArrowDown'
);

const keyboardMenuLeftMapping = new ButtonInputMapping(
  'keyboardMenuLeft',
  'keyboard',
  'ArrowLeft'
);

const keyboardMenuRightMapping = new ButtonInputMapping(
  'keyboardMenuRight',
  'keyboard',
  'ArrowRight'
);

const keyboardMenuSelectMapping = new ButtonInputMapping(
  'keyboardMenuSelect',
  'keyboard',
  'Enter'
);

const keyboardMenuBackMapping = new ButtonInputMapping(
  'keyboardMenuBack',
  'keyboard',
  'Escape'
);

const keyboardDialogNextMapping = new ButtonInputMapping(
  'keyboardDialogNext',
  'keyboard',
  'Space'
);

const keyboardDialogSkipMapping = new ButtonInputMapping(
  'keyboardDialogSkip',
  'keyboard',
  'Escape'
);

// 鼠标映射
const mouseFireMapping = new ButtonInputMapping(
  'mouseFire',
  'mouse',
  'button:0' // 左键
);

const mouseAimMapping = new AxisInputMapping(
  'mouseAim',
  'mouse',
  'wheel:delta',
  0.01
);

const mouseSwitchWeaponMapping = new AxisInputMapping(
  'mouseSwitchWeapon',
  'mouse',
  'wheel:delta',
  0.01
);

// 游戏手柄映射
const gamepadMoveMapping = new VectorInputMapping(
  'gamepadMove',
  'gamepad',
  '0:axis:0', // 左摇杆X
  '0:axis:1', // 左摇杆Y
  1,
  0.1
);

const gamepadJumpMapping = new ButtonInputMapping(
  'gamepadJump',
  'gamepad',
  '0:button:0' // A按钮
);

const gamepadFireMapping = new ButtonInputMapping(
  'gamepadFire',
  'gamepad',
  '0:button:7' // 右扳机
);

const gamepadRunMapping = new ButtonInputMapping(
  'gamepadRun',
  'gamepad',
  '0:button:8' // 左扳机
);

const gamepadCrouchMapping = new ButtonInputMapping(
  'gamepadCrouch',
  'gamepad',
  '0:button:1' // B按钮
);

const gamepadReloadMapping = new ButtonInputMapping(
  'gamepadReload',
  'gamepad',
  '0:button:2' // X按钮
);

const gamepadUseMapping = new ButtonInputMapping(
  'gamepadUse',
  'gamepad',
  '0:button:3' // Y按钮
);

const gamepadPauseMapping = new ButtonInputMapping(
  'gamepadPause',
  'gamepad',
  '0:button:9' // 开始按钮
);

// 创建输入绑定
// 游戏玩法绑定
const moveBinding = new CompositeInputBinding(
  'move',
  ['keyboardMove', 'gamepadMove'],
  CompositeBindingType.PRIORITY
);

const jumpBinding = new CompositeInputBinding(
  'jump',
  ['keyboardJump', 'gamepadJump'],
  CompositeBindingType.ANY
);

const fireBinding = new CompositeInputBinding(
  'fire',
  ['keyboardFire', 'mouseFire', 'gamepadFire'],
  CompositeBindingType.ANY
);

const aimBinding = new InputBinding('aim', 'mouseAim');

const runBinding = new CompositeInputBinding(
  'run',
  ['keyboardRun', 'gamepadRun'],
  CompositeBindingType.ANY
);

const crouchBinding = new CompositeInputBinding(
  'crouch',
  ['keyboardCrouch', 'gamepadCrouch'],
  CompositeBindingType.ANY
);

const reloadBinding = new CompositeInputBinding(
  'reload',
  ['keyboardReload', 'gamepadReload'],
  CompositeBindingType.ANY
);

const switchWeaponBinding = new InputBinding('switchWeapon', 'mouseSwitchWeapon');

const useBinding = new CompositeInputBinding(
  'use',
  ['keyboardUse', 'gamepadUse'],
  CompositeBindingType.ANY
);

const pauseBinding = new CompositeInputBinding(
  'pause',
  ['keyboardPause', 'gamepadPause'],
  CompositeBindingType.ANY
);

// 菜单绑定
const menuUpBinding = new InputBinding('menuUp', 'keyboardMenuUp');
const menuDownBinding = new InputBinding('menuDown', 'keyboardMenuDown');
const menuLeftBinding = new InputBinding('menuLeft', 'keyboardMenuLeft');
const menuRightBinding = new InputBinding('menuRight', 'keyboardMenuRight');
const menuSelectBinding = new InputBinding('menuSelect', 'keyboardMenuSelect');
const menuBackBinding = new InputBinding('menuBack', 'keyboardMenuBack');

// 对话绑定
const dialogNextBinding = new InputBinding('dialogNext', 'keyboardDialogNext');
const dialogSkipBinding = new InputBinding('dialogSkip', 'keyboardDialogSkip');

// 创建输入组件
const inputComponent = new InputComponent(player, {
  actions: [
    moveAction, jumpAction, fireAction, aimAction, runAction, crouchAction,
    reloadAction, switchWeaponAction, useAction, pauseAction,
    menuUpAction, menuDownAction, menuLeftAction, menuRightAction,
    menuSelectAction, menuBackAction,
    dialogNextAction, dialogSkipAction, dialogChoiceAction
  ],
  bindings: [
    moveBinding, jumpBinding, fireBinding, aimBinding, runBinding, crouchBinding,
    reloadBinding, switchWeaponBinding, useBinding, pauseBinding,
    menuUpBinding, menuDownBinding, menuLeftBinding, menuRightBinding,
    menuSelectBinding, menuBackBinding,
    dialogNextBinding, dialogSkipBinding
  ],
  mappings: [
    keyboardMoveMapping, gamepadMoveMapping,
    keyboardJumpMapping, gamepadJumpMapping,
    keyboardFireMapping, mouseFireMapping, gamepadFireMapping,
    mouseAimMapping,
    keyboardRunMapping, gamepadRunMapping,
    keyboardCrouchMapping, gamepadCrouchMapping,
    keyboardReloadMapping, gamepadReloadMapping,
    mouseSwitchWeaponMapping,
    keyboardUseMapping, gamepadUseMapping,
    keyboardPauseMapping, gamepadPauseMapping,
    keyboardMenuUpMapping, keyboardMenuDownMapping,
    keyboardMenuLeftMapping, keyboardMenuRightMapping,
    keyboardMenuSelectMapping, keyboardMenuBackMapping,
    keyboardDialogNextMapping, keyboardDialogSkipMapping
  ]
});

// 添加输入组件到玩家实体
player.addComponent(inputComponent);

// 创建UI元素
const createUI = () => {
  const container = document.createElement('div');
  container.style.position = 'absolute';
  container.style.top = '10px';
  container.style.left = '10px';
  container.style.color = 'white';
  container.style.fontFamily = 'Arial, sans-serif';
  container.style.fontSize = '14px';
  container.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
  container.style.padding = '10px';
  container.style.borderRadius = '5px';
  container.style.width = '300px';

  const title = document.createElement('h2');
  title.textContent = '高级输入系统示例';
  title.style.margin = '0 0 10px 0';
  container.appendChild(title);

  const contextInfo = document.createElement('div');
  contextInfo.id = 'contextInfo';
  contextInfo.textContent = `当前上下文: ${currentContext}`;
  container.appendChild(contextInfo);

  const contextButtons = document.createElement('div');
  contextButtons.style.marginBottom = '10px';
  
  const gameplayButton = document.createElement('button');
  gameplayButton.textContent = '游戏玩法';
  gameplayButton.onclick = () => setInputContext(InputContext.GAMEPLAY);
  contextButtons.appendChild(gameplayButton);
  
  const menuButton = document.createElement('button');
  menuButton.textContent = '菜单';
  menuButton.onclick = () => setInputContext(InputContext.MENU);
  contextButtons.appendChild(menuButton);
  
  const dialogButton = document.createElement('button');
  dialogButton.textContent = '对话';
  dialogButton.onclick = () => setInputContext(InputContext.DIALOG);
  contextButtons.appendChild(dialogButton);
  
  container.appendChild(contextButtons);

  const inputInfo = document.createElement('div');
  inputInfo.id = 'inputInfo';
  container.appendChild(inputInfo);

  document.body.appendChild(container);
};

// 设置输入上下文
const setInputContext = (context: InputContext) => {
  currentContext = context;
  
  // 更新UI
  const contextInfo = document.getElementById('contextInfo');
  if (contextInfo) {
    contextInfo.textContent = `当前上下文: ${currentContext}`;
  }
};

// 更新UI
const updateUI = () => {
  const inputInfo = document.getElementById('inputInfo');
  if (!inputInfo) return;

  let html = '';

  // 根据当前上下文显示不同的输入信息
  switch (currentContext) {
    case InputContext.GAMEPLAY:
      const move = moveAction.getValue();
      const jump = jumpAction.isPressed();
      const fire = fireAction.isPressed();
      const aim = aimAction.getValue();
      const run = runAction.isPressed();
      const crouch = crouchAction.isPressed();
      const reload = reloadAction.isPressed();
      const switchWeapon = switchWeaponAction.getValue();
      const use = useAction.isPressed();
      const pause = pauseAction.isPressed();

      html += `<h3>游戏玩法输入</h3>`;
      html += `<div>移动: [${move[0].toFixed(2)}, ${move[1].toFixed(2)}]</div>`;
      html += `<div>跳跃: ${jump ? '按下' : '未按下'}</div>`;
      html += `<div>开火: ${fire ? '按下' : '未按下'}</div>`;
      html += `<div>瞄准: ${aim.toFixed(2)}</div>`;
      html += `<div>奔跑: ${run ? '按下' : '未按下'}</div>`;
      html += `<div>蹲下: ${crouch ? '按下' : '未按下'}</div>`;
      html += `<div>装弹: ${reload ? '按下' : '未按下'}</div>`;
      html += `<div>切换武器: ${switchWeapon.toFixed(2)}</div>`;
      html += `<div>使用: ${use ? '按下' : '未按下'}</div>`;
      html += `<div>暂停: ${pause ? '按下' : '未按下'}</div>`;
      break;

    case InputContext.MENU:
      const menuUp = menuUpAction.isPressed();
      const menuDown = menuDownAction.isPressed();
      const menuLeft = menuLeftAction.isPressed();
      const menuRight = menuRightAction.isPressed();
      const menuSelect = menuSelectAction.isPressed();
      const menuBack = menuBackAction.isPressed();

      html += `<h3>菜单输入</h3>`;
      html += `<div>上: ${menuUp ? '按下' : '未按下'}</div>`;
      html += `<div>下: ${menuDown ? '按下' : '未按下'}</div>`;
      html += `<div>左: ${menuLeft ? '按下' : '未按下'}</div>`;
      html += `<div>右: ${menuRight ? '按下' : '未按下'}</div>`;
      html += `<div>选择: ${menuSelect ? '按下' : '未按下'}</div>`;
      html += `<div>返回: ${menuBack ? '按下' : '未按下'}</div>`;
      break;

    case InputContext.DIALOG:
      const dialogNext = dialogNextAction.isPressed();
      const dialogSkip = dialogSkipAction.isPressed();
      const dialogChoice = dialogChoiceAction.getValue();

      html += `<h3>对话输入</h3>`;
      html += `<div>下一步: ${dialogNext ? '按下' : '未按下'}</div>`;
      html += `<div>跳过: ${dialogSkip ? '按下' : '未按下'}</div>`;
      html += `<div>选择: ${dialogChoice.toFixed(2)}</div>`;
      break;
  }

  inputInfo.innerHTML = html;
};

// 创建UI
createUI();

// 游戏循环
const gameLoop = () => {
  // 更新输入管理器
  inputManager.update(engine.deltaTime);

  // 更新输入组件
  inputComponent.update(engine.deltaTime);

  // 更新UI
  updateUI();

  // 处理输入
  switch (currentContext) {
    case InputContext.GAMEPLAY:
      // 处理游戏玩法输入
      if (pauseAction.isJustPressed()) {
        setInputContext(InputContext.MENU);
      }
      break;

    case InputContext.MENU:
      // 处理菜单输入
      if (menuBackAction.isJustPressed()) {
        setInputContext(InputContext.GAMEPLAY);
      }
      break;

    case InputContext.DIALOG:
      // 处理对话输入
      if (dialogSkipAction.isJustPressed()) {
        setInputContext(InputContext.GAMEPLAY);
      }
      break;
  }

  // 更新世界
  world.update(engine.deltaTime);

  // 渲染场景
  renderer.render(scene, camera);

  // 请求下一帧
  requestAnimationFrame(gameLoop);
};

// 开始游戏循环
gameLoop();

// 窗口大小调整
window.addEventListener('resize', () => {
  renderer.setSize(window.innerWidth, window.innerHeight);
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
});
