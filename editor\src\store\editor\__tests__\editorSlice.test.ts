/**
 * 编辑器状态切片测试
 */
import editorReducer, {
  setActiveCamera,
  setSelectedObject,
  setSelectedObjects,
  setTransformMode,
  setTransformSpace,
  setSnapMode,
  setGridSize,
  setShowGrid,
  setShowAxes,
  setShowStats,
  addToUndoStack,
  undo,
  redo,
  clearUndoRedoStack,
  setIsPlaying,
  setViewportSize,
  setSceneGraph,
  clearError,
  TransformMode,
  TransformSpace,
  SnapMode,
} from '../editorSlice';

describe('编辑器状态切片', () => {
  // 初始状态测试
  it('应该返回初始状态', () => {
    const initialState = editorReducer(undefined, { type: 'unknown' });
    expect(initialState).toEqual({
      isLoading: false,
      error: null,
      activeCamera: null,
      selectedObject: null,
      selectedObjects: [],
      transformMode: TransformMode.TRANSLATE,
      transformSpace: TransformSpace.LOCAL,
      snapMode: SnapMode.DISABLED,
      gridSize: 1,
      showGrid: true,
      showAxes: true,
      showStats: false,
      undoStack: [],
      redoStack: [],
      isPlaying: false,
      viewportSize: { width: 0, height: 0 },
      sceneGraph: [],
    });
  });

  // 设置活动相机测试
  it('应该处理setActiveCamera', () => {
    const mockCamera = { id: 'camera1', type: 'perspective' };
    const state = editorReducer(undefined, setActiveCamera(mockCamera));
    expect(state.activeCamera).toEqual(mockCamera);
  });

  // 设置选中对象测试
  it('应该处理setSelectedObject', () => {
    const mockObject = { id: 'object1', name: '测试对象' };
    const state = editorReducer(undefined, setSelectedObject(mockObject));
    expect(state.selectedObject).toEqual(mockObject);
    expect(state.selectedObjects).toEqual([mockObject]);
  });

  // 设置选中对象数组测试
  it('应该处理setSelectedObjects', () => {
    const mockObjects = [
      { id: 'object1', name: '测试对象1' },
      { id: 'object2', name: '测试对象2' },
    ];
    const state = editorReducer(undefined, setSelectedObjects(mockObjects));
    expect(state.selectedObjects).toEqual(mockObjects);
    expect(state.selectedObject).toBeNull(); // 多选时selectedObject为null
  });

  // 设置单个选中对象时的selectedObject测试
  it('应该在setSelectedObjects只有一个对象时设置selectedObject', () => {
    const mockObject = { id: 'object1', name: '测试对象' };
    const state = editorReducer(undefined, setSelectedObjects([mockObject]));
    expect(state.selectedObjects).toEqual([mockObject]);
    expect(state.selectedObject).toEqual(mockObject);
  });

  // 设置变换模式测试
  it('应该处理setTransformMode', () => {
    const state = editorReducer(undefined, setTransformMode(TransformMode.ROTATE));
    expect(state.transformMode).toBe(TransformMode.ROTATE);
  });

  // 设置变换空间测试
  it('应该处理setTransformSpace', () => {
    const state = editorReducer(undefined, setTransformSpace(TransformSpace.WORLD));
    expect(state.transformSpace).toBe(TransformSpace.WORLD);
  });

  // 设置捕捉模式测试
  it('应该处理setSnapMode', () => {
    const state = editorReducer(undefined, setSnapMode(SnapMode.GRID));
    expect(state.snapMode).toBe(SnapMode.GRID);
  });

  // 设置网格大小测试
  it('应该处理setGridSize', () => {
    const state = editorReducer(undefined, setGridSize(0.5));
    expect(state.gridSize).toBe(0.5);
  });

  // 设置显示网格测试
  it('应该处理setShowGrid', () => {
    const state = editorReducer(undefined, setShowGrid(false));
    expect(state.showGrid).toBe(false);
  });

  // 设置显示坐标轴测试
  it('应该处理setShowAxes', () => {
    const state = editorReducer(undefined, setShowAxes(false));
    expect(state.showAxes).toBe(false);
  });

  // 设置显示统计信息测试
  it('应该处理setShowStats', () => {
    const state = editorReducer(undefined, setShowStats(true));
    expect(state.showStats).toBe(true);
  });

  // 添加到撤销栈测试
  it('应该处理addToUndoStack', () => {
    const mockAction = { type: 'TEST_ACTION', payload: { id: 'test' } };
    const state = editorReducer(undefined, addToUndoStack(mockAction));
    expect(state.undoStack).toEqual([mockAction]);
  });

  // 撤销操作测试
  it('应该处理undo', () => {
    const mockAction = { type: 'TEST_ACTION', payload: { id: 'test' } };
    let state = editorReducer(undefined, addToUndoStack(mockAction));
    state = editorReducer(state, undo());
    expect(state.undoStack).toEqual([]);
    expect(state.redoStack).toEqual([mockAction]);
  });

  // 重做操作测试
  it('应该处理redo', () => {
    const mockAction = { type: 'TEST_ACTION', payload: { id: 'test' } };
    let state = editorReducer(undefined, addToUndoStack(mockAction));
    state = editorReducer(state, undo());
    state = editorReducer(state, redo());
    expect(state.undoStack).toEqual([mockAction]);
    expect(state.redoStack).toEqual([]);
  });

  // 清除撤销重做栈测试
  it('应该处理clearUndoRedoStack', () => {
    const mockAction = { type: 'TEST_ACTION', payload: { id: 'test' } };
    let state = editorReducer(undefined, addToUndoStack(mockAction));
    state = editorReducer(state, undo());
    state = editorReducer(state, clearUndoRedoStack());
    expect(state.undoStack).toEqual([]);
    expect(state.redoStack).toEqual([]);
  });

  // 设置播放状态测试
  it('应该处理setIsPlaying', () => {
    const state = editorReducer(undefined, setIsPlaying(true));
    expect(state.isPlaying).toBe(true);
  });

  // 设置视口大小测试
  it('应该处理setViewportSize', () => {
    const size = { width: 1280, height: 720 };
    const state = editorReducer(undefined, setViewportSize(size));
    expect(state.viewportSize).toEqual(size);
  });

  // 设置场景图测试
  it('应该处理setSceneGraph', () => {
    const mockSceneGraph = [
      {
        key: 'root',
        title: 'Root',
        children: [
          { key: 'entity1', title: 'Entity 1' },
          { key: 'entity2', title: 'Entity 2' },
        ],
      },
    ];
    const state = editorReducer(undefined, setSceneGraph(mockSceneGraph));
    expect(state.sceneGraph).toEqual(mockSceneGraph);
  });

  // 清除错误测试
  it('应该处理clearError', () => {
    // 首先创建一个带有错误的状态
    const stateWithError = {
      ...editorReducer(undefined, { type: 'unknown' }),
      error: '测试错误',
    };
    const state = editorReducer(stateWithError, clearError());
    expect(state.error).toBeNull();
  });
});
