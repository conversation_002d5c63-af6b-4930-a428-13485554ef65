/**
 * 子片段过渡
 * 用于在子片段之间平滑过渡
 */
import * as THREE from 'three';
import { EventEmitter } from '../utils/EventEmitter';
import { SubClip } from './SubClip';
import { AnimationSubClip } from './AnimationSubClip';

/**
 * 子片段过渡事件类型
 */
export enum SubClipTransitionEventType {
  /** 过渡开始 */
  TRANSITION_START = 'transitionStart',
  /** 过渡结束 */
  TRANSITION_END = 'transitionEnd',
  /** 过渡更新 */
  TRANSITION_UPDATE = 'transitionUpdate',
  /** 过渡取消 */
  TRANSITION_CANCEL = 'transitionCancel'
}

/**
 * 过渡类型
 */
export enum TransitionType {
  /** 线性 */
  LINEAR = 'linear',
  /** 缓入 */
  EASE_IN = 'easeIn',
  /** 缓出 */
  EASE_OUT = 'easeOut',
  /** 缓入缓出 */
  EASE_IN_OUT = 'easeInOut',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 子片段过渡配置
 */
export interface SubClipTransitionConfig {
  /** 过渡名称 */
  name?: string;
  /** 源子片段 */
  fromClip?: SubClip | AnimationSubClip;
  /** 目标子片段 */
  toClip?: SubClip | AnimationSubClip;
  /** 过渡时间（秒） */
  duration?: number;
  /** 过渡类型 */
  type?: TransitionType;
  /** 自定义过渡函数 */
  customTransition?: (t: number) => number;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 子片段过渡
 */
export class SubClipTransition {
  /** 过渡名称 */
  private name: string;
  /** 源子片段 */
  private fromClip: SubClip | AnimationSubClip | null = null;
  /** 目标子片段 */
  private toClip: SubClip | AnimationSubClip | null = null;
  /** 过渡时间（秒） */
  private duration: number;
  /** 过渡类型 */
  private type: TransitionType;
  /** 自定义过渡函数 */
  private customTransition?: (t: number) => number;
  /** 是否启用调试 */
  private debug: boolean;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 是否正在过渡 */
  private isTransitioning: boolean = false;
  /** 当前过渡时间 */
  private currentTime: number = 0;
  /** 过渡进度（0-1） */
  private progress: number = 0;
  /** 源动作 */
  private fromAction: THREE.AnimationAction | null = null;
  /** 目标动作 */
  private toAction: THREE.AnimationAction | null = null;
  /** 混合后的片段 */
  private blendedClip: THREE.AnimationClip | null = null;

  /**
   * 创建子片段过渡
   * @param config 配置
   */
  constructor(config: SubClipTransitionConfig = {}) {
    this.name = config.name || 'transition';
    this.fromClip = config.fromClip || null;
    this.toClip = config.toClip || null;
    this.duration = config.duration !== undefined ? config.duration : 1.0;
    this.type = config.type || TransitionType.LINEAR;
    this.customTransition = config.customTransition;
    this.debug = config.debug !== undefined ? config.debug : false;
  }

  /**
   * 获取过渡名称
   * @returns 过渡名称
   */
  public getName(): string {
    return this.name;
  }

  /**
   * 设置过渡名称
   * @param name 过渡名称
   */
  public setName(name: string): void {
    this.name = name;
  }

  /**
   * 获取源子片段
   * @returns 源子片段
   */
  public getFromClip(): SubClip | AnimationSubClip | null {
    return this.fromClip;
  }

  /**
   * 设置源子片段
   * @param clip 源子片段
   */
  public setFromClip(clip: SubClip | AnimationSubClip): void {
    this.fromClip = clip;
  }

  /**
   * 获取目标子片段
   * @returns 目标子片段
   */
  public getToClip(): SubClip | AnimationSubClip | null {
    return this.toClip;
  }

  /**
   * 设置目标子片段
   * @param clip 目标子片段
   */
  public setToClip(clip: SubClip | AnimationSubClip): void {
    this.toClip = clip;
  }

  /**
   * 获取过渡时间
   * @returns 过渡时间（秒）
   */
  public getDuration(): number {
    return this.duration;
  }

  /**
   * 设置过渡时间
   * @param duration 过渡时间（秒）
   */
  public setDuration(duration: number): void {
    this.duration = duration;
  }

  /**
   * 获取过渡类型
   * @returns 过渡类型
   */
  public getType(): TransitionType {
    return this.type;
  }

  /**
   * 设置过渡类型
   * @param type 过渡类型
   */
  public setType(type: TransitionType): void {
    this.type = type;
  }

  /**
   * 设置自定义过渡函数
   * @param func 自定义过渡函数
   */
  public setCustomTransition(func: (t: number) => number): void {
    this.customTransition = func;
    this.type = TransitionType.CUSTOM;
  }

  /**
   * 是否正在过渡
   * @returns 是否正在过渡
   */
  public isInTransition(): boolean {
    return this.isTransitioning;
  }

  /**
   * 获取过渡进度
   * @returns 过渡进度（0-1）
   */
  public getProgress(): number {
    return this.progress;
  }

  /**
   * 开始过渡
   * @param fromClip 源子片段
   * @param toClip 目标子片段
   * @param duration 过渡时间（秒）
   */
  public start(
    fromClip?: SubClip | AnimationSubClip,
    toClip?: SubClip | AnimationSubClip,
    duration?: number
  ): void {
    // 更新参数
    if (fromClip) this.fromClip = fromClip;
    if (toClip) this.toClip = toClip;
    if (duration !== undefined) this.duration = duration;

    // 检查参数
    if (!this.fromClip || !this.toClip) {
      console.error('源子片段或目标子片段为空');
      return;
    }

    // 重置状态
    this.currentTime = 0;
    this.progress = 0;
    this.isTransitioning = true;

    // 触发过渡开始事件
    this.eventEmitter.emit(SubClipTransitionEventType.TRANSITION_START, {
      transition: this,
      fromClip: this.fromClip,
      toClip: this.toClip,
      duration: this.duration
    });

    if (this.debug) {
      console.log(`开始过渡: ${this.name}, 源子片段: ${this.fromClip.getName()}, 目标子片段: ${this.toClip.getName()}, 持续时间: ${this.duration}`);
    }
  }

  /**
   * 停止过渡
   */
  public stop(): void {
    if (!this.isTransitioning) return;

    this.isTransitioning = false;

    // 触发过渡取消事件
    this.eventEmitter.emit(SubClipTransitionEventType.TRANSITION_CANCEL, {
      transition: this
    });

    if (this.debug) {
      console.log(`停止过渡: ${this.name}`);
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public addEventListener(event: SubClipTransitionEventType, listener: (data: any) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public removeEventListener(event: SubClipTransitionEventType, listener: (data: any) => void): void {
    this.eventEmitter.off(event, listener);
  }
}
