.history-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .history-toolbar {
    padding: 12px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .history-content {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
    
    .ant-tabs {
      height: 100%;
      
      .ant-tabs-content {
        height: 100%;
        
        .ant-tabs-tabpane {
          height: 100%;
          overflow-y: auto;
        }
      }
    }
  }
  
  .comparison-result {
    padding: 12px;
    
    .ant-statistic {
      text-align: center;
    }
  }
  
  .detail-optimizations {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
    
    .detail-optimization-item {
      margin-bottom: 8px;
    }
  }
}

// 暗色主题样式
.dark-theme {
  .history-panel {
    .history-toolbar {
      background-color: #1e1e1e;
      border-bottom: 1px solid #303030;
    }
  }
}
