"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.System = void 0;
var EventEmitter_1 = require("../utils/EventEmitter");
var System = /** @class */ (function (_super) {
    __extends(System, _super);
    /**
     * 创建系统实例
     * @param priority 优先级（数字越小优先级越高）
     */
    function System(priority) {
        if (priority === void 0) { priority = 0; }
        var _this = _super.call(this) || this;
        /** 引擎引用 */
        _this.engine = null;
        /** 世界引用 */
        _this.world = null;
        /** 是否启用 */
        _this.enabled = true;
        _this.type = _this.constructor.name;
        _this.priority = priority;
        return _this;
    }
    /**
     * 获取系统类型
     * @returns 系统类型
     */
    System.prototype.getType = function () {
        return this.type;
    };
    /**
     * 设置引擎引用
     * @param engine 引擎实例
     */
    System.prototype.setEngine = function (engine) {
        this.engine = engine;
    };
    /**
     * 获取引擎引用
     * @returns 引擎实例
     */
    System.prototype.getEngine = function () {
        return this.engine;
    };
    /**
     * 设置世界引用
     * @param world 世界实例
     */
    System.prototype.setWorld = function (world) {
        this.world = world;
        this.engine = world.getEngine();
    };
    /**
     * 获取世界引用
     * @returns 世界实例
     */
    System.prototype.getWorld = function () {
        return this.world;
    };
    /**
     * 获取优先级
     * @returns 优先级
     */
    System.prototype.getPriority = function () {
        return this.priority;
    };
    /**
     * 设置优先级
     * @param priority 优先级
     */
    System.prototype.setPriority = function (priority) {
        this.priority = priority;
    };
    /**
     * 设置启用状态
     * @param enabled 是否启用
     */
    System.prototype.setEnabled = function (enabled) {
        if (this.enabled === enabled) {
            return;
        }
        this.enabled = enabled;
        if (enabled) {
            this.onEnable();
        }
        else {
            this.onDisable();
        }
        // 发出启用状态变更事件
        this.emit('enabledChanged', enabled);
    };
    /**
     * 是否启用
     * @returns 是否启用
     */
    System.prototype.isEnabled = function () {
        return this.enabled;
    };
    /**
     * 初始化系统
     */
    System.prototype.initialize = function () {
        // 子类可以重写此方法
    };
    /**
     * 当系统启用时调用
     */
    System.prototype.onEnable = function () {
        // 子类可以重写此方法
    };
    /**
     * 当系统禁用时调用
     */
    System.prototype.onDisable = function () {
        // 子类可以重写此方法
    };
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    System.prototype.update = function (deltaTime) {
        // 子类可以重写此方法
    };
    /**
     * 固定时间步长更新
     * @param fixedDeltaTime 固定帧间隔时间（秒）
     */
    System.prototype.fixedUpdate = function (fixedDeltaTime) {
        // 子类可以重写此方法
    };
    /**
     * 后更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    System.prototype.lateUpdate = function (deltaTime) {
        // 子类可以重写此方法
    };
    /**
     * 销毁系统
     */
    System.prototype.dispose = function () {
        // 清除引擎引用
        this.engine = null;
        // 清除世界引用
        this.world = null;
        // 移除所有事件监听器
        this.removeAllListeners();
    };
    return System;
}(EventEmitter_1.EventEmitter));
exports.System = System;
