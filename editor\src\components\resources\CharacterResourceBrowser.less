.character-resource-browser {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  .resource-search {
    margin-bottom: 16px;
    padding: 0 16px;
  }

  .ant-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .ant-tabs-content {
      flex: 1;
      overflow: auto;
      padding: 0 16px 16px;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }

  .resource-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
  }

  .resource-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    .ant-card-cover {
      height: 160px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .ant-card-body {
      flex: 1;
    }

    .resource-tags {
      margin-top: 8px;
      
      .ant-tag {
        cursor: pointer;
        margin-bottom: 4px;
        
        &:hover {
          opacity: 0.8;
        }
      }
    }

    .clip-count {
      margin-top: 8px;
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .resource-detail {
    .resource-detail-header {
      display: flex;
      gap: 24px;

      .detail-thumbnail {
        width: 240px;
        height: 180px;
        object-fit: cover;
        border-radius: 4px;
      }

      .detail-info {
        flex: 1;
      }

      .detail-meta {
        margin: 12px 0;
        display: flex;
        gap: 24px;
      }

      .detail-tags {
        margin: 12px 0;
      }

      .detail-features {
        margin: 12px 0;

        .feature-list {
          margin-top: 8px;
        }
      }
    }

    .preview-container {
      position: relative;

      .preview-button {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 24px;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .blend-shapes-section,
    .animation-clips-section {
      margin: 16px 0;

      .blend-shapes-container {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;
        margin-top: 12px;

        .blend-shapes-group {
          flex: 1;
          min-width: 200px;

          .blend-shapes-list {
            margin-top: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
          }
        }
      }
    }

    .clip-list {
      margin-top: 12px;
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      padding: 8px;

      .clip-meta {
        display: flex;
        gap: 16px;
        margin-top: 4px;
        color: rgba(0, 0, 0, 0.45);
      }
    }

    .detail-actions {
      margin-top: 24px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
