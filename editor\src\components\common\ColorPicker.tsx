/**
 * 颜色选择器组件
 * 用于选择颜色
 */
import React, { useState, useEffect } from 'react';
import { Button, Popover, Input, Space } from 'antd';
import { SketchPicker, ColorResult } from 'react-color';
import { useTranslation } from 'react-i18next';

/**
 * 颜色选择器组件属性
 */
interface ColorPickerProps {
  /** 值 */
  value?: string;
  /** 变更回调 */
  onChange?: (value: string) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否显示透明度 */
  showAlpha?: boolean;
  /** 预设颜色 */
  presetColors?: string[];
}

/**
 * 颜色选择器组件
 */
const ColorPicker: React.FC<ColorPickerProps> = ({
  value = '#000000',
  onChange,
  disabled = false,
  showAlpha = false,
  presetColors
}) => {
  const { t } = useTranslation();
  const [color, setColor] = useState<string>(value);
  const [popoverVisible, setPopoverVisible] = useState<boolean>(false);
  
  // 当值变化时更新内部状态
  useEffect(() => {
    setColor(value);
  }, [value]);
  
  // 处理颜色变更
  const handleColorChange = (colorResult: ColorResult) => {
    const { r, g, b, a } = colorResult.rgb;
    let colorValue: string;
    
    if (showAlpha && a !== undefined) {
      colorValue = `rgba(${r}, ${g}, ${b}, ${a})`;
    } else {
      colorValue = colorResult.hex;
    }
    
    setColor(colorValue);
    
    if (onChange) {
      onChange(colorValue);
    }
  };
  
  // 处理输入框变更
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const colorValue = e.target.value;
    setColor(colorValue);
    
    if (onChange) {
      onChange(colorValue);
    }
  };
  
  // 处理Popover可见性变更
  const handleVisibleChange = (visible: boolean) => {
    setPopoverVisible(visible);
  };
  
  // 渲染颜色选择器内容
  const colorPickerContent = (
    <div>
      <SketchPicker
        color={color}
        onChange={handleColorChange}
        disableAlpha={!showAlpha}
        presetColors={presetColors}
      />
    </div>
  );
  
  return (
    <Space>
      <Popover
        content={colorPickerContent}
        trigger="click"
        visible={popoverVisible && !disabled}
        onVisibleChange={handleVisibleChange}
      >
        <Button
          style={{
            backgroundColor: color,
            width: 32,
            height: 32,
            minWidth: 32,
            border: '1px solid #d9d9d9',
            cursor: disabled ? 'not-allowed' : 'pointer',
            opacity: disabled ? 0.5 : 1
          }}
          disabled={disabled}
        />
      </Popover>
      <Input
        value={color}
        onChange={handleInputChange}
        disabled={disabled}
        style={{ width: 120 }}
      />
    </Space>
  );
};

export default ColorPicker;
