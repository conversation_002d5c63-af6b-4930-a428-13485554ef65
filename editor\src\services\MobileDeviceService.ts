/**
 * 移动设备服务
 * 用于检测和管理移动设备相关功能
 */
import { EventEmitter } from 'events';

// 设备类型枚举
export enum DeviceType {
  DESKTOP = 'desktop',
  TABLET = 'tablet',
  MOBILE = 'mobile'
}

// 屏幕方向枚举
export enum ScreenOrientation {
  PORTRAIT = 'portrait',
  LANDSCAPE = 'landscape'
}

// 移动设备信息接口
export interface MobileDeviceInfo {
  // 设备类型
  type: DeviceType;
  // 是否是移动设备
  isMobile: boolean;
  // 是否是平板设备
  isTablet: boolean;
  // 是否是触摸设备
  isTouch: boolean;
  // 屏幕方向
  orientation: ScreenOrientation;
  // 屏幕宽度
  screenWidth: number;
  // 屏幕高度
  screenHeight: number;
  // 设备像素比
  pixelRatio: number;
  // 浏览器用户代理
  userAgent: string;
  // 操作系统
  os: string;
  // 浏览器
  browser: string;
}

// 移动设备服务配置接口
export interface MobileDeviceServiceConfig {
  // 是否启用调试模式
  debug?: boolean;
  // 是否自动检测设备变化
  autoDetect?: boolean;
  // 检测间隔（毫秒）
  detectInterval?: number;
  // 移动设备最大宽度（像素）
  mobileMaxWidth?: number;
  // 平板设备最大宽度（像素）
  tabletMaxWidth?: number;
}

// 移动设备服务事件类型
export enum MobileDeviceEventType {
  DEVICE_CHANGED = 'deviceChanged',
  ORIENTATION_CHANGED = 'orientationChanged',
  SIZE_CHANGED = 'sizeChanged'
}

/**
 * 移动设备服务类
 * 用于检测和管理移动设备相关功能
 */
export class MobileDeviceService extends EventEmitter {
  // 单例实例
  private static instance: MobileDeviceService;

  // 配置
  private config: MobileDeviceServiceConfig;

  // 设备信息
  private deviceInfo: MobileDeviceInfo;

  // 检测定时器ID
  private detectTimerId: number | null = null;

  // 上一次检测时间
  private lastDetectTime: number = 0;

  /**
   * 获取单例实例
   * @returns 移动设备服务实例
   */
  public static getInstance(): MobileDeviceService {
    if (!MobileDeviceService.instance) {
      MobileDeviceService.instance = new MobileDeviceService();
    }
    return MobileDeviceService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    super();

    // 默认配置
    this.config = {
      debug: false,
      autoDetect: true,
      detectInterval: 1000,
      mobileMaxWidth: 768,
      tabletMaxWidth: 1024
    };

    // 初始化设备信息
    this.deviceInfo = this.detectDevice();

    // 添加窗口事件监听
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', this.handleResize.bind(this));
      window.addEventListener('orientationchange', this.handleOrientationChange.bind(this));
    }

    // 启动自动检测
    if (this.config.autoDetect) {
      this.startAutoDetect();
    }
  }

  /**
   * 配置服务
   * @param config 配置
   */
  public configure(config: Partial<MobileDeviceServiceConfig>): void {
    this.config = { ...this.config, ...config };

    // 如果启用自动检测，则启动检测
    if (this.config.autoDetect && !this.detectTimerId) {
      this.startAutoDetect();
    }
    // 如果禁用自动检测，则停止检测
    else if (!this.config.autoDetect && this.detectTimerId) {
      this.stopAutoDetect();
    }

    if (this.config.debug) {
      console.log('移动设备服务配置已更新', this.config);
    }
  }

  /**
   * 获取设备信息
   * @returns 设备信息
   */
  public getDeviceInfo(): MobileDeviceInfo {
    return { ...this.deviceInfo };
  }

  /**
   * 检测设备是否为移动设备
   * @returns 是否为移动设备
   */
  public isMobileDevice(): boolean {
    return this.deviceInfo.isMobile;
  }

  /**
   * 检测设备是否为平板设备
   * @returns 是否为平板设备
   */
  public isTabletDevice(): boolean {
    return this.deviceInfo.isTablet;
  }

  /**
   * 检测设备是否为触摸设备
   * @returns 是否为触摸设备
   */
  public isTouchDevice(): boolean {
    return this.deviceInfo.isTouch;
  }

  /**
   * 获取屏幕方向
   * @returns 屏幕方向
   */
  public getOrientation(): ScreenOrientation {
    return this.deviceInfo.orientation;
  }

  /**
   * 检测设备
   * @returns 设备信息
   */
  private detectDevice(): MobileDeviceInfo {
    // 默认值
    const info: MobileDeviceInfo = {
      type: DeviceType.DESKTOP,
      isMobile: false,
      isTablet: false,
      isTouch: false,
      orientation: ScreenOrientation.LANDSCAPE,
      screenWidth: 1920,
      screenHeight: 1080,
      pixelRatio: 1,
      userAgent: '',
      os: 'unknown',
      browser: 'unknown'
    };

    // 如果不在浏览器环境中，返回默认值
    if (typeof window === 'undefined' || typeof navigator === 'undefined') {
      return info;
    }

    // 获取用户代理
    const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;
    info.userAgent = userAgent;

    // 检测触摸设备
    info.isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    // 获取屏幕尺寸
    info.screenWidth = window.innerWidth;
    info.screenHeight = window.innerHeight;
    info.pixelRatio = window.devicePixelRatio || 1;

    // 检测屏幕方向
    info.orientation = info.screenWidth > info.screenHeight ? ScreenOrientation.LANDSCAPE : ScreenOrientation.PORTRAIT;

    // 检测移动设备
    const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i;
    info.isMobile = mobileRegex.test(userAgent);

    // 检测平板设备
    const tabletRegex = /ipad|android(?!.*mobile)/i;
    info.isTablet = tabletRegex.test(userAgent) || (info.isMobile && info.screenWidth > this.config.mobileMaxWidth!);

    // 根据屏幕宽度确定设备类型
    if (info.screenWidth <= this.config.mobileMaxWidth!) {
      info.type = DeviceType.MOBILE;
    } else if (info.screenWidth <= this.config.tabletMaxWidth!) {
      info.type = DeviceType.TABLET;
    } else {
      info.type = DeviceType.DESKTOP;
    }

    // 检测操作系统
    if (/windows/i.test(userAgent)) {
      info.os = 'Windows';
    } else if (/macintosh|mac os x/i.test(userAgent)) {
      info.os = 'MacOS';
    } else if (/android/i.test(userAgent)) {
      info.os = 'Android';
    } else if (/iphone|ipad|ipod/i.test(userAgent)) {
      info.os = 'iOS';
    } else if (/linux/i.test(userAgent)) {
      info.os = 'Linux';
    }

    // 检测浏览器
    if (/chrome/i.test(userAgent)) {
      info.browser = 'Chrome';
    } else if (/firefox/i.test(userAgent)) {
      info.browser = 'Firefox';
    } else if (/safari/i.test(userAgent)) {
      info.browser = 'Safari';
    } else if (/edge/i.test(userAgent)) {
      info.browser = 'Edge';
    } else if (/opera/i.test(userAgent)) {
      info.browser = 'Opera';
    } else if (/msie|trident/i.test(userAgent)) {
      info.browser = 'IE';
    }

    return info;
  }

  /**
   * 启动自动检测
   */
  private startAutoDetect(): void {
    if (this.detectTimerId !== null) {
      return;
    }

    this.detectTimerId = window.setInterval(() => {
      this.checkDeviceChanges();
    }, this.config.detectInterval);

    if (this.config.debug) {
      console.log('移动设备自动检测已启动');
    }
  }

  /**
   * 停止自动检测
   */
  private stopAutoDetect(): void {
    if (this.detectTimerId !== null) {
      clearInterval(this.detectTimerId);
      this.detectTimerId = null;

      if (this.config.debug) {
        console.log('移动设备自动检测已停止');
      }
    }
  }

  /**
   * 检查设备变化
   */
  private checkDeviceChanges(): void {
    const now = Date.now();
    if (now - this.lastDetectTime < this.config.detectInterval!) {
      return;
    }

    this.lastDetectTime = now;
    const newInfo = this.detectDevice();
    const oldInfo = this.deviceInfo;

    // 检查设备类型变化
    if (newInfo.type !== oldInfo.type) {
      this.deviceInfo = newInfo;
      this.emit(MobileDeviceEventType.DEVICE_CHANGED, newInfo);

      if (this.config.debug) {
        console.log('设备类型已变化', oldInfo.type, '->', newInfo.type);
      }
    }
    // 检查屏幕方向变化
    else if (newInfo.orientation !== oldInfo.orientation) {
      this.deviceInfo = newInfo;
      this.emit(MobileDeviceEventType.ORIENTATION_CHANGED, newInfo);

      if (this.config.debug) {
        console.log('屏幕方向已变化', oldInfo.orientation, '->', newInfo.orientation);
      }
    }
    // 检查屏幕尺寸变化
    else if (newInfo.screenWidth !== oldInfo.screenWidth || newInfo.screenHeight !== oldInfo.screenHeight) {
      this.deviceInfo = newInfo;
      this.emit(MobileDeviceEventType.SIZE_CHANGED, newInfo);

      if (this.config.debug) {
        console.log('屏幕尺寸已变化', `${oldInfo.screenWidth}x${oldInfo.screenHeight}`, '->', `${newInfo.screenWidth}x${newInfo.screenHeight}`);
      }
    }
  }

  /**
   * 处理窗口大小变化事件
   */
  private handleResize(): void {
    this.checkDeviceChanges();
  }

  /**
   * 处理屏幕方向变化事件
   */
  private handleOrientationChange(): void {
    this.checkDeviceChanges();
  }
}

// 导出默认实例
export default MobileDeviceService.getInstance();
