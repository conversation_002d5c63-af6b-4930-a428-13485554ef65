/**
 * 地形预览组件
 * 用于预览地形生成效果
 */
import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { TerrainGenerationAlgorithms, TerrainFeatureType } from '../../../engine/src/terrain/utils/TerrainGenerationAlgorithms';
import { UndergroundRiverParams } from '../../../engine/src/terrain/utils/UndergroundRiverGenerator';
import './TerrainPreview.less';

/**
 * 地形预览属性
 */
interface TerrainPreviewProps {
  /** 算法类型 */
  algorithm: string;
  /** 算法参数 */
  params: any;
  /** 宽度 */
  width?: number;
  /** 高度 */
  height?: number;
  /** 是否启用 */
  enabled?: boolean;
  /** 分辨率 */
  resolution?: number;
}

/**
 * 地形预览组件
 */
const TerrainPreview: React.FC<TerrainPreviewProps> = ({
  algorithm,
  params,
  width = 300,
  height = 200,
  enabled = true,
  resolution = 64
}) => {
  // 引用
  const containerRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const controlsRef = useRef<OrbitControls | null>(null);
  const meshRef = useRef<THREE.Mesh | null>(null);
  const frameIdRef = useRef<number | null>(null);

  // 状态
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  // 初始化Three.js
  useEffect(() => {
    if (!containerRef.current || !enabled) {
      return;
    }

    // 创建场景
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // 创建相机
    const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
    camera.position.set(0.5, 0.5, 0.5);
    cameraRef.current = camera;

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(width, height);
    renderer.setClearColor(0xf0f0f0);
    renderer.shadowMap.enabled = true;
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // 创建控制器
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.25;
    controls.enableZoom = true;
    controlsRef.current = controls;

    // 添加光源
    const ambientLight = new THREE.AmbientLight(0x404040);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 1, 1);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    // 创建地形网格
    const geometry = new THREE.PlaneGeometry(1, 1, resolution - 1, resolution - 1);
    geometry.rotateX(-Math.PI / 2);

    const material = new THREE.MeshStandardMaterial({
      color: 0x3d8c40,
      side: THREE.DoubleSide,
      flatShading: false,
      metalness: 0.0,
      roughness: 0.8,
      wireframe: false
    });

    const mesh = new THREE.Mesh(geometry, material);
    mesh.castShadow = true;
    mesh.receiveShadow = true;
    scene.add(mesh);
    meshRef.current = mesh;

    // 添加坐标轴辅助
    const axesHelper = new THREE.AxesHelper(0.5);
    scene.add(axesHelper);

    // 添加网格辅助
    const gridHelper = new THREE.GridHelper(1, 10);
    scene.add(gridHelper);

    // 动画循环
    const animate = () => {
      frameIdRef.current = requestAnimationFrame(animate);

      if (controlsRef.current) {
        controlsRef.current.update();
      }

      if (rendererRef.current && sceneRef.current && cameraRef.current) {
        rendererRef.current.render(sceneRef.current, cameraRef.current);
      }
    };

    animate();
    setIsInitialized(true);

    // 清理函数
    return () => {
      if (frameIdRef.current !== null) {
        cancelAnimationFrame(frameIdRef.current);
      }

      if (rendererRef.current && containerRef.current) {
        containerRef.current.removeChild(rendererRef.current.domElement);
      }

      if (meshRef.current && sceneRef.current) {
        sceneRef.current.remove(meshRef.current);
      }

      if (meshRef.current && meshRef.current.geometry) {
        meshRef.current.geometry.dispose();
      }

      if (meshRef.current && meshRef.current.material) {
        (meshRef.current.material as THREE.Material).dispose();
      }
    };
  }, [enabled, width, height, resolution]);

  // 更新地形
  useEffect(() => {
    if (!isInitialized || !meshRef.current || !enabled) {
      return;
    }

    // 获取几何体
    const geometry = meshRef.current.geometry as THREE.BufferGeometry;
    const positions = geometry.attributes.position.array as Float32Array;

    // 创建高度数据
    const heightData = new Float32Array(resolution * resolution);

    // 根据算法生成高度数据
    generateHeightData(heightData, algorithm, params);

    // 更新几何体顶点高度
    for (let i = 0, j = 0; i < positions.length; i += 3, j++) {
      const x = Math.floor(j % resolution);
      const z = Math.floor(j / resolution);
      const index = z * resolution + x;
      positions[i + 1] = heightData[index] * 0.2; // 缩放高度以便于查看
    }

    // 更新几何体
    geometry.attributes.position.needsUpdate = true;
    geometry.computeVertexNormals();

    // 如果是地下河，添加可视化效果
    if (algorithm === 'underground_river' && meshRef.current) {
      // 移除之前的可视化效果
      const scene = meshRef.current.parent;
      if (scene) {
        const existingLines = scene.children.filter(child => child.name === 'underground-river-line');
        existingLines.forEach(line => scene.remove(line));
      }

      // 添加地下河可视化效果
      if (params && params.count && scene) {
        const riverCount = Math.min(params.count, 5); // 限制预览中的河流数量

        for (let i = 0; i < riverCount; i++) {
          // 创建随机河流路径
          const points = [];
          const startX = (Math.random() - 0.5) * 0.8;
          const startZ = (Math.random() - 0.5) * 0.8;
          let currentX = startX;
          let currentZ = startZ;
          let dirX = Math.random() - 0.5;
          let dirZ = Math.random() - 0.5;
          const length = Math.floor(10 + Math.random() * 20);

          // 标准化方向
          const dirLength = Math.sqrt(dirX * dirX + dirZ * dirZ);
          dirX /= dirLength;
          dirZ /= dirLength;

          // 生成路径点
          for (let j = 0; j < length; j++) {
            // 添加点
            points.push(new THREE.Vector3(currentX, 0.01, currentZ));

            // 更新方向（添加随机偏移）
            const angle = (Math.random() - 0.5) * params.sinuosity * 0.5;
            const newDirX = dirX * Math.cos(angle) - dirZ * Math.sin(angle);
            const newDirZ = dirX * Math.sin(angle) + dirZ * Math.cos(angle);
            dirX = newDirX;
            dirZ = newDirZ;

            // 更新位置
            currentX += dirX * 0.05;
            currentZ += dirZ * 0.05;

            // 确保位置在范围内
            if (currentX < -0.5 || currentX > 0.5 || currentZ < -0.5 || currentZ > 0.5) {
              break;
            }
          }

          // 创建线条几何体
          const geometry = new THREE.BufferGeometry().setFromPoints(points);
          const material = new THREE.LineBasicMaterial({ color: 0x0088ff, linewidth: 2 });
          const line = new THREE.Line(geometry, material);
          line.name = 'underground-river-line';
          scene.add(line);
        }
      }
    }

  }, [algorithm, params, isInitialized, enabled, resolution]);

  /**
   * 生成高度数据
   * @param heightData 高度数据
   * @param algorithm 算法
   * @param params 参数
   */
  const generateHeightData = (heightData: Float32Array, algorithm: string, params: any) => {
    // 初始化高度数据
    for (let i = 0; i < heightData.length; i++) {
      heightData[i] = 0;
    }

    // 根据算法生成高度数据
    switch (algorithm) {
      case 'perlin':
      case 'fractal':
      case 'multifractal':
        generateNoiseHeightData(heightData, params);
        break;
      case 'mountain':
        generateMountainHeightData(heightData, params);
        break;
      case 'canyon':
        generateCanyonHeightData(heightData, params);
        break;
      case 'river':
        generateRiverHeightData(heightData, params);
        break;
      case 'underground_river':
        generateUndergroundRiverHeightData(heightData, params);
        break;
      case 'cave':
        generateCaveHeightData(heightData, params);
        break;
      case 'cliff':
        generateCliffHeightData(heightData, params);
        break;
      case 'volcano':
        generateVolcanoHeightData(heightData, params);
        break;
      case 'feature_combination':
        generateCombinationHeightData(heightData, params);
        break;
      default:
        // 默认生成平坦地形
        for (let i = 0; i < heightData.length; i++) {
          heightData[i] = 0.1;
        }
        break;
    }
  };

  // 简化的噪声生成函数
  const generateNoiseHeightData = (heightData: Float32Array, params: any) => {
    const { seed = 0, scale = 100, persistence = 0.5, octaves = 6, frequency = 0.01, amplitude = 1.0 } = params;

    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        const index = z * resolution + x;

        // 简化的噪声计算
        let noise = 0;
        let amp = amplitude;
        let freq = frequency;

        for (let o = 0; o < octaves; o++) {
          const nx = x * freq;
          const nz = z * freq;
          const noiseValue = Math.sin(nx + seed) * Math.cos(nz + seed * 0.5);
          noise += noiseValue * amp;
          amp *= persistence;
          freq *= 2;
        }

        // 归一化到[0, 1]范围
        heightData[index] = Math.min(1, Math.max(0, (noise + 1) * 0.5));
      }
    }
  };

  // 简化的山脉生成函数
  const generateMountainHeightData = (heightData: Float32Array, params: any) => {
    const { count = 3, height = 0.5, width = 20, roughness = 0.5, sharpness = 2 } = params;

    // 先生成基础地形
    generateNoiseHeightData(heightData, { seed: 123, scale: 80, persistence: 0.4, octaves: 5, frequency: 0.01, amplitude: 0.5 });

    // 添加山脉
    for (let i = 0; i < count; i++) {
      const centerX = Math.floor(Math.random() * resolution);
      const centerZ = Math.floor(Math.random() * resolution);
      const mountainHeight = height * (0.5 + Math.random() * 0.5);

      for (let z = 0; z < resolution; z++) {
        for (let x = 0; x < resolution; x++) {
          const dx = x - centerX;
          const dz = z - centerZ;
          const distance = Math.sqrt(dx * dx + dz * dz);

          if (distance <= width) {
            const index = z * resolution + x;
            const heightFactor = Math.pow(1 - (distance / width), sharpness);
            heightData[index] += mountainHeight * heightFactor;
            heightData[index] = Math.min(1, heightData[index]);
          }
        }
      }
    }
  };

  // 简化的峡谷生成函数
  const generateCanyonHeightData = (heightData: Float32Array, params: any) => {
    // 实现简化的峡谷生成逻辑
    // 先生成基础地形
    generateNoiseHeightData(heightData, { seed: 456, scale: 80, persistence: 0.4, octaves: 5, frequency: 0.01, amplitude: 0.5 });

    const { count = 2, depth = 0.3, width = 15, sinuosity = 0.7 } = params;

    // 添加峡谷
    for (let i = 0; i < count; i++) {
      const startX = Math.floor(Math.random() * resolution);
      const startZ = Math.floor(Math.random() * resolution);
      const dirX = Math.cos(Math.random() * Math.PI * 2);
      const dirZ = Math.sin(Math.random() * Math.PI * 2);

      let currentX = startX;
      let currentZ = startZ;

      for (let step = 0; step < resolution * 0.5; step++) {
        const x = Math.floor(currentX);
        const z = Math.floor(currentZ);

        if (x < 0 || x >= resolution || z < 0 || z >= resolution) {
          break;
        }

        for (let dz = -Math.ceil(width); dz <= Math.ceil(width); dz++) {
          for (let dx = -Math.ceil(width); dx <= Math.ceil(width); dx++) {
            const nx = x + dx;
            const nz = z + dz;

            if (nx < 0 || nx >= resolution || nz < 0 || nz >= resolution) {
              continue;
            }

            const distance = Math.sqrt(dx * dx + dz * dz);

            if (distance <= width) {
              const index = nz * resolution + nx;
              const depthFactor = Math.pow(1 - (distance / width), 2);
              heightData[index] -= depth * depthFactor;
              heightData[index] = Math.max(0, heightData[index]);
            }
          }
        }

        // 更新位置
        const randomAngle = (Math.random() - 0.5) * sinuosity;
        const newDirX = dirX * Math.cos(randomAngle) - dirZ * Math.sin(randomAngle);
        const newDirZ = dirX * Math.sin(randomAngle) + dirZ * Math.cos(randomAngle);

        currentX += newDirX;
        currentZ += newDirZ;
      }
    }
  };

  // 简化的河流生成函数
  const generateRiverHeightData = (heightData: Float32Array, params: any) => {
    // 实现简化的河流生成逻辑
    // 先生成基础地形
    generateNoiseHeightData(heightData, { seed: 789, scale: 80, persistence: 0.4, octaves: 5, frequency: 0.01, amplitude: 0.5 });

    const { count = 5, width = 10, depth = 0.2, sinuosity = 0.5 } = params;

    // 添加河流
    for (let i = 0; i < count; i++) {
      const startX = Math.floor(Math.random() * resolution);
      const startZ = Math.floor(Math.random() * resolution);
      const dirX = Math.cos(Math.random() * Math.PI * 2);
      const dirZ = Math.sin(Math.random() * Math.PI * 2);

      let currentX = startX;
      let currentZ = startZ;

      for (let step = 0; step < resolution * 0.5; step++) {
        const x = Math.floor(currentX);
        const z = Math.floor(currentZ);

        if (x < 0 || x >= resolution || z < 0 || z >= resolution) {
          break;
        }

        for (let dz = -Math.ceil(width); dz <= Math.ceil(width); dz++) {
          for (let dx = -Math.ceil(width); dx <= Math.ceil(width); dx++) {
            const nx = x + dx;
            const nz = z + dz;

            if (nx < 0 || nx >= resolution || nz < 0 || nz >= resolution) {
              continue;
            }

            const distance = Math.sqrt(dx * dx + dz * dz);

            if (distance <= width) {
              const index = nz * resolution + nx;
              const depthFactor = 1 - (distance / width);
              heightData[index] -= depth * depthFactor;
              heightData[index] = Math.max(0, heightData[index]);
            }
          }
        }

        // 更新位置
        const randomAngle = (Math.random() - 0.5) * sinuosity;
        const newDirX = dirX * Math.cos(randomAngle) - dirZ * Math.sin(randomAngle);
        const newDirZ = dirX * Math.sin(randomAngle) + dirZ * Math.cos(randomAngle);

        currentX += newDirX;
        currentZ += newDirZ;
      }
    }
  };

  // 简化的洞穴生成函数
  const generateCaveHeightData = (heightData: Float32Array, params: any) => {
    // 实现简化的洞穴生成逻辑
    // 先生成基础地形
    generateNoiseHeightData(heightData, { seed: 101, scale: 80, persistence: 0.4, octaves: 5, frequency: 0.01, amplitude: 0.5 });

    const { count = 3, size = 20, depth = 0.4, complexity = 0.6 } = params;

    // 添加洞穴
    for (let i = 0; i < count; i++) {
      const centerX = Math.floor(Math.random() * resolution);
      const centerZ = Math.floor(Math.random() * resolution);
      const radius = size * (0.5 + Math.random() * 0.5);

      for (let z = 0; z < resolution; z++) {
        for (let x = 0; x < resolution; x++) {
          const dx = x - centerX;
          const dz = z - centerZ;
          const distance = Math.sqrt(dx * dx + dz * dz);

          if (distance <= radius) {
            const index = z * resolution + x;
            let depthFactor = Math.pow(1 - (distance / radius), 2);

            // 添加复杂度
            if (complexity > 0) {
              const noiseValue = Math.sin(x * 0.1 + 123) * Math.cos(z * 0.1 + 456) * complexity;
              depthFactor *= (1 + noiseValue);
            }

            heightData[index] -= depth * depthFactor;
            heightData[index] = Math.max(0, heightData[index]);
          }
        }
      }
    }
  };

  // 简化的悬崖生成函数
  const generateCliffHeightData = (heightData: Float32Array, params: any) => {
    // 实现简化的悬崖生成逻辑
    // 先生成基础地形
    generateNoiseHeightData(heightData, { seed: 202, scale: 80, persistence: 0.4, octaves: 5, frequency: 0.01, amplitude: 0.5 });

    const { count = 2, height = 0.4, width = 25, steepness = 2.5 } = params;

    // 添加悬崖
    for (let i = 0; i < count; i++) {
      const startX = Math.floor(Math.random() * resolution);
      const startZ = Math.floor(Math.random() * resolution);
      const dirX = Math.cos(Math.random() * Math.PI * 2);
      const dirZ = Math.sin(Math.random() * Math.PI * 2);

      // 垂直方向
      const perpDirX = -dirZ;
      const perpDirZ = dirX;

      for (let step = 0; step < resolution * 0.3; step++) {
        const x = Math.floor(startX + step * dirX);
        const z = Math.floor(startZ + step * dirZ);

        if (x < 0 || x >= resolution || z < 0 || z >= resolution) {
          break;
        }

        for (let d = -Math.ceil(width); d <= Math.ceil(width); d++) {
          const nx = Math.floor(x + d * perpDirX);
          const nz = Math.floor(z + d * perpDirZ);

          if (nx < 0 || nx >= resolution || nz < 0 || nz >= resolution) {
            continue;
          }

          const distance = Math.abs(d);

          if (distance <= width) {
            const index = nz * resolution + nx;
            let heightFactor = 0;

            if (d > 0) {
              if (distance < width * 0.2) {
                heightFactor = 1 - Math.pow(distance / (width * 0.2), steepness);
              }
            } else {
              heightFactor = Math.pow(1 - Math.abs(d) / width, 1 / steepness);
            }

            heightData[index] += height * heightFactor;
            heightData[index] = Math.min(1, heightData[index]);
          }
        }
      }
    }
  };

  // 简化的火山生成函数
  const generateVolcanoHeightData = (heightData: Float32Array, params: any) => {
    // 实现简化的火山生成逻辑
    const { count = 1, height = 0.8, radius = 100, craterSize = 30, craterDepth = 0.3 } = params;

    // 添加火山
    for (let i = 0; i < count; i++) {
      const centerX = Math.floor(resolution / 2);
      const centerZ = Math.floor(resolution / 2);

      for (let z = 0; z < resolution; z++) {
        for (let x = 0; x < resolution; x++) {
          const dx = x - centerX;
          const dz = z - centerZ;
          const distance = Math.sqrt(dx * dx + dz * dz);

          if (distance <= radius) {
            const index = z * resolution + x;
            let heightFactor = 0;

            if (distance > radius * 0.8) {
              heightFactor = 1 - (distance - radius * 0.8) / (radius * 0.2);
            } else if (distance > radius * 0.2) {
              heightFactor = 0.2 + 0.8 * (1 - (distance - radius * 0.2) / (radius * 0.6));
            } else {
              heightFactor = 1.0;
            }

            heightData[index] += height * heightFactor;

            if (distance < craterSize) {
              const craterFactor = 1 - distance / craterSize;
              heightData[index] -= craterDepth * craterFactor;
            }

            heightData[index] = Math.min(1, Math.max(0, heightData[index]));
          }
        }
      }
    }
  };

  // 简化的组合生成函数
  const generateCombinationHeightData = (heightData: Float32Array, params: any) => {
    // 实现简化的组合生成逻辑
    const { baseTerrainType, baseTerrainParams, features } = params;

    // 生成基础地形
    switch (baseTerrainType) {
      case TerrainFeatureType.HILLS:
        generateNoiseHeightData(heightData, baseTerrainParams);
        break;
      case TerrainFeatureType.PLAIN:
        for (let i = 0; i < heightData.length; i++) {
          heightData[i] = 0.1;
        }
        break;
      case TerrainFeatureType.ISLAND:
        generateIslandHeightData(heightData, baseTerrainParams);
        break;
      default:
        generateNoiseHeightData(heightData, { seed: 123, scale: 80, persistence: 0.4, octaves: 5, frequency: 0.01, amplitude: 0.5 });
        break;
    }

    // 应用特征
    if (features && features.length > 0) {
      for (const feature of features) {
        if (Math.random() <= feature.weight) {
          switch (feature.type) {
            case TerrainFeatureType.MOUNTAIN:
              generateMountainHeightData(heightData, feature.params);
              break;
            case TerrainFeatureType.CANYON:
              generateCanyonHeightData(heightData, feature.params);
              break;
            case TerrainFeatureType.RIVER:
              generateRiverHeightData(heightData, feature.params);
              break;
            case TerrainFeatureType.CAVE:
              generateCaveHeightData(heightData, feature.params);
              break;
            case TerrainFeatureType.CLIFF:
              generateCliffHeightData(heightData, feature.params);
              break;
            case TerrainFeatureType.VOLCANO:
              generateVolcanoHeightData(heightData, feature.params);
              break;
            case TerrainFeatureType.UNDERGROUND_RIVER:
              generateUndergroundRiverHeightData(heightData, feature.params);
              break;
          }
        }
      }
    }
  };

  // 简化的地下河生成函数
  const generateUndergroundRiverHeightData = (heightData: Float32Array, params: any) => {
    // 实现简化的地下河生成逻辑
    // 先生成基础地形
    generateNoiseHeightData(heightData, { seed: 303, scale: 80, persistence: 0.4, octaves: 5, frequency: 0.01, amplitude: 0.5 });

    const { count = 3, width = 5, depth = 0.2, sinuosity = 0.6 } = params;

    // 地下河不会直接修改地形高度，但我们可以在预览中稍微降低一些区域来表示地下河的位置
    for (let i = 0; i < count; i++) {
      const startX = Math.floor(Math.random() * resolution);
      const startZ = Math.floor(Math.random() * resolution);
      const dirX = Math.cos(Math.random() * Math.PI * 2);
      const dirZ = Math.sin(Math.random() * Math.PI * 2);

      let currentX = startX;
      let currentZ = startZ;

      for (let step = 0; step < resolution * 0.3; step++) {
        const x = Math.floor(currentX);
        const z = Math.floor(currentZ);

        if (x < 0 || x >= resolution || z < 0 || z >= resolution) {
          break;
        }

        // 在地下河路径上稍微降低地形高度，仅用于预览
        for (let dz = -Math.ceil(width / 2); dz <= Math.ceil(width / 2); dz++) {
          for (let dx = -Math.ceil(width / 2); dx <= Math.ceil(width / 2); dx++) {
            const nx = x + dx;
            const nz = z + dz;

            if (nx < 0 || nx >= resolution || nz < 0 || nz >= resolution) {
              continue;
            }

            const distance = Math.sqrt(dx * dx + dz * dz);

            if (distance <= width / 2) {
              const index = nz * resolution + nx;
              const depthFactor = 0.05 * (1 - (distance / (width / 2)));
              heightData[index] -= depthFactor;
              heightData[index] = Math.max(0, heightData[index]);
            }
          }
        }

        // 更新位置
        const randomAngle = (Math.random() - 0.5) * sinuosity;
        const newDirX = dirX * Math.cos(randomAngle) - dirZ * Math.sin(randomAngle);
        const newDirZ = dirX * Math.sin(randomAngle) + dirZ * Math.cos(randomAngle);

        currentX += newDirX;
        currentZ += newDirZ;
      }
    }
  };

  // 简化的岛屿生成函数
  const generateIslandHeightData = (heightData: Float32Array, params: any) => {
    const { radius = resolution * 0.4, height = 0.8, falloff = 0.7 } = params;

    const centerX = resolution / 2;
    const centerZ = resolution / 2;

    for (let z = 0; z < resolution; z++) {
      for (let x = 0; x < resolution; x++) {
        const dx = x - centerX;
        const dz = z - centerZ;
        const distance = Math.sqrt(dx * dx + dz * dz);

        if (distance <= radius) {
          const index = z * resolution + x;
          const distanceFactor = distance / radius;
          const heightFactor = Math.pow(1 - distanceFactor, falloff);

          heightData[index] = height * heightFactor;
        } else {
          const index = z * resolution + x;
          heightData[index] = 0;
        }
      }
    }
  };

  return (
    <div className="terrain-preview" ref={containerRef} style={{ width, height }}>
      {!enabled && (
        <div className="preview-disabled">
          <span>预览已禁用</span>
        </div>
      )}
    </div>
  );
};

export default TerrainPreview;
