/**
 * 场景图层示例
 * 展示如何使用场景图层功能
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { Entity } from '../../src/core/Entity';
import { Scene, SceneLayer, SceneLayerManager } from '../../src/scene';

export class SceneLayerExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 场景图层管理器 */
  private layerManager: SceneLayerManager;
  
  /** UI元素 */
  private ui: HTMLElement;
  
  /** 图层列表元素 */
  private layerListElement: HTMLElement;
  
  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建场景图层示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建场景
    this.scene = this.engine.getWorld().createScene('场景图层示例');
    this.engine.getWorld().setActiveScene(this.scene);
    
    // 获取场景图层管理器
    this.layerManager = this.scene.getLayerManager();
    
    // 创建UI
    this.ui = this.createUI();
    this.layerListElement = document.createElement('div');
    this.ui.appendChild(this.layerListElement);
  }

  /**
   * 初始化示例
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }
    
    // 创建场景内容
    await this.createSceneContent();
    
    // 初始化场景图层管理器
    this.layerManager.initialize();
    
    // 更新图层列表
    this.updateLayerList();
    
    // 启动引擎
    this.engine.start();
    
    this.initialized = true;
  }

  /**
   * 创建场景内容
   */
  private async createSceneContent(): Promise<void> {
    // 创建图层
    const defaultLayer = this.layerManager.getDefaultLayer()!;
    const buildingsLayer = this.layerManager.createLayer({
      id: 'buildings',
      name: '建筑物',
      visible: true,
      locked: false,
      order: 1,
      tags: ['建筑', '静态']
    });
    const vegetationLayer = this.layerManager.createLayer({
      id: 'vegetation',
      name: '植被',
      visible: true,
      locked: false,
      order: 2,
      tags: ['植被', '静态']
    });
    const charactersLayer = this.layerManager.createLayer({
      id: 'characters',
      name: '角色',
      visible: true,
      locked: false,
      order: 3,
      tags: ['角色', '动态']
    });
    
    // 设置图层颜色
    buildingsLayer.setColor(new THREE.Color(0xff0000));
    vegetationLayer.setColor(new THREE.Color(0x00ff00));
    charactersLayer.setColor(new THREE.Color(0x0000ff));
    
    // 创建地面
    const ground = new Entity('地面');
    ground.getTransform().setPosition(0, -1, 0);
    ground.getTransform().setScale(20, 0.1, 20);
    
    // 添加网格组件
    const groundGeometry = new THREE.BoxGeometry(1, 1, 1);
    const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x808080 });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.addComponent('MeshComponent', { mesh: groundMesh });
    
    // 添加到场景和默认图层
    this.scene.addEntity(ground);
    defaultLayer.addEntity(ground);
    
    // 创建建筑物
    for (let i = 0; i < 5; i++) {
      const building = new Entity(`建筑物_${i}`);
      building.getTransform().setPosition(-5 + i * 2.5, 1, -5);
      building.getTransform().setScale(1, 2 + Math.random() * 2, 1);
      
      // 添加网格组件
      const buildingGeometry = new THREE.BoxGeometry(1, 1, 1);
      const buildingMaterial = new THREE.MeshStandardMaterial({ color: 0xcc8866 });
      const buildingMesh = new THREE.Mesh(buildingGeometry, buildingMaterial);
      building.addComponent('MeshComponent', { mesh: buildingMesh });
      
      // 添加到场景和建筑物图层
      this.scene.addEntity(building);
      buildingsLayer.addEntity(building);
    }
    
    // 创建植被
    for (let i = 0; i < 10; i++) {
      const tree = new Entity(`树木_${i}`);
      tree.getTransform().setPosition(-8 + i * 1.8, 0, 5);
      tree.getTransform().setScale(0.5, 2, 0.5);
      
      // 添加网格组件
      const trunkGeometry = new THREE.CylinderGeometry(0.1, 0.2, 1, 8);
      const trunkMaterial = new THREE.MeshStandardMaterial({ color: 0x8B4513 });
      const trunkMesh = new THREE.Mesh(trunkGeometry, trunkMaterial);
      trunkMesh.position.y = 0.5;
      
      const leavesGeometry = new THREE.ConeGeometry(0.5, 1, 8);
      const leavesMaterial = new THREE.MeshStandardMaterial({ color: 0x228B22 });
      const leavesMesh = new THREE.Mesh(leavesGeometry, leavesMaterial);
      leavesMesh.position.y = 1.5;
      
      const treeGroup = new THREE.Group();
      treeGroup.add(trunkMesh);
      treeGroup.add(leavesMesh);
      
      tree.addComponent('MeshComponent', { mesh: treeGroup });
      
      // 添加到场景和植被图层
      this.scene.addEntity(tree);
      vegetationLayer.addEntity(tree);
    }
    
    // 创建角色
    const character = new Entity('角色');
    character.getTransform().setPosition(0, 0, 0);
    character.getTransform().setScale(0.5, 0.5, 0.5);
    
    // 添加网格组件
    const characterGeometry = new THREE.BoxGeometry(1, 2, 1);
    const characterMaterial = new THREE.MeshStandardMaterial({ color: 0x0088ff });
    const characterMesh = new THREE.Mesh(characterGeometry, characterMaterial);
    character.addComponent('MeshComponent', { mesh: characterMesh });
    
    // 添加到场景和角色图层
    this.scene.addEntity(character);
    charactersLayer.addEntity(character);
    
    // 设置环境光
    this.scene.setAmbientLight(new THREE.Color(0xffffff), 0.5);
    
    // 添加方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(5, 10, 5);
    this.scene.getThreeScene().add(directionalLight);
  }

  /**
   * 创建UI
   * @returns UI元素
   */
  private createUI(): HTMLElement {
    const ui = document.createElement('div');
    ui.style.position = 'absolute';
    ui.style.top = '10px';
    ui.style.left = '10px';
    ui.style.width = '300px';
    ui.style.maxHeight = '80vh';
    ui.style.overflowY = 'auto';
    ui.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    ui.style.color = 'white';
    ui.style.padding = '10px';
    ui.style.borderRadius = '5px';
    ui.style.fontFamily = 'Arial, sans-serif';
    
    // 创建标题
    const title = document.createElement('h2');
    title.textContent = '场景图层示例';
    title.style.margin = '0 0 10px 0';
    ui.appendChild(title);
    
    // 创建描述
    const description = document.createElement('p');
    description.textContent = '这个示例展示了如何使用场景图层功能。点击图层可以切换可见性。';
    description.style.margin = '0 0 10px 0';
    ui.appendChild(description);
    
    // 创建查询按钮
    const queryContainer = document.createElement('div');
    queryContainer.style.marginBottom = '10px';
    
    const queryStaticButton = document.createElement('button');
    queryStaticButton.textContent = '查询静态图层';
    queryStaticButton.style.marginRight = '5px';
    queryStaticButton.addEventListener('click', () => {
      this.queryStaticLayers();
    });
    queryContainer.appendChild(queryStaticButton);
    
    const queryDynamicButton = document.createElement('button');
    queryDynamicButton.textContent = '查询动态图层';
    queryDynamicButton.style.marginRight = '5px';
    queryDynamicButton.addEventListener('click', () => {
      this.queryDynamicLayers();
    });
    queryContainer.appendChild(queryDynamicButton);
    
    const resetButton = document.createElement('button');
    resetButton.textContent = '重置';
    resetButton.addEventListener('click', () => {
      this.resetLayers();
    });
    queryContainer.appendChild(resetButton);
    
    ui.appendChild(queryContainer);
    
    // 添加到文档
    document.body.appendChild(ui);
    
    return ui;
  }

  /**
   * 更新图层列表
   */
  private updateLayerList(): void {
    // 清空图层列表
    this.layerListElement.innerHTML = '';
    
    // 获取所有图层
    const layers = this.layerManager.getLayers();
    
    // 按顺序排序
    layers.sort((a, b) => a.getOrder() - b.getOrder());
    
    // 创建图层列表标题
    const listTitle = document.createElement('h3');
    listTitle.textContent = '图层列表';
    listTitle.style.margin = '10px 0';
    this.layerListElement.appendChild(listTitle);
    
    // 创建图层列表
    for (const layer of layers) {
      const layerItem = document.createElement('div');
      layerItem.style.padding = '5px';
      layerItem.style.marginBottom = '5px';
      layerItem.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
      layerItem.style.borderRadius = '3px';
      layerItem.style.cursor = 'pointer';
      layerItem.style.display = 'flex';
      layerItem.style.alignItems = 'center';
      
      // 创建可见性图标
      const visibilityIcon = document.createElement('span');
      visibilityIcon.textContent = layer.isVisible() ? '👁️' : '👁️‍🗨️';
      visibilityIcon.style.marginRight = '10px';
      layerItem.appendChild(visibilityIcon);
      
      // 创建图层颜色指示器
      const colorIndicator = document.createElement('span');
      const color = layer.getColor();
      colorIndicator.style.display = 'inline-block';
      colorIndicator.style.width = '12px';
      colorIndicator.style.height = '12px';
      colorIndicator.style.backgroundColor = `rgb(${color.r * 255}, ${color.g * 255}, ${color.b * 255})`;
      colorIndicator.style.marginRight = '10px';
      colorIndicator.style.borderRadius = '2px';
      layerItem.appendChild(colorIndicator);
      
      // 创建图层名称
      const layerName = document.createElement('span');
      layerName.textContent = `${layer.name} (${layer.getEntityCount()}个实体)`;
      layerItem.appendChild(layerName);
      
      // 添加点击事件
      layerItem.addEventListener('click', () => {
        layer.setVisible(!layer.isVisible());
        this.updateLayerList();
      });
      
      // 添加到图层列表
      this.layerListElement.appendChild(layerItem);
    }
  }

  /**
   * 查询静态图层
   */
  private queryStaticLayers(): void {
    // 查询带有"静态"标签的图层
    const staticLayers = this.layerManager.queryLayers({
      tagFilter: ['静态'],
      includeInvisible: true
    });
    
    console.log('静态图层:', staticLayers);
    
    // 显示静态图层，隐藏其他图层
    const allLayers = this.layerManager.getLayers();
    for (const layer of allLayers) {
      const isStatic = staticLayers.includes(layer);
      layer.setVisible(isStatic);
    }
    
    // 更新图层列表
    this.updateLayerList();
  }

  /**
   * 查询动态图层
   */
  private queryDynamicLayers(): void {
    // 查询带有"动态"标签的图层
    const dynamicLayers = this.layerManager.queryLayers({
      tagFilter: ['动态'],
      includeInvisible: true
    });
    
    console.log('动态图层:', dynamicLayers);
    
    // 显示动态图层，隐藏其他图层
    const allLayers = this.layerManager.getLayers();
    for (const layer of allLayers) {
      const isDynamic = dynamicLayers.includes(layer);
      layer.setVisible(isDynamic);
    }
    
    // 更新图层列表
    this.updateLayerList();
  }

  /**
   * 重置图层
   */
  private resetLayers(): void {
    // 显示所有图层
    const allLayers = this.layerManager.getLayers();
    for (const layer of allLayers) {
      layer.setVisible(true);
    }
    
    // 更新图层列表
    this.updateLayerList();
  }

  /**
   * 销毁示例
   */
  public dispose(): void {
    // 停止引擎
    this.engine.stop();
    
    // 移除UI
    if (this.ui && this.ui.parentNode) {
      this.ui.parentNode.removeChild(this.ui);
    }
    
    // 销毁引擎
    this.engine.dispose();
  }
}
