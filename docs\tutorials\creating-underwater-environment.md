# 创建水下环境教程

本教程将指导您使用DL（Digital Learning）引擎创建一个逼真的水下环境，包括地下湖泊、水下粒子效果和特殊光照效果。

## 目录

- [准备工作](#准备工作)
- [创建地下洞穴](#创建地下洞穴)
- [添加地下湖泊](#添加地下湖泊)
- [设置水体材质](#设置水体材质)
- [添加水下粒子](#添加水下粒子)
- [创建水下光照](#创建水下光照)
- [添加水下植物](#添加水下植物)
- [优化性能](#优化性能)
- [最终效果](#最终效果)

## 准备工作

在开始之前，请确保您已经：

1. 安装了最新版本的DL（Digital Learning）引擎编辑器。
2. 熟悉基本的场景编辑操作。
3. 了解基本的水体系统概念。

## 创建地下洞穴

首先，我们需要创建一个地下洞穴作为我们的水下环境的容器。

### 步骤1：创建新场景

1. 打开DL（Digital Learning）引擎编辑器。
2. 创建一个新场景：`文件 > 新建 > 场景`。
3. 保存场景为"UndergroundCave"。

### 步骤2：创建洞穴地形

1. 在场景中添加地形：`实体 > 添加 > 地形`。
2. 选择地形实体，在属性面板中找到"地形编辑器"。
3. 使用"挖掘"工具创建一个洞穴形状：
   - 设置笔刷大小为20。
   - 设置强度为0.8。
   - 在地形中心区域绘制一个大致圆形的凹陷。
4. 使用"平滑"工具使洞穴边缘更加自然：
   - 设置笔刷大小为15。
   - 设置强度为0.5。
   - 在洞穴边缘进行平滑处理。
5. 使用"细节"工具添加一些岩石纹理：
   - 设置笔刷大小为10。
   - 设置强度为0.3。
   - 在洞穴内部添加一些不规则的凸起和凹陷。

### 步骤3：设置洞穴材质

1. 选择地形实体，在属性面板中找到"材质"选项卡。
2. 添加岩石材质：
   - 点击"添加材质层"。
   - 选择"岩石"材质。
   - 设置贴图重复为10。
3. 添加苔藓材质作为细节：
   - 点击"添加材质层"。
   - 选择"苔藓"材质。
   - 设置混合模式为"覆盖"。
   - 设置强度为0.3。
4. 调整材质参数使其看起来更像地下洞穴：
   - 降低整体亮度。
   - 增加粗糙度。
   - 添加一些湿润效果。

## 添加地下湖泊

现在，我们将在洞穴中添加一个地下湖泊。

### 步骤1：创建水体实体

1. 在场景中添加水体：`实体 > 添加 > 水体`。
2. 将水体放置在洞穴的底部。
3. 调整水体的大小以适应洞穴形状：
   - 宽度：80
   - 高度：5
   - 深度：80

### 步骤2：设置水体类型

1. 选择水体实体，在属性面板中找到"水体组件"。
2. 设置水体类型为"地下湖泊"。
3. 调整基本参数：
   - 密度：1000
   - 粘度：1.2
   - 温度：15
4. 设置波动参数：
   - 振幅：0.05
   - 频率：0.2
   - 速度：0.1

## 设置水体材质

接下来，我们将设置水体的材质，使其看起来像真实的地下湖泊。

### 步骤1：打开水体材质编辑器

1. 选择水体实体，在属性面板中找到"水体材质"选项卡。
2. 点击"编辑材质"按钮打开水体材质编辑器。

### 步骤2：使用预设

1. 在水体材质编辑器中，点击"预设"按钮。
2. 在预设选择器中，选择"地下湖泊"预设。
3. 点击"应用"按钮应用预设。

### 步骤3：自定义材质

1. 调整基本属性：
   - 颜色：深蓝色（#225577）
   - 透明度：0.9
   - 反射率：0.3
   - 折射率：0.98
2. 调整波动属性：
   - 波动强度：0.02
   - 波动速度：0.2
   - 波动尺寸：4.0
3. 调整深度属性：
   - 深度：8.0
   - 深水颜色：深蓝色（#001122）
   - 浅水颜色：浅蓝色（#336688）
4. 调整特效属性：
   - 禁用因果波纹
   - 禁用泡沫
   - 启用水下雾效，密度设为0.1
   - 启用水下扭曲，强度设为0.05

### 步骤4：预览效果

1. 使用实时预览功能查看材质效果。
2. 根据需要进一步调整参数。
3. 点击"保存"按钮保存材质设置。

## 添加水下粒子

现在，我们将添加水下粒子效果，如气泡和悬浮物，增强水下环境的真实感。

### 步骤1：打开水下效果编辑器

1. 选择水体实体，在属性面板中找到"水下效果"选项卡。
2. 点击"编辑效果"按钮打开水下效果编辑器。

### 步骤2：添加气泡粒子

1. 点击"添加粒子组"按钮。
2. 设置粒子组类型为"气泡"。
3. 调整参数：
   - 数量：200
   - 大小范围：0.05-0.2
   - 颜色：白色（#ffffff）
   - 透明度：0.6
   - 生命周期：2-5秒
   - 速度：0.1-0.3
   - 加速度：向上（0, 0.05, 0）
4. 设置发射区域：
   - 形状：盒体
   - 大小：与水体大小相同，但高度减半
   - 位置：水体底部

### 步骤3：添加悬浮物粒子

1. 点击"添加粒子组"按钮。
2. 设置粒子组类型为"悬浮物"。
3. 调整参数：
   - 数量：100
   - 大小范围：0.1-0.3
   - 颜色：灰白色（#cccccc）
   - 透明度：0.4
   - 生命周期：5-10秒
   - 速度：0.02-0.05
   - 旋转：启用
   - 旋转速度：0.02-0.05
4. 设置发射区域：
   - 形状：盒体
   - 大小：与水体大小相同，但高度减半
   - 位置：水体底部

### 步骤4：预览效果

1. 使用预览功能查看粒子效果。
2. 根据需要调整参数。
3. 点击"应用"按钮应用设置。

## 创建水下光照

接下来，我们将添加特殊的水下光照效果，增强地下湖泊的氛围。

### 步骤1：打开光照编辑器

1. 在场景中选择水体实体。
2. 在属性面板中找到"光照"选项卡。
3. 点击"编辑光照"按钮打开光照编辑器。

### 步骤2：添加环境光

1. 点击"添加光源"按钮。
2. 选择"环境光"类型。
3. 设置参数：
   - 颜色：深蓝色（#112233）
   - 强度：0.3

### 步骤3：添加体积光

1. 点击"添加光源"按钮。
2. 选择"体积光"类型。
3. 设置参数：
   - 位置：水面上方
   - 方向：向下
   - 颜色：浅蓝色（#66ccff）
   - 强度：1.2
   - 角度：30度
   - 衰减：2.0
   - 距离：50
4. 启用动态效果：
   - 强度变化：0.2
   - 速度：0.5

### 步骤4：添加光线

1. 点击"添加光源"按钮。
2. 选择"光线"类型。
3. 设置参数：
   - 位置：洞穴顶部的一个小开口
   - 方向：向下
   - 颜色：白色（#ffffff）
   - 强度：1.5
   - 衰减：1.5
4. 启用动态效果：
   - 方向变化：0.1
   - 速度：0.3

### 步骤5：预览效果

1. 使用预览功能查看光照效果。
2. 根据需要调整参数。
3. 点击"应用"按钮应用设置。

## 添加水下植物

为了使水下环境更加丰富，我们将添加一些水下植物。

### 步骤1：添加水草

1. 在场景中添加模型：`实体 > 添加 > 模型`。
2. 从资源库中选择"水草"模型。
3. 将水草放置在湖底。
4. 调整大小和旋转使其看起来自然。
5. 复制水草并分布在湖底的不同位置。

### 步骤2：添加珊瑚

1. 在场景中添加模型：`实体 > 添加 > 模型`。
2. 从资源库中选择"珊瑚"模型。
3. 将珊瑚放置在湖底的岩石附近。
4. 调整大小和旋转。
5. 复制珊瑚并分布在湖底的不同位置。

### 步骤3：添加水下岩石

1. 在场景中添加模型：`实体 > 添加 > 模型`。
2. 从资源库中选择"岩石"模型。
3. 将岩石放置在湖底。
4. 调整大小和旋转。
5. 复制岩石并分布在湖底的不同位置。

## 优化性能

最后，我们将优化场景性能，确保在各种硬件上都能流畅运行。

### 步骤1：优化水体物理

1. 选择水体实体，在属性面板中找到"水体物理"选项卡。
2. 启用空间分区。
3. 设置动态更新频率：
   - 最小更新频率：1
   - 最大更新频率：10
   - 距离因子：0.1

### 步骤2：优化粒子系统

1. 选择水体实体，在属性面板中找到"水下效果"选项卡。
2. 启用自动性能调整。
3. 设置最大粒子数量为5000。
4. 启用GPU加速。

### 步骤3：优化光照系统

1. 选择水体实体，在属性面板中找到"光照"选项卡。
2. 调整阴影质量：
   - 阴影贴图大小：1024
   - 阴影类型：PCF
3. 对于远处的光源，禁用阴影。

### 步骤4：测试性能

1. 使用性能监视器检查帧率和资源使用情况。
2. 根据需要进一步调整参数。
3. 在不同视角和距离测试场景性能。

## 最终效果

恭喜！您已经成功创建了一个逼真的地下水下环境。最终效果应该包括：

- 自然的地下洞穴地形
- 逼真的地下湖泊水体
- 水下气泡和悬浮物粒子效果
- 大气的水下光照效果
- 丰富的水下植物和岩石
- 良好的性能优化

您可以进一步扩展这个环境，例如：

- 添加水下生物，如鱼类
- 创建水下洞穴系统
- 添加交互式元素，如可收集的物品
- 实现水下声音效果
- 添加水下相机效果，如扭曲和颜色调整

希望本教程对您有所帮助！如有任何问题，请参考DL（Digital Learning）引擎的官方文档或在社区论坛中提问。
