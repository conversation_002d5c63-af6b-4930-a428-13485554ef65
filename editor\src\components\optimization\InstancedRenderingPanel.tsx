/**
 * 实例化渲染面板组件
 * 用于配置和监控实例化渲染系统
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Row, Col, Switch, InputNumber, Button, Tooltip, Divider, Space, Tag, Alert, Collapse, Progress, Statistic, Table, Select } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  ReloadOutlined,
  SettingOutlined,
  EyeOutlined,
  LineChartOutlined,
  ThunderboltOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  BarChartOutlined,
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { AdvancedInstancedRenderingSystem } from '../../../engine/src/rendering/optimization/AdvancedInstancedRenderingSystem';
import { EngineService } from '../../services/EngineService';
import { SceneService } from '../../services/SceneService';
import './InstancedRenderingPanel.less';

const { Panel } = Collapse;
const { Option } = Select;

/**
 * 实例组信息接口
 */
interface InstanceGroupInfo {
  id: string;
  name: string;
  instanceCount: number;
  geometryType: string;
  materialType: string;
  visible: boolean;
  memoryUsage: number;
}

/**
 * 实例化渲染面板组件
 */
const InstancedRenderingPanel: React.FC = () => {
  const { t } = useTranslation();
  
  // 状态
  const [isEnabled, setIsEnabled] = useState(true);
  const [maxBatchSize, setMaxBatchSize] = useState(1000);
  const [useGPUInstanceUpdate, setUseGPUInstanceUpdate] = useState(true);
  const [useInstanceCulling, setUseInstanceCulling] = useState(true);
  const [useInstanceLOD, setUseInstanceLOD] = useState(true);
  const [useInstanceShadow, setUseInstanceShadow] = useState(true);
  const [supportCustomShaders, setSupportCustomShaders] = useState(true);
  const [supportMaterialVariants, setSupportMaterialVariants] = useState(true);
  const [supportInstanceAnimation, setSupportInstanceAnimation] = useState(true);
  const [useAutoInstancing, setUseAutoInstancing] = useState(false);
  const [instanceThreshold, setInstanceThreshold] = useState(10);
  const [instanceGroups, setInstanceGroups] = useState<InstanceGroupInfo[]>([]);
  const [totalInstances, setTotalInstances] = useState(0);
  const [memoryUsage, setMemoryUsage] = useState(0);
  const [isInitialized, setIsInitialized] = useState(false);
  const [renderingSystem, setRenderingSystem] = useState<AdvancedInstancedRenderingSystem | null>(null);
  
  // 引用
  const timerRef = useRef<number | null>(null);
  
  // 初始化
  useEffect(() => {
    // 获取引擎
    const engine = EngineService.getInstance().getEngine();
    if (!engine) {
      return;
    }
    
    // 获取实例化渲染系统
    let system = engine.getSystem('AdvancedInstancedRenderingSystem') as AdvancedInstancedRenderingSystem;
    
    // 如果系统不存在，则创建
    if (!system) {
      system = new AdvancedInstancedRenderingSystem({
        enabled: isEnabled,
        maxBatchSize,
        useGPUInstanceUpdate,
        useInstanceCulling,
        useInstanceLOD,
        useInstanceShadow,
        supportCustomShaders,
        supportMaterialVariants,
        supportInstanceAnimation,
        useAutoInstancing,
        instanceThreshold
      });
      
      // 添加到引擎
      engine.addSystem(system);
    }
    
    // 设置系统
    setRenderingSystem(system);
    
    // 更新状态
    setIsEnabled(system.isEnabled());
    
    // 加载实例组信息
    loadInstanceGroups();
    
    // 设置定时器定期更新实例组信息
    timerRef.current = window.setInterval(() => {
      loadInstanceGroups();
    }, 2000);
    
    setIsInitialized(true);
    
    // 清理函数
    return () => {
      // 清除定时器
      if (timerRef.current !== null) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, []);
  
  // 加载实例组信息
  const loadInstanceGroups = () => {
    if (!renderingSystem) {
      return;
    }
    
    // 这里应该从系统获取实例组信息
    // 这只是一个示例实现
    const groups: InstanceGroupInfo[] = [];
    let totalCount = 0;
    let totalMemory = 0;
    
    // 假设系统有一个方法来获取实例组
    const systemGroups = (renderingSystem as any).getBatchGroups?.() || [];
    
    for (const group of systemGroups) {
      const info: InstanceGroupInfo = {
        id: group.id,
        name: group.name || group.id,
        instanceCount: group.instanceCount || 0,
        geometryType: group.geometryType || 'Unknown',
        materialType: group.materialType || 'Unknown',
        visible: group.visible !== false,
        memoryUsage: group.memoryUsage || 0
      };
      
      groups.push(info);
      totalCount += info.instanceCount;
      totalMemory += info.memoryUsage;
    }
    
    setInstanceGroups(groups);
    setTotalInstances(totalCount);
    setMemoryUsage(totalMemory);
  };
  
  // 启用/禁用系统
  const handleEnableChange = (checked: boolean) => {
    if (renderingSystem) {
      renderingSystem.setEnabled(checked);
      setIsEnabled(checked);
    }
  };
  
  // 设置最大批处理大小
  const handleMaxBatchSizeChange = (value: number) => {
    setMaxBatchSize(value);
    if (renderingSystem) {
      // 假设有这个方法
      (renderingSystem as any).maxBatchSize = value;
    }
  };
  
  // 启用/禁用GPU实例更新
  const handleGPUInstanceUpdateChange = (checked: boolean) => {
    setUseGPUInstanceUpdate(checked);
    if (renderingSystem) {
      // 假设有这个方法
      (renderingSystem as any).useGPUInstanceUpdate = checked;
    }
  };
  
  // 启用/禁用实例剔除
  const handleInstanceCullingChange = (checked: boolean) => {
    setUseInstanceCulling(checked);
    if (renderingSystem) {
      // 假设有这个方法
      (renderingSystem as any).useInstanceCulling = checked;
    }
  };
  
  // 启用/禁用实例LOD
  const handleInstanceLODChange = (checked: boolean) => {
    setUseInstanceLOD(checked);
    if (renderingSystem) {
      // 假设有这个方法
      (renderingSystem as any).useInstanceLOD = checked;
    }
  };
  
  // 启用/禁用实例阴影
  const handleInstanceShadowChange = (checked: boolean) => {
    setUseInstanceShadow(checked);
    if (renderingSystem) {
      // 假设有这个方法
      (renderingSystem as any).useInstanceShadow = checked;
    }
  };
  
  // 启用/禁用自定义着色器
  const handleCustomShadersChange = (checked: boolean) => {
    setSupportCustomShaders(checked);
    if (renderingSystem) {
      // 假设有这个方法
      (renderingSystem as any).supportCustomShaders = checked;
    }
  };
  
  // 启用/禁用材质变体
  const handleMaterialVariantsChange = (checked: boolean) => {
    setSupportMaterialVariants(checked);
    if (renderingSystem) {
      // 假设有这个方法
      (renderingSystem as any).supportMaterialVariants = checked;
    }
  };
  
  // 启用/禁用实例动画
  const handleInstanceAnimationChange = (checked: boolean) => {
    setSupportInstanceAnimation(checked);
    if (renderingSystem) {
      // 假设有这个方法
      (renderingSystem as any).supportInstanceAnimation = checked;
    }
  };
  
  // 启用/禁用自动实例化
  const handleAutoInstancingChange = (checked: boolean) => {
    setUseAutoInstancing(checked);
    if (renderingSystem) {
      // 假设有这个方法
      (renderingSystem as any).useAutoInstancing = checked;
      
      // 如果启用自动实例化，扫描场景
      if (checked && (renderingSystem as any).scanSceneForInstancing) {
        (renderingSystem as any).scanSceneForInstancing();
      }
    }
  };
  
  // 设置实例阈值
  const handleInstanceThresholdChange = (value: number) => {
    setInstanceThreshold(value);
    if (renderingSystem) {
      // 假设有这个方法
      (renderingSystem as any).instanceThreshold = value;
    }
  };
  
  // 删除实例组
  const handleDeleteGroup = (groupId: string) => {
    if (!renderingSystem) {
      return;
    }
    
    // 假设有这个方法
    if ((renderingSystem as any).removeBatchGroup) {
      (renderingSystem as any).removeBatchGroup(groupId);
      loadInstanceGroups();
    }
  };
  
  // 扫描场景
  const handleScanScene = () => {
    if (!renderingSystem) {
      return;
    }
    
    // 假设有这个方法
    if ((renderingSystem as any).scanSceneForInstancing) {
      (renderingSystem as any).scanSceneForInstancing();
      loadInstanceGroups();
    }
  };
  
  // 优化实例
  const handleOptimizeInstances = () => {
    if (!renderingSystem) {
      return;
    }
    
    // 假设有这个方法
    if ((renderingSystem as any).optimizeInstances) {
      (renderingSystem as any).optimizeInstances();
      loadInstanceGroups();
    }
  };
  
  // 表格列定义
  const columns = [
    {
      title: t('optimization.instancedRendering.name'),
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: t('optimization.instancedRendering.instanceCount'),
      dataIndex: 'instanceCount',
      key: 'instanceCount',
      sorter: (a: InstanceGroupInfo, b: InstanceGroupInfo) => a.instanceCount - b.instanceCount,
    },
    {
      title: t('optimization.instancedRendering.geometryType'),
      dataIndex: 'geometryType',
      key: 'geometryType',
    },
    {
      title: t('optimization.instancedRendering.materialType'),
      dataIndex: 'materialType',
      key: 'materialType',
    },
    {
      title: t('optimization.instancedRendering.memoryUsage'),
      dataIndex: 'memoryUsage',
      key: 'memoryUsage',
      render: (text: number) => `${(text / 1024 / 1024).toFixed(2)} MB`,
      sorter: (a: InstanceGroupInfo, b: InstanceGroupInfo) => a.memoryUsage - b.memoryUsage,
    },
    {
      title: t('optimization.instancedRendering.visible'),
      dataIndex: 'visible',
      key: 'visible',
      render: (text: boolean) => <Switch size="small" checked={text} />,
    },
    {
      title: t('common.actions'),
      key: 'actions',
      render: (text: any, record: InstanceGroupInfo) => (
        <Button
          icon={<DeleteOutlined />}
          size="small"
          danger
          onClick={() => handleDeleteGroup(record.id)}
        />
      ),
    },
  ];
  
  // 如果未初始化，显示加载中
  if (!isInitialized) {
    return (
      <Card title={t('optimization.instancedRendering.title')} className="instanced-rendering-panel">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <div className="loading-text">{t('common.loading')}</div>
        </div>
      </Card>
    );
  }
  
  return (
    <Card 
      title={
        <Space>
          <span>{t('optimization.instancedRendering.title')}</span>
          <Switch 
            checked={isEnabled} 
            onChange={handleEnableChange} 
            size="small"
          />
        </Space>
      } 
      className="instanced-rendering-panel"
      extra={
        <Space>
          <Tooltip title={t('optimization.instancedRendering.scanScene')}>
            <Button 
              icon={<ReloadOutlined />} 
              size="small"
              onClick={handleScanScene}
              disabled={!isEnabled}
            />
          </Tooltip>
          <Tooltip title={t('optimization.instancedRendering.optimizeInstances')}>
            <Button 
              icon={<ThunderboltOutlined />} 
              size="small"
              onClick={handleOptimizeInstances}
              disabled={!isEnabled}
            />
          </Tooltip>
        </Space>
      }
    >
      <Collapse defaultActiveKey={['1', '2']}>
        <Panel header={t('optimization.instancedRendering.statistics')} key="1">
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Statistic 
                title={t('optimization.instancedRendering.totalInstances')} 
                value={totalInstances} 
              />
            </Col>
            <Col span={8}>
              <Statistic 
                title={t('optimization.instancedRendering.instanceGroups')} 
                value={instanceGroups.length} 
              />
            </Col>
            <Col span={8}>
              <Statistic 
                title={t('optimization.instancedRendering.memoryUsage')} 
                value={(memoryUsage / 1024 / 1024).toFixed(2)} 
                suffix="MB"
              />
            </Col>
          </Row>
        </Panel>
        
        <Panel header={t('optimization.instancedRendering.instanceGroups')} key="2">
          <Table 
            dataSource={instanceGroups} 
            columns={columns} 
            rowKey="id"
            size="small"
            pagination={{ pageSize: 5 }}
          />
        </Panel>
        
        <Panel header={t('optimization.instancedRendering.settings')} key="3">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.instancedRendering.maxBatchSize')}
                  <Tooltip title={t('optimization.instancedRendering.maxBatchSizeTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <InputNumber
                    min={10}
                    max={10000}
                    step={10}
                    value={maxBatchSize}
                    onChange={handleMaxBatchSizeChange}
                    disabled={!isEnabled}
                    style={{ width: '100%' }}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.instancedRendering.instanceThreshold')}
                  <Tooltip title={t('optimization.instancedRendering.instanceThresholdTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <InputNumber
                    min={2}
                    max={100}
                    step={1}
                    value={instanceThreshold}
                    onChange={handleInstanceThresholdChange}
                    disabled={!isEnabled || !useAutoInstancing}
                    style={{ width: '100%' }}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.instancedRendering.useGPUInstanceUpdate')}
                  <Tooltip title={t('optimization.instancedRendering.useGPUInstanceUpdateTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useGPUInstanceUpdate} 
                    onChange={handleGPUInstanceUpdateChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.instancedRendering.useInstanceCulling')}
                  <Tooltip title={t('optimization.instancedRendering.useInstanceCullingTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useInstanceCulling} 
                    onChange={handleInstanceCullingChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.instancedRendering.useInstanceLOD')}
                  <Tooltip title={t('optimization.instancedRendering.useInstanceLODTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useInstanceLOD} 
                    onChange={handleInstanceLODChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.instancedRendering.useInstanceShadow')}
                  <Tooltip title={t('optimization.instancedRendering.useInstanceShadowTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useInstanceShadow} 
                    onChange={handleInstanceShadowChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.instancedRendering.supportCustomShaders')}
                  <Tooltip title={t('optimization.instancedRendering.supportCustomShadersTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={supportCustomShaders} 
                    onChange={handleCustomShadersChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.instancedRendering.supportMaterialVariants')}
                  <Tooltip title={t('optimization.instancedRendering.supportMaterialVariantsTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={supportMaterialVariants} 
                    onChange={handleMaterialVariantsChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.instancedRendering.supportInstanceAnimation')}
                  <Tooltip title={t('optimization.instancedRendering.supportInstanceAnimationTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={supportInstanceAnimation} 
                    onChange={handleInstanceAnimationChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div className="setting-item">
                <div className="setting-label">
                  {t('optimization.instancedRendering.useAutoInstancing')}
                  <Tooltip title={t('optimization.instancedRendering.useAutoInstancingTip')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </div>
                <div className="setting-control">
                  <Switch 
                    checked={useAutoInstancing} 
                    onChange={handleAutoInstancingChange}
                    disabled={!isEnabled}
                  />
                </div>
              </div>
            </Col>
          </Row>
        </Panel>
      </Collapse>
    </Card>
  );
};

export default InstancedRenderingPanel;
