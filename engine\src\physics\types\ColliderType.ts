/**
 * 碰撞体类型定义
 */

export enum ColliderType {
  /** 盒子碰撞体 */
  BOX = 'box',
  /** 球体碰撞体 */
  SPHERE = 'sphere',
  /** 胶囊碰撞体 */
  CAPSULE = 'capsule',
  /** 圆柱体碰撞体 */
  CYLINDER = 'cylinder',
  /** 圆锥体碰撞体 */
  CONE = 'cone',
  /** 平面碰撞体 */
  PLANE = 'plane',
  /** 网格碰撞体 */
  MESH = 'mesh',
  /** 凸包碰撞体 */
  CONVEX_HULL = 'convexHull',
  /** 高度场碰撞体 */
  HEIGHTFIELD = 'heightfield',
  /** 复合碰撞体 */
  COMPOUND = 'compound'
}

export interface ColliderConfig {
  /** 碰撞体类型 */
  type: ColliderType;
  /** 尺寸参数 */
  size?: {
    x?: number;
    y?: number;
    z?: number;
    radius?: number;
    height?: number;
  };
  /** 材质属性 */
  material?: {
    friction?: number;
    restitution?: number;
    density?: number;
  };
  /** 是否为触发器 */
  isTrigger?: boolean;
  /** 碰撞层 */
  layer?: number;
  /** 碰撞掩码 */
  mask?: number;
}

export class ColliderTypeHelper {
  /**
   * 检查是否为基础几何体
   */
  public static isPrimitive(type: ColliderType): boolean {
    return [
      ColliderType.BOX,
      ColliderType.SPHERE,
      ColliderType.CAPSULE,
      ColliderType.CYLINDER,
      ColliderType.CONE,
      ColliderType.PLANE
    ].includes(type);
  }

  /**
   * 检查是否为复杂几何体
   */
  public static isComplex(type: ColliderType): boolean {
    return [
      ColliderType.MESH,
      ColliderType.CONVEX_HULL,
      ColliderType.HEIGHTFIELD,
      ColliderType.COMPOUND
    ].includes(type);
  }

  /**
   * 检查是否需要网格数据
   */
  public static requiresMeshData(type: ColliderType): boolean {
    return [
      ColliderType.MESH,
      ColliderType.CONVEX_HULL,
      ColliderType.HEIGHTFIELD
    ].includes(type);
  }

  /**
   * 获取默认尺寸
   */
  public static getDefaultSize(type: ColliderType): any {
    switch (type) {
      case ColliderType.BOX:
        return { x: 1, y: 1, z: 1 };
      case ColliderType.SPHERE:
        return { radius: 0.5 };
      case ColliderType.CAPSULE:
        return { radius: 0.5, height: 2 };
      case ColliderType.CYLINDER:
        return { radius: 0.5, height: 2 };
      case ColliderType.CONE:
        return { radius: 0.5, height: 2 };
      case ColliderType.PLANE:
        return { x: 10, z: 10 };
      default:
        return { x: 1, y: 1, z: 1 };
    }
  }

  /**
   * 获取默认配置
   */
  public static getDefaultConfig(type: ColliderType): ColliderConfig {
    return {
      type,
      size: this.getDefaultSize(type),
      material: {
        friction: 0.5,
        restitution: 0.3,
        density: 1.0
      },
      isTrigger: false,
      layer: 0,
      mask: 0xFFFFFFFF
    };
  }
}
