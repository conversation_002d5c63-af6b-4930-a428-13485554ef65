# 视觉脚本高级调试指南

本文档介绍了DL（Digital Learning）引擎视觉脚本系统中的高级调试功能，包括断点管理器、变量监视器等工具的使用方法。

## 目录

- [概述](#概述)
- [断点管理器](#断点管理器)
  - [断点类型](#断点类型)
  - [创建断点](#创建断点)
  - [管理断点](#管理断点)
  - [断点条件表达式](#断点条件表达式)
- [变量监视器](#变量监视器)
  - [添加变量监视](#添加变量监视)
  - [变量格式化](#变量格式化)
  - [变量历史记录](#变量历史记录)
  - [变量监视条件](#变量监视条件)
- [调试面板](#调试面板)
  - [断点面板](#断点面板)
  - [变量面板](#变量面板)
  - [执行控制面板](#执行控制面板)
  - [性能分析面板](#性能分析面板)
- [远程调试](#远程调试)
- [最佳实践](#最佳实践)
- [常见问题](#常见问题)

## 概述

视觉脚本系统提供了一套强大的调试工具，帮助开发者快速定位和解决问题。这些工具包括：

- **断点管理器**：支持多种类型的断点，包括普通断点、条件断点、日志断点、数据断点、异常断点和计数断点。
- **变量监视器**：监视变量的变化，支持条件监视、格式化显示和历史记录。
- **调试面板**：提供直观的界面来管理断点、监视变量、控制执行和分析性能。
- **远程调试**：支持远程连接和调试，适用于多用户环境。

## 断点管理器

断点管理器允许您在视觉脚本的执行过程中设置断点，当执行到断点时，脚本将暂停执行，让您检查当前状态。

### 断点类型

视觉脚本系统支持以下类型的断点：

- **普通断点**：当执行到该节点时，脚本暂停执行。
- **条件断点**：当执行到该节点且条件表达式为真时，脚本暂停执行。
- **日志断点**：当执行到该节点时，输出日志消息，但不暂停执行。
- **数据断点**：当指定变量的值满足条件时，脚本暂停执行。
- **异常断点**：当发生指定类型的异常时，脚本暂停执行。
- **计数断点**：当执行到该节点指定次数后，脚本暂停执行。

### 创建断点

可以通过以下方式创建断点：

1. **在编辑器中**：右键点击节点，选择"添加断点"，然后选择断点类型。
2. **通过代码**：使用断点管理器的API创建断点。

```typescript
// 创建普通断点
const breakpointId = breakpointManager.addBreakpoint(
  nodeId,
  graphId,
  BreakpointType.NORMAL
);

// 创建条件断点
const conditionalBreakpointId = breakpointManager.addBreakpoint(
  nodeId,
  graphId,
  BreakpointType.CONDITIONAL,
  {
    condition: 'count > 10'
  }
);

// 创建数据断点
const dataBreakpointId = breakpointManager.addBreakpoint(
  nodeId,
  graphId,
  BreakpointType.DATA,
  {
    variableName: 'score',
    variableCondition: 'value > 100'
  }
);

// 创建计数断点
const countBreakpointId = breakpointManager.addBreakpoint(
  nodeId,
  graphId,
  BreakpointType.COUNT,
  {
    countThreshold: 5
  }
);
```

### 管理断点

断点管理器提供了以下方法来管理断点：

- **获取断点**：`getBreakpoint(id)`
- **获取节点断点**：`getNodeBreakpoint(nodeId, graphId)`
- **获取所有断点**：`getAllBreakpoints()`
- **启用/禁用断点**：`enableBreakpoint(id, enabled)`
- **移除断点**：`removeBreakpoint(id)`
- **清除所有断点**：`clearAllBreakpoints()`
- **按标签查找断点**：`findBreakpointsByTag(tag)`
- **导出断点**：`exportBreakpoints()`
- **导入断点**：`importBreakpoints(data)`

### 断点条件表达式

条件断点和数据断点使用表达式来确定是否触发断点。表达式可以访问图中的变量和当前节点。

**条件断点表达式示例**：
```
count > 10 && isActive
```

**数据断点条件表达式示例**：
```
value > 100 || value < 0
```

## 变量监视器

变量监视器允许您监视视觉脚本中变量的变化，帮助您了解脚本的执行过程。

### 添加变量监视

可以通过以下方式添加变量监视：

1. **在编辑器中**：右键点击变量，选择"添加监视"。
2. **通过代码**：使用变量监视器的API添加监视。

```typescript
// 添加简单监视
const watchAdded = variableWatcher.addWatch(
  'score',
  graphId
);

// 添加条件监视
const conditionalWatchAdded = variableWatcher.addWatch(
  'score',
  graphId,
  {
    condition: 'value > 100',
    notifyOnCondition: true
  }
);

// 添加格式化监视
const formattedWatchAdded = variableWatcher.addWatch(
  'player',
  graphId,
  {
    format: 'JSON.stringify(value, null, 2)'
  }
);
```

### 变量格式化

变量监视器支持自定义格式化表达式，让您以更易读的方式查看变量值。

**格式化表达式示例**：
```
`玩家名称: ${value.name}, 分数: ${value.score}`
```

### 变量历史记录

变量监视器会记录变量的历史变化，让您了解变量是如何随时间变化的。

```typescript
// 获取变量的历史记录
const history = variableWatcher.getHistory('score', graphId);

// 清除变量的历史记录
variableWatcher.clearHistory('score', graphId);
```

### 变量监视条件

变量监视器支持条件表达式，当变量值满足条件时，可以触发通知。

**条件表达式示例**：
```
value > previousValue * 2
```

## 调试面板

调试面板提供了直观的界面来管理断点、监视变量、控制执行和分析性能。

### 断点面板

断点面板显示当前设置的所有断点，并提供以下功能：

- **查看断点**：显示断点的类型、位置和状态。
- **编辑断点**：修改断点的条件、消息等属性。
- **启用/禁用断点**：临时启用或禁用断点。
- **删除断点**：移除不需要的断点。
- **断点分组**：按标签或类型对断点进行分组。
- **断点搜索**：快速查找特定断点。
- **断点导入/导出**：保存和加载断点配置。

### 变量面板

变量面板显示当前监视的所有变量，并提供以下功能：

- **查看变量**：显示变量的当前值和类型。
- **编辑变量**：修改变量的值（在暂停状态下）。
- **添加监视**：添加新的变量监视。
- **移除监视**：移除不需要的监视。
- **变量历史**：查看变量的历史变化。
- **变量搜索**：快速查找特定变量。
- **变量导入/导出**：保存和加载变量监视配置。

### 执行控制面板

执行控制面板提供了控制脚本执行的工具，包括：

- **继续**：继续执行脚本。
- **暂停**：暂停脚本执行。
- **单步执行**：执行下一个节点。
- **单步进入**：进入子图执行。
- **单步跳出**：跳出当前子图。
- **重启**：重新启动脚本执行。
- **停止**：停止脚本执行。

### 性能分析面板

性能分析面板提供了分析脚本性能的工具，包括：

- **节点执行时间**：显示每个节点的执行时间。
- **热点分析**：识别执行时间最长的节点。
- **内存使用**：显示脚本的内存使用情况。
- **调用图**：显示节点之间的调用关系。
- **性能记录**：记录和回放脚本执行。

## 远程调试

远程调试功能允许您连接到远程运行的视觉脚本，进行调试。这对于多用户环境或生产环境中的问题排查非常有用。

**远程调试功能包括**：

- **远程连接**：连接到远程运行的视觉脚本。
- **远程断点**：在远程脚本中设置断点。
- **远程变量监视**：监视远程脚本中的变量。
- **远程执行控制**：控制远程脚本的执行。
- **远程日志**：查看远程脚本的日志输出。

## 最佳实践

- **使用有意义的断点标签**：给断点添加描述性标签，方便管理。
- **合理使用条件断点**：条件断点可能影响性能，请谨慎使用。
- **限制变量历史记录大小**：对于频繁变化的变量，考虑限制历史记录大小。
- **使用日志断点代替console.log**：日志断点提供更多上下文信息。
- **导出调试配置**：保存调试配置，方便在不同环境中复用。
- **使用性能分析工具**：定期分析脚本性能，识别潜在问题。

## 常见问题

**问题**：断点不触发怎么办？
**回答**：检查断点是否启用，条件表达式是否正确，节点是否实际执行。

**问题**：如何调试异步节点？
**回答**：使用异步断点，或在异步操作的回调中设置断点。

**问题**：如何在生产环境中调试问题？
**回答**：使用远程调试功能，或添加日志断点收集信息。

**问题**：如何调试性能问题？
**回答**：使用性能分析面板，识别执行时间长的节点和频繁调用的节点。

**问题**：如何共享调试配置？
**回答**：使用导出功能保存断点和变量监视配置，然后在其他环境中导入。
