# 水体系统

水体系统是DL（Digital Learning）引擎中用于创建和管理各种水体效果的系统，包括海洋、湖泊、河流、地下水体等。本文档将介绍水体系统的基本概念、使用方法以及高级功能。

## 目录

- [基本概念](#基本概念)
- [水体类型](#水体类型)
- [水体材质](#水体材质)
- [水体预设](#水体预设)
- [水下效果](#水下效果)
- [地下水体](#地下水体)
- [性能优化](#性能优化)
- [编辑器使用](#编辑器使用)
- [API参考](#api参考)

## 基本概念

水体系统由以下几个主要组件组成：

- **水体组件（WaterBodyComponent）**：表示一个水体实体，包含水体的物理属性和基本参数。
- **水体材质（WaterMaterial）**：控制水体的视觉外观，包括颜色、透明度、反射率等。
- **水体物理系统（WaterPhysicsSystem）**：处理水体的物理模拟，包括浮力、流动、波动等。
- **水下粒子系统（UnderwaterParticleSystem）**：创建水下的粒子效果，如气泡、悬浮物等。
- **地下环境光照系统（UndergroundLightingSystem）**：为地下水体提供特殊的光照效果。

## 水体类型

DL（Digital Learning）引擎支持多种水体类型，每种类型都有其特定的视觉和物理特性：

### 海洋（Ocean）

适用于大型开放水域，具有较强的波动和反射效果。

```typescript
// 创建海洋水体
const oceanEntity = new Entity();
oceanEntity.addComponent(new WaterBodyComponent({
  type: WaterBodyType.OCEAN,
  size: { width: 1000, height: 50, depth: 1000 },
  waveParams: { amplitude: 0.5, frequency: 0.2, speed: 0.5 }
}));
```

### 湖泊（Lake）

适用于封闭或半封闭水域，波动较小，通常有较高的透明度。

```typescript
// 创建湖泊水体
const lakeEntity = new Entity();
lakeEntity.addComponent(new WaterBodyComponent({
  type: WaterBodyType.LAKE,
  size: { width: 200, height: 20, depth: 200 },
  waveParams: { amplitude: 0.1, frequency: 0.3, speed: 0.2 }
}));
```

### 河流（River）

适用于流动的水体，具有方向性流动和较小的波动。

```typescript
// 创建河流水体
const riverEntity = new Entity();
riverEntity.addComponent(new WaterBodyComponent({
  type: WaterBodyType.RIVER,
  size: { width: 50, height: 5, depth: 200 },
  flowDirection: { x: 0, y: 0, z: 1 },
  flowSpeed: 2.0
}));
```

### 地下水体（Underground）

适用于洞穴、地下湖泊和地下河流，通常具有特殊的光照效果和较低的波动。

```typescript
// 创建地下湖泊
const undergroundLakeEntity = new Entity();
undergroundLakeEntity.addComponent(new WaterBodyComponent({
  type: WaterBodyType.UNDERGROUND_LAKE,
  size: { width: 100, height: 10, depth: 100 },
  waveParams: { amplitude: 0.05, frequency: 0.2, speed: 0.1 }
}));
```

## 水体材质

水体材质控制水体的视觉外观，包括颜色、透明度、反射率、折射率等参数。

### 基本属性

- **颜色（Color）**：水体的基本颜色。
- **透明度（Opacity）**：水体的透明度，值范围为0-1。
- **反射率（Reflectivity）**：水面反射环境的程度，值范围为0-1。
- **折射率（RefractionRatio）**：水体折射光线的程度，值范围为0-1。

### 波动属性

- **波动强度（WaveStrength）**：水面波动的幅度。
- **波动速度（WaveSpeed）**：水面波动的速度。
- **波动尺寸（WaveScale）**：水面波动的尺寸。
- **波动方向（WaveDirection）**：水面波动的主要方向。

### 深度属性

- **深度（Depth）**：水体的深度，影响颜色渐变。
- **深水颜色（DepthColor）**：深水区域的颜色。
- **浅水颜色（ShallowColor）**：浅水区域的颜色。

### 特效属性

- **因果波纹（Caustics）**：水下光线折射形成的波纹效果。
- **泡沫（Foam）**：水面边缘的泡沫效果。
- **水下雾效（UnderwaterFog）**：水下的雾效，增加深度感。
- **水下扭曲（UnderwaterDistortion）**：水下视觉的扭曲效果。

### 示例代码

```typescript
// 创建水体材质
const waterMaterial = new WaterMaterial({
  color: new THREE.Color(0x0055ff),
  opacity: 0.8,
  reflectivity: 0.6,
  refractionRatio: 0.98,
  waveStrength: 0.1,
  waveSpeed: 0.5,
  waveScale: 4.0,
  depth: 5.0,
  depthColor: new THREE.Color(0x001e0f),
  shallowColor: new THREE.Color(0x0077ff),
  enableCaustics: true,
  enableFoam: true
});

// 应用到水体
waterBodyComponent.setMaterial(waterMaterial);
```

## 水体预设

DL（Digital Learning）引擎提供了多种水体预设，可以快速创建常见的水体效果。

### 使用预设

```typescript
// 获取预设管理器
const presetManager = WaterMaterialPresetManager.getInstance();

// 使用预设创建材质
const oceanMaterial = presetManager.createMaterialFromPreset('ocean_deep_blue');
const lakeMaterial = presetManager.createMaterialFromPreset('lake_clear');
const riverMaterial = presetManager.createMaterialFromPreset('river_flowing');
const undergroundMaterial = presetManager.createMaterialFromPreset('underground_lake');
```

### 可用预设

- **深蓝海洋（ocean_deep_blue）**：深蓝色海洋水体，适合开阔海域。
- **清澈湖泊（lake_clear）**：清澈的湖泊水体，适合平静的湖面。
- **流动河流（river_flowing）**：流动的河流水体，适合有流动感的河流。
- **地下湖泊（underground_lake）**：地下湖泊水体，适合洞穴中的湖泊。
- **地下河流（underground_river）**：地下河流水体，适合洞穴中的河流。
- **温泉（hot_spring）**：温泉水体，适合温泉场景。
- **瀑布（waterfall）**：瀑布水体，适合瀑布场景。

## 水下效果

DL（Digital Learning）引擎提供了丰富的水下效果，增强水体的沉浸感。

### 水下粒子

水下粒子系统可以创建气泡、悬浮物、光束等效果。

```typescript
// 获取水下粒子系统
const particleSystem = new UnderwaterParticleSystem();
particleSystem.initialize(scene, camera);

// 添加水体
particleSystem.addWaterBody(entity, waterBodyComponent);

// 添加自定义粒子组
particleSystem.addParticleGroup(entity.id, 'bubbles', {
  type: UnderwaterParticleType.BUBBLE,
  count: 500,
  size: [0.05, 0.2],
  color: 0xffffff,
  opacity: 0.6,
  lifetime: [2, 5],
  speed: [0.2, 0.5],
  acceleration: new THREE.Vector3(0, 0.1, 0)
});
```

### 水下光照

地下环境光照系统可以创建特殊的光束效果，增强地下水体的氛围。

```typescript
// 获取地下环境光照系统
const lightingSystem = new UndergroundLightingSystem();
lightingSystem.initialize(scene, camera, renderer);

// 添加光束
lightingSystem.addLightShaft('shaft1', {
  type: LightShaftType.VOLUMETRIC,
  position: new THREE.Vector3(0, 10, 0),
  direction: new THREE.Vector3(0, -1, 0),
  color: 0x66ccff,
  intensity: 1.5,
  angle: Math.PI / 8,
  enableDynamicEffect: true,
  dynamicEffect: {
    intensityVariation: 0.2,
    speed: 0.5
  }
});
```

## 地下水体

地下水体是DL（Digital Learning）引擎的特色功能，可以创建洞穴、地下湖泊和地下河流等场景。

### 地下湖泊

地下湖泊通常具有较低的波动和特殊的光照效果。

```typescript
// 创建地下湖泊
const undergroundLakeEntity = new Entity();
undergroundLakeEntity.addComponent(new WaterBodyComponent({
  type: WaterBodyType.UNDERGROUND_LAKE,
  size: { width: 100, height: 10, depth: 100 },
  waveParams: { amplitude: 0.05, frequency: 0.2, speed: 0.1 }
}));

// 应用地下湖泊预设
const material = presetManager.createMaterialFromPreset('underground_lake');
undergroundLakeEntity.getComponent(WaterBodyComponent).setMaterial(material);
```

### 地下河流

地下河流通常具有方向性流动和较低的波动。

```typescript
// 创建地下河流
const undergroundRiverEntity = new Entity();
undergroundRiverEntity.addComponent(new WaterBodyComponent({
  type: WaterBodyType.UNDERGROUND_RIVER,
  size: { width: 20, height: 5, depth: 100 },
  flowDirection: { x: 0, y: 0, z: 1 },
  flowSpeed: 1.0,
  waveParams: { amplitude: 0.05, frequency: 0.3, speed: 0.2 }
}));

// 应用地下河流预设
const material = presetManager.createMaterialFromPreset('underground_river');
undergroundRiverEntity.getComponent(WaterBodyComponent).setMaterial(material);
```

## 性能优化

水体系统提供了多种性能优化选项，以适应不同的硬件环境。

### 水体物理优化

- **空间分区**：使用空间分区算法优化水体物理计算。
- **动态更新频率**：根据距离和可见性动态调整物理更新频率。
- **简化远处水体**：对远处的水体使用简化的物理模型。

### 渲染优化

- **LOD系统**：根据距离使用不同细节级别的水体网格。
- **着色器优化**：提供多种性能级别的水体着色器。
- **纹理压缩**：使用压缩纹理减少内存占用。

### 粒子系统优化

- **自动调整性能**：根据帧率自动调整粒子数量。
- **GPU加速**：使用GPU加速粒子模拟。
- **距离剔除**：根据距离剔除不可见的粒子。

## 编辑器使用

DL（Digital Learning）引擎编辑器提供了直观的水体编辑界面，可以轻松创建和编辑各种水体效果。

### 水体材质编辑器

水体材质编辑器允许您调整水体的视觉外观，包括颜色、透明度、波动等参数。

1. 在场景中选择水体实体。
2. 在属性面板中找到"水体材质"选项卡。
3. 调整各种参数，如颜色、透明度、波动等。
4. 使用实时预览功能查看效果。
5. 应用预设或保存自定义预设。

### 水体物理编辑器

水体物理编辑器允许您调整水体的物理属性，如密度、粘度、流动等。

1. 在场景中选择水体实体。
2. 在属性面板中找到"水体物理"选项卡。
3. 调整各种参数，如密度、粘度、流动等。
4. 使用模拟功能测试物理效果。

### 水下效果编辑器

水下效果编辑器允许您添加和编辑水下粒子和光照效果。

1. 在场景中选择水体实体。
2. 在属性面板中找到"水下效果"选项卡。
3. 添加和编辑粒子组，如气泡、悬浮物等。
4. 添加和编辑光束，如体积光、光线等。
5. 使用预览功能查看效果。

## API参考

详细的API参考请查看以下文档：

- [WaterBodyComponent API](../api/water/WaterBodyComponent.md)
- [WaterMaterial API](../api/water/WaterMaterial.md)
- [WaterPhysicsSystem API](../api/water/WaterPhysicsSystem.md)
- [UnderwaterParticleSystem API](../api/water/UnderwaterParticleSystem.md)
- [UndergroundLightingSystem API](../api/lighting/UndergroundLightingSystem.md)
