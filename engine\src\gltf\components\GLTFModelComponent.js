"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GLTFModelComponent = void 0;
/**
 * GLTF模型组件
 * 用于存储GLTF模型数据
 */
var THREE = require("three");
var Component_1 = require("../../core/Component");
/**
 * GLTF模型组件
 */
var GLTFModelComponent = exports.GLTFModelComponent = /** @class */ (function (_super) {
    __extends(GLTFModelComponent, _super);
    /**
     * 创建GLTF模型组件
     * @param gltf GLTF模型
     */
    function GLTFModelComponent(gltf) {
        var _this = _super.call(this, GLTFModelComponent.type) || this;
        /** 模型URL */
        _this.url = '';
        /** 是否已加载 */
        _this.loaded = false;
        /** 加载错误 */
        _this.error = null;
        _this.gltf = gltf;
        _this.loaded = true;
        return _this;
    }
    /**
     * 获取GLTF模型
     * @returns GLTF模型
     */
    GLTFModelComponent.prototype.getGLTF = function () {
        return this.gltf;
    };
    /**
     * 设置GLTF模型
     * @param gltf GLTF模型
     */
    GLTFModelComponent.prototype.setGLTF = function (gltf) {
        this.gltf = gltf;
        this.loaded = true;
        this.error = null;
    };
    /**
     * 获取模型URL
     * @returns 模型URL
     */
    GLTFModelComponent.prototype.getURL = function () {
        return this.url;
    };
    /**
     * 设置模型URL
     * @param url 模型URL
     */
    GLTFModelComponent.prototype.setURL = function (url) {
        this.url = url;
    };
    /**
     * 是否已加载
     * @returns 是否已加载
     */
    GLTFModelComponent.prototype.isLoaded = function () {
        return this.loaded;
    };
    /**
     * 设置加载状态
     * @param loaded 是否已加载
     */
    GLTFModelComponent.prototype.setLoaded = function (loaded) {
        this.loaded = loaded;
    };
    /**
     * 获取加载错误
     * @returns 加载错误
     */
    GLTFModelComponent.prototype.getError = function () {
        return this.error;
    };
    /**
     * 设置加载错误
     * @param error 加载错误
     */
    GLTFModelComponent.prototype.setError = function (error) {
        this.error = error;
    };
    /**
     * 获取场景
     * @returns 场景
     */
    GLTFModelComponent.prototype.getScene = function () {
        return this.gltf.scene;
    };
    /**
     * 获取动画
     * @returns 动画数组
     */
    GLTFModelComponent.prototype.getAnimations = function () {
        return this.gltf.animations || [];
    };
    /**
     * 获取相机
     * @returns 相机数组
     */
    GLTFModelComponent.prototype.getCameras = function () {
        return this.gltf.cameras || [];
    };
    /**
     * 获取场景
     * @returns 场景数组
     */
    GLTFModelComponent.prototype.getScenes = function () {
        return this.gltf.scenes || [];
    };
    /**
     * 获取资产
     * @returns 资产
     */
    GLTFModelComponent.prototype.getAsset = function () {
        return this.gltf.asset;
    };
    /**
     * 克隆组件
     * @returns 克隆的组件
     */
    GLTFModelComponent.prototype.clone = function () {
        var component = new GLTFModelComponent(this.gltf);
        component.url = this.url;
        component.loaded = this.loaded;
        component.error = this.error;
        return component;
    };
    /**
     * 销毁组件
     */
    GLTFModelComponent.prototype.dispose = function () {
        var _this = this;
        // 销毁GLTF模型中的资源
        if (this.gltf) {
            // 销毁几何体
            this.gltf.scene.traverse(function (node) {
                if (node instanceof THREE.Mesh) {
                    if (node.geometry) {
                        node.geometry.dispose();
                    }
                    if (node.material) {
                        if (Array.isArray(node.material)) {
                            for (var _i = 0, _a = node.material; _i < _a.length; _i++) {
                                var material = _a[_i];
                                _this.disposeMaterial(material);
                            }
                        }
                        else {
                            _this.disposeMaterial(node.material);
                        }
                    }
                }
            });
        }
    };
    /**
     * 销毁材质
     * @param material 材质
     */
    GLTFModelComponent.prototype.disposeMaterial = function (material) {
        // 销毁材质中的纹理
        for (var key in material) {
            var value = material[key];
            if (value instanceof THREE.Texture) {
                value.dispose();
            }
        }
        material.dispose();
    };
    /** 组件类型 */
    GLTFModelComponent.type = 'GLTFModelComponent';
    return GLTFModelComponent;
}(Component_1.Component));
