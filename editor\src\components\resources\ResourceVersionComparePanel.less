/**
 * 资源版本比较面板样式
 */
.resource-version-compare-panel {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  .compare-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .ant-card-body {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
    }
    
    .compare-content {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
    }
    
    .versions-container {
      display: flex;
      margin-bottom: 16px;
      
      .version-column {
        flex: 1;
        padding: 16px;
        
        .version-info {
          h4 {
            margin-bottom: 16px;
          }
          
          .version-actions {
            margin-top: 16px;
            text-align: center;
          }
        }
      }
      
      .version-divider {
        height: auto;
        margin: 0 16px;
      }
    }
    
    .differences-container {
      margin-bottom: 16px;
      
      .ant-tabs-content {
        padding: 16px;
      }
      
      .difference-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        
        h4 {
          margin: 0;
          margin-right: 16px;
        }
      }
      
      .content-details,
      .metadata-details {
        margin-top: 16px;
      }
      
      .different-value {
        background-color: #fff1f0;
        padding: 2px 4px;
        border-radius: 2px;
      }
      
      .added-value {
        background-color: #f6ffed;
        padding: 2px 4px;
        border-radius: 2px;
      }
      
      .removed-value {
        background-color: #fff1f0;
        padding: 2px 4px;
        border-radius: 2px;
        text-decoration: line-through;
      }
      
      .modified-value {
        background-color: #e6f7ff;
        padding: 2px 4px;
        border-radius: 2px;
      }
    }
    
    .compare-footer {
      padding: 16px;
      background-color: #f9f9f9;
    }
  }
}
