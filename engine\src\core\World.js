"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.World = void 0;
var Entity_1 = require("./Entity");
var Scene_1 = require("../scene/Scene");
var EventEmitter_1 = require("../utils/EventEmitter");
var UUID_1 = require("../utils/UUID");
var World = /** @class */ (function (_super) {
    __extends(World, _super);
    /**
     * 创建世界实例
     * @param engine 引擎实例
     */
    function World(engine) {
        var _this = _super.call(this) || this;
        /** 实体映射 */
        _this.entities = new Map();
        /** 当前场景 */
        _this.activeScene = null;
        /** 场景映射 */
        _this.scenes = new Map();
        /** 系统列表 */
        _this.systems = [];
        /** 是否已初始化 */
        _this.initialized = false;
        _this.engine = engine;
        return _this;
    }
    /**
     * 初始化世界
     */
    World.prototype.initialize = function () {
        if (this.initialized) {
            return;
        }
        // 创建默认场景
        var defaultScene = new Scene_1.Scene('默认场景');
        this.addScene(defaultScene);
        this.setActiveScene(defaultScene);
        this.initialized = true;
        this.emit('initialized');
    };
    /**
     * 更新世界
     * @param deltaTime 帧间隔时间（秒）
     */
    World.prototype.update = function (deltaTime) {
        // 更新所有系统
        for (var _i = 0, _a = this.systems; _i < _a.length; _i++) {
            var system = _a[_i];
            if (system.isEnabled()) {
                system.update(deltaTime);
            }
        }
        // 更新所有实体
        for (var _b = 0, _c = Array.from(this.entities.values()); _b < _c.length; _b++) {
            var entity = _c[_b];
            if (entity.isActive()) {
                entity.update(deltaTime);
            }
        }
        // 更新当前场景
        if (this.activeScene) {
            this.activeScene.update(deltaTime);
        }
    };
    /**
     * 固定时间步长更新
     * @param fixedDeltaTime 固定帧间隔时间（秒）
     */
    World.prototype.fixedUpdate = function (fixedDeltaTime) {
        // 固定更新所有系统
        for (var _i = 0, _a = this.systems; _i < _a.length; _i++) {
            var system = _a[_i];
            if (system.isEnabled()) {
                system.fixedUpdate(fixedDeltaTime);
            }
        }
        // 固定更新所有实体
        for (var _b = 0, _c = Array.from(this.entities.values()); _b < _c.length; _b++) {
            var entity = _c[_b];
            if (entity.isActive()) {
                entity.fixedUpdate(fixedDeltaTime);
            }
        }
        // 固定更新当前场景
        if (this.activeScene) {
            this.activeScene.fixedUpdate(fixedDeltaTime);
        }
    };
    /**
     * 创建实体
     * @param name 实体名称
     * @returns 创建的实体
     */
    World.prototype.createEntity = function (name) {
        if (name === void 0) { name = '实体'; }
        var entity = new Entity_1.Entity(name);
        this.addEntity(entity);
        return entity;
    };
    /**
     * 添加实体
     * @param entity 实体实例
     * @returns 添加的实体
     */
    World.prototype.addEntity = function (entity) {
        // 如果实体已经有ID，则使用该ID，否则生成新ID
        if (!entity.id) {
            entity.id = (0, UUID_1.generateUUID)();
        }
        // 设置实体的世界引用
        entity.setWorld(this);
        // 添加到实体映射
        this.entities.set(entity.id, entity);
        // 如果有活跃场景，将实体添加到场景
        if (this.activeScene) {
            this.activeScene.addEntity(entity);
        }
        // 发出实体创建事件
        this.emit('entityCreated', entity);
        return entity;
    };
    /**
     * 获取实体
     * @param id 实体ID
     * @returns 实体实例，如果不存在则返回null
     */
    World.prototype.getEntity = function (id) {
        return this.entities.get(id) || null;
    };
    /**
     * 移除实体
     * @param entity 实体实例或ID
     * @returns 是否成功移除
     */
    World.prototype.removeEntity = function (entity) {
        var id = typeof entity === 'string' ? entity : entity.id;
        if (!id || !this.entities.has(id)) {
            return false;
        }
        var entityToRemove = this.entities.get(id);
        // 如果有活跃场景，从场景中移除实体
        if (this.activeScene) {
            this.activeScene.removeEntity(entityToRemove);
        }
        // 销毁实体
        entityToRemove.dispose();
        // 从实体映射中移除
        this.entities.delete(id);
        // 发出实体移除事件
        this.emit('entityRemoved', entityToRemove);
        return true;
    };
    /**
     * 获取所有实体
     * @returns 实体数组
     */
    World.prototype.getAllEntities = function () {
        return Array.from(this.entities.values());
    };
    /**
     * 根据名称查找实体
     * @param name 实体名称
     * @returns 匹配的实体数组
     */
    World.prototype.findEntitiesByName = function (name) {
        var result = [];
        for (var _i = 0, _a = Array.from(this.entities.values()); _i < _a.length; _i++) {
            var entity = _a[_i];
            if (entity.name === name) {
                result.push(entity);
            }
        }
        return result;
    };
    /**
     * 根据标签查找实体
     * @param tag 实体标签
     * @returns 匹配的实体数组
     */
    World.prototype.findEntitiesByTag = function (tag) {
        var result = [];
        for (var _i = 0, _a = Array.from(this.entities.values()); _i < _a.length; _i++) {
            var entity = _a[_i];
            if (entity.hasTag(tag)) {
                result.push(entity);
            }
        }
        return result;
    };
    /**
     * 创建场景
     * @param name 场景名称
     * @returns 创建的场景
     */
    World.prototype.createScene = function (name) {
        if (name === void 0) { name = '场景'; }
        var scene = new Scene_1.Scene(name);
        this.addScene(scene);
        return scene;
    };
    /**
     * 添加场景
     * @param scene 场景实例
     * @returns 添加的场景
     */
    World.prototype.addScene = function (scene) {
        // 如果场景已经有ID，则使用该ID，否则生成新ID
        if (!scene.id) {
            scene.id = (0, UUID_1.generateUUID)();
        }
        // 添加到场景映射
        this.scenes.set(scene.id, scene);
        // 发出场景添加事件
        this.emit('sceneAdded', scene);
        return scene;
    };
    /**
     * 获取场景
     * @param id 场景ID
     * @returns 场景实例，如果不存在则返回null
     */
    World.prototype.getScene = function (id) {
        return this.scenes.get(id) || null;
    };
    /**
     * 移除场景
     * @param scene 场景实例或ID
     * @returns 是否成功移除
     */
    World.prototype.removeScene = function (scene) {
        var id = typeof scene === 'string' ? scene : scene.id;
        if (!id || !this.scenes.has(id)) {
            return false;
        }
        var sceneToRemove = this.scenes.get(id);
        // 如果是当前活跃场景，则清除活跃场景
        if (this.activeScene === sceneToRemove) {
            this.activeScene = null;
        }
        // 销毁场景
        sceneToRemove.dispose();
        // 从场景映射中移除
        this.scenes.delete(id);
        // 发出场景移除事件
        this.emit('sceneRemoved', sceneToRemove);
        return true;
    };
    /**
     * 获取所有场景
     * @returns 场景数组
     */
    World.prototype.getAllScenes = function () {
        return Array.from(this.scenes.values());
    };
    /**
     * 设置活跃场景
     * @param scene 场景实例或ID
     * @returns 是否成功设置
     */
    World.prototype.setActiveScene = function (scene) {
        var targetScene = typeof scene === 'string' ? this.getScene(scene) : scene;
        if (!targetScene) {
            return false;
        }
        // 如果已经是活跃场景，则不做任何操作
        if (this.activeScene === targetScene) {
            return true;
        }
        // 保存旧场景
        var oldScene = this.activeScene;
        // 设置新的活跃场景
        this.activeScene = targetScene;
        // 发出场景切换事件
        this.emit('sceneChanged', targetScene, oldScene);
        return true;
    };
    /**
     * 获取活跃场景
     * @returns 活跃场景实例
     */
    World.prototype.getActiveScene = function () {
        return this.activeScene;
    };
    /**
     * 获取引擎实例
     * @returns 引擎实例
     */
    World.prototype.getEngine = function () {
        return this.engine;
    };
    /**
     * 添加系统
     * @param system 系统实例
     * @returns 添加的系统
     */
    World.prototype.addSystem = function (system) {
        // 设置系统的世界引用
        system.setWorld(this);
        // 添加到系统列表
        this.systems.push(system);
        // 按优先级排序
        this.systems.sort(function (a, b) { return a.getPriority() - b.getPriority(); });
        // 初始化系统
        system.initialize();
        // 发出系统添加事件
        this.emit('systemAdded', system);
        return system;
    };
    /**
     * 获取系统
     * @param systemClass 系统类
     * @returns 系统实例，如果不存在则返回null
     */
    World.prototype.getSystem = function (systemClass) {
        for (var _i = 0, _a = this.systems; _i < _a.length; _i++) {
            var system = _a[_i];
            if (system instanceof systemClass) {
                return system;
            }
        }
        return null;
    };
    /**
     * 移除系统
     * @param system 系统实例或类
     * @returns 是否成功移除
     */
    World.prototype.removeSystem = function (system) {
        var index = -1;
        if (typeof system === 'function') {
            // 如果是类，查找该类的实例
            index = this.systems.findIndex(function (s) { return s instanceof system; });
        }
        else {
            // 如果是实例，直接查找
            index = this.systems.indexOf(system);
        }
        if (index !== -1) {
            var removedSystem = this.systems[index];
            removedSystem.dispose();
            this.systems.splice(index, 1);
            // 发出系统移除事件
            this.emit('systemRemoved', removedSystem);
            return true;
        }
        return false;
    };
    /**
     * 获取所有系统
     * @returns 系统数组
     */
    World.prototype.getSystems = function () {
        return __spreadArray([], this.systems, true);
    };
    /**
     * 获取所有实体
     * @returns 实体映射
     */
    World.prototype.getEntities = function () {
        return this.entities;
    };
    /**
     * 清空世界
     */
    World.prototype.clear = function () {
        // 移除所有系统
        for (var _i = 0, _a = Array.from(this.systems); _i < _a.length; _i++) {
            var system = _a[_i];
            system.dispose();
        }
        this.systems.length = 0;
        // 移除所有实体
        for (var _b = 0, _c = Array.from(this.entities.values()); _b < _c.length; _b++) {
            var entity = _c[_b];
            entity.dispose();
        }
        this.entities.clear();
        // 移除所有场景
        for (var _d = 0, _e = Array.from(this.scenes.values()); _d < _e.length; _d++) {
            var scene = _e[_d];
            scene.dispose();
        }
        this.scenes.clear();
        // 清除活跃场景
        this.activeScene = null;
        // 发出清空事件
        this.emit('cleared');
    };
    /**
     * 销毁世界
     */
    World.prototype.dispose = function () {
        // 清空世界
        this.clear();
        // 移除所有事件监听器
        this.removeAllListeners();
        this.initialized = false;
    };
    return World;
}(EventEmitter_1.EventEmitter));
exports.World = World;
