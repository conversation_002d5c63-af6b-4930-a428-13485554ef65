"use strict";
/**
 * 环境模块
 *
 * 该模块提供了环境感知和响应功能，使角色能够感知周围的环境并做出相应的反应。
 * 包括环境感知组件、环境响应组件、环境感知系统等。
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
// 导出组件
__exportStar(require("./components/EnvironmentAwarenessComponent"), exports);
__exportStar(require("./components/EnvironmentResponseComponent"), exports);
// 导出系统
__exportStar(require("./systems/EnvironmentAwarenessSystem"), exports);
// 导出预设
__exportStar(require("./presets/EnvironmentPresets"), exports);
