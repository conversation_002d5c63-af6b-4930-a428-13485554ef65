/**
 * AI模型缓存
 * 用于缓存AI模型的结果，提高性能
 */
import { AIModelType } from './AIModelType';

/**
 * 缓存项
 */
interface CacheItem<T> {
  /** 值 */
  value: T;
  /** 过期时间 */
  expireTime: number;
  /** 最后访问时间 */
  lastAccessTime: number;
  /** 访问次数 */
  accessCount: number;
}

/**
 * 缓存配置
 */
export interface AIModelCacheConfig {
  /** 最大缓存大小 */
  maxSize?: number;
  /** 缓存过期时间（毫秒） */
  expireTime?: number;
  /** 是否启用调试 */
  debug?: boolean;
  /** 清理间隔（毫秒） */
  cleanupInterval?: number;
  /** 缓存命中率阈值 */
  hitRateThreshold?: number;
  /** 缓存命中率窗口大小 */
  hitRateWindowSize?: number;
}

/**
 * 缓存统计
 */
interface CacheStats {
  /** 缓存大小 */
  size: number;
  /** 最大缓存大小 */
  maxSize: number;
  /** 缓存命中次数 */
  hits: number;
  /** 缓存未命中次数 */
  misses: number;
  /** 缓存命中率 */
  hitRate: number;
  /** 缓存过期次数 */
  expirations: number;
  /** 缓存驱逐次数 */
  evictions: number;
}

/**
 * AI模型缓存
 */
export class AIModelCache<T> {
  /** 缓存 */
  private cache: Map<string, CacheItem<T>> = new Map();
  
  /** 配置 */
  private config: Required<AIModelCacheConfig>;
  
  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: Required<AIModelCacheConfig> = {
    maxSize: 100,
    expireTime: 3600000, // 1小时
    debug: false,
    cleanupInterval: 300000, // 5分钟
    hitRateThreshold: 0.5,
    hitRateWindowSize: 100
  };
  
  /** 缓存命中次数 */
  private hits: number = 0;
  
  /** 缓存未命中次数 */
  private misses: number = 0;
  
  /** 缓存过期次数 */
  private expirations: number = 0;
  
  /** 缓存驱逐次数 */
  private evictions: number = 0;
  
  /** 清理定时器 */
  private cleanupTimer: NodeJS.Timeout | null = null;
  
  /** 最近的访问历史 */
  private recentAccesses: boolean[] = [];
  
  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: AIModelCacheConfig = {}) {
    this.config = {
      ...AIModelCache.DEFAULT_CONFIG,
      ...config
    };
    
    // 启动清理定时器
    this.startCleanupTimer();
  }
  
  /**
   * 生成缓存键
   * @param modelType 模型类型
   * @param input 输入
   * @param options 选项
   * @returns 缓存键
   */
  public generateKey(modelType: AIModelType, input: string, options?: any): string {
    // 基本键
    let key = `${modelType}:${input}`;
    
    // 添加选项
    if (options) {
      key += `:${JSON.stringify(options)}`;
    }
    
    return key;
  }
  
  /**
   * 获取缓存项
   * @param key 缓存键
   * @returns 缓存值
   */
  public get(key: string): T | null {
    // 获取缓存项
    const item = this.cache.get(key);
    
    // 如果缓存项不存在，返回null
    if (!item) {
      this.misses++;
      this.updateRecentAccesses(false);
      return null;
    }
    
    // 检查是否过期
    if (item.expireTime > 0 && Date.now() > item.expireTime) {
      this.cache.delete(key);
      this.expirations++;
      this.misses++;
      this.updateRecentAccesses(false);
      return null;
    }
    
    // 更新访问信息
    item.lastAccessTime = Date.now();
    item.accessCount++;
    
    // 更新统计
    this.hits++;
    this.updateRecentAccesses(true);
    
    // 返回值
    return item.value;
  }
  
  /**
   * 设置缓存项
   * @param key 缓存键
   * @param value 缓存值
   * @param expireTime 过期时间（毫秒）
   * @returns 是否成功
   */
  public set(key: string, value: T, expireTime?: number): boolean {
    // 如果缓存已满，清理一些项
    if (this.cache.size >= this.config.maxSize) {
      this.evictItems();
    }
    
    // 计算过期时间
    const expire = expireTime !== undefined
      ? (expireTime > 0 ? Date.now() + expireTime : 0)
      : (this.config.expireTime > 0 ? Date.now() + this.config.expireTime : 0);
    
    // 创建缓存项
    const item: CacheItem<T> = {
      value,
      expireTime: expire,
      lastAccessTime: Date.now(),
      accessCount: 0
    };
    
    // 设置缓存项
    this.cache.set(key, item);
    
    return true;
  }
  
  /**
   * 删除缓存项
   * @param key 缓存键
   * @returns 是否成功
   */
  public delete(key: string): boolean {
    return this.cache.delete(key);
  }
  
  /**
   * 清空缓存
   */
  public clear(): void {
    this.cache.clear();
    this.hits = 0;
    this.misses = 0;
    this.expirations = 0;
    this.evictions = 0;
    this.recentAccesses = [];
  }
  
  /**
   * 获取缓存大小
   * @returns 缓存大小
   */
  public size(): number {
    return this.cache.size;
  }
  
  /**
   * 获取缓存统计
   * @returns 缓存统计
   */
  public getStats(): CacheStats {
    const totalAccesses = this.hits + this.misses;
    const hitRate = totalAccesses > 0 ? this.hits / totalAccesses : 0;
    
    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hits: this.hits,
      misses: this.misses,
      hitRate,
      expirations: this.expirations,
      evictions: this.evictions
    };
  }
  
  /**
   * 清理过期项
   */
  public cleanup(): void {
    const now = Date.now();
    let expiredCount = 0;
    
    // 遍历缓存项
    for (const [key, item] of this.cache.entries()) {
      // 如果过期时间大于0且已过期，删除
      if (item.expireTime > 0 && now > item.expireTime) {
        this.cache.delete(key);
        expiredCount++;
        this.expirations++;
      }
    }
    
    // 如果启用调试且有过期项，输出日志
    if (this.config.debug && expiredCount > 0) {
      console.log(`[AIModelCache] 清理了 ${expiredCount} 个过期项`);
    }
  }
  
  /**
   * 驱逐项
   */
  private evictItems(): void {
    // 如果缓存为空，直接返回
    if (this.cache.size === 0) return;
    
    // 计算要驱逐的项数
    const evictCount = Math.max(1, Math.floor(this.cache.size * 0.1));
    
    // 按最后访问时间和访问次数排序
    const sortedItems = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => {
        // 首先按访问次数排序
        const countDiff = a.accessCount - b.accessCount;
        if (countDiff !== 0) return countDiff;
        
        // 然后按最后访问时间排序
        return a.lastAccessTime - b.lastAccessTime;
      });
    
    // 驱逐最不常用的项
    for (let i = 0; i < evictCount && i < sortedItems.length; i++) {
      this.cache.delete(sortedItems[i][0]);
      this.evictions++;
    }
    
    // 如果启用调试，输出日志
    if (this.config.debug) {
      console.log(`[AIModelCache] 驱逐了 ${evictCount} 个项`);
    }
  }
  
  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    // 如果已有定时器，先清除
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    // 创建新定时器
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
      
      // 检查缓存命中率
      this.checkHitRate();
    }, this.config.cleanupInterval);
  }
  
  /**
   * 停止清理定时器
   */
  public stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }
  
  /**
   * 更新最近访问历史
   * @param hit 是否命中
   */
  private updateRecentAccesses(hit: boolean): void {
    // 添加到历史
    this.recentAccesses.push(hit);
    
    // 如果超过窗口大小，移除最早的
    if (this.recentAccesses.length > this.config.hitRateWindowSize) {
      this.recentAccesses.shift();
    }
  }
  
  /**
   * 检查缓存命中率
   */
  private checkHitRate(): void {
    // 如果历史不足，直接返回
    if (this.recentAccesses.length < this.config.hitRateWindowSize) {
      return;
    }
    
    // 计算最近的命中率
    const recentHits = this.recentAccesses.filter(hit => hit).length;
    const recentHitRate = recentHits / this.recentAccesses.length;
    
    // 如果命中率低于阈值，增加缓存大小
    if (recentHitRate < this.config.hitRateThreshold) {
      const newMaxSize = Math.min(this.config.maxSize * 1.5, 1000);
      
      if (this.config.debug) {
        console.log(`[AIModelCache] 命中率低 (${recentHitRate.toFixed(2)}), 增加缓存大小: ${this.config.maxSize} -> ${newMaxSize}`);
      }
      
      this.config.maxSize = newMaxSize;
    }
  }
  
  /**
   * 销毁
   */
  public dispose(): void {
    this.stopCleanupTimer();
    this.clear();
  }
}
