.network-status-panel {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.network-status-panel .panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.network-status-panel .panel-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.network-overview {
  margin-top: 16px;
}

.network-charts {
  margin-top: 16px;
}

.network-charts .ant-tabs-card .ant-tabs-content {
  height: 400px;
  margin-top: -16px;
}

.network-charts .ant-tabs-card .ant-tabs-content > .ant-tabs-tabpane {
  padding: 16px;
  background: #fff;
}

.network-charts .ant-tabs-card .ant-tabs-nav::before {
  display: none;
}

.network-charts .ant-tabs-card .ant-tabs-tab {
  background: transparent;
  border-color: transparent;
}

.network-charts .ant-tabs-card .ant-tabs-tab-active {
  background: #fff;
  border-color: #f0f0f0;
}

.network-status-panel .ant-statistic-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.network-status-panel .ant-statistic-content {
  font-size: 24px;
  font-weight: 500;
}

.network-status-panel .ant-progress-text {
  font-size: 12px;
}

.network-status-panel .ant-alert {
  margin: 16px 0;
}

.network-status-panel .ant-table-small {
  border: none;
}

.network-status-panel .ant-table-small .ant-table-thead > tr > th {
  background-color: #fafafa;
}

.network-status-panel .ant-table-small .ant-table-tbody > tr > td {
  padding: 8px 16px;
}

.network-status-panel .ant-tooltip {
  max-width: 300px;
}

.network-status-panel .ant-tooltip-inner {
  white-space: pre-wrap;
}

.network-dashboard {
  margin-top: 16px;
}

.network-dashboard .ant-card {
  height: 100%;
}

.network-dashboard h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
}

.network-dashboard .ant-statistic {
  text-align: center;
}

.network-dashboard .ant-statistic-content {
  font-size: 28px;
}

.network-dashboard .ant-btn-link {
  margin-top: 8px;
}
