/**
 * 响应式控件样式
 */
@import '../../styles/responsive.less';

// 响应式按钮
.responsive-button {
  &.touch-friendly {
    min-height: @touch-min-height;
    min-width: @touch-min-width;
    
    &:active {
      transform: scale(0.98);
    }
  }
  
  &.size-extra_large {
    font-size: @font-size-lg;
    padding: @spacing-lg @spacing-xl;
    height: auto;
    
    .anticon {
      font-size: @touch-icon-size;
    }
  }
  
  &.size-large {
    font-size: @font-size-md;
    padding: @spacing-md @spacing-lg;
    height: auto;
    
    .anticon {
      font-size: 20px;
    }
  }
  
  &.size-medium {
    font-size: @font-size-sm;
    
    .anticon {
      font-size: 16px;
    }
  }
}

// 响应式输入框
.responsive-input {
  &.touch-friendly {
    min-height: @touch-min-height;
    
    input {
      font-size: @font-size-md;
    }
  }
  
  &.size-extra_large {
    font-size: @font-size-lg;
    padding: @spacing-md;
    height: auto;
  }
  
  &.size-large {
    font-size: @font-size-md;
    padding: @spacing-sm;
    height: auto;
  }
  
  &.size-medium {
    font-size: @font-size-sm;
  }
}

// 响应式选择器
.responsive-select {
  &.touch-friendly {
    min-height: @touch-min-height;
    
    .ant-select-selector {
      min-height: @touch-min-height;
      padding: @spacing-sm @spacing-md !important;
    }
    
    .ant-select-selection-item {
      line-height: @touch-min-height;
      font-size: @font-size-md;
    }
  }
  
  &.size-extra_large {
    font-size: @font-size-lg;
    
    .ant-select-selector {
      height: auto !important;
      padding: @spacing-md !important;
    }
    
    .ant-select-selection-item {
      line-height: 1.5;
    }
  }
  
  &.size-large {
    font-size: @font-size-md;
    
    .ant-select-selector {
      height: auto !important;
      padding: @spacing-sm @spacing-md !important;
    }
    
    .ant-select-selection-item {
      line-height: 1.5;
    }
  }
  
  &.size-medium {
    font-size: @font-size-sm;
  }
}

// 响应式复选框
.responsive-checkbox {
  &.touch-friendly {
    .ant-checkbox {
      transform: scale(1.2);
    }
    
    .ant-checkbox + span {
      font-size: @font-size-md;
      padding-left: @spacing-md;
    }
  }
  
  &.size-extra_large {
    .ant-checkbox {
      transform: scale(1.4);
    }
    
    .ant-checkbox + span {
      font-size: @font-size-lg;
      padding-left: @spacing-lg;
    }
  }
  
  &.size-large {
    .ant-checkbox {
      transform: scale(1.2);
    }
    
    .ant-checkbox + span {
      font-size: @font-size-md;
      padding-left: @spacing-md;
    }
  }
  
  &.size-medium {
    .ant-checkbox + span {
      font-size: @font-size-sm;
    }
  }
}

// 响应式单选框
.responsive-radio {
  &.touch-friendly {
    .ant-radio {
      transform: scale(1.2);
    }
    
    .ant-radio + span {
      font-size: @font-size-md;
      padding-left: @spacing-md;
    }
  }
  
  &.size-extra_large {
    .ant-radio {
      transform: scale(1.4);
    }
    
    .ant-radio + span {
      font-size: @font-size-lg;
      padding-left: @spacing-lg;
    }
  }
  
  &.size-large {
    .ant-radio {
      transform: scale(1.2);
    }
    
    .ant-radio + span {
      font-size: @font-size-md;
      padding-left: @spacing-md;
    }
  }
  
  &.size-medium {
    .ant-radio + span {
      font-size: @font-size-sm;
    }
  }
}

// 响应式滑块
.responsive-slider {
  &.touch-friendly {
    .ant-slider-handle {
      width: 20px;
      height: 20px;
      margin-top: -8px;
    }
    
    .ant-slider-rail,
    .ant-slider-track {
      height: 6px;
    }
  }
  
  &.size-extra_large {
    .ant-slider-handle {
      width: 24px;
      height: 24px;
      margin-top: -10px;
    }
    
    .ant-slider-rail,
    .ant-slider-track {
      height: 8px;
    }
  }
  
  &.size-large {
    .ant-slider-handle {
      width: 20px;
      height: 20px;
      margin-top: -8px;
    }
    
    .ant-slider-rail,
    .ant-slider-track {
      height: 6px;
    }
  }
  
  &.size-medium {
    .ant-slider-handle {
      width: 16px;
      height: 16px;
      margin-top: -6px;
    }
    
    .ant-slider-rail,
    .ant-slider-track {
      height: 4px;
    }
  }
}

// 响应式开关
.responsive-switch {
  &.touch-friendly {
    min-height: @touch-min-height;
    min-width: 44px;
    
    &.ant-switch-small {
      min-height: 22px;
      min-width: 36px;
    }
  }
  
  &.size-extra_large {
    height: 28px;
    min-width: 56px;
    
    &::after {
      width: 24px;
      height: 24px;
    }
  }
  
  &.size-large {
    height: 24px;
    min-width: 48px;
    
    &::after {
      width: 20px;
      height: 20px;
    }
  }
}
