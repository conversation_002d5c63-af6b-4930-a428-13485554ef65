# 入门视频教程脚本

本文档提供了DL（Digital Learning）引擎编辑器入门视频教程的详细脚本，包括画面描述、旁白文本和关键点提示。

## 视频概述

- **标题**：DL（Digital Learning）引擎编辑器入门指南
- **时长**：约15分钟
- **目标受众**：初次使用DL（Digital Learning）引擎编辑器的用户
- **学习目标**：
  - 了解编辑器界面布局
  - 掌握基本导航和操作
  - 创建简单的3D场景
  - 保存和导出项目

## 视频脚本

### 1. 介绍（0:00-1:00）

**画面**：DL（Digital Learning）引擎编辑器启动画面，然后过渡到编辑器主界面。

**旁白**：
> 欢迎使用DL（Digital Learning）引擎编辑器！在这个视频教程中，我们将带您快速了解编辑器的基本功能和操作方法。无论您是3D设计新手还是有经验的开发者，这个教程都能帮助您快速上手DL（Digital Learning）引擎编辑器。
>
> DL（Digital Learning）引擎编辑器是一个功能强大的3D创作工具，可以用于创建游戏、虚拟现实体验、建筑可视化等各种3D应用。它采用直观的可视化界面，让您无需编写代码就能创建复杂的3D场景。
>
> 在开始之前，请确保您已经安装了最新版本的DL（Digital Learning）引擎编辑器。如果还没有安装，请访问我们的官方网站下载并安装。

**关键点**：
- 视频目标：快速入门DL（Digital Learning）引擎编辑器
- 编辑器用途：创建3D应用
- 前提条件：安装最新版本

### 2. 界面概览（1:00-3:30）

**画面**：展示编辑器主界面，依次高亮各个主要区域。

**旁白**：
> 让我们先来了解一下DL（Digital Learning）引擎编辑器的界面布局。编辑器界面分为几个主要区域，每个区域都有特定的功能。
>
> 顶部是菜单栏和工具栏。菜单栏包含所有功能的分类菜单，如文件、编辑、视图等。工具栏提供了常用工具的快速访问按钮，如变换工具、播放按钮等。
>
> 中央区域是场景视图，这是您创建和编辑3D场景的主要工作区。在这里，您可以直接操作场景中的对象，预览场景效果。
>
> 左侧是层次面板，显示场景中所有对象的层次结构。您可以在这里选择、重命名、重新排列对象，创建父子关系等。
>
> 下方是资产面板，用于管理项目中的所有资源，如模型、材质、纹理、脚本等。您可以从这里将资源拖放到场景中。
>
> 右侧是属性面板，显示当前选中对象的属性。您可以在这里修改对象的变换、材质、组件等属性。
>
> 底部是状态栏，显示当前编辑器状态和有用信息，如选中对象信息、坐标信息、性能指标等。

**关键点**：
- 菜单栏和工具栏：顶部，包含所有功能
- 场景视图：中央，主要工作区
- 层次面板：左侧，管理对象层次结构
- 资产面板：下方，管理项目资源
- 属性面板：右侧，编辑对象属性
- 状态栏：底部，显示状态信息

### 3. 基本导航（3:30-5:30）

**画面**：演示在场景视图中的各种导航操作。

**旁白**：
> 现在，让我们学习如何在场景中导航。熟练的导航操作是高效使用编辑器的基础。
>
> 要旋转视图，按住鼠标右键并拖动。这样您可以从不同角度查看场景。
>
> 要平移视图，按住鼠标中键或按住Alt键加鼠标左键，然后拖动。这样您可以移动视图位置而不改变视角。
>
> 要缩放视图，滚动鼠标滚轮向上或向下。向上滚动会放大视图，向下滚动会缩小视图。
>
> 如果您想快速聚焦到某个对象，只需在场景或层次面板中选择该对象，然后按F键。视图将自动聚焦到选中的对象。
>
> 编辑器还提供了多种预设视图。您可以使用数字键快速切换：1键为前视图，3键为右视图，7键为顶视图，5键可以在透视视图和正交视图之间切换。

**关键点**：
- 旋转视图：鼠标右键拖动
- 平移视图：鼠标中键或Alt+鼠标左键拖动
- 缩放视图：鼠标滚轮
- 聚焦对象：选中对象后按F键
- 预设视图：数字键1、3、7、5

### 4. 创建和选择对象（5:30-7:30）

**画面**：演示创建各种基本对象，并展示不同的选择方法。

**旁白**：
> 接下来，我们将学习如何创建和选择对象。DL（Digital Learning）引擎编辑器提供了多种基本3D对象，可以作为创建复杂场景的基础。
>
> 要创建新对象，点击顶部菜单栏的"创建"，或右键点击层次面板中的空白区域。从下拉菜单中，您可以选择要创建的对象类型，如立方体、球体、圆柱体等。
>
> 让我们创建一个立方体。点击"创建 > 3D对象 > 立方体"。一个立方体将出现在场景中，并在层次面板中添加相应的条目。
>
> 要选择对象，只需在场景视图中点击它，或在层次面板中点击其名称。选中的对象会在场景中高亮显示，其属性会显示在属性面板中。
>
> 要选择多个对象，按住Ctrl键（Windows）或Command键（Mac）的同时点击多个对象。或者，您可以在场景视图中按住鼠标左键拖动，创建一个选择框。
>
> 要取消选择，点击场景中的空白区域，或按下Esc键。

**关键点**：
- 创建对象：菜单"创建"或右键菜单
- 基本对象类型：立方体、球体、圆柱体等
- 单选：点击对象或层次面板中的名称
- 多选：Ctrl/Command+点击或框选
- 取消选择：点击空白区域或Esc键

### 5. 变换操作（7:30-10:00）

**画面**：演示移动、旋转和缩放操作，展示变换工具和属性面板的使用。

**旁白**：
> 现在，让我们学习如何变换对象，包括移动、旋转和缩放。这些是编辑3D场景最基本的操作。
>
> 首先是移动操作。选择一个对象，然后按下W键或点击工具栏中的移动工具按钮。您会看到对象上出现了三个彩色箭头，分别代表X、Y、Z三个坐标轴。拖动这些箭头可以沿着相应轴移动对象。拖动轴之间的彩色平面可以在平面上自由移动对象。
>
> 接下来是旋转操作。选择对象后，按下E键或点击旋转工具按钮。对象上会出现三个彩色环，分别代表绕X、Y、Z轴的旋转。拖动这些环可以绕相应轴旋转对象。拖动外部的白色环可以自由旋转对象。
>
> 最后是缩放操作。选择对象后，按下R键或点击缩放工具按钮。对象上会出现三个彩色轴和中心的白色立方体。拖动彩色轴可以沿相应轴缩放对象，拖动白色立方体可以统一缩放对象。
>
> 除了使用变换工具，您还可以在属性面板中直接输入精确的变换值。在属性面板的"变换"部分，您可以看到位置、旋转和缩放的数值输入框。
>
> 默认情况下，变换操作是在局部坐标系中进行的。您可以按下X键或点击工具栏中的变换空间切换按钮，在局部坐标系和世界坐标系之间切换。

**关键点**：
- 移动工具：W键，拖动坐标轴
- 旋转工具：E键，拖动旋转环
- 缩放工具：R键，拖动缩放轴
- 精确变换：属性面板中输入数值
- 变换空间切换：X键，局部/世界坐标系

### 6. 创建简单场景（10:00-12:30）

**画面**：演示创建一个简单的场景，包括地面、几个基本对象、灯光和相机。

**旁白**：
> 现在，让我们运用所学的知识，创建一个简单的场景。我们将创建一个包含地面、几个基本对象、灯光和相机的场景。
>
> 首先，创建一个平面作为地面。点击"创建 > 3D对象 > 平面"。在属性面板中，将平面的缩放设置为10,1,10，使其足够大。
>
> 接下来，添加一些基本对象。创建一个立方体，将其位置设置为0,0.5,0，这样它会位于地面上方。再创建一个球体，将其位置设置为2,1,0。最后创建一个圆柱体，将其位置设置为-2,1,0。
>
> 现在，添加一些灯光。场景默认有一个方向光，我们可以调整它的角度和强度。选择方向光，在属性面板中将其旋转设置为50,30,0，这样光线会从一个角度照射场景。然后添加一个点光源，点击"创建 > 灯光 > 点光源"，将其位置设置为0,3,0，这样它会从上方照亮场景中心。
>
> 最后，调整相机位置。选择主相机，将其位置设置为5,3,5，旋转设置为-30,-45,0，这样可以从一个俯视角度查看整个场景。
>
> 现在，我们已经创建了一个简单但完整的场景，包含地面、物体、灯光和相机。

**关键点**：
- 创建地面：平面，缩放设置
- 添加基本对象：立方体、球体、圆柱体
- 调整灯光：方向光角度，添加点光源
- 设置相机：位置和角度调整
- 场景组成：地面、物体、灯光、相机

### 7. 保存和导出（12:30-14:30）

**画面**：演示保存项目和导出场景的过程。

**旁白**：
> 最后，让我们学习如何保存项目和导出场景。保存您的工作是非常重要的，以防数据丢失，同时也方便日后继续编辑。
>
> 要保存项目，点击顶部菜单栏的"文件 > 保存项目"，或按下Ctrl+S（Windows）/Command+S（Mac）。如果是首次保存，会弹出对话框，输入项目名称和保存位置，然后点击"保存"按钮。
>
> DL（Digital Learning）引擎编辑器会自动创建项目文件夹，包含场景文件、资源文件和项目配置。建议定期保存项目，以免意外丢失工作进度。
>
> 除了保存项目，您还可以导出场景为各种格式，用于在其他应用中使用或发布。点击"文件 > 导出"，选择导出格式，如glTF、FBX或Unity包等。根据选择的格式，可能会出现不同的导出选项，根据需要进行设置，然后点击"导出"按钮。
>
> 您还可以直接发布项目为可运行的应用。点击"文件 > 构建和运行"，选择目标平台（如Web、移动端、桌面端），配置构建设置，然后点击"构建"按钮。构建完成后，您可以直接运行应用或获取发布包。

**关键点**：
- 保存项目：文件菜单或Ctrl+S/Command+S
- 项目文件结构：场景文件、资源文件、配置
- 导出场景：选择格式和设置选项
- 构建应用：选择平台和配置设置

### 8. 结束和下一步（14:30-15:00）

**画面**：回到编辑器主界面，显示更多教程的链接。

**旁白**：
> 恭喜您！您已经完成了DL（Digital Learning）引擎编辑器的入门教程。现在您已经了解了编辑器的基本界面和操作，可以开始创建自己的3D场景了。
>
> 这只是DL（Digital Learning）引擎编辑器强大功能的一小部分。要进一步学习，请查看我们的其他视频教程，如材质编辑、动画系统、物理模拟等。您还可以访问我们的文档中心，获取详细的用户指南和参考资料。
>
> 感谢观看！如果您有任何问题或反馈，请在评论区留言或访问我们的官方论坛。祝您创作愉快！

**关键点**：
- 总结学习内容：界面、导航、创建对象、变换操作、保存导出
- 下一步学习：其他视频教程、文档中心
- 获取帮助：评论区、官方论坛

## 视频制作注意事项

### 技术要求

- **分辨率**：1920x1080 (1080p)，16:9比例
- **帧率**：30fps
- **音频**：清晰的旁白，无背景音乐或轻柔的背景音乐
- **字幕**：中英文字幕

### 视觉元素

- 在关键操作时使用放大镜效果
- 使用高亮或箭头指示重要UI元素
- 在复杂操作时使用慢动作
- 添加文字标注说明快捷键
- 使用简洁的过渡效果

### 教学技巧

- 每个操作都要清晰展示鼠标移动和点击
- 解释每个操作的目的和结果
- 提供操作的替代方法（如菜单操作和快捷键）
- 在出现常见错误时提供解决方法
- 保持节奏适中，给观众足够时间理解

## 后期制作清单

- [ ] 添加开场和结束画面
- [ ] 添加章节标题和时间戳
- [ ] 添加字幕
- [ ] 添加视觉提示（箭头、高亮等）
- [ ] 添加快捷键文字提示
- [ ] 调整音频质量
- [ ] 添加视频缩略图
- [ ] 准备视频描述和标签
