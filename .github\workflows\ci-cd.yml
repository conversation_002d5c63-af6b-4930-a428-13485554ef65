name: IR Engine CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        cd engine && npm ci
        cd ../editor && npm ci
        cd ../server/api-gateway && npm ci
        cd ../service-registry && npm ci
        cd ../user-service && npm ci
        cd ../project-service && npm ci
        cd ../asset-service && npm ci
        cd ../render-service && npm ci
    
    - name: Run tests
      run: |
        cd engine && npm test
        cd ../editor && npm test
        cd ../server/api-gateway && npm test
        cd ../service-registry && npm test
        cd ../user-service && npm test
        cd ../project-service && npm test
        cd ../asset-service && npm test
        cd ../render-service && npm test
  
  build:
    name: Build
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to DockerHub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}
    
    - name: Build and push Engine
      uses: docker/build-push-action@v4
      with:
        context: ./engine
        push: true
        tags: irengine/engine:latest
    
    - name: Build and push Editor
      uses: docker/build-push-action@v4
      with:
        context: ./editor
        push: true
        tags: irengine/editor:latest
    
    - name: Build and push API Gateway
      uses: docker/build-push-action@v4
      with:
        context: ./server/api-gateway
        push: true
        tags: irengine/api-gateway:latest
    
    - name: Build and push Service Registry
      uses: docker/build-push-action@v4
      with:
        context: ./server/service-registry
        push: true
        tags: irengine/service-registry:latest
    
    - name: Build and push User Service
      uses: docker/build-push-action@v4
      with:
        context: ./server/user-service
        push: true
        tags: irengine/user-service:latest
    
    - name: Build and push Project Service
      uses: docker/build-push-action@v4
      with:
        context: ./server/project-service
        push: true
        tags: irengine/project-service:latest
    
    - name: Build and push Asset Service
      uses: docker/build-push-action@v4
      with:
        context: ./server/asset-service
        push: true
        tags: irengine/asset-service:latest
    
    - name: Build and push Render Service
      uses: docker/build-push-action@v4
      with:
        context: ./server/render-service
        push: true
        tags: irengine/render-service:latest
  
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Copy docker-compose.yml to server
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.DEPLOY_HOST }}
        username: ${{ secrets.DEPLOY_USERNAME }}
        key: ${{ secrets.DEPLOY_KEY }}
        source: "docker-compose.yml,.env.example"
        target: "/opt/ir-engine"
    
    - name: Deploy to server
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.DEPLOY_HOST }}
        username: ${{ secrets.DEPLOY_USERNAME }}
        key: ${{ secrets.DEPLOY_KEY }}
        script: |
          cd /opt/ir-engine
          cp .env.example .env
          docker-compose pull
          docker-compose down
          docker-compose up -d
