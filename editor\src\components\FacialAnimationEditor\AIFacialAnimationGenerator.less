/**
 * AI面部动画生成面板样式
 */
.ai-facial-animation-generator {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f0f2f5;
  border-radius: 4px;
  overflow: hidden;
  
  .generator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #fff;
    border-bottom: 1px solid #e8e8e8;
    
    .generator-title {
      font-size: 16px;
      font-weight: 500;
      
      .anticon {
        margin-right: 8px;
        color: #1890ff;
      }
    }
    
    .generator-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .generator-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    
    .generator-main {
      flex: 1;
      padding: 16px;
      overflow-y: auto;
      
      .form-row {
        display: flex;
        gap: 16px;
        
        .form-item-half {
          flex: 1;
        }
      }
      
      .section-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 12px;
        color: #333;
      }
      
      .model-selection {
        margin-top: 16px;
        
        .ant-radio-wrapper {
          margin-bottom: 8px;
        }
      }
    }
    
    .generator-sidebar {
      width: 280px;
      padding: 16px;
      background-color: #fff;
      border-left: 1px solid #e8e8e8;
      overflow-y: auto;
      
      .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
      
      .section-title {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        
        .anticon {
          margin-right: 8px;
        }
      }
      
      .advanced-settings {
        padding: 8px 0;
        
        .feature-list {
          margin-top: 8px;
          
          .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            padding: 4px 8px;
            background-color: #f5f5f5;
            border-radius: 4px;
            
            .feature-name {
              font-size: 13px;
              color: #333;
              cursor: help;
            }
          }
        }
      }
      
      .history-list {
        margin-top: 8px;
        
        .history-item {
          margin-bottom: 8px;
          cursor: pointer;
          transition: all 0.3s;
          
          &:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }
          
          .history-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
            
            .history-item-time {
              font-size: 12px;
              color: #999;
            }
          }
          
          .history-item-prompt {
            font-size: 13px;
            color: #333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        
        .empty-history {
          padding: 16px 0;
          text-align: center;
          color: #999;
          font-size: 13px;
        }
      }
    }
  }
}

// 暗色主题适配
.dark {
  .ai-facial-animation-generator {
    background-color: #1f1f1f;
    
    .generator-header {
      background-color: #141414;
      border-bottom-color: #303030;
      
      .generator-title {
        color: #fff;
      }
    }
    
    .generator-content {
      .generator-main {
        .section-title {
          color: #d9d9d9;
        }
      }
      
      .generator-sidebar {
        background-color: #141414;
        border-left-color: #303030;
        
        .section-title {
          color: #d9d9d9;
        }
        
        .advanced-settings {
          .feature-list {
            .feature-item {
              background-color: #1f1f1f;
              
              .feature-name {
                color: #d9d9d9;
              }
            }
          }
        }
        
        .history-list {
          .history-item {
            background-color: #1f1f1f;
            
            &:hover {
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            }
            
            .history-item-prompt {
              color: #d9d9d9;
            }
          }
        }
      }
    }
  }
}
