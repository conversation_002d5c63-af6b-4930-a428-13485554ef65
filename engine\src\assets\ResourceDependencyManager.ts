/**
 * 资源依赖管理器
 * 用于管理资源之间的依赖关系
 */
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 依赖类型
 */
export enum DependencyType {
  /** 强依赖（必须加载） */
  STRONG = 'strong',
  /** 弱依赖（可选加载） */
  WEAK = 'weak',
}

/**
 * 依赖信息
 */
export interface DependencyInfo {
  /** 依赖资源ID */
  id: string;
  /** 依赖类型 */
  type: DependencyType;
}

/**
 * 资源依赖管理器
 */
export class ResourceDependencyManager extends EventEmitter {
  /** 依赖映射（资源ID -> 依赖资源ID数组） */
  private dependencies: Map<string, DependencyInfo[]> = new Map();
  
  /** 反向依赖映射（被依赖资源ID -> 依赖它的资源ID数组） */
  private reverseDependencies: Map<string, string[]> = new Map();
  
  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建资源依赖管理器实例
   */
  constructor() {
    super();
  }

  /**
   * 初始化资源依赖管理器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }
    
    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 添加依赖关系
   * @param resourceId 资源ID
   * @param dependencyId 依赖资源ID
   * @param type 依赖类型
   */
  public addDependency(resourceId: string, dependencyId: string, type: DependencyType = DependencyType.STRONG): void {
    // 如果资源ID和依赖资源ID相同，则忽略
    if (resourceId === dependencyId) {
      return;
    }
    
    // 获取资源的依赖列表
    let deps = this.dependencies.get(resourceId);
    
    if (!deps) {
      deps = [];
      this.dependencies.set(resourceId, deps);
    }
    
    // 检查是否已存在相同依赖
    const existingDep = deps.find(dep => dep.id === dependencyId);
    
    if (existingDep) {
      // 如果已存在，则更新依赖类型（强依赖优先）
      if (type === DependencyType.STRONG) {
        existingDep.type = DependencyType.STRONG;
      }
    } else {
      // 添加新依赖
      deps.push({ id: dependencyId, type });
      
      // 更新反向依赖
      let revDeps = this.reverseDependencies.get(dependencyId);
      
      if (!revDeps) {
        revDeps = [];
        this.reverseDependencies.set(dependencyId, revDeps);
      }
      
      if (!revDeps.includes(resourceId)) {
        revDeps.push(resourceId);
      }
      
      // 发出依赖添加事件
      this.emit('dependencyAdded', { resourceId, dependencyId, type });
    }
  }

  /**
   * 移除依赖关系
   * @param resourceId 资源ID
   * @param dependencyId 依赖资源ID
   * @returns 是否成功移除
   */
  public removeDependency(resourceId: string, dependencyId: string): boolean {
    // 获取资源的依赖列表
    const deps = this.dependencies.get(resourceId);
    
    if (!deps) {
      return false;
    }
    
    // 查找依赖索引
    const index = deps.findIndex(dep => dep.id === dependencyId);
    
    if (index === -1) {
      return false;
    }
    
    // 移除依赖
    deps.splice(index, 1);
    
    // 如果依赖列表为空，则移除映射
    if (deps.length === 0) {
      this.dependencies.delete(resourceId);
    }
    
    // 更新反向依赖
    const revDeps = this.reverseDependencies.get(dependencyId);
    
    if (revDeps) {
      const revIndex = revDeps.indexOf(resourceId);
      
      if (revIndex !== -1) {
        revDeps.splice(revIndex, 1);
        
        // 如果反向依赖列表为空，则移除映射
        if (revDeps.length === 0) {
          this.reverseDependencies.delete(dependencyId);
        }
      }
    }
    
    // 发出依赖移除事件
    this.emit('dependencyRemoved', { resourceId, dependencyId });
    
    return true;
  }

  /**
   * 获取资源的依赖
   * @param resourceId 资源ID
   * @returns 依赖信息数组
   */
  public getDependencies(resourceId: string): DependencyInfo[] {
    return this.dependencies.get(resourceId) || [];
  }

  /**
   * 获取资源的强依赖
   * @param resourceId 资源ID
   * @returns 依赖资源ID数组
   */
  public getStrongDependencies(resourceId: string): string[] {
    const deps = this.getDependencies(resourceId);
    return deps.filter(dep => dep.type === DependencyType.STRONG).map(dep => dep.id);
  }

  /**
   * 获取资源的弱依赖
   * @param resourceId 资源ID
   * @returns 依赖资源ID数组
   */
  public getWeakDependencies(resourceId: string): string[] {
    const deps = this.getDependencies(resourceId);
    return deps.filter(dep => dep.type === DependencyType.WEAK).map(dep => dep.id);
  }

  /**
   * 获取依赖资源的资源
   * @param dependencyId 依赖资源ID
   * @returns 资源ID数组
   */
  public getReverseDependencies(dependencyId: string): string[] {
    return this.reverseDependencies.get(dependencyId) || [];
  }

  /**
   * 获取资源的所有依赖（包括间接依赖）
   * @param resourceId 资源ID
   * @param includeWeak 是否包含弱依赖
   * @returns 依赖资源ID数组
   */
  public getAllDependencies(resourceId: string, includeWeak: boolean = false): string[] {
    const result = new Set<string>();
    
    // 递归获取依赖
    const collectDependencies = (id: string) => {
      const deps = this.getDependencies(id);
      
      for (const dep of deps) {
        // 如果不包含弱依赖且当前依赖是弱依赖，则跳过
        if (!includeWeak && dep.type === DependencyType.WEAK) {
          continue;
        }
        
        // 如果已经添加过，则跳过
        if (result.has(dep.id)) {
          continue;
        }
        
        // 添加依赖
        result.add(dep.id);
        
        // 递归获取依赖的依赖
        collectDependencies(dep.id);
      }
    };
    
    collectDependencies(resourceId);
    
    return Array.from(result);
  }

  /**
   * 检查是否存在循环依赖
   * @param resourceId 资源ID
   * @returns 是否存在循环依赖
   */
  public hasCircularDependency(resourceId: string): boolean {
    const visited = new Set<string>();
    const path = new Set<string>();
    
    // 深度优先搜索检查循环依赖
    const dfs = (id: string): boolean => {
      // 如果已经在当前路径中，则存在循环依赖
      if (path.has(id)) {
        return true;
      }
      
      // 如果已经访问过且不存在循环依赖，则跳过
      if (visited.has(id)) {
        return false;
      }
      
      // 标记为已访问
      visited.add(id);
      
      // 添加到当前路径
      path.add(id);
      
      // 检查所有依赖
      const deps = this.getDependencies(id);
      
      for (const dep of deps) {
        if (dfs(dep.id)) {
          return true;
        }
      }
      
      // 从当前路径中移除
      path.delete(id);
      
      return false;
    };
    
    return dfs(resourceId);
  }

  /**
   * 获取资源的依赖树
   * @param resourceId 资源ID
   * @param includeWeak 是否包含弱依赖
   * @returns 依赖树
   */
  public getDependencyTree(resourceId: string, includeWeak: boolean = false): any {
    const tree: any = {
      id: resourceId,
      dependencies: [],
    };
    
    // 已处理的资源ID集合，用于避免循环依赖
    const processed = new Set<string>();
    
    // 递归构建依赖树
    const buildTree = (node: any, id: string) => {
      // 如果已经处理过，则跳过
      if (processed.has(id)) {
        return;
      }
      
      // 标记为已处理
      processed.add(id);
      
      // 获取依赖
      const deps = this.getDependencies(id);
      
      for (const dep of deps) {
        // 如果不包含弱依赖且当前依赖是弱依赖，则跳过
        if (!includeWeak && dep.type === DependencyType.WEAK) {
          continue;
        }
        
        // 创建依赖节点
        const depNode = {
          id: dep.id,
          type: dep.type,
          dependencies: [],
        };
        
        // 添加到当前节点的依赖列表
        node.dependencies.push(depNode);
        
        // 递归构建依赖的依赖
        buildTree(depNode, dep.id);
      }
    };
    
    buildTree(tree, resourceId);
    
    return tree;
  }

  /**
   * 获取资源的依赖排序（拓扑排序）
   * @param resourceId 资源ID
   * @param includeWeak 是否包含弱依赖
   * @returns 排序后的资源ID数组（从依赖到被依赖）
   */
  public getDependencyOrder(resourceId: string, includeWeak: boolean = false): string[] {
    const result: string[] = [];
    const visited = new Set<string>();
    
    // 深度优先搜索进行拓扑排序
    const dfs = (id: string) => {
      // 如果已经访问过，则跳过
      if (visited.has(id)) {
        return;
      }
      
      // 标记为已访问
      visited.add(id);
      
      // 获取依赖
      const deps = this.getDependencies(id);
      
      for (const dep of deps) {
        // 如果不包含弱依赖且当前依赖是弱依赖，则跳过
        if (!includeWeak && dep.type === DependencyType.WEAK) {
          continue;
        }
        
        // 递归处理依赖
        dfs(dep.id);
      }
      
      // 将当前资源添加到结果中
      result.push(id);
    };
    
    dfs(resourceId);
    
    return result;
  }

  /**
   * 清空所有依赖关系
   */
  public clear(): void {
    this.dependencies.clear();
    this.reverseDependencies.clear();
    
    // 发出清空事件
    this.emit('cleared');
  }

  /**
   * 销毁资源依赖管理器
   */
  public dispose(): void {
    // 清空所有依赖关系
    this.clear();
    
    // 移除所有事件监听器
    this.removeAllListeners();
    
    this.initialized = false;
  }
}
