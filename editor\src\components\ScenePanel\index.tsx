/**
 * 场景面板组件
 */
import React, { useState } from 'react';
import { Tree, Button, Input, Dropdown, Menu, message } from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  LockOutlined,
  UnlockOutlined,
  CopyOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { selectEntity, addEntity, removeEntity, updateEntity } from '../../store/scene/sceneSlice';
import './ScenePanel.less';

const { Search } = Input;

const ScenePanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { entities, selectedEntityId } = useSelector((state: RootState) => state.scene);
  const [searchValue, setSearchValue] = useState<string>('');

  // 将实体数据转换为Tree组件所需的数据结构
  const convertToTreeData = (entities: any[], parentId: string | null = null) => {
    return entities
      .filter(entity => entity.parentId === parentId)
      .map(entity => ({
        key: entity.id,
        title: (
          <div className="entity-item">
            <span className="entity-title">{entity.name}</span>
            <div className="entity-actions">
              <Button
                type="text"
                size="small"
                icon={entity.visible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  dispatch(updateEntity({
                    id: entity.id,
                    changes: { visible: !entity.visible }
                  }));
                }}
              />
              <Dropdown
                overlay={
                  <Menu
                    items={[
                      {
                        key: 'copy',
                        icon: <CopyOutlined />,
                        label: t('editor.scene.duplicate'),
                        onClick: () => {
                          message.info(`复制实体: ${entity.name}`);
                        }
                      },
                      {
                        key: 'lock',
                        icon: entity.locked ? <UnlockOutlined /> : <LockOutlined />,
                        label: entity.locked ? t('editor.scene.unlock') : t('editor.scene.lock'),
                        onClick: () => {
                          dispatch(updateEntity({
                            id: entity.id,
                            changes: { locked: !entity.locked }
                          }));
                        }
                      },
                      {
                        key: 'delete',
                        icon: <DeleteOutlined />,
                        label: t('editor.scene.delete'),
                        danger: true,
                        onClick: () => {
                          dispatch(removeEntity(entity.id));
                        }
                      }
                    ]}
                  />
                }
                trigger={['click']}
              >
                <Button
                  type="text"
                  size="small"
                  icon={<MoreOutlined />}
                  onClick={(e) => e.stopPropagation()}
                />
              </Dropdown>
            </div>
          </div>
        ),
        children: convertToTreeData(entities, entity.id),
        className: `entity-node ${entity.locked ? 'entity-locked' : ''} ${!entity.visible ? 'entity-hidden' : ''}`
      }));
  };

  // 过滤实体
  const filterEntities = (entities: any[], searchValue: string) => {
    if (!searchValue) return entities;
    
    return entities.filter(entity => 
      entity.name.toLowerCase().includes(searchValue.toLowerCase())
    );
  };

  const treeData = convertToTreeData(filterEntities(entities, searchValue));

  // 处理添加实体
  const handleAddEntity = () => {
    const newEntity = {
      id: `entity-${Date.now()}`,
      name: `新实体 ${entities.length + 1}`,
      type: 'mesh',
      parentId: selectedEntityId || null,
      visible: true,
      locked: false,
      transform: {
        position: [0, 0, 0],
        rotation: [0, 0, 0],
        scale: [1, 1, 1]
      },
      components: {}
    };
    
    dispatch(addEntity(newEntity));
  };

  return (
    <div className="scene-panel">
      <div className="panel-header">
        <h3>{t('editor.panels.scene')}</h3>
        <Button
          type="primary"
          size="small"
          icon={<PlusOutlined />}
          onClick={handleAddEntity}
        >
          {t('editor.scene.add')}
        </Button>
      </div>
      
      <Search
        placeholder={t('editor.scene.search')}
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        className="search-input"
      />
      
      <div className="tree-container">
        <Tree
          treeData={treeData}
          selectedKeys={selectedEntityId ? [selectedEntityId] : []}
          onSelect={(selectedKeys) => {
            if (selectedKeys.length > 0) {
              dispatch(selectEntity(selectedKeys[0] as string));
            }
          }}
          draggable
          blockNode
        />
      </div>
    </div>
  );
};

export default ScenePanel;
