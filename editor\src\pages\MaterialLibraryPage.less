/**
 * 材质库页面样式
 */
.material-library-page {
  height: 100vh;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    z-index: 1;
    
    .header-title {
      h1 {
        margin: 0;
        font-size: 20px;
      }
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .page-content {
    padding: 24px;
    overflow-y: auto;
    
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 400px;
    }
    
    .material-preview {
      height: 150px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 4px 4px 0 0;
    }
    
    .material-type {
      margin-top: 8px;
    }
  }
}
