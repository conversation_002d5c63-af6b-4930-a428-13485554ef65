/**
 * 可停靠面板布局样式
 */

.dock-layout-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  // 亮色主题样式
  &.light-theme {
    .dock-panel {
      background-color: #f0f2f5;
      border-color: #d9d9d9;
    }

    .dock-tab {
      background-color: #f5f5f5;
      color: #333;
      
      &.dock-tab-active {
        background-color: #fff;
        color: #1890ff;
      }
      
      &:hover {
        background-color: #e6f7ff;
      }
    }
    
    .dock-divider {
      background-color: #d9d9d9;
    }
  }

  // 暗色主题样式
  &.dark-theme {
    .dock-panel {
      background-color: #1e1e1e;
      border-color: #303030;
    }

    .dock-tab {
      background-color: #252526;
      color: #ccc;
      
      &.dock-tab-active {
        background-color: #1e1e1e;
        color: #1890ff;
      }
      
      &:hover {
        background-color: #2c2c2c;
      }
    }
    
    .dock-divider {
      background-color: #303030;
    }
  }
}

// 自定义rc-dock样式
.dock-panel {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  
  .dock-bar {
    padding: 4px;
  }
  
  .dock-content {
    padding: 8px;
  }
}

.dock-tab {
  border-radius: 4px 4px 0 0;
  margin-right: 2px;
  padding: 4px 12px;
  transition: all 0.3s;
  
  .dock-tab-close-btn {
    margin-left: 8px;
    opacity: 0.5;
    
    &:hover {
      opacity: 1;
    }
  }
}

.dock-divider {
  &:hover {
    background-color: #1890ff;
  }
}

.dock-drop-indicator {
  background-color: #1890ff;
  border-radius: 2px;
}
