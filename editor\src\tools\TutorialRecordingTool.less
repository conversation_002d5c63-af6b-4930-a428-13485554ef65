/**
 * 视频教程录制辅助工具样式
 */

.tutorial-recording-tool {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .ant-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .ant-card-body {
      flex: 1;
      overflow: auto;
      display: flex;
      flex-direction: column;
    }
  }
  
  .tutorial-info {
    margin-bottom: 16px;
    
    h4 {
      margin-bottom: 8px;
    }
    
    .chapter-navigation {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;
    }
  }
  
  .ant-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .ant-tabs-content {
      flex: 1;
      overflow: auto;
    }
  }
  
  .script-content {
    padding: 16px;
    background-color: #f9f9f9;
    border-radius: 4px;
    
    .script-section {
      margin-bottom: 24px;
      
      h5 {
        margin-bottom: 8px;
        color: #1890ff;
      }
      
      .ant-typography {
        font-size: 16px;
        line-height: 1.6;
      }
      
      .ant-list-item {
        padding: 4px 0;
        border-bottom: none;
      }
    }
  }
  
  .recording-controls {
    padding: 16px;
    
    .recording-status {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .status-indicator {
        .ant-tag {
          font-size: 14px;
          padding: 4px 8px;
        }
      }
      
      .time-display {
        font-size: 16px;
        font-weight: bold;
      }
    }
    
    .audio-meter {
      margin-bottom: 24px;
      
      .ant-progress {
        margin-bottom: 4px;
        
        &.ant-progress-status-active .ant-progress-bg {
          background-color: #52c41a;
        }
        
        &.ant-progress-status-exception .ant-progress-bg {
          background-color: #ff4d4f;
        }
      }
      
      .audio-level-text {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
    
    .recording-buttons {
      display: flex;
      justify-content: center;
      margin-bottom: 16px;
      
      .ant-btn {
        min-width: 100px;
      }
    }
    
    .recording-notes {
      margin-top: 16px;
      
      h5 {
        margin-bottom: 8px;
      }
    }
  }
  
  .countdown-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .countdown {
      font-size: 64px;
      font-weight: bold;
      color: #1890ff;
      margin-bottom: 16px;
      width: 100px;
      height: 100px;
      border-radius: 50%;
      background-color: #e6f7ff;
      display: flex;
      justify-content: center;
      align-items: center;
      animation: pulse 1s infinite;
    }
    
    .ant-btn {
      margin-top: 16px;
    }
  }
  
  .settings-content {
    .setting-item {
      margin-bottom: 24px;
      
      .ant-typography {
        display: block;
        margin-bottom: 8px;
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
