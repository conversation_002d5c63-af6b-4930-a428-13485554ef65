/**
 * 地下环境光照编辑器组件
 * 用于编辑地下环境光照属性
 */
import React, { useState, useEffect } from 'react';
import { Form, Input, InputNumber, Select, Switch, Slider, Button, Card, Tabs, Row, Col, Tooltip, Space, Divider, List } from 'antd';
import { 
  BulbOutlined, 
  SettingOutlined, 
  FireOutlined, 
  HighlightOutlined, 
  CloudOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../../store';
import { Vector3Input } from '../../common/Vector3Input';
import { ColorPicker } from '../../common/ColorPicker';

const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 地下环境光照编辑器属性
 */
interface UndergroundLightingEditorProps {
  /** 实体ID */
  entityId: string;
}

/**
 * 洞穴光照配置
 */
interface CaveLightingConfig {
  id: string;
  position: any; // Vector3
  color: string;
  intensity: number;
  size: any; // Vector3
  decay: number;
  castShadow: boolean;
}

/**
 * 钟乳石反射光配置
 */
interface StalactiteReflectionConfig {
  id: string;
  position: any; // Vector3
  color: string;
  intensity: number;
  size: number;
  flickerSpeed: number;
  flickerIntensity: number;
}

/**
 * 水面反射光配置
 */
interface WaterReflectionConfig {
  id: string;
  position: any; // Vector3
  color: string;
  intensity: number;
  size: any; // Vector2
  waveFrequency: number;
  waveIntensity: number;
}

/**
 * 体积光配置
 */
interface VolumetricLightConfig {
  id: string;
  position: any; // Vector3
  direction: any; // Vector3
  color: string;
  intensity: number;
  length: number;
  angle: number;
  decay: number;
  density: number;
}

/**
 * 体积雾配置
 */
interface VolumetricFogConfig {
  id: string;
  position: any; // Vector3
  size: any; // Vector3
  color: string;
  density: number;
  enableNoise: boolean;
  noiseScale: number;
  noiseSpeed: number;
}

/**
 * 地下环境光照编辑器组件
 */
const UndergroundLightingEditor: React.FC<UndergroundLightingEditorProps> = ({ entityId }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  
  // 状态
  const [activeTab, setActiveTab] = useState<string>('system');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editingLightId, setEditingLightId] = useState<string | null>(null);
  const [editingLightType, setEditingLightType] = useState<string | null>(null);
  
  // 从Redux获取地下环境光照数据
  const undergroundLighting = useSelector((state: RootState) => 
    state.rendering.undergroundLighting.find(ul => ul.entityId === entityId)
  );
  
  // 初始化表单
  useEffect(() => {
    if (undergroundLighting) {
      form.setFieldsValue({
        // 系统属性
        enabled: undergroundLighting.enabled,
        autoUpdate: undergroundLighting.autoUpdate,
        updateFrequency: undergroundLighting.updateFrequency,
        enableCaveLighting: undergroundLighting.enableCaveLighting,
        enableStalactiteReflection: undergroundLighting.enableStalactiteReflection,
        enableWaterReflection: undergroundLighting.enableWaterReflection,
        enableVolumetricLight: undergroundLighting.enableVolumetricLight,
        enableVolumetricFog: undergroundLighting.enableVolumetricFog,
        enableDebugVisualization: undergroundLighting.enableDebugVisualization,
        enablePerformanceMonitoring: undergroundLighting.enablePerformanceMonitoring,
      });
    }
  }, [undergroundLighting, form]);
  
  // 处理表单变更
  const handleValuesChange = (changedValues: any, allValues: any) => {
    // 更新Redux状态
    if (entityId) {
      // 这里应该dispatch一个action来更新地下环境光照属性
      // dispatch(updateUndergroundLightingProperties(entityId, changedValues));
    }
  };
  
  // 处理保存
  const handleSave = () => {
    form.validateFields().then(values => {
      // 保存到Redux
      // dispatch(saveUndergroundLightingProperties(entityId, values));
      setIsEditing(false);
    });
  };
  
  // 处理取消
  const handleCancel = () => {
    // 重置表单
    if (undergroundLighting) {
      form.setFieldsValue({
        // 重置为原始值
        // ...
      });
    }
    setIsEditing(false);
  };
  
  // 处理添加光源
  const handleAddLight = (type: string) => {
    // 根据类型创建不同的光源
    switch (type) {
      case 'cave':
        // 添加洞穴光照
        // dispatch(addCaveLight(entityId, { /* 默认配置 */ }));
        break;
      case 'stalactite':
        // 添加钟乳石反射光
        // dispatch(addStalactiteReflection(entityId, { /* 默认配置 */ }));
        break;
      case 'water':
        // 添加水面反射光
        // dispatch(addWaterReflection(entityId, { /* 默认配置 */ }));
        break;
      case 'volumetricLight':
        // 添加体积光
        // dispatch(addVolumetricLight(entityId, { /* 默认配置 */ }));
        break;
      case 'volumetricFog':
        // 添加体积雾
        // dispatch(addVolumetricFog(entityId, { /* 默认配置 */ }));
        break;
    }
  };
  
  // 处理编辑光源
  const handleEditLight = (type: string, id: string) => {
    setEditingLightType(type);
    setEditingLightId(id);
    
    // 根据类型获取不同的光源数据
    let lightData;
    switch (type) {
      case 'cave':
        lightData = undergroundLighting?.caveLights.find(light => light.id === id);
        break;
      case 'stalactite':
        lightData = undergroundLighting?.stalactiteReflections.find(light => light.id === id);
        break;
      case 'water':
        lightData = undergroundLighting?.waterReflections.find(light => light.id === id);
        break;
      case 'volumetricLight':
        lightData = undergroundLighting?.volumetricLights.find(light => light.id === id);
        break;
      case 'volumetricFog':
        lightData = undergroundLighting?.volumetricFogs.find(light => light.id === id);
        break;
    }
    
    if (lightData) {
      // 设置表单值
      form.setFieldsValue(lightData);
    }
  };
  
  // 处理删除光源
  const handleDeleteLight = (type: string, id: string) => {
    // 根据类型删除不同的光源
    switch (type) {
      case 'cave':
        // 删除洞穴光照
        // dispatch(removeCaveLight(entityId, id));
        break;
      case 'stalactite':
        // 删除钟乳石反射光
        // dispatch(removeStalactiteReflection(entityId, id));
        break;
      case 'water':
        // 删除水面反射光
        // dispatch(removeWaterReflection(entityId, id));
        break;
      case 'volumetricLight':
        // 删除体积光
        // dispatch(removeVolumetricLight(entityId, id));
        break;
      case 'volumetricFog':
        // 删除体积雾
        // dispatch(removeVolumetricFog(entityId, id));
        break;
    }
  };
  
  // 渲染系统属性标签页
  const renderSystemTab = () => {
    return (
      <div className="system-tab">
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleValuesChange}
          disabled={!isEditing}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enabled"
                label={t('editor.rendering.undergroundLighting.enabled')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="autoUpdate"
                label={t('editor.rendering.undergroundLighting.autoUpdate')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="updateFrequency"
            label={t('editor.rendering.undergroundLighting.updateFrequency')}
            tooltip={t('editor.rendering.undergroundLighting.updateFrequencyTooltip')}
          >
            <InputNumber min={1} max={60} step={1} style={{ width: '100%' }} />
          </Form.Item>
          
          <Divider>{t('editor.rendering.undergroundLighting.features')}</Divider>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableCaveLighting"
                label={t('editor.rendering.undergroundLighting.enableCaveLighting')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="enableStalactiteReflection"
                label={t('editor.rendering.undergroundLighting.enableStalactiteReflection')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableWaterReflection"
                label={t('editor.rendering.undergroundLighting.enableWaterReflection')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="enableVolumetricLight"
                label={t('editor.rendering.undergroundLighting.enableVolumetricLight')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableVolumetricFog"
                label={t('editor.rendering.undergroundLighting.enableVolumetricFog')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="enableDebugVisualization"
                label={t('editor.rendering.undergroundLighting.enableDebugVisualization')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="enablePerformanceMonitoring"
            label={t('editor.rendering.undergroundLighting.enablePerformanceMonitoring')}
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Form>
      </div>
    );
  };
  
  // 渲染洞穴光照标签页
  const renderCaveLightingTab = () => {
    return (
      <div className="cave-lighting-tab">
        <div className="light-list-header">
          <h3>{t('editor.rendering.undergroundLighting.caveLights')}</h3>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={() => handleAddLight('cave')}
            disabled={!isEditing}
          >
            {t('editor.rendering.undergroundLighting.addCaveLight')}
          </Button>
        </div>
        
        <List
          dataSource={undergroundLighting?.caveLights || []}
          renderItem={(item: CaveLightingConfig) => (
            <List.Item
              actions={[
                <Button 
                  icon={<EditOutlined />} 
                  onClick={() => handleEditLight('cave', item.id)}
                  disabled={!isEditing}
                />,
                <Button 
                  danger 
                  icon={<DeleteOutlined />} 
                  onClick={() => handleDeleteLight('cave', item.id)}
                  disabled={!isEditing}
                />
              ]}
            >
              <List.Item.Meta
                title={`${t('editor.rendering.undergroundLighting.caveLight')} ${item.id}`}
                description={`${t('editor.rendering.undergroundLighting.intensity')}: ${item.intensity}, ${t('editor.rendering.undergroundLighting.color')}: ${item.color}`}
              />
            </List.Item>
          )}
        />
        
        {editingLightType === 'cave' && editingLightId && (
          <Card title={t('editor.rendering.undergroundLighting.editCaveLight')} style={{ marginTop: 16 }}>
            <Form
              form={form}
              layout="vertical"
              onValuesChange={handleValuesChange}
              disabled={!isEditing}
            >
              <Form.Item
                name="position"
                label={t('editor.rendering.undergroundLighting.position')}
              >
                <Vector3Input />
              </Form.Item>
              
              <Form.Item
                name="color"
                label={t('editor.rendering.undergroundLighting.color')}
              >
                <ColorPicker />
              </Form.Item>
              
              <Form.Item
                name="intensity"
                label={t('editor.rendering.undergroundLighting.intensity')}
              >
                <Slider min={0} max={10} step={0.1} />
              </Form.Item>
              
              <Form.Item
                name="size"
                label={t('editor.rendering.undergroundLighting.size')}
              >
                <Vector3Input />
              </Form.Item>
              
              <Form.Item
                name="decay"
                label={t('editor.rendering.undergroundLighting.decay')}
              >
                <Slider min={0} max={2} step={0.1} />
              </Form.Item>
              
              <Form.Item
                name="castShadow"
                label={t('editor.rendering.undergroundLighting.castShadow')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              
              <Button type="primary" onClick={() => setEditingLightId(null)}>
                {t('editor.common.done')}
              </Button>
            </Form>
          </Card>
        )}
      </div>
    );
  };
  
  // 渲染钟乳石反射光标签页
  const renderStalactiteReflectionTab = () => {
    return (
      <div className="stalactite-reflection-tab">
        <div className="light-list-header">
          <h3>{t('editor.rendering.undergroundLighting.stalactiteReflections')}</h3>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={() => handleAddLight('stalactite')}
            disabled={!isEditing}
          >
            {t('editor.rendering.undergroundLighting.addStalactiteReflection')}
          </Button>
        </div>
        
        <List
          dataSource={undergroundLighting?.stalactiteReflections || []}
          renderItem={(item: StalactiteReflectionConfig) => (
            <List.Item
              actions={[
                <Button 
                  icon={<EditOutlined />} 
                  onClick={() => handleEditLight('stalactite', item.id)}
                  disabled={!isEditing}
                />,
                <Button 
                  danger 
                  icon={<DeleteOutlined />} 
                  onClick={() => handleDeleteLight('stalactite', item.id)}
                  disabled={!isEditing}
                />
              ]}
            >
              <List.Item.Meta
                title={`${t('editor.rendering.undergroundLighting.stalactiteReflection')} ${item.id}`}
                description={`${t('editor.rendering.undergroundLighting.intensity')}: ${item.intensity}, ${t('editor.rendering.undergroundLighting.color')}: ${item.color}`}
              />
            </List.Item>
          )}
        />
        
        {editingLightType === 'stalactite' && editingLightId && (
          <Card title={t('editor.rendering.undergroundLighting.editStalactiteReflection')} style={{ marginTop: 16 }}>
            <Form
              form={form}
              layout="vertical"
              onValuesChange={handleValuesChange}
              disabled={!isEditing}
            >
              <Form.Item
                name="position"
                label={t('editor.rendering.undergroundLighting.position')}
              >
                <Vector3Input />
              </Form.Item>
              
              <Form.Item
                name="color"
                label={t('editor.rendering.undergroundLighting.color')}
              >
                <ColorPicker />
              </Form.Item>
              
              <Form.Item
                name="intensity"
                label={t('editor.rendering.undergroundLighting.intensity')}
              >
                <Slider min={0} max={10} step={0.1} />
              </Form.Item>
              
              <Form.Item
                name="size"
                label={t('editor.rendering.undergroundLighting.size')}
              >
                <InputNumber min={0.1} max={10} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name="flickerSpeed"
                label={t('editor.rendering.undergroundLighting.flickerSpeed')}
              >
                <Slider min={0} max={5} step={0.1} />
              </Form.Item>
              
              <Form.Item
                name="flickerIntensity"
                label={t('editor.rendering.undergroundLighting.flickerIntensity')}
              >
                <Slider min={0} max={1} step={0.01} />
              </Form.Item>
              
              <Button type="primary" onClick={() => setEditingLightId(null)}>
                {t('editor.common.done')}
              </Button>
            </Form>
          </Card>
        )}
      </div>
    );
  };
  
  // 渲染水面反射光标签页
  const renderWaterReflectionTab = () => {
    // 类似于洞穴光照和钟乳石反射光的实现
    return (
      <div className="water-reflection-tab">
        {/* 实现水面反射光编辑界面 */}
      </div>
    );
  };
  
  // 渲染体积光标签页
  const renderVolumetricLightTab = () => {
    // 类似于洞穴光照和钟乳石反射光的实现
    return (
      <div className="volumetric-light-tab">
        {/* 实现体积光编辑界面 */}
      </div>
    );
  };
  
  // 渲染体积雾标签页
  const renderVolumetricFogTab = () => {
    // 类似于洞穴光照和钟乳石反射光的实现
    return (
      <div className="volumetric-fog-tab">
        {/* 实现体积雾编辑界面 */}
      </div>
    );
  };
  
  return (
    <div className="component-editor underground-lighting-editor">
      <Card 
        title={
          <Space>
            <BulbOutlined />
            {t('editor.rendering.undergroundLighting.title')}
          </Space>
        }
        extra={
          <Space>
            {isEditing ? (
              <>
                <Button onClick={handleCancel}>{t('editor.common.cancel')}</Button>
                <Button type="primary" onClick={handleSave}>{t('editor.common.save')}</Button>
              </>
            ) : (
              <Button type="primary" onClick={() => setIsEditing(true)}>{t('editor.common.edit')}</Button>
            )}
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <span>
                <SettingOutlined />
                {t('editor.rendering.undergroundLighting.systemTab')}
              </span>
            } 
            key="system"
          >
            {renderSystemTab()}
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <FireOutlined />
                {t('editor.rendering.undergroundLighting.caveLightingTab')}
              </span>
            } 
            key="caveLighting"
          >
            {renderCaveLightingTab()}
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <HighlightOutlined />
                {t('editor.rendering.undergroundLighting.stalactiteReflectionTab')}
              </span>
            } 
            key="stalactiteReflection"
          >
            {renderStalactiteReflectionTab()}
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <WaterOutlined />
                {t('editor.rendering.undergroundLighting.waterReflectionTab')}
              </span>
            } 
            key="waterReflection"
          >
            {renderWaterReflectionTab()}
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <BulbOutlined />
                {t('editor.rendering.undergroundLighting.volumetricLightTab')}
              </span>
            } 
            key="volumetricLight"
          >
            {renderVolumetricLightTab()}
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <CloudOutlined />
                {t('editor.rendering.undergroundLighting.volumetricFogTab')}
              </span>
            } 
            key="volumetricFog"
          >
            {renderVolumetricFogTab()}
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default UndergroundLightingEditor;
