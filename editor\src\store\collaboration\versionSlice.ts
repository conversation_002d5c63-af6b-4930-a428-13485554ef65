/**
 * 版本历史状态管理Slice
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../../store';
import { Version } from '../../services/VersionHistoryService';

// 版本历史状态接口
export interface VersionState {
  versions: Version[];
  currentVersionId: string | null;
  showVersionPanel: boolean;
  isRollingBack: boolean;
}

// 初始状态
const initialState: VersionState = {
  versions: [],
  currentVersionId: null,
  showVersionPanel: false,
  isRollingBack: false
};

// 创建Slice
export const versionSlice = createSlice({
  name: 'version',
  initialState,
  reducers: {
    // 添加版本
    addVersion: (state, action: PayloadAction<Version>) => {
      // 检查是否已存在相同ID的版本
      const existingIndex = state.versions.findIndex(v => v.id === action.payload.id);
      
      if (existingIndex >= 0) {
        // 更新现有版本
        state.versions[existingIndex] = action.payload;
      } else {
        // 添加新版本
        state.versions.push(action.payload);
      }
    },
    
    // 设置版本列表
    setVersions: (state, action: PayloadAction<Version[]>) => {
      state.versions = action.payload;
    },
    
    // 设置当前版本
    setCurrentVersion: (state, action: PayloadAction<string | null>) => {
      state.currentVersionId = action.payload;
    },
    
    // 显示/隐藏版本面板
    setShowVersionPanel: (state, action: PayloadAction<boolean>) => {
      state.showVersionPanel = action.payload;
    },
    
    // 设置是否正在回滚
    setIsRollingBack: (state, action: PayloadAction<boolean>) => {
      state.isRollingBack = action.payload;
    },
    
    // 清除所有版本
    clearVersions: (state) => {
      state.versions = [];
      state.currentVersionId = null;
    },
    
    // 删除版本
    deleteVersion: (state, action: PayloadAction<string>) => {
      state.versions = state.versions.filter(v => v.id !== action.payload);
      
      // 如果删除的是当前版本，清除当前版本ID
      if (state.currentVersionId === action.payload) {
        state.currentVersionId = null;
      }
    }
  }
});

// 导出Actions
export const {
  addVersion,
  setVersions,
  setCurrentVersion,
  setShowVersionPanel,
  setIsRollingBack,
  clearVersions,
  deleteVersion
} = versionSlice.actions;

// 选择器
export const selectVersions = (state: RootState) => state.version.versions;
export const selectCurrentVersionId = (state: RootState) => state.version.currentVersionId;
export const selectCurrentVersion = (state: RootState) => {
  const id = state.version.currentVersionId;
  return id ? state.version.versions.find(v => v.id === id) || null : null;
};
export const selectShowVersionPanel = (state: RootState) => state.version.showVersionPanel;
export const selectIsRollingBack = (state: RootState) => state.version.isRollingBack;

// 导出Reducer
export default versionSlice.reducer;
