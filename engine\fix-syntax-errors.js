#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 批量修复语法错误的脚本
 */

// 修复规则
const fixes = [
  // 1. 修复 this.(property as any) 语法错误
  {
    name: '修复this属性访问语法',
    pattern: /this\.\(([^)]+)\)/g,
    replacement: '(this.$1)'
  },
  
  // 2. 修复 object.(property as any) 语法错误
  {
    name: '修复对象属性访问语法',
    pattern: /(\w+)\.\(([^)]+)\)/g,
    replacement: '($1.$2)'
  },
  
  // 3. 修复 (super as any).dispose() 语法错误
  {
    name: '修复super调用语法',
    pattern: /\(super as any\)\.dispose\(\);?/g,
    replacement: 'super.dispose();'
  },
  
  // 4. 修复 object.property.(subproperty as any) 语法错误
  {
    name: '修复嵌套属性访问语法',
    pattern: /(\w+\.\w+)\.\(([^)]+)\)/g,
    replacement: '($1.$2)'
  }
];

/**
 * 获取所有TypeScript文件
 * @param {string} dir 目录路径
 * @returns {string[]} 文件路径数组
 */
function getAllTsFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // 跳过node_modules和其他不需要的目录
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          traverse(fullPath);
        }
      } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

/**
 * 修复单个文件
 * @param {string} filePath 文件路径
 * @returns {boolean} 是否有修改
 */
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    for (const fix of fixes) {
      const originalContent = content;
      content = content.replace(fix.pattern, fix.replacement);
      
      if (content !== originalContent) {
        modified = true;
        console.log(`应用修复规则 "${fix.name}" 到文件: ${path.relative(process.cwd(), filePath)}`);
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`修复文件失败 ${filePath}:`, error.message);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('src目录不存在');
    process.exit(1);
  }
  
  console.log('开始批量修复语法错误...\n');
  
  const files = getAllTsFiles(srcDir);
  console.log(`找到 ${files.length} 个TypeScript文件`);
  
  let fixedCount = 0;
  for (const file of files) {
    if (fixFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n修复完成！`);
  console.log(`已修复 ${fixedCount} 个文件`);
  
  if (fixedCount > 0) {
    console.log('\n建议运行以下命令检查编译状态：');
    console.log('npx tsc --noEmit');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixFile, getAllTsFiles };
