/**
 * 物理体类型定义
 */

export enum BodyType {
  /** 静态物体 - 不会移动，无限质量 */
  STATIC = 'static',
  /** 动态物体 - 可以移动，受力影响 */
  DYNAMIC = 'dynamic',
  /** 运动学物体 - 可以移动，但不受力影响 */
  KINEMATIC = 'kinematic'
}

export interface BodyTypeConfig {
  /** 物体类型 */
  type: BodyType;
  /** 质量 */
  mass?: number;
  /** 是否受重力影响 */
  useGravity?: boolean;
  /** 是否为触发器 */
  isTrigger?: boolean;
  /** 线性阻尼 */
  linearDamping?: number;
  /** 角阻尼 */
  angularDamping?: number;
}

export class BodyTypeHelper {
  /**
   * 检查物体类型是否为静态
   */
  public static isStatic(type: BodyType): boolean {
    return type === BodyType.STATIC;
  }

  /**
   * 检查物体类型是否为动态
   */
  public static isDynamic(type: BodyType): boolean {
    return type === BodyType.DYNAMIC;
  }

  /**
   * 检查物体类型是否为运动学
   */
  public static isKinematic(type: BodyType): boolean {
    return type === BodyType.KINEMATIC;
  }

  /**
   * 检查物体是否可以移动
   */
  public static canMove(type: BodyType): boolean {
    return type === BodyType.DYNAMIC || type === BodyType.KINEMATIC;
  }

  /**
   * 检查物体是否受力影响
   */
  public static isAffectedByForces(type: BodyType): boolean {
    return type === BodyType.DYNAMIC;
  }

  /**
   * 获取默认配置
   */
  public static getDefaultConfig(type: BodyType): BodyTypeConfig {
    switch (type) {
      case BodyType.STATIC:
        return {
          type,
          mass: 0,
          useGravity: false,
          isTrigger: false,
          linearDamping: 0,
          angularDamping: 0
        };
      case BodyType.DYNAMIC:
        return {
          type,
          mass: 1,
          useGravity: true,
          isTrigger: false,
          linearDamping: 0.1,
          angularDamping: 0.1
        };
      case BodyType.KINEMATIC:
        return {
          type,
          mass: 0,
          useGravity: false,
          isTrigger: false,
          linearDamping: 0,
          angularDamping: 0
        };
      default:
        return {
          type: BodyType.DYNAMIC,
          mass: 1,
          useGravity: true,
          isTrigger: false,
          linearDamping: 0.1,
          angularDamping: 0.1
        };
    }
  }
}
