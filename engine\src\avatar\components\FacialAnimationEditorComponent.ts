/**
 * 面部动画编辑器组件
 * 用于编辑和管理面部动画
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EventEmitter, type EventCallback } from '../../utils/EventEmitter';
import { FacialAnimationComponent, FacialExpressionType, VisemeType } from './FacialAnimationComponent';
import { FacialAnimationClip } from '../animation/FacialAnimationClip';

/**
 * 编辑器状态
 */
export enum EditorState {
  /** 停止 */
  STOPPED = 'stopped',
  /** 播放 */
  PLAYING = 'playing',
  /** 暂停 */
  PAUSED = 'paused',
  /** 录制 */
  RECORDING = 'recording'
}

/**
 * 编辑器历史记录项
 */
interface EditorHistoryItem {
  /** 片段名称 */
  clipName: string;
  /** 片段数据 */
  clipData: any;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 面部动画编辑器组件类型
 */
export const FacialAnimationEditorComponentType = 'FacialAnimationEditorComponent';

/**
 * 面部动画编辑器组件
 */
export class FacialAnimationEditorComponent extends Component {
  /** 组件类型 */
  static readonly TYPE = FacialAnimationEditorComponentType;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();



  /** 动画片段映射 */
  private clips: Map<string, FacialAnimationClip> = new Map();

  /** 当前片段名称 */
  private currentClipName: string = '';

  /** 当前时间（秒） */
  private currentTime: number = 0;

  /** 播放速度 */
  private playbackSpeed: number = 1.0;

  /** 编辑器状态 */
  private state: EditorState = EditorState.STOPPED;

  /** 历史记录 */
  private history: EditorHistoryItem[] = [];

  /** 历史记录索引 */
  private historyIndex: number = -1;

  /** 最大历史记录数 */
  private maxHistoryItems: number = 20;

  /** 是否启用撤销/重做 */
  private undoRedoEnabled: boolean = true;

  /** 是否循环播放 */
  private loop: boolean = true;

  /** 是否启用自动保存 */
  private autoSave: boolean = true;

  /** 自动保存间隔（毫秒） */
  private autoSaveInterval: number = 60000;

  /** 自动保存计时器 */
  private autoSaveTimer: number = 0;

  /** 是否启用调试 */
  private debug: boolean = false;

  /**
   * 构造函数
   * @param entity 实体
   */
  constructor(entity: Entity) {
    super(FacialAnimationEditorComponentType);
    this.setEntity(entity);
  }

  /**
   * 获取组件类型
   */
  public getType(): string {
    return FacialAnimationEditorComponent.TYPE;
  }



  /**
   * 添加动画片段
   * @param clip 动画片段
   * @returns 是否成功添加
   */
  public addClip(clip: FacialAnimationClip): boolean {
    if (!this.isEnabled()) return false;

    // 检查是否已存在同名片段
    if (this.clips.has(clip.name)) {
      if (this.debug) {
        console.warn('已存在同名片段:', clip.name);
      }
      return false;
    }

    // 添加片段
    this.clips.set(clip.name, clip);

    // 如果是第一个片段，设置为当前片段
    if (this.clips.size === 1) {
      this.currentClipName = clip.name;
    }

    // 发出事件
    this.eventEmitter.emit('clipAdded', { clip });

    return true;
  }

  /**
   * 移除动画片段
   * @param name 片段名称
   * @returns 是否成功移除
   */
  public removeClip(name: string): boolean {
    if (!this.isEnabled()) return false;

    // 检查是否存在片段
    if (!this.clips.has(name)) {
      return false;
    }

    // 获取片段
    const clip = this.clips.get(name)!;

    // 移除片段
    this.clips.delete(name);

    // 如果是当前片段，重置当前片段
    if (this.currentClipName === name) {
      this.currentClipName = this.clips.size > 0 ? Array.from(this.clips.keys())[0] : '';
      this.currentTime = 0;
      this.state = EditorState.STOPPED;
    }

    // 发出事件
    this.eventEmitter.emit('clipRemoved', { clip });

    return true;
  }

  /**
   * 获取动画片段
   * @param name 片段名称
   * @returns 动画片段
   */
  public getClip(name: string): FacialAnimationClip | null {
    return this.clips.get(name) || null;
  }

  /**
   * 获取当前动画片段
   * @returns 当前动画片段
   */
  public getCurrentClip(): FacialAnimationClip | null {
    return this.currentClipName ? this.clips.get(this.currentClipName) || null : null;
  }

  /**
   * 获取所有动画片段
   * @returns 动画片段映射
   */
  public getAllClips(): Map<string, FacialAnimationClip> {
    return new Map(this.clips);
  }

  /**
   * 设置当前动画片段
   * @param name 片段名称
   * @returns 是否成功设置
   */
  public setCurrentClip(name: string): boolean {
    if (!this.isEnabled()) return false;

    // 检查是否存在片段
    if (!this.clips.has(name)) {
      return false;
    }

    // 保存当前状态
    const wasPlaying = this.state === EditorState.PLAYING;

    // 停止当前播放
    this.stop();

    // 设置当前片段
    this.currentClipName = name;
    this.currentTime = 0;

    // 如果之前在播放，继续播放
    if (wasPlaying) {
      this.play();
    }

    // 发出事件
    this.eventEmitter.emit('currentClipChanged', { clipName: name });

    return true;
  }

  /**
   * 播放
   * @returns 是否成功播放
   */
  public play(): boolean {
    if (!this.isEnabled()) return false;

    // 检查是否有当前片段
    if (!this.currentClipName || !this.clips.has(this.currentClipName)) {
      return false;
    }

    // 设置状态为播放
    this.state = EditorState.PLAYING;

    // 发出事件
    this.eventEmitter.emit('playbackStarted', { clipName: this.currentClipName });

    return true;
  }

  /**
   * 停止
   * @returns 是否成功停止
   */
  public stop(): boolean {
    if (!this.isEnabled()) return false;

    // 检查是否有当前片段
    if (!this.currentClipName || !this.clips.has(this.currentClipName)) {
      return false;
    }

    // 设置状态为停止
    this.state = EditorState.STOPPED;
    this.currentTime = 0;

    // 发出事件
    this.eventEmitter.emit('playbackStopped', { clipName: this.currentClipName });

    return true;
  }

  /**
   * 暂停
   * @returns 是否成功暂停
   */
  public pause(): boolean {
    if (!this.isEnabled()) return false;

    // 检查是否有当前片段
    if (!this.currentClipName || !this.clips.has(this.currentClipName)) {
      return false;
    }

    // 检查是否正在播放
    if (this.state !== EditorState.PLAYING) {
      return false;
    }

    // 设置状态为暂停
    this.state = EditorState.PAUSED;

    // 发出事件
    this.eventEmitter.emit('playbackPaused', { clipName: this.currentClipName });

    return true;
  }

  /**
   * 设置播放速度
   * @param speed 速度
   * @returns 是否成功设置
   */
  public setPlaybackSpeed(speed: number): boolean {
    if (!this.isEnabled()) return false;

    // 检查速度是否有效
    if (speed <= 0) {
      return false;
    }

    // 设置播放速度
    this.playbackSpeed = speed;

    // 发出事件
    this.eventEmitter.emit('playbackSpeedChanged', { speed });

    return true;
  }

  /**
   * 设置当前时间
   * @param time 时间（秒）
   * @returns 是否成功设置
   */
  public setCurrentTime(time: number): boolean {
    if (!this.isEnabled()) return false;

    // 检查是否有当前片段
    if (!this.currentClipName || !this.clips.has(this.currentClipName)) {
      return false;
    }

    // 获取当前片段
    const clip = this.clips.get(this.currentClipName)!;

    // 检查时间是否有效
    if (time < 0 || time > clip.duration) {
      return false;
    }

    // 设置当前时间
    this.currentTime = time;

    // 发出事件
    this.eventEmitter.emit('timeChanged', { time });

    return true;
  }

  /**
   * 添加表情关键帧
   * @param time 时间（秒）
   * @param expression 表情类型
   * @param weight 权重
   * @returns 是否成功添加
   */
  public addExpressionKeyframe(
    time: number,
    expression: FacialExpressionType,
    weight: number = 1.0
  ): boolean {
    if (!this.isEnabled()) return false;

    // 检查是否有当前片段
    if (!this.currentClipName || !this.clips.has(this.currentClipName)) {
      return false;
    }

    // 获取当前片段
    const clip = this.clips.get(this.currentClipName)!;

    // 检查时间是否有效
    if (time < 0 || time > clip.duration) {
      return false;
    }

    // 保存历史记录
    this.saveHistory();

    // 添加关键帧
    const index = clip.addExpressionKeyframe(time, expression, weight);

    // 发出事件
    this.eventEmitter.emit('expressionKeyframeAdded', { time, expression, weight, index });

    return true;
  }

  /**
   * 添加口型关键帧
   * @param time 时间（秒）
   * @param viseme 口型类型
   * @param weight 权重
   * @returns 是否成功添加
   */
  public addVisemeKeyframe(
    time: number,
    viseme: VisemeType,
    weight: number = 1.0
  ): boolean {
    if (!this.isEnabled()) return false;

    // 检查是否有当前片段
    if (!this.currentClipName || !this.clips.has(this.currentClipName)) {
      return false;
    }

    // 获取当前片段
    const clip = this.clips.get(this.currentClipName)!;

    // 检查时间是否有效
    if (time < 0 || time > clip.duration) {
      return false;
    }

    // 保存历史记录
    this.saveHistory();

    // 添加关键帧
    const index = clip.addVisemeKeyframe(time, viseme, weight);

    // 发出事件
    this.eventEmitter.emit('visemeKeyframeAdded', { time, viseme, weight, index });

    return true;
  }

  /**
   * 移除表情关键帧
   * @param time 时间（秒）
   * @returns 是否成功移除
   */
  public removeExpressionKeyframe(time: number): boolean {
    if (!this.isEnabled()) return false;

    // 检查是否有当前片段
    if (!this.currentClipName || !this.clips.has(this.currentClipName)) {
      return false;
    }

    // 获取当前片段
    const clip = this.clips.get(this.currentClipName)!;

    // 保存历史记录
    this.saveHistory();

    // 获取所有表情关键帧
    const keyframes = clip.getAllExpressionKeyframes();

    // 查找最接近的关键帧
    let closestIndex = -1;
    let closestDistance = Number.MAX_VALUE;

    for (let i = 0; i < keyframes.length; i++) {
      const distance = Math.abs(keyframes[i].time - time);
      if (distance < closestDistance) {
        closestDistance = distance;
        closestIndex = i;
      }
    }

    // 如果找到关键帧，移除它
    if (closestIndex >= 0 && closestDistance < 0.1) {
      const removed = clip.removeExpressionKeyframe(closestIndex);

      if (removed) {
        // 发出事件
        this.eventEmitter.emit('expressionKeyframeRemoved', { time, index: closestIndex });
      }

      return removed;
    }

    return false;
  }

  /**
   * 移除口型关键帧
   * @param time 时间（秒）
   * @returns 是否成功移除
   */
  public removeVisemeKeyframe(time: number): boolean {
    if (!this.isEnabled()) return false;

    // 检查是否有当前片段
    if (!this.currentClipName || !this.clips.has(this.currentClipName)) {
      return false;
    }

    // 获取当前片段
    const clip = this.clips.get(this.currentClipName)!;

    // 保存历史记录
    this.saveHistory();

    // 获取所有口型关键帧
    const keyframes = clip.getAllVisemeKeyframes();

    // 查找最接近的关键帧
    let closestIndex = -1;
    let closestDistance = Number.MAX_VALUE;

    for (let i = 0; i < keyframes.length; i++) {
      const distance = Math.abs(keyframes[i].time - time);
      if (distance < closestDistance) {
        closestDistance = distance;
        closestIndex = i;
      }
    }

    // 如果找到关键帧，移除它
    if (closestIndex >= 0 && closestDistance < 0.1) {
      const removed = clip.removeVisemeKeyframe(closestIndex);

      if (removed) {
        // 发出事件
        this.eventEmitter.emit('visemeKeyframeRemoved', { time, index: closestIndex });
      }

      return removed;
    }

    return false;
  }

  /**
   * 保存历史记录
   */
  private saveHistory(): void {
    if (!this.undoRedoEnabled) return;

    // 检查是否有当前片段
    if (!this.currentClipName || !this.clips.has(this.currentClipName)) {
      return;
    }

    // 获取当前片段
    const clip = this.clips.get(this.currentClipName)!;

    // 创建历史记录项
    const historyItem: EditorHistoryItem = {
      clipName: this.currentClipName,
      clipData: clip.toJSON(),
      timestamp: Date.now()
    };

    // 如果不是在历史记录的末尾，删除后面的历史记录
    if (this.historyIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.historyIndex + 1);
    }

    // 添加历史记录
    this.history.push(historyItem);
    this.historyIndex = this.history.length - 1;

    // 如果历史记录超过最大数量，删除最早的记录
    if (this.history.length > this.maxHistoryItems) {
      this.history.shift();
      this.historyIndex--;
    }
  }

  /**
   * 撤销
   * @returns 是否成功撤销
   */
  public undo(): boolean {
    if (!this.enabled || !this.undoRedoEnabled) return false;

    // 检查是否有历史记录
    if (this.historyIndex <= 0) {
      return false;
    }

    // 减少历史记录索引
    this.historyIndex--;

    // 获取历史记录项
    const historyItem = this.history[this.historyIndex];

    // 恢复片段
    const clip = FacialAnimationClip.fromJSON(historyItem.clipData);
    this.clips.set(historyItem.clipName, clip);

    // 设置当前片段
    this.currentClipName = historyItem.clipName;

    // 发出事件
    this.eventEmitter.emit('undo', { clipName: historyItem.clipName });

    return true;
  }

  /**
   * 重做
   * @returns 是否成功重做
   */
  public redo(): boolean {
    if (!this.enabled || !this.undoRedoEnabled) return false;

    // 检查是否有历史记录
    if (this.historyIndex >= this.history.length - 1) {
      return false;
    }

    // 增加历史记录索引
    this.historyIndex++;

    // 获取历史记录项
    const historyItem = this.history[this.historyIndex];

    // 恢复片段
    const clip = FacialAnimationClip.fromJSON(historyItem.clipData);
    this.clips.set(historyItem.clipName, clip);

    // 设置当前片段
    this.currentClipName = historyItem.clipName;

    // 发出事件
    this.eventEmitter.emit('redo', { clipName: historyItem.clipName });

    return true;
  }

  /**
   * 保存状态
   */
  public saveState(): void {
    // 检查是否有当前片段
    if (!this.currentClipName || !this.clips.has(this.currentClipName)) {
      return;
    }

    // 发出事件
    this.eventEmitter.emit('stateSaved', { clipName: this.currentClipName });
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.isEnabled()) return;

    // 检查是否有当前片段
    if (!this.currentClipName || !this.clips.has(this.currentClipName)) {
      return;
    }

    // 获取当前片段
    const clip = this.clips.get(this.currentClipName)!;

    // 更新自动保存计时器
    if (this.autoSave) {
      this.autoSaveTimer += deltaTime * 1000;

      if (this.autoSaveTimer >= this.autoSaveInterval) {
        this.saveState();
        this.autoSaveTimer = 0;
      }
    }

    // 如果正在播放，更新当前时间
    if (this.state === EditorState.PLAYING) {
      this.currentTime += deltaTime * this.playbackSpeed;

      // 检查是否到达片段结束
      if (this.currentTime >= clip.duration) {
        if (this.loop) {
          // 循环播放
          this.currentTime = 0;
        } else {
          // 停止播放
          this.state = EditorState.STOPPED;
          this.currentTime = clip.duration;

          // 发出事件
          this.eventEmitter.emit('playbackCompleted', { clipName: this.currentClipName });
        }
      }

      // 发出事件
      this.eventEmitter.emit('timeChanged', { time: this.currentTime });
    }

    // 获取面部动画组件
    const facialComponent = this.entity.getComponent(FacialAnimationComponent.TYPE) as unknown as FacialAnimationComponent;
    if (facialComponent) {
      // 获取当前表情和口型
      const { expression, weight: expressionWeight } = clip.getExpressionAtTime(this.currentTime);
      const { viseme, weight: visemeWeight } = clip.getVisemeAtTime(this.currentTime);

      // 应用表情和口型
      facialComponent.setExpression(expression, expressionWeight);
      facialComponent.setViseme(viseme, visemeWeight);
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (...args: any[]) => void): void {
    this.eventEmitter.off(event, callback);
  }
}
