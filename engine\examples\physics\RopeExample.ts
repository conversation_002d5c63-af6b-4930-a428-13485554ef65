/**
 * 绳索物理示例
 * 展示如何使用软体物理系统创建绳索效果
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Vector3 } from '../../src/math/Vector3';
import { Quaternion } from '../../src/math/Quaternion';
import { 
  PhysicsSystem, 
  PhysicsBodyComponent, 
  PhysicsColliderComponent,
  BodyType,
  ColliderType,
  PhysicsMaterialFactory
} from '../../src/physics';
import { SoftBodySystem } from '../../src/physics/softbody/SoftBodySystem';
import { SoftBodyComponent, SoftBodyType } from '../../src/physics/softbody/SoftBodyComponent';
import { InputSystem } from '../../src/input/InputSystem';
import { KeyCode } from '../../src/input/KeyCode';

/**
 * 绳索物理示例
 */
export class RopeExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  
  /** 软体物理系统 */
  private softBodySystem: SoftBodySystem;
  
  /** 输入系统 */
  private inputSystem: InputSystem;
  
  /** 地面实体 */
  private ground: Entity;
  
  /** 绳索实体 */
  private rope: Entity;
  
  /** 绳索末端附着的物体 */
  private weight: Entity;
  
  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建绳索物理示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建场景
    this.scene = new Scene('绳索物理示例场景');
    
    // 创建物理系统
    this.physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.82, z: 0 },
      debug: true
    });
    
    // 创建软体物理系统
    this.softBodySystem = new SoftBodySystem({
      physicsSystem: this.physicsSystem,
      debug: true
    });
    
    // 创建输入系统
    this.inputSystem = new InputSystem();
    
    // 添加系统到引擎
    this.engine.addSystem(this.physicsSystem);
    this.engine.addSystem(this.softBodySystem);
    this.engine.addSystem(this.inputSystem);
    
    // 初始化物理材质工厂
    PhysicsMaterialFactory.initialize();
    
    // 创建地面
    this.ground = this.createGround();
    
    // 添加地面到场景
    this.scene.addEntity(this.ground);
    
    // 注册更新回调
    this.engine.onUpdate.add(this.update.bind(this));
  }

  /**
   * 初始化示例
   */
  public initialize(): void {
    if (this.initialized) return;
    
    // 创建绳索
    this.rope = this.createRope();
    this.scene.addEntity(this.rope);
    
    // 创建重物
    this.weight = this.createWeight();
    this.scene.addEntity(this.weight);
    
    // 设置场景为活跃场景
    this.engine.getWorld().setActiveScene(this.scene);
    
    this.initialized = true;
  }

  /**
   * 更新
   * @param deltaTime 帧间隔时间（秒）
   */
  private update(deltaTime: number): void {
    // 处理输入
    this.handleInput();
  }

  /**
   * 处理输入
   */
  private handleInput(): void {
    // 移动绳索固定点
    if (this.inputSystem.isKeyPressed(KeyCode.W)) {
      this.moveRopeAnchor(0, 0, -0.1);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.S)) {
      this.moveRopeAnchor(0, 0, 0.1);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.A)) {
      this.moveRopeAnchor(-0.1, 0, 0);
    }
    
    if (this.inputSystem.isKeyPressed(KeyCode.D)) {
      this.moveRopeAnchor(0.1, 0, 0);
    }
    
    // 施加力到重物
    if (this.inputSystem.isKeyPressed(KeyCode.SPACE)) {
      this.applyForceToWeight();
    }
  }

  /**
   * 移动绳索固定点
   * @param x X轴移动量
   * @param y Y轴移动量
   * @param z Z轴移动量
   */
  private moveRopeAnchor(x: number, y: number, z: number): void {
    const transform = this.rope.getTransform();
    const position = transform.getPosition();
    position.x += x;
    position.z += z;
    transform.setPosition(position.x, position.y, position.z);
    
    // 更新软体位置
    const softBody = this.rope.getComponent(SoftBodyComponent);
    if (softBody) {
      // 注意：这需要在SoftBodyComponent中添加updateAnchorPosition方法
      // softBody.updateAnchorPosition(position);
    }
  }

  /**
   * 施加力到重物
   */
  private applyForceToWeight(): void {
    const physicsBody = this.weight.getComponent(PhysicsBodyComponent);
    if (physicsBody) {
      // 施加向上的力
      physicsBody.applyImpulse(new Vector3(0, 10, 0));
    }
  }

  /**
   * 创建地面
   * @returns 地面实体
   */
  private createGround(): Entity {
    // 创建地面实体
    const ground = new Entity('ground');
    
    // 添加变换组件
    const transform = ground.getTransform();
    transform.setPosition(0, -2, 0);
    
    // 创建地面网格
    const geometry = new THREE.BoxGeometry(20, 1, 20);
    const material = new THREE.MeshStandardMaterial({ color: 0x999999 });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    ground.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    ground.addComponent(new PhysicsBodyComponent({
      type: BodyType.STATIC,
      mass: 0
    }));
    
    // 添加碰撞器组件
    ground.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: 10, y: 0.5, z: 10 }
      }
    }));
    
    return ground;
  }

  /**
   * 创建绳索
   * @returns 绳索实体
   */
  private createRope(): Entity {
    // 创建绳索实体
    const rope = new Entity('rope');
    
    // 添加变换组件
    const transform = rope.getTransform();
    transform.setPosition(0, 8, 0);
    
    // 添加软体组件
    const softBodyComponent = new SoftBodyComponent({
      type: SoftBodyType.ROPE,
      mass: 1,
      stiffness: 100,
      damping: 0.1,
      fixedEnds: true,
      params: {
        segments: 20,
        length: 10
      }
    });
    
    rope.addComponent(softBodyComponent);
    
    // 将软体组件添加到软体系统
    this.softBodySystem.addSoftBody(softBodyComponent);
    
    return rope;
  }

  /**
   * 创建重物
   * @returns 重物实体
   */
  private createWeight(): Entity {
    // 创建重物实体
    const weight = new Entity('weight');
    
    // 添加变换组件
    const transform = weight.getTransform();
    transform.setPosition(0, 0, 0);
    
    // 创建重物网格
    const geometry = new THREE.SphereGeometry(0.5, 32, 32);
    const material = new THREE.MeshStandardMaterial({ color: 0xff0000 });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    weight.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    weight.addComponent(new PhysicsBodyComponent({
      type: BodyType.DYNAMIC,
      mass: 5
    }));
    
    // 添加碰撞器组件
    weight.addComponent(new PhysicsColliderComponent({
      type: ColliderType.SPHERE,
      params: {
        radius: 0.5
      }
    }));
    
    return weight;
  }
}
