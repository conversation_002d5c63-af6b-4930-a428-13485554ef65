# DL（Digital Learning）引擎示例项目结构说明

本文档介绍了DL（Digital Learning）引擎示例项目的标准目录结构和文件组织方式，以帮助开发者理解和创建符合规范的示例项目。

## 目录

- [总体结构](#总体结构)
- [示例项目结构](#示例项目结构)
- [资源组织](#资源组织)
- [文档要求](#文档要求)
- [命名规范](#命名规范)
- [最佳实践](#最佳实践)

## 总体结构

DL（Digital Learning）引擎示例项目库的总体结构如下：

```
/examples/
  ├── index.html                # 示例项目浏览器
  ├── assets/                   # 共享资源
  │   ├── images/               # 示例项目预览图
  │   ├── models/               # 共享3D模型
  │   ├── textures/             # 共享纹理
  │   └── scripts/              # 共享脚本
  ├── editor-basics/            # 基础编辑器功能演示
  ├── material-editor/          # 材质编辑器演示
  ├── animation-demo/           # 动画系统演示
  ├── physics-demo/             # 物理系统演示
  ├── visualscript-demo/        # 视觉脚本演示
  ├── performance-optimization/ # 性能优化最佳实践
  ├── collaborative-editing/    # 协作编辑最佳实践
  ├── asset-management/         # 资产管理最佳实践
  ├── ui-design/                # UI设计最佳实践
  ├── cross-platform/           # 跨平台发布最佳实践
  ├── beginner-tutorial/        # 新手入门教程
  ├── scene-building-tutorial/  # 场景构建教程
  ├── character-creation-tutorial/ # 角色创建教程
  ├── gameplay-tutorial/        # 游戏机制教程
  └── multiplayer-tutorial/     # 多人场景教程
```

## 示例项目结构

每个示例项目应遵循以下标准结构：

```
/[示例项目名称]/
  ├── index.html                # 主入口文件
  ├── README.md                 # 项目说明文档
  ├── assets/                   # 项目特定资源
  │   ├── images/               # 图片资源
  │   ├── models/               # 3D模型
  │   ├── textures/             # 纹理
  │   └── scripts/              # 脚本
  ├── scenes/                   # 场景文件
  │   ├── main.json             # 主场景
  │   └── [其他场景].json       # 其他场景
  ├── scripts/                  # 项目脚本
  │   ├── main.js               # 主脚本
  │   └── [其他脚本].js         # 其他脚本
  └── styles/                   # 样式文件
      └── main.css              # 主样式文件
```

### 主入口文件 (index.html)

主入口文件应包含以下内容：

1. 基本HTML结构
2. 引擎初始化代码
3. 场景加载代码
4. 用户界面元素
5. 交互控制代码

示例：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>示例项目名称</title>
  <link rel="stylesheet" href="styles/main.css">
</head>
<body>
  <div id="canvas-container"></div>
  <div id="ui-container">
    <!-- UI元素 -->
  </div>
  
  <script type="module">
    import { Engine, World, Scene } from '/engine/dist/index.js';
    import { initScene } from './scripts/main.js';
    
    // 初始化引擎
    const engine = new Engine({
      container: document.getElementById('canvas-container'),
      antialias: true,
    });
    
    // 创建世界
    const world = new World(engine);
    
    // 初始化场景
    initScene(engine, world);
    
    // 启动引擎
    engine.start();
  </script>
</body>
</html>
```

### 项目说明文档 (README.md)

项目说明文档应包含以下内容：

1. 项目名称和简介
2. 功能特性列表
3. 使用说明
4. 技术要点说明
5. 学习要点
6. 扩展建议

示例：

```markdown
# 示例项目名称

## 简介

简要描述项目的目的和内容。

## 功能特性

- 特性1：描述
- 特性2：描述
- 特性3：描述

## 使用说明

1. 步骤1
2. 步骤2
3. 步骤3

## 技术要点

- 技术点1：描述
- 技术点2：描述
- 技术点3：描述

## 学习要点

- 学习点1：描述
- 学习点2：描述
- 学习点3：描述

## 扩展建议

- 扩展1：描述
- 扩展2：描述
- 扩展3：描述
```

## 资源组织

### 共享资源

共享资源位于 `/examples/assets/` 目录下，包括：

- **images/**: 示例项目预览图和共享图片资源
- **models/**: 共享3D模型
- **textures/**: 共享纹理
- **scripts/**: 共享脚本

### 项目特定资源

项目特定资源位于项目目录下的 `assets/` 目录中，结构与共享资源类似。

## 文档要求

每个示例项目应包含以下文档：

1. **README.md**: 项目说明文档
2. **代码注释**: 详细的代码注释，解释关键功能和实现方式
3. **UI提示**: 用户界面中的提示和说明，帮助用户理解和使用示例项目

## 命名规范

### 文件和目录命名

- 使用小写字母和连字符（例如：`editor-basics`）
- 避免使用空格和特殊字符
- 使用有意义的名称，反映内容或功能

### 代码命名

- 变量和函数：使用驼峰命名法（例如：`createScene`）
- 类：使用帕斯卡命名法（例如：`SceneManager`）
- 常量：使用大写字母和下划线（例如：`MAX_OBJECTS`）

## 最佳实践

### 代码组织

- 将代码分解为模块化的函数和类
- 使用ES6模块系统组织代码
- 避免全局变量和函数
- 使用适当的设计模式

### 性能优化

- 优化资源加载和使用
- 使用适当的LOD（细节层次）
- 优化渲染和物理计算
- 避免不必要的计算和内存使用

### 用户体验

- 提供清晰的用户界面
- 添加适当的交互反馈
- 提供详细的说明和提示
- 确保示例项目在不同设备和浏览器上正常工作

### 可扩展性

- 设计可扩展的代码结构
- 提供扩展点和示例
- 使用配置而非硬编码
- 提供API文档和使用示例

## 贡献指南

如果您想贡献新的示例项目，请遵循以下步骤：

1. 创建符合上述结构和规范的示例项目
2. 编写详细的文档和注释
3. 测试项目在不同环境下的表现
4. 提交项目到DL（Digital Learning）引擎示例项目仓库
5. 等待审核和合并

有关贡献示例项目的详细指南，请参阅[贡献指南](../CONTRIBUTING.md)。
