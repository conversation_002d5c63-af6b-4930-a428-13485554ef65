{"title": "性能优化", "analyze": "分析", "analyzePerformance": "分析场景性能", "autoOptimize": "自动优化", "applyAutoOptimization": "应用自动优化", "dynamicLOD": {"tabTitle": "动态LOD", "title": "动态LOD系统", "currentStatus": "当前状态", "settings": "基本设置", "advancedSettings": "高级设置", "currentFPS": "当前帧率", "targetFPS": "目标帧率", "qualityLevel": "质量级别", "enableDynamicLOD": "启用动态LOD", "enableDynamicLODTip": "根据性能和视距动态调整LOD级别", "adjustmentSensitivity": "调整灵敏度", "adjustmentSensitivityTip": "调整质量级别的灵敏度，值越大调整越快", "adjustmentInterval": "调整间隔", "useSmoothTransition": "使用平滑过渡", "transitionTime": "过渡时间", "useAutoQualityAdjustment": "使用自动质量调整", "useDistanceOffset": "使用距离偏移", "useDistanceOffsetTip": "根据质量级别调整LOD切换距离", "distanceOffsetFactor": "距离偏移系数", "fpsGood": "良好", "fpsWarning": "警告", "fpsCritical": "严重", "qualityHigh": "高", "qualityMedium": "中", "qualityLow": "低", "qualityVeryLow": "极低", "presetLow": "低质量", "presetMedium": "中等质量", "presetHigh": "高质量", "presetUltra": "超高质量"}, "occlusionCulling": {"tabTitle": "遮挡剔除", "title": "遮挡剔除系统", "currentStatus": "当前状态", "settings": "设置", "culledObjects": "已剔除对象", "cullingRate": "剔除率", "cullingTime": "剔除时间", "currentAlgorithm": "当前算法", "algorithm": "算法", "algorithmHZB": "层次Z缓冲区", "algorithmOQ": "遮挡查询", "algorithmSR": "软件光栅化", "algorithmHOQ": "硬件遮挡查询", "algorithmUnknown": "未知算法", "adaptive": "自适应", "useAdaptiveAlgorithm": "使用自适应算法", "useAdaptiveAlgorithmTip": "根据性能自动选择最佳算法", "useMultiLevelCulling": "使用多级剔除", "useMultiLevelCullingTip": "先进行粗略剔除，再进行精细剔除", "usePredictiveCulling": "使用预测剔除", "usePredictiveCullingTip": "预测相机移动并提前剔除", "useTemporalCoherence": "使用时间一致性", "useTemporalCoherenceTip": "利用前一帧的剔除结果加速当前帧的剔除", "useGPUAcceleration": "使用GPU加速", "useGPUAccelerationTip": "使用GPU加速遮挡剔除", "useConservativeCulling": "使用保守剔除", "useConservativeCullingTip": "使用保守的剔除策略，减少错误剔除", "collectStats": "收集统计信息", "useAutoOptimization": "使用自动优化", "useAutoOptimizationTip": "根据性能自动优化剔除设置", "useDebugVisualization": "使用调试可视化"}, "instancedRendering": {"tabTitle": "实例化渲染", "title": "实例化渲染系统", "statistics": "统计信息", "instanceGroups": "实例组", "settings": "设置", "totalInstances": "总实例数", "memoryUsage": "内存使用量", "name": "名称", "instanceCount": "实例数量", "geometryType": "几何体类型", "materialType": "材质类型", "visible": "可见", "maxBatchSize": "最大批处理大小", "maxBatchSizeTip": "每个批处理的最大实例数量", "instanceThreshold": "实例阈值", "instanceThresholdTip": "自动实例化的最小实例数量", "useGPUInstanceUpdate": "使用GPU实例更新", "useGPUInstanceUpdateTip": "使用GPU更新实例数据", "useInstanceCulling": "使用实例剔除", "useInstanceCullingTip": "剔除不可见的实例", "useInstanceLOD": "使用实例LOD", "useInstanceLODTip": "为实例提供LOD支持", "useInstanceShadow": "使用实例阴影", "useInstanceShadowTip": "为实例提供阴影支持", "supportCustomShaders": "支持自定义着色器", "supportCustomShadersTip": "为实例提供自定义着色器支持", "supportMaterialVariants": "支持材质变体", "supportMaterialVariantsTip": "为实例提供材质变体支持", "supportInstanceAnimation": "支持实例动画", "supportInstanceAnimationTip": "为实例提供动画支持", "useAutoInstancing": "使用自动实例化", "useAutoInstancingTip": "自动检测和实例化相似对象", "scanScene": "扫描场景", "optimizeInstances": "优化实例"}, "chunkedSceneLoading": {"tabTitle": "场景分块加载", "title": "场景分块加载系统", "statistics": "统计信息", "chunks": "场景块", "settings": "设置", "loadedChunks": "已加载块", "loadPercentage": "加载百分比", "memoryUsage": "内存使用量", "name": "名称", "level": "级别", "resources": "资源数", "status": "状态", "progress": "进度", "loaded": "已加载", "loading": "加载中", "unloaded": "未加载", "refresh": "刷新", "createChunk": "创建块", "useOctree": "使用八叉树", "useOctreeTip": "使用八叉树加速空间查询", "useLOD": "使用LOD", "useLODTip": "为块提供LOD支持", "usePredictiveLoading": "使用预测加载", "usePredictiveLoadingTip": "预测相机移动并提前加载", "useProgressiveLoading": "使用渐进式加载", "useProgressiveLoadingTip": "先加载低细节版本，再加载高细节版本", "useMemoryManagement": "使用内存管理", "useMemoryManagementTip": "自动管理内存使用", "useDebugVisualization": "使用调试可视化", "useDebugVisualizationTip": "可视化显示块的状态", "chunkSize": "块大小", "maxChunkLevel": "最大块级别", "loadDistance": "加载距离", "unloadDistance": "卸载距离", "preloadDistance": "预加载距离", "maxMemoryUsage": "最大内存使用量", "maxConcurrentLoads": "最大并发加载数"}}