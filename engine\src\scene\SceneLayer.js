"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SceneLayer = exports.SceneLayerType = void 0;
var EventEmitter_1 = require("../utils/EventEmitter");
var THREE = require("three");
/**
 * 场景图层类型
 */
var SceneLayerType;
(function (SceneLayerType) {
    /** 普通图层 */
    SceneLayerType["NORMAL"] = "normal";
    /** 图层组 */
    SceneLayerType["GROUP"] = "group";
})(SceneLayerType || (exports.SceneLayerType = SceneLayerType = {}));
/**
 * 场景图层
 */
var SceneLayer = /** @class */ (function (_super) {
    __extends(SceneLayer, _super);
    /**
     * 创建场景图层
     * @param scene 所属场景
     * @param options 图层选项
     */
    function SceneLayer(scene, options) {
        var _this = _super.call(this) || this;
        /** 图层中的实体 */
        _this.entities = new Set();
        /** 图层标签 */
        _this.tags = new Set();
        /** 父图层ID */
        _this.parentId = null;
        /** 子图层ID列表 */
        _this.childrenIds = new Set();
        /** 自定义数据 */
        _this.userData = {};
        /** 是否已初始化 */
        _this.initialized = false;
        _this.scene = scene;
        _this.id = options.id;
        _this.name = options.name;
        _this.type = options.type !== undefined ? options.type : SceneLayerType.NORMAL;
        _this.visible = options.visible !== undefined ? options.visible : true;
        _this.locked = options.locked !== undefined ? options.locked : false;
        _this.excludeFromRender = options.excludeFromRender !== undefined ? options.excludeFromRender : false;
        _this.excludeFromPhysics = options.excludeFromPhysics !== undefined ? options.excludeFromPhysics : false;
        _this.excludeFromRaycast = options.excludeFromRaycast !== undefined ? options.excludeFromRaycast : false;
        _this.order = options.order !== undefined ? options.order : 0;
        _this.expanded = options.expanded !== undefined ? options.expanded : true;
        // 设置父图层ID
        if (options.parentId) {
            _this.parentId = options.parentId;
        }
        // 添加标签
        if (options.tags) {
            for (var _i = 0, _a = options.tags; _i < _a.length; _i++) {
                var tag = _a[_i];
                _this.tags.add(tag);
            }
        }
        _this.color = options.color || new THREE.Color(0xffffff);
        // 设置自定义数据
        if (options.userData) {
            _this.userData = __assign({}, options.userData);
        }
        return _this;
    }
    /**
     * 初始化图层
     */
    SceneLayer.prototype.initialize = function () {
        if (this.initialized) {
            return;
        }
        this.initialized = true;
        this.emit('initialized');
    };
    /**
     * 添加实体
     * @param entity 实体
     * @returns 是否成功添加
     */
    SceneLayer.prototype.addEntity = function (entity) {
        if (this.entities.has(entity)) {
            return false;
        }
        this.entities.add(entity);
        // 设置实体的可见性
        if (!this.visible) {
            entity.setActive(false);
        }
        // 发出实体添加事件
        this.emit('entityAdded', entity);
        return true;
    };
    /**
     * 移除实体
     * @param entity 实体
     * @returns 是否成功移除
     */
    SceneLayer.prototype.removeEntity = function (entity) {
        if (!this.entities.has(entity)) {
            return false;
        }
        this.entities.delete(entity);
        // 发出实体移除事件
        this.emit('entityRemoved', entity);
        return true;
    };
    /**
     * 获取图层中的所有实体
     * @returns 实体数组
     */
    SceneLayer.prototype.getEntities = function () {
        return Array.from(this.entities);
    };
    /**
     * 获取图层中的实体数量
     * @returns 实体数量
     */
    SceneLayer.prototype.getEntityCount = function () {
        return this.entities.size;
    };
    /**
     * 清空图层
     */
    SceneLayer.prototype.clear = function () {
        // 清空前发出事件
        this.emit('beforeClear');
        // 清空实体
        this.entities.clear();
        // 发出清空事件
        this.emit('cleared');
    };
    /**
     * 设置图层可见性
     * @param visible 是否可见
     */
    SceneLayer.prototype.setVisible = function (visible) {
        if (this.visible === visible) {
            return;
        }
        this.visible = visible;
        // 更新所有实体的可见性
        for (var _i = 0, _a = Array.from(this.entities); _i < _a.length; _i++) {
            var entity = _a[_i];
            entity.setActive(visible);
        }
        // 发出可见性变化事件
        this.emit('visibilityChanged', visible);
    };
    /**
     * 是否可见
     * @returns 是否可见
     */
    SceneLayer.prototype.isVisible = function () {
        return this.visible;
    };
    /**
     * 设置图层锁定状态
     * @param locked 是否锁定
     */
    SceneLayer.prototype.setLocked = function (locked) {
        if (this.locked === locked) {
            return;
        }
        this.locked = locked;
        // 发出锁定状态变化事件
        this.emit('lockChanged', locked);
    };
    /**
     * 是否锁定
     * @returns 是否锁定
     */
    SceneLayer.prototype.isLocked = function () {
        return this.locked;
    };
    /**
     * 设置图层顺序
     * @param order 图层顺序
     */
    SceneLayer.prototype.setOrder = function (order) {
        if (this.order === order) {
            return;
        }
        this.order = order;
        // 发出顺序变化事件
        this.emit('orderChanged', order);
    };
    /**
     * 获取图层顺序
     * @returns 图层顺序
     */
    SceneLayer.prototype.getOrder = function () {
        return this.order;
    };
    /**
     * 添加标签
     * @param tag 标签
     * @returns 是否成功添加
     */
    SceneLayer.prototype.addTag = function (tag) {
        if (this.tags.has(tag)) {
            return false;
        }
        this.tags.add(tag);
        // 发出标签添加事件
        this.emit('tagAdded', tag);
        return true;
    };
    /**
     * 移除标签
     * @param tag 标签
     * @returns 是否成功移除
     */
    SceneLayer.prototype.removeTag = function (tag) {
        if (!this.tags.has(tag)) {
            return false;
        }
        this.tags.delete(tag);
        // 发出标签移除事件
        this.emit('tagRemoved', tag);
        return true;
    };
    /**
     * 是否有标签
     * @param tag 标签
     * @returns 是否有标签
     */
    SceneLayer.prototype.hasTag = function (tag) {
        return this.tags.has(tag);
    };
    /**
     * 获取所有标签
     * @returns 标签数组
     */
    SceneLayer.prototype.getTags = function () {
        return Array.from(this.tags);
    };
    /**
     * 设置图层颜色
     * @param color 颜色
     */
    SceneLayer.prototype.setColor = function (color) {
        this.color = color;
        // 发出颜色变化事件
        this.emit('colorChanged', color);
    };
    /**
     * 获取图层颜色
     * @returns 颜色
     */
    SceneLayer.prototype.getColor = function () {
        return this.color;
    };
    /**
     * 设置自定义数据
     * @param key 键
     * @param value 值
     */
    SceneLayer.prototype.setUserData = function (key, value) {
        this.userData[key] = value;
        // 发出自定义数据变化事件
        this.emit('userDataChanged', key, value);
    };
    /**
     * 获取自定义数据
     * @param key 键
     * @returns 值
     */
    SceneLayer.prototype.getUserData = function (key) {
        return this.userData[key];
    };
    /**
     * 获取图层类型
     * @returns 图层类型
     */
    SceneLayer.prototype.getType = function () {
        return this.type;
    };
    /**
     * 设置图层类型
     * @param type 图层类型
     */
    SceneLayer.prototype.setType = function (type) {
        if (this.type === type) {
            return;
        }
        this.type = type;
        // 发出类型变化事件
        this.emit('typeChanged', type);
    };
    /**
     * 是否为图层组
     * @returns 是否为图层组
     */
    SceneLayer.prototype.isGroup = function () {
        return this.type === SceneLayerType.GROUP;
    };
    /**
     * 获取父图层ID
     * @returns 父图层ID
     */
    SceneLayer.prototype.getParentId = function () {
        return this.parentId;
    };
    /**
     * 设置父图层ID
     * @param parentId 父图层ID
     */
    SceneLayer.prototype.setParentId = function (parentId) {
        if (this.parentId === parentId) {
            return;
        }
        var oldParentId = this.parentId;
        this.parentId = parentId;
        // 发出父图层变化事件
        this.emit('parentChanged', parentId, oldParentId);
    };
    /**
     * 添加子图层
     * @param childId 子图层ID
     * @returns 是否成功添加
     */
    SceneLayer.prototype.addChild = function (childId) {
        if (this.childrenIds.has(childId)) {
            return false;
        }
        this.childrenIds.add(childId);
        // 发出子图层添加事件
        this.emit('childAdded', childId);
        return true;
    };
    /**
     * 移除子图层
     * @param childId 子图层ID
     * @returns 是否成功移除
     */
    SceneLayer.prototype.removeChild = function (childId) {
        if (!this.childrenIds.has(childId)) {
            return false;
        }
        this.childrenIds.delete(childId);
        // 发出子图层移除事件
        this.emit('childRemoved', childId);
        return true;
    };
    /**
     * 获取所有子图层ID
     * @returns 子图层ID数组
     */
    SceneLayer.prototype.getChildrenIds = function () {
        return Array.from(this.childrenIds);
    };
    /**
     * 是否有子图层
     * @returns 是否有子图层
     */
    SceneLayer.prototype.hasChildren = function () {
        return this.childrenIds.size > 0;
    };
    /**
     * 获取子图层数量
     * @returns 子图层数量
     */
    SceneLayer.prototype.getChildCount = function () {
        return this.childrenIds.size;
    };
    /**
     * 设置展开状态
     * @param expanded 是否展开
     */
    SceneLayer.prototype.setExpanded = function (expanded) {
        if (this.expanded === expanded) {
            return;
        }
        this.expanded = expanded;
        // 发出展开状态变化事件
        this.emit('expandedChanged', expanded);
    };
    /**
     * 是否展开
     * @returns 是否展开
     */
    SceneLayer.prototype.isExpanded = function () {
        return this.expanded;
    };
    /**
     * 销毁图层
     */
    SceneLayer.prototype.dispose = function () {
        // 清空图层
        this.clear();
        // 移除所有事件监听器
        this.removeAllListeners();
        this.initialized = false;
    };
    return SceneLayer;
}(EventEmitter_1.EventEmitter));
exports.SceneLayer = SceneLayer;
