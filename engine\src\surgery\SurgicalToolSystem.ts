/**
 * 手术工具系统 - 管理手术工具和交互
 */
export class SurgicalToolSystem extends System {
  /** 可用工具 */
  private tools: Map<string, SurgicalTool> = new Map();
  /** 当前选中的工具 */
  private activeTool: SurgicalTool | null = null;
  /** 工具交互管理器 */
  private interactionManager: ToolInteractionManager;
  
  /**
   * 初始化手术工具系统
   */
  public initialize(): void {
    // 创建基本手术工具
    this.createScalpel();
    this.createForceps();
    this.createNeedleHolder();
    this.createSuture();
    this.createClamp();
    
    // 初始化交互管理器
    this.interactionManager = new ToolInteractionManager(this.world);
  }
  
  /**
   * 执行切割操作
   * @param targetEntity 目标实体
   * @param cutPath 切割路径
   */
  public performIncision(targetEntity: Entity, cutPath: Vector3[]): void {
    // 获取目标软体组件
    const softBody = targetEntity.getComponent(SoftBodyComponent);
    if (!softBody) return;
    
    // 创建切割平面序列
    const cutPlanes = this.createCutPlanesFromPath(cutPath);
    
    // 执行切割
    for (const plane of cutPlanes) {
      this.world.getSystem(SoftBodySystem).cutSoftBodyWithPlane(
        targetEntity.id, 
        plane
      );
    }
  }
}