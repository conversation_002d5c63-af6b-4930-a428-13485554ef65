/**
 * 陀螺仪控制组件样式
 */
@import '../../styles/responsive.less';

.gyroscope-control {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 900;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: @spacing-md;
  
  // 陀螺仪按钮组
  .gyroscope-buttons {
    display: flex;
    gap: @spacing-sm;
    background-color: rgba(0, 0, 0, 0.5);
    padding: @spacing-sm;
    border-radius: @touch-border-radius;
    
    .gyroscope-button {
      width: @touch-min-width;
      height: @touch-min-height;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .anticon {
        font-size: @touch-icon-size;
      }
      
      &:active {
        transform: scale(0.95);
      }
    }
  }
  
  // 陀螺仪设置面板
  .gyroscope-settings {
    width: 280px;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    
    .ant-card-head {
      min-height: auto;
      padding: @spacing-sm @spacing-md;
      
      .ant-card-head-title {
        padding: @spacing-xs 0;
      }
    }
    
    .ant-card-body {
      padding: @spacing-md;
    }
    
    .setting-item {
      margin-bottom: @spacing-md;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .setting-label {
        margin-bottom: @spacing-xs;
        font-weight: 500;
      }
    }
    
    .gyroscope-unavailable {
      margin-top: @spacing-md;
      padding: @spacing-md;
      background-color: #fff2f0;
      border: 1px solid #ffccc7;
      border-radius: @spacing-xs;
      display: flex;
      flex-direction: column;
      gap: @spacing-md;
      align-items: center;
      text-align: center;
    }
  }
  
  // 陀螺仪指示器
  .gyroscope-indicator {
    background-color: rgba(0, 0, 0, 0.5);
    padding: @spacing-sm;
    border-radius: @touch-border-radius;
    color: #fff;
    
    .gyroscope-values {
      display: flex;
      gap: @spacing-md;
      font-family: monospace;
      font-size: @font-size-sm;
    }
  }
  
  // 响应式调整
  @media (max-width: @screen-md) {
    top: 70px;
    
    .gyroscope-settings {
      width: 250px;
    }
  }
  
  @media (max-width: @screen-sm) {
    top: 70px;
    right: 8px;
    
    .gyroscope-buttons {
      padding: @spacing-xs;
      
      .gyroscope-button {
        width: 40px;
        height: 40px;
        
        .anticon {
          font-size: 20px;
        }
      }
    }
    
    .gyroscope-settings {
      width: 220px;
      
      .ant-card-head {
        padding: @spacing-xs @spacing-sm;
      }
      
      .ant-card-body {
        padding: @spacing-sm;
      }
    }
    
    .gyroscope-indicator {
      padding: @spacing-xs;
      
      .gyroscope-values {
        gap: @spacing-sm;
        font-size: 10px;
      }
    }
  }
  
  // 暗色主题
  .dark-theme & {
    .gyroscope-settings {
      background-color: rgba(0, 0, 0, 0.8);
      color: #fff;
      
      .ant-card-head {
        background-color: rgba(0, 0, 0, 0.5);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        
        .ant-card-head-title {
          color: #fff;
        }
      }
      
      .setting-item {
        .setting-label {
          color: #fff;
        }
      }
      
      .ant-slider-rail {
        background-color: rgba(255, 255, 255, 0.2);
      }
      
      .gyroscope-unavailable {
        background-color: rgba(255, 77, 79, 0.2);
        border: 1px solid rgba(255, 77, 79, 0.5);
        color: #ff7875;
      }
    }
  }
}
