/**
 * 模型量化工具
 * 用于减小模型大小并提高推理速度
 */

/**
 * 量化位数
 */
export type QuantizationBits = 8 | 16 | 32;

/**
 * 量化配置
 */
export interface QuantizationConfig {
  /** 是否启用量化 */
  enabled: boolean;
  /** 量化位数 */
  bits: QuantizationBits;
  /** 是否使用对称量化 */
  symmetric?: boolean;
  /** 是否使用通道量化 */
  perChannel?: boolean;
  /** 是否使用动态量化 */
  dynamic?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 量化统计
 */
export interface QuantizationStats {
  /** 原始模型大小（字节） */
  originalSize: number;
  /** 量化后模型大小（字节） */
  quantizedSize: number;
  /** 压缩比 */
  compressionRatio: number;
  /** 量化时间（毫秒） */
  quantizationTime: number;
  /** 精度损失 */
  precisionLoss?: number;
}

/**
 * 模型量化工具
 */
export class ModelQuantization {
  /** 配置 */
  private config: QuantizationConfig;
  
  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: QuantizationConfig = {
    enabled: false,
    bits: 8,
    symmetric: true,
    perChannel: true,
    dynamic: false,
    debug: false
  };
  
  /** 量化统计 */
  private stats: QuantizationStats | null = null;
  
  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: Partial<QuantizationConfig> = {}) {
    this.config = {
      ...ModelQuantization.DEFAULT_CONFIG,
      ...config
    };
  }
  
  /**
   * 量化模型
   * @param model 模型数据
   * @returns 量化后的模型数据
   */
  public quantize(model: ArrayBuffer): ArrayBuffer {
    // 如果未启用量化，直接返回原始模型
    if (!this.config.enabled) {
      return model;
    }
    
    const startTime = Date.now();
    
    try {
      // 根据量化位数选择不同的量化方法
      let quantizedModel: ArrayBuffer;
      
      switch (this.config.bits) {
        case 8:
          quantizedModel = this.quantize8Bit(model);
          break;
        case 16:
          quantizedModel = this.quantize16Bit(model);
          break;
        case 32:
        default:
          // 32位不进行量化，直接返回原始模型
          quantizedModel = model;
          break;
      }
      
      // 计算量化统计
      const endTime = Date.now();
      this.stats = {
        originalSize: model.byteLength,
        quantizedSize: quantizedModel.byteLength,
        compressionRatio: model.byteLength / quantizedModel.byteLength,
        quantizationTime: endTime - startTime
      };
      
      // 如果启用调试，输出统计信息
      if (this.config.debug) {
        console.log('[ModelQuantization] 量化统计:', this.stats);
      }
      
      return quantizedModel;
    } catch (error) {
      console.error('[ModelQuantization] 量化失败:', error);
      
      // 量化失败，返回原始模型
      return model;
    }
  }
  
  /**
   * 8位量化
   * @param model 模型数据
   * @returns 量化后的模型数据
   */
  private quantize8Bit(model: ArrayBuffer): ArrayBuffer {
    // 这里是8位量化的实现
    // 实际实现需要根据具体需求和环境
    
    // 模拟量化过程
    const float32Array = new Float32Array(model);
    const int8Array = new Int8Array(float32Array.length);
    
    // 找出最大值和最小值
    let max = -Infinity;
    let min = Infinity;
    
    for (let i = 0; i < float32Array.length; i++) {
      const value = float32Array[i];
      max = Math.max(max, value);
      min = Math.min(min, value);
    }
    
    // 计算缩放因子
    const scale = this.config.symmetric
      ? Math.max(Math.abs(min), Math.abs(max)) / 127
      : (max - min) / 255;
    
    const zeroPoint = this.config.symmetric
      ? 0
      : Math.round((0 - min) / scale);
    
    // 量化
    for (let i = 0; i < float32Array.length; i++) {
      const value = float32Array[i];
      int8Array[i] = Math.round(value / scale) + zeroPoint;
    }
    
    // 创建量化后的模型数据
    const quantizedModel = new ArrayBuffer(int8Array.byteLength + 8);
    const quantizedView = new DataView(quantizedModel);
    
    // 存储缩放因子和零点
    quantizedView.setFloat32(0, scale, true);
    quantizedView.setInt32(4, zeroPoint, true);
    
    // 存储量化后的数据
    const quantizedArray = new Int8Array(quantizedModel, 8);
    quantizedArray.set(int8Array);
    
    return quantizedModel;
  }
  
  /**
   * 16位量化
   * @param model 模型数据
   * @returns 量化后的模型数据
   */
  private quantize16Bit(model: ArrayBuffer): ArrayBuffer {
    // 这里是16位量化的实现
    // 实际实现需要根据具体需求和环境
    
    // 模拟量化过程
    const float32Array = new Float32Array(model);
    const int16Array = new Int16Array(float32Array.length);
    
    // 找出最大值和最小值
    let max = -Infinity;
    let min = Infinity;
    
    for (let i = 0; i < float32Array.length; i++) {
      const value = float32Array[i];
      max = Math.max(max, value);
      min = Math.min(min, value);
    }
    
    // 计算缩放因子
    const scale = this.config.symmetric
      ? Math.max(Math.abs(min), Math.abs(max)) / 32767
      : (max - min) / 65535;
    
    const zeroPoint = this.config.symmetric
      ? 0
      : Math.round((0 - min) / scale);
    
    // 量化
    for (let i = 0; i < float32Array.length; i++) {
      const value = float32Array[i];
      int16Array[i] = Math.round(value / scale) + zeroPoint;
    }
    
    // 创建量化后的模型数据
    const quantizedModel = new ArrayBuffer(int16Array.byteLength + 8);
    const quantizedView = new DataView(quantizedModel);
    
    // 存储缩放因子和零点
    quantizedView.setFloat32(0, scale, true);
    quantizedView.setInt32(4, zeroPoint, true);
    
    // 存储量化后的数据
    const quantizedArray = new Int16Array(quantizedModel, 8);
    quantizedArray.set(int16Array);
    
    return quantizedModel;
  }
  
  /**
   * 反量化模型
   * @param quantizedModel 量化后的模型数据
   * @returns 反量化后的模型数据
   */
  public dequantize(quantizedModel: ArrayBuffer): ArrayBuffer {
    // 如果未启用量化，直接返回原始模型
    if (!this.config.enabled) {
      return quantizedModel;
    }
    
    try {
      // 读取缩放因子和零点
      const quantizedView = new DataView(quantizedModel);
      const scale = quantizedView.getFloat32(0, true);
      const zeroPoint = quantizedView.getInt32(4, true);
      
      // 根据量化位数选择不同的反量化方法
      switch (this.config.bits) {
        case 8:
          return this.dequantize8Bit(quantizedModel, scale, zeroPoint);
        case 16:
          return this.dequantize16Bit(quantizedModel, scale, zeroPoint);
        case 32:
        default:
          // 32位不进行反量化，直接返回原始模型
          return quantizedModel;
      }
    } catch (error) {
      console.error('[ModelQuantization] 反量化失败:', error);
      
      // 反量化失败，返回原始模型
      return quantizedModel;
    }
  }
  
  /**
   * 8位反量化
   * @param quantizedModel 量化后的模型数据
   * @param scale 缩放因子
   * @param zeroPoint 零点
   * @returns 反量化后的模型数据
   */
  private dequantize8Bit(quantizedModel: ArrayBuffer, scale: number, zeroPoint: number): ArrayBuffer {
    // 读取量化后的数据
    const quantizedArray = new Int8Array(quantizedModel, 8);
    
    // 创建反量化后的模型数据
    const dequantizedModel = new ArrayBuffer(quantizedArray.length * 4);
    const float32Array = new Float32Array(dequantizedModel);
    
    // 反量化
    for (let i = 0; i < quantizedArray.length; i++) {
      float32Array[i] = (quantizedArray[i] - zeroPoint) * scale;
    }
    
    return dequantizedModel;
  }
  
  /**
   * 16位反量化
   * @param quantizedModel 量化后的模型数据
   * @param scale 缩放因子
   * @param zeroPoint 零点
   * @returns 反量化后的模型数据
   */
  private dequantize16Bit(quantizedModel: ArrayBuffer, scale: number, zeroPoint: number): ArrayBuffer {
    // 读取量化后的数据
    const quantizedArray = new Int16Array(quantizedModel, 8);
    
    // 创建反量化后的模型数据
    const dequantizedModel = new ArrayBuffer(quantizedArray.length * 4);
    const float32Array = new Float32Array(dequantizedModel);
    
    // 反量化
    for (let i = 0; i < quantizedArray.length; i++) {
      float32Array[i] = (quantizedArray[i] - zeroPoint) * scale;
    }
    
    return dequantizedModel;
  }
  
  /**
   * 获取量化统计
   * @returns 量化统计
   */
  public getStats(): QuantizationStats | null {
    return this.stats;
  }
  
  /**
   * 获取配置
   * @returns 配置
   */
  public getConfig(): QuantizationConfig {
    return { ...this.config };
  }
  
  /**
   * 设置配置
   * @param config 配置
   */
  public setConfig(config: Partial<QuantizationConfig>): void {
    this.config = {
      ...this.config,
      ...config
    };
  }
}
