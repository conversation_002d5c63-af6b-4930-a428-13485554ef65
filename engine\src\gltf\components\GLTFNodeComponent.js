"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GLTFNodeComponent = void 0;
/**
 * GLTF节点组件
 * 用于存储GLTF节点数据
 */
var THREE = require("three");
var Component_1 = require("../../core/Component");
/**
 * GLTF节点组件
 */
var GLTFNodeComponent = exports.GLTFNodeComponent = /** @class */ (function (_super) {
    __extends(GLTFNodeComponent, _super);
    /**
     * 创建GLTF节点组件
     * @param object Three.js对象
     */
    function GLTFNodeComponent(object) {
        var _this = _super.call(this, GLTFNodeComponent.type) || this;
        /** 节点索引 */
        _this.nodeIndex = -1;
        /** 节点名称 */
        _this.nodeName = '';
        /** 节点类型 */
        _this.nodeType = '';
        /** 节点额外数据 */
        _this.userData = {};
        _this.object = object;
        _this.nodeName = object.name || '';
        _this.nodeType = _this.determineNodeType(object);
        _this.userData = __assign({}, object.userData);
        // 尝试从userData中获取节点索引
        if (object.userData && typeof object.userData.nodeIndex === 'number') {
            _this.nodeIndex = object.userData.nodeIndex;
        }
        return _this;
    }
    /**
     * 确定节点类型
     * @param object Three.js对象
     * @returns 节点类型
     */
    GLTFNodeComponent.prototype.determineNodeType = function (object) {
        if (object instanceof THREE.Mesh) {
            return 'Mesh';
        }
        else if (object instanceof THREE.Camera) {
            return 'Camera';
        }
        else if (object instanceof THREE.Light) {
            return 'Light';
        }
        else if (object instanceof THREE.Bone) {
            return 'Bone';
        }
        else if (object instanceof THREE.SkinnedMesh) {
            return 'SkinnedMesh';
        }
        else if (object instanceof THREE.Group) {
            return 'Group';
        }
        else {
            return 'Object3D';
        }
    };
    /**
     * 获取Three.js对象
     * @returns Three.js对象
     */
    GLTFNodeComponent.prototype.getObject = function () {
        return this.object;
    };
    /**
     * 设置Three.js对象
     * @param object Three.js对象
     */
    GLTFNodeComponent.prototype.setObject = function (object) {
        this.object = object;
        this.nodeName = object.name || '';
        this.nodeType = this.determineNodeType(object);
        this.userData = __assign({}, object.userData);
        // 尝试从userData中获取节点索引
        if (object.userData && typeof object.userData.nodeIndex === 'number') {
            this.nodeIndex = object.userData.nodeIndex;
        }
    };
    /**
     * 获取节点索引
     * @returns 节点索引
     */
    GLTFNodeComponent.prototype.getNodeIndex = function () {
        return this.nodeIndex;
    };
    /**
     * 设置节点索引
     * @param index 节点索引
     */
    GLTFNodeComponent.prototype.setNodeIndex = function (index) {
        this.nodeIndex = index;
        // 同时更新对象的userData
        if (this.object) {
            this.object.userData.nodeIndex = index;
        }
    };
    /**
     * 获取节点名称
     * @returns 节点名称
     */
    GLTFNodeComponent.prototype.getNodeName = function () {
        return this.nodeName;
    };
    /**
     * 设置节点名称
     * @param name 节点名称
     */
    GLTFNodeComponent.prototype.setNodeName = function (name) {
        this.nodeName = name;
        // 同时更新对象的名称
        if (this.object) {
            this.object.name = name;
        }
    };
    /**
     * 获取节点类型
     * @returns 节点类型
     */
    GLTFNodeComponent.prototype.getNodeType = function () {
        return this.nodeType;
    };
    /**
     * 获取节点额外数据
     * @returns 节点额外数据
     */
    GLTFNodeComponent.prototype.getUserData = function () {
        return __assign({}, this.userData);
    };
    /**
     * 设置节点额外数据
     * @param userData 节点额外数据
     */
    GLTFNodeComponent.prototype.setUserData = function (userData) {
        this.userData = __assign({}, userData);
        // 同时更新对象的userData
        if (this.object) {
            this.object.userData = __assign(__assign({}, this.object.userData), userData);
        }
    };
    /**
     * 克隆组件
     * @returns 克隆的组件
     */
    GLTFNodeComponent.prototype.clone = function () {
        var component = new GLTFNodeComponent(this.object.clone());
        component.nodeIndex = this.nodeIndex;
        component.nodeName = this.nodeName;
        component.nodeType = this.nodeType;
        component.userData = __assign({}, this.userData);
        return component;
    };
    /** 组件类型 */
    GLTFNodeComponent.type = 'GLTFNodeComponent';
    return GLTFNodeComponent;
}(Component_1.Component));
