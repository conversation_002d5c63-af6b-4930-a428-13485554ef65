/**
 * 响应式面板样式
 */
@import '../../styles/responsive.less';

.responsive-panel {
  position: relative;
  transition: all 0.3s ease;
  
  // 面板卡片
  .panel-card {
    width: 100%;
    height: 100%;
    overflow: hidden;
    
    .ant-card-head {
      padding: 0;
      min-height: auto;
      
      .ant-card-head-wrapper {
        padding: 0;
      }
      
      .ant-card-head-title {
        padding: 0;
      }
    }
    
    .ant-card-body {
      overflow: auto;
      transition: all 0.3s ease;
    }
  }
  
  // 面板头部
  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: @spacing-md;
    cursor: move;
    
    .panel-title {
      font-weight: 500;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .panel-actions {
      display: flex;
      align-items: center;
      gap: @spacing-xs;
    }
  }
  
  // 面板内容
  .panel-content {
    height: 100%;
  }
  
  // 大小变体
  &.size-small {
    width: 250px;
    
    @media (max-width: @screen-md) {
      width: 200px;
    }
    
    @media (max-width: @screen-sm) {
      width: 100%;
    }
  }
  
  &.size-medium {
    width: 350px;
    
    @media (max-width: @screen-md) {
      width: 280px;
    }
    
    @media (max-width: @screen-sm) {
      width: 100%;
    }
  }
  
  &.size-large {
    width: 450px;
    
    @media (max-width: @screen-md) {
      width: 350px;
    }
    
    @media (max-width: @screen-sm) {
      width: 100%;
    }
  }
  
  &.size-full {
    width: 100%;
    height: 100%;
  }
  
  // 位置变体
  &.position-left {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 100;
    
    .panel-card {
      height: 100%;
    }
  }
  
  &.position-right {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 100;
    
    .panel-card {
      height: 100%;
    }
  }
  
  &.position-top {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    z-index: 100;
    height: 300px;
    
    @media (max-width: @screen-md) {
      height: 250px;
    }
    
    @media (max-width: @screen-sm) {
      height: 200px;
    }
  }
  
  &.position-bottom {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
    height: 300px;
    
    @media (max-width: @screen-md) {
      height: 250px;
    }
    
    @media (max-width: @screen-sm) {
      height: 200px;
    }
  }
  
  &.position-center {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 100;
    
    .panel-card {
      height: 100%;
    }
  }
  
  &.position-float {
    position: absolute;
    z-index: 200;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    
    &.dragging {
      opacity: 0.8;
      pointer-events: none;
    }
  }
  
  // 折叠状态
  &.collapsed {
    .panel-card {
      .ant-card-body {
        padding: 0;
        height: 0;
        overflow: hidden;
      }
    }
    
    &.position-left {
      width: auto;
    }
    
    &.position-right {
      width: auto;
    }
    
    &.position-top {
      height: auto;
    }
    
    &.position-bottom {
      height: auto;
    }
  }
  
  // 设备类型适配
  &.device-mobile {
    .panel-header {
      padding: @touch-padding;
      
      .panel-title {
        font-size: @font-size-md;
      }
    }
    
    .panel-content {
      padding: @touch-padding;
    }
    
    .ant-btn {
      min-height: @touch-min-height;
      min-width: @touch-min-width;
    }
  }
  
  &.device-tablet {
    .panel-header {
      padding: @spacing-md;
    }
  }
  
  // 屏幕方向适配
  &.orientation-portrait {
    &.position-left,
    &.position-right {
      width: 100%;
      height: auto;
      max-height: 50%;
    }
    
    &.position-top,
    &.position-bottom {
      height: 50%;
    }
  }
  
  // 断点适配
  &.breakpoint-xs,
  &.breakpoint-sm {
    &.position-left,
    &.position-right {
      width: 100%;
    }
    
    &.position-top,
    &.position-bottom {
      height: 200px;
    }
  }
  
  // 触控优化
  .touch-friendly-controls {
    .ant-btn {
      .touch-friendly-button();
    }
    
    .ant-input {
      .touch-friendly-input();
    }
    
    .ant-select {
      .touch-friendly-select();
    }
    
    .ant-checkbox-wrapper,
    .ant-radio-wrapper {
      .touch-friendly-checkbox-radio();
    }
  }
}
