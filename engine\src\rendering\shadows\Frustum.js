"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Frustum = void 0;
/**
 * 视锥体类
 * 用于级联阴影映射的视锥体分割
 */
var THREE = require("three");
/**
 * 视锥体类
 */
var Frustum = /** @class */ (function () {
    /**
     * 创建视锥体
     * @param params 视锥体参数
     */
    function Frustum(params) {
        if (params === void 0) { params = {}; }
        /**
         * 视锥体顶点
         */
        this.vertices = {
            near: [
                new THREE.Vector3(),
                new THREE.Vector3(),
                new THREE.Vector3(),
                new THREE.Vector3()
            ],
            far: [
                new THREE.Vector3(),
                new THREE.Vector3(),
                new THREE.Vector3(),
                new THREE.Vector3()
            ]
        };
        if (params.projectionMatrix !== undefined) {
            this.setFromProjectionMatrix(params.projectionMatrix, params.maxFar || 10000);
        }
    }
    /**
     * 从投影矩阵设置视锥体
     * @param projectionMatrix 投影矩阵
     * @param maxFar 最大远平面距离
     */
    Frustum.prototype.setFromProjectionMatrix = function (projectionMatrix, maxFar) {
        var isOrthographic = projectionMatrix.elements[2 * 4 + 3] === 0;
        if (isOrthographic) {
            this.setFromOrthographicProjectionMatrix(projectionMatrix, maxFar);
        }
        else {
            this.setFromPerspectiveProjectionMatrix(projectionMatrix, maxFar);
        }
    };
    /**
     * 从透视投影矩阵设置视锥体
     * @param projectionMatrix 投影矩阵
     * @param maxFar 最大远平面距离
     */
    Frustum.prototype.setFromPerspectiveProjectionMatrix = function (projectionMatrix, maxFar) {
        var near = projectionMatrix.elements[14] / (projectionMatrix.elements[10] - 1);
        var far = Math.min(maxFar, projectionMatrix.elements[14] / (projectionMatrix.elements[10] + 1));
        var left = near * (projectionMatrix.elements[8] - 1) / projectionMatrix.elements[0];
        var right = near * (projectionMatrix.elements[8] + 1) / projectionMatrix.elements[0];
        var top = near * (projectionMatrix.elements[9] + 1) / projectionMatrix.elements[5];
        var bottom = near * (projectionMatrix.elements[9] - 1) / projectionMatrix.elements[5];
        // 近平面顶点
        this.vertices.near[0].set(left, top, -near);
        this.vertices.near[1].set(right, top, -near);
        this.vertices.near[2].set(right, bottom, -near);
        this.vertices.near[3].set(left, bottom, -near);
        // 远平面顶点
        var farLeft = far * left / near;
        var farRight = far * right / near;
        var farTop = far * top / near;
        var farBottom = far * bottom / near;
        this.vertices.far[0].set(farLeft, farTop, -far);
        this.vertices.far[1].set(farRight, farTop, -far);
        this.vertices.far[2].set(farRight, farBottom, -far);
        this.vertices.far[3].set(farLeft, farBottom, -far);
    };
    /**
     * 从正交投影矩阵设置视锥体
     * @param projectionMatrix 投影矩阵
     * @param maxFar 最大远平面距离
     */
    Frustum.prototype.setFromOrthographicProjectionMatrix = function (projectionMatrix, maxFar) {
        var left = (projectionMatrix.elements[12] - 1) / projectionMatrix.elements[0];
        var right = (projectionMatrix.elements[12] + 1) / projectionMatrix.elements[0];
        var top = (projectionMatrix.elements[13] + 1) / projectionMatrix.elements[5];
        var bottom = (projectionMatrix.elements[13] - 1) / projectionMatrix.elements[5];
        var near = -projectionMatrix.elements[14] / projectionMatrix.elements[10];
        var far = Math.min(maxFar, -(projectionMatrix.elements[14] - 2) / projectionMatrix.elements[10]);
        // 近平面顶点
        this.vertices.near[0].set(left, top, -near);
        this.vertices.near[1].set(right, top, -near);
        this.vertices.near[2].set(right, bottom, -near);
        this.vertices.near[3].set(left, bottom, -near);
        // 远平面顶点
        this.vertices.far[0].set(left, top, -far);
        this.vertices.far[1].set(right, top, -far);
        this.vertices.far[2].set(right, bottom, -far);
        this.vertices.far[3].set(left, bottom, -far);
    };
    /**
     * 获取包围盒
     * @param box 包围盒
     * @returns 包围盒
     */
    Frustum.prototype.getBoundingBox = function (box) {
        if (box === void 0) { box = new THREE.Box3(); }
        box.makeEmpty();
        // 添加近平面顶点
        for (var i = 0; i < 4; i++) {
            box.expandByPoint(this.vertices.near[i]);
        }
        // 添加远平面顶点
        for (var i = 0; i < 4; i++) {
            box.expandByPoint(this.vertices.far[i]);
        }
        return box;
    };
    /**
     * 应用矩阵变换
     * @param matrix 变换矩阵
     */
    Frustum.prototype.applyMatrix4 = function (matrix) {
        // 变换近平面顶点
        for (var i = 0; i < 4; i++) {
            this.vertices.near[i].applyMatrix4(matrix);
        }
        // 变换远平面顶点
        for (var i = 0; i < 4; i++) {
            this.vertices.far[i].applyMatrix4(matrix);
        }
    };
    /**
     * 复制视锥体
     * @param frustum 源视锥体
     * @returns 当前视锥体
     */
    Frustum.prototype.copy = function (frustum) {
        // 复制近平面顶点
        for (var i = 0; i < 4; i++) {
            this.vertices.near[i].copy(frustum.vertices.near[i]);
        }
        // 复制远平面顶点
        for (var i = 0; i < 4; i++) {
            this.vertices.far[i].copy(frustum.vertices.far[i]);
        }
        return this;
    };
    /**
     * 分割视锥体
     * @param breaks 分割点
     * @param result 结果数组
     * @returns 分割后的视锥体数组
     */
    Frustum.prototype.split = function (breaks, result) {
        if (result === void 0) { result = []; }
        result.length = 0;
        for (var i = 0; i < breaks.length; i++) {
            var start = i === 0 ? 0 : breaks[i - 1];
            var end = breaks[i];
            var subFrustum = new Frustum();
            // 计算子视锥体的顶点
            for (var j = 0; j < 4; j++) {
                var nearPoint = this.vertices.near[j];
                var farPoint = this.vertices.far[j];
                var startPoint = new THREE.Vector3().lerpVectors(nearPoint, farPoint, start);
                var endPoint = new THREE.Vector3().lerpVectors(nearPoint, farPoint, end);
                subFrustum.vertices.near[j].copy(startPoint);
                subFrustum.vertices.far[j].copy(endPoint);
            }
            result.push(subFrustum);
        }
        return result;
    };
    return Frustum;
}());
exports.Frustum = Frustum;
