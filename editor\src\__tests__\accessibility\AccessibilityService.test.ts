/**
 * 辅助功能服务测试
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import AccessibilityService, { AccessibilityConfig, AccessibilityEventType } from '../../services/AccessibilityService';

// 模拟matchMedia
const mockMatchMedia = (matches: boolean) => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });
};

// 模拟document.createElement
const mockCreateElement = () => {
  const originalCreateElement = document.createElement;
  const mockElement = {
    style: {},
    setAttribute: vi.fn(),
    appendChild: vi.fn(),
    id: '',
    textContent: '',
  };
  
  vi.spyOn(document, 'createElement').mockImplementation((tagName: string) => {
    if (tagName === 'style') {
      return mockElement as any;
    }
    return originalCreateElement.call(document, tagName);
  });
  
  return mockElement;
};

describe('AccessibilityService', () => {
  let mockStyleElement: any;
  let mockAnnouncerElement: any;
  
  beforeEach(() => {
    // 重置服务状态
    AccessibilityService['instance'] = null as any;
    
    // 模拟document.head.appendChild
    vi.spyOn(document.head, 'appendChild').mockImplementation(vi.fn());
    
    // 模拟document.body.appendChild
    vi.spyOn(document.body, 'appendChild').mockImplementation(vi.fn());
    
    // 模拟document.getElementById
    vi.spyOn(document, 'getElementById').mockImplementation(() => null);
    
    // 模拟matchMedia
    mockMatchMedia(false);
    
    // 模拟createElement
    mockStyleElement = mockCreateElement();
  });
  
  afterEach(() => {
    vi.restoreAllMocks();
  });
  
  it('应该创建单例实例', () => {
    const instance1 = AccessibilityService;
    const instance2 = AccessibilityService;
    
    expect(instance1).toBe(instance2);
  });
  
  it('应该使用默认配置初始化', () => {
    const config = AccessibilityService.getConfig();
    
    expect(config).toEqual({
      enabled: true,
      enableScreenReader: true,
      enableKeyboardNavigation: true,
      enableHighContrast: false,
      enableTextScaling: false,
      enableReducedMotion: false,
      enableAutoAnnounce: true,
      enableFocusIndicator: true,
      enableColorAdjustment: false,
      debug: false
    });
  });
  
  it('应该检测系统辅助功能设置', () => {
    // 模拟系统减弱动画设置
    mockMatchMedia(true);
    
    // 重新初始化服务
    AccessibilityService['instance'] = null as any;
    const service = AccessibilityService;
    const config = service.getConfig();
    
    expect(config.enableReducedMotion).toBe(true);
  });
  
  it('应该配置服务', () => {
    const newConfig: Partial<AccessibilityConfig> = {
      enableHighContrast: true,
      enableTextScaling: true
    };
    
    const eventSpy = vi.spyOn(AccessibilityService, 'emit');
    
    AccessibilityService.configure(newConfig);
    const config = AccessibilityService.getConfig();
    
    expect(config.enableHighContrast).toBe(true);
    expect(config.enableTextScaling).toBe(true);
    expect(eventSpy).toHaveBeenCalledWith(AccessibilityEventType.HIGH_CONTRAST_CHANGED, {
      enabled: true
    });
  });
  
  it('应该应用高对比度模式', () => {
    AccessibilityService.configure({
      enableHighContrast: true
    });
    
    expect(mockStyleElement.textContent).toContain('high-contrast');
    expect(document.head.appendChild).toHaveBeenCalled();
  });
  
  it('应该应用文本缩放', () => {
    AccessibilityService.configure({
      enableTextScaling: true
    });
    
    expect(mockStyleElement.textContent).toContain('font-size');
    expect(document.head.appendChild).toHaveBeenCalled();
  });
  
  it('应该应用减弱动画', () => {
    AccessibilityService.configure({
      enableReducedMotion: true
    });
    
    expect(mockStyleElement.textContent).toContain('transition');
    expect(document.head.appendChild).toHaveBeenCalled();
  });
  
  it('应该应用颜色调整', () => {
    AccessibilityService.configure({
      enableColorAdjustment: true
    });
    
    AccessibilityService.setColorAdjustments({
      brightness: 1.2,
      contrast: 1.1,
      saturation: 0.9
    });
    
    expect(mockStyleElement.textContent).toContain('filter');
    expect(mockStyleElement.textContent).toContain('brightness');
    expect(mockStyleElement.textContent).toContain('contrast');
    expect(mockStyleElement.textContent).toContain('saturate');
    expect(document.head.appendChild).toHaveBeenCalled();
  });
  
  it('应该朗读消息', () => {
    // 模拟document.getElementById返回null，然后创建新元素
    vi.spyOn(document, 'getElementById').mockImplementation(() => null);
    mockAnnouncerElement = mockCreateElement();
    
    // 模拟setTimeout
    vi.useFakeTimers();
    
    AccessibilityService.announce('测试消息', 'polite');
    
    expect(document.createElement).toHaveBeenCalledWith('div');
    expect(mockAnnouncerElement.setAttribute).toHaveBeenCalledWith('aria-live', 'polite');
    expect(mockAnnouncerElement.setAttribute).toHaveBeenCalledWith('aria-atomic', 'true');
    
    // 前进时间以触发setTimeout回调
    vi.advanceTimersByTime(100);
    
    expect(mockAnnouncerElement.textContent).toBe('测试消息');
    
    // 恢复真实计时器
    vi.useRealTimers();
  });
  
  it('应该在禁用时不朗读消息', () => {
    AccessibilityService.configure({
      enableScreenReader: false
    });
    
    AccessibilityService.announce('测试消息');
    
    expect(document.createElement).not.toHaveBeenCalled();
  });
  
  it('应该正确报告屏幕阅读器状态', () => {
    AccessibilityService.configure({
      enableScreenReader: true
    });
    
    expect(AccessibilityService.isScreenReaderEnabled()).toBe(true);
    
    AccessibilityService.configure({
      enableScreenReader: false
    });
    
    expect(AccessibilityService.isScreenReaderEnabled()).toBe(false);
    
    AccessibilityService.configure({
      enabled: false,
      enableScreenReader: true
    });
    
    expect(AccessibilityService.isScreenReaderEnabled()).toBe(false);
  });
  
  it('应该正确报告高对比度模式状态', () => {
    AccessibilityService.configure({
      enableHighContrast: true
    });
    
    expect(AccessibilityService.isHighContrastEnabled()).toBe(true);
    
    AccessibilityService.configure({
      enableHighContrast: false
    });
    
    expect(AccessibilityService.isHighContrastEnabled()).toBe(false);
    
    AccessibilityService.configure({
      enabled: false,
      enableHighContrast: true
    });
    
    expect(AccessibilityService.isHighContrastEnabled()).toBe(false);
  });
  
  it('应该获取文本缩放比例', () => {
    const defaultFactor = AccessibilityService.getTextScaleFactor();
    expect(defaultFactor).toBe(1);
    
    // 设置文本缩放
    AccessibilityService['instance'].textScaleFactor = 1.5;
    
    const factor = AccessibilityService.getTextScaleFactor();
    expect(factor).toBe(1.5);
  });
  
  it('应该获取颜色调整设置', () => {
    const defaultAdjustments = AccessibilityService.getColorAdjustments();
    expect(defaultAdjustments).toEqual({
      brightness: 1,
      contrast: 1,
      saturation: 1
    });
    
    // 设置颜色调整
    AccessibilityService.setColorAdjustments({
      brightness: 1.2,
      contrast: 1.1
    });
    
    const adjustments = AccessibilityService.getColorAdjustments();
    expect(adjustments).toEqual({
      brightness: 1.2,
      contrast: 1.1,
      saturation: 1
    });
  });
});
