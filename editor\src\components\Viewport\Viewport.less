/**
 * 视口样式
 */
.viewport-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  
  .viewport-canvas {
    width: 100%;
    height: 100%;
    display: block;
  }
  
  .viewport-toolbar {
    position: absolute;
    top: 16px;
    left: 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .toolbar-group {
      display: flex;
      gap: 4px;
      background: rgba(0, 0, 0, 0.5);
      padding: 4px;
      border-radius: 4px;
      
      .ant-btn {
        color: #fff;
        border-color: #444;
        background: rgba(0, 0, 0, 0.3);
        
        &:hover {
          background: rgba(0, 0, 0, 0.5);
        }
        
        &.ant-btn-primary {
          background: #1890ff;
          border-color: #1890ff;
        }
      }
    }
  }
  
  .viewport-status {
    position: absolute;
    bottom: 16px;
    left: 16px;
    right: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.5);
    padding: 8px;
    border-radius: 4px;
    color: #fff;
    
    .status-info {
      font-size: 12px;
    }
  }
}
