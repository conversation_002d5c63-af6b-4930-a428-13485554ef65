/**
 * 教程高亮样式
 */

.tutorial-highlight {
  position: absolute;
  pointer-events: none;
  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.5);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.tutorial-highlight.pulse {
  animation: tutorial-highlight-pulse 2s infinite;
}

@keyframes tutorial-highlight-pulse {
  0% {
    box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(24, 144, 255, 0.3);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.5);
  }
}

.tutorial-highlight-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  pointer-events: none;
}

.tutorial-highlight-arrow-top {
  border-width: 0 8px 8px 8px;
  border-color: transparent transparent rgba(24, 144, 255, 0.9) transparent;
}

.tutorial-highlight-arrow-right {
  border-width: 8px 0 8px 8px;
  border-color: transparent transparent transparent rgba(24, 144, 255, 0.9);
}

.tutorial-highlight-arrow-bottom {
  border-width: 8px 8px 0 8px;
  border-color: rgba(24, 144, 255, 0.9) transparent transparent transparent;
}

.tutorial-highlight-arrow-left {
  border-width: 8px 8px 8px 0;
  border-color: transparent rgba(24, 144, 255, 0.9) transparent transparent;
}

.tutorial-highlight-tooltip-trigger {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.tutorial-highlight-tooltip {
  z-index: 1100;
}

.tutorial-highlight-tooltip .ant-tooltip-inner {
  background-color: rgba(24, 144, 255, 0.9);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  max-width: 300px;
}

.tutorial-highlight-tooltip .ant-tooltip-arrow-content {
  background-color: rgba(24, 144, 255, 0.9);
}
