/**
 * 动画系统示例
 * 展示如何使用动画系统
 */
import * as THREE from 'three';
import { Engine } from '../../core/Engine';
import { Entity } from '../../core/Entity';
import { World } from '../../core/World';
import { Camera } from '../../core/Camera';
import { Animator } from '../../animation/Animator';
import { AnimationClip, LoopMode } from '../../animation/AnimationClip';
import { BlendSpace1D } from '../../animation/BlendSpace1D';
import { BlendSpace2D } from '../../animation/BlendSpace2D';
import { AnimationStateMachine } from '../../animation/AnimationStateMachine';
import { GLTFLoader } from '../../gltf/GLTFLoader';
import { GLTFModelComponent } from '../../gltf/components/GLTFModelComponent';
import { GLTFAnimationComponent } from '../../gltf/components/GLTFAnimationComponent';
import { AvatarAnimationComponent } from '../../avatar/components/AvatarAnimationComponent';
import { AvatarRigComponent } from '../../avatar/components/AvatarRigComponent';
import { AvatarAnimationSystem } from '../../avatar/systems/AvatarAnimationSystem';

/**
 * 动画示例
 */
export class AnimationExample {
  /** 引擎实例 */
  private engine: Engine;
  /** 世界实例 */
  private world: World;
  /** 相机实体 */
  private cameraEntity: Entity;
  /** 模型实体 */
  private modelEntity: Entity;
  /** Avatar实体 */
  private avatarEntity: Entity;
  /** 动画系统 */
  private avatarAnimationSystem: AvatarAnimationSystem;
  /** 是否已加载模型 */
  private modelLoaded: boolean = false;
  /** 动画ID */
  private animationId: number = 0;
  /** 上一帧时间 */
  private _lastTime: number = 0;
  /** 是否正在运行 */
  private running: boolean = false;

  /**
   * 构造函数
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 获取世界
    this.world = this.engine.getWorld();
    
    // 创建Avatar动画系统
    this.avatarAnimationSystem = new AvatarAnimationSystem(this.world, {
      debug: true,
      autoRetarget: true,
      useStateMachine: true,
      useBlendSpace: true
    });
    
    // 添加系统到世界
    this.world.addSystem(this.avatarAnimationSystem);
  }

  /**
   * 启动示例
   */
  public start(): void {
    // 创建场景
    this.createScene();
    
    // 加载模型
    this.loadModel();
    
    // 启动引擎
    this.engine.start();
    
    // 开始动画循环
    this.running = true;
    this.animate();
    
    console.log('动画示例已启动');
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 创建相机实体
    this.cameraEntity = this.world.createEntity();
    
    // 添加相机组件
    const camera = new Camera();
    camera.setPerspective(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.setPosition(0, 5, 10);
    camera.lookAt(0, 0, 0);
    this.cameraEntity.addComponent(camera);
    
    // 创建灯光
    const lightEntity = this.world.createEntity();
    const light = new THREE.DirectionalLight(0xffffff, 1);
    light.position.set(0, 10, 10);
    lightEntity.addComponent('Light', light);
    
    // 创建地面
    const groundEntity = this.world.createEntity();
    const groundGeometry = new THREE.PlaneGeometry(20, 20);
    const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x808080 });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    groundEntity.addComponent('Transform', ground);
  }

  /**
   * 加载模型
   */
  private loadModel(): void {
    // 创建模型实体
    this.modelEntity = this.world.createEntity();
    
    // 添加GLTF模型组件
    const modelComponent = new GLTFModelComponent();
    this.modelEntity.addComponent(modelComponent);
    
    // 加载模型
    modelComponent.load('models/character.glb');
    
    // 创建Avatar实体
    this.avatarEntity = this.world.createEntity();
    
    // 添加Avatar组件
    const avatarRigComponent = new AvatarRigComponent();
    this.avatarEntity.addComponent(avatarRigComponent);
    
    const avatarAnimationComponent = new AvatarAnimationComponent();
    this.avatarEntity.addComponent(avatarAnimationComponent);
    
    // 注册Avatar实体
    this.avatarAnimationSystem.registerAvatar(this.avatarEntity);
  }

  /**
   * 更新
   * @param deltaTime 时间增量（秒）
   */
  private update(deltaTime: number): void {
    // 检查模型是否已加载
    const modelComponent = this.modelEntity.getComponent<GLTFModelComponent>(GLTFModelComponent.type);
    if (modelComponent && modelComponent.isLoaded() && !this.modelLoaded) {
      this.modelLoaded = true;
      console.log('模型已加载');
      
      // 获取动画组件
      const animationComponent = this.modelEntity.getComponent<GLTFAnimationComponent>(GLTFAnimationComponent.type);
      if (animationComponent) {
        // 获取所有动画剪辑
        const clips = animationComponent.getClips();
        console.log(`模型包含 ${clips.length} 个动画`);
        
        // 如果有动画，播放第一个
        if (clips.length > 0) {
          animationComponent.play(clips[0].name);
        }
        
        // 设置Avatar动画组件的动画控制器
        const avatarAnimationComponent = this.avatarEntity.getComponent<AvatarAnimationComponent>(AvatarAnimationComponent.type);
        if (avatarAnimationComponent) {
          avatarAnimationComponent.setAnimator(animationComponent.getAnimator());
        }
      }
    }
    
    // 更新Avatar动画系统
    this.avatarAnimationSystem.update(deltaTime);
  }

  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;
    
    const now = performance.now();
    const deltaTime = Math.min((now - (this._lastTime || now)) / 1000, 0.1);
    this._lastTime = now;
    
    this.update(deltaTime);
    
    this.animationId = requestAnimationFrame(this.animate.bind(this));
  }

  /**
   * 停止示例
   */
  public stop(): void {
    // 停止动画循环
    this.running = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = 0;
    }
    
    // 停止引擎
    this.engine.stop();
    
    console.log('动画示例已停止');
  }

  /**
   * 播放动画
   * @param name 动画名称
   */
  public playAnimation(name: string): void {
    // 获取Avatar动画组件
    const avatarAnimationComponent = this.avatarEntity.getComponent<AvatarAnimationComponent>(AvatarAnimationComponent.type);
    if (!avatarAnimationComponent) {
      console.warn('Avatar没有动画组件');
      return;
    }
    
    // 设置当前节点
    avatarAnimationComponent.setCurrentNode(name);
  }

  /**
   * 设置运动参数
   * @param x X分量
   * @param y Y分量
   * @param z Z分量
   */
  public setLocomotion(x: number, y: number, z: number): void {
    // 获取Avatar动画组件
    const avatarAnimationComponent = this.avatarEntity.getComponent<AvatarAnimationComponent>(AvatarAnimationComponent.type);
    if (!avatarAnimationComponent) {
      return;
    }
    
    // 设置运动向量
    avatarAnimationComponent.setLocomotion(x, y, z);
  }

  /**
   * 调整大小
   */
  public resize(): void {
    // 获取相机组件
    const camera = this.cameraEntity.getComponent<Camera>(Camera.type);
    if (camera) {
      // 更新相机宽高比
      camera.updateAspect();
    }
  }
}
