.git-panel {
  position: absolute;
  top: 50px;
  right: 20px;
  width: 600px;
  z-index: 1000;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

.git-card {
  border-radius: 4px;
  overflow: hidden;
}

.git-branch-info {
  font-size: 12px;
  background-color: rgba(0, 0, 0, 0.06);
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
}

.git-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.git-loading-text {
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.45);
}
