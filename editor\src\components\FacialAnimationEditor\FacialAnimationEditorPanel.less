/**
 * 面部动画编辑器面板样式
 */
.facial-animation-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #1f1f1f;
  color: #f0f0f0;
  
  .facial-animation-editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #2a2a2a;
    border-bottom: 1px solid #3a3a3a;
    
    .clip-info {
      display: flex;
      align-items: center;
    }
    
    .editor-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .facial-animation-editor-content {
    flex: 1;
    display: flex;
    overflow: hidden;
    
    .editor-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      
      .preview-panel {
        height: 40%;
        min-height: 200px;
        padding: 16px;
        display: flex;
        flex-direction: column;
        
        .playback-controls {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-top: 16px;
          
          .time-display {
            margin-left: 8px;
            font-family: monospace;
          }
        }
      }
      
      .editor-tabs {
        flex: 1;
        overflow: hidden;
        
        :global(.ant-tabs) {
          height: 100%;
          
          :global(.ant-tabs-content) {
            height: 100%;
            
            :global(.ant-tabs-tabpane) {
              height: 100%;
              overflow: auto;
            }
          }
        }
      }
    }
  }
}
