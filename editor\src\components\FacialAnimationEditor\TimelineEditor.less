/**
 * 时间轴编辑器样式
 */
.timeline-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .timeline-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background-color: #2a2a2a;
    border-bottom: 1px solid #3a3a3a;
  }
  
  .timeline-container {
    flex: 1;
    display: flex;
    overflow: hidden;
    
    .tracks-header {
      width: 200px;
      flex-shrink: 0;
      background-color: #252525;
      border-right: 1px solid #3a3a3a;
      overflow-y: auto;
      
      .track-header {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        height: 40px;
        border-bottom: 1px solid #333;
        cursor: pointer;
        
        &:hover {
          background-color: #2f2f2f;
        }
        
        &.selected {
          background-color: #303030;
        }
        
        .track-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
          margin-right: 8px;
        }
        
        .track-name {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .track-actions {
          display: flex;
          gap: 4px;
          opacity: 0.5;
          
          &:hover {
            opacity: 1;
          }
        }
      }
    }
    
    .timeline-view {
      flex: 1;
      position: relative;
      background-color: #1a1a1a;
      overflow: auto;
      
      .time-ruler {
        position: relative;
        height: 24px;
        background-color: #252525;
        border-bottom: 1px solid #3a3a3a;
        
        .time-mark {
          position: absolute;
          top: 0;
          height: 100%;
          
          &.second-mark {
            .mark-line {
              position: absolute;
              top: 0;
              left: 0;
              width: 1px;
              height: 100%;
              background-color: #555;
            }
            
            .mark-label {
              position: absolute;
              top: 4px;
              left: 4px;
              font-size: 10px;
              color: #aaa;
            }
          }
          
          &.frame-mark {
            .mark-line {
              position: absolute;
              top: 12px;
              left: 0;
              width: 1px;
              height: 12px;
              background-color: #444;
            }
          }
        }
      }
      
      .current-time-indicator {
        position: absolute;
        top: 0;
        width: 1px;
        height: 100%;
        background-color: #f00;
        z-index: 10;
        pointer-events: none;
      }
      
      .tracks-container {
        .timeline-track {
          position: relative;
          height: 40px;
          border-bottom: 1px solid #333;
          
          &:hover {
            background-color: rgba(255, 255, 255, 0.05);
          }
          
          &.selected {
            background-color: rgba(24, 144, 255, 0.1);
          }
          
          .keyframe {
            position: absolute;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 10px;
            height: 10px;
            background-color: #fff;
            border-radius: 50%;
            cursor: pointer;
            z-index: 5;
            
            &:hover {
              transform: translate(-50%, -50%) scale(1.2);
            }
            
            &.selected {
              background-color: #1890ff;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.5);
            }
          }
        }
      }
    }
  }
}
