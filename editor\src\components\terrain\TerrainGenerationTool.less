/**
 * 地形生成工具样式
 */
.terrain-generation-tool {
  width: 100%;
  
  .generation-parameters-card,
  .generation-preview-card,
  .generation-presets-card,
  .generation-actions-card {
    height: 100%;
    
    .ant-card-body {
      padding: 16px;
    }
  }
  
  .preview-container {
    width: 100%;
    height: 200px;
    border-radius: 4px;
    overflow: hidden;
    background-color: #f0f0f0;
    margin-bottom: 16px;
    position: relative;
    
    .preview-disabled {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.1);
      
      .anticon {
        font-size: 32px;
        margin-bottom: 8px;
        color: #999;
      }
      
      .ant-typography {
        color: #666;
      }
    }
  }
  
  .preview-controls {
    display: flex;
    align-items: center;
  }
  
  .presets-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
    
    .preset-button {
      flex: 1 0 calc(50% - 8px);
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  .presets-actions {
    display: flex;
    justify-content: flex-end;
  }
  
  .generation-progress {
    .ant-progress {
      margin-bottom: 16px;
    }
  }
  
  .generation-buttons {
    display: flex;
    gap: 8px;
  }
}
