/**
 * 事件发射器类
 * 用于实现事件的发布-订阅模式
 */
export class EventEmitter {
  /** 事件监听器映射 */
  private listeners: Map<string, Array<(...args: any[]) => void>> = new Map();

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  public on(event: string, listener: (...args: any[]) => void): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }

  /**
   * 添加一次性事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  public once(event: string, listener: (...args: any[]) => void): void {
    const onceWrapper = (...args: any[]) => {
      listener(...args);
      this.off(event, onceWrapper);
    };
    this.on(event, onceWrapper);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  public off(event: string, listener: (...args: any[]) => void): void {
    if (!this.listeners.has(event)) {
      return;
    }

    const eventListeners = this.listeners.get(event)!;
    const index = eventListeners.indexOf(listener);
    
    if (index !== -1) {
      eventListeners.splice(index, 1);
    }

    // 如果没有监听器了，则删除该事件
    if (eventListeners.length === 0) {
      this.listeners.delete(event);
    }
  }

  /**
   * 移除所有事件监听器
   * @param event 事件名称，如果不提供则移除所有事件的所有监听器
   */
  public removeAllListeners(event?: string): void {
    if (event) {
      this.listeners.delete(event);
    } else {
      this.listeners.clear();
    }
  }

  /**
   * 发射事件
   * @param event 事件名称
   * @param args 事件参数
   */
  public emit(event: string, ...args: any[]): boolean {
    if (!this.listeners.has(event)) {
      return false;
    }

    const eventListeners = [...this.listeners.get(event)!];
    
    for (const listener of eventListeners) {
      try {
        listener(...args);
      } catch (error) {
        console.error(`事件监听器执行错误: ${event}`, error);
      }
    }

    return true;
  }

  /**
   * 获取事件监听器数量
   * @param event 事件名称
   * @returns 监听器数量
   */
  public listenerCount(event: string): number {
    if (!this.listeners.has(event)) {
      return 0;
    }
    return this.listeners.get(event)!.length;
  }

  /**
   * 获取事件监听器列表
   * @param event 事件名称
   * @returns 监听器列表
   */
  public listeners(event: string): Array<(...args: any[]) => void> {
    if (!this.listeners.has(event)) {
      return [];
    }
    return [...this.listeners.get(event)!];
  }

  /**
   * 获取所有事件名称
   * @returns 事件名称列表
   */
  public eventNames(): string[] {
    return Array.from(this.listeners.keys());
  }
}
