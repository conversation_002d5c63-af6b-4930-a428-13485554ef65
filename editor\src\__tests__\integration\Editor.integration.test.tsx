/**
 * 编辑器集成测试
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '../../__tests__/utils/test-utils';
import { EditorPage } from '../../pages/EditorPage';
import { vi } from 'vitest';
import { MemoryRouter, Route, Routes } from 'react-router-dom';

// 模拟服务
vi.mock('../../services/EngineService', () => ({
  __esModule: true,
  default: {
    initialize: vi.fn().mockResolvedValue(undefined),
    on: vi.fn(),
    off: vi.fn(),
    getActiveScene: vi.fn(),
    getActiveCamera: vi.fn(),
    EngineEventType: {
      OBJECT_SELECTED: 'object-selected',
      OBJECT_DESELECTED: 'object-deselected',
    },
  },
  EngineEventType: {
    OBJECT_SELECTED: 'object-selected',
    OBJECT_DESELECTED: 'object-deselected',
  },
  TransformMode: {
    TRANSLATE: 'translate',
    ROTATE: 'rotate',
    SCALE: 'scale',
  },
  TransformSpace: {
    LOCAL: 'local',
    WORLD: 'world',
  },
}));

vi.mock('../../services/SceneService', () => ({
  __esModule: true,
  default: {
    loadScene: vi.fn().mockResolvedValue(undefined),
    on: vi.fn(),
    off: vi.fn(),
    SceneEventType: {
      SCENE_LOADED: 'scene-loaded',
      SCENE_UNLOADED: 'scene-unloaded',
    },
  },
  SceneEventType: {
    SCENE_LOADED: 'scene-loaded',
    SCENE_UNLOADED: 'scene-unloaded',
  },
}));

vi.mock('../../services/ProjectService', () => ({
  __esModule: true,
  default: {
    getProject: vi.fn().mockResolvedValue({
      id: 'test-project',
      name: '测试项目',
      description: '测试项目描述',
      scenes: [
        {
          id: 'test-scene',
          name: '测试场景',
        },
      ],
    }),
    getScenes: vi.fn().mockResolvedValue([
      {
        id: 'test-scene',
        name: '测试场景',
      },
    ]),
  },
}));

// 模拟rc-dock
vi.mock('rc-dock', () => ({
  DockLayout: ({ defaultLayout, onLayoutChange, style, ref }: any) => (
    <div data-testid="mock-dock-layout" style={style}>
      <div data-testid="mock-dock-layout-content">
        {JSON.stringify(defaultLayout)}
      </div>
      <button 
        data-testid="mock-layout-change-button"
        onClick={() => onLayoutChange && onLayoutChange({ test: 'new-layout' })}
      >
        Change Layout
      </button>
    </div>
  ),
}));

// 模拟组件
vi.mock('../../components/layout/EditorLayout', () => ({
  __esModule: true,
  EditorLayout: ({ projectId, sceneId }: any) => (
    <div data-testid="mock-editor-layout">
      <div>Project ID: {projectId}</div>
      <div>Scene ID: {sceneId}</div>
    </div>
  ),
}));

describe('编辑器集成测试', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('应该正确渲染编辑器页面', async () => {
    render(
      <MemoryRouter initialEntries={['/editor/test-project/test-scene']}>
        <Routes>
          <Route path="/editor/:projectId/:sceneId" element={<EditorPage />} />
        </Routes>
      </MemoryRouter>
    );

    // 等待编辑器布局加载
    await waitFor(() => {
      expect(screen.getByTestId('mock-editor-layout')).toBeInTheDocument();
    });

    // 验证项目ID和场景ID已正确传递
    expect(screen.getByText('Project ID: test-project')).toBeInTheDocument();
    expect(screen.getByText('Scene ID: test-scene')).toBeInTheDocument();
  });

  it('应该在加载编辑器时显示加载状态', async () => {
    // 模拟ProjectService.getProject返回一个延迟的Promise
    const mockGetProject = vi.fn().mockImplementation(() => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            id: 'test-project',
            name: '测试项目',
            description: '测试项目描述',
            scenes: [
              {
                id: 'test-scene',
                name: '测试场景',
              },
            ],
          });
        }, 100);
      });
    });

    // 替换模拟实现
    vi.mock('../../services/ProjectService', () => ({
      __esModule: true,
      default: {
        getProject: mockGetProject,
        getScenes: vi.fn().mockResolvedValue([
          {
            id: 'test-scene',
            name: '测试场景',
          },
        ]),
      },
    }), { virtual: true });

    render(
      <MemoryRouter initialEntries={['/editor/test-project/test-scene']}>
        <Routes>
          <Route path="/editor/:projectId/:sceneId" element={<EditorPage />} />
        </Routes>
      </MemoryRouter>
    );

    // 验证加载状态
    expect(screen.getByText('editor.loading')).toBeInTheDocument();

    // 等待加载完成
    await waitFor(() => {
      expect(screen.getByTestId('mock-editor-layout')).toBeInTheDocument();
    });
  });

  it('应该处理无效的项目ID或场景ID', async () => {
    // 模拟ProjectService.getProject返回错误
    const mockGetProject = vi.fn().mockRejectedValue(new Error('项目不存在'));

    // 替换模拟实现
    vi.mock('../../services/ProjectService', () => ({
      __esModule: true,
      default: {
        getProject: mockGetProject,
        getScenes: vi.fn().mockResolvedValue([]),
      },
    }), { virtual: true });

    render(
      <MemoryRouter initialEntries={['/editor/invalid-project/invalid-scene']}>
        <Routes>
          <Route path="/editor/:projectId/:sceneId" element={<EditorPage />} />
        </Routes>
      </MemoryRouter>
    );

    // 等待错误消息显示
    await waitFor(() => {
      expect(screen.getByText('editor.projectNotFound')).toBeInTheDocument();
    });
  });
});
