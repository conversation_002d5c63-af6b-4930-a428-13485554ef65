/**
 * 资源更新进度监控组件样式
 */
.resource-update-monitor {
  width: 100%;
  
  &.compact {
    .ant-card {
      box-shadow: none;
      
      .ant-card-head {
        min-height: 40px;
        padding: 0 12px;
        
        .ant-card-head-title {
          padding: 8px 0;
        }
      }
      
      .ant-card-body {
        padding: 12px;
      }
    }
    
    .update-controls {
      margin-top: 8px;
    }
  }
  
  .monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h4 {
      margin: 0;
    }
  }
  
  .status-header {
    margin-bottom: 12px;
  }
  
  .update-controls {
    margin-top: 16px;
  }
  
  .update-details {
    margin-top: 16px;
    
    .details-content {
      padding: 8px;
      background-color: #f5f5f5;
      border-radius: 4px;
      
      .detail-item {
        margin-bottom: 8px;
        display: flex;
        
        strong {
          width: 100px;
          flex-shrink: 0;
        }
        
        &.changelog {
          flex-direction: column;
          
          ul {
            margin-top: 8px;
            padding-left: 20px;
          }
        }
      }
    }
  }
  
  .update-history {
    margin-top: 16px;
    
    .ant-list-item {
      cursor: pointer;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #f0f0f0;
      }
      
      &.selected-history-item {
        background-color: #e6f7ff;
      }
      
      .history-item-details {
        margin-top: 8px;
        padding: 8px;
        background-color: #f5f5f5;
        border-radius: 4px;
        width: 100%;
        
        ul {
          margin-top: 8px;
          padding-left: 20px;
        }
      }
    }
  }
}
