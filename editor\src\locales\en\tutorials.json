{"tutorials": {"title": "Tutorials", "description": "Learn the features of the IR Engine Editor through interactive tutorials.", "allTutorials": "All Tutorials", "recommended": "Recommended", "completed": {"title": "Tutorial Completed!", "message": "Congratulations! You have successfully completed the \"{title}\" tutorial.", "reward": "<PERSON><PERSON> Earned", "badgeReward": "Badge: {badge}", "itemReward": "Item: {item}", "close": "Close"}, "interactive": "Interactive Tutorials", "interactiveTutorials": "Interactive Tutorials", "interactiveDescription": "Interactive tutorials guide you step by step through tasks and provide real-time feedback.", "noInteractiveTutorials": "No interactive tutorials available yet. Please check back later.", "recommendedForYou": "Recommended for You", "completedTutorials": "Completed Tutorials", "start": "Start", "continue": "Continue", "restart": "<PERSON><PERSON>", "next": "Next", "previous": "Previous", "complete": "Complete", "skip": "<PERSON><PERSON>", "exit": "Exit", "step": "Step", "duration": "Estimated Time", "minutes": "minutes", "progressStatus": "Progress: {current}/{total}", "taskProgress": "Tasks: {completed}/{total}", "progress": "Step {current}/{total}", "completedTitle": "Tutorial Completed", "completedMessage": "Congratulations! You have completed the \"{title}\" tutorial.", "achievementUnlocked": "Achievement Unlocked!", "prerequisites": "Prerequisites", "hasPrerequisites": "Has Prerequisites", "helpTooltip": "Need help?", "difficulty": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced", "easy": "Easy", "medium": "Medium", "hard": "Hard", "expert": "Expert"}, "editorBasics": {"title": "Editor Basics", "description": "Learn the basic interface and operations of the IR Engine Editor.", "steps": {"welcome": {"title": "Welcome", "description": "Welcome to the IR Engine Editor! This tutorial will help you understand the basic interface and operations of the editor."}, "interfaceOverview": {"title": "Interface Overview", "description": "The IR Engine Editor interface consists of multiple panels, including the Scene View, Hierarchy Panel, Properties Panel, and more. You can adjust the layout of these panels as needed."}, "navigation": {"title": "Scene Navigation", "description": "Learn how to navigate in the scene, including panning, rotating, and zooming the view."}, "selection": {"title": "Selecting Objects", "description": "Learn how to select objects in the scene, as well as how to use multi-selection and box selection."}, "transformation": {"title": "Transforming Objects", "description": "Learn how to move, rotate, and scale objects in the scene."}, "properties": {"title": "Editing Properties", "description": "Learn how to edit object properties in the Properties Panel."}, "conclusion": {"title": "Conclusion", "description": "Congratulations! You have learned the basic operations of the IR Engine Editor. Now you can start creating your own scenes."}}}, "animationSystem": {"title": "Animation System", "description": "Learn how to create and edit animations using the IR Engine animation system.", "steps": {"introduction": {"title": "Introduction", "description": "The IR Engine animation system allows you to create complex character animations and scene animations. This tutorial will help you understand the basic concepts and usage of the animation system."}, "animationTypes": {"title": "Animation Types", "description": "Learn about different types of animations, including keyframe animations, skeletal animations, and blended animations."}, "keyframeAnimation": {"title": "Keyframe Animation", "description": "Learn how to create and edit keyframe animations."}, "animationBlending": {"title": "Animation Blending", "description": "Learn how to blend multiple animations to create smooth transitions."}, "stateMachines": {"title": "State Machines", "description": "Learn how to use state machines to control character animations."}, "conclusion": {"title": "Conclusion", "description": "Congratulations! You have learned the basic usage of the IR Engine animation system. Now you can start creating your own animations."}}}, "visualScripting": {"title": "Visual Scripting", "description": "Learn how to create interactive content using the visual scripting system.", "steps": {"introduction": {"title": "Introduction", "description": "Visual scripting is a way to create interactive content and game logic without writing code. This tutorial will help you understand the basic concepts and usage of the visual scripting system."}, "basicConcepts": {"title": "Basic Concepts", "description": "Learn about basic concepts such as nodes, connections, and events."}, "creatingLogic": {"title": "Creating Logic", "description": "Learn how to create simple game logic."}, "variables": {"title": "Variables", "description": "Learn how to use variables to store and manipulate data."}, "events": {"title": "Events", "description": "Learn how to respond to user input and other events."}, "conclusion": {"title": "Conclusion", "description": "Congratulations! You have learned the basic usage of the visual scripting system. Now you can start creating your own interactive content."}}}, "characterCreation": {"title": "Character Creation", "description": "Learn how to create and customize 3D characters.", "steps": {"introduction": {"title": "Introduction", "description": "This tutorial will guide you through creating a complete 3D character, including model import, material setup, rigging, and animation setup."}, "modelImport": {"title": "Model Import", "description": "Learn how to import character models."}, "materialSetup": {"title": "Material Setup", "description": "Learn how to set up character materials."}, "rigging": {"title": "Rigging", "description": "Learn how to set up a skeleton for your character."}, "animationSetup": {"title": "Animation Setup", "description": "Learn how to set up animations for your character."}, "conclusion": {"title": "Conclusion", "description": "Congratulations! You have learned how to create a complete 3D character."}}}}}