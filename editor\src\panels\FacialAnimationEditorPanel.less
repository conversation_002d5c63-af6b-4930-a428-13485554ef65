/**
 * 面部动画编辑器面板样式
 */
.facial-animation-editor-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .ant-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .ant-tabs-content {
      flex: 1;
      height: 0;
      
      .ant-tabs-tabpane {
        height: 100%;
        overflow-y: auto;
      }
    }
  }
  
  .expressions-tab,
  .visemes-tab,
  .presets-tab {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .expression-controls,
    .viseme-controls {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      margin-bottom: 16px;
      padding: 16px;
      background-color: #f5f5f5;
      border-radius: 4px;
      
      .control-group {
        display: flex;
        align-items: center;
        gap: 8px;
        
        label {
          margin-bottom: 0;
          white-space: nowrap;
        }
      }
    }
    
    .blend-shape-editor-container,
    .viseme-editor-container {
      flex: 1;
      overflow: hidden;
      
      h3 {
        margin-bottom: 16px;
      }
    }
  }
  
  .presets-tab {
    padding: 0;
    
    .facial-animation-preset-panel {
      height: 100%;
    }
  }
}
