# 动作捕捉系统文档

## 概述

动作捕捉系统（Motion Capture System）是DL（Digital Learning）引擎的一个重要组成部分，用于处理动作捕捉数据并将其应用到角色骨骼上。该系统支持从摄像头或其他输入设备获取动作捕捉数据，解算姿势，评估姿势类型，并通过网络同步动作捕捉数据。

## 主要特性

- **动作捕捉数据处理**：处理来自MediaPipe Pose等模型的关键点数据
- **姿势解算**：将关键点数据转换为骨骼旋转
- **姿势评估**：识别站立、坐姿、T姿势、A姿势等姿势类型
- **网络同步**：支持多用户环境下的动作捕捉数据同步
- **平滑处理**：对动作捕捉数据进行平滑处理，减少抖动
- **可视化调试**：支持可视化调试动作捕捉数据和骨骼旋转

## 系统组件

动作捕捉系统由以下主要组件组成：

### 基础组件
1. **MotionCaptureSystem**：动作捕捉系统的核心，负责管理和协调所有动作捕捉组件
2. **MotionCaptureComponent**：存储动作捕捉数据的组件
3. **MotionCapturePoseComponent**：存储姿势状态的组件

### 工具类
1. **solveMotionCapturePose**：解算动作捕捉姿势的函数
2. **evaluatePose**：评估姿势的函数
3. **LandmarkData**：关键点数据类型
4. **LandmarkIndices**：关键点索引常量

## 使用方法

### 1. 初始化动作捕捉系统

首先，需要创建并初始化动作捕捉系统，并将其添加到世界中：

```typescript
// 创建动作捕捉系统
const motionCaptureSystem = new MotionCaptureSystem(world, {
  debug: true,                    // 是否启用调试模式
  enableNetworkSync: true,        // 是否启用网络同步
  enablePoseEvaluation: true,     // 是否启用姿势评估
  smoothingFactor: 0.5,           // 平滑系数
  visibilityThreshold: 0.1        // 可见度阈值
});

// 添加动作捕捉系统到世界
world.addSystem(motionCaptureSystem);
```

### 2. 创建角色实体

然后，创建角色实体并添加相关组件：

```typescript
// 创建角色实体
const avatarEntity = new Entity(world);
avatarEntity.name = '角色';

// 添加角色组件
const avatarComponent = new AvatarComponent(avatarEntity, {
  type: AvatarType.LOCAL_PLAYER,
  userId: 'local-user',
  name: '本地玩家',
  modelUrl: 'models/avatar.glb'
});
avatarEntity.addComponent(avatarComponent);

// 添加变换组件
const avatarTransform = new Transform({
  position: new THREE.Vector3(0, 0, 0),
  rotation: new THREE.Quaternion(),
  scale: new THREE.Vector3(1, 1, 1)
});
avatarEntity.addComponent(avatarTransform);

// 添加骨骼组件
const avatarRigComponent = new AvatarRigComponent(avatarEntity);
avatarEntity.addComponent(avatarRigComponent);

// 添加动作捕捉组件
const motionCaptureComponent = new MotionCaptureComponent(avatarEntity, {
  smoothingFactor: 0.5,
  visibilityThreshold: 0.1
});
avatarEntity.addComponent(motionCaptureComponent);

// 添加姿势组件
const motionCapturePoseComponent = new MotionCapturePoseComponent(avatarEntity, {
  poseHoldThreshold: 0.25,
  poseAngleThreshold: 1.25
});
avatarEntity.addComponent(motionCapturePoseComponent);

// 注册动作捕捉组件到系统
motionCaptureSystem.registerMotionCaptureComponent(avatarEntity, motionCaptureComponent);
motionCaptureSystem.registerPoseComponent(avatarEntity, motionCapturePoseComponent);

// 添加角色到场景
world.addEntity(avatarEntity);
```

### 3. 设置骨骼层级

接下来，设置角色的骨骼层级：

```typescript
// 创建骨骼实体
function createBoneEntity(boneName: VRMHumanBoneName, parent: Entity): Entity {
  const boneEntity = new Entity(world);
  boneEntity.name = `骨骼_${boneName}`;
  
  // 添加变换组件
  const boneTransform = new Transform({
    position: new THREE.Vector3(0, 0, 0),
    rotation: new THREE.Quaternion(),
    scale: new THREE.Vector3(1, 1, 1)
  });
  boneEntity.addComponent(boneTransform);
  
  // 设置父实体
  boneEntity.setParent(parent);
  
  // 注册到骨骼组件
  avatarRigComponent.setBoneEntity(boneName, boneEntity);
  
  // 创建骨骼对象
  const bone = new THREE.Bone();
  avatarRigComponent.setBoneObject(boneName, bone);
  
  return boneEntity;
}

// 创建骨骼层级
const hipsEntity = createBoneEntity(VRMHumanBoneName.Hips, avatarEntity);
const spineEntity = createBoneEntity(VRMHumanBoneName.Spine, hipsEntity);
const chestEntity = createBoneEntity(VRMHumanBoneName.Chest, spineEntity);
// ... 创建其他骨骼
```

### 4. 处理动作捕捉数据

最后，处理动作捕捉数据并应用到角色骨骼上：

```typescript
// 从摄像头或其他输入设备获取动作捕捉数据
function getMotionCaptureData(): { worldLandmarks: WorldLandmarkData[], landmarks: LandmarkData[] } {
  // 实现获取动作捕捉数据的逻辑
  // ...
  
  return { worldLandmarks, landmarks };
}

// 每帧更新动作捕捉数据
engine.onUpdate = (deltaTime: number) => {
  // 获取动作捕捉数据
  const { worldLandmarks, landmarks } = getMotionCaptureData();
  
  // 设置动作捕捉数据
  motionCaptureComponent.setWorldLandmarks(worldLandmarks);
  motionCaptureComponent.setLandmarks(landmarks);
  
  // 发送动作捕捉数据
  motionCaptureSystem.sendMotionCaptureResults({
    worldLandmarks,
    landmarks
  });
};
```

## 姿势评估

动作捕捉系统支持评估多种姿势类型，包括：

- **站立（STANDING）**：默认姿势
- **坐姿（SITTING）**：检测到角色坐下
- **T姿势（T_POSE）**：双臂水平伸展
- **A姿势（A_POSE）**：双臂向下45度伸展
- **行走（WALKING）**：检测到角色行走
- **跑步（RUNNING）**：检测到角色跑步
- **跳跃（JUMPING）**：检测到角色跳跃
- **蹲下（CROUCHING）**：检测到角色蹲下
- **趴下（PRONE）**：检测到角色趴下

可以通过`MotionCapturePoseComponent`获取当前姿势类型：

```typescript
// 获取当前姿势类型
const currentPose = motionCapturePoseComponent.currentPose;

// 检查是否是特定姿势
const isSitting = motionCapturePoseComponent.getPoseState(MotionCapturePoseType.SITTING)?.begun;
```

## 网络同步

动作捕捉系统支持通过网络同步动作捕捉数据，实现多用户环境下的动作捕捉：

```typescript
// 发送动作捕捉数据
motionCaptureSystem.sendMotionCaptureResults({
  worldLandmarks,
  landmarks
});
```

接收方会自动处理接收到的动作捕捉数据，并应用到对应的角色骨骼上。

## 调试

动作捕捉系统提供了多种调试功能，帮助开发者调试动作捕捉数据和骨骼旋转：

```typescript
// 启用调试模式
const motionCaptureSystem = new MotionCaptureSystem(world, {
  debug: true
});

// 获取调试信息
const debugInfo = {
  currentPose: motionCapturePoseComponent.currentPose,
  sittingState: motionCapturePoseComponent.getPoseState(MotionCapturePoseType.SITTING),
  solvingLowerBody: motionCaptureComponent.solvingLowerBody,
  footOffset: motionCaptureComponent.footOffset
};

console.log('动作捕捉调试信息:', debugInfo);
```

## 示例

完整的动作捕捉系统示例可以在以下文件中找到：
- `examples/mocap/MotionCaptureExample.ts`

这个示例展示了如何创建和使用动作捕捉系统，包括初始化系统、创建角色实体、设置骨骼层级、处理动作捕捉数据等功能。

## 高级用法

### 自定义姿势评估

可以通过继承`MotionCapturePoseComponent`或实现自定义组件来扩展姿势评估功能：

```typescript
// 自定义姿势组件
class CustomPoseComponent extends MotionCapturePoseComponent {
  constructor(entity: Entity, config: MotionCapturePoseComponentConfig = {}) {
    super(entity, config);
    // 自定义初始化
  }

  // 自定义姿势评估方法
  evaluateCustomPose(landmarks: WorldLandmarkData[]): boolean {
    // 实现自定义姿势评估逻辑
    // ...
    
    return isPose;
  }
}
```

### 与其他系统集成

动作捕捉系统可以与其他系统集成，例如动画系统、物理系统等：

```typescript
// 与动画系统集成
motionCapturePoseComponent.on('poseChanged', (oldPose, newPose) => {
  // 根据姿势类型播放不同的动画
  if (newPose === MotionCapturePoseType.SITTING) {
    animationSystem.playAnimation(avatarEntity, 'sit');
  } else if (newPose === MotionCapturePoseType.STANDING) {
    animationSystem.playAnimation(avatarEntity, 'stand');
  }
});

// 与物理系统集成
motionCapturePoseComponent.on('poseChanged', (oldPose, newPose) => {
  // 根据姿势类型调整物理属性
  if (newPose === MotionCapturePoseType.SITTING) {
    physicsSystem.setColliderHeight(avatarEntity, 1.0);
  } else if (newPose === MotionCapturePoseType.STANDING) {
    physicsSystem.setColliderHeight(avatarEntity, 1.8);
  }
});
```

## 性能优化

动作捕捉系统提供了多种性能优化选项：

1. **平滑系数**：调整平滑系数可以平衡响应速度和平滑度
2. **可见度阈值**：调整可见度阈值可以过滤掉低质量的关键点数据
3. **下半身解算**：可以根据关键点可见度自动决定是否解算下半身
4. **网络同步频率**：可以调整网络同步频率，减少网络流量

```typescript
// 优化配置
const motionCaptureSystem = new MotionCaptureSystem(world, {
  smoothingFactor: 0.3,           // 降低平滑系数，提高响应速度
  visibilityThreshold: 0.2,       // 提高可见度阈值，过滤更多低质量数据
  enablePoseEvaluation: false     // 禁用姿势评估，减少计算量
});
```

## 常见问题

### 1. 动作捕捉数据抖动

**问题**：动作捕捉数据出现抖动，导致角色动作不稳定。

**解决方案**：
- 增加平滑系数（`smoothingFactor`）
- 提高可见度阈值（`visibilityThreshold`）
- 使用更高质量的输入设备或改善环境光线条件

### 2. 姿势识别不准确

**问题**：系统无法正确识别某些姿势。

**解决方案**：
- 调整姿势角度阈值（`poseAngleThreshold`）
- 调整姿势持续阈值（`poseHoldThreshold`）
- 自定义姿势评估逻辑

### 3. 网络同步延迟

**问题**：多用户环境下动作捕捉数据同步延迟。

**解决方案**：
- 优化网络连接
- 减少同步数据量
- 实现预测算法，减少延迟影响
