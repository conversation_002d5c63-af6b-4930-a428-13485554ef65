/**
 * 版本历史面板组件
 */
import React, { useEffect, useState } from 'react';
import {
  Card,
  List,
  Button,
  Space,
  Divider,
  Typography,
  Tag,
  Tooltip,
  Modal,
  Empty,
  Popconfirm,
  Input,
  Form,
  Select,
  Switch,
  Tabs,
  Drawer,
  Timeline,
  Badge,
  Alert,
  Collapse,
  message
} from 'antd';
import {
  HistoryOutlined,
  RollbackOutlined,
  DeleteOutlined,
  CloseCircleOutlined,
  UserOutlined,
  ClockCircleOutlined,
  TagOutlined,
  InfoCircleOutlined,
  DiffOutlined,
  SettingOutlined,
  SaveOutlined,
  CompareOutlined,
  EyeOutlined,
  SyncOutlined,
  EditOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import {
  selectVersions,
  selectCurrentVersionId,
  selectShowVersionPanel,
  selectIsRollingBack,
  setShowVersionPanel,
  setIsRollingBack,
  deleteVersion
} from '../../store/collaboration/versionSlice';
import {
  Version,
  versionHistoryService
} from '../../services/VersionHistoryService';
import SceneVersionComparePanel from './SceneVersionComparePanel';
import './VersionHistoryPanel.less';

const { Title, Text, Paragraph } = Typography;

// 格式化时间
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleString();
};

/**
 * 版本历史面板组件
 */
const VersionHistoryPanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 从Redux获取状态
  const versions = useSelector(selectVersions);
  const currentVersionId = useSelector(selectCurrentVersionId);
  const showVersionPanel = useSelector(selectShowVersionPanel);
  const isRollingBack = useSelector(selectIsRollingBack);

  // 本地状态
  const [selectedVersionIds, setSelectedVersionIds] = useState<string[]>([]);
  const [showComparePanel, setShowComparePanel] = useState<boolean>(false);
  const [comparisonResult, setComparisonResult] = useState<any>(null);
  const [editingVersionId, setEditingVersionId] = useState<string | null>(null);
  const [editingDescription, setEditingDescription] = useState<string>('');
  const [newTagValue, setNewTagValue] = useState<string>('');
  const [newTagVersionId, setNewTagVersionId] = useState<string | null>(null);

  // 初始化版本历史服务
  useEffect(() => {
    versionHistoryService.initialize();
  }, []);

  // 处理关闭面板
  const handleClose = () => {
    dispatch(setShowVersionPanel(false));
  };

  // 处理回滚到版本
  const handleRollback = (versionId: string, options?: any) => {
    dispatch(setIsRollingBack(true));

    // 回滚到指定版本
    versionHistoryService.rollbackToVersion(versionId, options);

    dispatch(setIsRollingBack(false));
  };

  // 处理删除版本
  const handleDeleteVersion = (versionId: string) => {
    dispatch(deleteVersion(versionId));
  };

  // 处理创建版本
  const handleCreateVersion = () => {
    // 显示创建版本对话框
    Modal.confirm({
      title: '创建版本',
      content: (
        <div>
          <Paragraph>
            创建一个新的版本快照，您可以随时回滚到此版本。
          </Paragraph>
          <Paragraph>
            <Text strong>描述：</Text>
            <input
              id="version-description"
              type="text"
              style={{ width: '100%', marginTop: 8 }}
              placeholder="输入版本描述"
            />
          </Paragraph>
          <Paragraph>
            <Text strong>标签：</Text>
            <input
              id="version-tags"
              type="text"
              style={{ width: '100%', marginTop: 8 }}
              placeholder="输入标签，用逗号分隔"
            />
          </Paragraph>
        </div>
      ),
      onOk: () => {
        const descriptionInput = document.getElementById('version-description') as HTMLInputElement;
        const tagsInput = document.getElementById('version-tags') as HTMLInputElement;
        const description = descriptionInput?.value || '手动创建的版本';
        const tags = tagsInput?.value ? tagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

        // 创建版本
        versionHistoryService.createVersion(description, tags);
      }
    });
  };

  // 处理版本选择
  const handleVersionSelect = (versionId: string) => {
    const newSelectedIds = [...selectedVersionIds];
    const index = newSelectedIds.indexOf(versionId);

    if (index >= 0) {
      // 如果已选中，则取消选中
      newSelectedIds.splice(index, 1);
    } else {
      // 如果未选中，则添加到选中列表
      // 最多选择两个版本
      if (newSelectedIds.length >= 2) {
        newSelectedIds.shift();
      }
      newSelectedIds.push(versionId);
    }

    setSelectedVersionIds(newSelectedIds);
  };

  // 处理比较版本
  const handleCompareVersions = () => {
    if (selectedVersionIds.length !== 2) {
      return;
    }

    // 比较版本
    const result = versionHistoryService.compareVersions(selectedVersionIds[0], selectedVersionIds[1]);

    if (result) {
      setComparisonResult(result);
      setShowComparePanel(true);
    } else {
      message.error('比较版本失败');
    }
  };

  // 处理关闭比较面板
  const handleCloseComparePanel = () => {
    setShowComparePanel(false);
    setComparisonResult(null);
  };

  // 处理编辑描述
  const handleEditDescription = (version: Version) => {
    setEditingVersionId(version.id);
    setEditingDescription(version.description);
  };

  // 处理保存描述
  const handleSaveDescription = () => {
    if (editingVersionId && editingDescription) {
      // 更新版本描述
      // 这里需要实现版本描述更新功能
      setEditingVersionId(null);
      setEditingDescription('');
    }
  };

  // 处理取消编辑
  const handleCancelEdit = () => {
    setEditingVersionId(null);
    setEditingDescription('');
  };

  // 渲染版本列表项
  const renderVersionItem = (version: Version) => {
    const isCurrent = version.id === currentVersionId;
    const isSelected = selectedVersionIds.includes(version.id);
    const isEditing = editingVersionId === version.id;

    return (
      <List.Item
        key={version.id}
        className={`version-item ${isCurrent ? 'current' : ''} ${isSelected ? 'selected' : ''}`}
        onClick={() => handleVersionSelect(version.id)}
        actions={[
          <Tooltip title="比较" key="compare">
            <Button
              icon={<CompareOutlined />}
              size="small"
              type={isSelected ? 'primary' : 'default'}
              onClick={(e) => {
                e.stopPropagation();
                handleVersionSelect(version.id);
              }}
            />
          </Tooltip>,
          <Tooltip title="回滚到此版本" key="rollback">
            <Button
              icon={<RollbackOutlined />}
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                handleRollback(version.id);
              }}
              loading={isRollingBack && version.id === currentVersionId}
              disabled={isCurrent}
            />
          </Tooltip>,
          <Tooltip title="删除此版本" key="delete">
            <Popconfirm
              title="确定要删除此版本吗？"
              onConfirm={(e) => {
                e.stopPropagation();
                handleDeleteVersion(version.id);
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
                disabled={isCurrent}
                onClick={(e) => e.stopPropagation()}
              />
            </Popconfirm>
          </Tooltip>
        ]}
      >
        <List.Item.Meta
          title={
            <Space>
              {isEditing ? (
                <Input
                  value={editingDescription}
                  onChange={(e) => setEditingDescription(e.target.value)}
                  onPressEnter={handleSaveDescription}
                  autoFocus
                  onClick={(e) => e.stopPropagation()}
                  addonAfter={
                    <Space>
                      <Button size="small" type="link" onClick={handleSaveDescription}>保存</Button>
                      <Button size="small" type="link" onClick={handleCancelEdit}>取消</Button>
                    </Space>
                  }
                />
              ) : (
                <>
                  <Text strong>{version.description}</Text>
                  <Button
                    icon={<EditOutlined />}
                    size="small"
                    type="text"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditDescription(version);
                    }}
                  />
                </>
              )}
              {isCurrent && <Tag color="success">当前版本</Tag>}
            </Space>
          }
          description={
            <div onClick={(e) => e.stopPropagation()}>
              <div>
                <ClockCircleOutlined /> {formatTime(version.timestamp)}
              </div>
              <div>
                <UserOutlined /> {version.userName}
              </div>
              {version.tags && version.tags.length > 0 && (
                <div>
                  <TagOutlined />
                  <Space size={[0, 4]} wrap>
                    {version.tags.map(tag => (
                      <Tag key={tag}>{tag}</Tag>
                    ))}
                  </Space>
                </div>
              )}
            </div>
          }
        />
      </List.Item>
    );
  };

  if (!showVersionPanel) {
    return null;
  }

  return (
    <div className="version-history-panel">
      {showComparePanel && comparisonResult ? (
        <SceneVersionComparePanel
          version1={versions.find(v => v.id === selectedVersionIds[0])!}
          version2={versions.find(v => v.id === selectedVersionIds[1])!}
          comparisonResult={comparisonResult}
          onClose={handleCloseComparePanel}
          onRollbackVersion={handleRollback}
        />
      ) : (
        <Card
          title={
            <Space>
              <HistoryOutlined />
              <span>版本历史</span>
            </Space>
          }
          extra={
            <Space>
              {selectedVersionIds.length === 2 && (
                <Button
                  type="primary"
                  icon={<CompareOutlined />}
                  onClick={handleCompareVersions}
                >
                  比较版本
                </Button>
              )}
              <Button
                type="primary"
                icon={<HistoryOutlined />}
                onClick={handleCreateVersion}
              >
                创建版本
              </Button>
              <Button
                type="text"
                icon={<CloseCircleOutlined />}
                onClick={handleClose}
              />
            </Space>
          }
          className="version-card"
        >
          {versions.length > 0 ? (
            <List
              className="version-list"
              dataSource={versions.sort((a, b) => b.timestamp - a.timestamp)}
              renderItem={renderVersionItem}
            />
          ) : (
            <Empty
              description="没有版本历史记录"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}

          <Divider />

          <div className="version-info">
            <Space>
              <InfoCircleOutlined />
              <Text type="secondary">
                版本历史允许您保存场景的快照，并在需要时回滚到之前的版本。
                {selectedVersionIds.length === 1 ? (
                  <span> 再选择一个版本进行比较。</span>
                ) : selectedVersionIds.length === 2 ? (
                  <span> 点击"比较版本"按钮比较选中的两个版本。</span>
                ) : (
                  <span> 选择两个版本进行比较。</span>
                )}
              </Text>
            </Space>
          </div>
        </Card>
      )}
    </div>
  );
};

export default VersionHistoryPanel;
