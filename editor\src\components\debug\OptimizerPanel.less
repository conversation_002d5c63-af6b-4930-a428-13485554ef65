.optimizer-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .optimizer-toolbar {
    padding: 12px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .optimization-progress {
    padding: 8px 12px;
    background-color: #f0f8ff;
  }
  
  .optimizer-content {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
  }
  
  .optimization-score {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    
    .ant-progress {
      margin: 16px 0;
    }
    
    .optimization-score-text {
      margin-top: 16px;
      width: 100%;
      max-width: 400px;
    }
  }
  
  .optimization-item-header {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .optimization-item-title {
      margin-left: 8px;
      font-weight: 500;
    }
  }
  
  .optimization-item-content {
    padding: 8px 16px;
    
    .ant-typography {
      margin-bottom: 8px;
    }
  }
  
  .optimization-tips {
    list-style: none;
    padding-left: 0;
    margin-bottom: 16px;
    
    li {
      margin-bottom: 8px;
      display: flex;
      align-items: flex-start;
      
      .optimization-tip-icon {
        margin-right: 8px;
        margin-top: 4px;
        color: #1890ff;
      }
    }
  }
  
  .optimization-warning {
    color: #faad14;
  }
  
  .optimization-error {
    color: #f5222d;
  }
  
  .optimization-good {
    color: #52c41a;
  }
  
  .analyzing-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 0;
    
    .analyzing-text {
      margin-top: 16px;
      font-size: 16px;
      color: #1890ff;
    }
  }
}

// 暗色主题样式
.dark-theme {
  .optimizer-panel {
    .optimizer-toolbar {
      background-color: #1e1e1e;
      border-bottom: 1px solid #303030;
    }
    
    .optimization-progress {
      background-color: #1a2a3a;
    }
  }
}
