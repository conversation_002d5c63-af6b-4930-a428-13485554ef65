/**
 * 高级混合示例
 * 演示如何使用高级混合功能，包括遮罩、子片段和物理/输入集成
 */
import * as THREE from 'three';
import { Engine } from '../../src/core/Engine';
import { World } from '../../src/core/World';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Camera } from '../../src/rendering/Camera';
import { Renderer } from '../../src/rendering/Renderer';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { AnimationClip } from '../../src/animation/AnimationClip';
import { Animator } from '../../src/animation/Animator';
import { AnimationBlender, BlendMode } from '../../src/animation/AnimationBlender';
import { AnimationMask, MaskType, MaskWeightType } from '../../src/animation/AnimationMask';
import { SubClip } from '../../src/animation/SubClip';
import { PhysicsSystem } from '../../src/physics/PhysicsSystem';
import { PhysicsBodyComponent } from '../../src/physics/components/PhysicsBodyComponent';
import { PhysicsColliderComponent } from '../../src/physics/components/PhysicsColliderComponent';
import { InputSystem } from '../../src/input/InputSystem';
import { InputComponent } from '../../src/input/components/InputComponent';
import { ButtonInputAction } from '../../src/input/InputAction';
import { ButtonInputMapping } from '../../src/input/InputMapping';
import { InputBinding } from '../../src/input/InputBinding';

/**
 * 高级混合示例
 */
export class AdvancedBlendingExample {
  /** 引擎 */
  private engine: Engine;
  /** 世界 */
  private world: World;
  /** 场景 */
  private scene: Scene;
  /** 相机 */
  private camera: Camera;
  /** 渲染器 */
  private renderer: Renderer;
  /** 控制器 */
  private controls: OrbitControls;
  /** 角色实体 */
  private character: Entity;
  /** 动画控制器 */
  private animator: Animator;
  /** 动画混合器 */
  private blender: AnimationBlender;
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  /** 输入系统 */
  private inputSystem: InputSystem;
  /** 是否已初始化 */
  private initialized: boolean = false;
  /** 动画片段映射 */
  private clips: Map<string, AnimationClip> = new Map();
  /** 子片段映射 */
  private subClips: Map<string, string> = new Map();
  /** 遮罩映射 */
  private masks: Map<string, AnimationMask> = new Map();
  /** 当前动画 */
  private currentAnimation: string = 'idle';
  /** 是否正在混合 */
  private isBlending: boolean = false;

  /**
   * 构造函数
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();

    // 创建世界
    this.world = new World();
    this.engine.setWorld(this.world);

    // 创建场景
    this.scene = new Scene();
    this.world.addEntity(this.scene);

    // 创建相机
    this.camera = new Camera({
      type: 'perspective',
      fov: 75,
      aspect: window.innerWidth / window.innerHeight,
      near: 0.1,
      far: 1000
    });
    this.camera.position.set(0, 2, 5);
    this.camera.lookAt(0, 1, 0);
    this.world.addEntity(this.camera);

    // 创建渲染器
    this.renderer = new Renderer({
      width: window.innerWidth,
      height: window.innerHeight,
      antialias: true
    });
    document.body.appendChild(this.renderer.getDomElement());

    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.getDomElement());
    this.controls.target.set(0, 1, 0);
    this.controls.update();

    // 创建物理系统
    this.physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.82, z: 0 },
      debug: true
    });
    this.world.addSystem(this.physicsSystem);

    // 创建输入系统
    this.inputSystem = new InputSystem();
    this.world.addSystem(this.inputSystem);

    // 创建角色实体
    this.character = new Entity();
    this.character.name = '角色';

    // 添加物理组件
    this.character.addComponent(new PhysicsBodyComponent({
      type: 'dynamic',
      mass: 70,
      fixedRotation: true
    }));
    this.character.addComponent(new PhysicsColliderComponent({
      type: 'capsule',
      radius: 0.3,
      height: 1.8
    }));

    // 添加输入组件
    const inputComponent = new InputComponent();

    // 添加动作
    const jumpAction = new ButtonInputAction('jump');
    const attackAction = new ButtonInputAction('attack');
    const danceAction = new ButtonInputAction('dance');

    // 添加映射
    const jumpMapping = new ButtonInputMapping('jumpKey', 'keyboard', 'Space');
    const attackMapping = new ButtonInputMapping('attackKey', 'keyboard', 'KeyF');
    const danceMapping = new ButtonInputMapping('danceKey', 'keyboard', 'KeyG');

    // 添加绑定
    const jumpBinding = new InputBinding('jump', 'jumpKey');
    const attackBinding = new InputBinding('attack', 'attackKey');
    const danceBinding = new InputBinding('dance', 'danceKey');

    // 设置输入组件
    inputComponent.addAction(jumpAction);
    inputComponent.addAction(attackAction);
    inputComponent.addAction(danceAction);
    inputComponent.addMapping(jumpMapping);
    inputComponent.addMapping(attackMapping);
    inputComponent.addMapping(danceMapping);
    inputComponent.addBinding(jumpBinding);
    inputComponent.addBinding(attackBinding);
    inputComponent.addBinding(danceBinding);

    this.character.addComponent(inputComponent);

    // 添加角色到场景
    this.world.addEntity(this.character);

    // 添加灯光
    const light = new Entity();
    light.name = '灯光';
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(1, 2, 3);
    light.setObject3D(directionalLight);
    this.world.addEntity(light);

    // 添加环境光
    const ambientLight = new Entity();
    ambientLight.name = '环境光';
    const ambient = new THREE.AmbientLight(0x404040, 1);
    ambientLight.setObject3D(ambient);
    this.world.addEntity(ambientLight);

    // 添加地面
    const ground = new Entity();
    ground.name = '地面';
    const groundMesh = new THREE.Mesh(
      new THREE.PlaneGeometry(10, 10),
      new THREE.MeshStandardMaterial({ color: 0x808080 })
    );
    groundMesh.rotation.x = -Math.PI / 2;
    ground.setObject3D(groundMesh);
    this.world.addEntity(ground);
  }

  /**
   * 初始化
   */
  public async initialize(): Promise<void> {
    if (this.initialized) return;

    // 加载角色模型
    await this.loadCharacterModel();

    // 创建动画控制器
    this.animator = new Animator(this.character);

    // 加载动画
    await this.loadAnimations();

    // 创建动画混合器
    this.blender = new AnimationBlender(this.animator);

    // 创建遮罩
    this.createMasks();

    // 创建子片段
    this.createSubClips();

    // 集成物理系统
    this.blender.integratePhysics(this.character, this.physicsSystem, {
      debug: true,
      autoUpdateParameters: true
    });

    // 集成输入系统
    this.blender.integrateInput(this.character, this.inputSystem, {
      debug: true,
      autoUpdateParameters: true
    });

    // 添加输入事件监听器
    this.setupInputListeners();

    // 播放默认动画
    this.playAnimation('idle');

    this.initialized = true;
  }

  /**
   * 加载角色模型
   */
  private async loadCharacterModel(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      const loader = new GLTFLoader();

      loader.load(
        'assets/models/character.glb',
        (gltf) => {
          // 设置模型
          this.character.setObject3D(gltf.scene);

          // 设置位置
          this.character.position.set(0, 0, 0);

          resolve();
        },
        undefined,
        (error) => {
          console.error('加载角色模型失败:', error);
          reject(error);
        }
      );
    });
  }

  /**
   * 加载动画
   */
  private async loadAnimations(): Promise<void> {
    // 加载动画文件
    const animations = await this.loadAnimationFiles();

    // 添加到动画控制器
    for (const [name, clip] of animations) {
      this.animator.addClip(clip);
      this.clips.set(name, clip);
    }
  }

  /**
   * 加载动画文件
   */
  private async loadAnimationFiles(): Promise<Map<string, AnimationClip>> {
    const animations = new Map<string, AnimationClip>();

    // 加载动画文件
    const animationFiles = [
      { name: 'idle', url: 'assets/animations/idle.glb' },
      { name: 'walk', url: 'assets/animations/walk.glb' },
      { name: 'run', url: 'assets/animations/run.glb' },
      { name: 'jump', url: 'assets/animations/jump.glb' },
      { name: 'attack', url: 'assets/animations/attack.glb' },
      { name: 'dance', url: 'assets/animations/dance.glb' }
    ];

    // 加载每个动画文件
    for (const file of animationFiles) {
      try {
        const clip = await this.loadAnimationClip(file.url);
        clip.name = file.name;
        animations.set(file.name, clip);
      } catch (error) {
        console.error(`加载动画 ${file.name} 失败:`, error);
      }
    }

    return animations;
  }

  /**
   * 加载动画片段
   * @param url 动画文件URL
   */
  private async loadAnimationClip(url: string): Promise<AnimationClip> {
    return new Promise<AnimationClip>((resolve, reject) => {
      const loader = new GLTFLoader();

      loader.load(
        url,
        (gltf) => {
          if (gltf.animations && gltf.animations.length > 0) {
            resolve(gltf.animations[0]);
          } else {
            reject(new Error('动画文件不包含动画'));
          }
        },
        undefined,
        (error) => {
          console.error('加载动画文件失败:', error);
          reject(error);
        }
      );
    });
  }

  /**
   * 创建遮罩
   */
  private createMasks(): void {
    // 创建上半身遮罩
    const upperBodyMask = this.blender.createUpperBodyMask();
    this.masks.set('upperBody', upperBodyMask);

    // 创建下半身遮罩
    const lowerBodyMask = this.blender.createLowerBodyMask();
    this.masks.set('lowerBody', lowerBodyMask);

    // 创建左手遮罩
    const leftHandMask = this.blender.createLeftHandMask();
    this.masks.set('leftHand', leftHandMask);

    // 创建右手遮罩
    const rightHandMask = this.blender.createRightHandMask();
    this.masks.set('rightHand', rightHandMask);

    // 创建自定义遮罩
    const customMask = this.blender.createMask(
      'custom',
      MaskType.INCLUDE,
      ['spine', 'spine1', 'spine2', 'rightShoulder', 'rightArm', 'rightForeArm', 'rightHand'],
      MaskWeightType.SMOOTH
    );
    this.masks.set('custom', customMask);

    console.log('创建了遮罩:', Array.from(this.masks.keys()));
  }

  /**
   * 创建子片段
   */
  private createSubClips(): void {
    // 创建跳跃子片段
    if (this.clips.has('jump')) {
      const jumpClip = this.clips.get('jump')!;

      // 创建起跳子片段
      const jumpStartName = this.blender.createSubClip(
        'jump_start',
        'jump',
        0,
        0.5,
        false
      );
      this.subClips.set('jumpStart', jumpStartName);

      // 创建空中子片段
      const jumpAirName = this.blender.createSubClip(
        'jump_air',
        'jump',
        0.5,
        1.0,
        true
      );
      this.subClips.set('jumpAir', jumpAirName);

      // 创建落地子片段
      const jumpLandName = this.blender.createSubClip(
        'jump_land',
        'jump',
        1.0,
        jumpClip.duration,
        false
      );
      this.subClips.set('jumpLand', jumpLandName);
    }

    // 创建攻击子片段
    if (this.clips.has('attack')) {
      const attackClip = this.clips.get('attack')!;

      // 创建攻击准备子片段
      const attackPrepName = this.blender.createSubClip(
        'attack_prep',
        'attack',
        0,
        0.3,
        false
      );
      this.subClips.set('attackPrep', attackPrepName);

      // 创建攻击执行子片段
      const attackExecName = this.blender.createSubClip(
        'attack_exec',
        'attack',
        0.3,
        0.6,
        false
      );
      this.subClips.set('attackExec', attackExecName);

      // 创建攻击恢复子片段
      const attackRecovName = this.blender.createSubClip(
        'attack_recov',
        'attack',
        0.6,
        attackClip.duration,
        false
      );
      this.subClips.set('attackRecov', attackRecovName);
    }

    // 创建舞蹈循环子片段
    if (this.clips.has('dance')) {
      const danceClip = this.clips.get('dance')!;

      // 创建舞蹈入场子片段
      const danceEntryName = this.blender.createEntrySubClip(
        'dance',
        1.0
      );
      this.subClips.set('danceEntry', danceEntryName);

      // 创建舞蹈循环子片段
      const danceLoopName = this.blender.createLoopSubClip(
        'dance',
        1.0,
        danceClip.duration - 1.0
      );
      this.subClips.set('danceLoop', danceLoopName);

      // 创建舞蹈退场子片段
      const danceExitName = this.blender.createExitSubClip(
        'dance',
        danceClip.duration - 1.0
      );
      this.subClips.set('danceExit', danceExitName);
    }

    console.log('创建了子片段:', Array.from(this.subClips.keys()));
  }

  /**
   * 设置输入监听器
   */
  private setupInputListeners(): void {
    // 获取输入组件
    const inputComponent = this.character.getComponent<InputComponent>(InputComponent.type);
    if (!inputComponent) return;

    // 获取动作
    const jumpAction = inputComponent.getAction('jump');
    const attackAction = inputComponent.getAction('attack');
    const danceAction = inputComponent.getAction('dance');

    // 添加跳跃监听器
    if (jumpAction) {
      jumpAction.addEventListener('started', () => {
        this.playAnimation('jumpStart');
      });
    }

    // 添加攻击监听器
    if (attackAction) {
      attackAction.addEventListener('started', () => {
        // 使用上半身遮罩播放攻击动画
        this.playAnimationWithMask('attackExec', 'upperBody');
      });
    }

    // 添加舞蹈监听器
    if (danceAction) {
      danceAction.addEventListener('started', () => {
        this.playAnimation('danceLoop');
      });
    }

    // 添加键盘监听器
    window.addEventListener('keydown', (event) => {
      switch (event.code) {
        case 'KeyW':
          this.playAnimation('walk');
          break;
        case 'KeyR':
          this.playAnimation('run');
          break;
        case 'KeyI':
          this.playAnimation('idle');
          break;
        case 'Digit1':
          this.playAnimationWithMask('attack', 'upperBody');
          break;
        case 'Digit2':
          this.playAnimationWithMask('attack', 'rightHand');
          break;
        case 'Digit3':
          this.playAnimationWithMask('dance', 'upperBody');
          break;
        case 'Digit4':
          this.playAnimationWithMask('dance', 'lowerBody');
          break;
      }
    });
  }

  /**
   * 播放动画
   * @param name 动画名称
   * @param fadeTime 淡入时间
   */
  private playAnimation(name: string, fadeTime: number = 0.3): void {
    if (this.isBlending) return;

    // 检查动画是否存在
    if (!this.animator.hasClip(name)) {
      console.warn(`动画 "${name}" 不存在`);
      return;
    }

    // 设置当前动画
    this.currentAnimation = name;

    // 播放动画
    this.blender.clearLayers();
    this.blender.addLayer(name, 1.0, BlendMode.OVERRIDE, fadeTime);

    console.log(`播放动画: ${name}`);
  }

  /**
   * 使用遮罩播放动画
   * @param name 动画名称
   * @param maskName 遮罩名称
   * @param fadeTime 淡入时间
   */
  private playAnimationWithMask(name: string, maskName: string, fadeTime: number = 0.3): void {
    // 检查动画是否存在
    if (!this.animator.hasClip(name)) {
      console.warn(`动画 "${name}" 不存在`);
      return;
    }

    // 检查遮罩是否存在
    if (!this.masks.has(maskName)) {
      console.warn(`遮罩 "${maskName}" 不存在`);
      return;
    }

    // 应用遮罩
    const maskedName = `${name}_${maskName}`;

    // 如果已经应用过遮罩，直接使用
    if (this.animator.hasClip(maskedName)) {
      this.blender.addLayer(maskedName, 1.0, BlendMode.ADDITIVE, fadeTime);
    } else {
      // 复制动画
      const clip = this.animator.getClip(name)!.clone();
      clip.name = maskedName;
      this.animator.addClip(clip);

      // 应用遮罩
      this.blender.applyMaskToClip(maskedName, maskName);

      // 添加层
      this.blender.addLayer(maskedName, 1.0, BlendMode.ADDITIVE, fadeTime);
    }

    console.log(`使用遮罩 ${maskName} 播放动画: ${name}`);
  }

  /**
   * 更新
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized) return;

    // 更新动画
    this.animator.update(deltaTime);

    // 更新物理集成
    const physicsIntegration = this.blender.getPhysicsIntegration();
    if (physicsIntegration) {
      physicsIntegration.update(deltaTime);
    }

    // 更新输入集成
    const inputIntegration = this.blender.getInputIntegration();
    if (inputIntegration) {
      inputIntegration.update(deltaTime);
    }

    // 更新控制器
    this.controls.update();
  }

  /**
   * 渲染
   */
  public render(): void {
    if (!this.initialized) return;

    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }

  /**
   * 运行
   */
  public async run(): Promise<void> {
    // 初始化
    await this.initialize();

    // 渲染循环
    const animate = () => {
      requestAnimationFrame(animate);

      // 计算帧间隔时间
      const deltaTime = this.engine.getDeltaTime();

      // 更新
      this.update(deltaTime);

      // 渲染
      this.render();
    };

    animate();
  }
}
