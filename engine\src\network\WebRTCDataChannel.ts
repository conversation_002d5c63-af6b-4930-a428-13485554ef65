/**
 * WebRTC数据通道
 * 用于WebRTC点对点数据传输
 */

export interface WebRTCDataChannelOptions {
  /** 通道标签 */
  label: string;
  /** 是否有序 */
  ordered?: boolean;
  /** 最大重传次数 */
  maxRetransmits?: number;
  /** 最大重传时间 */
  maxRetransmitTime?: number;
  /** 协议 */
  protocol?: string;
  /** 是否协商 */
  negotiated?: boolean;
  /** 通道ID */
  id?: number;
}

export class WebRTCDataChannel {
  private channel: RTCDataChannel;
  private options: WebRTCDataChannelOptions;

  constructor(channel: RTCDataChannel, options: WebRTCDataChannelOptions) {
    this.channel = channel;
    this.options = options;
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.channel.onopen = () => {
      console.log(`数据通道 ${this.options.label} 已打开`);
    };

    this.channel.onclose = () => {
      console.log(`数据通道 ${this.options.label} 已关闭`);
    };

    this.channel.onerror = (error) => {
      console.error(`数据通道 ${this.options.label} 错误:`, error);
    };

    this.channel.onmessage = (event) => {
      this.handleMessage(event.data);
    };
  }

  private handleMessage(data: any): void {
    // 处理接收到的消息
    console.log(`收到消息:`, data);
  }

  public send(data: string | ArrayBuffer | Blob): void {
    if (this.channel.readyState === 'open') {
      this.channel.send(data);
    } else {
      console.warn('数据通道未打开，无法发送消息');
    }
  }

  public close(): void {
    this.channel.close();
  }

  public get readyState(): RTCDataChannelState {
    return this.channel.readyState;
  }

  public get label(): string {
    return this.channel.label;
  }
}
