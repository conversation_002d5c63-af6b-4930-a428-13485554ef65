/**
 * 增强资源版本管理器
 * 负责管理资源的版本控制、比较和回滚功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { AssetType } from './ResourceManager';

/**
 * 资源版本接口
 */
export interface ResourceVersion {
  /** 版本ID */
  id: string;
  /** 资源ID */
  resourceId: string;
  /** 版本号 */
  version: number;
  /** 创建时间戳 */
  timestamp: number;
  /** 资源URL */
  url: string;
  /** 资源类型 */
  type: AssetType;
  /** 资源哈希 */
  hash: string;
  /** 资源大小（字节） */
  size: number;
  /** 资源元数据 */
  metadata?: Record<string, any>;
  /** 版本描述 */
  description: string;
  /** 创建用户ID */
  userId: string;
  /** 创建用户名 */
  userName: string;
  /** 版本标签 */
  tags?: string[];
}

/**
 * 版本比较结果接口
 */
export interface VersionComparisonResult {
  /** 版本1 */
  version1: ResourceVersion;
  /** 版本2 */
  version2: ResourceVersion;
  /** 是否有差异 */
  hasDifferences: boolean;
  /** 差异类型 */
  differenceType: 'content' | 'metadata' | 'both' | 'none';
  /** 内容差异（取决于资源类型） */
  contentDifferences?: any;
  /** 元数据差异 */
  metadataDifferences?: {
    added: Record<string, any>;
    removed: Record<string, any>;
    modified: Record<string, { from: any; to: any }>;
  };
  /** 比较时间戳 */
  comparisonTimestamp: number;
}

/**
 * 增强资源版本管理器选项
 */
export interface EnhancedResourceVersionManagerOptions {
  /** 是否启用版本控制 */
  enabled?: boolean;
  /** 最大版本数量（每个资源） */
  maxVersionsPerResource?: number;
  /** 是否自动创建初始版本 */
  autoCreateInitialVersion?: boolean;
  /** 是否保存完整资源副本 */
  saveFullCopy?: boolean;
  /** 是否启用版本比较 */
  enableComparison?: boolean;
  /** 是否启用版本标记 */
  enableTags?: boolean;
  /** 是否启用调试模式 */
  debug?: boolean;
}

/**
 * 增强资源版本管理器事件类型
 */
export enum ResourceVersionEventType {
  VERSION_CREATED = 'versionCreated',
  VERSION_DELETED = 'versionDeleted',
  VERSION_ROLLBACK = 'versionRollback',
  VERSION_TAGGED = 'versionTagged',
  VERSION_COMPARED = 'versionCompared',
}

/**
 * 增强资源版本管理器
 * 负责管理资源的版本控制、比较和回滚功能
 */
export class EnhancedResourceVersionManager extends EventEmitter {
  /** 是否启用版本控制 */
  private enabled: boolean;

  /** 最大版本数量（每个资源） */
  private maxVersionsPerResource: number;

  /** 是否自动创建初始版本 */
  private autoCreateInitialVersion: boolean;

  /** 是否保存完整资源副本 */
  private saveFullCopy: boolean;

  /** 是否启用版本比较 */
  private enableComparison: boolean;

  /** 是否启用版本标记 */
  private enableTags: boolean;

  /** 是否启用调试模式 */
  private debug: boolean;

  /** 资源版本映射（resourceId -> 版本数组） */
  private resourceVersions: Map<string, ResourceVersion[]> = new Map();

  /** 最近比较结果 */
  private lastComparisonResult: VersionComparisonResult | null = null;

  /** 单例实例 */
  private static instance: EnhancedResourceVersionManager;

  /**
   * 获取单例实例
   * @param options 选项
   * @returns 单例实例
   */
  public static getInstance(options?: EnhancedResourceVersionManagerOptions): EnhancedResourceVersionManager {
    if (!EnhancedResourceVersionManager.instance) {
      EnhancedResourceVersionManager.instance = new EnhancedResourceVersionManager(options);
    }
    return EnhancedResourceVersionManager.instance;
  }

  /**
   * 创建增强资源版本管理器实例
   * @param options 选项
   */
  constructor(options: EnhancedResourceVersionManagerOptions = {}) {
    super();

    // 设置选项
    this.enabled = options.enabled !== undefined ? options.enabled : true;
    this.maxVersionsPerResource = options.maxVersionsPerResource || 10;
    this.autoCreateInitialVersion = options.autoCreateInitialVersion !== undefined ? options.autoCreateInitialVersion : true;
    this.saveFullCopy = options.saveFullCopy !== undefined ? options.saveFullCopy : true;
    this.enableComparison = options.enableComparison !== undefined ? options.enableComparison : true;
    this.enableTags = options.enableTags !== undefined ? options.enableTags : true;
    this.debug = options.debug !== undefined ? options.debug : false;

    if (this.debug) {
      console.log('[ResourceVersionManager] 初始化完成');
    }
  }

  /**
   * 创建资源版本
   * @param resourceId 资源ID
   * @param url 资源URL
   * @param type 资源类型
   * @param hash 资源哈希
   * @param size 资源大小
   * @param metadata 资源元数据
   * @param description 版本描述
   * @param userId 用户ID
   * @param userName 用户名
   * @param tags 标签
   * @returns 创建的版本
   */
  public createVersion(
    resourceId: string,
    url: string,
    type: AssetType,
    hash: string,
    size: number,
    metadata: Record<string, any> = {},
    description: string = '创建新版本',
    userId: string = 'system',
    userName: string = '系统',
    tags: string[] = []
  ): ResourceVersion {
    if (!this.enabled) {
      throw new Error('版本控制未启用');
    }

    // 获取资源的版本列表
    let versions = this.resourceVersions.get(resourceId) || [];

    // 计算新版本号
    const version = versions.length > 0 ? Math.max(...versions.map(v => v.version)) + 1 : 1;

    // 创建新版本
    const newVersion: ResourceVersion = {
      id: this.generateId(),
      resourceId,
      version,
      timestamp: Date.now(),
      url,
      type,
      hash,
      size,
      metadata,
      description,
      userId,
      userName,
      tags: this.enableTags ? tags : []
    };

    // 添加到版本列表
    versions.push(newVersion);

    // 限制版本数量
    if (versions.length > this.maxVersionsPerResource) {
      versions = versions.sort((a, b) => b.timestamp - a.timestamp).slice(0, this.maxVersionsPerResource);
    }

    // 更新资源版本映射
    this.resourceVersions.set(resourceId, versions);

    // 发出版本创建事件
    this.emit(ResourceVersionEventType.VERSION_CREATED, newVersion);

    if (this.debug) {
      console.log(`[ResourceVersionManager] 创建版本: ${resourceId}, 版本号: ${version}`);
    }

    return newVersion;
  }

  /**
   * 获取资源的所有版本
   * @param resourceId 资源ID
   * @returns 版本数组
   */
  public getVersions(resourceId: string): ResourceVersion[] {
    if (!this.enabled) {
      return [];
    }

    return this.resourceVersions.get(resourceId) || [];
  }

  /**
   * 获取资源的特定版本
   * @param resourceId 资源ID
   * @param versionId 版本ID
   * @returns 版本对象，如果不存在则返回null
   */
  public getVersion(resourceId: string, versionId: string): ResourceVersion | null {
    if (!this.enabled) {
      return null;
    }

    const versions = this.resourceVersions.get(resourceId) || [];
    return versions.find(v => v.id === versionId) || null;
  }

  /**
   * 获取资源的最新版本
   * @param resourceId 资源ID
   * @returns 最新版本，如果不存在则返回null
   */
  public getLatestVersion(resourceId: string): ResourceVersion | null {
    if (!this.enabled) {
      return null;
    }

    const versions = this.resourceVersions.get(resourceId) || [];
    if (versions.length === 0) {
      return null;
    }

    return versions.sort((a, b) => b.version - a.version)[0];
  }

  /**
   * 删除资源版本
   * @param resourceId 资源ID
   * @param versionId 版本ID
   * @returns 是否成功删除
   */
  public deleteVersion(resourceId: string, versionId: string): boolean {
    if (!this.enabled) {
      return false;
    }

    const versions = this.resourceVersions.get(resourceId) || [];
    const index = versions.findIndex(v => v.id === versionId);

    if (index === -1) {
      return false;
    }

    // 不允许删除唯一的版本
    if (versions.length === 1) {
      if (this.debug) {
        console.warn(`[ResourceVersionManager] 无法删除唯一版本: ${resourceId}`);
      }
      return false;
    }

    // 删除版本
    const deletedVersion = versions.splice(index, 1)[0];
    this.resourceVersions.set(resourceId, versions);

    // 发出版本删除事件
    this.emit(ResourceVersionEventType.VERSION_DELETED, deletedVersion);

    if (this.debug) {
      console.log(`[ResourceVersionManager] 删除版本: ${resourceId}, 版本号: ${deletedVersion.version}`);
    }

    return true;
  }

  /**
   * 比较两个版本
   * @param resourceId 资源ID
   * @param versionId1 版本1 ID
   * @param versionId2 版本2 ID
   * @returns 比较结果
   */
  public compareVersions(resourceId: string, versionId1: string, versionId2: string): VersionComparisonResult | null {
    if (!this.enabled || !this.enableComparison) {
      return null;
    }

    const version1 = this.getVersion(resourceId, versionId1);
    const version2 = this.getVersion(resourceId, versionId2);

    if (!version1 || !version2) {
      if (this.debug) {
        console.error(`[ResourceVersionManager] 找不到要比较的版本: ${versionId1}, ${versionId2}`);
      }
      return null;
    }

    // 创建比较结果
    const result: VersionComparisonResult = {
      version1,
      version2,
      hasDifferences: false,
      differenceType: 'none',
      comparisonTimestamp: Date.now()
    };

    // 比较内容（哈希值）
    const contentDifferent = version1.hash !== version2.hash;

    // 比较元数据
    const metadataDifferences = this.compareObjects(version1.metadata || {}, version2.metadata || {});
    const metadataDifferent = metadataDifferences.hasChanges;

    // 设置差异类型
    if (contentDifferent && metadataDifferent) {
      result.differenceType = 'both';
      result.hasDifferences = true;
    } else if (contentDifferent) {
      result.differenceType = 'content';
      result.hasDifferences = true;
    } else if (metadataDifferent) {
      result.differenceType = 'metadata';
      result.hasDifferences = true;
      result.metadataDifferences = metadataDifferences.changes;
    }

    // 保存最近比较结果
    this.lastComparisonResult = result;

    // 发出版本比较事件
    this.emit(ResourceVersionEventType.VERSION_COMPARED, result);

    if (this.debug) {
      console.log(`[ResourceVersionManager] 比较版本: ${resourceId}, 版本1: ${version1.version}, 版本2: ${version2.version}, 有差异: ${result.hasDifferences}`);
    }

    return result;
  }

  /**
   * 获取最近的比较结果
   * @returns 最近的比较结果
   */
  public getLastComparisonResult(): VersionComparisonResult | null {
    return this.lastComparisonResult;
  }

  /**
   * 添加版本标签
   * @param resourceId 资源ID
   * @param versionId 版本ID
   * @param tag 标签
   * @returns 是否成功添加
   */
  public addVersionTag(resourceId: string, versionId: string, tag: string): boolean {
    if (!this.enabled || !this.enableTags) {
      return false;
    }

    const version = this.getVersion(resourceId, versionId);
    if (!version) {
      return false;
    }

    // 确保标签数组存在
    if (!version.tags) {
      version.tags = [];
    }

    // 检查标签是否已存在
    if (version.tags.includes(tag)) {
      return true;
    }

    // 添加标签
    version.tags.push(tag);

    // 发出版本标记事件
    this.emit(ResourceVersionEventType.VERSION_TAGGED, { version, tag });

    if (this.debug) {
      console.log(`[ResourceVersionManager] 添加标签: ${resourceId}, 版本: ${version.version}, 标签: ${tag}`);
    }

    return true;
  }

  /**
   * 移除版本标签
   * @param resourceId 资源ID
   * @param versionId 版本ID
   * @param tag 标签
   * @returns 是否成功移除
   */
  public removeVersionTag(resourceId: string, versionId: string, tag: string): boolean {
    if (!this.enabled || !this.enableTags) {
      return false;
    }

    const version = this.getVersion(resourceId, versionId);
    if (!version || !version.tags) {
      return false;
    }

    // 查找标签索引
    const index = version.tags.indexOf(tag);
    if (index === -1) {
      return false;
    }

    // 移除标签
    version.tags.splice(index, 1);

    if (this.debug) {
      console.log(`[ResourceVersionManager] 移除标签: ${resourceId}, 版本: ${version.version}, 标签: ${tag}`);
    }

    return true;
  }

  /**
   * 更新版本描述
   * @param resourceId 资源ID
   * @param versionId 版本ID
   * @param description 新描述
   * @returns 是否成功更新
   */
  public updateVersionDescription(resourceId: string, versionId: string, description: string): boolean {
    if (!this.enabled) {
      return false;
    }

    const version = this.getVersion(resourceId, versionId);
    if (!version) {
      return false;
    }

    // 更新描述
    version.description = description;

    if (this.debug) {
      console.log(`[ResourceVersionManager] 更新描述: ${resourceId}, 版本: ${version.version}, 描述: ${description}`);
    }

    return true;
  }

  /**
   * 生成唯一ID
   * @returns 唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  /**
   * 比较两个对象
   * @param obj1 对象1
   * @param obj2 对象2
   * @returns 比较结果
   */
  private compareObjects(obj1: Record<string, any>, obj2: Record<string, any>): {
    hasChanges: boolean;
    changes: {
      added: Record<string, any>;
      removed: Record<string, any>;
      modified: Record<string, { from: any; to: any }>;
    };
  } {
    const result = {
      hasChanges: false,
      changes: {
        added: {} as Record<string, any>,
        removed: {} as Record<string, any>,
        modified: {} as Record<string, { from: any; to: any }>
      }
    };

    // 查找添加和修改的属性
    for (const key in obj2) {
      if (!(key in obj1)) {
        result.hasChanges = true;
        result.changes.added[key] = obj2[key];
      } else if (JSON.stringify(obj1[key]) !== JSON.stringify(obj2[key])) {
        result.hasChanges = true;
        result.changes.modified[key] = {
          from: obj1[key],
          to: obj2[key]
        };
      }
    }

    // 查找删除的属性
    for (const key in obj1) {
      if (!(key in obj2)) {
        result.hasChanges = true;
        result.changes.removed[key] = obj1[key];
      }
    }

    return result;
  }
}
