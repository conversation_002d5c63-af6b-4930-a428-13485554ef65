/**
 * 角色控制器示例
 * 展示如何使用动画系统创建角色控制器
 */
import * as THREE from 'three';
import { Entity } from '../../core/Entity';
import { Component } from '../../core/Component';
import { AnimationClip } from '../../animation/AnimationClip';
import { AnimationStateMachine } from '../../animation/AnimationStateMachine';
import { BlendSpace2D } from '../../animation/BlendSpace2D';
import { SkeletonAnimation } from '../../animation/SkeletonAnimation';
import { InputSystem } from '../../input/InputSystem';
import { PhysicsSystem } from '../../physics/PhysicsSystem';

/**
 * 角色控制器状态
 */
export enum CharacterState {
  /** 空闲 */
  IDLE = 'Idle',
  /** 行走 */
  WALK = 'Walk',
  /** 跑步 */
  RUN = 'Run',
  /** 跳跃 */
  JUMP = 'Jump',
  /** 下落 */
  FALL = 'Fall',
  /** 着陆 */
  LAND = 'Land',
  /** 攻击 */
  ATTACK = 'Attack',
  /** 受伤 */
  HIT = 'Hit',
  /** 死亡 */
  DEATH = 'Death'
}

/**
 * 角色控制器配置
 */
export interface CharacterControllerConfig {
  /** 行走速度 */
  walkSpeed?: number;
  /** 跑步速度 */
  runSpeed?: number;
  /** 跳跃力量 */
  jumpForce?: number;
  /** 重力 */
  gravity?: number;
  /** 旋转速度 */
  rotationSpeed?: number;
  /** 是否使用物理 */
  usePhysics?: boolean;
  /** 是否使用状态机 */
  useStateMachine?: boolean;
  /** 是否使用混合空间 */
  useBlendSpace?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 角色控制器组件
 */
export class CharacterController extends Component {
  /** 组件类型 */
  public static readonly type: string = 'CharacterController';

  /** 配置 */
  private config: CharacterControllerConfig;
  /** 骨骼动画组件 */
  private skeletonAnimation: SkeletonAnimation | null = null;
  /** 状态机 */
  private stateMachine: AnimationStateMachine | null = null;
  /** 混合空间 */
  private locomotionBlendSpace: BlendSpace2D | null = null;
  /** 输入系统 */
  private inputSystem: InputSystem | null = null;
  /** 物理系统 */
  private physicsSystem: PhysicsSystem | null = null;
  /** 当前状态 */
  private currentState: CharacterState = CharacterState.IDLE;
  /** 移动方向 */
  private moveDirection: THREE.Vector3 = new THREE.Vector3();
  /** 速度 */
  private velocity: THREE.Vector3 = new THREE.Vector3();
  /** 是否在地面上 */
  private isGrounded: boolean = true;
  /** 是否正在跳跃 */
  private isJumping: boolean = false;
  /** 是否正在跑步 */
  private isRunning: boolean = false;
  /** 是否正在攻击 */
  private isAttacking: boolean = false;
  /** 是否已死亡 */
  private isDead: boolean = false;
  /** 是否启用 */
  private enabled: boolean = true;

  /**
   * 创建角色控制器组件
   * @param config 配置
   */
  constructor(config: CharacterControllerConfig = {}) {
    super(CharacterController.type);
    this.config = {
      walkSpeed: config.walkSpeed || 2.0,
      runSpeed: config.runSpeed || 5.0,
      jumpForce: config.jumpForce || 5.0,
      gravity: config.gravity || 9.8,
      rotationSpeed: config.rotationSpeed || 5.0,
      usePhysics: config.usePhysics !== undefined ? config.usePhysics : true,
      useStateMachine: config.useStateMachine !== undefined ? config.useStateMachine : true,
      useBlendSpace: config.useBlendSpace !== undefined ? config.useBlendSpace : true,
      debug: config.debug || false
    };
  }

  /**
   * 初始化控制器
   * @param entity 实体
   * @param skeletonAnimation 骨骼动画组件
   * @param inputSystem 输入系统
   * @param physicsSystem 物理系统
   */
  public initialize(
    entity: Entity,
    skeletonAnimation: SkeletonAnimation,
    inputSystem: InputSystem,
    physicsSystem?: PhysicsSystem
  ): void {
    this.skeletonAnimation = skeletonAnimation;
    this.inputSystem = inputSystem;
    this.physicsSystem = physicsSystem || null;

    // 设置实体
    this.setEntity(entity);

    // 初始化状态机
    if (this.config.useStateMachine) {
      this.initStateMachine();
    }

    // 初始化混合空间
    if (this.config.useBlendSpace) {
      this.initBlendSpace();
    }
  }

  /**
   * 初始化状态机
   */
  private initStateMachine(): void {
    if (!this.skeletonAnimation) return;

    // 创建状态机
    this.stateMachine = new AnimationStateMachine(this.skeletonAnimation.getAnimator());

    // 添加状态
    this.stateMachine.addState({
      name: CharacterState.IDLE,
      type: 'SingleAnimationState',
      clipName: 'idle',
      loop: true,
      clamp: false
    });

    this.stateMachine.addState({
      name: CharacterState.WALK,
      type: 'SingleAnimationState',
      clipName: 'walk',
      loop: true,
      clamp: false
    });

    this.stateMachine.addState({
      name: CharacterState.RUN,
      type: 'SingleAnimationState',
      clipName: 'run',
      loop: true,
      clamp: false
    });

    this.stateMachine.addState({
      name: CharacterState.JUMP,
      type: 'SingleAnimationState',
      clipName: 'jump',
      loop: false,
      clamp: false
    });

    this.stateMachine.addState({
      name: CharacterState.FALL,
      type: 'SingleAnimationState',
      clipName: 'fall',
      loop: true,
      clamp: false
    });

    this.stateMachine.addState({
      name: CharacterState.LAND,
      type: 'SingleAnimationState',
      clipName: 'land',
      loop: false,
      clamp: false
    });

    this.stateMachine.addState({
      name: CharacterState.ATTACK,
      type: 'SingleAnimationState',
      clipName: 'attack',
      loop: false,
      clamp: false
    });

    // 添加转换规则
    this.stateMachine.addTransition({
      from: CharacterState.IDLE,
      to: CharacterState.WALK,
      condition: () => this.moveDirection.lengthSq() > 0.1 && !this.isRunning,
      duration: 0.2,
      canInterrupt: true
    });

    this.stateMachine.addTransition({
      from: CharacterState.WALK,
      to: CharacterState.IDLE,
      condition: () => this.moveDirection.lengthSq() <= 0.1,
      duration: 0.2,
      canInterrupt: true
    });

    this.stateMachine.addTransition({
      from: CharacterState.WALK,
      to: CharacterState.RUN,
      condition: () => this.isRunning && this.moveDirection.lengthSq() > 0.1,
      duration: 0.2,
      canInterrupt: true
    });

    this.stateMachine.addTransition({
      from: CharacterState.RUN,
      to: CharacterState.WALK,
      condition: () => !this.isRunning && this.moveDirection.lengthSq() > 0.1,
      duration: 0.2,
      canInterrupt: true
    });

    this.stateMachine.addTransition({
      from: CharacterState.RUN,
      to: CharacterState.IDLE,
      condition: () => this.moveDirection.lengthSq() <= 0.1,
      duration: 0.2,
      canInterrupt: true
    });

    // 设置初始状态
    this.stateMachine.setCurrentState(CharacterState.IDLE);
  }

  /**
   * 初始化混合空间
   */
  private initBlendSpace(): void {
    if (!this.skeletonAnimation) return;

    // 创建混合空间
    this.locomotionBlendSpace = new BlendSpace2D({
      minX: -1,
      maxX: 1,
      minY: -1,
      maxY: 1
    });

    // 获取动画片段
    const idleClip = this.skeletonAnimation.getClip('idle');
    const walkForwardClip = this.skeletonAnimation.getClip('walkForward');
    const walkBackwardClip = this.skeletonAnimation.getClip('walkBackward');
    const walkLeftClip = this.skeletonAnimation.getClip('walkLeft');
    const walkRightClip = this.skeletonAnimation.getClip('walkRight');
    const runForwardClip = this.skeletonAnimation.getClip('runForward');

    // 添加节点
    if (idleClip) {
      this.locomotionBlendSpace.addNode(idleClip, new THREE.Vector2(0, 0));
    }

    if (walkForwardClip) {
      this.locomotionBlendSpace.addNode(walkForwardClip, new THREE.Vector2(0, 1));
    }

    if (walkBackwardClip) {
      this.locomotionBlendSpace.addNode(walkBackwardClip, new THREE.Vector2(0, -1));
    }

    if (walkLeftClip) {
      this.locomotionBlendSpace.addNode(walkLeftClip, new THREE.Vector2(-1, 0));
    }

    if (walkRightClip) {
      this.locomotionBlendSpace.addNode(walkRightClip, new THREE.Vector2(1, 0));
    }

    if (runForwardClip) {
      this.locomotionBlendSpace.addNode(runForwardClip, new THREE.Vector2(0, 2));
    }
  }

  /**
   * 更新控制器
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled || !this.skeletonAnimation || this.isDead) return;

    // 处理输入
    this.handleInput();

    // 更新物理
    this.updatePhysics(deltaTime);

    // 更新动画
    this.updateAnimation(deltaTime);

    // 更新变换
    this.updateTransform(deltaTime);
  }

  /**
   * 处理输入
   */
  private handleInput(): void {
    if (!this.inputSystem) return;

    // 获取移动输入
    const moveX = this.inputSystem.getAxis('Horizontal');
    const moveZ = this.inputSystem.getAxis('Vertical');

    // 设置移动方向
    this.moveDirection.set(moveX, 0, moveZ);

    // 规范化移动方向
    if (this.moveDirection.lengthSq() > 1) {
      this.moveDirection.normalize();
    }

    // 检查是否正在跑步
    this.isRunning = this.inputSystem.getButton('Run');

    // 检查是否正在跳跃
    if (this.inputSystem.getButtonDown('Jump') && this.isGrounded && !this.isJumping) {
      this.jump();
    }

    // 检查是否正在攻击
    if (this.inputSystem.getButtonDown('Attack') && !this.isAttacking) {
      this.attack();
    }
  }

  /**
   * 更新物理
   * @param deltaTime 帧间隔时间（秒）
   */
  private updatePhysics(deltaTime: number): void {
    // 应用重力
    if (!this.isGrounded) {
      this.velocity.y -= this.config.gravity! * deltaTime;
    }

    // 设置水平速度
    const speed = this.isRunning ? this.config.runSpeed! : this.config.walkSpeed!;
    this.velocity.x = this.moveDirection.x * speed;
    this.velocity.z = this.moveDirection.z * speed;

    // 如果使用物理系统
    if (this.config.usePhysics && this.physicsSystem) {
      // 应用物理
      const entity = this.getEntity();
      if (entity) {
        // 获取刚体组件
        const rigidBody = entity.getComponent('RigidBody');
        if (rigidBody) {
          // 设置速度
          rigidBody.setLinearVelocity(this.velocity);

          // 检查是否在地面上
          this.isGrounded = this.physicsSystem.checkGrounded(entity);
        }
      }
    } else {
      // 简单物理模拟
      const entity = this.getEntity();
      if (entity) {
        // 获取变换组件
        const transform = entity.getTransform();
        if (transform) {
          // 应用速度
          const position = transform.getPosition();
          position.x += this.velocity.x * deltaTime;
          position.y += this.velocity.y * deltaTime;
          position.z += this.velocity.z * deltaTime;
          transform.setPosition(position);

          // 简单地面检测
          if (position.y <= 0) {
            position.y = 0;
            transform.setPosition(position);
            this.velocity.y = 0;
            this.isGrounded = true;

            // 如果正在跳跃，触发着陆
            if (this.isJumping) {
              this.land();
            }
          }
        }
      }
    }
  }

  /**
   * 更新动画
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateAnimation(deltaTime: number): void {
    // 如果使用状态机
    if (this.config.useStateMachine && this.stateMachine) {
      // 更新状态机
      this.stateMachine.update(deltaTime);
    }
    // 如果使用混合空间
    else if (this.config.useBlendSpace && this.locomotionBlendSpace && this.skeletonAnimation) {
      // 设置混合空间位置
      const x = this.moveDirection.x;
      const y = this.moveDirection.z;
      const speedFactor = this.isRunning ? 2.0 : 1.0;

      this.locomotionBlendSpace.setPosition(x, y * speedFactor);
      this.locomotionBlendSpace.update();

      // 应用混合结果
      const activeNodes = this.locomotionBlendSpace.getActiveNodes();
      for (const node of activeNodes) {
        this.skeletonAnimation.play(node.clip.name, 0.2);
        // 设置权重
        const action = this.skeletonAnimation.getAnimator().getAction(node.clip.name);
        if (action) {
          action.weight = node.weight;
        }
      }
    }
    // 否则，使用简单动画
    else if (this.skeletonAnimation) {
      // 根据状态播放动画
      switch (this.currentState) {
        case CharacterState.IDLE:
          this.skeletonAnimation.play('idle');
          break;
        case CharacterState.WALK:
          this.skeletonAnimation.play('walk');
          break;
        case CharacterState.RUN:
          this.skeletonAnimation.play('run');
          break;
        case CharacterState.JUMP:
          this.skeletonAnimation.play('jump');
          break;
        case CharacterState.FALL:
          this.skeletonAnimation.play('fall');
          break;
        case CharacterState.LAND:
          this.skeletonAnimation.play('land');
          break;
        case CharacterState.ATTACK:
          this.skeletonAnimation.play('attack');
          break;
        case CharacterState.HIT:
          this.skeletonAnimation.play('hit');
          break;
        case CharacterState.DEATH:
          this.skeletonAnimation.play('death');
          break;
      }
    }
  }

  /**
   * 更新变换
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateTransform(deltaTime: number): void {
    // 如果没有移动，不需要旋转
    if (this.moveDirection.lengthSq() <= 0.1) return;

    // 获取实体
    const entity = this.getEntity();
    if (!entity) return;

    // 获取变换组件
    const transform = entity.getTransform();
    if (!transform) return;

    // 计算目标旋转
    const targetRotation = new THREE.Quaternion();
    const targetDirection = new THREE.Vector3(this.moveDirection.x, 0, this.moveDirection.z);
    targetDirection.normalize();

    // 创建旋转矩阵
    const rotationMatrix = new THREE.Matrix4();
    rotationMatrix.lookAt(new THREE.Vector3(0, 0, 0), targetDirection, new THREE.Vector3(0, 1, 0));
    targetRotation.setFromRotationMatrix(rotationMatrix);

    // 平滑旋转
    const currentRotation = transform.getRotation();
    currentRotation.slerp(targetRotation, this.config.rotationSpeed! * deltaTime);
    transform.setRotation(currentRotation);
  }

  /**
   * 跳跃
   */
  private jump(): void {
    if (!this.isGrounded || this.isJumping) return;

    // 设置垂直速度
    this.velocity.y = this.config.jumpForce!;
    this.isGrounded = false;
    this.isJumping = true;

    // 设置状态
    this.currentState = CharacterState.JUMP;

    // 如果使用状态机
    if (this.config.useStateMachine && this.stateMachine) {
      this.stateMachine.setCurrentState(CharacterState.JUMP);
    }
  }

  /**
   * 着陆
   */
  private land(): void {
    this.isJumping = false;

    // 设置状态
    this.currentState = CharacterState.LAND;

    // 如果使用状态机
    if (this.config.useStateMachine && this.stateMachine) {
      this.stateMachine.setCurrentState(CharacterState.LAND);
    }
  }

  /**
   * 攻击
   */
  private attack(): void {
    this.isAttacking = true;

    // 设置状态
    this.currentState = CharacterState.ATTACK;

    // 如果使用状态机
    if (this.config.useStateMachine && this.stateMachine) {
      this.stateMachine.setCurrentState(CharacterState.ATTACK);
    }

    // 攻击结束后恢复
    setTimeout(() => {
      this.isAttacking = false;
    }, 1000); // 假设攻击动画持续1秒
  }

  /**
   * 受伤
   */
  public hit(): void {
    if (this.isDead) return;

    // 设置状态
    this.currentState = CharacterState.HIT;

    // 如果使用状态机
    if (this.config.useStateMachine && this.stateMachine) {
      this.stateMachine.setCurrentState(CharacterState.HIT);
    }
  }

  /**
   * 死亡
   */
  public die(): void {
    if (this.isDead) return;

    this.isDead = true;

    // 设置状态
    this.currentState = CharacterState.DEATH;

    // 如果使用状态机
    if (this.config.useStateMachine && this.stateMachine) {
      this.stateMachine.setCurrentState(CharacterState.DEATH);
    }
  }

  /**
   * 复活
   */
  public revive(): void {
    if (!this.isDead) return;

    this.isDead = false;

    // 设置状态
    this.currentState = CharacterState.IDLE;

    // 如果使用状态机
    if (this.config.useStateMachine && this.stateMachine) {
      this.stateMachine.setCurrentState(CharacterState.IDLE);
    }
  }

  /**
   * 是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 设置启用状态
   * @param enabled 启用状态
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }
}
