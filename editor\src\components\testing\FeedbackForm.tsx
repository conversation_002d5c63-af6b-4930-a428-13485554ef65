/**
 * 反馈表单组件
 */
import React, { useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Radio,
  Rate,
  Button,
  Space,
  Typography,
  Select,
  Divider,
  message
} from 'antd';
import {
  BulbOutlined,
  BugOutlined,
  StarOutlined,
  CommentOutlined,
  SendOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { selectCurrentTaskId } from '../../store/testing/userTestingSlice';
import { userTestingService, UserFeedback } from '../../services/UserTestingService';

const { TextArea } = Input;
const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

/**
 * 反馈表单属性
 */
interface FeedbackFormProps {
  visible: boolean;
  onClose: () => void;
}

/**
 * 反馈表单组件
 */
const FeedbackForm: React.FC<FeedbackFormProps> = ({ visible, onClose }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  
  // 从Redux获取状态
  const currentTaskId = useSelector(selectCurrentTaskId);
  
  // 本地状态
  const [feedbackType, setFeedbackType] = useState<UserFeedback['type']>('comment');
  const [submitting, setSubmitting] = useState(false);
  
  // 处理反馈类型变更
  const handleFeedbackTypeChange = (e: any) => {
    setFeedbackType(e.target.value);
  };
  
  // 处理提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);
      
      // 准备反馈数据
      const feedbackData: Omit<UserFeedback, 'id' | 'userId' | 'userName' | 'sessionId' | 'timestamp'> = {
        type: feedbackType,
        content: values.content,
        taskId: currentTaskId || undefined,
        metadata: {
          browser: getBrowserInfo(),
          os: getOSInfo(),
          screenSize: `${window.innerWidth}x${window.innerHeight}`
        }
      };
      
      // 添加评分（如果有）
      if (feedbackType === 'rating' && values.rating) {
        feedbackData.rating = values.rating;
      }
      
      // 添加反馈
      try {
        userTestingService.addFeedback(feedbackData);
        message.success('感谢您的反馈！');
        form.resetFields();
        onClose();
      } catch (error) {
        message.error('提交反馈失败');
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setSubmitting(false);
    }
  };
  
  // 处理取消
  const handleCancel = () => {
    form.resetFields();
    onClose();
  };
  
  // 获取浏览器信息
  const getBrowserInfo = (): string => {
    const userAgent = navigator.userAgent;
    let browserName = 'Unknown';
    
    if (userAgent.indexOf('Chrome') > -1) {
      browserName = 'Chrome';
    } else if (userAgent.indexOf('Safari') > -1) {
      browserName = 'Safari';
    } else if (userAgent.indexOf('Firefox') > -1) {
      browserName = 'Firefox';
    } else if (userAgent.indexOf('MSIE') > -1 || userAgent.indexOf('Trident') > -1) {
      browserName = 'Internet Explorer';
    } else if (userAgent.indexOf('Edge') > -1) {
      browserName = 'Edge';
    }
    
    return browserName;
  };
  
  // 获取操作系统信息
  const getOSInfo = (): string => {
    const userAgent = navigator.userAgent;
    let osName = 'Unknown';
    
    if (userAgent.indexOf('Win') > -1) {
      osName = 'Windows';
    } else if (userAgent.indexOf('Mac') > -1) {
      osName = 'MacOS';
    } else if (userAgent.indexOf('Linux') > -1) {
      osName = 'Linux';
    } else if (userAgent.indexOf('Android') > -1) {
      osName = 'Android';
    } else if (userAgent.indexOf('iOS') > -1 || userAgent.indexOf('iPhone') > -1 || userAgent.indexOf('iPad') > -1) {
      osName = 'iOS';
    }
    
    return osName;
  };
  
  // 渲染表单内容
  const renderFormContent = () => {
    return (
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          type: feedbackType,
          content: '',
          rating: 3
        }}
      >
        <Form.Item
          name="type"
          label="反馈类型"
        >
          <Radio.Group onChange={handleFeedbackTypeChange} value={feedbackType}>
            <Space direction="vertical">
              <Radio value="bug">
                <Space>
                  <BugOutlined />
                  <span>问题报告</span>
                </Space>
              </Radio>
              
              <Radio value="suggestion">
                <Space>
                  <BulbOutlined />
                  <span>改进建议</span>
                </Space>
              </Radio>
              
              <Radio value="rating">
                <Space>
                  <StarOutlined />
                  <span>功能评分</span>
                </Space>
              </Radio>
              
              <Radio value="comment">
                <Space>
                  <CommentOutlined />
                  <span>一般评论</span>
                </Space>
              </Radio>
            </Space>
          </Radio.Group>
        </Form.Item>
        
        {feedbackType === 'rating' && (
          <Form.Item
            name="rating"
            label="您对协作编辑功能的评分"
            rules={[{ required: true, message: '请给出评分' }]}
          >
            <Rate allowHalf />
          </Form.Item>
        )}
        
        <Form.Item
          name="content"
          label={getFeedbackContentLabel()}
          rules={[{ required: true, message: '请输入反馈内容' }]}
        >
          <TextArea
            rows={6}
            placeholder={getFeedbackContentPlaceholder()}
          />
        </Form.Item>
        
        {currentTaskId && (
          <Form.Item label="关联任务">
            <Input value={currentTaskId} disabled />
          </Form.Item>
        )}
      </Form>
    );
  };
  
  // 获取反馈内容标签
  const getFeedbackContentLabel = (): string => {
    switch (feedbackType) {
      case 'bug':
        return '问题描述';
      case 'suggestion':
        return '建议内容';
      case 'rating':
        return '评价内容';
      case 'comment':
      default:
        return '评论内容';
    }
  };
  
  // 获取反馈内容占位符
  const getFeedbackContentPlaceholder = (): string => {
    switch (feedbackType) {
      case 'bug':
        return '请描述您遇到的问题，包括如何重现、预期行为和实际行为...';
      case 'suggestion':
        return '请描述您的改进建议，包括为什么需要这个改进以及如何实现...';
      case 'rating':
        return '请说明您给出这个评分的原因...';
      case 'comment':
      default:
        return '请输入您的评论...';
    }
  };
  
  return (
    <Modal
      title={
        <Space>
          <BulbOutlined />
          <span>提交反馈</span>
        </Space>
      }
      open={visible}
      onCancel={handleCancel}
      width={600}
      footer={[
        <Button key="cancel" onClick={handleCancel} icon={<CloseCircleOutlined />}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={handleSubmit}
          loading={submitting}
          icon={<SendOutlined />}
        >
          提交
        </Button>
      ]}
    >
      <Paragraph>
        您的反馈对我们改进协作编辑功能非常重要。请分享您的使用体验、发现的问题或改进建议。
      </Paragraph>
      
      <Divider />
      
      {renderFormContent()}
    </Modal>
  );
};

export default FeedbackForm;
