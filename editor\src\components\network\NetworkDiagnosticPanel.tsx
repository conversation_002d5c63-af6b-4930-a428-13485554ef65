/**
 * 网络诊断面板组件
 * 用于诊断网络问题并提供解决方案
 */
import React, { useState, useEffect } from 'react';
import { Card, Button, Steps, Collapse, Alert, Result, Spin, Typography, Space, Divider, List, Tag } from 'antd';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  WarningOutlined, 
  LoadingOutlined,
  SearchOutlined,
  SyncOutlined,
  BugOutlined,
  ToolOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { NetworkIssue, NetworkIssueType } from '../../../engine/src/network/NetworkQualityMonitor';
import './NetworkDiagnosticPanel.css';

const { Step } = Steps;
const { Panel } = Collapse;
const { Title, Text, Paragraph } = Typography;

// 诊断测试类型
enum DiagnosticTestType {
  CONNECTIVITY = 'connectivity',
  LATENCY = 'latency',
  PACKET_LOSS = 'packetLoss',
  BANDWIDTH = 'bandwidth',
  STABILITY = 'stability',
  SERVER = 'server',
}

// 诊断测试状态
enum DiagnosticTestStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  SUCCESS = 'success',
  WARNING = 'warning',
  FAILED = 'failed',
}

// 诊断测试结果
interface DiagnosticTestResult {
  type: DiagnosticTestType;
  status: DiagnosticTestStatus;
  name: string;
  description: string;
  details?: string;
  value?: number | string;
  threshold?: number | string;
  issues?: NetworkIssue[];
  solutions?: string[];
}

interface NetworkDiagnosticPanelProps {
  /** 当前网络问题 */
  issues?: NetworkIssue[];
  /** 是否连接 */
  connected?: boolean;
  /** 服务器URL */
  serverUrl?: string;
  /** 开始诊断回调 */
  onStartDiagnostic?: () => void;
  /** 解决问题回调 */
  onResolveIssue?: (issue: NetworkIssue) => void;
  /** 重新连接回调 */
  onReconnect?: () => void;
}

/**
 * 网络诊断面板组件
 */
const NetworkDiagnosticPanel: React.FC<NetworkDiagnosticPanelProps> = ({
  issues = [],
  connected = false,
  serverUrl = '',
  onStartDiagnostic,
  onResolveIssue,
  onReconnect,
}) => {
  const { t } = useTranslation();
  const [diagnosing, setDiagnosing] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [testResults, setTestResults] = useState<DiagnosticTestResult[]>([]);
  const [overallResult, setOverallResult] = useState<'success' | 'warning' | 'error' | null>(null);
  
  // 开始诊断
  const handleStartDiagnostic = () => {
    setDiagnosing(true);
    setCurrentStep(0);
    setTestResults([]);
    setOverallResult(null);
    
    if (onStartDiagnostic) {
      onStartDiagnostic();
    }
    
    // 模拟诊断过程
    runDiagnosticTests();
  };
  
  // 运行诊断测试
  const runDiagnosticTests = () => {
    // 初始化测试结果
    const initialTests: DiagnosticTestResult[] = [
      {
        type: DiagnosticTestType.CONNECTIVITY,
        status: DiagnosticTestStatus.PENDING,
        name: t('network.diagnostic.connectivityTest'),
        description: t('network.diagnostic.connectivityTestDescription'),
      },
      {
        type: DiagnosticTestType.LATENCY,
        status: DiagnosticTestStatus.PENDING,
        name: t('network.diagnostic.latencyTest'),
        description: t('network.diagnostic.latencyTestDescription'),
      },
      {
        type: DiagnosticTestType.PACKET_LOSS,
        status: DiagnosticTestStatus.PENDING,
        name: t('network.diagnostic.packetLossTest'),
        description: t('network.diagnostic.packetLossTestDescription'),
      },
      {
        type: DiagnosticTestType.BANDWIDTH,
        status: DiagnosticTestStatus.PENDING,
        name: t('network.diagnostic.bandwidthTest'),
        description: t('network.diagnostic.bandwidthTestDescription'),
      },
      {
        type: DiagnosticTestType.STABILITY,
        status: DiagnosticTestStatus.PENDING,
        name: t('network.diagnostic.stabilityTest'),
        description: t('network.diagnostic.stabilityTestDescription'),
      },
      {
        type: DiagnosticTestType.SERVER,
        status: DiagnosticTestStatus.PENDING,
        name: t('network.diagnostic.serverTest'),
        description: t('network.diagnostic.serverTestDescription'),
      },
    ];
    
    setTestResults(initialTests);
    
    // 模拟测试过程
    // 连接测试
    setTimeout(() => {
      setCurrentStep(0);
      setTestResults(prev => {
        const updated = [...prev];
        updated[0] = {
          ...updated[0],
          status: connected ? DiagnosticTestStatus.SUCCESS : DiagnosticTestStatus.FAILED,
          details: connected 
            ? t('network.diagnostic.connectedToServer', { server: serverUrl }) 
            : t('network.diagnostic.notConnectedToServer'),
          issues: !connected ? [
            {
              type: NetworkIssueType.CONNECTION_INTERRUPTED,
              severity: 1,
              description: t('network.diagnostic.connectionInterruptedDescription'),
              solution: t('network.diagnostic.connectionInterruptedSolution'),
              startTime: Date.now(),
              duration: 0,
              resolved: false,
            }
          ] : undefined,
          solutions: !connected ? [
            t('network.diagnostic.checkInternetConnection'),
            t('network.diagnostic.checkServerStatus'),
            t('network.diagnostic.tryReconnect'),
          ] : undefined,
        };
        return updated;
      });
      
      // 延迟测试
      setTimeout(() => {
        setCurrentStep(1);
        const hasLatencyIssue = issues.some(issue => issue.type === NetworkIssueType.HIGH_LATENCY);
        const latencyValue = hasLatencyIssue ? 250 : 80;
        
        setTestResults(prev => {
          const updated = [...prev];
          updated[1] = {
            ...updated[1],
            status: hasLatencyIssue ? DiagnosticTestStatus.WARNING : DiagnosticTestStatus.SUCCESS,
            details: t('network.diagnostic.latencyDetails', { value: latencyValue }),
            value: latencyValue,
            threshold: 100,
            issues: hasLatencyIssue ? issues.filter(issue => issue.type === NetworkIssueType.HIGH_LATENCY) : undefined,
            solutions: hasLatencyIssue ? [
              t('network.diagnostic.closeBackgroundApplications'),
              t('network.diagnostic.useWiredConnection'),
              t('network.diagnostic.connectToCloserServer'),
            ] : undefined,
          };
          return updated;
        });
        
        // 丢包测试
        setTimeout(() => {
          setCurrentStep(2);
          const hasPacketLossIssue = issues.some(issue => issue.type === NetworkIssueType.PACKET_LOSS);
          const packetLossValue = hasPacketLossIssue ? 8.5 : 1.2;
          
          setTestResults(prev => {
            const updated = [...prev];
            updated[2] = {
              ...updated[2],
              status: hasPacketLossIssue ? DiagnosticTestStatus.WARNING : DiagnosticTestStatus.SUCCESS,
              details: t('network.diagnostic.packetLossDetails', { value: packetLossValue }),
              value: packetLossValue,
              threshold: 5,
              issues: hasPacketLossIssue ? issues.filter(issue => issue.type === NetworkIssueType.PACKET_LOSS) : undefined,
              solutions: hasPacketLossIssue ? [
                t('network.diagnostic.checkNetworkCongestion'),
                t('network.diagnostic.useWiredConnection'),
                t('network.diagnostic.restartRouter'),
              ] : undefined,
            };
            return updated;
          });
          
          // 带宽测试
          setTimeout(() => {
            setCurrentStep(3);
            const hasBandwidthIssue = issues.some(issue => issue.type === NetworkIssueType.LOW_BANDWIDTH);
            const bandwidthValue = hasBandwidthIssue ? 120 : 850;
            
            setTestResults(prev => {
              const updated = [...prev];
              updated[3] = {
                ...updated[3],
                status: hasBandwidthIssue ? DiagnosticTestStatus.WARNING : DiagnosticTestStatus.SUCCESS,
                details: t('network.diagnostic.bandwidthDetails', { value: bandwidthValue }),
                value: bandwidthValue,
                threshold: 500,
                issues: hasBandwidthIssue ? issues.filter(issue => issue.type === NetworkIssueType.LOW_BANDWIDTH) : undefined,
                solutions: hasBandwidthIssue ? [
                  t('network.diagnostic.closeBackgroundDownloads'),
                  t('network.diagnostic.upgradeInternetPlan'),
                  t('network.diagnostic.reduceSyncQuality'),
                ] : undefined,
              };
              return updated;
            });
            
            // 稳定性测试
            setTimeout(() => {
              setCurrentStep(4);
              const hasStabilityIssue = issues.some(issue => 
                issue.type === NetworkIssueType.HIGH_JITTER || 
                issue.type === NetworkIssueType.UNSTABLE_CONNECTION
              );
              const stabilityValue = hasStabilityIssue ? 0.65 : 0.92;
              
              setTestResults(prev => {
                const updated = [...prev];
                updated[4] = {
                  ...updated[4],
                  status: hasStabilityIssue ? DiagnosticTestStatus.WARNING : DiagnosticTestStatus.SUCCESS,
                  details: t('network.diagnostic.stabilityDetails', { value: (stabilityValue * 100).toFixed(0) }),
                  value: stabilityValue,
                  threshold: 0.8,
                  issues: hasStabilityIssue ? issues.filter(issue => 
                    issue.type === NetworkIssueType.HIGH_JITTER || 
                    issue.type === NetworkIssueType.UNSTABLE_CONNECTION
                  ) : undefined,
                  solutions: hasStabilityIssue ? [
                    t('network.diagnostic.useWiredConnection'),
                    t('network.diagnostic.reduceWirelessInterference'),
                    t('network.diagnostic.restartRouter'),
                  ] : undefined,
                };
                return updated;
              });
              
              // 服务器测试
              setTimeout(() => {
                setCurrentStep(5);
                
                setTestResults(prev => {
                  const updated = [...prev];
                  updated[5] = {
                    ...updated[5],
                    status: DiagnosticTestStatus.SUCCESS,
                    details: t('network.diagnostic.serverDetails', { server: serverUrl }),
                  };
                  return updated;
                });
                
                // 完成诊断
                setTimeout(() => {
                  setDiagnosing(false);
                  
                  // 确定整体结果
                  const hasFailure = testResults.some(test => test.status === DiagnosticTestStatus.FAILED);
                  const hasWarning = testResults.some(test => test.status === DiagnosticTestStatus.WARNING);
                  
                  if (hasFailure) {
                    setOverallResult('error');
                  } else if (hasWarning) {
                    setOverallResult('warning');
                  } else {
                    setOverallResult('success');
                  }
                }, 500);
              }, 1000);
            }, 1000);
          }, 1000);
        }, 1000);
      }, 1000);
    }, 1000);
  };
  
  // 渲染测试结果
  const renderTestResult = (result: DiagnosticTestResult) => {
    const getStatusIcon = () => {
      switch (result.status) {
        case DiagnosticTestStatus.SUCCESS:
          return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
        case DiagnosticTestStatus.WARNING:
          return <WarningOutlined style={{ color: '#faad14' }} />;
        case DiagnosticTestStatus.FAILED:
          return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
        case DiagnosticTestStatus.RUNNING:
          return <LoadingOutlined style={{ color: '#1890ff' }} />;
        default:
          return <InfoCircleOutlined style={{ color: '#d9d9d9' }} />;
      }
    };
    
    return (
      <div className="test-result">
        <div className="test-header">
          <Space>
            {getStatusIcon()}
            <Text strong>{result.name}</Text>
          </Space>
          <Tag color={
            result.status === DiagnosticTestStatus.SUCCESS ? 'success' :
            result.status === DiagnosticTestStatus.WARNING ? 'warning' :
            result.status === DiagnosticTestStatus.FAILED ? 'error' :
            'default'
          }>
            {result.status === DiagnosticTestStatus.SUCCESS ? t('network.diagnostic.passed') :
             result.status === DiagnosticTestStatus.WARNING ? t('network.diagnostic.warning') :
             result.status === DiagnosticTestStatus.FAILED ? t('network.diagnostic.failed') :
             result.status === DiagnosticTestStatus.RUNNING ? t('network.diagnostic.running') :
             t('network.diagnostic.pending')}
          </Tag>
        </div>
        
        <div className="test-content">
          <Paragraph>{result.description}</Paragraph>
          
          {result.details && (
            <Paragraph type="secondary">{result.details}</Paragraph>
          )}
          
          {result.value !== undefined && result.threshold !== undefined && (
            <div className="test-metrics">
              <Text>
                {t('network.diagnostic.value')}: <Text strong>{result.value}</Text>
              </Text>
              <Text>
                {t('network.diagnostic.threshold')}: <Text strong>{result.threshold}</Text>
              </Text>
            </div>
          )}
          
          {result.issues && result.issues.length > 0 && (
            <div className="test-issues">
              <Text strong>{t('network.diagnostic.detectedIssues')}:</Text>
              <List
                size="small"
                dataSource={result.issues}
                renderItem={issue => (
                  <List.Item
                    actions={[
                      <Button 
                        type="link" 
                        size="small"
                        onClick={() => onResolveIssue && onResolveIssue(issue)}
                      >
                        {t('network.diagnostic.resolve')}
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={<WarningOutlined style={{ color: '#faad14' }} />}
                      title={issue.description}
                      description={issue.solution}
                    />
                  </List.Item>
                )}
              />
            </div>
          )}
          
          {result.solutions && result.solutions.length > 0 && (
            <div className="test-solutions">
              <Text strong>{t('network.diagnostic.recommendedSolutions')}:</Text>
              <List
                size="small"
                dataSource={result.solutions}
                renderItem={solution => (
                  <List.Item>
                    <Text>{solution}</Text>
                  </List.Item>
                )}
              />
            </div>
          )}
        </div>
      </div>
    );
  };
  
  // 渲染诊断结果
  const renderDiagnosticResult = () => {
    if (diagnosing) {
      return (
        <div className="diagnostic-running">
          <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
          <Text>{t('network.diagnostic.runningTest', { test: testResults[currentStep]?.name || '' })}</Text>
        </div>
      );
    }
    
    if (overallResult === null) {
      return (
        <div className="diagnostic-start">
          <Alert
            message={t('network.diagnostic.startPrompt')}
            description={t('network.diagnostic.startPromptDescription')}
            type="info"
            showIcon
          />
          <Button 
            type="primary" 
            icon={<SearchOutlined />} 
            onClick={handleStartDiagnostic}
            style={{ marginTop: 16 }}
          >
            {t('network.diagnostic.startDiagnostic')}
          </Button>
        </div>
      );
    }
    
    return (
      <div className="diagnostic-results">
        <Result
          status={overallResult}
          title={
            overallResult === 'success' ? t('network.diagnostic.allTestsPassed') :
            overallResult === 'warning' ? t('network.diagnostic.someIssuesDetected') :
            t('network.diagnostic.criticalIssuesDetected')
          }
          subTitle={
            overallResult === 'success' ? t('network.diagnostic.networkHealthy') :
            overallResult === 'warning' ? t('network.diagnostic.networkIssues') :
            t('network.diagnostic.networkProblems')
          }
          extra={[
            <Button 
              type="primary" 
              key="restart" 
              icon={<SyncOutlined />} 
              onClick={handleStartDiagnostic}
            >
              {t('network.diagnostic.runAgain')}
            </Button>,
            overallResult === 'error' && (
              <Button 
                key="reconnect" 
                icon={<SyncOutlined />} 
                onClick={onReconnect}
              >
                {t('network.diagnostic.reconnect')}
              </Button>
            )
          ]}
        />
        
        <Divider>{t('network.diagnostic.testResults')}</Divider>
        
        <Collapse defaultActiveKey={testResults
          .filter(result => result.status === DiagnosticTestStatus.WARNING || result.status === DiagnosticTestStatus.FAILED)
          .map((_, index) => index.toString())
        }>
          {testResults.map((result, index) => (
            <Panel 
              header={
                <Space>
                  {result.status === DiagnosticTestStatus.SUCCESS && <CheckCircleOutlined style={{ color: '#52c41a' }} />}
                  {result.status === DiagnosticTestStatus.WARNING && <WarningOutlined style={{ color: '#faad14' }} />}
                  {result.status === DiagnosticTestStatus.FAILED && <CloseCircleOutlined style={{ color: '#f5222d' }} />}
                  {result.name}
                </Space>
              } 
              key={index.toString()}
            >
              {renderTestResult(result)}
            </Panel>
          ))}
        </Collapse>
      </div>
    );
  };
  
  return (
    <div className="network-diagnostic-panel">
      <Card 
        title={
          <Space>
            <BugOutlined />
            {t('network.diagnostic.title')}
          </Space>
        }
      >
        <div className="diagnostic-content">
          <Steps current={diagnosing ? currentStep : -1} status={diagnosing ? 'process' : 'wait'}>
            <Step title={t('network.diagnostic.connectivity')} icon={diagnosing && currentStep === 0 ? <LoadingOutlined /> : <InfoCircleOutlined />} />
            <Step title={t('network.diagnostic.latency')} icon={diagnosing && currentStep === 1 ? <LoadingOutlined /> : <InfoCircleOutlined />} />
            <Step title={t('network.diagnostic.packetLoss')} icon={diagnosing && currentStep === 2 ? <LoadingOutlined /> : <InfoCircleOutlined />} />
            <Step title={t('network.diagnostic.bandwidth')} icon={diagnosing && currentStep === 3 ? <LoadingOutlined /> : <InfoCircleOutlined />} />
            <Step title={t('network.diagnostic.stability')} icon={diagnosing && currentStep === 4 ? <LoadingOutlined /> : <InfoCircleOutlined />} />
            <Step title={t('network.diagnostic.server')} icon={diagnosing && currentStep === 5 ? <LoadingOutlined /> : <InfoCircleOutlined />} />
          </Steps>
          
          <div className="diagnostic-result-container">
            {renderDiagnosticResult()}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default NetworkDiagnosticPanel;
