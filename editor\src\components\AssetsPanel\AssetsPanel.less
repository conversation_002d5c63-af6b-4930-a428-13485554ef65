/**
 * 资产面板样式
 */
.assets-panel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    h3 {
      margin: 0;
      font-size: 16px;
    }
  }

  .search-input {
    margin-bottom: 12px;
  }

  .ant-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .ant-tabs-content {
      flex: 1;
      overflow-y: auto;
    }
  }

  .asset-thumbnail {
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f0f0;
    overflow: hidden;
    
    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
  }

  .asset-tags {
    margin-top: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
}
