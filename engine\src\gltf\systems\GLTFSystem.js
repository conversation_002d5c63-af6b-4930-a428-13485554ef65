"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GLTFSystem = void 0;
var System_1 = require("../../core/System");
var GLTFLoader_1 = require("../GLTFLoader");
var GLTFExporter_1 = require("../GLTFExporter");
var GLTFModelComponent_1 = require("../components/GLTFModelComponent");
var GLTFAnimationComponent_1 = require("../components/GLTFAnimationComponent");
/**
 * GLTF系统
 */
var GLTFSystem = exports.GLTFSystem = /** @class */ (function (_super) {
    __extends(GLTFSystem, _super);
    /**
     * 创建GLTF系统
     * @param options GLTF系统选项
     */
    function GLTFSystem(options) {
        if (options === void 0) { options = {}; }
        var _this = _super.call(this, 5) || this;
        /** 加载中的模型 */
        _this.loadingModels = new Map();
        /** 已加载的模型 */
        _this.loadedModels = new Map();
        /** 实体到URL的映射 */
        _this.entityToUrl = new Map();
        // 创建GLTF加载器
        _this.loader = new GLTFLoader_1.GLTFLoader({
            useDraco: options.useDraco !== undefined ? options.useDraco : true,
            dracoDecoderPath: options.dracoDecoderPath,
            useKTX2: options.useKTX2 !== undefined ? options.useKTX2 : true,
            ktx2DecoderPath: options.ktx2DecoderPath,
            optimizeGeometry: options.optimizeGeometry !== undefined ? options.optimizeGeometry : true,
        });
        // 创建GLTF导出器
        _this.exporter = new GLTFExporter_1.GLTFExporter();
        // 设置选项
        _this.autoPlayAnimations = options.autoPlayAnimations !== undefined ? options.autoPlayAnimations : true;
        return _this;
    }
    /**
     * 初始化系统
     */
    GLTFSystem.prototype.initialize = function () {
        if (this.engine) {
            // 获取世界中的所有实体
            var world = this.engine.getWorld();
            // 查找具有GLTF组件的实体
            var entities = world.getAllEntities();
            for (var _i = 0, entities_1 = entities; _i < entities_1.length; _i++) {
                var entity = entities_1[_i];
                this.setupEntityGLTF(entity);
            }
            // 监听实体创建事件
            world.on('entityCreated', this.handleEntityCreated.bind(this));
            // 监听实体移除事件
            world.on('entityRemoved', this.handleEntityRemoved.bind(this));
        }
    };
    /**
     * 处理实体创建事件
     * @param entity 创建的实体
     */
    GLTFSystem.prototype.handleEntityCreated = function (entity) {
        this.setupEntityGLTF(entity);
    };
    /**
     * 处理实体移除事件
     * @param entity 移除的实体
     */
    GLTFSystem.prototype.handleEntityRemoved = function (entity) {
        // 移除实体到URL的映射
        this.entityToUrl.delete(entity.id);
    };
    /**
     * 设置实体的GLTF
     * @param entity 实体
     */
    GLTFSystem.prototype.setupEntityGLTF = function (entity) {
        var _this = this;
        if (!entity)
            return;
        // 检查实体是否有GLTF模型组件
        var modelComponent = entity.getComponent(GLTFModelComponent_1.GLTFModelComponent.type);
        if (modelComponent) {
            // 如果已加载，则不需要再次加载
            if (modelComponent.isLoaded()) {
                return;
            }
            // 获取URL
            var url = modelComponent.getURL();
            if (url) {
                // 记录实体到URL的映射
                this.entityToUrl.set(entity.id, url);
                // 加载模型
                this.loadModel(url).then(function (gltf) {
                    // 设置GLTF模型
                    modelComponent.setGLTF(gltf);
                    // 创建实体
                    _this.loader.createEntity(gltf, entity);
                    // 初始化动画
                    var animationComponent = entity.getComponent(GLTFAnimationComponent_1.GLTFAnimationComponent.type);
                    if (animationComponent) {
                        // 获取实体的变换组件
                        var transform = entity.getTransform();
                        if (transform) {
                            // 初始化动画混合器
                            animationComponent.initMixer(transform.getObject3D());
                            // 如果启用自动播放动画，则播放第一个动画
                            if (_this.autoPlayAnimations && gltf.animations && gltf.animations.length > 0) {
                                animationComponent.play(gltf.animations[0].name);
                            }
                        }
                    }
                }).catch(function (error) {
                    console.error("\u52A0\u8F7DGLTF\u6A21\u578B\u5931\u8D25: ".concat(error.message));
                    modelComponent.setError(error);
                });
            }
        }
    };
    /**
     * 加载模型
     * @param url 模型URL
     * @returns Promise，解析为加载的GLTF模型
     */
    GLTFSystem.prototype.loadModel = function (url) {
        return __awaiter(this, void 0, void 0, function () {
            var loadPromise;
            var _this = this;
            return __generator(this, function (_a) {
                // 检查是否已加载
                if (this.loadedModels.has(url)) {
                    return [2 /*return*/, this.loadedModels.get(url)];
                }
                // 检查是否正在加载
                if (this.loadingModels.has(url)) {
                    return [2 /*return*/, this.loadingModels.get(url)];
                }
                loadPromise = this.loader.load(url).then(function (gltf) {
                    // 缓存模型
                    _this.loadedModels.set(url, gltf);
                    // 移除加载中的记录
                    _this.loadingModels.delete(url);
                    return gltf;
                });
                // 记录加载中的模型
                this.loadingModels.set(url, loadPromise);
                return [2 /*return*/, loadPromise];
            });
        });
    };
    /**
     * 更新系统
     * @param deltaTime 帧间隔时间（秒）
     */
    GLTFSystem.prototype.update = function (deltaTime) {
        if (!this.engine)
            return;
        // 获取世界中的所有实体
        var world = this.engine.getWorld();
        // 更新所有具有GLTF动画组件的实体
        var entities = world.getAllEntities();
        for (var _i = 0, entities_2 = entities; _i < entities_2.length; _i++) {
            var entity = entities_2[_i];
            var animationComponent = entity.getComponent(GLTFAnimationComponent_1.GLTFAnimationComponent.type);
            if (animationComponent && animationComponent.isEnabled()) {
                animationComponent.update(deltaTime);
            }
        }
    };
    /**
     * 导出场景
     * @param scene 场景
     * @param options 导出选项
     * @returns Promise，解析为导出的GLTF数据
     */
    GLTFSystem.prototype.exportScene = function (scene, options) {
        if (options === void 0) { options = {}; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.exporter.exportScene(scene, options)];
            });
        });
    };
    /**
     * 导出实体
     * @param entity 实体
     * @param options 导出选项
     * @returns Promise，解析为导出的GLTF数据
     */
    GLTFSystem.prototype.exportEntity = function (entity, options) {
        if (options === void 0) { options = {}; }
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, this.exporter.exportEntity(entity, options)];
            });
        });
    };
    /**
     * 获取GLTF加载器
     * @returns GLTF加载器
     */
    GLTFSystem.prototype.getLoader = function () {
        return this.loader;
    };
    /**
     * 获取GLTF导出器
     * @returns GLTF导出器
     */
    GLTFSystem.prototype.getExporter = function () {
        return this.exporter;
    };
    /**
     * 销毁系统
     */
    GLTFSystem.prototype.dispose = function () {
        // 清除加载中的模型
        this.loadingModels.clear();
        // 清除已加载的模型
        this.loadedModels.clear();
        // 清除实体到URL的映射
        this.entityToUrl.clear();
        // 销毁加载器
        this.loader.dispose();
        // 调用父类销毁方法
        _super.prototype.dispose.call(this);
    };
    /** 系统类型 */
    GLTFSystem.type = 'GLTFSystem';
    return GLTFSystem;
}(System_1.System));
