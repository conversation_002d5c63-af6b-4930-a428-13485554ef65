/**
 * AI面部动画生成面板
 * 提供基于AI的面部动画生成功能
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Form,
  Input,
  Button,
  Select,
  Slider,
  Switch,
  Card,
  Divider,
  Space,
  Tooltip,
  message,
  Spin,
  Tag,
  Radio,
  Collapse
} from 'antd';
import {
  RobotOutlined,
  ExperimentOutlined,
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './AIFacialAnimationGenerator.less';

const { Option } = Select;
const { TextArea } = Input;
const { Panel } = Collapse;

// AI模型类型
export enum AIModelType {
  BASIC = 'basic',
  ADVANCED = 'advanced',
  BERT = 'bert',
  ROBERTA = 'roberta',
  DISTILBERT = 'distilbert',
  ALBERT = 'albert',
  XLNET = 'xlnet',
  CUSTOM = 'custom'
}

// 生成请求
interface GenerationRequest {
  id: string;
  prompt: string;
  duration: number;
  loop: boolean;
  style?: string;
  intensity?: number;
  modelType: AIModelType;
  useAdvancedFeatures: boolean;
}

// 生成历史记录
interface GenerationHistory {
  id: string;
  prompt: string;
  timestamp: number;
  modelType: AIModelType;
  success: boolean;
}

// 组件属性
interface AIFacialAnimationGeneratorProps {
  entityId?: string;
  onGenerate?: (request: GenerationRequest) => Promise<boolean>;
  onApply?: (animationId: string) => void;
  onPreview?: (animationId: string) => void;
  onClose?: () => void;
}

/**
 * AI面部动画生成面板
 */
const AIFacialAnimationGenerator: React.FC<AIFacialAnimationGeneratorProps> = ({
  entityId,
  onGenerate,
  onApply,
  onPreview,
  onClose
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 状态
  const [generating, setGenerating] = useState<boolean>(false);
  const [modelType, setModelType] = useState<AIModelType>(AIModelType.ADVANCED);
  const [useAdvancedFeatures, setUseAdvancedFeatures] = useState<boolean>(true);
  const [history, setHistory] = useState<GenerationHistory[]>([]);
  const [currentAnimationId, setCurrentAnimationId] = useState<string>('');
  const [showAdvancedSettings, setShowAdvancedSettings] = useState<boolean>(false);

  // 生成动画
  const handleGenerate = async () => {
    try {
      const values = await form.validateFields();

      setGenerating(true);

      const request: GenerationRequest = {
        id: Date.now().toString(),
        prompt: values.prompt,
        duration: values.duration,
        loop: values.loop,
        style: values.style,
        intensity: values.intensity,
        modelType,
        useAdvancedFeatures
      };

      // 调用生成回调
      if (onGenerate) {
        const success = await onGenerate(request);

        // 添加到历史记录
        const historyItem: GenerationHistory = {
          id: request.id,
          prompt: request.prompt,
          timestamp: Date.now(),
          modelType: request.modelType,
          success
        };

        setHistory(prev => [historyItem, ...prev].slice(0, 10));

        if (success) {
          setCurrentAnimationId(request.id);
          message.success(t('editor.ai.generationSuccess'));
        } else {
          message.error(t('editor.ai.generationFailed'));
        }
      }
    } catch (error) {
      console.error('生成面部动画失败:', error);
      message.error(t('editor.ai.generationError'));
    } finally {
      setGenerating(false);
    }
  };

  // 应用动画
  const handleApply = () => {
    if (currentAnimationId && onApply) {
      onApply(currentAnimationId);
      message.success(t('editor.ai.animationApplied'));
    }
  };

  // 预览动画
  const handlePreview = () => {
    if (currentAnimationId && onPreview) {
      onPreview(currentAnimationId);
    }
  };

  // 从历史记录中加载
  const handleLoadFromHistory = (item: GenerationHistory) => {
    form.setFieldsValue({
      prompt: item.prompt
    });

    setModelType(item.modelType);
    setCurrentAnimationId(item.id);
  };

  // 渲染模型类型标签
  const renderModelTypeTag = (type: AIModelType) => {
    let color = 'blue';
    let text = t('editor.ai.basicModel');

    switch (type) {
      case AIModelType.ADVANCED:
        color = 'green';
        text = t('editor.ai.advancedModel');
        break;
      case AIModelType.BERT:
        color = 'purple';
        text = t('editor.ai.bertModel');
        break;
      case AIModelType.ROBERTA:
        color = 'magenta';
        text = t('editor.ai.robertaModel');
        break;
      case AIModelType.DISTILBERT:
        color = 'volcano';
        text = t('editor.ai.distilbertModel');
        break;
      case AIModelType.ALBERT:
        color = 'geekblue';
        text = t('editor.ai.albertModel');
        break;
      case AIModelType.XLNET:
        color = 'gold';
        text = t('editor.ai.xlnetModel');
        break;
      case AIModelType.CUSTOM:
        color = 'orange';
        text = t('editor.ai.customModel');
        break;
    }

    return <Tag color={color}>{text}</Tag>;
  };

  // 渲染历史记录项
  const renderHistoryItem = (item: GenerationHistory) => {
    return (
      <Card
        key={item.id}
        size="small"
        className="history-item"
        onClick={() => handleLoadFromHistory(item)}
      >
        <div className="history-item-header">
          <div className="history-item-time">
            {new Date(item.timestamp).toLocaleTimeString()}
          </div>
          <div className="history-item-model">
            {renderModelTypeTag(item.modelType)}
          </div>
        </div>
        <div className="history-item-prompt">{item.prompt}</div>
      </Card>
    );
  };

  return (
    <div className="ai-facial-animation-generator">
      <div className="generator-header">
        <div className="generator-title">
          <RobotOutlined /> {t('editor.ai.facialAnimationGenerator')}
        </div>
        <div className="generator-actions">
          <Button onClick={onClose}>{t('common.close')}</Button>
        </div>
      </div>

      <div className="generator-content">
        <div className="generator-main">
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              prompt: '',
              duration: 5,
              loop: true,
              style: 'natural',
              intensity: 0.8
            }}
          >
            <Form.Item
              name="prompt"
              label={t('editor.ai.emotionPrompt')}
              rules={[{ required: true, message: t('editor.ai.promptRequired') }]}
            >
              <TextArea
                rows={4}
                placeholder={t('editor.ai.promptPlaceholder')}
                disabled={generating}
              />
            </Form.Item>

            <div className="form-row">
              <Form.Item
                name="duration"
                label={t('editor.ai.duration')}
                className="form-item-half"
              >
                <Slider
                  min={1}
                  max={10}
                  step={0.5}
                  marks={{ 1: '1s', 5: '5s', 10: '10s' }}
                  disabled={generating}
                />
              </Form.Item>

              <Form.Item
                name="intensity"
                label={t('editor.ai.intensity')}
                className="form-item-half"
              >
                <Slider
                  min={0.1}
                  max={1}
                  step={0.1}
                  marks={{ 0.1: '0.1', 0.5: '0.5', 1: '1.0' }}
                  disabled={generating}
                />
              </Form.Item>
            </div>

            <div className="form-row">
              <Form.Item
                name="style"
                label={t('editor.ai.style')}
                className="form-item-half"
              >
                <Select disabled={generating}>
                  <Option value="natural">{t('editor.ai.styleNatural')}</Option>
                  <Option value="cartoon">{t('editor.ai.styleCartoon')}</Option>
                  <Option value="exaggerated">{t('editor.ai.styleExaggerated')}</Option>
                  <Option value="subtle">{t('editor.ai.styleSubtle')}</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="loop"
                label={t('editor.ai.loop')}
                valuePropName="checked"
                className="form-item-half"
              >
                <Switch disabled={generating} />
              </Form.Item>
            </div>
          </Form>

          <Divider />

          <div className="model-selection">
            <div className="section-title">{t('editor.ai.modelSelection')}</div>

            <Radio.Group
              value={modelType}
              onChange={e => setModelType(e.target.value)}
              disabled={generating}
            >
              <Space direction="vertical">
                <Radio value={AIModelType.BASIC}>
                  {t('editor.ai.basicModel')}
                  <Tooltip title={t('editor.ai.basicModelDescription')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </Radio>

                <Radio value={AIModelType.ADVANCED}>
                  {t('editor.ai.advancedModel')}
                  <Tooltip title={t('editor.ai.advancedModelDescription')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </Radio>

                <Radio value={AIModelType.BERT}>
                  {t('editor.ai.bertModel')}
                  <Tooltip title={t('editor.ai.bertModelDescription')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </Radio>

                <Radio value={AIModelType.ROBERTA}>
                  {t('editor.ai.robertaModel')}
                  <Tooltip title={t('editor.ai.robertaModelDescription')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </Radio>

                <Radio value={AIModelType.DISTILBERT}>
                  {t('editor.ai.distilbertModel')}
                  <Tooltip title={t('editor.ai.distilbertModelDescription')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </Radio>

                <Radio value={AIModelType.ALBERT}>
                  {t('editor.ai.albertModel')}
                  <Tooltip title={t('editor.ai.albertModelDescription')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </Radio>

                <Radio value={AIModelType.XLNET}>
                  {t('editor.ai.xlnetModel')}
                  <Tooltip title={t('editor.ai.xlnetModelDescription')}>
                    <InfoCircleOutlined style={{ marginLeft: 8 }} />
                  </Tooltip>
                </Radio>
              </Space>
            </Radio.Group>
          </div>
        </div>

        <div className="generator-sidebar">
          <div className="action-buttons">
            <Button
              type="primary"
              icon={<RobotOutlined />}
              onClick={handleGenerate}
              loading={generating}
              block
            >
              {generating ? t('editor.ai.generating') : t('editor.ai.generate')}
            </Button>

            <Button
              icon={<SaveOutlined />}
              onClick={handleApply}
              disabled={!currentAnimationId || generating}
              block
            >
              {t('editor.ai.apply')}
            </Button>

            <Button
              icon={<ReloadOutlined />}
              onClick={handlePreview}
              disabled={!currentAnimationId || generating}
              block
            >
              {t('editor.ai.preview')}
            </Button>
          </div>

          <Divider />

          <Collapse ghost>
            <Panel
              header={
                <div className="section-title">
                  <SettingOutlined /> {t('editor.ai.advancedSettings')}
                </div>
              }
              key="advanced"
            >
              <div className="advanced-settings">
                <Form.Item
                  label={t('editor.ai.useAdvancedFeatures')}
                >
                  <Switch
                    checked={useAdvancedFeatures}
                    onChange={setUseAdvancedFeatures}
                    disabled={generating}
                  />
                </Form.Item>

                <div className="feature-list">
                  <div className="feature-item">
                    <Tooltip title={t('editor.ai.microExpressionsDescription')}>
                      <div className="feature-name">
                        {t('editor.ai.microExpressions')}
                        <InfoCircleOutlined style={{ marginLeft: 4 }} />
                      </div>
                    </Tooltip>
                  </div>

                  <div className="feature-item">
                    <Tooltip title={t('editor.ai.emotionTransitionsDescription')}>
                      <div className="feature-name">
                        {t('editor.ai.emotionTransitions')}
                        <InfoCircleOutlined style={{ marginLeft: 4 }} />
                      </div>
                    </Tooltip>
                  </div>

                  <div className="feature-item">
                    <Tooltip title={t('editor.ai.naturalVariationDescription')}>
                      <div className="feature-name">
                        {t('editor.ai.naturalVariation')}
                        <InfoCircleOutlined style={{ marginLeft: 4 }} />
                      </div>
                    </Tooltip>
                  </div>
                </div>
              </div>
            </Panel>

            <Panel
              header={
                <div className="section-title">
                  <HistoryOutlined /> {t('editor.ai.generationHistory')}
                </div>
              }
              key="history"
            >
              <div className="history-list">
                {history.length > 0 ? (
                  history.map(renderHistoryItem)
                ) : (
                  <div className="empty-history">{t('editor.ai.noHistory')}</div>
                )}
              </div>
            </Panel>
          </Collapse>
        </div>
      </div>
    </div>
  );
};

export default AIFacialAnimationGenerator;
