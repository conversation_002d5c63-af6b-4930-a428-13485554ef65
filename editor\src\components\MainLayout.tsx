/**
 * 编辑器主布局组件
 * 使用rc-dock库实现可停靠的面板布局
 */
import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Dropdown, Modal } from 'antd';
import { 
  MenuUnfoldOutlined, 
  MenuFoldOutlined, 
  AppstoreOutlined, 
  SettingOutlined, 
  QuestionCircleOutlined,
  BookOutlined,
  VideoCameraOutlined,
  UserOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import { DockLayout } from 'rc-dock';
import 'rc-dock/dist/rc-dock.css';
import { useTranslation } from 'react-i18next';

import ScenePanel from './panels/ScenePanel';
import HierarchyPanel from './panels/HierarchyPanel';
import InspectorPanel from './panels/InspectorPanel';
import ProjectPanel from './panels/ProjectPanel';
import ConsolePanel from './panels/ConsolePanel';
import HelpPanel from './panels/HelpPanel';
import TutorialPanel from './tutorials/TutorialPanel';

import './MainLayout.less';

const { Header, Sider, Content } = Layout;

interface MainLayoutProps {
  // 可以添加属性
}

const MainLayout: React.FC<MainLayoutProps> = (props) => {
  const { t, i18n } = useTranslation();
  const [collapsed, setCollapsed] = useState(false);
  const [helpVisible, setHelpVisible] = useState(false);
  const [tutorialVisible, setTutorialVisible] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language);
  
  // 初始化停靠布局配置
  const defaultLayout = {
    dockbox: {
      mode: 'horizontal',
      children: [
        {
          mode: 'vertical',
          size: 200,
          children: [
            {
              tabs: [
                { id: 'hierarchy', title: t('panels.hierarchy'), content: <HierarchyPanel /> }
              ]
            },
            {
              tabs: [
                { id: 'project', title: t('panels.project'), content: <ProjectPanel /> }
              ]
            }
          ]
        },
        {
          mode: 'vertical',
          size: 600,
          children: [
            {
              size: 400,
              tabs: [
                { id: 'scene', title: t('panels.scene'), content: <ScenePanel /> }
              ]
            },
            {
              size: 200,
              tabs: [
                { id: 'console', title: t('panels.console'), content: <ConsolePanel /> }
              ]
            }
          ]
        },
        {
          mode: 'vertical',
          size: 300,
          children: [
            {
              tabs: [
                { id: 'inspector', title: t('panels.inspector'), content: <InspectorPanel /> }
              ]
            }
          ]
        }
      ]
    }
  };
  
  const [layout, setLayout] = useState(defaultLayout);
  
  // 切换侧边栏折叠状态
  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };
  
  // 切换帮助面板可见性
  const toggleHelp = () => {
    setHelpVisible(!helpVisible);
  };
  
  // 切换教程面板可见性
  const toggleTutorial = () => {
    setTutorialVisible(!tutorialVisible);
  };
  
  // 切换语言
  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);
    setCurrentLanguage(lang);
  };
  
  // 语言菜单
  const languageMenu = (
    <Menu>
      <Menu.Item key="zh-CN" onClick={() => changeLanguage('zh-CN')}>
        中文
      </Menu.Item>
      <Menu.Item key="en" onClick={() => changeLanguage('en')}>
        English
      </Menu.Item>
    </Menu>
  );
  
  // 帮助菜单
  const helpMenu = (
    <Menu>
      <Menu.Item key="help" onClick={toggleHelp}>
        <QuestionCircleOutlined /> {t('menu.help')}
      </Menu.Item>
      <Menu.Item key="tutorials" onClick={toggleTutorial}>
        <VideoCameraOutlined /> {t('menu.tutorials')}
      </Menu.Item>
      <Menu.Item key="documentation">
        <BookOutlined /> {t('menu.documentation')}
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="about">
        <InfoCircleOutlined /> {t('menu.about')}
      </Menu.Item>
    </Menu>
  );
  
  return (
    <Layout className="main-layout">
      <Header className="header">
        <div className="logo">IR Editor</div>
        <Menu theme="dark" mode="horizontal" defaultSelectedKeys={['1']}>
          <Menu.Item key="1">{t('menu.file')}</Menu.Item>
          <Menu.Item key="2">{t('menu.edit')}</Menu.Item>
          <Menu.Item key="3">{t('menu.view')}</Menu.Item>
          <Menu.Item key="4">{t('menu.window')}</Menu.Item>
        </Menu>
        <div className="header-right">
          <Dropdown overlay={languageMenu} placement="bottomRight">
            <Button type="text" icon={<GlobalOutlined />} style={{ color: 'white' }}>
              {currentLanguage === 'zh-CN' ? '中文' : 'English'}
            </Button>
          </Dropdown>
          <Dropdown overlay={helpMenu} placement="bottomRight">
            <Button type="text" icon={<QuestionCircleOutlined />} style={{ color: 'white' }} />
          </Dropdown>
          <Button type="text" icon={<UserOutlined />} style={{ color: 'white' }} />
        </div>
      </Header>
      <Layout>
        <Sider trigger={null} collapsible collapsed={collapsed} width={200}>
          <Button 
            type="text" 
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={toggleCollapsed}
            style={{ margin: '16px', color: 'white' }}
          />
          <Menu theme="dark" mode="inline" defaultSelectedKeys={['1']}>
            <Menu.Item key="1" icon={<AppstoreOutlined />}>
              {t('menu.project')}
            </Menu.Item>
            <Menu.Item key="2" icon={<SettingOutlined />}>
              {t('menu.settings')}
            </Menu.Item>
          </Menu>
        </Sider>
        <Content className="content">
          <DockLayout
            defaultLayout={layout}
            style={{ position: 'absolute', left: 0, top: 0, right: 0, bottom: 0 }}
          />
        </Content>
      </Layout>
      
      {/* 帮助面板模态框 */}
      <Modal
        title={t('help.title')}
        visible={helpVisible}
        onCancel={toggleHelp}
        footer={null}
        width={800}
        bodyStyle={{ height: '600px', padding: 0, overflow: 'hidden' }}
      >
        <HelpPanel />
      </Modal>
      
      {/* 教程面板模态框 */}
      <Modal
        title={t('tutorials.title')}
        visible={tutorialVisible}
        onCancel={toggleTutorial}
        footer={null}
        width={800}
        bodyStyle={{ height: '600px', padding: 0, overflow: 'hidden' }}
      >
        <TutorialPanel onClose={toggleTutorial} />
      </Modal>
    </Layout>
  );
};

export default MainLayout;
