/**
 * 口型同步工作线程
 * 用于在后台线程中处理音频分析，减少主线程负担
 * 支持高级分析功能，包括MFCC、LPC、频谱图分析等
 */

// 配置
let config = {
  fftSize: 1024,
  sampleRate: 44100,
  volumeThreshold: 0.01,
  frequencyBandBoundaries: [],
  useMFCC: false,
  useLPC: false,
  useSpectrogram: false,
  useContextPrediction: true,
  contextWindowSize: 5,
  numMelFilters: 26,
  numCepstralCoeffs: 13,
  usePhonemeRecognition: false
};

// 频率带数据
let frequencyBands = null;

// 梅尔滤波器组
let melFilterBank = [];

// 口型历史
let visemeHistory = [];

// 频谱图历史
let spectrogramHistory = [];

// 音素到口型映射
const phonemeToVisemeMap = new Map();

// 口型类型枚举（与主线程保持一致）
const VisemeType = {
  SILENT: 'silent',
  PP: 'pp',
  FF: 'ff',
  TH: 'th',
  DD: 'dd',
  KK: 'kk',
  CH: 'ch',
  SS: 'ss',
  NN: 'nn',
  RR: 'rr',
  AA: 'aa',
  EE: 'ee',
  IH: 'ih',
  OH: 'oh',
  OU: 'ou'
};

/**
 * 消息处理
 */
self.onmessage = function(event) {
  const data = event.data;

  switch (data.command) {
    case 'init':
      // 初始化配置
      initConfig(data.config);
      break;

    case 'analyze':
      // 分析音频数据
      const spectrum = new Float32Array(data.spectrum);
      const result = analyzeAudio(spectrum);

      // 返回结果
      self.postMessage({
        command: 'analyzeResult',
        viseme: result
      });
      break;
  }
};

/**
 * 初始化配置
 * @param {Object} newConfig 新配置
 */
function initConfig(newConfig) {
  config = { ...config, ...newConfig };

  // 初始化频率带
  if (config.frequencyBandBoundaries && config.frequencyBandBoundaries.length > 0) {
    const numBands = config.frequencyBandBoundaries.length - 1;
    frequencyBands = new Float32Array(numBands);
  } else {
    // 如果没有提供频率带边界，创建默认的
    const numBands = 15; // 增加频率带数量，提高精度
    frequencyBands = new Float32Array(numBands);

    // 计算频率带边界
    const minFreq = 80;    // 人声最低频率约80Hz
    const maxFreq = 8000;  // 人声最高频率约8kHz

    config.frequencyBandBoundaries = [];

    // 使用梅尔刻度进行频率带划分，更符合人耳感知
    const melMin = hzToMel(minFreq);
    const melMax = hzToMel(maxFreq);

    for (let i = 0; i <= numBands; i++) {
      // 梅尔刻度均匀分布
      const t = i / numBands;
      const melFreq = melMin + t * (melMax - melMin);
      const freq = melToHz(melFreq);
      config.frequencyBandBoundaries.push(freq);
    }
  }

  // 初始化梅尔滤波器组
  if (config.useMFCC) {
    initMelFilterBank();
  }

  // 初始化音素到口型映射
  initPhonemeToVisemeMap();

  // 确认初始化完成
  self.postMessage({
    command: 'initComplete',
    numBands: frequencyBands.length
  });
}

/**
 * 初始化梅尔滤波器组
 */
function initMelFilterBank() {
  const numFilters = config.numMelFilters;
  const fftSize = config.fftSize;
  const sampleRate = config.sampleRate;

  // 计算梅尔刻度范围
  const lowFreq = 80;
  const highFreq = sampleRate / 2;
  const lowMel = hzToMel(lowFreq);
  const highMel = hzToMel(highFreq);

  // 创建梅尔滤波器组
  melFilterBank = new Array(numFilters);

  // 在梅尔刻度上均匀分布滤波器中心点
  const melPoints = new Array(numFilters + 2);
  for (let i = 0; i < melPoints.length; i++) {
    melPoints[i] = lowMel + (highMel - lowMel) / (numFilters + 1) * i;
  }

  // 转换回Hz
  const fftFreqs = new Array(melPoints.length);
  for (let i = 0; i < melPoints.length; i++) {
    fftFreqs[i] = melToHz(melPoints[i]);
  }

  // 计算滤波器组系数
  const binFreqs = new Array(fftSize / 2 + 1);
  for (let i = 0; i < binFreqs.length; i++) {
    binFreqs[i] = i * sampleRate / fftSize;
  }

  // 创建每个滤波器
  for (let i = 0; i < numFilters; i++) {
    melFilterBank[i] = new Float32Array(fftSize / 2 + 1);

    for (let j = 0; j < binFreqs.length; j++) {
      const freq = binFreqs[j];

      // 三角滤波器
      if (freq < fftFreqs[i] || freq > fftFreqs[i + 2]) {
        melFilterBank[i][j] = 0;
      } else if (freq <= fftFreqs[i + 1]) {
        melFilterBank[i][j] = (freq - fftFreqs[i]) / (fftFreqs[i + 1] - fftFreqs[i]);
      } else {
        melFilterBank[i][j] = (fftFreqs[i + 2] - freq) / (fftFreqs[i + 2] - fftFreqs[i + 1]);
      }
    }
  }
}

/**
 * 初始化音素到口型映射
 */
function initPhonemeToVisemeMap() {
  // 英语音素到口型的映射
  phonemeToVisemeMap.set('p', VisemeType.PP);
  phonemeToVisemeMap.set('b', VisemeType.PP);
  phonemeToVisemeMap.set('m', VisemeType.PP);

  phonemeToVisemeMap.set('f', VisemeType.FF);
  phonemeToVisemeMap.set('v', VisemeType.FF);

  phonemeToVisemeMap.set('th', VisemeType.TH);
  phonemeToVisemeMap.set('dh', VisemeType.TH);

  phonemeToVisemeMap.set('t', VisemeType.DD);
  phonemeToVisemeMap.set('d', VisemeType.DD);
  phonemeToVisemeMap.set('n', VisemeType.DD);

  phonemeToVisemeMap.set('k', VisemeType.KK);
  phonemeToVisemeMap.set('g', VisemeType.KK);
  phonemeToVisemeMap.set('ng', VisemeType.KK);

  phonemeToVisemeMap.set('ch', VisemeType.CH);
  phonemeToVisemeMap.set('j', VisemeType.CH);
  phonemeToVisemeMap.set('sh', VisemeType.CH);
  phonemeToVisemeMap.set('zh', VisemeType.CH);

  phonemeToVisemeMap.set('s', VisemeType.SS);
  phonemeToVisemeMap.set('z', VisemeType.SS);

  phonemeToVisemeMap.set('l', VisemeType.NN);
  phonemeToVisemeMap.set('n', VisemeType.NN);

  phonemeToVisemeMap.set('r', VisemeType.RR);

  phonemeToVisemeMap.set('a', VisemeType.AA);
  phonemeToVisemeMap.set('ae', VisemeType.AA);

  phonemeToVisemeMap.set('e', VisemeType.EE);
  phonemeToVisemeMap.set('eh', VisemeType.EE);

  phonemeToVisemeMap.set('i', VisemeType.IH);
  phonemeToVisemeMap.set('ih', VisemeType.IH);
  phonemeToVisemeMap.set('y', VisemeType.IH);

  phonemeToVisemeMap.set('o', VisemeType.OH);
  phonemeToVisemeMap.set('aw', VisemeType.OH);

  phonemeToVisemeMap.set('u', VisemeType.OU);
  phonemeToVisemeMap.set('uw', VisemeType.OU);
  phonemeToVisemeMap.set('w', VisemeType.OU);
}

/**
 * 将Hz转换为梅尔刻度
 * @param {number} hz 频率(Hz)
 * @returns {number} 梅尔刻度值
 */
function hzToMel(hz) {
  return 2595 * Math.log10(1 + hz / 700);
}

/**
 * 将梅尔刻度转换为Hz
 * @param {number} mel 梅尔刻度值
 * @returns {number} 频率(Hz)
 */
function melToHz(mel) {
  return 700 * (Math.pow(10, mel / 2595) - 1);
}

/**
 * 分析音频数据
 * @param {Float32Array} spectrum 频谱数据
 * @returns {string} 口型类型
 */
function analyzeAudio(spectrum) {
  // 计算RMS
  const rms = calculateRMS(spectrum);

  // 如果音量太小，则设置为静默
  if (rms < config.volumeThreshold) {
    const silentViseme = VisemeType.SILENT;
    updateVisemeHistory(silentViseme);
    return silentViseme;
  }

  // 更新频谱图历史
  if (config.useSpectrogram) {
    updateSpectrogramHistory(spectrum);
  }

  // 根据配置选择分析方法
  let viseme;

  if (config.useMFCC) {
    // 使用MFCC分析
    viseme = analyzeMFCC(spectrum);
  } else if (config.useLPC) {
    // 使用LPC分析
    viseme = analyzeLPC(spectrum);
  } else if (config.usePhonemeRecognition) {
    // 使用音素识别
    const phoneme = recognizePhoneme(spectrum);
    if (phoneme && phonemeToVisemeMap.has(phoneme)) {
      viseme = phonemeToVisemeMap.get(phoneme);
    } else {
      // 如果音素识别失败，回退到频率带分析
      calculateFrequencyBands(spectrum);
      viseme = analyzeFrequencyBands();
    }
  } else {
    // 使用频率带分析
    calculateFrequencyBands(spectrum);
    viseme = analyzeFrequencyBands();
  }

  // 使用上下文预测改进结果
  if (config.useContextPrediction && visemeHistory.length > 0) {
    viseme = predictVisemeFromContext(viseme);
  }

  // 更新历史
  updateVisemeHistory(viseme);

  return viseme;
}

/**
 * 更新口型历史
 * @param {string} viseme 口型
 */
function updateVisemeHistory(viseme) {
  // 添加到历史
  visemeHistory.push(viseme);

  // 限制历史大小
  if (visemeHistory.length > config.contextWindowSize) {
    visemeHistory.shift();
  }
}

/**
 * 更新频谱图历史
 * @param {Float32Array} spectrum 频谱数据
 */
function updateSpectrogramHistory(spectrum) {
  // 添加到历史
  spectrogramHistory.push(new Float32Array(spectrum));

  // 限制历史大小
  if (spectrogramHistory.length > config.contextWindowSize * 2) {
    spectrogramHistory.shift();
  }
}

/**
 * 基于上下文预测口型
 * @param {string} currentViseme 当前口型
 * @returns {string} 预测的口型
 */
function predictVisemeFromContext(currentViseme) {
  if (visemeHistory.length === 0) {
    return currentViseme;
  }

  // 获取上一个口型
  const previousViseme = visemeHistory[visemeHistory.length - 1];

  // 如果当前口型与上一个相同，增加置信度
  if (currentViseme === previousViseme) {
    return currentViseme;
  }

  // 简单的上下文规则
  // 实际应用中，这里可以使用更复杂的统计模型或规则

  // 如果上一个是静默，当前不是静默，可能是噪声，保持静默
  if (previousViseme === VisemeType.SILENT &&
      currentViseme !== VisemeType.SILENT &&
      visemeHistory.length < 3) {
    return VisemeType.SILENT;
  }

  // 如果上一个不是静默，当前是静默，可能是短暂停顿，保持上一个口型
  if (previousViseme !== VisemeType.SILENT &&
      currentViseme === VisemeType.SILENT &&
      visemeHistory.length > 3) {
    return previousViseme;
  }

  // 其他情况使用当前预测
  return currentViseme;
}

/**
 * 使用MFCC分析音频
 * @param {Float32Array} spectrum 频谱数据
 * @returns {string} 口型类型
 */
function analyzeMFCC(spectrum) {
  // 提取MFCC特征
  const mfcc = extractMFCC(spectrum);

  // 基于MFCC特征确定口型
  return mapMFCCToViseme(mfcc);
}

/**
 * 提取MFCC特征
 * @param {Float32Array} spectrum 频谱数据
 * @returns {Float32Array} MFCC特征
 */
function extractMFCC(spectrum) {
  // 计算梅尔滤波器组输出
  const melEnergies = new Float32Array(config.numMelFilters);

  for (let i = 0; i < config.numMelFilters; i++) {
    let sum = 0;
    for (let j = 0; j < spectrum.length; j++) {
      // 将dB转换为功率谱
      const power = Math.pow(10, spectrum[j] / 10);
      sum += power * melFilterBank[i][j];
    }
    // 取对数
    melEnergies[i] = sum > 0 ? Math.log(sum) : 0;
  }

  // 计算DCT（离散余弦变换）得到MFCC
  const mfcc = new Float32Array(config.numCepstralCoeffs);

  for (let i = 0; i < config.numCepstralCoeffs; i++) {
    let sum = 0;
    for (let j = 0; j < config.numMelFilters; j++) {
      sum += melEnergies[j] * Math.cos(Math.PI * i * (j + 0.5) / config.numMelFilters);
    }
    mfcc[i] = sum;
  }

  return mfcc;
}

/**
 * 将MFCC映射到口型
 * @param {Float32Array} mfcc MFCC特征
 * @returns {string} 口型类型
 */
function mapMFCCToViseme(mfcc) {
  // 简化的映射方法
  // 实际应用中，这里可以使用预训练的分类器

  // 使用前几个系数的特征来区分主要口型
  const c1 = mfcc[1]; // 第二个系数，通常与开口度相关
  const c2 = mfcc[2]; // 第三个系数，通常与前后位置相关

  // 基于MFCC特征的简单规则
  if (c1 < -10) {
    return VisemeType.PP; // 闭合口型
  } else if (c1 < -5) {
    return VisemeType.FF; // 半开口型
  } else if (c1 < 0) {
    if (c2 > 0) {
      return VisemeType.TH; // 舌齿音
    } else {
      return VisemeType.DD; // 舌尖音
    }
  } else if (c1 < 5) {
    if (c2 > 0) {
      return VisemeType.KK; // 舌根音
    } else {
      return VisemeType.CH; // 齿擦音
    }
  } else if (c1 < 10) {
    if (c2 > 0) {
      return VisemeType.EE; // 前元音
    } else {
      return VisemeType.IH; // 高元音
    }
  } else {
    return VisemeType.AA; // 开口元音
  }
}

/**
 * 使用LPC分析音频
 * @param {Float32Array} spectrum 频谱数据
 * @returns {string} 口型类型
 */
function analyzeLPC(spectrum) {
  // LPC分析需要时域信号
  // 这里简化处理，实际应用中需要从频谱转换回时域或直接使用时域信号

  // 简单返回基于频率带的结果
  calculateFrequencyBands(spectrum);
  return analyzeFrequencyBands();
}

/**
 * 识别音素
 * @param {Float32Array} spectrum 频谱数据
 * @returns {string|null} 识别的音素
 */
function recognizePhoneme(spectrum) {
  // 这里是简化的音素识别实现
  // 实际应用中，应该使用更复杂的音素识别算法或模型

  // 使用MFCC特征进行简单分类
  const mfcc = extractMFCC(spectrum);

  // 基于MFCC特征的简单规则
  const c1 = mfcc[1]; // 第二个系数，通常与开口度相关
  const c2 = mfcc[2]; // 第三个系数，通常与前后位置相关

  // 简单的规则分类
  if (c1 < -10) {
    return 'p'; // 闭合音素
  } else if (c1 < -5) {
    return 'f'; // 摩擦音素
  } else if (c1 < 0) {
    if (c2 > 0) {
      return 'th'; // 舌齿音素
    } else {
      return 'd'; // 舌尖音素
    }
  } else if (c1 < 5) {
    if (c2 > 0) {
      return 'k'; // 舌根音素
    } else {
      return 'ch'; // 齿擦音素
    }
  } else if (c1 < 10) {
    if (c2 > 0) {
      return 'e'; // 前元音
    } else {
      return 'i'; // 高元音
    }
  } else {
    return 'a'; // 开口元音
  }
}

/**
 * 计算RMS（均方根）
 * @param {Float32Array} spectrum 频谱数据
 * @returns {number} RMS值
 */
function calculateRMS(spectrum) {
  let sum = 0;
  for (let i = 0; i < spectrum.length; i++) {
    sum += spectrum[i] * spectrum[i];
  }
  return Math.sqrt(sum / spectrum.length);
}

/**
 * 计算频率带能量
 * @param {Float32Array} spectrum 频谱数据
 */
function calculateFrequencyBands(spectrum) {
  const binCount = spectrum.length;
  const binWidth = config.sampleRate / (binCount * 2);

  // 重置频率带
  for (let i = 0; i < frequencyBands.length; i++) {
    frequencyBands[i] = 0;
  }

  // 计算每个频率带的能量
  for (let i = 0; i < binCount; i++) {
    const frequency = i * binWidth;

    // 找到对应的频率带
    for (let j = 0; j < config.frequencyBandBoundaries.length - 1; j++) {
      if (frequency >= config.frequencyBandBoundaries[j] && frequency < config.frequencyBandBoundaries[j + 1]) {
        // 转换dB到线性刻度并累加
        frequencyBands[j] += Math.pow(10, spectrum[i] / 20);
        break;
      }
    }
  }

  // 归一化
  for (let i = 0; i < frequencyBands.length; i++) {
    // 计算该频率带包含的bin数量
    const startBin = Math.floor(config.frequencyBandBoundaries[i] / binWidth);
    const endBin = Math.floor(config.frequencyBandBoundaries[i + 1] / binWidth);
    const binCount = endBin - startBin;

    if (binCount > 0) {
      frequencyBands[i] /= binCount;
    }
  }
}

/**
 * 分析频率带并确定口型
 * @returns {string} 口型类型
 */
function analyzeFrequencyBands() {
  if (!frequencyBands || frequencyBands.length === 0) {
    return VisemeType.SILENT;
  }

  // 找出能量最大的频率带
  let maxEnergy = 0;
  let maxBandIndex = -1;

  for (let i = 0; i < frequencyBands.length; i++) {
    if (frequencyBands[i] > maxEnergy) {
      maxEnergy = frequencyBands[i];
      maxBandIndex = i;
    }
  }

  // 根据主导频率带确定口型
  if (maxBandIndex === -1) {
    return VisemeType.SILENT;
  }

  // 低频带 (80-250Hz)
  if (maxBandIndex === 0) {
    return VisemeType.PP; // 闭合口型，如 p, b, m
  }
  // 中低频带 (250-500Hz)
  else if (maxBandIndex === 1) {
    return VisemeType.FF; // 半开口型，如 f, v
  }
  // 中频带 (500-1000Hz)
  else if (maxBandIndex === 2) {
    return VisemeType.TH; // 舌齿音，如 th
  }
  // 中高频带 (1000-2000Hz)
  else if (maxBandIndex === 3) {
    return VisemeType.DD; // 舌尖音，如 d, t, n
  }
  // 高频带 (2000-4000Hz)
  else if (maxBandIndex === 4) {
    return VisemeType.SS; // 摩擦音，如 s, z
  }
  // 更高频带 (4000-8000Hz)
  else {
    return VisemeType.AA; // 开口元音，如 a
  }
}
