/**
 * 动画状态机序列化器
 * 用于序列化和反序列化动画状态机数据
 */
import { AnimationState, AnimationStateMachine, BlendAnimationState, SingleAnimationState, TransitionRule } from './AnimationStateMachine';
import { Animator } from './Animator';

/**
 * 状态机序列化数据
 */
export interface AnimationStateMachineData {
  /** 状态列表 */
  states: AnimationStateData[];
  /** 转换规则列表 */
  transitions: TransitionRuleData[];
  /** 参数列表 */
  parameters: ParameterData[];
  /** 当前状态名称 */
  currentState?: string;
}

/**
 * 状态序列化数据
 */
export interface AnimationStateData {
  /** 状态名称 */
  name: string;
  /** 状态类型 */
  type: string;
  /** 状态位置（编辑器中的位置） */
  position?: { x: number; y: number };
  /** 状态颜色（编辑器中的颜色） */
  color?: string;
  /** 状态数据 */
  [key: string]: any;
}

/**
 * 转换规则序列化数据
 */
export interface TransitionRuleData {
  /** 源状态名称 */
  from: string;
  /** 目标状态名称 */
  to: string;
  /** 转换条件表达式 */
  conditionExpression: string;
  /** 转换持续时间（秒） */
  duration: number;
  /** 是否可以中断 */
  canInterrupt: boolean;
  /** 转换曲线类型 */
  curveType?: string;
  /** 优先级 */
  priority?: number;
}

/**
 * 参数数据
 */
export interface ParameterData {
  /** 参数名称 */
  name: string;
  /** 参数类型 */
  type: 'number' | 'boolean' | 'string' | 'vector2' | 'vector3';
  /** 参数默认值 */
  defaultValue: any;
  /** 参数最小值（仅适用于数值类型） */
  minValue?: number;
  /** 参数最大值（仅适用于数值类型） */
  maxValue?: number;
}

/**
 * 动画状态机序列化器
 */
export class AnimationStateMachineSerializer {
  /**
   * 序列化状态机
   * @param stateMachine 状态机
   * @returns 序列化数据
   */
  public static serialize(stateMachine: AnimationStateMachine): AnimationStateMachineData {
    const data: AnimationStateMachineData = {
      states: [],
      transitions: [],
      parameters: []
    };

    // 序列化状态
    for (const state of stateMachine.getStates()) {
      const stateData: AnimationStateData = {
        name: state.name,
        type: state.type
      };

      // 根据状态类型添加特定数据
      if (state.type === 'SingleAnimationState') {
        const singleState = state as SingleAnimationState;
        stateData.clipName = singleState.clipName;
        stateData.loop = singleState.loop;
        stateData.clamp = singleState.clamp;
      } else if (state.type === 'BlendAnimationState') {
        const blendState = state as BlendAnimationState;
        stateData.parameterName = blendState.parameterName;
        stateData.blendSpaceType = blendState.blendSpaceType;
        stateData.blendSpaceConfig = blendState.blendSpaceConfig;
      }

      // 添加编辑器相关数据
      if (state.position) {
        stateData.position = state.position;
      }
      if (state.color) {
        stateData.color = state.color;
      }

      data.states.push(stateData);
    }

    // 序列化转换规则
    for (const transition of stateMachine.getTransitions()) {
      const transitionData: TransitionRuleData = {
        from: transition.from,
        to: transition.to,
        conditionExpression: transition.conditionExpression || '',
        duration: transition.duration,
        canInterrupt: transition.canInterrupt
      };

      if (transition.curveType) {
        transitionData.curveType = transition.curveType;
      }
      if (transition.priority !== undefined) {
        transitionData.priority = transition.priority;
      }

      data.transitions.push(transitionData);
    }

    // 序列化参数
    for (const [name, value] of stateMachine.getParameters()) {
      const paramData: ParameterData = {
        name,
        type: typeof value === 'number' ? 'number' :
              typeof value === 'boolean' ? 'boolean' :
              typeof value === 'string' ? 'string' :
              Array.isArray(value) && value.length === 2 ? 'vector2' :
              Array.isArray(value) && value.length === 3 ? 'vector3' :
              'string',
        defaultValue: value
      };

      // 如果有参数元数据，添加到参数数据中
      const metadata = stateMachine.getParameterMetadata(name);
      if (metadata) {
        if (metadata.minValue !== undefined) paramData.minValue = metadata.minValue;
        if (metadata.maxValue !== undefined) paramData.maxValue = metadata.maxValue;
      }

      data.parameters.push(paramData);
    }

    // 添加当前状态
    const currentState = stateMachine.getCurrentState();
    if (currentState) {
      data.currentState = currentState.name;
    }

    return data;
  }

  /**
   * 反序列化状态机
   * @param data 序列化数据
   * @param animator 动画控制器
   * @returns 状态机
   */
  public static deserialize(data: AnimationStateMachineData, animator: Animator): AnimationStateMachine {
    const stateMachine = new AnimationStateMachine(animator);

    // 添加状态
    for (const stateData of data.states) {
      let state: AnimationState;

      if (stateData.type === 'SingleAnimationState') {
        state = {
          name: stateData.name,
          type: stateData.type,
          clipName: stateData.clipName,
          loop: stateData.loop,
          clamp: stateData.clamp
        } as SingleAnimationState;
      } else if (stateData.type === 'BlendAnimationState') {
        state = {
          name: stateData.name,
          type: stateData.type,
          parameterName: stateData.parameterName,
          blendSpaceType: stateData.blendSpaceType,
          blendSpaceConfig: stateData.blendSpaceConfig
        } as BlendAnimationState;
      } else {
        // 默认为基本状态
        state = {
          name: stateData.name,
          type: stateData.type
        };
      }

      // 添加编辑器相关数据
      if (stateData.position) {
        (state as any).position = stateData.position;
      }
      if (stateData.color) {
        (state as any).color = stateData.color;
      }

      stateMachine.addState(state);
    }

    // 添加转换规则
    for (const transitionData of data.transitions) {
      const transition: TransitionRule = {
        from: transitionData.from,
        to: transitionData.to,
        condition: () => false, // 初始条件为false，后面会根据表达式更新
        conditionExpression: transitionData.conditionExpression,
        duration: transitionData.duration,
        canInterrupt: transitionData.canInterrupt
      };

      if (transitionData.curveType) {
        (transition as any).curveType = transitionData.curveType;
      }
      if (transitionData.priority !== undefined) {
        (transition as any).priority = transitionData.priority;
      }

      // 解析条件表达式
      if (transitionData.conditionExpression) {
        try {
          // 创建一个函数，接受stateMachine作为参数
          const conditionFunc = new Function('stateMachine', `
            return ${transitionData.conditionExpression};
          `);
          
          transition.condition = () => {
            try {
              return conditionFunc(stateMachine);
            } catch (error) {
              console.error(`条件表达式执行错误: ${error}`);
              return false;
            }
          };
        } catch (error) {
          console.error(`条件表达式解析错误: ${error}`);
        }
      }

      stateMachine.addTransition(transition);
    }

    // 设置参数
    for (const paramData of data.parameters) {
      stateMachine.setParameter(paramData.name, paramData.defaultValue);
      
      // 设置参数元数据
      if (paramData.minValue !== undefined || paramData.maxValue !== undefined) {
        stateMachine.setParameterMetadata(paramData.name, {
          minValue: paramData.minValue,
          maxValue: paramData.maxValue
        });
      }
    }

    // 设置当前状态
    if (data.currentState) {
      stateMachine.setCurrentState(data.currentState);
    }

    return stateMachine;
  }
}
