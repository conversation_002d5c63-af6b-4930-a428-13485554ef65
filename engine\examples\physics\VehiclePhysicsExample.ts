/**
 * 车辆物理示例
 * 展示如何使用物理系统创建可驾驶的车辆
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { Engine } from '../../src/core/Engine';
import { Entity } from '../../src/core/Entity';
import { Scene } from '../../src/scene/Scene';
import { Vector3 } from '../../src/math/Vector3';
import { Quaternion } from '../../src/math/Quaternion';
import { 
  PhysicsSystem, 
  PhysicsBodyComponent, 
  PhysicsColliderComponent,
  BodyType,
  ColliderType,
  PhysicsMaterialFactory,
  PhysicsConstraintComponent
} from '../../src/physics';
import { InputSystem } from '../../src/input/InputSystem';
import { KeyCode } from '../../src/input/KeyCode';

/**
 * 车辆物理示例
 */
export class VehiclePhysicsExample {
  /** 引擎实例 */
  private engine: Engine;
  
  /** 场景实例 */
  private scene: Scene;
  
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;
  
  /** 输入系统 */
  private inputSystem: InputSystem;
  
  /** 地面实体 */
  private ground: Entity;
  
  /** 车辆底盘实体 */
  private chassis: Entity;
  
  /** 车轮实体列表 */
  private wheels: Entity[] = [];
  
  /** 车辆配置 */
  private vehicleConfig = {
    /** 底盘质量 */
    chassisMass: 800,
    /** 车轮质量 */
    wheelMass: 30,
    /** 车轮半径 */
    wheelRadius: 0.4,
    /** 车轮宽度 */
    wheelWidth: 0.3,
    /** 悬挂刚度 */
    suspensionStiffness: 30,
    /** 悬挂休息长度 */
    suspensionRestLength: 0.3,
    /** 悬挂阻尼 */
    suspensionDamping: 4.4,
    /** 悬挂压缩 */
    suspensionCompression: 2.3,
    /** 滚动影响 */
    rollInfluence: 0.01,
    /** 最大悬挂行程 */
    maxSuspensionTravel: 0.3,
    /** 最大悬挂力 */
    maxSuspensionForce: 100000,
    /** 摩擦滑移 */
    frictionSlip: 30,
    /** 最大转向角度 */
    maxSteeringAngle: Math.PI / 6,
    /** 最大驱动力 */
    maxDriveForce: 1000,
    /** 最大刹车力 */
    maxBrakeForce: 100,
    /** 转向速度 */
    steeringSpeed: 5,
    /** 转向恢复速度 */
    steeringReturnSpeed: 10
  };
  
  /** 车轮位置 */
  private wheelPositions = [
    { x: -1, y: 0, z: -1.5 },  // 左前
    { x: 1, y: 0, z: -1.5 },   // 右前
    { x: -1, y: 0, z: 1.5 },   // 左后
    { x: 1, y: 0, z: 1.5 }     // 右后
  ];
  
  /** 当前转向角度 */
  private currentSteeringAngle: number = 0;
  
  /** 当前驱动力 */
  private currentDriveForce: number = 0;
  
  /** 当前刹车力 */
  private currentBrakeForce: number = 0;
  
  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建车辆物理示例
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 创建场景
    this.scene = new Scene('车辆物理示例场景');
    
    // 创建物理系统
    this.physicsSystem = new PhysicsSystem({
      gravity: { x: 0, y: -9.82, z: 0 },
      debug: true
    });
    
    // 创建输入系统
    this.inputSystem = new InputSystem();
    
    // 添加系统到引擎
    this.engine.addSystem(this.physicsSystem);
    this.engine.addSystem(this.inputSystem);
    
    // 初始化物理材质工厂
    PhysicsMaterialFactory.initialize();
    
    // 创建地面
    this.ground = this.createGround();
    
    // 添加地面到场景
    this.scene.addEntity(this.ground);
    
    // 注册更新回调
    this.engine.onUpdate.add(this.update.bind(this));
  }

  /**
   * 初始化示例
   */
  public initialize(): void {
    if (this.initialized) return;
    
    // 创建车辆
    this.createVehicle();
    
    // 创建障碍物
    this.createObstacles();
    
    // 设置场景为活跃场景
    this.engine.getWorld().setActiveScene(this.scene);
    
    this.initialized = true;
  }
  
  /**
   * 更新
   * @param deltaTime 时间增量
   */
  private update(deltaTime: number): void {
    if (!this.initialized) return;
    
    // 处理输入
    this.handleInput(deltaTime);
    
    // 应用车辆控制
    this.applyVehicleControl();
  }
  
  /**
   * 处理输入
   * @param deltaTime 时间增量
   */
  private handleInput(deltaTime: number): void {
    // 转向控制
    if (this.inputSystem.isKeyDown(KeyCode.A) || this.inputSystem.isKeyDown(KeyCode.LEFT)) {
      // 左转
      this.currentSteeringAngle = Math.min(
        this.currentSteeringAngle + this.vehicleConfig.steeringSpeed * deltaTime,
        this.vehicleConfig.maxSteeringAngle
      );
    } else if (this.inputSystem.isKeyDown(KeyCode.D) || this.inputSystem.isKeyDown(KeyCode.RIGHT)) {
      // 右转
      this.currentSteeringAngle = Math.max(
        this.currentSteeringAngle - this.vehicleConfig.steeringSpeed * deltaTime,
        -this.vehicleConfig.maxSteeringAngle
      );
    } else {
      // 回正
      if (this.currentSteeringAngle > 0) {
        this.currentSteeringAngle = Math.max(
          0,
          this.currentSteeringAngle - this.vehicleConfig.steeringReturnSpeed * deltaTime
        );
      } else if (this.currentSteeringAngle < 0) {
        this.currentSteeringAngle = Math.min(
          0,
          this.currentSteeringAngle + this.vehicleConfig.steeringReturnSpeed * deltaTime
        );
      }
    }
    
    // 加速控制
    if (this.inputSystem.isKeyDown(KeyCode.W) || this.inputSystem.isKeyDown(KeyCode.UP)) {
      // 前进
      this.currentDriveForce = this.vehicleConfig.maxDriveForce;
      this.currentBrakeForce = 0;
    } else if (this.inputSystem.isKeyDown(KeyCode.S) || this.inputSystem.isKeyDown(KeyCode.DOWN)) {
      // 后退
      this.currentDriveForce = -this.vehicleConfig.maxDriveForce / 2;
      this.currentBrakeForce = 0;
    } else {
      // 停止
      this.currentDriveForce = 0;
    }
    
    // 刹车控制
    if (this.inputSystem.isKeyDown(KeyCode.SPACE)) {
      // 刹车
      this.currentBrakeForce = this.vehicleConfig.maxBrakeForce;
      this.currentDriveForce = 0;
    } else if (!this.inputSystem.isKeyDown(KeyCode.W) && 
               !this.inputSystem.isKeyDown(KeyCode.UP) && 
               !this.inputSystem.isKeyDown(KeyCode.S) && 
               !this.inputSystem.isKeyDown(KeyCode.DOWN)) {
      // 自动刹车
      this.currentBrakeForce = this.vehicleConfig.maxBrakeForce * 0.1;
    } else {
      // 不刹车
      this.currentBrakeForce = 0;
    }
  }
  
  /**
   * 应用车辆控制
   */
  private applyVehicleControl(): void {
    if (!this.chassis || this.wheels.length !== 4) return;
    
    // 获取底盘物理体
    const chassisBody = this.chassis.getComponent(PhysicsBodyComponent);
    if (!chassisBody) return;
    
    // 获取车轮物理体
    const wheelBodies = this.wheels.map(wheel => wheel.getComponent(PhysicsBodyComponent));
    if (wheelBodies.some(body => !body)) return;
    
    // 应用转向
    // 前轮转向
    for (let i = 0; i < 2; i++) {
      const wheel = this.wheels[i];
      const wheelTransform = wheel.getTransform();
      wheelTransform.setRotation(0, this.currentSteeringAngle, 0);
    }
    
    // 应用驱动力
    // 后轮驱动
    for (let i = 2; i < 4; i++) {
      const wheelBody = wheelBodies[i];
      
      // 获取车轮前向方向
      const wheelForward = new Vector3(0, 0, 1);
      wheelForward.applyQuaternion(this.wheels[i].getTransform().getWorldQuaternion());
      
      // 应用驱动力
      wheelBody.applyForce(wheelForward.multiplyScalar(this.currentDriveForce));
    }
    
    // 应用刹车力
    for (let i = 0; i < 4; i++) {
      const wheelBody = wheelBodies[i];
      
      // 获取车轮线速度
      const wheelVelocity = wheelBody.getLinearVelocity();
      
      // 应用与速度方向相反的刹车力
      if (wheelVelocity.length() > 0.1) {
        const brakeForce = wheelVelocity.clone().normalize().multiplyScalar(-this.currentBrakeForce);
        wheelBody.applyForce(brakeForce);
      }
    }
  }

  /**
   * 创建地面
   * @returns 地面实体
   */
  private createGround(): Entity {
    // 创建地面实体
    const ground = new Entity('ground');
    
    // 添加变换组件
    const transform = ground.getTransform();
    transform.setPosition(0, -0.5, 0);
    transform.setScale(100, 1, 100);
    
    // 创建地面网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ color: 0x808080 });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    ground.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    ground.addComponent(new PhysicsBodyComponent({
      type: BodyType.STATIC,
      mass: 0
    }));
    
    // 添加碰撞器组件
    ground.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: 50, y: 0.5, z: 50 }
      }
    }));
    
    return ground;
  }
  
  /**
   * 创建车辆
   */
  private createVehicle(): void {
    // 创建底盘
    this.chassis = this.createChassis();
    this.scene.addEntity(this.chassis);
    
    // 创建车轮
    for (let i = 0; i < this.wheelPositions.length; i++) {
      const wheel = this.createWheel(i);
      this.wheels.push(wheel);
      this.scene.addEntity(wheel);
    }
    
    // 创建悬挂约束
    for (let i = 0; i < this.wheels.length; i++) {
      this.createSuspension(this.chassis, this.wheels[i], this.wheelPositions[i]);
    }
  }
  
  /**
   * 创建底盘
   * @returns 底盘实体
   */
  private createChassis(): Entity {
    // 创建底盘实体
    const chassis = new Entity('vehicle_chassis');
    
    // 添加变换组件
    const transform = chassis.getTransform();
    transform.setPosition(0, 1, 0);
    transform.setScale(2, 0.5, 4);
    
    // 创建底盘网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ color: 0x3366cc });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    chassis.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    chassis.addComponent(new PhysicsBodyComponent({
      type: BodyType.DYNAMIC,
      mass: this.vehicleConfig.chassisMass,
      material: PhysicsMaterialFactory.getMaterial('metal')
    }));
    
    // 添加碰撞器组件
    chassis.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: 1, y: 0.25, z: 2 }
      }
    }));
    
    return chassis;
  }
  
  /**
   * 创建车轮
   * @param index 车轮索引
   * @returns 车轮实体
   */
  private createWheel(index: number): Entity {
    // 创建车轮实体
    const wheel = new Entity(`vehicle_wheel_${index}`);
    
    // 获取车轮位置
    const wheelPos = this.wheelPositions[index];
    
    // 添加变换组件
    const transform = wheel.getTransform();
    transform.setPosition(
      this.chassis.getTransform().position.x + wheelPos.x,
      this.chassis.getTransform().position.y + wheelPos.y - this.vehicleConfig.suspensionRestLength,
      this.chassis.getTransform().position.z + wheelPos.z
    );
    transform.setRotation(Math.PI / 2, 0, 0);
    transform.setScale(
      this.vehicleConfig.wheelRadius * 2,
      this.vehicleConfig.wheelWidth,
      this.vehicleConfig.wheelRadius * 2
    );
    
    // 创建车轮网格
    const geometry = new THREE.CylinderGeometry(1, 1, 1, 24);
    const material = new THREE.MeshStandardMaterial({ color: 0x333333 });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    wheel.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    wheel.addComponent(new PhysicsBodyComponent({
      type: BodyType.DYNAMIC,
      mass: this.vehicleConfig.wheelMass,
      material: PhysicsMaterialFactory.getMaterial('rubber')
    }));
    
    // 添加碰撞器组件
    wheel.addComponent(new PhysicsColliderComponent({
      type: ColliderType.CYLINDER,
      params: {
        radiusTop: this.vehicleConfig.wheelRadius,
        radiusBottom: this.vehicleConfig.wheelRadius,
        height: this.vehicleConfig.wheelWidth,
        numSegments: 16
      }
    }));
    
    return wheel;
  }
  
  /**
   * 创建悬挂约束
   * @param chassis 底盘实体
   * @param wheel 车轮实体
   * @param wheelPos 车轮位置
   */
  private createSuspension(chassis: Entity, wheel: Entity, wheelPos: { x: number, y: number, z: number }): void {
    // 创建悬挂约束
    const constraint = new PhysicsConstraintComponent({
      type: 'spring',
      entityA: chassis,
      entityB: wheel,
      pivotA: new Vector3(wheelPos.x, wheelPos.y, wheelPos.z),
      pivotB: new Vector3(0, 0, 0),
      axisA: new Vector3(0, -1, 0),
      axisB: new Vector3(0, 0, 1),
      stiffness: this.vehicleConfig.suspensionStiffness,
      damping: this.vehicleConfig.suspensionDamping,
      restLength: this.vehicleConfig.suspensionRestLength
    });
    
    // 添加约束到底盘
    chassis.addComponent(constraint);
  }
  
  /**
   * 创建障碍物
   */
  private createObstacles(): void {
    // 创建坡道
    this.createRamp(10, 0, 10, 10, 2, 5, Math.PI / 12);
    
    // 创建盒子障碍物
    for (let i = 0; i < 10; i++) {
      this.createBox(
        Math.random() * 20 - 10,
        1,
        Math.random() * 20 - 10,
        Math.random() * 0.5 + 0.5
      );
    }
  }
  
  /**
   * 创建坡道
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   * @param width 宽度
   * @param height 高度
   * @param length 长度
   * @param angle 角度
   */
  private createRamp(x: number, y: number, z: number, width: number, height: number, length: number, angle: number): void {
    // 创建坡道实体
    const ramp = new Entity('ramp');
    
    // 添加变换组件
    const transform = ramp.getTransform();
    transform.setPosition(x, y + height / 2, z);
    transform.setRotation(angle, 0, 0);
    transform.setScale(width, height, length);
    
    // 创建坡道网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ color: 0x8080a0 });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    ramp.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    ramp.addComponent(new PhysicsBodyComponent({
      type: BodyType.STATIC,
      mass: 0
    }));
    
    // 添加碰撞器组件
    ramp.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: width / 2, y: height / 2, z: length / 2 }
      }
    }));
    
    // 添加到场景
    this.scene.addEntity(ramp);
  }
  
  /**
   * 创建盒子
   * @param x X坐标
   * @param y Y坐标
   * @param z Z坐标
   * @param size 盒子大小
   */
  private createBox(x: number, y: number, z: number, size: number): void {
    // 创建盒子实体
    const box = new Entity(`obstacle_box_${Math.random().toString(36).substr(2, 5)}`);
    
    // 添加变换组件
    const transform = box.getTransform();
    transform.setPosition(x, y, z);
    transform.setScale(size, size, size);
    
    // 创建盒子网格
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({ 
      color: new THREE.Color(Math.random(), Math.random(), Math.random()) 
    });
    const mesh = new THREE.Mesh(geometry, material);
    
    // 添加网格组件
    box.addComponent('MeshComponent', { mesh });
    
    // 添加物理体组件
    box.addComponent(new PhysicsBodyComponent({
      type: BodyType.DYNAMIC,
      mass: size * 10,
      material: PhysicsMaterialFactory.getMaterial('wood')
    }));
    
    // 添加碰撞器组件
    box.addComponent(new PhysicsColliderComponent({
      type: ColliderType.BOX,
      params: {
        halfExtents: { x: size / 2, y: size / 2, z: size / 2 }
      }
    }));
    
    // 添加到场景
    this.scene.addEntity(box);
  }
  
  /**
   * 重置车辆位置
   */
  public resetVehicle(): void {
    if (!this.chassis) return;
    
    // 重置底盘位置和速度
    const chassisBody = this.chassis.getComponent(PhysicsBodyComponent);
    if (chassisBody) {
      chassisBody.setPosition(new Vector3(0, 1, 0));
      chassisBody.setQuaternion(new Quaternion());
      chassisBody.setLinearVelocity(new Vector3(0, 0, 0));
      chassisBody.setAngularVelocity(new Vector3(0, 0, 0));
    }
    
    // 重置车轮位置和速度
    for (let i = 0; i < this.wheels.length; i++) {
      const wheelBody = this.wheels[i].getComponent(PhysicsBodyComponent);
      if (wheelBody) {
        const wheelPos = this.wheelPositions[i];
        wheelBody.setPosition(new Vector3(
          wheelPos.x,
          this.chassis.getTransform().position.y - this.vehicleConfig.suspensionRestLength,
          wheelPos.z
        ));
        wheelBody.setLinearVelocity(new Vector3(0, 0, 0));
        wheelBody.setAngularVelocity(new Vector3(0, 0, 0));
      }
    }
    
    // 重置控制状态
    this.currentSteeringAngle = 0;
    this.currentDriveForce = 0;
    this.currentBrakeForce = 0;
  }
}
