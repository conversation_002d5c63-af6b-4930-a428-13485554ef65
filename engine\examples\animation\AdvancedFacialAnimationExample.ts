/**
 * 高级面部动画示例
 * 演示面部动画系统的高级功能，包括多角色口型同步、表情混合、AI驱动动画等
 */
import * as THREE from 'three';
import { Engine } from '../../core/Engine';
import { World } from '../../core/World';
import { Entity } from '../../core/Entity';
import { FacialAnimationSystem, FacialAnimationComponent, FacialExpressionType, VisemeType } from '../../animation/FacialAnimation';
import { LipSyncSystem, LipSyncComponent } from '../../animation/LipSync';
import { FacialAnimationEditorSystem } from '../../animation/FacialAnimationEditorSystem';
import { AIAnimationSynthesisSystem } from '../../animation/AIAnimationSynthesisSystem';
import { GPUFacialAnimationSystem } from '../../animation/GPUFacialAnimation';
import { GLTFLoader } from '../../loaders/GLTFLoader';
import { OrbitControls } from '../../utils/OrbitControls';
import { GUI } from '../../utils/GUI';

/**
 * 高级面部动画示例
 */
export class AdvancedFacialAnimationExample {
  /** 引擎 */
  private engine: Engine;
  /** 世界 */
  private world: World;
  /** 场景 */
  private scene: THREE.Scene;
  /** 相机 */
  private camera: THREE.PerspectiveCamera;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer;
  /** 控制器 */
  private controls: OrbitControls;
  /** 角色实体列表 */
  private characterEntities: Entity[] = [];
  /** 面部动画系统 */
  private facialAnimationSystem: FacialAnimationSystem;
  /** 口型同步系统 */
  private lipSyncSystem: LipSyncSystem;
  /** 面部动画编辑器系统 */
  private facialAnimationEditorSystem: FacialAnimationEditorSystem;
  /** AI动画合成系统 */
  private aiAnimationSynthesisSystem: AIAnimationSynthesisSystem;
  /** GPU面部动画系统 */
  private gpuFacialAnimationSystem: GPUFacialAnimationSystem;
  /** 音频元素列表 */
  private audioElements: Map<string, HTMLAudioElement> = new Map();
  /** 是否运行中 */
  private running: boolean = false;
  /** GUI控制面板 */
  private gui: GUI;
  /** 当前选中的角色索引 */
  private selectedCharacterIndex: number = 0;
  /** 是否使用GPU加速 */
  private useGPUAcceleration: boolean = true;
  /** 是否使用工作线程 */
  private useWorker: boolean = true;
  /** 分析间隔（毫秒） */
  private analysisInterval: number = 100;
  /** 频率带数量 */
  private numFrequencyBands: number = 8;

  /**
   * 构造函数
   */
  constructor() {
    // 创建引擎
    this.engine = new Engine();
    
    // 获取世界
    this.world = this.engine.getWorld();
    
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x333333);
    
    // 创建相机
    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.camera.position.set(0, 1.6, 4);
    
    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    document.body.appendChild(this.renderer.domElement);
    
    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 1.6, 0);
    this.controls.update();
    
    // 创建面部动画系统
    this.facialAnimationSystem = new FacialAnimationSystem(this.world, {
      debug: true,
      autoDetectAudio: true
    });
    
    // 创建口型同步系统
    this.lipSyncSystem = new LipSyncSystem(this.world, {
      debug: true,
      fftSize: 1024,
      volumeThreshold: 0.01,
      analysisInterval: this.analysisInterval,
      useWorker: this.useWorker,
      numFrequencyBands: this.numFrequencyBands
    });
    
    // 创建面部动画编辑器系统
    this.facialAnimationEditorSystem = new FacialAnimationEditorSystem(this.world, {
      debug: true
    });
    
    // 创建AI动画合成系统
    this.aiAnimationSynthesisSystem = new AIAnimationSynthesisSystem(this.world, {
      debug: true,
      useLocalModel: true
    });
    
    // 创建GPU面部动画系统
    this.gpuFacialAnimationSystem = new GPUFacialAnimationSystem(this.world, {
      debug: true,
      useComputeShader: false,
      maxBlendShapes: 32,
      textureSize: 16
    });
    
    // 添加系统到世界
    this.world.addSystem(this.facialAnimationSystem);
    this.world.addSystem(this.lipSyncSystem);
    this.world.addSystem(this.facialAnimationEditorSystem);
    this.world.addSystem(this.aiAnimationSynthesisSystem);
    this.world.addSystem(this.gpuFacialAnimationSystem);
    
    // 创建GUI控制面板
    this.gui = new GUI();
    
    // 设置窗口大小变化监听
    window.addEventListener('resize', this.onWindowResize.bind(this));
  }

  /**
   * 窗口大小变化处理
   */
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  /**
   * 创建场景
   */
  private createScene(): void {
    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(ambientLight);
    
    // 添加方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 2, 3);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);
    
    // 设置阴影
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.1;
    directionalLight.shadow.camera.far = 10;
    directionalLight.shadow.camera.left = -5;
    directionalLight.shadow.camera.right = 5;
    directionalLight.shadow.camera.top = 5;
    directionalLight.shadow.camera.bottom = -5;
    
    // 添加地面
    const ground = new THREE.Mesh(
      new THREE.PlaneGeometry(10, 10),
      new THREE.MeshStandardMaterial({ color: 0x999999 })
    );
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    this.scene.add(ground);
    
    // 添加坐标轴辅助
    const axesHelper = new THREE.AxesHelper(1);
    this.scene.add(axesHelper);
  }

  /**
   * 加载多个角色模型
   */
  private loadCharacters(): void {
    // 创建GLTF加载器
    const loader = new GLTFLoader();
    
    // 角色模型路径列表
    const characterPaths = [
      'models/character1.glb',
      'models/character2.glb',
      'models/character3.glb'
    ];
    
    // 角色位置
    const positions = [
      new THREE.Vector3(-2, 0, 0),
      new THREE.Vector3(0, 0, 0),
      new THREE.Vector3(2, 0, 0)
    ];
    
    // 加载所有角色模型
    characterPaths.forEach((path, index) => {
      // 创建角色实体
      const entity = this.world.createEntity();
      this.characterEntities.push(entity);
      
      // 加载角色模型
      loader.load(path, (gltf) => {
        // 设置位置
        gltf.scene.position.copy(positions[index]);
        
        // 添加模型到场景
        this.scene.add(gltf.scene);
        
        // 设置阴影
        gltf.scene.traverse((object) => {
          if (object instanceof THREE.Mesh) {
            object.castShadow = true;
            object.receiveShadow = true;
            
            // 如果是骨骼网格，设置面部动画
            if (object instanceof THREE.SkinnedMesh) {
              this.setupFacialAnimation(entity, object, index);
            }
          }
        });
        
        console.log(`角色${index + 1}加载完成`);
      }, undefined, (error) => {
        console.error(`加载角色${index + 1}失败:`, error);
      });
    });
  }

  /**
   * 设置面部动画
   * @param entity 实体
   * @param mesh 骨骼网格
   * @param index 角色索引
   */
  private setupFacialAnimation(entity: Entity, mesh: THREE.SkinnedMesh, index: number): void {
    // 创建面部动画组件
    const facialAnimation = this.facialAnimationSystem.createFacialAnimation(entity);
    
    // 创建口型同步组件
    const lipSync = this.lipSyncSystem.createLipSync(entity);
    
    // 创建面部动画编辑器
    const editor = this.facialAnimationEditorSystem.createEditor(entity);
    
    // 如果启用GPU加速，创建GPU面部动画组件
    if (this.useGPUAcceleration) {
      this.gpuFacialAnimationSystem.createGPUFacialAnimation(entity, mesh);
    }
    
    // 创建默认表情动画
    this.createDefaultAnimations(entity, index);
    
    // 加载音频
    this.loadAudioForCharacter(entity, index);
  }

  /**
   * 创建默认动画
   * @param entity 实体
   * @param index 角色索引
   */
  private createDefaultAnimations(entity: Entity, index: number): void {
    const editor = this.facialAnimationEditorSystem.getEditor(entity);
    if (!editor) return;
    
    // 创建表情动画
    const expressionClip = editor.createClip(`角色${index + 1}_表情`, 5.0, true);
    
    // 添加关键帧
    editor.addKeyframe(0.0, {
      expression: FacialExpressionType.NEUTRAL,
      expressionWeight: 0.0
    });
    
    editor.addKeyframe(1.0, {
      expression: FacialExpressionType.HAPPY,
      expressionWeight: 1.0
    });
    
    editor.addKeyframe(2.0, {
      expression: FacialExpressionType.SURPRISED,
      expressionWeight: 1.0
    });
    
    editor.addKeyframe(3.0, {
      expression: FacialExpressionType.ANGRY,
      expressionWeight: 1.0
    });
    
    editor.addKeyframe(4.0, {
      expression: FacialExpressionType.SAD,
      expressionWeight: 1.0
    });
    
    editor.addKeyframe(5.0, {
      expression: FacialExpressionType.NEUTRAL,
      expressionWeight: 0.0
    });
    
    // 设置当前片段
    editor.setCurrentClip(`角色${index + 1}_表情`);
  }

  /**
   * 为角色加载音频
   * @param entity 实体
   * @param index 角色索引
   */
  private loadAudioForCharacter(entity: Entity, index: number): void {
    // 音频路径
    const audioPath = `audio/speech${index + 1}.mp3`;
    
    // 创建音频元素
    const audioElement = new Audio(audioPath);
    audioElement.loop = true;
    
    // 保存音频元素
    this.audioElements.set(`角色${index + 1}`, audioElement);
    
    // 设置音频加载完成回调
    audioElement.addEventListener('canplaythrough', () => {
      console.log(`角色${index + 1}音频加载完成`);
    });
    
    // 加载音频
    audioElement.load();
  }

  /**
   * 创建GUI控制面板
   */
  private createGUI(): void {
    // 创建角色文件夹
    const characterFolder = this.gui.addFolder('角色控制');
    
    // 添加角色选择控制
    characterFolder.add(this, 'selectedCharacterIndex', 0, this.characterEntities.length - 1, 1)
      .name('选择角色')
      .onChange(this.onCharacterSelected.bind(this));
    
    // 添加表情控制
    const expressionFolder = this.gui.addFolder('表情控制');
    
    // 添加表情类型选择
    const expressionTypes = Object.values(FacialExpressionType);
    const expressionController = expressionFolder.add({ expression: FacialExpressionType.NEUTRAL }, 'expression', expressionTypes)
      .name('表情类型');
    
    // 添加表情权重控制
    const expressionWeightController = expressionFolder.add({ weight: 1.0 }, 'weight', 0, 1, 0.01)
      .name('表情权重');
    
    // 添加表情应用按钮
    expressionFolder.add({ apply: () => {
      const entity = this.characterEntities[this.selectedCharacterIndex];
      const facialAnimation = this.facialAnimationSystem.getFacialAnimation(entity);
      if (facialAnimation) {
        facialAnimation.setExpression(
          expressionController.getValue(),
          expressionWeightController.getValue(),
          0.5
        );
      }
    }}, 'apply').name('应用表情');
    
    // 添加口型控制
    const visemeFolder = this.gui.addFolder('口型控制');
    
    // 添加口型类型选择
    const visemeTypes = Object.values(VisemeType);
    const visemeController = visemeFolder.add({ viseme: VisemeType.SILENT }, 'viseme', visemeTypes)
      .name('口型类型');
    
    // 添加口型权重控制
    const visemeWeightController = visemeFolder.add({ weight: 1.0 }, 'weight', 0, 1, 0.01)
      .name('口型权重');
    
    // 添加口型应用按钮
    visemeFolder.add({ apply: () => {
      const entity = this.characterEntities[this.selectedCharacterIndex];
      const lipSync = this.lipSyncSystem.getLipSync(entity);
      if (lipSync) {
        lipSync.setViseme(
          visemeController.getValue(),
          visemeWeightController.getValue(),
          0.2
        );
      }
    }}, 'apply').name('应用口型');
    
    // 添加音频控制
    const audioFolder = this.gui.addFolder('音频控制');
    
    // 添加播放按钮
    audioFolder.add({ play: () => {
      const entity = this.characterEntities[this.selectedCharacterIndex];
      const audioElement = this.audioElements.get(`角色${this.selectedCharacterIndex + 1}`);
      if (audioElement) {
        this.lipSyncSystem.startTracking(audioElement);
        audioElement.play();
      }
    }}, 'play').name('播放音频');
    
    // 添加暂停按钮
    audioFolder.add({ pause: () => {
      const audioElement = this.audioElements.get(`角色${this.selectedCharacterIndex + 1}`);
      if (audioElement) {
        audioElement.pause();
      }
    }}, 'pause').name('暂停音频');
    
    // 添加停止按钮
    audioFolder.add({ stop: () => {
      this.lipSyncSystem.stopTracking();
      for (const audio of this.audioElements.values()) {
        audio.pause();
        audio.currentTime = 0;
      }
    }}, 'stop').name('停止所有音频');
    
    // 添加性能控制
    const performanceFolder = this.gui.addFolder('性能控制');
    
    // 添加GPU加速控制
    performanceFolder.add(this, 'useGPUAcceleration')
      .name('GPU加速')
      .onChange(this.updatePerformanceSettings.bind(this));
    
    // 添加工作线程控制
    performanceFolder.add(this, 'useWorker')
      .name('工作线程')
      .onChange(this.updatePerformanceSettings.bind(this));
    
    // 添加分析间隔控制
    performanceFolder.add(this, 'analysisInterval', 50, 500, 10)
      .name('分析间隔(ms)')
      .onChange(this.updatePerformanceSettings.bind(this));
    
    // 添加频率带数量控制
    performanceFolder.add(this, 'numFrequencyBands', 4, 16, 1)
      .name('频率带数量')
      .onChange(this.updatePerformanceSettings.bind(this));
    
    // 打开所有文件夹
    characterFolder.open();
    expressionFolder.open();
    visemeFolder.open();
    audioFolder.open();
    performanceFolder.open();
  }

  /**
   * 角色选择变化处理
   */
  private onCharacterSelected(): void {
    console.log(`选择角色: ${this.selectedCharacterIndex + 1}`);
  }

  /**
   * 更新性能设置
   */
  private updatePerformanceSettings(): void {
    // 重新启动口型同步系统
    this.lipSyncSystem.stopTracking();
    
    // 更新配置
    this.lipSyncSystem = new LipSyncSystem(this.world, {
      debug: true,
      fftSize: 1024,
      volumeThreshold: 0.01,
      analysisInterval: this.analysisInterval,
      useWorker: this.useWorker,
      useGPU: this.useGPUAcceleration,
      numFrequencyBands: this.numFrequencyBands
    });
    
    // 重新添加系统
    this.world.removeSystem(this.lipSyncSystem.constructor.name);
    this.world.addSystem(this.lipSyncSystem);
    
    console.log('性能设置已更新:', {
      useGPUAcceleration: this.useGPUAcceleration,
      useWorker: this.useWorker,
      analysisInterval: this.analysisInterval,
      numFrequencyBands: this.numFrequencyBands
    });
  }

  /**
   * 启动示例
   */
  public start(): void {
    // 创建场景
    this.createScene();
    
    // 加载角色
    this.loadCharacters();
    
    // 创建GUI控制面板
    this.createGUI();
    
    // 启动引擎
    this.engine.start();
    
    // 开始动画循环
    this.running = true;
    this.animate();
    
    console.log('高级面部动画示例已启动');
  }

  /**
   * 停止示例
   */
  public stop(): void {
    this.running = false;
    
    // 停止所有音频
    for (const audio of this.audioElements.values()) {
      audio.pause();
    }
    
    // 停止口型同步
    this.lipSyncSystem.stopTracking();
    
    // 停止引擎
    this.engine.stop();
    
    // 移除GUI
    this.gui.destroy();
    
    console.log('高级面部动画示例已停止');
  }

  /**
   * 动画循环
   */
  private animate(): void {
    if (!this.running) return;
    
    requestAnimationFrame(this.animate.bind(this));
    
    // 更新控制器
    this.controls.update();
    
    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }
}
